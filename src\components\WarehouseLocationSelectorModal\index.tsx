import type { ProFormFieldProps } from '@ant-design/pro-form';

import styles from '../WarehouseLocationSelector/index.less';
import { ArrowLeftOutlined, EllipsisOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { But<PERSON>, Popover, Row, Space } from 'antd';
import ProCard from '@ant-design/pro-card';
import { INITIAL_OPEN_OTHERS, WL_SELECTION_STEPS } from '../WarehouseLocationSelector';

type WarehouseLocationSelectorModalType = {
  defaultValue?: string;
  onChange: (value: string) => void;
  eleOptions?: ProFormFieldProps & { showDecimal?: boolean };
  showBodyScroll?: boolean;
  initialOpen?: boolean;
};

/**
 * @deprecated under development
 *
 * Numpad
 */
const WarehouseLocationSelectorModal: React.FC<WarehouseLocationSelectorModalType> = ({
  defaultValue,
  onChange,
  children,
  eleOptions,
  showBodyScroll,
  initialOpen,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [openOthers, setOpenOthers] = useState<Record<number, boolean>>(INITIAL_OPEN_OTHERS);
  const [value, setValue] = useState<string>('');

  const gStep = value.length;

  const onClickHandler = (letterP: string | number, stepP?: number) => {
    console.log('onClickHandler', letterP, 'step=', stepP);
    if (stepP == gStep + 1) {
      setValue((prev) => `${prev}${letterP}`);
    } else if (stepP == gStep) {
      setValue((prev) => prev.slice(0, -1));
    }

    setOpenOthers(INITIAL_OPEN_OTHERS);
  };

  useEffect(() => {
    setValue(defaultValue || '');
  }, [defaultValue]);

  useEffect(() => {
    if (value.length >= 4) {
      setOpen(false);
      setOpenOthers(INITIAL_OPEN_OTHERS);
    }
    onChange(value || '');
  }, [value]);

  useEffect(() => {
    console.log(`-->[UseEffect] initialOpen: ${initialOpen}`);

    setOpen(!!initialOpen);
  }, [initialOpen]);

  console.log('[WarehouseLocationSelector]', `initialOpen? ${initialOpen}`, 'open?', open);

  return (
    <div className={styles.warehouseLocationSelectorWrap}>
      <Popover
        placement="bottom"
        open={open}
        onOpenChange={setOpen}
        trigger={['click']}
        content={
          <ProCard
            className={styles.content}
            title={value || 'Select Location'}
            headerBordered
            bodyStyle={{
              padding: 0,
              ...(showBodyScroll ? { maxHeight: 'calc(100vh - 250px)', overflowY: 'auto' } : {}),
            }}
            headStyle={{
              padding: 0,
            }}
            extra={
              <Space>
                <Button
                  icon={<ArrowLeftOutlined />}
                  size="large"
                  className="keypad-btn"
                  disabled={value.length == 0}
                  onClick={() => {
                    setValue((prev) => `${prev}`.slice(0, -1));
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                  }}
                />
                <Button
                  icon={<ArrowLeftOutlined />}
                  size="large"
                  className="keypad-btn"
                  disabled={value.length == 0}
                  onClick={() => {
                    setValue((prev) => `${prev}`.slice(0, -1));
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                  }}
                />
              </Space>
            }
          >
            {WL_SELECTION_STEPS.map((step) => (
              <Row className={`step step${step}`} key={step}>
                {KEYPAD_LETTERS[step].map((letter) =>
                  letter.length == 1 ? (
                    <Button
                      key={`${step}_${letter}`}
                      size="large"
                      type="primary"
                      className={`keypad-btn${
                        (step <= gStep && letter != value.slice(step - 1, step)) || step > gStep + 1 ? ' disabled' : ''
                      }`}
                      disabled={(step <= gStep && letter != value.slice(step - 1, step)) || step > gStep + 1}
                      onClick={() => onClickHandler(letter, step)}
                    >
                      {letter}
                    </Button>
                  ) : (
                    <Popover
                      key={`${step}_${letter}`}
                      placement="bottom"
                      trigger={[]}
                      open={openOthers[step]}
                      onOpenChange={(visible) => setOpenOthers((prev) => ({ ...prev, [step]: visible }))}
                      content={
                        <div className={styles.contentSmall}>
                          <Row className={`step step${step}`}>
                            {KEYPAD_LETTERS_OTHERS[step].map((letter) => (
                              <Button
                                key={`${step}_${letter}`}
                                size="large"
                                type="primary"
                                className={`keypad-btn keypad-btn-sm ${step <= gStep ? ' disabled' : ''}`}
                                disabled={step <= gStep}
                                onClick={() => onClickHandler(letter, step)}
                              >
                                {letter}
                              </Button>
                            ))}
                          </Row>
                        </div>
                      }
                    >
                      <Button
                        size="large"
                        icon={<EllipsisOutlined />}
                        className={`keypad-btn other  ${
                          (step <= gStep && !KEYPAD_LETTERS_OTHERS[step].includes(value.slice(step - 1, step))) ||
                          step > gStep + 1
                            ? ' disabled'
                            : ''
                        }`}
                        disabled={
                          (step <= gStep && !KEYPAD_LETTERS_OTHERS[step].includes(value.slice(step - 1, step))) ||
                          step > gStep + 1
                        }
                        onClick={(e) => {
                          console.log('Sub-modal opening...');
                          setOpenOthers((prev) => ({ ...prev, [step]: !prev[step] }));
                        }}
                      />
                    </Popover>
                  ),
                )}
              </Row>
            ))}
          </ProCard>
        }
      >
        <Button type="primary" size="large" onClick={() => setOpen(true)} style={{ width: 130 }}>
          {value ? `${value}` : 'Select Location'}
        </Button>
      </Popover>
    </div>
  );
};

export default WarehouseLocationSelectorModal;
