import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Card, Popconfirm } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { nl2br, sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getEanTaskList } from '@/services/foodstore-one/Item/ean-task';
import { DEFAULT_PER_PAGE_PAGINATION, DictType, TaskStatus, TaskStatusOptions } from '@/constants';
import { useModel } from 'umi';
import { TaskStatusComp, handleRemove } from '.';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';

export type SearchFormValueType = Partial<API.EanTask>;

type EanTaskListPageProps = {
  ean?: string;
  reloadList?: () => void;
  refreshTick?: number;
};

const EanTaskListPage: React.FC<EanTaskListPageProps> = ({ ean, reloadList, refreshTick }) => {
  const { getDictByCode, getDictOptionsCV } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.EanTask>();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<API.EanTask>[] = [
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      width: 120,
      copyable: true,
    },
    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      width: 150,
      copyable: true,
    },
    {
      title: 'Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      width: 250,
      ellipsis: true,
    },
    {
      title: 'Category',
      dataIndex: ['category_code'],
      width: 170,
      className: 'bl-2',
      render: (__, record) => getDictByCode(record.category_code),
    },
    {
      title: 'Task',
      dataIndex: 'task',
      sorter: false,
      render: (dom, record) => <span dangerouslySetInnerHTML={{ __html: nl2br(record.task) }} />,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: false,
      align: 'center',
      className: 'p-0',
      width: 120,
      render: (dom, record) => (
        <TaskStatusComp status={record.status ?? TaskStatus.Open} id={record.id} actionRef={actionRef} />
      ),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 70,
      align: 'center',
      sorter: true,
      renderText: (val: string) => `${val}`,
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          <EditOutlined />
        </a>,

        <Popconfirm
          key="delete"
          title={<>Are you sure you want to delete?</>}
          okText="Yes"
          cancelText="No"
          overlayStyle={{ maxWidth: 350 }}
          onConfirm={() => {
            handleRemove({ id: record?.id }).then((res) => {
              actionRef.current?.reload();
            });
          }}
        >
          <DeleteOutlined className="c-red" />
        </Popconfirm>,
      ],
    },
  ];

  useEffect(() => {
    if (refreshTick) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            name="category_code"
            label="Category"
            width="sm"
            options={getDictOptionsCV(DictType.EanTaskCategory)}
          />
          <ProFormSelect
            name="status"
            label="Status"
            width="sm"
            options={TaskStatusOptions}
            initialValue={TaskStatus.Open}
          />
          <ProFormText name={'sku'} label="SKU" placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" placeholder={'EAN'} />
          <ProFormText name={'keyword'} label="Task" placeholder={'Task'} />
        </ProForm>
      </Card>

      <ProTable<API.EanTask, API.PageParams>
        headerTitle={'EAN Tasks List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        size="small"
        options={{ fullScreen: false, density: false, reload: true, setting: false }}
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_tasks', searchFormValues);
          Util.setSfValues('sf_ean_tasks_p', params);

          setLoading(true);
          const res = await getEanTaskList(
            {
              ...searchFormValues,
              ...params,
              pageSize: 10,
              with: 'categoryDict,itemEan',
            },
            { created_on: 'descend', ...sort },
            filter,
          ).finally(() => setLoading(false));
          return res;
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_tasks_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        columns={columns}
        rowSelection={false}
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        values={{ ean: ean }}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
          reloadList?.();
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default EanTaskListPage;
