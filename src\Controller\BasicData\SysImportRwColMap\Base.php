<?php

declare(strict_types=1);

namespace App\Controller\BasicData\SysImportRwColMap;

use App\Controller\BaseController;
use App\Service\BasicData\SysImportRwColMap\SysImportRwColMapService;
use Slim\Container;

abstract class Base extends BaseController
{
    public SysImportRwColMapService $sysImportRwColMapService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->sysImportRwColMapService = $container->get(SysImportRwColMapService::class);
    }
}
