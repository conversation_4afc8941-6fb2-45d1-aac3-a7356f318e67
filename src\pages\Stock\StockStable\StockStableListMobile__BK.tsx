import { <PERSON><PERSON>, <PERSON>, <PERSON>, Drawer, Popover, Row, Space, Typography, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { FooterToolbar, PageContainer, PageContainerProps } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import {
  exportStockStableList,
  getStockStable,
  stockStableBulkMove2Target,
  stockStableBulkMove2TargetByName,
  stockStableBulkMoveAll,
} from '@/services/foodstore-one/Stock/stock-stable';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import Util, { nf2, ni, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import SDatePicker from '@/components/SDatePicker';
import _ from 'lodash';
import SPrices from '@/components/SPrices';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import SelectIboModal from './components/SelectIboModal';
import { EditOutlined, FileExcelOutlined, FilePdfOutlined } from '@ant-design/icons';
import { IRouteComponentProps, useLocation } from 'umi';
import ExportPdfStockStableSettingFormModal from './components/ExportPdfStockStableSettingFormModal';
import StockStableQtyUpdateFormModal from './components/StockStableQtyUpdateFormModal';
import StockStableUpdateExpForm from '@/pages/Item/EanList/components/StockStableUpdateExpForm';
import SkuComp from '@/components/SkuComp';
import WarehouseLocationSelector from '@/components/WarehouseLocationSelector';

export type SearchFormValueType = Partial<API.StockStable>;

type BulkStockMoveFormValueType = {
  new_wl_id?: number;
  new_wl_name?: string;
};

type BulkStockMoveAllFormValueType = {
  old_wl_id?: number;
  new_wl_id?: number;
};

const StockStableListMobile: React.FC<IRouteComponentProps> = (props) => {
  const location: any = useLocation();

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);

  const [currentRow, setCurrentRow] = useState<SearchFormValueType & { openParent?: boolean }>();
  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<API.StockStable[]>([]);

  // stock qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  // open select IBO modal
  const [openSelectIboModalVisible, setOpenSelectIboModalVisible] = useState<boolean>(false);

  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});

  // bulk stock movements
  const [openBulkStockMoveForm, setOpenBulkStockMoveForm] = useState<boolean>(false);
  const bulkStockMoveFormRef = useRef<ProFormInstance>();
  const [openBulkStockMoveAllForm, setOpenBulkStockMoveAllForm] = useState<boolean>(false);

  // Export PDF Option Form
  const [openExportPdfForm, setOpenExportPdfForm] = useState<boolean>(false);

  // Qty Edit modal
  const [openStockStableQtyUpdateFormModal, setOpenStockStableQtyUpdateFormModal] = useState<boolean>(false);

  const [expDateModalVisible, handleExpDateModalVisible] = useState<boolean>(false);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  useEffect(() => {
    getWarehouseLocationACList({})
      .then((res) => {
        setWarehouseLocations(res);
      })
      .catch(Util.error);
  }, []);

  /* useEffect(() => {
    if (location.query.wl_id || location.query.sku) {
      searchFormRef.current?.setFieldsValue({
        wl_id: location.query.wl_id ? sn(location.query.wl_id) : undefined,
        sku: location.query.sku,
      });
      actionRef.current?.reload();
    }
  }, [location.query.sku, location.query.wl_id]); */

  const columns: ProColumns<API.StockStable>[] = [
    /* {
      title: 'Warehouse',
      dataIndex: ['warehouse_location', 'name'],
      width: 80,
      sorter: true,
    }, */
    {
      title: 'Trademark',
      dataIndex: ['item', 'trademark', 'name'],
      sorter: false,
      width: 100,
      render: (dom, record) => (record?.item?.trademark?.name ? `${record?.item?.trademark?.name}` : ''),
    },
    {
      title: 'Single',
      dataIndex: ['single_summary'],
      sorter: true,
      width: 350,
      className: 'p-0',
      align: 'center',
      render(__, entity) {
        if (!entity.item_ean?.is_single) return null;
        return (
          <Space direction="vertical" size={4} style={{ textAlign: 'left', width: '100%' }}>
            <Row gutter={0} wrap={false}>
              <Col flex="0 0 105px">
                <SkuComp sku={entity.item_ean?.sku} style={{ color: '#000' }} />
              </Col>
              <Col flex="auto">
                <Typography.Text copyable style={{ color: entity.item_ean?.is_single ? 'blue' : '#000' }}>
                  {entity.parent_item_ean?.ean}
                </Typography.Text>
              </Col>
              {!entity.item_ean?.is_single && (
                <Col flex="0 0 120px">
                  <Typography.Text copyable style={{ color: 'blue' }}>
                    {entity.item_ean?.ean}
                  </Typography.Text>
                </Col>
              )}
            </Row>
            <div>{entity.item_ean?.ean_text_de?.name ?? entity.item_ean?.item?.name ?? ''}</div>
          </Space>
        );
      },
    },
    {
      title: 'Qty',
      dataIndex: ['single_qty'],
      sorter: true,
      align: 'center',
      className: 'p-0',
      width: 110,
      tooltip: 'Click Qty or Exp. Date to edit...',
      render(__, entity) {
        if (!entity.item_ean?.is_single) return null;
        return (
          <Space direction="vertical" size={4} style={{ width: '100%', textAlign: 'center' }}>
            <div
              className="text-md bold cursor-pointer"
              onClick={() => {
                setCurrentRow(entity);
                setOpenStockStableQtyUpdateFormModal(true);
              }}
            >
              {entity.case_qty == 1 ? `${entity.piece_qty}` : `${entity.case_qty} x ${entity.box_qty}`}
            </div>
            <div>
              (
              <span
                style={{ verticalAlign: 'middle' }}
                className="cursor-pointer"
                onClick={() => {
                  setCurrentRow({ ...entity });
                  handleExpDateModalVisible(true);
                }}
              >
                {Util.dtToDMY(entity?.exp_date)}
              </span>
              )
            </div>
          </Space>
        );
      },
    },

    // -----------------
    {
      title: 'Qty',
      dataIndex: ['multi_qty'],
      sorter: true,
      align: 'center',
      className: 'p-0 bl2',
      width: 110,
      tooltip: 'Click Qty or Exp. Date to edit...',
      render(__, entity) {
        if (entity.item_ean?.is_single) return null;
        return (
          <Space direction="vertical" size={4} style={{ width: '100%', textAlign: 'center' }}>
            <div
              className="text-md bold cursor-pointer"
              onClick={() => {
                setCurrentRow(entity);
                setOpenStockStableQtyUpdateFormModal(true);
              }}
            >
              {entity.case_qty == 1 ? `${entity.piece_qty}` : `${entity.case_qty} x ${entity.box_qty}`}
            </div>
            <div>
              (
              <span
                style={{ verticalAlign: 'middle' }}
                className="cursor-pointer"
                onClick={() => {
                  setCurrentRow({ ...entity });
                  handleExpDateModalVisible(true);
                }}
              >
                {Util.dtToDMY(entity?.exp_date)}
              </span>
              )
            </div>
          </Space>
        );
      },
    },
    {
      title: 'Multi',
      dataIndex: ['multi_summary'],
      sorter: true,
      width: 350,
      className: 'p-0',
      align: 'center',
      render(__, entity) {
        if (entity.item_ean?.is_single) return null;

        return (
          <Space direction="vertical" size={4} style={{ textAlign: 'left', width: '100%' }}>
            <Row gutter={0} wrap={false}>
              <Col flex="0 0 105px">
                <SkuComp sku={entity.item_ean?.sku} style={{ color: '#000' }} />
              </Col>
              <Col flex="auto">
                <Typography.Text copyable style={{ color: entity.item_ean?.is_single ? 'blue' : '#000' }}>
                  {entity.parent_item_ean?.ean}
                </Typography.Text>
              </Col>
              {!entity.item_ean?.is_single && (
                <Col flex="0 0 120px">
                  <Typography.Text copyable style={{ color: 'blue' }}>
                    {entity.item_ean?.ean}
                  </Typography.Text>
                </Col>
              )}
            </Row>
            <div>{entity.item_ean?.ean_text_de?.name ?? entity.item_ean?.item?.name ?? ''}</div>
          </Space>
        );
      },
    },
  ];

  return (
    <div style={{ margin: -24 }}>
      <ProTable<API.StockStable, API.PageParams>
        headerTitle={
          <Space size={48}>
            <div>{props.route.name}</div>

            <div>
              <WarehouseLocationSelector
                defaultValue={Util.getSfValues('sf_stock_stable_mobile')?.wl_name || ''}
                onChange={(value) => {
                  if (value.length >= 4) {
                    console.log('--> conChanged: ', value);
                    searchFormRef.current?.setFieldValue('wl_name', value);
                    actionRef.current?.reload();
                  }
                }}
              />
            </div>

            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              size="large"
              style={{ display: 'none' }}
              initialValues={Util.getSfValues('sf_stock_stable_mobile')}
              submitter={{
                searchConfig: { submitText: 'Search' },
                submitButtonProps: { loading, htmlType: 'submit' },
                onSubmit: (values) => actionRef.current?.reload(),
                onReset: (values) => actionRef.current?.reload(),
              }}
            >
              <ProFormText
                name={'wl_name'}
                label="Location"
                width={'sm'}
                placeholder={'Location. e.g. B0001'}
                requiredMark
              />
            </ProForm>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        size="middle"
        scroll={{ x: 800 }}
        rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        onChange={handleTableChange}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          if (!searchFormValues.wl_name) {
            return Promise.resolve([]);
          }

          Util.setSfValues('sf_stock_stable_mobile', searchFormValues);

          if (searchFormValues.exp_date_start) {
            searchFormValues.exp_date_start = Util.dtToYMD(searchFormValues.exp_date_start);
          } else {
            searchFormValues.exp_date_start = undefined;
          }
          if (searchFormValues.exp_date_end) {
            searchFormValues.exp_date_end = Util.dtToYMD(searchFormValues.exp_date_end);
          } else {
            searchFormValues.exp_date_end = undefined;
          }
          setLoading(true);
          return getStockStable(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              sort_mode: 'stock_stable_mobile',
            },
            sort,
            filter,
          )
            .then((res) => {
              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.StockStable) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        columns={columns}
        tableAlertRender={false}
        rowSelection={{
          fixed: true,
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
        columnEmptyText=""
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;Stocks.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popover
            title="Move Stocks"
            trigger="click"
            open={openBulkStockMoveForm}
            onOpenChange={(visible) => {
              setOpenBulkStockMoveForm(visible);
            }}
            content={
              <ProForm<BulkStockMoveFormValueType>
                size="large"
                formRef={bulkStockMoveFormRef}
                onFinish={async (values) => {
                  if (!values.new_wl_name) {
                    message.error('Please fill WL Name!');
                    return;
                  }
                  const hide = message.loading('Moving stocks...', 0);
                  stockStableBulkMove2TargetByName(
                    selectedRows.map((x) => ({
                      id: x.id,
                      ean_id: x.ean_id,
                      wl_id: x.wl_id,
                      exp_date: x.exp_date,
                      ibo_id: x.ibo_id,
                      piece_qty: x.piece_qty,
                      box_qty: x.box_qty,
                    })),
                    values.new_wl_name,
                  )
                    .then((res) => {
                      message.success('Moved successfully.');
                      setOpenBulkStockMoveForm(false);
                      setSelectedRows([]);
                      bulkStockMoveFormRef.current?.resetFields();
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Move' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                {/* <ProFormSelect
                  name="new_wl_id"
                  rules={[
                    {
                      required: true,
                      message: 'New Warehouse Location is required',
                    },
                  ]}
                  request={(params) => {
                    return getWarehouseLocationACList(params).catch(Util.error);
                  }}
                  width="md"
                  label="New Location"
                  placeholder="New Location"
                  showSearch
                /> */}

                <ProFormText
                  name="new_wl_name"
                  rules={[
                    {
                      required: true,
                      message: 'New Warehouse Location is required',
                    },
                  ]}
                  width="md"
                  label="New Location"
                  placeholder="New Location"
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              size="large"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenBulkStockMoveForm(true)}
            >
              Move Stocks
            </Button>
          </Popover>
        </FooterToolbar>
      )}
      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />
      <SelectIboModal
        modalVisible={openSelectIboModalVisible}
        handleModalVisible={setOpenSelectIboModalVisible}
        initialValue={{
          id: sn(currentRow?.id),
          ibo_id: currentRow?.ibo_id,
          itemEan: currentRow?.item_ean,
        }}
        selectCallback={(data: API.StockStable) => {
          actionRef.current?.reload();
          setOpenSelectIboModalVisible(false);
        }}
      />

      <ExportPdfStockStableSettingFormModal
        offer={currentRow}
        modalVisible={openExportPdfForm}
        handleModalVisible={setOpenExportPdfForm}
        getParentParams={() => {
          return {
            ssIds: selectedRows?.length ? selectedRows.map((x) => x.id) : null,
            ...searchFormRef.current?.getFieldsValue(),
          };
        }}
      />

      <StockStableQtyUpdateFormModal
        initialValues={currentRow}
        modalVisible={openStockStableQtyUpdateFormModal}
        handleModalVisible={setOpenStockStableQtyUpdateFormModal}
        onSubmit={(__) => {
          actionRef.current?.reload();
          setOpenStockStableQtyUpdateFormModal(false);
        }}
        formSize="large"
      />

      <StockStableUpdateExpForm
        modalVisible={expDateModalVisible}
        handleModalVisible={handleExpDateModalVisible}
        ean={currentRow?.item_ean}
        initialData={currentRow || {}}
        onSubmit={async () => {
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleExpDateModalVisible(false);
        }}
        formSize="large"
      />

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.item_ean?.id && <ImportedPrices itemEan={currentRow?.item_ean} />}
      </Drawer>
    </div>
  );
};

export default StockStableListMobile;
