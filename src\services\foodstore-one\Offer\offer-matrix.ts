import { request } from "umi";
import { paramsSerializer } from "../api";

const urlPrefix = '/api/offer';


/** get GET /api/offer/getOfferMatrixExt */
export async function getOfferMatrixExt(params?: API.PageParams) {
    return request<API.ResultObject<API.PaginatedResult<API.Offer> & { ibo_pre_managements: API.IboPreManagement[] }>>(`${urlPrefix}/getOfferMatrixExt`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params?.pageSize || 100,
            page: params?.current || 1,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
        ibo_pre_managements: res.message.ibo_pre_managements,
    }));
}