import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { message, Modal, Spin } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea, ProFormUploadButton } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';

import Util, { sn } from '@/util';
import {
  deleteIboPreManagementPicture,
  getIboPreManagement,
  updateIboPreManagement,
} from '@/services/foodstore-one/IBO/ibo-pre-management';
import { UploadChangeParam } from 'antd/lib/upload';
import { LS_TOKEN_NAME } from '@/constants';

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    const res = await updateIboPreManagement(id, fields);
    message.success('Updated successfully.');
    return res;
  } catch (error) {
    Util.error(error);
    return null;
  } finally {
    hide();
  }
};

type FormValueType = API.IboPreManagement;

export type UpdateNoteAndPictureFormProps = {
  initialValues?: API.IboPreManagement;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPreManagement) => Promise<boolean | void>;
};

const UpdateNoteAndPictureForm: React.FC<UpdateNoteAndPictureFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (formRef.current && modalVisible) {
      const newValues = { ...(initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [initialValues, modalVisible]);

  const loadDetail = useCallback(() => {
    if (initialValues?.id) {
      setLoading(true);
      getIboPreManagement(initialValues.id, { with: 'files' })
        .then((res) => {
          formRef.current?.setFieldsValue({ files: res.files });
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [initialValues?.id]);

  useEffect(() => {
    if (modalVisible) {
      loadDetail();
    }
  }, [loadDetail, modalVisible]);

  return (
    <ModalForm
      title={`Update Pre IBO Management - #${initialValues?.id}`}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 5 }}
      wrapperCol={{ span: 19 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: initialValues?.id,
        };
        const res = await handleUpdate(sn(initialValues?.id), data);

        if (res) {
          handleModalVisible(false);
          if (onSubmit) onSubmit({ ...res });
        }
      }}
      modalProps={{ okText: 'Save' }}
    >
      <Spin spinning={loading}>
        <ProFormTextArea name="note2" label="Note 2" fieldProps={{ rows: 10 }} />

        <ProFormUploadButton
          name="files"
          label="Pictures"
          title="Select File"
          accept="image/*"
          action={`${API_URL}/api/ibo/ibo-pre-management/upload-picture/${initialValues?.id}`}
          fieldProps={{
            headers: {
              Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
            },
            listType: 'picture-card',
            beforeUpload: async (file, fList) => {
              return Promise.resolve(file);
            },
            onChange: (info: UploadChangeParam, updateState = true) => {
              if (info.file.status == 'done') {
                info.file.url = info.file.response.url;
                info.file.uid = info.file.response.uid;
                (info.file as any).id = info.file.response.uid;
                (info.file as any).file_name = info.file.response.file_name;
                (info.file as any).clean_file_name = info.file.response.clean_file_name;
                (info.file as any).path = info.file.response.path;
                (info.file as any).org_path = info.file.response.org_path;
                (info.file as any).pivot = info.file.response.pivot;

                const newFiles = [...info.fileList];
                formRef.current?.setFieldsValue({ files: newFiles });
              }
            },
            onRemove: async (file: API.File) => {
              if (!file.id) return Promise.resolve(true);
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to delete?',
                  onOk: async () => {
                    resolve(true);
                    const hide = message.loading('Deleting...', 0);
                    const res = await deleteIboPreManagementPicture(file.id)
                      .then((re) => {
                        message.success('Deleted successfully.');
                        return re;
                      })
                      .catch(Util.error)
                      .finally(() => hide());
                    return res?.message;
                  },
                  onCancel: () => {
                    reject(true);
                  },
                });
              });
            },
          }}
        />
      </Spin>
    </ModalForm>
  );
};

export default UpdateNoteAndPictureForm;
