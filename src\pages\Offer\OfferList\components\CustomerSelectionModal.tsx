/* eslint-disable @typescript-eslint/dot-notation */
import { Modal, Typography, Button, Space, Card } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { sn, sUrlByTpl } from '@/util';
import { DictCode, MagentoQuoteStatusEnum } from '@/constants';
import { getQuoteCustomersListByPage, getQuoteStatusACList } from '@/services/foodstore-one/Magento/quote';
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { DefaultOptionType } from 'antd/lib/select';
import { useModel } from 'umi';

export type SearchFormValueType = Partial<API.MagQuote>;

type CustomerSelectionModalProps = {
  offer?: API.Offer;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSelect: (customer?: API.MagQuote) => void;
};

const CustomerSelectionModal: React.FC<CustomerSelectionModalProps> = (props) => {
  const { modalVisible, handleModalVisible, onSelect, offer } = props;

  const { getDictByCode } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [statusACList, setStatusACList] = useState<(DefaultOptionType & { cnt?: number })[]>([]);

  const columns: ProColumns<API.MagQuote>[] = [
    /* {
      title: 'Quote ID',
      dataIndex: 'id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      width: 55,
      render(dom, entity) {
        return (
          <Typography.Link
            href={sUrlByTpl(getDictByCode(DictCode.MAG_ADMIN_URL_QUOTE), {
              quote_id: entity.id,
            })}
            title="Go to FsOne Shop admin page."
            target="_blank"
          >
            {dom}
          </Typography.Link>
        );
      },
    }, */
    {
      title: 'Name',
      dataIndex: 'customer_fullname',
      width: 100,
      render: (dom, record) => `${record.customer_firstname} ${record.customer_lastname}`,
    },
    {
      title: 'Email',
      dataIndex: 'customer_email',
      width: 100,
    },
    {
      title: '',
      valueType: 'option',
      width: 80,
      showSorterTooltip: false,
      fixed: 'right',
      render: (__, record) =>
        offer?.customer_id != record.customer_id ? (
          <Button
            type="primary"
            size="small"
            onClick={() => {
              onSelect({ id: record.customer_id } as API.MagQuote);
              handleModalVisible(false);
            }}
          >
            Select
          </Button>
        ) : (
          <Button
            type="primary"
            ghost
            size="small"
            onClick={() => {
              onSelect({} as API.MagQuote);
              handleModalVisible(false);
            }}
          >
            Deselect
          </Button>
        ),
    },
  ];

  const loadStatusACList = () => {
    getQuoteStatusACList()
      .catch((err) => {
        Util.error(err);
        return [];
      })
      .then((res) => setStatusACList(res));
  };

  useEffect(() => {
    loadStatusACList();
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_customer_selection', {}));
  }, []);

  return (
    <Modal
      title={
        <>
          Select a Customer
          {offer
            ? ` for #${offer.offer_no || '-'} | ${Util.dtToDMY(offer.created_on)}${
                offer.note ? ` | ${offer.note}` : ''
              }${offer.quote?.customer_fullname ? ` - ${offer.quote?.customer_fullname}` : ''}${
                offer.ibo_status ? ` (${offer.ibo_status})` : ''
              }`
            : ''}
        </>
      }
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="1200px"
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <Card style={{ marginBottom: 0 }} bordered={false} size="small">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          size="small"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name={'status'}
            label="Quote Status"
            allowClear
            options={statusACList}
            placeholder={'Status'}
            width={'sm'}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name={'keyWords'} label="KeyWords" width={'md'} placeholder={'Email / Name / Customer notes'} />
          <ProFormText name={'id'} label="Quote ID" width={'xs'} placeholder={'Quote ID'} />
        </ProForm>
      </Card>

      <ProTable<API.MagQuote, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Quotes List</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{ defaultPageSize: sn(Util.getSfValues('sf_customer_selection_p')?.pageSize ?? 20) }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_customer_selection', searchFormValues);
          Util.setSfValues('sf_customer_selection_p', params);

          return getQuoteCustomersListByPage(
            {
              ...params,
              sort: { ...sort, id: 'descend' },
              with: 'store',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          );
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default CustomerSelectionModal;
