<?php
/**
 *
 * Down syncing GDSN message item's detail3 data (farming origin, BIO certificate) from WHC GDSN server.
 *
 *
 * - has a feature of singleton of cron. Lock file name which contains PID is "ds-whc-gdsn3-sync.lock"
 *
 * - save EAN data in `detail3` field and fill `parse_version` with 3.
 *
 * @package     Cron job script.
 * @since       2025-03-31
 */

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Magento\MagSyncLog;
use App\Service\EanApi\EanApiBaseService;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

$lockFilePath = PRIVATE_DATA_PATH . DS.  "ds-whc-gdsn3-sync.lock";
$pageSize = 400;

// Func::getLogger()->error("Sync GDSN3 PID: " . getmypid());

/** @var \Slim\Container $container */
/** @var EanApiBaseService $eanApiBaseService */
$eanApiBaseService = $container->get(EanApiBaseService::class);

// singleton check from lock file.
if (DIRECTORY_SEPARATOR !== '\\') {
    try {
        if( $pidsOrFalse = Func::isProcessLocked($lockFilePath) ) {
            // die("Already running.\n");
            FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_DOWN, MagSyncLog::NAME_GDSN_SYNC_PROCESS_RUNNING3, json_encode($pidsOrFalse));
            die();
        }
    } catch (Exception $exception) {
        Func::getLogger()->error($exception->getMessage());
        throw $exception;
    }
}

// Get the aggregated messages items
try {
    $eanApiBaseService->dsMessageItemsDetail3(['pageSize' => $pageSize]);
} catch (Exception $exception) {
    Func::getLogger()->error($exception->getMessage() . $exception->getTraceAsString());
}








