ALTER TABLE `offer`
    CHANGE `ibo_status` `ibo_status` ENUM (
        'open',
        'expecting items',
        'closed',
        'in discussion',
        'in preparation',
        'paid',
        'closed lost'
        ) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'open' NULL COMMENT 'Status for IBO. open,expecting items,closed';


ALTER TABLE `offer_item`
    ADD COLUMN `price_stable` DECIMAL (20, 4) NULL COMMENT 'Special GFC Net Price' AFTER `price_special`;


ALTER TABLE `item`
    ADD COLUMN `fs_bio_certificate` VARCHAR (255) NULL AFTER `special_filter`,
    ADD COLUMN `fs_bio_origin` VARCHAR (255) NULL AFTER `fs_bio_certificate`;


ALTER TABLE `item_ean_gdsn`
    ADD COLUMN `parse_version` TINYINT default 2 COMMENT 'Parser version of GDSN message' AFTER `contacts`;
