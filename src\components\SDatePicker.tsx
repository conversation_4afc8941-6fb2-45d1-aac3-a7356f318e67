import { DT_FORMAT_DMY, DT_FORMAT_MY } from '@/constants';
import Util from '@/util';
import type { ProFormItemProps } from '@ant-design/pro-form';
import { ProFormDatePicker } from '@ant-design/pro-form';
import type { DatePickerProps } from 'antd';

import deDE from 'antd/lib/locale-provider/de_DE';

import moment from 'moment';

moment.locale('de', {
  week: {
    dow: 1, /// Date offset
  },
});

const SDatePicker = (props: ProFormItemProps<DatePickerProps, any>) => {
  return (
    <ProFormDatePicker
      {...props}
      fieldProps={{
        ...(props?.fieldProps || {}),
        locale: deDE.DatePicker,
        format: props?.fieldProps?.picker === 'month' ? DT_FORMAT_MY : DT_FORMAT_DMY,
      }}
      getValueFromEvent={(value) =>
        props?.fieldProps?.picker === 'month' ? Util.dtToYMD(value, DT_FORMAT_MY) : Util.dtToYMD(value)
      }
    />
  );
};

export default SDatePicker;
