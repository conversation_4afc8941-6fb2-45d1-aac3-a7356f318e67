ALTER TABLE `stock_stable_us_log`
    ADD COLUMN `snapshot_input` LONGTEXT NULL COMMENT 'Details info in JSON' AFTER `last_us_qty`;

ALTER TABLE `stock_stable_us_log`
    ADD COLUMN `snapshot_output` LONGTEXT NULL COMMENT 'Details info in JSON' AFTER `snapshot_input`;

ALTER TABLE `xmag_ds_stat`
    ADD COLUMN `detail` LONGTEXT NULL COMMENT 'Details info in JSON' AFTER `sync_count`;

ALTER TABLE `xmag_ds_stat`
    CHANGE `type` `type` VARCHAR(31) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'base' NOT NULL;
