<?php

use App\App\Constants;
use App\Lib\Func;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

$id = 444;
$email  = '<EMAIL>';

$url = '?id=' . Func::mcrypt('encrypt', $id, Constants::SEC_KEY_FILE) .'&src=' . Func::mcrypt('encrypt', $email, Constants::SEC_KEY_FILE);

print_r($url);
exit;
// Make 1 X 1 PNG image.
$im = imagecreatetruecolor(1, 1);
$bgc = imagecolorallocate($im, 255, 255, 255);
imagefilledrectangle($im, 0, 0, 1, 1, $bgc);


imagepng($im, APP_PATH . DS . 'pixel.png');

