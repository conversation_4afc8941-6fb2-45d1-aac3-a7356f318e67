/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/stock/stock-stable-problem';

/** 
 * Get stock stable problem list
 * 
 * GET /api/stock/stock-stable-problem */
export async function getStockStableProblemList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * Get stock stable problem list
 * 
 * GET /api/stock/stock-stable-problem/getOneByParam */
export async function getStockStableProblemByParam(params?: API.StockStableProblem) {
  return request<API.ResultObject<API.StockStableProblem>>(`${urlPrefix}/getOneByParam`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** post POST /api/stock/stock-stable-problem/createOrUpdate */
export async function createOrUpdateStockStableProblem(data: API.StockStableProblem & { qty_picked?: number }, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/createOrUpdate`, {
    method: 'POST',
    data: data,
    ...(options || {}),
    paramsSerializer,
  }).then(res => res.message);
}

/**
 * update stockStable data
 *
 * PUT /api/stock/stock-stable-problem/{id}
 */
export async function updateStockStableProblem(id: number, data: Partial<API.StockStableProblem>, options?: { [key: string]: any }) {
  return request<API.StockStableProblem>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}


/** post POST /api/stock/stock-stable-problem */
export async function addStockStableProblem(data: API.StockStableProblem, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}


/** delete DELETE /api/stock/stock-stable-problem/{id} */
export async function deleteStockStableProblem(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export async function exportStockStableProblemList(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}


