<?php
/**
 * Parsing the GDSN full XML file.
 *
 */
use App\Lib\Func;
use App\Service\EanApi\EanApiBaseService;
use App\Service\Gdsn\GdsnMessageItemService;
use DiDom\Document;

error_reporting(E_ALL);
require __DIR__ . '/../../src/App/App.php';

/** @var \App\Repository\Import\ImportRepository $repo */
$repo = Func::getContainer()->get(\App\Repository\Import\ImportRepository::class);


// $xmlPath = "F:\\htdocs\\whc-foodstore-backend\\extras\\dev\\test-4250448329522.xml"; // OK
// $xmlPath = "F:\\htdocs\\whc-foodstore-backend\\extras\\dev\\test-4062300398917.xml";
$xmlPath = "F:\\htdocs\\whc-foodstore-backend\\extras\\dev\\test-4008100169302.xml";

/** @var GdsnMessageItemService $service */
$service = Func::getContainer()->get(GdsnMessageItemService::class);

$data = $service->parseGdsnTradeItemDetail3Xml(file_get_contents($xmlPath));

print_r($data);


/** @var EanApiBaseService $eanApiService */
$eanApiService = Func::getContainer()->get(\App\Service\EanApi\EanApiBaseService::class);

$result = $eanApiService->dsMessageItemsDetail3(['gdsn_item_id'=> 17082]);

/*
$xmlPath = "F:\\htdocs\\whc-foodstore-backend\\extras\\dev\\test-4250448329522.xml";

$d = new Document();
$d->loadXmlFile($xmlPath, LIBXML_PARSEHUGE);

$transactions = $d->find("//transaction", \DiDom\Query::TYPE_XPATH);
// print_r($transactions);

foreach ($transactions as $t) {
    $transactionId = $t->xpath("//transactionIdentification/entityIdentification")[0]->text();
    $contentOwner = $t->xpath("//transactionIdentification/contentOwner/gln")[0]->text();
    printf("%- 20s%s\n", "UID", $transactionId);
    printf("%- 20s%s\n", "contentOwner", $contentOwner);

    $itemNotification = $t->xpath("//documentCommand/catalogue_item_notification:catalogueItemNotification")[0];
    printf("%- 20s%s\n", "creationDateTime", $itemNotification->first('creationDateTime')->text());
    printf("%- 20s%s\n", "documentStatusCode", $itemNotification->first('documentStatusCode')->text());
    printf("%- 20s%s\n", "lastUpdateDateTime", $itemNotification->first('lastUpdateDateTime')->text());
    printf("%- 20s%s\n", "isReload", $itemNotification->first('isReload')->text());

    printf('------------------------------' . PHP_EOL);
    $item = $itemNotification->first('catalogueItem');
    printf("%- 20s%s\n", "dataRecipient", $item->first('dataRecipient')->text());
    printf("%- 20s%s\n", "sourceDataPool", $item->first('sourceDataPool')->text());


    printf('------------------------------' . PHP_EOL);
    $tradeItem = $itemNotification->first('tradeItem');
    $tradeItemGtin = $tradeItem->first('gtin')->text();
    printf("%- 20s%s\n", "EAN", $tradeItemGtin);
    $provider = $tradeItem->xpath("//informationProviderOfTradeItem/gln")[0]->text();
    $providerName = $tradeItem->xpath("//informationProviderOfTradeItem/partyName")[0]->text();
    printf("%- 20s%s, %s\n", "Provider", $provider, $providerName);

    $tradItemCategory = [];
    if ($tradeItem->has('gdsnTradeItemClassification')) {
        $tmp = $tradeItem->xpath("//gdsnTradeItemClassification");
        $tradItemCategory['gpcCategoryCode'] = $tradeItem->first('gpcCategoryCode')->text();
        $tradItemCategory['gpcCategoryDefinition'] = $tradeItem->first('gpcCategoryDefinition')->text();
        $tradItemCategory['gpcCategoryName'] = $tradeItem->first('gpcCategoryName')->text();
    }
    print_r($tradItemCategory);

    printf('------------------------------' . PHP_EOL);
    $lowTradeItems = [];
    foreach ($tradeItem->xpath("//nextLowerLevelTradeItemInformation/childTradeItem") as $info) {
        $lowTradeItems[] = [
            'ean' => $info->child(0)->text(),
            'qty' => $info->child(1)->text(),
        ];
    }
    print_r($lowTradeItems);


    print_r($itemNotification->first("creationDateTime")->text() . PHP_EOL);
    foreach ($itemNotification->children() as $x) {
        print_r($x->getNode()->nodeName . ':' . $x->getNode()->nodeValue . PHP_EOL);
    }
    break;
}*/








/*print_r(\App\Models\User::query()->count());
print_r(Func::getDb(GDSN)->table('contact')->first());
print_r(\App\ModelsGdsn\Contact::query()->first()->toArray());*/

// $xml = simplexml_load_file($xmlPath, null) or die("Error: Cannot create object");
// print_r($xml->transaction[0]->documentCommand);

/*$doc = new DOMDocument();
$doc->load($xmlPath);
$xpath = new DOMXPath($doc);

$transactions = $doc->getElementsByTagName('transaction');

$transaction = $transactions->item(0);
print_r($transaction->);*/

// print_r($transactions[0]);


