import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Button, Col, Divider, Row, Tag, Typography, message } from 'antd';
import Util, { sn } from '@/util';
import { getOrderShippingProviderList } from '@/services/foodstore-one/BasicData/order-shipping-provider';
import { createOrderShipment, updateOrderExtra } from '@/services/foodstore-one/Magento/order';
import { ShipmentCarrior } from '@/constants';
import { useModel } from 'umi';

const handleUpdate = async (fields: API.OrderExtra) => {
  const hide = message.loading("Updating order's shipping setting...", 0);
  const data = { ...fields };
  try {
    await updateOrderExtra(sn(data.entity_id), data);
    message.success('Updated successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.OrderExtra>;

export type UpdateOrderExtraFormModalProps = {
  order?: Partial<API.Order>;
  values?: Partial<API.OrderExtra>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderExtra) => Promise<boolean | void>;
};

const UpdateOrderExtraFormModal: React.FC<UpdateOrderExtraFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, order, values } = props;

  const { getParcelUrl } = useModel('app-settings');

  // const [shippingImportedList, setShippingImportedList] = useState<API.WarehousePicklistShippingImported[]>([]);

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(values || {}) };
      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, values]);

  /* useEffect(() => {
    if (modalVisible && order?.entity_id) {
      getWarehousePicklistShippingImportedListById(order.entity_id)
        .then((res) => setShippingImportedList(res))
        .catch(Util.error);
    }
  }, [modalVisible, order?.entity_id]); */

  return (
    <ModalForm
      title={'Update Order Shipping Setting | Create Shipment'}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, entity_id: values?.entity_id });
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          handleModalVisible(false);
          if (onSubmit) await onSubmit({ ...value, entity_id: values?.entity_id });
        }
      }}
    >
      {order?.sa_company && (
        <Row style={{ marginBottom: 8 }}>
          <Col span={6}>Company:</Col>
          <Col span={18}>{order?.sa_company || '-'}</Col>
        </Row>
      )}
      <Row style={{ marginBottom: 8 }}>
        <Col span={6}>Name:</Col>
        <Col span={18}>{order?.sa_fullname || '-'}</Col>
      </Row>
      <Row style={{ marginBottom: 24 }}>
        <Col span={6}>Address:</Col>
        <Col span={18}>{order?.sa_full || '-'}</Col>
      </Row>

      <Row style={{ marginBottom: 24 }}>
        <Col span={6}>Original Provider:</Col>
        <Col span={18}>
          <Tag>{values?.shipping_provider_name_old || 'N/A'}</Tag> ({order?.shipping_description || 'N/A'})
        </Col>
      </Row>
      <Row style={{ marginBottom: 24 }}>
        <Col span={6}>Weight:</Col>
        <Col span={18}>{Util.numberFormat(order?.weight || 0, true, 5, true)}kg</Col>
      </Row>

      <ProFormSelect
        width="sm"
        name="shipping_provider_name"
        label="Shipping Provider"
        request={async (params) => {
          return getOrderShippingProviderList(params, {}, {}).then((res) =>
            res.data.map((x) => ({ value: x.name, lable: x.name })),
          );
        }}
      />

      <ProFormText
        width="lg"
        name="shipping_provider_change_notes"
        label="Notes"
        help="Reason for shipping provider change."
      />

      {values?.shipping_address_check_detail ? (
        <Row style={{ marginTop: 48 }}>
          <Col span={6}>Address validation: </Col>
          <Col span={18}>
            {values?.shipping_address_check_detail?.split('^').map((x, i) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={`${i}_${x}`} className="c-red">
                {x}
              </div>
            ))}
          </Col>
        </Row>
      ) : null}

      {!!order?.latest_shipping ? (
        <Row style={{ marginTop: 48 }}>
          <Col span={6}>Parcel: </Col>
          <Col span={18}>
            {[order?.latest_shipping]?.map((x) => {
              return (
                <Row key={x.id} style={{ marginTop: 8 }}>
                  <Col span={8}>
                    <Typography.Text copyable={{ text: x.parcel_no ?? '' }}>
                      <a
                        href={getParcelUrl(x.parcel_no)}
                        target="_blank"
                        rel="noreferrer"
                        className={x.mag_ship_id ? 'c-green' : ''}
                      >
                        {x.parcel_no}
                      </a>
                    </Typography.Text>
                  </Col>
                  <Col span={8}>{x.title}</Col>
                  <Col span={8}>{x.carrier_code}</Col>
                </Row>
              );
            })}
          </Col>
        </Row>
      ) : null}

      <Divider orientation="left">Create shipment</Divider>

      <ProFormText name={['shipment', 'parcel_no']} width="sm" label="Parcel No" placeholder="Parcel No" />
      <ProFormSelect
        width="sm"
        name={['shipment', 'carrier_code']}
        label="Shipping Carrier"
        options={Object.keys(ShipmentCarrior).map((x) => ({ value: x, label: ShipmentCarrior[x] ?? '' }))}
      />
      <Row>
        <Col span={18} offset={6}>
          <Button
            type="primary"
            size="small"
            htmlType="button"
            onClick={() => {
              if (!order?.entity_id) {
                message.error('No order ID selected!');
                return;
              }

              const shipmentData = formRef.current?.getFieldValue(['shipment']);
              console.log('shipment', shipmentData);
              if (!shipmentData.parcel_no || !shipmentData.carrier_code) {
                message.error('Please fill Parcel No and select Shipping Carrier');
                return;
              }

              const hide = message.loading('Creating an order shipment...', 0);
              createOrderShipment(order?.entity_id, shipmentData)
                .then((res) => {
                  message.success('Created successfully.');
                  handleModalVisible(false);
                  onSubmit?.({ ...values, entity_id: values?.entity_id });
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            Create Shipment
          </Button>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default UpdateOrderExtraFormModal;
