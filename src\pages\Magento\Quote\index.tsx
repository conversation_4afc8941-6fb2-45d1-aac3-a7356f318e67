import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, message, Popover, Row, Space, Tag, Typography } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { DEFAULT_PER_PAGE_PAGINATION, DictCode, EURO, MagentoQuoteStatusEnum, MagentoQuoteStatusKv } from '@/constants';
import Util, { nf2, ni, skuToItemId, sn, sShortImportDbTableName, sUrlByTpl } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { useLocation, useModel } from 'umi';
import { ExportOutlined, LinkOutlined, SyncOutlined } from '@ant-design/icons';
import { isArray } from 'lodash';
import {
  copyToOffer,
  exportQuotesList,
  getQuotesListByPage,
  getQuoteStatusACList,
} from '@/services/foodstore-one/Magento/quote';
import { dsQuotes } from '@/services/foodstore-one/api';
import _ from 'lodash';
import { calcCheapestXlsPrice } from '@/pages/Item/EanList/EanAllPrices';
import useOfferOptions from '@/hooks/BasicData/useOfferOptions';
const Text = Typography.Text;

type ExportForm = {
  include_bp?: boolean;
};

export const FullAddress: React.FC<{ order: API.Order; type?: 'shipping' | 'invoice' }> = ({ order, type }) => {
  if (type == 'shipping') {
    let addr = <></>;
    // if (order.sa_fullname) addr += order.sa_fullname;
    if (order.sa_street)
      addr = (
        <>
          {addr}{' '}
          <Text mark={order.warn_sa_street || order.warn_sa_street_long || order.warn_sa_street_no}>
            {order.sa_street}
          </Text>
        </>
      );
    if (order.sa_city)
      addr = (
        <>
          {addr}, <Text>{order.sa_city}</Text>
        </>
      );
    if (order.sa_zip)
      addr = (
        <>
          {addr}, <Text mark={order.warn_sa_zip || order.warn_sa_zip_de_wrong}>{order.sa_zip}</Text>
        </>
      );
    if (order.sa_country_code)
      addr = (
        <>
          {addr}, <Text mark={order.warn_sa_zip || order.warn_sa_zip_de_wrong}>{order.country?.name || ''}</Text>
        </>
      );
    return addr;
  } else {
    let addr = '';
    if (order?.detail?.billing_address) {
      const addrObj = order.detail.billing_address;
      // if (addrObj.sa_fullname) addr += addrObj.sa_fullname;
      if (addrObj.street) addr += ' ' + (isArray(addrObj.street) ? addrObj.street.join(', ') : addrObj.street);
      if (addrObj.city) addr += ', ' + addrObj.city;
      if (addrObj.postcode) addr += ', ' + addrObj.postcode;
      if (addrObj.country_id) addr += ', ' + addrObj.country_id;
    }
    return <>{addr}</>;
  }
};

export const MagentoQuoteStatus: React.FC<{ status?: MagentoQuoteStatusEnum | string | number }> = ({ status }) => {
  let color = 'default';
  if (status || status === 0) {
    switch (status) {
      case MagentoQuoteStatusEnum.Pending:
        color = 'orange';
        break;
      case MagentoQuoteStatusEnum.Cancel:
        color = 'red';
        break;
      default:
        break;
    }
  }
  return <Tag color={color as any}>{MagentoQuoteStatusKv[status ?? '-'] ?? status}</Tag>;
};

export type SearchFormValueType = Partial<API.MagQuote>;
export type OfferSelectionFormValueType = { offer_id?: number };

export const ExportButtonComp: React.FC<{
  searchFormRef: React.MutableRefObject<ProFormInstance | undefined>;
  setLoading: Dispatch<SetStateAction<boolean>>;
  id: number;
}> = ({ searchFormRef, setLoading, id }) => {
  // Export Option Form
  const exportFormRef = useRef<ProFormInstance<ExportForm>>();
  const [openExportForm, setOpenExportForm] = useState<boolean>(false);

  return (
    <Popover
      key="export-xls"
      placement="top"
      title="Select Export Mode"
      trigger="click"
      open={openExportForm}
      onOpenChange={(visible) => setOpenExportForm(visible)}
      content={
        <ProForm<ExportForm>
          formRef={exportFormRef}
          size="small"
          onFinish={async (values) => {
            // const include_bp = (document.getElementById(`include_bp${id}`) as any).checked;
            setOpenExportForm(false);

            const searchFormValues = searchFormRef.current?.getFieldsValue();
            Util.setSfValues('sf_quotes', searchFormValues);

            const hide = message.loading('Exporting...', 0);
            exportQuotesList({
              with: 'magQuote,itemEan,itemEan.magInventoryStocksQty,itemEan.supplierXlsBps,store,itemEan.ibosItemMinPrice',
              quote_id: id,
              include_bp: exportFormRef.current?.getFieldValue('include_bp'),
              ...Util.mergeGSearch(searchFormValues),
            })
              .then((res) => {
                window.open(`${API_URL}/api/${res.url}`, '_blank');
              })
              .catch(Util.error)
              .finally(() => {
                hide();
                setLoading(false);
              });
          }}
          submitter={{
            searchConfig: { submitText: 'Export' },
            render(__, dom2) {
              return [dom2[1]];
            },
          }}
        >
          <ProFormCheckbox fieldId={`include_bp${id}`} name="include_bp" label="Including Buying Price?" />
        </ProForm>
      }
    >
      <Button
        key={'export quotes'}
        type="primary"
        icon={<ExportOutlined />}
        size="small"
        onClick={() => setOpenExportForm(true)}
      >
        Export
      </Button>
    </Popover>
  );
};

const QuoteList: React.FC = (props) => {
  const location: any = useLocation();

  const { appSettings, setAppSettings, getDictByCode } = useModel('app-settings');
  const quoteIdInUrl = location.query?.id;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [statusACList, setStatusACList] = useState<(DefaultOptionType & { cnt?: number })[]>([]);

  const [xlsImports, setXlsImports] = useState<API.Import[]>([]);

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.MagQuote>();

  // copy (2)
  const [openOfferSelectionForm, setOpenOfferSelectionForm] = useState<any>({});
  const { searchOfferOptions } = useOfferOptions(undefined, searchFormRef);

  const columns: ProColumns<API.MagQuote>[] = [
    {
      title: 'Quote ID',
      dataIndex: 'id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      width: 55,
      render(dom, entity) {
        return (
          <Typography.Link
            href={sUrlByTpl(getDictByCode(DictCode.MAG_ADMIN_URL_QUOTE), {
              quote_id: entity.id,
            })}
            title="Go to FsOne Shop admin page."
            target="_blank"
          >
            {dom}
          </Typography.Link>
        );
      },
    },
    {
      title: 'Store',
      dataIndex: ['store', 'code'],
      align: 'center',
      ellipsis: true,
      className: 'text-sm',
      width: 55,
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      align: 'center',
      ellipsis: true,
      width: 50,
      render: (__, record) => <MagentoQuoteStatus status={record.status ?? 0} />,
    },
    {
      title: 'Items Qty',
      dataIndex: 'items_count',
      align: 'right',
      width: 50,
      tooltip: 'Qty of included items',
      showSorterTooltip: false,
      render(dom, entity) {
        return ni(entity.items_count);
      },
    },
    {
      title: 'Ordered Qty',
      dataIndex: 'items_qty',
      align: 'right',
      width: 50,
      render(dom, entity) {
        return ni(entity.items_qty);
      },
    },

    {
      title: 'Grand Total',
      dataIndex: 'grand_total',
      sorter: true,
      align: 'right',
      width: 65,
      render: (dom, record) => (
        <span style={{ paddingRight: 12 }}>
          {nf2(record.grand_total)}
          {record.grand_total ? EURO : ''}
        </span>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'customer_fullname',
      width: 100,
      //   tooltip: 'Red colored rows are in warnings list. Highlighted parts may be wrong!',
      render: (dom, record) => record.customer_fullname,
    },
    {
      title: 'Email',
      dataIndex: 'customer_email',
      width: 100,
    },
    {
      title: 'Customer Note',
      dataIndex: 'quote_customer_note',
      width: 280,
      className: 'text-sm',
    },
    {
      title: 'Created on',
      dataIndex: ['created_at'],
      sorter: true,
      width: 70,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMMTz(record.created_at),
    },
    {
      title: 'Updated on',
      dataIndex: ['updated_at'],
      sorter: true,
      width: 70,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMYHHMMTz(record.updated_at),
    },
    {
      title: '',
      valueType: 'option',
      width: 200,
      showSorterTooltip: false,
      fixed: 'right',
      render: (__, record) => (
        <Space size={8}>
          <ExportButtonComp id={sn(record.id)} searchFormRef={searchFormRef} setLoading={setLoading} />
          <Typography.Link
            style={{ width: 60, lineHeight: 1.1, display: 'inline-block' }}
            href={`/quotes/offer?quote_id=${record.id}`}
            target="_blank"
          >
            {/* {sn(record.offers_count) || sn(record.copying_history?.length)
              ? sn(record.offers_count) + sn(record.copying_history?.length)
              : ' '}
               */}
            {/* {record.offers?.map((offer) => {
              return (
                <span key={offer.id} style={{ paddingRight: 6 }}>
                  <Typography.Link
                    href={`/quotes/offer?offer_id=${offer.id}&offer_no=${offer.offer_no}`}
                  >{`#${offer.offer_no}`}</Typography.Link>
                </span>
              );
            })} */}
            {record.offers?.map((x) => x.offer_no)?.join(', ')}
          </Typography.Link>
          <Button
            type="primary"
            ghost
            size="small"
            title="Copy to Offer"
            onClick={() => {
              const hide = message.loading('Creating a new offer from this quote...', 0);
              copyToOffer(sn(record.id))
                .then((offer) => {
                  message.success(`Offer #${offer.offer_no} created successfully.`);
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            Create Offer
          </Button>
          <Popover
            title="Move Stocks"
            trigger="click"
            open={!!openOfferSelectionForm[`${record.id}`]}
            onOpenChange={(visible) => {
              setOpenOfferSelectionForm({ [`${record.id}`]: visible });
            }}
            content={
              <ProForm<OfferSelectionFormValueType>
                size="small"
                onFinish={async (values) => {
                  const hide = message.loading('Copying quote to the selected Offer...', 0);
                  copyToOffer(sn(record.id), values)
                    .then((offer) => {
                      message.success(`Copied to Offer #${offer.offer_no} successfully.`);
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Copy' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name="offer_id"
                  rules={[
                    {
                      required: true,
                      message: 'Offer is required!',
                    },
                  ]}
                  request={(params) => {
                    return searchOfferOptions(params).catch(Util.error);
                  }}
                  width="md"
                  label="Offer"
                  placeholder="Offer"
                  showSearch
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              size="small"
              htmlType="button"
              loading={loading}
              disabled={loading}
              ghost
              title="Copy to Existing Offer"
              onClick={() => setOpenOfferSelectionForm({ [`${record.id}`]: true })}
            >
              Insert
            </Button>
          </Popover>
        </Space>
      ),
    },

    /* {
      title: 'Address Status',
      dataIndex: ['extra', 'shipping_address_status'],
      align: 'left',
      width: 100,
      tooltip: 'Click to edit.',
      className: 'bl2',
      render: (dom, record) => (
        <EditableCell
          dataType="select"
          defaultValue={record.extra?.shipping_address_status}
          options={Object.values(ShippingAddressStatus).map(
            (x) => ({ value: x, label: x } as unknown as DefaultOptionType),
          )}
          triggerUpdate={async (newValue: any, cancelEdit) => {
            if (!record.entity_id) return Promise.reject();
            return updateOrderExtra(record.entity_id, {
              shipping_address_status: newValue,
            })
              .then((res) => {
                message.destroy();
                message.success('Updated successfully.');
                actionRef.current?.reload();
              })
              .catch(Util.error)
              .finally(() => cancelEdit?.());
          }}
        >
          {dom}
        </EditableCell>
      ),
    }, */
    /* {
      title: 'Shipping provider (Old)',
      dataIndex: ['extra', 'shipping_provider_name_old'],
      align: 'left',
      width: 100,
      tooltip: 'Original shipping provider',
    }, */
    /* {
      dataIndex: 'option_shipping',
      valueType: 'option',
      width: 40,
      fixed: 'right',
      render(dom, record) {
        return (
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            title="Edit shipping setting..."
            onClick={() => {
              setCurrentRow(record);
              setOpenUpdateOrderExtraModal(true);
            }}
          />
        );
      },
    }, */
  ];

  const expandedRowRender = (record: API.MagQuote) => {
    const xlsColumns: ProColumns<API.MagQuoteItem>[] = xlsImports?.map((x, ind) => {
      return {
        title: (
          <>
            <div style={{ textAlign: 'left' }}>{x.supplier?.name}</div>
            <div style={{ textAlign: 'left', fontSize: 10 }}>
              {x.supplier_add ? x.supplier_add : sShortImportDbTableName(x.table_name, true)}
            </div>
          </>
        ),
        dataIndex: [`xls_bp_${x.id}`],
        sorter: false,
        align: 'right',
        width: 80,
        className: ind == 0 ? 'bl2' : '',
        render: (___, r) => {
          const item_ean: any = r.item_ean ?? {};
          const price = item_ean[`xls_bp_${x.id}`] ?? item_ean[`xls_bp2_${x.id}`];

          return (
            <>
              <div style={{ lineHeight: 1.5, minHeight: 12 }}>{nf2(price)}</div>
            </>
          );
        },
        onCell: (r) => {
          const item_ean: any = r.item_ean ?? {};
          const price = item_ean[`xls_bp_${x.id}`] ?? item_ean[`xls_bp2_${x.id}`] ?? 0;

          const arr = calcCheapestXlsPrice(r.item_ean ?? ({} as any), xlsImports);
          const cheapestPrice = arr[0];
          let cls = '';
          if (cheapestPrice == price && price > 0) {
            /* const bp_pcs = r.qty_total ? r.bp / r.qty_total : 0;
            if (price > bp_pcs) cls = 'bg-light-orange2';
            else if (price < bp_pcs) cls = 'bg-green3'; */
            cls = 'bg-green3';
          }
          return {
            className: cls,
          };
        },
      } as ProColumnType<API.MagQuoteItem>;
    });

    return (
      <div style={{ paddingLeft: 150 }}>
        <ProTable<API.MagQuoteItem>
          cardProps={{ bodyStyle: { padding: 0 } }}
          bordered
          columns={[
            {
              title: 'SKU',
              dataIndex: 'sku',
              width: 220,
              render: (dom, recordIn) => {
                return (
                  <Row wrap={false}>
                    <Col flex="auto">
                      <Typography.Link
                        href={`/item/ean-all-summary?sku=${skuToItemId(recordIn.sku)}_`}
                        target="_blank"
                        copyable
                      >
                        {recordIn.sku}
                      </Typography.Link>
                    </Col>
                    <Col flex="0 1 12px">
                      <a
                        href={`/item/ean-all-prices?sku=${skuToItemId(recordIn.sku)}_`}
                        target="_blank"
                        title="Open EAN price page on new tab."
                        className="text-sm"
                        rel="noreferrer"
                      >
                        <LinkOutlined />
                      </a>
                    </Col>
                  </Row>
                );
              },
            },
            { title: 'EAN', dataIndex: ['item_ean', 'ean'], width: 150 },
            { title: 'Name', dataIndex: 'name', width: 350 },
            {
              title: 'Ordered Qty',
              dataIndex: 'qty',
              align: 'right',
              width: 100,
              render(dom, recordIn) {
                return ni(recordIn.qty);
              },
            },
            {
              title: 'Net Price',
              dataIndex: ['price'],
              align: 'right',
              width: 80,
              render: (dom, recordIn) => nf2(recordIn?.price),
            },
            {
              title: 'Min Buying',
              dataIndex: ['item_ean', 'ibos_item_min_price'],
              align: 'right',
              width: 80,
              render: (dom, recordIn) => nf2(recordIn?.item_ean?.ibos_item_min_price),
            },
            {
              title: 'Note',
              dataIndex: ['customer_note'],
              className: 'text-sm',
              width: 200,
            },
            {
              title: 'Item ID',
              dataIndex: 'item_id',
              align: 'center',
              width: 80,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
            },
            ...xlsColumns,
          ]}
          style={{ width: 1300 }}
          rowKey="item_id"
          headerTitle={false}
          search={false}
          options={false}
          pagination={false}
          size="small"
          dataSource={record.mag_quote_items ?? []}
          columnEmptyText={''}
          rowClassName={(recordIn) => (recordIn?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        />
      </div>
    );
  };

  const loadStatusACList = () => {
    getQuoteStatusACList()
      .catch((err) => {
        Util.error(err);
        return [];
      })
      .then((res) => setStatusACList(res));
  };

  useEffect(() => {
    loadStatusACList();
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_quotes', {}));
  }, []);

  useEffect(() => {
    if (quoteIdInUrl) {
      searchFormRef.current?.setFieldValue('status', null);
      searchFormRef.current?.setFieldValue('sku', null);
      searchFormRef.current?.setFieldValue('ean', null);
      searchFormRef.current?.setFieldValue('id', quoteIdInUrl);
      searchFormRef.current?.submit();
    }
  }, [quoteIdInUrl]);

  const pendingCount = useMemo(() => {
    const pending = statusACList.find((x) => x.value == 'pending');
    return pending ? pending?.cnt : 0;
  }, [statusACList]);

  const [warningsDefCount, setWarningsDefCount] = useState<number>(0);

  // const [selectedRows, setSelectedRows] = useState<API.Order[]>([]);

  return (
    <PageContainer
      extra={
        pendingCount ? (
          <div style={{ position: 'absolute', top: 10, left: '50%', marginLeft: -150 }}>
            <Alert
              message={`WARNING: You have ${pendingCount} pending quotes.`}
              type="error"
              style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
            />
          </div>
        ) : undefined
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name={'status'}
            label="Status"
            allowClear
            options={statusACList}
            initialValue={MagentoQuoteStatusEnum.Pending}
            placeholder={'Status'}
            width={'sm'}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={140} placeholder={'EAN'} />
          <ProFormText name={'id'} label="Quote ID" width={'xs'} placeholder={'Quote ID'} />
          {/* <ProFormText name={'name_address'} label="Name & Address" width={'xs'} placeholder={'Name / Address'} /> */}
          <ProFormCheckbox name="valid_qty_only" label="Has items?" />
          {/* <ProFormCheckbox name="has_warnings" label="Error?" tooltip="Show orders with wrong delivery address" /> */}
        </ProForm>
      </Card>

      <ProTable<API.MagQuote, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Quotes List</span>
          </Space>
        }
        toolBarRender={() =>
          (warningsDefCount
            ? [
                <Alert
                  key="warn-def"
                  message={`WARNING: ${warningsDefCount} invalid delivery addresses detected in red colored rows!`}
                  type="error"
                  style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
                />,
              ]
            : []
          ).concat([
            <Button
              key="down-sync"
              type="primary"
              className="btn-green"
              icon={<SyncOutlined />}
              title={`Down sync quotes. Latest synced on ${
                appSettings?.magDsStat?.xmag_quotebase?.last_sync_at
                  ? Util.dtToDMYHHMM(appSettings?.magDsStat?.xmag_quotebase?.last_sync_at)
                  : '-'
              }`}
              onClick={() => {
                const hide = message.loading('Down Syncing latest quotes...', 0);
                dsQuotes()
                  .then((res) => {
                    setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  })
                  .catch(Util.error)
                  .finally(hide);
              }}
            >
              Sync Quotes
            </Button>,
          ])
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{ defaultPageSize: sn(Util.getSfValues('sf_quotes_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_quotes', searchFormValues);
          Util.setSfValues('sf_quotes_p', params);

          setLoading(true);
          return getQuotesListByPage(
            {
              ...params,
              with: 'magQuoteItems,magQuoteItems.itemEan,itemEan.supplierXlsBps,store,includeImports,itemEan.ibosItemMinPrice,offers.basic,offers_count',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              // calc weight info
              /* res.data.forEach((record) => {
                const map = { weight: 0, zeroCnt: 0 };
                record?.mag_quote_items?.reduce((prev, x) => {
                  x.weight = sn(x.item_ean?.weight) * sn(x.qty_ordered);
                  if (x.weight) prev.weight += x.weight;
                  else prev.zeroCnt++;
                  return prev;
                }, map);
                record.weight_map = map;
              }); */

              setXlsImports(res.imports || []);

              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              //   setWarningsDefCount(res.data.reduce((prev, current) => prev + (current.warnings_def ? 1 : 0), 0));
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        /* onRow={(record) => {
          let cls = '',
            title = '';
          const warning_def = record.warnings_def || '';
          if (warning_def) {
            cls += ' reset-tds-bg bg-red';
            title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
          }
          return { title: title, className: cls };
        }} */
        expandable={{
          expandedRowRender,
          expandRowByClick: false,
          indentSize: 150,
          showExpandColumn: true,
          columnWidth: 16,
          rowExpandable(record) {
            return sn(record?.mag_quote_items?.length) > 0;
          },
        }}
        columnEmptyText=""
      />
    </PageContainer>
  );
};

export default QuoteList;
