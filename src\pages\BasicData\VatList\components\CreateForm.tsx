import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { ProFormDigit, ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { addVat } from '@/services/foodstore-one/BasicData/vat';
import { message } from 'antd';
import Util from '@/util';

const handleAdd = async (fields: API.Vat) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addVat(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Vat) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Vat'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Vat);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormDigit
        rules={[
          {
            required: true,
            message: 'Value is required',
          },
        ]}
        width="sm"
        name="value"
        label="Value"
        addonAfter="%"
        min={0}
        max={100}
      />
      <ProFormDigit
        name="magento_id"
        label="Magento ID"
        placeholder="Magento ID"
        formItemProps={{
          tooltip: 'Please fill ID in Magento shop.',
        }}
      />
    </ModalForm>
  );
};

export default CreateForm;
