import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { message, Button } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanBatch } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _ from 'lodash';
import { useState } from 'react';
import { SaveOutlined } from '@ant-design/icons';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';

const handleUpdate = async (fields: any) => {
  const hide = message.loading('Batch updating categoreis...', 0);

  try {
    await updateEanBatch(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateEanTextProducerFormBulkProps = {
  eanIds: number[];
  vats: API.Vat[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean) => void;
};

const UpdateEanTextProducerFormBulk: React.FC<UpdateEanTextProducerFormBulkProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <ModalForm
      title={<>{`Update ${props.eanIds.length} EANs Text (DE) - Producer`}</>}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      width={400}
      grid
      modalProps={{
        confirmLoading: loading,
        maskClosable: false,
      }}
      formRef={formRef}
      submitter={{
        render: (__, doms) => {
          return (
            <>
              <Button
                type="primary"
                onClick={() => {
                  formRef.current?.setFieldsValue({ mode: 'textsBatch' });
                  formRef.current?.submit();
                }}
                icon={<SaveOutlined />}
              >
                Update
              </Button>
              <Button type="default" key="close" onClick={() => props.handleModalVisible(false)}>
                Close
              </Button>
            </>
          );
        },
      }}
      onFinish={async (value) => {
        if (formRef.current?.isFieldsTouched()) {
          const data = {
            ids: props.eanIds,
            mode: value.mode,
            data: {
              ean_texts: [
                {
                  lang: 'DE',
                  official_producer: value.official_producer,
                },
              ],
            },
          };

          setLoading(true);
          const success = await handleUpdate(data);
          setLoading(false);

          if (success) {
            props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          props.handleModalVisible(false);
        }
      }}
    >
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText name="mode" formItemProps={{ style: { display: 'none' } }} />
        <ProFormSelect
          name={['official_producer']}
          label="Lebensmittelunternehmer"
          tooltip="Official Producer"
          placeholder="Please select producer"
          mode="single"
          showSearch
          request={getProducerListSelectOptions}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateEanTextProducerFormBulk;
