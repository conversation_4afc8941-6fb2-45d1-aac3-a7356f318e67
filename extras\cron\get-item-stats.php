<?php
/**
 *
 * Get item's stats on orders for EAN Prices
 *
 * @deprecated  2025-03-10
 * @package     Cron job script.
 * @since       2025-03-10
 *
 * @recommendedPeriod: a day
 */

use App\Models\ItemStats;
use App\Models\Sys\SysLog;
use App\Service\Item\ItemEan\ItemEanService;
use App\Service\Sys\SysLog\SysLogService;
use Slim\Container;

error_reporting(E_ALL);

require __DIR__ . '/../../extras/cron/_cron_before.php';
/** @var Container $container */

/** @var ItemEanService $service */
$service = $container->get(ItemEanService::class);


$db = $service->getEanRepository()->getDb();

$params['ean_type_search'] = 'base';
$params['with'] = 'fullMode,latestIbo,last30_sales_qty,last365_sales_qty,magInventoryStocksQty,avg_exp_days,last_avg_gps,stockStablesQty,stockRelated,ggp,gogp';
$query = $service->getEanRepository()->getQueryEansByPage($params);

$query->addSelect('item_ean.item_id');
/*$query = $db->query()->fromSub($query, 'item_ean');

$query->select('item_ean.item_id');
$query->selectRaw("last365_sales_qty");
$query->selectRaw("last365_cturover");
$query->selectRaw("last30_sales_qty");
$query->selectRaw("gp_single_gp_avg_365");
$query->selectRaw("gp_single_qty_ordered_sum_365");
$query->selectRaw("gp_single_gp_avg_30");
$query->selectRaw("gp_single_qty_ordered_sum_30");
$query->selectRaw("gp_single_cturnover_30");
$query->selectRaw("gp_single_cturnover_365");
$query->selectRaw("gp_multi_gp_avg_365");
$query->selectRaw("gp_multi_qty_ordered_sum_365");
$query->selectRaw("gp_multi_gp_avg_30");
$query->selectRaw("gp_multi_qty_ordered_sum_30");
$query->selectRaw("gp_multi_cturnover_30");
$query->selectRaw("gp_multi_cturnover_365");
$query->selectRaw("gp_sum_30");
$query->selectRaw("order_count_30");
$query->selectRaw("ggp_avg_30");
$query->selectRaw("gp_sum_365");
$query->selectRaw("order_count_365");
$query->selectRaw("ggp_avg_365");
$query->selectRaw("gogp_order_count_30");
$query->selectRaw("gogp_order_count_365");
$query->selectRaw("gogp_sum_30");
$query->selectRaw("gogp_sum_365");
$query->selectRaw("gogp_avg_30");
$query->selectRaw("gogp_avg_365");
$query->selectRaw("gogp_order_ids");
$query->selectRaw("stockRelated1");
$query->selectRaw("stockRelated2");
$query->selectRaw("NOW() updated_on");

$pdo = $service->getEanRepository()->getDb()->getPdo();


try {
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_STARTED);
    $pdo->exec("TRUNCATE TABLE item_stats");

    $pdo->beginTransaction();

    $pdo->prepare($query->toSql());
    $pdo->execute($query->getBindings());

    $pdo->commit();
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_SUCCESS,);

} catch (Exception $exception) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_ERROR, $exception->getMessage());
}*/

$pdo = $service->getEanRepository()->getDb()->getPdo();

try {
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_STARTED);

    $pdo->exec("TRUNCATE TABLE item_stats");

    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, 'Truncated & got Total = ' . $query->count('item_ean.item_id'), SysLog::STATUS_SUCCESS);
    //$pdo->beginTransaction();

    $query->chunk(200, function ($items) {
        SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, 'Fetched', SysLog::STATUS_SUCCESS, count($items));
        $rows = [];
        /** @var \App\Models\ItemEan $item */
        foreach ($items as $item) {
            $rows[] = [
                'item_id' => $item->item_id,
                'last365_sales_qty' => $item->last365_sales_qty,
                'last365_cturover' => $item->last365_cturover,
                'last30_sales_qty' => $item->last30_sales_qty,
                'last30_cturover' => $item->last30_cturover,
                'gp_single_gp_avg_365' => $item->gp_single_gp_avg_365,
                'gp_single_qty_ordered_sum_365' => $item->gp_single_qty_ordered_sum_365,
                'gp_single_gp_avg_30' => $item->gp_single_gp_avg_30,
                'gp_single_qty_ordered_sum_30' => $item->gp_single_qty_ordered_sum_30,
                'gp_single_cturnover_30' => $item->gp_single_cturnover_30,
                'gp_single_cturnover_365' => $item->gp_single_cturnover_365,
                'gp_multi_gp_avg_365' => $item->gp_multi_gp_avg_365,
                'gp_multi_qty_ordered_sum_365' => $item->gp_multi_qty_ordered_sum_365,
                'gp_multi_gp_avg_30' => $item->gp_multi_gp_avg_30,
                'gp_multi_qty_ordered_sum_30' => $item->gp_multi_qty_ordered_sum_30,
                'gp_multi_cturnover_30' => $item->gp_multi_cturnover_30,
                'gp_multi_cturnover_365' => $item->gp_multi_cturnover_365,
                'gp_sum_30' => $item->gp_sum_30,
                'order_count_30' => $item->order_count_30,
                'ggp_avg_30' => $item->ggp_avg_30,
                'gp_sum_365' => $item->gp_sum_365,
                'order_count_365' => $item->order_count_365,
                'ggp_avg_365' => $item->ggp_avg_365,
                'gogp_order_count_30' => $item->gogp_order_count_30,
                'gogp_order_count_365' => $item->gogp_order_count_365,
                'gogp_sum_30' => $item->gogp_sum_30,
                'gogp_sum_365' => $item->gogp_sum_365,
                'gogp_avg_30' => $item->gogp_avg_30,
                'gogp_avg_365' => $item->gogp_avg_365,
                'gogp_order_ids' => $item->gogp_order_ids,
                'stockRelated1' => $item->stockRelated1,
                'stockRelated2' => $item->stockRelated2,
                'parent_stock_stables_sum_total_piece_qty' => $item->parent_stock_stables_sum_total_piece_qty,
                'updated_on' => \App\Lib\FuncDate::dtToday(),
            ];
        }
        ItemStats::insert($rows);
    });

    //$pdo->commit();
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_SUCCESS);

} catch (Exception $exception) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_ERROR, $exception->getMessage());
}
require __DIR__ . '/../../extras/cron/_cron_after.php';