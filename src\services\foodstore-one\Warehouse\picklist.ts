/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/warehouse/picklist';

/** rule GET /api/warehouse/picklist */
export async function getPicklist(
  params?: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.WarehousePicklist>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


export const preparePicklistOption = (picklist: API.WarehousePicklist) => {
  return {
    id: picklist.id,
    value: picklist.id,
    label: `#${picklist?.id}-${picklist?.username} ${picklist?.note ?? ''
      } (${picklist?.orders_count} / ${picklist?.items_count})`,
    unbooked_count: picklist.unbooked_count,
    is_full_stock_stable_updated: picklist.is_full_stock_stable_updated,
    label_files: picklist.label_files,
  };
}

export async function getPicklistACList(params?: API.PageParams, sort?: any): Promise<API.WarehousePicklist[]> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort: sort ?? { id: 'ascend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message.data);
}

/** post POST /api/warehouse/picklist */
export async function createPicklist(
  data?: API.WarehousePicklist,
  options?: { [key: string]: any },
): Promise<API.WarehousePicklist> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}


/** 
 * 
 * PUT /api/warehouse/picklist */
export async function updatePicklist(
  id: number,
  data?: API.WarehousePicklist & { timeTrack?: Partial<API.WarehousePicklistTimeTrack> } & { mode?: null | 'startTimeTrack' | 'pauseTimeTrack' | 'finishTimeTrack', trackType?: 0 | 1 | 2 },
  options?: { [key: string]: any },
): Promise<API.WarehousePicklist> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** export as PDF GET /api/warehouse/picklist/export-pdf/{id} */
export async function exportPicklistPdf(id?: number, params?: { mode?: 'groupByOrder'; isPreList?: boolean; skipPreFileGeneration?: boolean, box_step?: number | string }) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-pdf/${id}`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/** export as PDF GET /api/warehouse/picklist/export-pdf-box/{id} */
export async function exportPicklistPdfByBox(id?: number, params?: { mode?: 'groupByOrder'; box_step?: number | string }) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-pdf-box/${id}`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}



/** export as PDF GET /api/warehouse/picklist/export-delivery-note/{id} */
export async function exportPicklistDeliveryNote(id?: number, params?: Record<string, any>) {
  return request<API.ResultObject<API.Downloadable[]>>(`${urlPrefix}/export-delivery-note/${id}`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}


/** export as PDF GET /api/warehouse/picklist/export-delivery-note-by-order-id/{id} */
export async function exportPicklistDeliveryNoteByEntityId(id?: number, params?: Record<string, any>) {
  return request<API.ResultObject<API.Downloadable[]>>(`${urlPrefix}/export-delivery-note-by-order-id/${id}`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/** export as PDF GET /api/warehouse/picklist/export-csv/{id} */
export async function exportPicklistCsv(id?: number, params?: Record<string, any>): Promise<{ url?: string }> {
  return request<API.BaseResult>(`${urlPrefix}/export-csv/${id}`, {
    method: 'GET',
  }).then((res) => res.message);
}

/**
 * Print PDF Label
 *
 * GET /api/warehouse/picklist/export-label-in-picklist/{id} */
export async function exportShippingLabelsInPicklist(id?: number, params?: Record<string, any>) {
  return request<API.ResultObject<{ files?: API.Downloadable[]; labels?: API.OrderLabel[] }>>(
    `${urlPrefix}/export-label-in-picklist/${id}`,
    {
      method: 'GET',
      params,
    },
  ).then((res) => res.message);
}

/**
 * Print PDF Label
 *
 * GET /api/warehouse/picklist/export-merged-label-in-picklist/{id} */
export async function exportShippingMergedLabelsInPicklist(id?: number, params?: Record<string, any>) {
  return request<API.ResultObject<{ file?: API.Downloadable; label_files?: string[] }>>(
    `${urlPrefix}/export-merged-label-in-picklist/${id}`,
    {
      method: 'GET',
      params,
    },
  ).then((res) => res.message);
}


/** 
 * Check parcels status and create shipments
 * 
 * GET /api/warehouse/picklist/check-parcel-and-create-shipment/{id} 
 */
export async function checkParcelsAndCreateShipment(id?: number, params?: Record<string, any>) {
  return request<API.ResultObject<any>>(`${urlPrefix}/check-parcel-and-create-shipment/${id}`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}




/** import a csv
 *
 * POST /api/warehouse/picklist/import-csv/{id}
 *
 */
export async function importPicklistCsv(id?: number, data?: any, options?: { [key: string]: any }) {
  const url = `${urlPrefix}/import-csv/${id}`;
  const config: { [key: string]: any } = {
    method: 'POST',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config['body'] = data;
  } else {
    config['data'] = data;
  }

  return request<API.BaseResult>(url, config).then((res) => res.message);
}

export type NoStockSkuType = {
  sku: string;
  qty: number;
}

/** booking PUT /api/warehouse/picklist/book/{id} */
export async function bookPicklist(
  id?: number,
  data?: API.WarehousePicklist & { useStockAdjustment?: boolean, noStockSkus?: NoStockSkuType[] },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/book/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/warehouse/picklist/{id} */
export async function deletePicklist(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}



export type UnbookedQtyInPicklistType = {
  picklist_id: number;
  unbooked_qty: number;
}
/** 
 * get unbooked qtys in all picklists
 * 
 * GET /api/warehouse/picklist/unbookedQtys */
export async function getUnbookedQtysInPicklist(params?: API.PageParams) {
  return request<API.ResultObject<UnbookedQtyInPicklistType[]>>(`${urlPrefix}/unbookedQtys`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * ---------------------------------------------------------------------------------------- //
 * Create a boxes (orders) 
 * ---------------------------------------------------------------------------------------- //
 * 
 * POST /api/warehouse/picklist/create-box/{id} */
export async function createBox(id?: number, data?: any) {
  return request<API.ResultObject<number>>(`${urlPrefix}/create-box/${id}`, {
    method: 'POST',
    data
  }).then((res) => res.message);
}