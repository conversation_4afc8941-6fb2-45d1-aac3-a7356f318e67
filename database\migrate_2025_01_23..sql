/*SELECT item.id
FROM item
         LEFT JOIN trademark
                   ON trademark.id = item.`trademark_id`
WHERE trademark.`id` IS NULL
  AND item.`trademark_id` IS NOT NULL;
*/

-- Set invalid trademark_id to NULL in order to set FK constraints correctly.
update
    item
set trademark_id = null
where id in
      (select item.id
       from item
                left join trademark
                          on trademark.id = item.`trademark_id`
       where trademark.`id` is null
         and item.`trademark_id` is not null);


ALTER TABLE `item`
    DROP FOREIGN KEY `FK_item_trademark_id`;

ALTER TABLE `item`
    ADD CONSTRAINT `FK_item_trademark_id` FOREIGN KEY (`trademark_id`) REFERENCES `trademark` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;




UPDATE
    item
SET
    vat_id = NULL
WHERE id IN
      (SELECT
           item.id
       FROM
           item
               LEFT JOIN vat
                         ON vat.id = item.`vat_id`
       WHERE vat.`id` IS NULL
         AND item.`vat_id` IS NOT NULL);

ALTER TABLE `item`
    DROP FOREIGN KEY `FK_item_vat_id`;

ALTER TABLE `item`
    ADD CONSTRAINT `FK_item_vat_id` FOREIGN KEY (`vat_id`) REFERENCES `vat` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;