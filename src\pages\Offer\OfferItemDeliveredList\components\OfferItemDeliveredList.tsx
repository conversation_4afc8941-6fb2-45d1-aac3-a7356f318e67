import { DeleteOutlined } from '@ant-design/icons';
import { message, Space, Popconfirm, Button } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, sn } from '@/util';
import { deleteOfferItem } from '@/services/foodstore-one/Offer/offer-item';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import { DefaultOptionType } from 'antd/lib/select';
import {
  deleteOfferItemDelivered,
  getOfferItemDeliveredList,
} from '@/services/foodstore-one/Offer/offer-item-delivered';

export type SearchFormValueType = Partial<API.OfferItemDelivered> & {
  includeSubTable?: boolean;
  trademark?: DefaultOptionType;
};

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.OfferItemDelivered[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteOfferItem(selectedRows.map((row) => row.id).join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type OfferItemDeliveredListType = {
  item_id?: number;
  offer_item_id?: number;

  reloadTick?: number;
};

/**
 *
 * @param props
 * @returns
 */
const OfferItemDeliveredList: React.FC<OfferItemDeliveredListType> = ({ offer_item_id, item_id, reloadTick }) => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<API.OfferItemDelivered>();
  const [selectedRowsState, setSelectedRows] = useState<API.OfferItemDelivered[]>([]);

  const columns: ProColumns<API.OfferItemDelivered>[] = [
    /* {
      title: 'Image',
      dataIndex: ['item_ean', 'files', 0, 'url'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      width: 80,
      render: (dom, record) => {
        const files = record.item_ean?.files;
        return files ? (
          <Image.PreviewGroup>
            {files &&
              files.map((file, ind) => (
                <Image
                  key={file.id}
                  src={file.thumb_url}
                  preview={{
                    src: file.url,
                  }}
                  wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                  width={40}
                />
              ))}
          </Image.PreviewGroup>
        ) : (
          <></>
        );
      },
    }, */
    /* {
      title: 'SKU',
      dataIndex: ['sku'],
      sorter: true,
      copyable: true,
      width: 120,
      render: (dom, record) => {
        return (
          <Row>
            <Col flex="auto">
              <Typography.Link
                href={`/item/ean-all-summary?sku=${skuToItemId(record.item_ean?.sku)}_`}
                target="_blank"
                copyable
              >
                {record.item_ean?.sku}
              </Typography.Link>
            </Col>
          </Row>
        );
      },
    }, */
    {
      title: 'Qty / Case',
      dataIndex: ['case_qty'],
      width: 40,
      align: 'right',
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 40,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },

    {
      title: 'Qty',
      dataIndex: ['qty_pcs'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record.qty) * sn(record.case_qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 60,
      fixed: 'right',
      render: (_, record) => [
        <Popconfirm
          key="delete"
          title="Are you sure you want to delete?"
          okButtonProps={{ size: 'large' }}
          cancelButtonProps={{ size: 'large' }}
          onConfirm={() => {
            deleteOfferItemDelivered(sn(record.id))
              .then((res) => {
                message.success('Deleted successfully.');
                actionRef.current?.reload();
              })
              .catch(Util.error);
          }}
        >
          <Button type="default" key="rest" icon={<DeleteOutlined />} danger />
        </Popconfirm>,
      ],
    },
  ];

  useEffect(() => {
    if (reloadTick) {
      actionRef.current?.reload();
    }
  }, [reloadTick]);

  return (
    <>
      <ProTable<API.OfferItemDelivered, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Offer Item's Delivered List</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: false, setting: false }}
        size="large"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        toolBarRender={() => []}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            offer_item_id,
            item_id,
            with: 'itemEan,itemEan.eanTextDe,offerItem,item',
          };

          return getOfferItemDeliveredList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          selectedRowKeys: selectedRowsState.map((x) => x.id as any),
        }}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
    </>
  );
};

export default OfferItemDeliveredList;
