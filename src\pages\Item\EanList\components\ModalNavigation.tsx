import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import React from 'react';
import type { ButtonProps } from 'antd';
import { Button, Space } from 'antd';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons/lib/icons';
import type { HandleNavFuncType, NavigationModelsType } from '../hooks/useModalNavigation';

const DefaultButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  style: { width: 24, height: 24, fontSize: 14 },
};

export type ModalNavigationProps = {
  modalName?: NavigationModelsType;
  eanId?: number;
  itemId?: number;
  style?: any;
  handleNavigation?: HandleNavFuncType;
};

const ModalNavigation: React.FC<ModalNavigationProps> = ({ modalName, itemId, eanId, style, handleNavigation }) => {
  return (
    <Space.Compact style={style}>
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'prevModal', eanId, itemId)}
        icon={<ArrowLeftOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'nextModal', eanId, itemId)}
        icon={<ArrowRightOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'prev', eanId, itemId)}
        icon={<ArrowUpOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'next', eanId, itemId)}
        icon={<ArrowDownOutlined />}
      />
    </Space.Compact>
  );
};

export default ModalNavigation;
