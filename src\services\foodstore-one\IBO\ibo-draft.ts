/* eslint-disable */
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';

const urlPrefix = '/api/ibo/ibo-draft';

/** get GET /api/ibo/ibo-draft */
export async function getIboDraftList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getIboDraft(id: string | number, params?: { [key: string]: any }): Promise<API.IboDraft> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params: {
      ...params,
      with: 'detail',
    },
    withToken: true,
  }).then((res) => res.message);
}

export async function copyAndCreateIboDraft(
  id: string | number,
  params?: { [key: string]: any },
): Promise<API.IboDraft> {
  return request<API.BaseResult>(`${urlPrefix}/copy/${id}`, {
    method: 'POST',
    data: {
      ...params,
    },
    withToken: true,
  }).then((res) => res.message);
}

/** put PUT /api/ibo/ibo-draft */
export async function updateIboDraft(data: API.IboDraft, options?: { [key: string]: any }): Promise<API.IboDraft> {
  return request<API.BaseResult>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/ibo/ibo-draft */
export async function createDraft(data: API.IboDraft, options?: { [key: string]: any }): Promise<API.IboDraft> {
  return request<API.BaseResult>(urlPrefix, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/ibo/ibo-draft/create-or-update */
export async function createOrUpdateDraft(data: API.IboDraft, options?: { [key: string]: any }): Promise<API.IboDraft> {
  return request<API.BaseResult>(`${urlPrefix}/create-or-update`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/ibo/ibo-draft */
export async function deleteIboDraft(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/ibo/ibo-draft/ac-list
 *
 * get the autocomplet lists.
 *
 */
export async function getIboDraftACList(params: { [key: string]: string }, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}/get/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      sort_detail: JSON.stringify(sort ?? { created_on: 'descend', id: 'descend' }),
    },
    withToken: true,
  }).then((res) =>
    res.message.map((x: API.IboDraft) => ({
      ...x,
      value: x.id,
      label: `${x.name || '-'}`, //${x.type == 1 ? ` (booked)` : ''}
    })),
  );
}
