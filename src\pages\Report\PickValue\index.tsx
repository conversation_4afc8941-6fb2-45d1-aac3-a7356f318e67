import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { sUrlByTpl, sn } from '@/util';
import { ni } from '@/util';
import Util, { nf2 } from '@/util';
import { ProForm, ProFormText, type ProFormInstance } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { StatsPickValueRecordType, getSalesStatsPickValueList } from '@/services/foodstore-one/Report/sales-stat';
import { PageContainer } from '@ant-design/pro-layout';
import { Col, Row, Typography, Image, Card } from 'antd';
import { useModel } from 'umi';
import { DictCode } from '@/constants';
import { CloseOutlined, LinkOutlined } from '@ant-design/icons';
import useTrademarkFormFilter, {
  TrademarkChangeCallbackHandlerTypeParamType,
} from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import SProFormDateRange, { DRSelection } from '@/components/SProFormDateRange';
import { StockQtyComp } from '@/pages/Item/EanList/EanAllSummary';
import _ from 'lodash';

type SearchFormValueType = {
  trademark?: DefaultOptionType;
  sku?: string;
  name?: string;
};

type RecordType = StatsPickValueRecordType;

const PickValue: React.FC<{ route?: any }> = (props) => {
  const {
    getDictByCode,
    appSettings: { drSelection },
  } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  // Trademark search filter
  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements: formElementsTrademark } = useTrademarkFormFilter(
    searchFormRef.current,
    trademarkChangeCallbackHandler,
    {
      parentLoading: loading,
    },
  );

  // Date Range selector
  useEffect(() => {
    searchFormRef.current?.resetFields();
    searchFormRef.current?.setFieldValue('sku', props.route?.sku || '');

    const initialDrSelection = DRSelection.DR_LAST_30_DAYS;
    const range = drSelection[initialDrSelection as any];
    searchFormRef?.current?.setFieldValue('dr_selection', initialDrSelection);
    if (range) {
      searchFormRef?.current?.setFieldValue('start_date', range[0] ? range[0] : null);
      searchFormRef?.current?.setFieldValue('end_date', range[1] ? range[1] : null);
    }
    actionRef.current?.reload();
  }, [props.route?.sku]);

  const defaultColumns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        title: 'Item',
        dataIndex: ['ean_text_de', 'name'],
        align: 'left',
        width: 500,
        render: (dom, record) => {
          return (
            <Row gutter={8} wrap={false}>
              <Col flex="auto">
                <Typography.Text ellipsis title={`${record?.ean_text_de?.name || ''}`}>
                  <a
                    href={`/item/ean-all-summary?sku=${record.item_id}_`}
                    target="_blank"
                    rel="noreferrer"
                    title="Search on website"
                  >
                    {`${record.item_id || ''}_`}
                  </a>
                  {`--- ${record?.ean_text_de?.name || ''}`}
                </Typography.Text>
              </Col>
              <Col flex="0 0 20px">
                <Typography.Text
                  title={`${record?.ean_text_de?.name || ''}`}
                  copyable={{ text: `${record?.ean_text_de?.name || ''}` }}
                >
                  {''}
                </Typography.Text>
              </Col>
              <Col flex="0 0 20px">
                <Typography.Link
                  href={sUrlByTpl(getDictByCode(DictCode.MAG_SEARCH_URL), {
                    q: record.item_id,
                  })}
                  className="text-sm"
                  title="Search EANs on new tab."
                  target="_blank"
                >
                  <LinkOutlined />
                </Typography.Link>
              </Col>
            </Row>
          );
        },
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 50,
        render: (dom, record) => {
          return record.files ? (
            <Image.PreviewGroup>
              {record.files &&
                record.files.map((file, ind) => (
                  <Image
                    key={file.id}
                    src={file.thumb_url}
                    preview={{
                      src: file.url,
                    }}
                    wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                    width={40}
                  />
                ))}
            </Image.PreviewGroup>
          ) : (
            <></>
          );
        },
      },
      {
        title: 'Pcs',
        dataIndex: `pcs_qty`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          return <span>{ni(record.pcs_qty)}</span>;
        },
      },
      {
        title: 'Picks',
        dataIndex: ['orders_cnt'],
        width: 40,
        align: 'right',
        render(__, entity) {
          /* return (
            <Link
              href={`/report/order/sales-stat-order-list?start_date=${entity.uid}&end_date=${entity.uid}`}
              target="_blank"
            >
              {ni(entity.orders_cnt)}
            </Link>
          ); */
          return ni(entity.orders_cnt);
        },
      },
      {
        title: 'GP / pcs',
        dataIndex: ['gp_pcs'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          const gp_pcs = sn(record.pcs_qty) ? sn(record.gp) / sn(record.pcs_qty) : 0;
          return <div title={`${gp_pcs}`}>{nf2(gp_pcs)}</div>;
        },
      },
      {
        title: 'GP / pick',
        dataIndex: ['gp_pcs'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return sn(record.orders_cnt) ? nf2(sn(record.gp) / sn(record.orders_cnt)) : null;
        },
      },
      {
        title: 'AVG BP',
        dataIndex: ['avg_bp'],
        width: 70,
        align: 'right',
        className: 'bl2',
        render(__, record) {
          return nf2(record.avg_bp);
        },
      },
      {
        title: 'Cur. BP',
        dataIndex: ['latest_ibo', 'price'],
        width: 70,
        align: 'right',
        render(__, record) {
          return nf2(record.latest_ibo?.price);
        },
      },
      {
        title: 'Price_Stable BP',
        dataIndex: ['cur_bp'],
        width: 80,
        align: 'right',
        render(__, record) {
          return nf2(record.ean_price_stable?.cur_price);
        },
      },

      {
        title: 'AVG Sales',
        dataIndex: ['avg_sales_price'],
        width: 80,
        align: 'right',
        render(__, record) {
          return nf2(record.avg_sales_price);
        },
      },
      {
        title: 'Cur Sales',
        dataIndex: ['cur_sales_price'],
        width: 80,
        align: 'right',
        render(__, record) {
          return nf2(record.cur_sales_price);
        },
      },
      {
        title: '%',
        dataIndex: ['cur_sales_per'],
        width: 60,
        align: 'right',
        tooltip: 'Round(([CurSales / PriceStable BP] - 1)  100 ,0)',
        render(__, record) {
          // % = Round(([CurSales / PriceStable BP] - 1)  100 ,0)
          const stable_bp = sn(record.ean_price_stable?.cur_price);
          const percent = stable_bp && record.cur_sales_price ? (sn(record.cur_sales_price) / stable_bp - 1) * 100 : 0;
          return percent ? ni(percent) + '%' : null;
        },
      },
      {
        title: 'Cur Special',
        dataIndex: ['fs_special_discount'],
        width: 60,
        align: 'center',
        className: 'bl2',
        // tooltip: 'Current discount',
        // className: 'cursor-pointer',
        /* onCell(record, index) {
          return {
            onClick(e) {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        }, */
      },

      {
        title: 'Stock Qty',
        dataIndex: ['stock_mix_qty'],
        width: 80,
        hideInSearch: true,

        // tooltip: 'Qty in Stock Warehouse. Please click to view stock details. Italic --> blocked qty.',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record) => {
          return (
            <StockQtyComp
              availableQty={record?.stock_mix_qty}
              blockedQty={record?.stock_mix_qty_b}
              is_single={record.is_single}
            />
          );
        },
        /* onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        }, */
      },
      {
        title: 'Avg. Exp. Days',
        dataIndex: ['avg_exp_days'],
        width: 60,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return ni(record.avg_exp_days);
        },
      },
      {
        title: 'Avg. Shelflife',
        dataIndex: ['avg_shelflife'],
        width: 40,
        className: 'text-sm',
        render: (__, record, index) => {
          return ni(record.avg_shelf_life);
        },
      },
      {
        title: '',
        dataIndex: ['stock_related'],
        width: 60,
        align: 'center',
        tooltip: 'Round(Stock.Qty / Qty.Sold(30), 1) : Round(Result*30 / (AVG(EXP) - 30),1)',
        render(dom, record) {
          const allStockQty = sn(record?.stock_mix_qty) + sn(record?.stock_mix_qty_b);
          const sale30Qty = sn(record.last30_sales_qty);

          return (
            <>
              <Row gutter={2} className="text-right text-sm">
                <Col span={12}>{sale30Qty ? Util.numberFormat(record.stockRelated1, false, 1, true) : null}</Col>
                <Col span={12}>
                  {sale30Qty ? (
                    Util.numberFormat(record.stockRelated2, false, 1, true)
                  ) : allStockQty ? (
                    <CloseOutlined color="red" />
                  ) : null}
                </Col>
              </Row>
            </>
          );
        },
      },
      {
        title: '',
        dataIndex: `gap`,
      },
      /* {
        title: 'GP',
        dataIndex: `gp`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          return <span>{nf2(record.gp)}</span>;
        },
      },
      {
        title: 'GP / Order',
        dataIndex: `gp`,
        align: 'right',
        width: 50,
        sorter: false,
        render: (__, record) => {
          return sn(record.orders_cnt) ? <span>{nf2(sn(record.gp) / sn(record.orders_cnt))}</span> : null;
        },
      },
      {
        title: 'G. Turnover',
        dataIndex: ['turnover'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return nf2(record.cturnover);
        },
      },
      {
        title: 'G. Turnover / Order',
        dataIndex: ['turnover'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return sn(record.orders_cnt) ? nf2(sn(record.cturnover) / sn(record.orders_cnt)) : null;
        },
      },
      {
        title: 'BP',
        dataIndex: `bp`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          return <span>{nf2(record.bp)}</span>;
        },
      }, */
    ],
    [],
  );

  useEffect(() => {
    setColumns(defaultColumns);
  }, [defaultColumns]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_pick_value', {})}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          {formElementsTrademark}
          <SProFormDateRange
            label="Date"
            formRef={searchFormRef}
            style={{ marginLeft: 16, display: 'none' }}
            disabled={loading}
          />

          <ProFormText name={'sku'} label="SKU" width={100} placeholder={'SKU'} disabled={loading} />
          <ProFormText name={'ean'} label="EAN" width={100} placeholder={'EAN'} disabled={loading} />
          <ProFormText name={'name'} label="Name" width={150} placeholder={'Name'} disabled={loading} />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={'Daily Sales Statistics'}
        actionRef={actionRef}
        size="small"
        rowKey="id" // EAN's id
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, setting: false }}
        search={false}
        sticky
        bordered
        scroll={{ x: 400 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_pick_value', searchFormValues);
          Util.setSfValues('sf_pick_value_p', params);

          setLoading(true);
          return getSalesStatsPickValueList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
            },
            sort,
            filter,
          )
            .then((res) => {
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_pick_value_p')?.pageSize ?? 10),
        }}
        columns={columns}
        columnEmptyText=""
        rowSelection={false}
        /* rowClassName={(record) => {
          if (record.uid == 'total') {
            return 'total-row';
          } else return record?.is_single ? 'row-single' : 'row-multi';
        }} */
      />
    </PageContainer>
  );
};

export default PickValue;
