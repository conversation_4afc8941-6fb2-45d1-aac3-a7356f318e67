/* eslint-disable */
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';
import { paramsSerializer } from './api';

const urlPrefix = '/api/basic-data/supplier';

/** rule GET /api/basic-data/supplier */
export async function getSupplierList(params: API.PageParams, sort: any, filter: any) {
  return request<API.ResultList<API.Supplier>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getSupplier(id?: number, params?: Record<string, any>) {
  return request<API.ResultList<API.Supplier>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      id: id,
      perPage: 1,
      page: 1,
    },
    withToken: true,
  }).then((res) => res.message.data[0]);
}

/** put PUT /api/basic-data/supplier */
export async function updateSupplier(data: API.Supplier, options?: { [key: string]: any }) {
  return request<API.Supplier>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/supplier */
export async function addSupplier(data: API.Supplier, options?: { [key: string]: any }) {
  return request<API.Supplier>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/supplier/{id} */
export async function deleteSupplier(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/supplier
 *
 * get the autocomplete lists.
 *
 */
export async function getSupplierACList(params?: Record<string, any>, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize || 100,
      sort: sort ? sort : { name: 'ascend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) =>
    res.message?.data?.map((x: API.Supplier) => ({
      ...x,
      value: x.id,
      label: `${x.supplier_no} - ${x.name}`,
    })),
  );
}
