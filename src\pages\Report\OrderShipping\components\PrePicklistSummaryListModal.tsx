import { DictCode, MagentoOrderStatusOptions, StockStableStatusOptionsKv } from '@/constants';
import useFileDownloadAndPrintApi from '@/hooks/BasicData/useFileDownloadAndPrintApi';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import { FullAddress } from '@/pages/Magento/Order';
import { getPrePicklistSummary, getPrePicklistSummaryPdf } from '@/services/foodstore-one/Report/order-report';
import Util, { nf2, ni, sn } from '@/util';
import { FilePdfOutlined, LinkOutlined, MinusOutlined, PlusOutlined, PrinterOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormCheckbox, ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Modal, Row, Space, Tag, Typography, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useModel } from 'umi';
import ExpDate from '../../Order/components/ExpDate';
import ToughStocksModal from '@/pages/Warehouse/PicklistDetail/components/ToughStocksModal';

type RowType = API.Order &
  API.OrderItem & {
    entity_id: number;
    qty_ordered: number;

    turnover: number;
    net_turnover: number;
    cturnover: number;
    ebay_fee: number;
    gp: number;
    bp: number;

    turnover_pcs: number;
    net_turnover_pcs: number;
    cturnover_pcs: number;
    ebay_fee_pcs: number;
    gp_pcs: number;
    bp_pcs: number;
  };

export type SearchFormValueType = Partial<API.Order>;

type PrePicklistSummaryListModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  reloadParent?: () => any;
  noStocksOnly?: boolean;
};

const PrePicklistSummaryListModal: React.FC<PrePicklistSummaryListModalProps> = (props) => {
  const { modalVisible, handleModalVisible, reloadParent } = props;

  const { getDictByCode, getParcelUrl } = useModel('app-settings');
  const { downloadB64AndCallDirectPrintApi } = useFileDownloadAndPrintApi();

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);

  // collapsible expandedAll button
  const [dataSource, setDataSource] = useState<API.Order[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

  // stock qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [currentOrderItemRow, setCurrentOrderItemRow] = useState<API.OrderItem>();

  // tough stock list
  const [openToughSSModal, setOpenToughSSModal] = useState<boolean>(false);
  const [toughSSList, setToughSSList] = useState<API.StockStable[]>([]);
  const [tmpPrePdfFile, setTmpPrePdfFile] = useState<API.Downloadable | null>(null);

  const [currentRow, setCurrentRow] = useState<API.Order>();

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Order ID',
      dataIndex: 'entity_id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      defaultSortOrder: 'descend',
      width: 100,
      copyable: true,
    },
    {
      title: 'Store',
      dataIndex: ['store_id'],
      sorter: true,
      showSorterTooltip: false,
      align: 'center',
      ellipsis: true,
      width: 80,
    },
    {
      title: 'Increment ID',
      dataIndex: ['increment_id'],
      sorter: true,
      showSorterTooltip: false,
      width: 120,
      render(dom, record) {
        return record?.entity_id ? (
          <Typography.Link
            href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${record?.entity_id}/`}
            target="_blank"
            copyable
          >
            {record.increment_id}
          </Typography.Link>
        ) : (
          dom
        );
      },
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      sorter: true,
      showSorterTooltip: false,
      align: 'center',
      ellipsis: true,
      width: 120,
      render: (_, record) => {
        const status = record.status;
        let color = 'default';
        switch (status ?? '') {
          case 'complete':
          case 'closed':
            color = 'success';
            break;
          case 'processing':
            color = 'blue';
            break;
          case 'pending':
            color = 'orange';
            break;
          case 'canceled':
            color = 'red';
            break;
        }

        return <Tag color={color as any}>{MagentoOrderStatusOptions[status || '-'] ?? '-'}</Tag>;
      },
    },
    {
      title: 'Name',
      dataIndex: ['sa_initials'],
      sorter: false,
      align: 'center',
      ellipsis: true,
      width: 80,
      render: (dom, record) => (
        <Typography.Text mark={record?.warn_sa_fullname || record?.warn_sa_fullname_wrong}>
          {record.sa_initials}
        </Typography.Text>
      ),
    },
    {
      title: 'Delivery Address',
      dataIndex: 'sa_full',
      align: 'left',
      width: 500,
      tooltip: 'Orange colored rows are in warnings list. Highlighted parts may be wrong!',
      ellipsis: true,
      render(dom, record) {
        return (
          <>
            <FullAddress order={record} type="shipping" />
          </>
        );
      },
      onCell(record) {
        return { style: { paddingRight: 16 } };
      },
    },
    /* {
      title: 'Shipped Parcel',
      dataIndex: ['shipping_imported_list'],
      align: 'left',
      width: 150,
      tooltip: 'Green indicates a processed shipping on Magento',
      render(dom, record) {
        return record.latest_shipping
          ? [record.latest_shipping]?.map((x) => {
              let cls = 'text-sm';
              if (x.mag_ship_id) {
                cls += ' c-green';
              }
              const title = x.carrier_code || x.title ? `${x.title} | ${x.carrier_code}` : '';
              return (
                <div key={x.id} className={cls} title={title}>
                  <a
                    href={`${getDictByCode(DictCode.MAG_ADMIN_URL_TRACKING)}${x.parcel_no}`}
                    target="_blank"
                    rel="noreferrer"
                    className={cls}
                  >
                    {x.parcel_no}
                  </a>
                </div>
              );
            })
          : null;
      },
    }, */
    {
      title: 'Parcel Labels',
      dataIndex: ['labels'],
      align: 'left',
      width: 200,
      // tooltip: 'Green indicates a processed shipping on Magento',
      render(dom, record) {
        return record.labels
          ? record.labels?.map((x) => {
              return (
                <Row key={x.track_id} gutter={4} style={{ alignItems: 'center' }}>
                  <Col className="text-sm" span={4}>
                    <Typography.Text ellipsis>{x.service_name}</Typography.Text>
                  </Col>
                  <Col className="text-sm" span={13}>
                    <Typography.Link copyable href={getParcelUrl(x.parcel_no)} target="_blank">
                      {x.track_id}
                    </Typography.Link>
                  </Col>
                  <Col span={1} className="text-sm c-red">
                    <span title="Return Label">{x.ref_no?.endsWith(',Return') ? 'R' : ''}</span>
                  </Col>
                  <Col span={2}>
                    <Typography.Link href={`${API_URL}/api/${x.url}`} title="Open Label PDF" target="_blank">
                      <LinkOutlined />
                    </Typography.Link>
                  </Col>
                  <Col span={2}>
                    <Button
                      type="link"
                      size="small"
                      icon={<PrinterOutlined />}
                      title="Re-print Label"
                      style={{ height: 16 }}
                      onClick={() => {
                        downloadB64AndCallDirectPrintApi(x, 'browserPrinter');
                      }}
                    />
                  </Col>
                </Row>
              );
            })
          : null;
      },
    },
  ];

  const onHeaderCell = useCallback((__: any) => {
    const defaultHeaderProps = {
      className: 'text-sm',
      style: {
        paddingTop: 4,
        paddingBottom: 4,
        fontWeight: 'normal',
      },
    };
    return defaultHeaderProps;
  }, []);

  const smColumns: ProColumns<Partial<API.StockStable>>[] = useMemo(
    () => [
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'name'],
        editable: false,
        width: 90,
        align: 'center',
        onHeaderCell,
      },
      {
        title: 'Priority',
        dataIndex: ['warehouse_location', 'priority'],
        editable: false,
        width: 90,
        align: 'right',
        onHeaderCell,
        render: (dom, record) => {
          return ni(record.warehouse_location?.priority);
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 90,
        align: 'center',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return <ExpDate date={record.exp_date} />;
        },
      },
      {
        title: 'IBO ID',
        dataIndex: ['ibo', 'id'],
        width: 60,
        align: 'left',
        editable: false,
        onHeaderCell,
        render: (dom, record) => (record.ibo ? <span className="">#{record.ibo_id}</span> : null),
      },
      /* {
        title: 'BP',
        dataIndex: ['ibo', 'price'],
        width: 70,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => (record.ibo?.price ? <span>€{nf2(record.ibo.price)}</span> : null),
      }, */
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 90,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 80,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'Status',
        dataIndex: ['status'],
        dataType: 'select',
        width: 90,
        editable: false,
        valueEnum: StockStableStatusOptionsKv,
        onHeaderCell,
      },
    ],
    [onHeaderCell],
  );

  const [warningsDefCount, setWarningsDefCount] = useState<number>(0);

  const expandedRowRender = (record: API.Order) => {
    return (
      <ProTable<API.OrderItem>
        columns={[
          {
            title: 'SKU',
            dataIndex: 'sku',
            width: 100,
            tooltip: 'Click to view stock detail.',
            copyable: true,
            onHeaderCell,
            onCell: (recordOI) => {
              return {
                className: 'cursor-pointer c-blue',
                onClick: (e) => {
                  setCurrentOrderItemRow(recordOI);
                  handleQtyModalVisible(true);
                },
              };
            },
          },
          {
            title: 'EAN',
            dataIndex: ['item_ean', 'ean'],
            width: 140,
            onHeaderCell,
            copyable: true,
          },
          { title: 'Name', dataIndex: 'name', width: 200, onHeaderCell },
          { title: 'Ordered Qty', dataIndex: 'qty_ordered', width: 100, onHeaderCell },
          {
            title: 'Open Qty / Stock',
            dataIndex: 'open_qty_ordered',
            width: 100,
            onHeaderCell,
            tooltip:
              'Open orders qty pcs & Stock qty pcs. If Open > Stock Qty, RED background and you need to pay attention to control stocks .',
            render: (dom, r) => {
              const openQtyPcs = sn(r.open_qty_ordered);
              const stockQtyPcs = sn(
                r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
              );
              return (
                <Row gutter={8} className="c-grey text-right">
                  <Col span={10}>{ni(openQtyPcs, true)}</Col>
                  <Col span={14}>{ni(stockQtyPcs, true)}</Col>
                </Row>
              );
            },
            onCell: (r) => {
              const openQtyPcs = sn(r.open_qty_ordered);
              const stockQtyPcs = sn(
                r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
              );
              return {
                className: openQtyPcs > stockQtyPcs ? 'bg-red' : '',
              };
            },
          },
          {
            title: 'Price / pcs',
            dataIndex: 'price',
            width: 70,
            align: 'right',
            onHeaderCell,
            render: (dom, r) =>
              r?.item_ean?.attr_case_qty ? nf2(sn(r.price) / sn(r?.item_ean?.attr_case_qty)) : nf2(r.price),
          },
          {
            title: 'Discount',
            dataIndex: ['item_ean', 'fs_special_discount'],
            width: 70,
            onHeaderCell,
            align: 'center',
            render: (dom, r) => <span className="c-orange">{r.item_ean?.fs_special_discount}</span>,
          },
          {
            title: 'Stocks',
            dataIndex: 'stock_stables',
            width: 700,
            onHeaderCell,
            render: (dom, smRecord) => {
              if (!smRecord?.item_ean?.stock_stables || !smRecord?.item_ean?.stock_stables?.length) return <></>;
              return (
                <>
                  <ProTable
                    columns={smColumns}
                    cardProps={{ bodyStyle: { padding: '0 0' } }}
                    rowKey="id"
                    headerTitle={false}
                    search={false}
                    options={false}
                    pagination={false}
                    scroll={{ y: 'auto' }}
                    dataSource={smRecord?.item_ean?.stock_stables ?? []}
                    columnEmptyText={''}
                    locale={{ emptyText: <></> }}
                    size="small"
                  />
                </>
              );
            },
          },
          {
            title: 'Mag. IDs',
            dataIndex: 'product_id',
            align: 'center',
            width: 110,
            tooltip: 'Product ID / Order Item ID in Magento',
            showSorterTooltip: false,
            onHeaderCell,
            className: 'text-sm c-grey',
            render: (__, r) => `${r.product_id} / ${r.item_id}`,
          },
        ]}
        cardProps={{ bodyStyle: { padding: '0 0 0 100px' } }}
        rowKey="item_id"
        size="small"
        headerTitle={false}
        search={false}
        options={false}
        pagination={false}
        dataSource={record?.mag_order_items ?? []}
        rowClassName={(oiRecord) => (oiRecord.item_ean?.is_single ? 'row-single' : 'row-multi')}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
    );
  };

  useEffect(() => {
    if (modalVisible) {
      searchFormRef.current?.setFieldValue('no_stocks_only', !!props.noStocksOnly);
      actionRef.current?.reload();
    }
  }, [modalVisible, props.noStocksOnly]);

  return (
    <Modal
      title={
        <Space size={16}>
          <span>Pre Picklist Summary List</span>
          {warningsDefCount > 0 ? (
            <div style={{ position: 'absolute', top: 10, left: '50%', marginLeft: -150 }}>
              <Alert
                key="warn-def"
                message={`WARNING: ${warningsDefCount} invalid delivery addresses detected in orange colored rows!`}
                type="error"
                style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
              />
            </div>
          ) : undefined}
        </Space>
      }
      maskClosable={false}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="90%"
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <Card style={{ marginBottom: 0 }} bodyStyle={{ paddingBottom: 0 }} bordered={false} size="small">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          size="small"
          initialValues={Util.getSfValues('sf_pre_picklist', {})}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormText
            name={'entity_id'}
            label="Order ID"
            width={200}
            placeholder={'Order ID'}
            tooltip="You can specify several orders by comma."
          />
          <ProFormText name={'increment_id'} label="Increment ID" width={140} placeholder={'Increment ID'} />
          <ProFormText name={'sku'} label="SKU" width={120} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormCheckbox name={'no_stocks_only'} label="No Stocks Only?" initialValue={false} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        actionRef={actionRef}
        rowKey="entity_id"
        headerTitle="Orders List"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, reload: true, density: false, search: false }}
        search={false}
        sticky
        scroll={{ x: 1300 }}
        cardProps={{
          headStyle: { padding: 0 },
          bodyStyle: { padding: 0 },
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_pre_picklist_p')?.pageSize ?? 20),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_pre_picklist', searchFormValues);
          Util.setSfValues('sf_pre_picklist_p', params);
          setLoading(true);
          return getPrePicklistSummary(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.entity_id) {
                setCurrentRow(res.data?.find((x) => x.entity_id == currentRow.entity_id));
              }

              setExpandedRowKeys(res?.data?.map?.((x_1: API.Order) => x_1.entity_id as any));
              setDataSource(res?.data?.map?.((x_2: API.Order) => ({ entity_id: x_2.entity_id } as any)));
              setWarningsDefCount(res.data.reduce((prev, current) => prev + (current.warnings_def ? 1 : 0), 0));

              return res;
            })
            .finally(() => {
              setLoading(false);
            }) as any;
        }}
        onRequestError={Util.error}
        columns={columns}
        expandable={{
          expandedRowRender,
          expandRowByClick: false,
          defaultExpandAllRows: true,
          indentSize: 0,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            setExpandedRowKeys(expandedKeys as any);
          },
          showExpandColumn: true,
          columnWidth: 50,
          columnTitle: (
            <div style={{ paddingLeft: 16 }}>
              {expandedRowKeys.length == dataSource.length ? (
                <MinusOutlined
                  onClick={() => {
                    setExpandedRowKeys([]);
                  }}
                />
              ) : (
                <PlusOutlined
                  onClick={() => {
                    setExpandedRowKeys(dataSource.map((x) => x.entity_id as any));
                  }}
                />
              )}
            </div>
          ),
        }}
        tableAlertRender={false}
        rowSelection={false}
        rowClassName={(record) => {
          const defaultCls = 'disable-selection reset-tds-bg ';
          let rowCls = '';
          if (record.entity_id <= 0) {
            rowCls = defaultCls + 'total-row';
          } else
            rowCls =
              defaultCls +
              (record.item_ean?.id && record.item_ean
                ? record?.item_ean?.is_single
                  ? 'row-single'
                  : 'row-multi'
                : '');
          if (!record.mag_order_items?.length) {
            rowCls += ' bg-grey';
          } else {
            rowCls += ' bg-light-blue2';
          }

          return rowCls;
        }}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="export-pdf-pre2"
            size="small"
            icon={<FilePdfOutlined />}
            disabled={!dataSource?.length}
            title="Download pre summary in PDF"
            onClick={() => {
              const hide = message.loading('Downloading pre picklist data as PDF format...', 0);
              // const searchFormValues = searchFormRef.current?.getFieldsValue();
              // ignore filters
              const searchFormValues = {};
              setLoading(true);
              getPrePicklistSummaryPdf({ ...searchFormValues })
                .then((res) => {
                  hide();

                  if (res.extra?.toughSS?.length) {
                    setToughSSList(res.extra?.toughSS || []);
                    setOpenToughSSModal(true);
                    setTmpPrePdfFile({ type: res.type, key: res.key, url: res.url });
                  } else {
                    setToughSSList([]);
                    setOpenToughSSModal(false);
                    setTmpPrePdfFile(null);
                  }

                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                  setLoading(false);
                });
            }}
          >
            Pre Summary
          </Button>,
          <Button
            type="default"
            key="export-pdf-pre2-filtered"
            size="small"
            icon={<FilePdfOutlined />}
            disabled={!dataSource?.length}
            title="Download a filtered pre summary in PDF. Pagination will be skipped."
            onClick={() => {
              const hide = message.loading('Downloading pre picklist data as PDF format...', 0);
              const searchFormValues = searchFormRef.current?.getFieldsValue();
              setLoading(true);
              getPrePicklistSummaryPdf({ ...searchFormValues })
                .then((res) => {
                  hide();

                  if (res.extra?.toughSS?.length) {
                    setToughSSList(res.extra?.toughSS || []);
                    setOpenToughSSModal(true);
                  } else {
                    setToughSSList([]);
                    setOpenToughSSModal(false);
                  }

                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                  setLoading(false);
                });
            }}
          >
            Pre Summary (Filtered)
          </Button>,
        ]}
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentOrderItemRow?.item_ean?.id,
          item_id: currentOrderItemRow?.item_ean?.item_id,
          parent_id: currentOrderItemRow?.item_ean?.parent_id,
          is_single: currentOrderItemRow?.item_ean?.is_single,
          sku: currentOrderItemRow?.item_ean?.sku,
          ean: currentOrderItemRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentOrderItemRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentOrderItemRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentOrderItemRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <ToughStocksModal
        handleModalVisible={setOpenToughSSModal}
        modalVisible={openToughSSModal}
        stocks={toughSSList}
        tmpFile={tmpPrePdfFile}
        successCb={() => {
          actionRef.current?.reload();
        }}
      />
    </Modal>
  );
};

export default PrePicklistSummaryListModal;
