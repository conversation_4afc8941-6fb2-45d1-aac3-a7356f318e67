import { type EanPriceRecordType } from '@/services/foodstore-one/Item/ean';
import { ProTable, type ActionType, type ProColumns } from '@ant-design/pro-table';
import { useMemo, useRef, useEffect } from 'react';
import Util, { nf2, ni, sn } from '@/util';
import _ from 'lodash';
import { getOrderItemsList } from '@/services/foodstore-one/Magento/order-item';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormRadio } from '@ant-design/pro-form';

type SearchFormValueType = {
  is_single?: boolean;
};
export type RecordType = API.Order &
  API.OrderItem & {
    entity_id: number;
    qty_ordered: number;

    turnover: number;
    net_turnover: number;
    cturnover: number;
    ebay_fee: number;
    gp: number;
    bp: number;

    turnover_pcs: number;
    net_turnover_pcs: number;
    cturnover_pcs: number;
    ebay_fee_pcs: number;
    gp_pcs: number;
    bp_pcs: number;
  } & {
    cnt?: number;
  };

export type EanSaleStatsMiniTableProps = {
  itemEan?: EanPriceRecordType;
  eqSku?: string;
  refreshTick?: number;
  modalVisible?: boolean; // Parent's modal visible
  defaultPageSize?: number;
};

const EanSaleStatsMiniTable: React.FC<EanSaleStatsMiniTableProps> = (props) => {
  const { itemEan, modalVisible, defaultPageSize, eqSku } = props;

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const columns: ProColumns<RecordType>[] = useMemo(() => {
    return [
      {
        title: 'Date',
        dataIndex: ['created_at'],
        width: 80,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return Util.dtToDMY(record.created_at);
        },
      },
      {
        title: 'Increment ID',
        dataIndex: ['increment_id'],
        width: 120,
        hideInSearch: true,
        copyable: true,
      },
      /* {
        title: 'SKU',
        dataIndex: ['mag_order_items', 0, 'sku'],
        width: 120,
        align: 'center',
        hideInSearch: true,
        copyable: true,
      }, */
      {
        title: 'Qty',
        dataIndex: ['qty_ordered'],
        width: 40,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return ni(record.qty_ordered);
        },
      },
      {
        title: 'G. Turnover',
        dataIndex: ['turnover'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return nf2(sn(record.qty_ordered) * sn(record.price_incl_tax));
        },
      },
      {
        title: 'G. Turnover / pcs',
        dataIndex: ['turnover_pcs'],
        width: 60,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return sn(record.item_ean?.attr_case_qty)
            ? nf2(sn(record.price_incl_tax) / sn(record.item_ean?.attr_case_qty))
            : null;
        },
      },
      {
        title: 'GP',
        dataIndex: `gp`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          let cls = '';
          const gp = sn(record.gp);
          if (gp >= 5) cls += 'c-green';
          else if (gp >= 1) {
          } else if (gp >= 0) cls += 'c-orange';
          else cls += 'c-red';

          return <span className={cls}>{nf2(record.gp)}</span>;
        },
      },
    ];
  }, []);

  useEffect(() => {
    actionRef.current?.reload();
  }, [modalVisible, itemEan?.id]);

  return (
    <>
      <ProForm<SearchFormValueType>
        layout="inline"
        formRef={searchFormRef}
        isKeyPressSubmit
        className="search-form"
        size="small"
        submitter={false}
      >
        <ProFormRadio.Group
          name="eanMode"
          label=""
          radioType="button"
          initialValue={''}
          options={[
            { value: '', label: 'All' },
            { value: 'multi', label: 'Multi' },
            { value: 'single', label: 'Single' },
          ]}
          fieldProps={{ onChange: (e) => actionRef.current?.reload(), buttonStyle: 'solid' }}
        />
      </ProForm>

      <ProTable<RecordType, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={false}
        size="small"
        bordered
        columnEmptyText=""
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: defaultPageSize ?? 10,
        }}
        request={async (params, sort, filter) => {
          const sfValues = searchFormRef?.current?.getFieldsValue();

          return getOrderItemsList(
            { ...params, ...sfValues, sku: `${itemEan?.item_id}_`, eqSku, version: 2 },
            sort,
            filter,
          ) as any;
        }}
        scroll={{ x: 300 }}
        onRequestError={Util.error}
        rowClassName={(record) => {
          const recordIn = record.mag_order_items?.[0];
          if (!recordIn) return '';

          const defaultCls = 'disable-selection reset-tds-bg ';

          const rowCls =
            defaultCls +
            (recordIn.item_ean?.id && recordIn.item_ean
              ? recordIn?.item_ean?.is_single
                ? 'row-single'
                : 'row-multi'
              : '');
          return rowCls;
        }}
        search={false}
        columns={columns}
        tableAlertRender={false}
        locale={{ emptyText: null }}
        cardProps={{ bodyStyle: { padding: 0 }, headStyle: { padding: 0 } }}
      />
    </>
  );
};

export default EanSaleStatsMiniTable;
