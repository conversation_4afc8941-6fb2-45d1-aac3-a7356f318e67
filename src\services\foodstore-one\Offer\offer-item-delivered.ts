/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/offer-item-delivered';

/** get GET /api/offer-item-delivered */
export async function getOfferItemDeliveredList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * @deprecated not used!
 * 
 * POST /api/offer-item-delivered 
 * */
export async function addOfferItemDelivered(data: API.OfferItemDelivered, options?: { [key: string]: any }) {
  return request<API.OfferItemDelivered>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/offer-item-delivered/bulk */
export async function addOfferItemDeliveredBulk(rows: API.OfferItemDelivered[], options?: { [key: string]: any }) {
  return request<API.OfferItemDelivered>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: {
      rows,
    },
    ...(options || {}),
  });
}


/** put PUT /api/offer-item-delivered/{id} */
export async function updateOfferItemDelivered(id: number, data: API.OfferItemDelivered, options?: { [key: string]: any }) {
  return request<API.OfferItemDelivered>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}



/** delete DELETE /api/offer-item-delivered */
export async function deleteOfferItemDelivered(id: number | string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}


