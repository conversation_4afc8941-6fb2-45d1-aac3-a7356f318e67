import { DeleteOutlined } from '@ant-design/icons';
import { message, Space, Popconfirm, Button, Row, Col, Typography } from 'antd';
import React, { useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, skuToItemId, sn } from '@/util';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import { DefaultOptionType } from 'antd/lib/select';
import {
  deleteOfferItemIboPrePackReadyMap,
  getOfferItemIboPrePackReadyMapList,
} from '@/services/foodstore-one/Offer/offer-item-packed-ready';

export type SearchFormValueType = Partial<API.OfferItemIboPrePackReadyMap> & {
  includeSubTable?: boolean;
  trademark?: DefaultOptionType;
};

export type OfferItemPackedReadyListType = {
  ibo_pre_id?: number;
  offer_id?: number;

  reloadTick?: number;

  cbDelete?: (data?: any) => void;
};

/**
 *
 * @param props
 * @returns
 */
const OfferItemPackedReadyList: React.FC<OfferItemPackedReadyListType> = ({
  ibo_pre_id,
  offer_id,
  reloadTick,
  cbDelete,
}) => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  // const [loading, setLoading] = useState<boolean>(false);

  // const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  // const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  // const [currentRow, setCurrentRow] = useState<API.OfferItemIboPrePackReadyMap>();
  // const [selectedRowsState, setSelectedRows] = useState<API.OfferItemIboPrePackReadyMap[]>([]);

  const columns: ProColumns<API.OfferItemIboPrePackReadyMap>[] = [
    /* {
      title: 'Image',
      dataIndex: ['item_ean', 'files', 0, 'url'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      width: 80,
      render: (dom, record) => {
        const files = record.item_ean?.files;
        return files ? (
          <Image.PreviewGroup>
            {files &&
              files.map((file, ind) => (
                <Image
                  key={file.id}
                  src={file.thumb_url}
                  preview={{
                    src: file.url,
                  }}
                  wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                  width={40}
                />
              ))}
          </Image.PreviewGroup>
        ) : (
          <></>
        );
      },
    }, */
    /* {
      title: 'SKU',
      dataIndex: ['sku'],
      sorter: true,
      copyable: true,
      width: 120,
      render: (dom, record) => {
        return (
          <Row>
            <Col flex="auto">
              <Typography.Link
                href={`/item/ean-all-summary?sku=${skuToItemId(record.item_ean?.sku)}_`}
                target="_blank"
                copyable
              >
                {record.item_ean?.sku}
              </Typography.Link>
            </Col>
          </Row>
        );
      },
    }, */
    {
      title: 'Offer No',
      dataIndex: ['offer_item', 'offer', 'offer_no'],
      width: 60,
    },
    {
      title: 'Offer Qty (pcs)',
      dataIndex: ['offer_item', 'qty'],
      width: 80,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record.offer_item?.case_qty) * sn(record.offer_item?.qty));
      },
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 65,
      align: 'right',
      className: 'bl2',
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },

    {
      title: 'Qty (pcs)',
      dataIndex: ['qty_pcs'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record?.case_qty) * sn(record?.qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 60,
      fixed: 'right',
      render: (_, record) => [
        <Popconfirm
          key="delete"
          title="Are you sure you want to delete?"
          okButtonProps={{ size: 'large' }}
          cancelButtonProps={{ size: 'large' }}
          onConfirm={() => {
            deleteOfferItemIboPrePackReadyMap({
              offer_item_id: record.offer_item_id,
              ibo_pre_id: record.ibo_pre_id,
              ean_id: record.ean_id,
              exp_date: record.exp_date,
            })
              .then((res) => {
                message.success('Deleted successfully.');
                actionRef.current?.reload();
                cbDelete?.();
              })
              .catch(Util.error);
          }}
        >
          <Button type="default" key="rest" icon={<DeleteOutlined />} danger />
        </Popconfirm>,
      ],
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [ibo_pre_id]);

  useEffect(() => {
    if (reloadTick) {
      actionRef.current?.reload();
    }
  }, [reloadTick]);

  return (
    <>
      <ProTable<API.OfferItemIboPrePackReadyMap, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Qty Packed Ready List</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey={(entity) => `${entity.offer_item}_${entity.ibo_pre_id}_${entity.ean_id}_${entity.exp_date}`}
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: true, setting: false }}
        size="large"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        toolBarRender={() => []}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            ibo_pre_id,
            offer_id,
            with: '',
          };

          return getOfferItemIboPrePackReadyMapList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record.offer_item?.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
    </>
  );
};

export default OfferItemPackedReadyList;
