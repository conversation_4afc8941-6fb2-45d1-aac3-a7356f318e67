import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormUploadButton } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';

import Util, { sn } from '@/util';
import { importXls } from '@/services/foodstore-one/IBO/ibo-pre';
import { RcFile } from 'antd/lib/upload';

type FormValueType = API.IboPreManagement;

export type ImportXlsModalFormProps = {
  initialValues?: API.IboPreManagement;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPreManagement) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const ImportXlsModalForm: React.FC<ImportXlsModalFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit, onCancel } = props;
  const formRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (formRef.current && modalVisible) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues, modalVisible]);

  return (
    <ModalForm
      title={`Import XLS`}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      disabled={loading}
      submitter={{
        searchConfig: { submitText: 'Upload & Import' },
        submitButtonProps: { loading: loading || !initialValues?.id },
        onReset(value) {
          handleModalVisible(false);
          onCancel?.();
        },
      }}
      onFinish={async (values) => {
        if (!initialValues?.id) {
          message.warning('Please select Pre Order!');
          return;
        }

        const data = new FormData();
        data.set('ibo_pre_management_id', `${initialValues.id}`);
        data.set('supplier_id', `${initialValues.supplier_id}`);

        if (values?.files) {
          data.append(`files[]`, values?.files[0].originFileObj as RcFile);
        }

        const hide = message.loading('Uploading & importing file...');
        setLoading(true);
        const res = await importXls(data)
          .then((res) => {
            formRef.current?.resetFields();
            message.success('Imported successfully.');
            return res;
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });

        if (res) {
          handleModalVisible(false);
          if (onSubmit) onSubmit({ ...res });
        }
      }}
    >
      <ProFormUploadButton
        max={1}
        name="files"
        label="File"
        title="Select File"
        accept=".xls,.xlsx,.csv,.txt"
        required
        rules={[
          {
            required: true,
            message: 'File is required',
          },
        ]}
        fieldProps={{
          beforeUpload: (file) => {
            return false;
          },
        }}
      />
    </ModalForm>
  );
};

export default ImportXlsModalForm;
