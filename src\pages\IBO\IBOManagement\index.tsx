import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Drawer, Card, Typography, Row, Col } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { ni } from '@/util';
import CreateForm from './components/CreateForm';
import { getIBOManagementList, deleteIBOManagement } from '@/services/foodstore-one/IBO/ibo-management';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import ProForm, { ProFormCheckbox, ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import useSupplierOptions from '@/hooks/BasicData/useSupplierOptions';

export type SearchFormValueType = Partial<API.IBOManagement>;

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.IBOManagement[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteIBOManagement({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const IBOManagement: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();
  const { searchSupplierOptions } = useSupplierOptions();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.IBOManagement>();
  const [selectedRowsState, setSelectedRows] = useState<API.IBOManagement[]>([]);

  const columns: ProColumns<API.IBOManagement>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier_name',

      width: 150,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow({ ...entity });
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      hideInForm: true,
      defaultSortOrder: 'descend',
      width: 100,
      render: (__, record) =>
        record.order_no ? (
          <a
            href={`/ibo/item-buying-order?ibom_id=${record.id}`}
            target="_blank"
            rel="noreferrer"
            title="Open IBOs filtered on new tab"
          >
            {record.order_no}
          </a>
        ) : null,
    },
    {
      title: 'BIO?',
      dataIndex: 'bio',
      width: 50,
      tooltip: 'IBO which includes BIO EANs exists?',
      render(dom, record) {
        if (record.BIOExists) {
          return (
            <a
              href={`/ibo/item-buying-order?ibom_id=${record.id}&BIO_only=1`}
              target="_blank"
              rel="noreferrer"
              title="Open IBOs filtered on new tab"
            >
              BIO
            </a>
          );
        }
        return null;
      },
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      ellipsis: true,
    },
    {
      title: 'Unbooked Count',
      dataIndex: 'unbooked_count',
      tooltip: 'Open IBO Register page.',
      width: 90,
      align: 'right',
      render: (__, record) =>
        record.unbooked_count ? (
          <a href={`/ibo/ibo-register?ibomId=${record.id}`} target="_blank">
            {ni(record.unbooked_count)}
          </a>
        ) : null,
    },

    {
      title: 'Items w/o trademark',
      dataIndex: 'item_ids_w_o_trademark',
      tooltip: 'Click to open EAN detail page in new tab',
      width: 190,
      render: (__, record) => (
        <Row style={{ maxHeight: 60, overflowY: 'auto' }}>
          {record.item_ids_w_o_trademark?.split(',')?.map?.((itemId) => (
            <Col key={itemId} style={{ width: 45 }}>
              <Typography.Link href={`/item/ean-all-summary?sku=${itemId}_`} target="_blank">
                {`${itemId}  `}
              </Typography.Link>
            </Col>
          ))}
        </Row>
      ),
    },

    {
      title: 'Items w/o prices',
      dataIndex: 'skus_w_o_price',
      tooltip: 'Click to open EAN detail page in new tab',
      width: 270,
      render: (__, record) => (
        <Row style={{ maxHeight: 60, overflowY: 'auto' }}>
          {record.skus_w_o_price?.split(',')?.map?.((sku) => (
            <Col key={sku} style={{ width: 70 }}>
              <Typography.Link href={`/item/ean-all-summary?sku=${sku}`} target="_blank">
                {`${sku}  `}
              </Typography.Link>
            </Col>
          ))}
        </Row>
      ),
    },
    {
      title: 'Items w/o IBO Price',
      dataIndex: 'items_count_w_o_ibo_price',
      width: 60,
      render: (__, record) => ni(record.items_count_w_o_ibo_price),
    },
    {
      title: 'Items with blocked stocks',
      dataIndex: 'items_with_blocked_stock',
      tooltip: 'Click to open EAN detail page in new tab',
      width: 270,
      render: (__, record) => (
        <Row style={{ maxHeight: 60, overflowY: 'auto' }}>
          {record.items_with_blocked_stock?.split(',')?.map?.((item_id) => (
            <Col key={item_id} style={{ width: 70 }}>
              <Typography.Link href={`/stock/stock-warehouse?sku=${item_id}_`} target="_blank">
                {`${item_id}  `}
              </Typography.Link>
            </Col>
          ))}
        </Row>
      ),
    },

    {
      title: 'Order Date',
      dataIndex: 'order_date',
      hideInForm: true,
      valueType: 'date',
      width: 120,
      render: (dom, record) => Util.dtToDMY(record.order_date),
    },
    {
      title: 'Received Date',
      dataIndex: 'received_date',
      hideInForm: true,
      valueType: 'date',
      width: 120,
      render: (dom, record) => Util.dtToDMY(record.received_date),
    },
    {
      title: 'Owner',
      dataIndex: 'owner',

      hideInForm: true,
      width: 120,
      align: 'center',
    },
    {
      title: 'Created on',

      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return defaultRender(item);
      },
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',

      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return defaultRender(item);
      },
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      // tip: 'The username is the unique key',
      colSize: 1,
      search: false,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({ ...record });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  useEffect(() => {
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_ibo_management', {}));
  }, []);

  return (
    <PageContainer title="Item Buying Order Overview">
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={{}}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            request={async (params) => {
              return searchSupplierOptions(params);
            }}
            width="md"
            name="supplier_id"
            label="Supplier"
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name={'order_no'} label="Order No" width={180} placeholder={''} />
          <ProFormCheckbox
            name="bio_exists"
            label="BIO Exists?"
            fieldProps={{
              onChange(e) {
                actionRef.current?.reload();
              },
            }}
          />
        </ProForm>
      </Card>

      <ProTable<API.IBOManagement, API.PageParams>
        headerTitle={'Item Buying Order Overview'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        scroll={{ x: 900 }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        params={{
          with: 'BIOExists,includeUnbookedCount,includeItemsWithoutTrademark,includeItemsWithoutPrice,includeItemsWithBlockedStocks,includeItemCountWithoutIBOPrice',
        }}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ibo_management', searchValues);
          Util.setSfValues('sf_ibo_management_p', params);

          const newParams = {
            ...params,
            ...Util.mergeGSearch(searchValues),
          };

          return getIBOManagementList(newParams, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          selectedRowKeys: selectedRowsState.map((x) => x.id as any),
        }}
        columnEmptyText={''}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              IBOMs &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              const res = await handleRemove(selectedRowsState);
              if (res) {
                setSelectedRows([]);
                actionRef.current?.reload();
              }
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.IBOManagement>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.IBOManagement>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default IBOManagement;
