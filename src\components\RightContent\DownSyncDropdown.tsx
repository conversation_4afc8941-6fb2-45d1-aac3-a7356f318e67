/* eslint-disable @typescript-eslint/dot-notation */
import React, { useCallback } from 'react';
import { AuditOutlined, HddOutlined, ProfileOutlined, ShopOutlined, SyncOutlined } from '@ant-design/icons';
import { Menu, message, Modal } from 'antd';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import type { MenuInfo } from 'rc-menu/lib/interface';
import {
  dsFullOrders,
  dsFullOrderShipmentComments,
  dsFullQuotes,
  dsOrders,
  dsProductAttributes,
  dsProductAttributeSet,
  dsQuotes,
  dsStoreConfig,
  getAppSettings,
} from '@/services/foodstore-one/api';
import Util, { ni, notifySuccess } from '@/util';
import { useModel } from 'umi';
import { dsMagentoInventoryStock } from '@/services/foodstore-one/Stock/stock-compare';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const DownSyncDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const { appSettings, setAppSettings } = useModel('app-settings');

  const { magDsStat } = appSettings;

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'ds-store') {
        const hide = message.loading('Down Syncing magento store configuration info...', 0);
        dsStoreConfig()
          .then((res) => {
            setAppSettings((prev) => ({ ...prev, ...res }));
            hide();
            notifySuccess('Successfully synced!');
          })
          .catch(Util.error)
          .finally(hide);
        return;
      } else if (key === 'ds-product-attribute-set') {
        const hide = message.loading('Down Syncing product attribute set...', 0);
        dsProductAttributeSet()
          .then((res) => {
            hide();
            setAppSettings((prev) => ({
              ...prev,
              magDsStat: res.magDsStat,
              productAttributeSet: res.items,
            }));
            notifySuccess('Successfully synced!');
          })
          .catch(Util.error)
          .finally(hide);
        return;
      } else if (key === 'ds-product-attributes') {
        const hide = message.loading('Down Syncing product attributes...', 0);
        dsProductAttributes()
          .then(async (res) => {
            await getAppSettings()
              .then((res2) => setAppSettings(res2))
              .catch((e2) => Util.error('Failed to fetch app settings. Please try to reload a page!'));
            hide();
            notifySuccess('Successfully synced!');
          })
          .catch(Util.error)
          .finally(hide);
        return;
      } else if (key === 'ds-order-comments-full') {
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: "Are you sure you want to sync all order's shipment comments?",
            onOk: async () => {
              resolve(true);
              const hide = message.loading('Down Syncing full order shipment comments...', 0);
              dsFullOrderShipmentComments()
                .then((res) => {
                  hide();
                  setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  notifySuccess('Successfully synced!');
                })
                .catch(Util.error)
                .finally(hide);
              return true;
            },
            onCancel: () => {
              reject(true);
            },
          });
        });
      } else if (key === 'ds-quotes-full') {
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: 'Are you sure you want to sync all quotes?',
            onOk: async () => {
              resolve(true);
              const hide = message.loading('Down Syncing full quotes...', 0);
              dsFullQuotes()
                .then((res) => {
                  hide();
                  setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  notifySuccess('Successfully synced!');
                })
                .catch(Util.error)
                .finally(hide);
              return true;
            },
            onCancel: () => {
              reject(true);
            },
          });
        });
      } else if (key === 'ds-orders-full') {
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: 'Are you sure you want to sync all order?',
            onOk: async () => {
              resolve(true);
              const hide = message.loading('Down Syncing full orders...', 0);
              dsFullOrders()
                .then((res) => {
                  hide();
                  setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  notifySuccess('Successfully synced!');
                })
                .catch(Util.error)
                .finally(hide);
              return true;
            },
            onCancel: () => {
              reject(true);
            },
          });
        });
      } else if (key === 'ds-stocks-deep') {
        return new Promise((resolve, reject) => {
          Modal.confirm({
            title: (
              <>
                Are you sure you want to sync all stocks? <br />
                It would take some time.
              </>
            ),
            onOk: async () => {
              const hide = message.loading('Down Syncing stock items depply... It would take several minutes.', 0);
              return dsMagentoInventoryStock({ isDeep: true })
                .then((res) => {
                  setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  notifySuccess('Successfully synced!');
                })
                .catch((e) => {
                  console.log('error', e);
                  Util.error(e);
                })
                .finally(hide);
            },
            onCancel: () => {
              reject(true);
            },
          });
        });
      } else if (key === 'ds-stocks') {
        const hide = message.loading('Down Syncing stock items...', 0);
        dsMagentoInventoryStock()
          .then((res) => {
            setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
            notifySuccess(
              `${ni(res.magDsStat['xmag_inventory_stockbase']['sync_count'])} stock items synced successfully!`,
            );
          })
          .catch(Util.error)
          .finally(hide);
      } else if (key === 'ds-quotes') {
        const hide = message.loading('Down Syncing latest quotes...', 0);
        dsQuotes()
          .then((res) => {
            hide();
            setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
          })
          .catch(Util.error)
          .finally(hide);
      } else if (key === 'ds-orders') {
        const hide = message.loading('Down Syncing orders...', 0);
        dsOrders()
          .then((res) => {
            hide();
            setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
          })
          .catch(Util.error)
          .finally(hide);
      }
      return;
    },
    [setAppSettings],
  );

  const menuHeaderDropdown = (
    <Menu
      className={styles.menu}
      selectedKeys={[]}
      onClick={onMenuClick}
      items={[
        {
          key: 'ds-orders',
          label: (
            <>
              <div>
                <AuditOutlined />
                Orders
              </div>
              {magDsStat?.['xmag_orderbase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_orderbase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          key: 'ds-stocks-deep',
          label: (
            <>
              <div>
                <HddOutlined />
                Inventory Stock Items (deep)
              </div>
              {magDsStat?.['xmag_inventory_stockbase_deep']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_inventory_stockbase_deep']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          key: 'ds-stocks',
          label: (
            <>
              <div>
                <HddOutlined />
                Inventory Stock Items
              </div>
              {magDsStat?.['xmag_inventory_stockbase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_inventory_stockbase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          type: 'divider',
        },
        {
          key: 'ds-product-attributes',
          label: (
            <>
              <div>
                <ProfileOutlined />
                Product Attributes
              </div>
              {magDsStat?.['xmag_product_attributebase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_product_attributebase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          key: 'ds-product-attribute-set',
          label: (
            <>
              <div>
                <ProfileOutlined />
                Product Attributes Set
              </div>
              {magDsStat?.['xmag_product_attribute_setbase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_product_attribute_setbase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          key: 'ds-store',
          label: (
            <>
              <div>
                <ShopOutlined />
                Store Configuration
              </div>
              {magDsStat?.['xmag_store_configbase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_store_configbase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          type: 'divider',
        },
        {
          key: 'ds-orders-full',
          label: (
            <>
              <div>
                <AuditOutlined />
                Full Orders
              </div>
              {magDsStat?.['xmag_orderbase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_orderbase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          key: 'ds-order-comments-full',
          label: (
            <>
              <div>
                <AuditOutlined />
                Full Order Shipment Comments
              </div>
              {magDsStat?.['xmag_order_shipment_commentbase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_order_shipment_commentbase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          type: 'divider',
        },
        {
          key: 'ds-quotes',
          label: (
            <>
              <div>
                <AuditOutlined />
                Quotes
              </div>
              {magDsStat?.['xmag_quotebase']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_quotebase']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
        {
          key: 'ds-quotes-full',
          label: (
            <>
              <div>
                <AuditOutlined />
                Full Quotes
              </div>
              {magDsStat?.['xmag_quotefull']?.['last_sync_at'] && (
                <div className="text-sm c-gray" style={{ paddingLeft: 24 }}>
                  {Util.dtToDMYHHMM(magDsStat?.['xmag_quotefull']?.['last_sync_at'])}
                </div>
              )}
            </>
          ),
        },
      ]}
    />
  );

  return (
    <HeaderDropdown overlay={menuHeaderDropdown}>
      <span className={`${styles.action} ${styles.account}`}>
        <SyncOutlined /> &nbsp;
        <span className={`${styles.name} anticon`}>Down Sync</span>
      </span>
    </HeaderDropdown>
  );
};

export default DownSyncDropdown;
