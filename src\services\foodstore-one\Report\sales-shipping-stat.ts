import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/report/order';

export type RecordTypeSalesShippingStatsList = API.OrderItem;

/** 
 * Sales shipping stats monthly.
 * 
 * GET /api/report/order/sales-shipping-stat */
export async function getSalesShippingStatsList(
  params: API.PageParams & { year?: number; is_fiscal_year?: boolean; },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<RecordTypeSalesShippingStatsList> & { summary?: API.OrderItem } & { ymList?: string[] }> {
  return request<API.BaseResult>(`${urlPrefix}/sales-shipping-stat`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.data.length,
    summary: res.message.summary ?? {},
    ymList: res.message.ymList ?? [],
  }));
}
