import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, TreeSelect } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup, ProFormSwitch, ProFormTreeSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import _ from 'lodash';
import type { DataNode } from 'antd/lib/tree';
import { updateItem } from '@/services/foodstore-one/Item/item';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);
  const data = {
    id: fields.id,
    mode: 'categories',
    categories: fields?.categories?.map((x: any) => x.value),
    overrideEans: fields.overrideEans,
  };

  try {
    await updateItem(data as API.Item);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = {
  overrideEans?: boolean;
} & Partial<API.Ean>;

export type UpdateCategoriesFormProps = {
  initialValues?: Partial<API.Ean>;
  treeData: DataNode[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateCategoriesForm: React.FC<UpdateCategoriesFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  return (
    <ModalForm
      title={'Update item categories'}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      modalProps={{
        maskClosable: false,
      }}
      formRef={formRef}
      onFinish={async (value) => {
        if (formRef.current?.isFieldsTouched()) {
          const success = await handleUpdate({ ...value, id: props.initialValues?.id });

          if (success) {
            props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          props.handleModalVisible(false);
        }
      }}
    >
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormTreeSelect
          placeholder="Select categories"
          request={async () => props.treeData}
          allowClear
          name={'categories'}
          label="Select categories"
          // tree-select args
          fieldProps={{
            showCheckedStrategy: TreeSelect.SHOW_ALL,
            filterTreeNode: true,
            showSearch: true,
            dropdownMatchSelectWidth: false,
            autoClearSearchValue: true,
            multiple: true,
            labelInValue: true,
            showArrow: true,
            treeLine: true,
            treeNodeFilterProp: 'title',
            fieldNames: {
              label: 'title',
            },
          }}
        />
        <ProFormSwitch
          width="md"
          name="overrideEans"
          label="Override to all of EANs"
          tooltip="The category setting will be overwritten to all EANs of this item."
          initialValue={true}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateCategoriesForm;
