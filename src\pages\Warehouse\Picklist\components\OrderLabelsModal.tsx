import { SOrderId } from '@/pages/Magento/Order';
import { InfoCircleOutlined, LinkOutlined } from '@ant-design/icons';
import { Col, Modal, Row, Typography } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useModel } from 'umi';

export type OrderLabelsModalProps = {
  picklist: Partial<API.WarehousePicklist>;
  orderIds?: number[];
  order2labels?: Record<number | string, API.OrderLabel[]>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onCancel?: () => void;
};

const OrderLabelsModal: React.FC<OrderLabelsModalProps> = ({
  picklist,
  orderIds,
  order2labels,
  modalVisible,
  handleModalVisible,
}) => {
  const { getParcelUrl } = useModel('app-settings');

  const ele = orderIds?.map((orderId) => {
    return (
      <Row key={orderId} style={{ lineHeight: 2, borderTop: '1px solid #eee' }}>
        <Col span={4}>
          <SOrderId order={{ entity_id: orderId }} style={{ color: order2labels?.[orderId] ? 'auto' : 'red' }} />
        </Col>
        <Col span={20}>
          {order2labels?.[orderId]?.map((label) => {
            return (
              <Row key={label.id}>
                <Col span={5} className="text-center">
                  {label.service_name}
                </Col>
                <Col span={17}>
                  <Typography.Link
                    copyable
                    href={getParcelUrl(label.parcel_no, label.service_name as any)}
                    target="_blank"
                  >
                    {label.track_id}
                  </Typography.Link>
                </Col>
                <Col span={1} className="c-red">
                  <span title="Return Label">{label.ref_no?.endsWith(',Return') ? 'R' : ''}</span>
                </Col>
                <Col span={1}>
                  <Typography.Link href={`${API_URL}/api/${label.url}`} title="Open Label PDF" target="_blank">
                    <LinkOutlined />
                  </Typography.Link>
                </Col>
              </Row>
            );
          })}
        </Col>
      </Row>
    );
  });

  return (
    <Modal
      title={
        <span>
          Parcels in Picklist #{picklist?.id} - {picklist?.note}
        </span>
      }
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={500}
      onOk={() => handleModalVisible(false)}
      cancelButtonProps={{ style: { display: 'none' } }}
    >
      <Row style={{ lineHeight: 2 }}>
        <Col span={4}>Order ID</Col>
        <Col span={20}>
          <Row>
            <Col span={5} className="text-center">
              Provider
            </Col>
            <Col span={17}>Track ID</Col>
            <Col span={1}>
              <InfoCircleOutlined title="Return label?" />
            </Col>
            <Col span={1}>
              <InfoCircleOutlined title="View PDF file in new tab" />
            </Col>
          </Row>
        </Col>
      </Row>
      {ele}
    </Modal>
  );
};

export default OrderLabelsModal;
