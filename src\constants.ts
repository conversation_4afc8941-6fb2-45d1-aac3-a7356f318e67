export const LS_PREFIX = 'FS_';
export const LS_TOKEN_NAME = `${LS_PREFIX}TOKEN`;
export const DEFAULT_PER_PAGE_PAGINATION = 100;
export const AC_PER_PAGE_PAGINATION = 100;
export const EURO = '€';

export enum UserRole {
  USER = 0,
  ADMIN = 1,
  EDITOR = 2,
  WAREHOUSE = 3,
  STOCK_LOT = 4,
  QUOTES = 5,
}

export const UserRoleOptions = [
  { value: UserRole.USER, label: 'User' },
  { value: UserRole.ADMIN, label: 'Admin' },
  { value: UserRole.EDITOR, label: 'Content Editor' },
  { value: UserRole.WAREHOUSE, label: 'Warehouse' },
  { value: UserRole.STOCK_LOT, label: 'Stock Lots' },
  { value: UserRole.QUOTES, label: 'Quotes' },
];

export enum UserStatus {
  DISABLED = 0,
  ENABLED = 1,
  BLOCKED = 2,
}

export enum AlertStatus {
  'success' = 'success',
  'error' = 'error',
  'info' = 'info',
}

export enum ItemSpecialFilter {
  'SF_SUGAR_FREE' = 1,
  'SF_VEGAN' = 2,
  'SF_LACTOSE_FREE' = 3,
}

export const ItemSpecialFilterOptions = [
  { value: `${ItemSpecialFilter.SF_SUGAR_FREE}`, label: 'Zuckerfrei' },
  { value: `${ItemSpecialFilter.SF_VEGAN}`, label: 'Vegan' },
  { value: `${ItemSpecialFilter.SF_LACTOSE_FREE}`, label: 'Laktosefrei' },
];

/**
 * Item Ean's status
 */
export enum Status {
  'ACTIVE' = 1,
  'INACTIVE' = 0,
}

export const StatusOptions = [
  { value: Status.ACTIVE, label: 'Active' },
  { value: Status.INACTIVE, label: 'Inactive' },
];

export const YNOptions = [
  { value: 1, label: 'Ja' },
  { value: 0, label: 'Nein' },
];

export const YNOptionsStr = [
  { value: '1', label: 'Ja' },
  { value: '0', label: 'Nein' },
];

export enum ItemEANStatus {
  'ACTIVE' = 1,
  'INACTIVE' = 0,
}

export const ItemEANStatusOptions = [
  { value: ItemEANStatus.ACTIVE, label: 'Active' },
  { value: ItemEANStatus.INACTIVE, label: 'Inactive' },
];

export enum MopProductStatus {
  'DRAFT' = 0,
  'ACTIVE' = 1,
  'PASSIVE' = 2,
}

export const MopProductStatusOptions = [
  { value: MopProductStatus.DRAFT, label: 'Draft' },
  { value: MopProductStatus.ACTIVE, label: 'Active' },
  { value: MopProductStatus.PASSIVE, label: 'Passive' },
];

export enum ImportType {
  'SUPPLIER_DATA' = 1,
}

export enum ImportStatus {
  'STATUS_UPLOADED' = 0,
  'STATUS_IMPORTED' = 1,
}

export const ImportStatusOptions = [
  { value: ImportStatus.STATUS_UPLOADED, label: 'Uploaded' },
  { value: ImportStatus.STATUS_IMPORTED, label: 'Imported' },
];

export enum IbomOwner {
  'WHC' = 'WHC',
  'PLE' = 'PLE',
}

export const IbomOwnerOptions = [
  { value: IbomOwner.WHC, label: IbomOwner.WHC },
  { value: IbomOwner.PLE, label: IbomOwner.PLE },
];

export enum StockMovementReason {
  'In' = 'In',
  'Out' = 'Out',
  'Sold' = 'Sold',
  'MoveIn' = 'MoveIn',
  'MoveOut' = 'MoveOut',
  'MoveIn2' = 'MoveIn2',
  'MoveOut2' = 'MoveOut2', // Out to other
  'MoveInFromMulti' = 'MoveInFromMulti',
  'MoveOutToSingle' = 'MoveOutToSingle',
  'Correction' = 'Correction',
  'Damage' = 'Damage',
  'Sample' = 'Sample',
  'Return' = 'Return',
  'OutForPackedQty' = 'Out for Packed Qty',
}

export const StockMovementReasonOptions = [
  { value: StockMovementReason.In, label: 'In' },
  { value: StockMovementReason.Out, label: 'Out' },
  { value: StockMovementReason.Sold, label: 'Sold' },
  { value: StockMovementReason.MoveIn, label: 'Move In' },
  { value: StockMovementReason.MoveOut, label: 'Move Out' },
  { value: StockMovementReason.MoveIn2, label: 'In from other' },
  { value: StockMovementReason.MoveOut2, label: 'Out to other' },
  { value: StockMovementReason.MoveInFromMulti, label: 'Move from Multi' },
  { value: StockMovementReason.MoveOutToSingle, label: 'Move to Single' },

  { value: StockMovementReason.Damage, label: StockMovementReason.Damage },
  { value: StockMovementReason.Return, label: StockMovementReason.Return },
  { value: StockMovementReason.Correction, label: StockMovementReason.Correction },
  { value: StockMovementReason.OutForPackedQty, label: StockMovementReason.OutForPackedQty },
];

export const ProductWebsiteShortCode: Record<number, string> = {
  1: '1',
  2: 'G',
  3: 'RS',
};

export type MagentoOrderStatusType =
  | 'pending'
  | 'processing'
  | 'payment_review'
  | 'complete'
  | 'closed'
  | 'pending_payment'
  | 'pending_paypal'
  | 'canceled'
  | 'fraud'
  | 'holded'
  | 'paypal_canceled_reversal'
  | 'paypal_reversed'
  | string;

export const MagentoOrderStatusOptions: Record<MagentoOrderStatusType | string, string> = {
  pending: 'Pending',
  processing: 'Processing',
  payment_review: 'Payment Review',
  complete: 'Complete',
  closed: 'Closed',
  pending_payment: 'Pending Payment',
  pending_paypal: 'Pending PayPal',
  canceled: 'Canceled',
  fraud: 'Suspected Fraud',
  holded: 'On Hold',
  paypal_canceled_reversal: 'PayPal Canceled Reversal',
  paypal_reversed: 'PayPal Reversed',
};

export enum MagentoQuoteStatusEnum {
  Open = 0,
  Pending = 1,
  Approved = 2,
  Complete = 3,
  Cancel = 4,
  Holded = 5,
  AdminNew = 6,
  AdminCreated = 7,
}

export const MagentoQuoteStatusKv: Record<any, string> = {
  [MagentoQuoteStatusEnum.Open]: 'Open',
  [MagentoQuoteStatusEnum.Pending]: 'Pending',
  [MagentoQuoteStatusEnum.Approved]: 'Approved',
  [MagentoQuoteStatusEnum.Complete]: 'Complete',
  [MagentoQuoteStatusEnum.Cancel]: 'Canceled',
  [MagentoQuoteStatusEnum.Holded]: 'Holded',
  [MagentoQuoteStatusEnum.AdminNew]: 'Admin New',
  [MagentoQuoteStatusEnum.AdminCreated]: 'Admin Created',
};

export enum MagOrderExtraStatus {
  Success = '',
  OnHold = 'On Hold',
  OnHoldCleared = 'On Hold (Cleared)',
}

export const MagOrderExtraStatusOptions: any = [
  { value: MagOrderExtraStatus.Success, label: 'OK' },
  { value: MagOrderExtraStatus.OnHold, label: 'On Hold' },
  { value: MagOrderExtraStatus.OnHoldCleared, label: 'On Hold (Cleared)' },
];

export enum IboPreManagementStatus {
  Open = 'open',
  Sent = 'sent',
  Invoiced = 'invoiced',
  Done = 'done',
}

export const IboPreManagementStatusKv: Record<any, string> = {
  [IboPreManagementStatus.Open]: 'Open',
  [IboPreManagementStatus.Sent]: 'Sent',
  [IboPreManagementStatus.Invoiced]: 'Invoiced',
  [IboPreManagementStatus.Done]: 'Done',
};

/**
 * IBO status in Offer
 */
export enum OfferIboStatus {
  Open = 'open',
  InDiscussion = 'in discussion',
  Paid = 'paid',
  ExpectingItems = 'expecting items',
  InPreparation = 'in preparation',
  Closed = 'closed',
  ClosedLost = 'closed lost',
  WaitingForLoading = 'waiting for loading',
  Shipped = 'shipped',
}




export const OfferIboStatusKv: Record<any, string> = {
  [OfferIboStatus.Open]: 'Open',
  [OfferIboStatus.InDiscussion]: 'In Discussion',
  [OfferIboStatus.Paid]: 'Paid / Please Order',
  [OfferIboStatus.ExpectingItems]: 'Expecting Items',
  [OfferIboStatus.InPreparation]: 'In Preparation',
  [OfferIboStatus.WaitingForLoading]: 'Waiting for loading',
  [OfferIboStatus.Shipped]: 'Shipped',
  [OfferIboStatus.Closed]: 'Closed (Won)',
  [OfferIboStatus.ClosedLost]: 'Closed (Lost)',
};


export enum ScrapSystem {
  WoS = 'WoS',
  SwO = 'SwO',
}
export const ScrapSystemIds = [ScrapSystem.WoS, ScrapSystem.SwO];

export enum TaskStatus {
  Open = 0,
  InProgress = 1,
  Done = 2,
}

export const TaskStatusOptions = [
  { value: TaskStatus.Open, label: 'Open' },
  { value: TaskStatus.InProgress, label: 'In Progress' },
  { value: TaskStatus.Done, label: 'Done' },
];

/**
 * Dict types for creatable system configuration
 */
export enum DictType {
  SysConfig = 'sys config',
  ProductBadge = 'product badge',
  TitlePrefix = 'title prefix', // EAN title's prefix list
  Lang = 'lang',
  WarehousePicklistCarton = 'Carton', // Warehouse picklist PDF carton type
  WarehousePicklistFillMaterial = 'Fill Material', // Warehouse picklist PDF fill material type

  EmailConfig = 'Email Config', // Email config
  EmailStatus = 'Email Status', // Email status
  ExtOrder = 'Ext Order', // ext order

  CRMCaseStatus = 'CRM Case Status',
  // CRMCaseReason = 'CRM Case Reason',
  MagentoAdminURL = 'Magento Admin URL',
  EanTaskCategory = 'EAN Task Category',
  OpenaiTextModule = 'Openai Text Module',
  ShippingConfig = 'Shipping Config',
  EanPageFilter = 'EAN Page Filter',
  PicklistGrouping = 'Picklist Grouping',
  Offer = 'Offer',
  PreOrderDeliveryNotePrefix = 'Pre Order Delivery Note Prefix',
}

export const DictTypeKv: Record<DictType | string, string> = {
  [DictType.SysConfig]: 'System Config',
  [DictType.ProductBadge]: 'Product badge',
  [DictType.TitlePrefix]: 'Product Title Prefix',
  [DictType.Lang]: 'Language',
  [DictType.WarehousePicklistCarton]: 'Kartons (Picklist PDF)',
  [DictType.WarehousePicklistFillMaterial]: 'Füllmatarial (Picklist PDF)',
};


export const DictTypeCreatableKv: Record<DictType | string, string> = {
  [DictType.ProductBadge]: 'Product badge',
  [DictType.TitlePrefix]: 'Product Title Prefix',
  [DictType.WarehousePicklistCarton]: 'Kartons (Picklist PDF)',
  [DictType.WarehousePicklistFillMaterial]: 'Füllmatarial (Picklist PDF)',
  [DictType.EmailStatus]: DictType.EmailStatus,
  [DictType.CRMCaseStatus]: DictType.CRMCaseStatus,
  [DictType.PreOrderDeliveryNotePrefix]: DictType.PreOrderDeliveryNotePrefix,
};



export enum DictCode {
  MAG_ADMIN_URL_ORDER_BASE = 'MAG_ADMIN_URL_ORDER_BASE',
  MAG_ADMIN_URL_INVOICE = 'MAG_ADMIN_URL_INVOICE',
  MAG_ADMIN_URL_TRACKING = 'MAG_ADMIN_URL_TRACKING',
  MAG_ADMIN_URL_QUOTE = 'MAG_ADMIN_URL_QUOTE',
  MAG_SEARCH_URL = 'MAG_SEARCH_URL',
  EMAIL_STATUS_NO_ACTION = 'EMAIL_STATUS_NO_ACTION',
  EMAIL_BG_BY_CRM_CASE_STATUS = 'EMAIL_BG_BY_CRM_CASE_STATUS',
  EBAY_ORDER_ADMIN_URL = 'EBAY_ORDER_ADMIN_URL',
  KL_ORDER_ADMIN_URL = 'KL_ORDER_ADMIN_URL',

  CE_OPENAI_PRESET_TEXT_MODULE = 'CE_OPENAI_PRESET_TEXT_MODULE',
  CE_OPENAI_PRESET_TEXT_MODULE2 = 'CE_OPENAI_PRESET_TEXT_MODULE2',
  CE_OPENAI_PRESET_TEXT_MD = 'CE_OPENAI_PRESET_TEXT_MD',
  CE_OPENAI_PRESET_TEXT_MT = 'CE_OPENAI_PRESET_TEXT_MT',
  CE_OPENAI_PRESET_TEXT_SD = 'CE_OPENAI_PRESET_TEXT_SD',
  CE_OPENAI_PRESET_TEXT_LD = 'CE_OPENAI_PRESET_TEXT_LD',
  CE_OPENAI_PRESET_GOOGLE_DESC = 'CE_OPENAI_PRESET_GOOGLE_DESC',

  CE_OPENAI_PRESET_TEXT_MODULE_S = 'CE_OPENAI_PRESET_TEXT_MODULE_S',
  CE_OPENAI_PRESET_TEXT_MODULE2_S = 'CE_OPENAI_PRESET_TEXT_MODULE2_S',
  CE_OPENAI_PRESET_TEXT_MD_S = 'CE_OPENAI_PRESET_TEXT_MD_S',
  CE_OPENAI_PRESET_TEXT_MT_S = 'CE_OPENAI_PRESET_TEXT_MT_S',
  CE_OPENAI_PRESET_TEXT_SD_S = 'CE_OPENAI_PRESET_TEXT_SD_S',
  CE_OPENAI_PRESET_TEXT_LD_S = 'CE_OPENAI_PRESET_TEXT_LD_S',
  CE_OPENAI_PRESET_GOOGLE_DESC_S = 'CE_OPENAI_PRESET_GOOGLE_DESC_S',

  SUPPLIER_PRICE_PERCENTAGE = 'SUPPLIER_PRICE_PERCENTAGE',

  SHIPPING_PRINTER_DHL = 'SHIPPING_PRINTER_DHL',
  SHIPPING_PRINTER_GLS = 'SHIPPING_PRINTER_GLS',
  SHIPPING_PRINTER_DPD = 'SHIPPING_PRINTER_DPD',
  SHIPPING_PRINTER_DELIVERY_NOTE = 'SHIPPING_PRINTER_DELIVERY_NOTE',  // default printer

  PARCEL_TRACKING_GLS = 'PARCEL_TRACKING_GLS',
  PARCEL_TRACKING_DPD = 'PARCEL_TRACKING_DPD',
  PARCEL_TRACKING_DHL = 'PARCEL_TRACKING_DHL',

  PRINTER_API_URL = 'PRINTER_API_URL',
  PRINTER_API_TOKEN = 'PRINTER_API_TOKEN',
  LOTUS_PATH = 'LOTUS_PATH',

  // Picklist grouping by box
  PL_MAX_ORDERS_IN_BOX = 'PL_MAX_ORDERS_IN_BOX',
  PL_ORDERS_IN_BOX = 'PL_ORDERS_IN_BOX',
  PL_MAX_ORDER_ITEMS_IN_BOX = 'PL_MAX_ORDER_ITEMS_IN_BOX',
  // Offer
  OFFER_DEFAULT_PERCENTAGE = 'OFFER_DEFAULT_PERCENTAGE',
}

export enum ShippingAddressStatus {
  Open = 'Open',
  Problem = 'Problem',
  Done = 'Done',
}

export const DT_MAX_DATE = '2099-12-31';
export const DT_MIN_DATE = '1900-01-01';

export const DT_FORMAT_MY = 'MM.YYYY';
export const DT_FORMAT_DMY = 'DD.MM.YYYY';
export const DT_FORMAT_DMY_HHMMSS = 'DD.MM.YYYY';
export const DT_FORMAT_YMD = 'YYYY-MM-DD';
export const DT_FORMAT_TIME_MAX = '23:59:59';
export const DT_FORMAT_TIME_MAX_S = ' 23:59:59';
export const DT_FORMAT_LIST = [DT_FORMAT_DMY, DT_FORMAT_YMD];

export enum StockStableStatus {
  STATUS_AVAILABLE_SALE = 0,
  STATUS_BLOCK_FOR_SALE = 1,
}

export const StockStableStatusOptionsKv = {
  [StockStableStatus.STATUS_AVAILABLE_SALE]: 'Available Sale',
  [StockStableStatus.STATUS_BLOCK_FOR_SALE]: 'Block for Sale',
};

export const StockStableStatusOptions = [
  { value: StockStableStatus.STATUS_AVAILABLE_SALE, label: 'Available Sale' },
  { value: StockStableStatus.STATUS_BLOCK_FOR_SALE, label: 'Block for Sale' },
];

export const DAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

export const CURRENT_YEAR = new Date().getFullYear();

export enum CaseReason {
  Shipping = 'Shipping',
  MissingItems = 'Missing items',
  DamageItems = 'Damage items',
  Other = 'Other',
}

export const ShipmentCarrior: Record<string, string> = {
  shqtracker3: 'GLS - Deutschland',
  custom: 'System - DPD',
  dhl: 'DHL',
};

export type ShippingServiceNameType = 'GLS' | 'DPD' | 'DHL';



export enum PrePickingStatus {
  Open = 0,
  InProgress = 1,
  Done = 2,
}

export enum PickingStatus {
  Open = 0,
  InProgress = 1,
  Done = 2,
}
export enum PackingStatus {
  Open = 0,
  InProgress = 1,
  Done = 2,
}


export enum OrderUserActionLogType {
  OrderDetail = 'Order Detail',
  OrderDetailWH = 'Order Detail(WH)',
}
export enum OrderUserActionLogNote {
  Visit = 'Visit',
  Printed = 'Printed',
  PrintTried = 'Print tried',
  PrintFailed = 'Print Failed',
  OnHold = MagOrderExtraStatus.OnHold,
  OnHoldCleared = MagOrderExtraStatus.OnHoldCleared,
  Finish = 'Finish',
}

/**
 * Category of SysLog
 */
export enum SysLogCategory {
  CATEGORY_LOGIN = 'login',
  CATEGORY_IBO_PRE_REGISTER_PACKED_QTY_MODAL = 'Wholesale (IBO Pre) - Packed Ready Qty Modal',
  CATEGORY_OFFER = 'Offer',
  CATEGORY_OFFER_ITEM = 'Offer Item',
}

export const SysLogCategoryOptions = Object.entries(SysLogCategory).map((x, value) => {
  return {
    value: x[1],
    label: x[1],
  };
});


export const PageSizeOptionsOnCard3 = ['12', '24', '48', '60', '120'];

export const THIS_YEAR = new Date().getFullYear();