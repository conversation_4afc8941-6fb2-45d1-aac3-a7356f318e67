import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, message, Row, Space, Spin } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import _ from 'lodash';
import Util, { nf2, ni, sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { StockMovementReason, StockStableStatus, StockStableStatusOptions } from '@/constants';
import SProFormDigit from '@/components/SProFormDigit';
import type { StockStableCorrectionParamType } from '@/services/foodstore-one/Stock/stock-stable';
import { updateStockStableStatus } from '@/services/foodstore-one/Stock/stock-stable';
import { addStockStable } from '@/services/foodstore-one/Stock/stock-stable';
import { stockStableCorrection } from '@/services/foodstore-one/Stock/stock-stable';
import StockStableMoveSettingForm from './StockStableMoveSettingForm';
import type { DefaultOptionType } from 'antd/lib/select';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import type { Rule } from 'antd/lib/form';
import SDatePicker from '@/components/SDatePicker';
import { AlertOutlined, EditOutlined, InfoCircleOutlined, SaveOutlined } from '@ant-design/icons';
import StockStableUpdateExpForm from './StockStableUpdateExpForm';
import EditableCell from '../EditableCell';
import SelectIboModal from '@/pages/Stock/StockStable/components/SelectIboModal';

export type FormValueType = Partial<API.Ean>;

export type StockStableRowType = API.StockStable & {
  key?: string;
  box_qty_edit?: number;
  piece_qty_edit?: number;
  is_single?: boolean;
};

export type StockStableCorrectionEditableTableProps = {
  ean?: Partial<API.Ean>;
  initialData?: Partial<StockStableRowType>[];
  itemMode?: boolean;
  wl_id?: number;
  reload?: () => void;
};

const StockStableCorrectionEditableTable: React.FC<StockStableCorrectionEditableTableProps> = (props) => {
  const [loading, setLoading] = useState<boolean>(false);

  const editableFormRef = useRef<ProFormInstance>();
  const [data, setData] = useState<Partial<StockStableRowType>[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  // stock move form.
  const [moveModalVisible, handleMoveModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<Partial<StockStableRowType>>();
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);

  const [expDateModalVisible, handleExpDateModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  // open select IBO modal
  const [openSelectIboModalVisible, setOpenSelectIboModalVisible] = useState<boolean>(false);

  useEffect(() => {
    const newData = [...(props?.initialData || [])];
    for (const x of newData) {
      x.box_qty_edit = x.box_qty;
      x.piece_qty_edit = x.piece_qty;
    }
    setData(newData);
    setEditableRowKeys(newData.map((x) => x.key as React.Key));
  }, [props?.initialData]);

  const isEditable = props?.itemMode !== true;
  useEffect(() => {
    if (isEditable) {
      setEditableRowKeys(data.map((x) => x.key as React.Key));
    } else {
      setEditableRowKeys([]);
    }
  }, [isEditable, data]);

  useEffect(() => {
    getWarehouseLocationACList({})
      .then((res) => {
        setWarehouseLocations(res);
      })
      .catch(Util.error);
  }, []);

  const locations = useMemo(() => {
    const ret: any = {};
    if (warehouseLocations.length) {
      for (const w of warehouseLocations) {
        ret[w.id] = { text: w.label, value: w.id };
      }
    }
    return ret;
  }, [warehouseLocations]);

  const hiddenColumns: ProColumns<Partial<StockStableRowType>>[] = useMemo(() => [], []);

  const columnsAll: ProColumns<Partial<StockStableRowType>>[] = useMemo(
    () => [
      ...hiddenColumns,
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'id'],
        dataType: 'select',
        valueEnum: locations,
        editable: (value: any, record: any, index: number) => {
          return `${record.key}`.startsWith?.('newKey');
        },
        formItemProps: (form, { rowIndex }) => {
          const rules: Rule[] = [{ required: true }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        width: 80,
        align: 'left',
        render: (dom, record) => record.warehouse_location?.name,
        renderFormItem(schema, { record }, form, action) {
          return (
            <ProFormSelect
              options={warehouseLocations || []}
              style={{ marginBottom: 0 }}
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                showSearch: true,
                onInputKeyDown: (e: any) => {
                  if (Util.isTabPressed(e)) {
                    const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
                    if (dropdownWrapper?.children.length == 1) {
                      const id = e.target.getAttribute('aria-activedescendant');
                      const value = sn(document.getElementById(id)?.innerText);

                      setData((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.key == record?.key);
                        if (current) {
                          (current as any).warehouse_location = { id: value };
                        }
                        return newList;
                      });
                    }
                  }
                },
              }}
            />
          );
        },
      },
      {
        title: 'Priority',
        dataIndex: ['warehouse_location', 'priority'],
        width: 80,
        align: 'right',
        editable: false,
        className: 'text-sm c-grey',
        render(__, entity) {
          return ni(entity.warehouse_location?.priority);
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 90,
        align: 'left',
        valueType: 'date',
        editable: (value: any, record: any, index: number) => {
          return `${record.key}`.startsWith?.('newKey');
        },
        formItemProps: (form, { rowIndex }) => {
          const rules: Rule[] = [{ required: true }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender, ...rest }, form) => {
          return <SDatePicker placeholder="EXP. Date" formItemProps={{ style: { marginBottom: 0, marginRight: 0 } }} />;
        },
        render: (dom, record) => {
          return (
            <>
              <span style={{ verticalAlign: 'middle' }}>{Util.dtToDMY(record?.exp_date)}</span>
              <Button
                type="link"
                title="Change Exp. Date..."
                size="small"
                icon={<EditOutlined />}
                style={{ float: 'right' }}
                onClick={() => {
                  setCurrentRow({ ...record });
                  handleExpDateModalVisible(true);
                }}
              />
            </>
          );
        },
      },

      {
        title: 'Case Qty',
        dataIndex: ['case_qty'],
        width: 70,
        align: 'right',
        className: 'c-grey',
        editable: false,
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        render: (__, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'New Pcs Qty',
        dataIndex: ['piece_qty_edit'],
        width: 110,
        align: 'right',
        className: 'p0',
        formItemProps: () => {
          return {};
        },
        editable: (value, row, index) => !!row?.item_ean?.is_single,
        renderFormItem: (item, { record }) => {
          return (
            <SProFormDigit readonly={!isEditable} min={0} formItemProps={{ style: { margin: 0 } }} placeholder="" />
          );
        },
        render: (__, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'New Box Qty',
        dataIndex: ['box_qty_edit'],
        width: 100,
        align: 'right',
        formItemProps: () => {
          return {};
        },
        className: 'p0',
        editable: (value, row, index) => !props.ean?.is_single,
        renderFormItem: (item, { record }) => {
          return (
            <SProFormDigit readonly={!isEditable} min={0} formItemProps={{ style: { margin: 0 } }} placeholder="" />
          );
        },
      },
      {
        title: 'Status',
        dataIndex: 'status',
        width: 80,
        editable: false,
        align: 'center',
        tooltip: 'Click to change.',
        render: (dom, record, index, action) => {
          const defaultValue = record.status;
          return (
            <EditableCell
              dataType="select"
              defaultValue={defaultValue}
              dataOptions={StockStableStatusOptions}
              rules={[
                {
                  required: true,
                  message: 'Status is required',
                },
              ]}
              fieldProps={{ dropdownMatchSelectWidth: false }}
              width={80}
              convertValue={(value) => (value === null ? value : value)}
              triggerUpdate={async (newValue: any, cancelEdit) => {
                return updateStockStableStatus({
                  id: record.id,
                  status: newValue,
                })
                  .then((res) => {
                    message.destroy();
                    message.success('Updated successfully.');
                    props.reload?.();
                    cancelEdit?.();
                  })
                  .catch(Util.error);
              }}
            >
              <span
                className={record.status != StockStableStatus.STATUS_AVAILABLE_SALE ? 'c-orange' : ''}
                style={{ verticalAlign: 'middle' }}
              >
                {StockStableStatusOptions.find((x) => x.value == record.status)?.label}
              </span>
            </EditableCell>
          );
        },
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'left',
        fixed: 'right',
        width: 220,
        render: () => {
          return null;
        },
      },
      {
        title: 'BP / IBOM',
        dataIndex: ['ibo_id'],
        width: 120,
        align: 'left',
        editable: false,
        tooltip: 'Click to change',
        fixed: 'right',
        render: (dom, record: StockStableRowType) => {
          return record.ibo ? (
            <Row gutter={4}>
              <Col span={10}>{record.ibo.price ? `€${nf2(record.ibo.price)}` : ''}</Col>
              <Col
                span={14}
                className=" ant-table-cell-ellipsis"
                title={'IBO ID: #' + record.ibo.id}
              >{`#${record.ibo.ibom?.order_no} | ${record.ibo.ibom?.supplier?.name}`}</Col>
            </Row>
          ) : null;
        },
        onCell: (record) => {
          return record.ibo
            ? {
                // todo: This will be disabled after testing
                onClick: (e) => {
                  setCurrentRow({ ...record });
                  setOpenSelectIboModalVisible(true);
                },
                className: 'cursor-pointer',
              }
            : {
                onClick: (e) => {
                  setCurrentRow({ ...record });
                  setOpenSelectIboModalVisible(true);
                },
                className: 'cursor-pointer',
              };
        },
      },
    ],
    [hiddenColumns, locations, warehouseLocations, props, isEditable],
  );

  const handleCorrection = (row: StockStableCorrectionParamType) => {
    if (!isEditable) {
      message.error('Please switch "Include All" off!');
      return;
    }

    if (sn(row?.box_qty_edit) < 0 && sn(row?.piece_qty_edit) < 0) {
      message.error('Invalid quantities! Please fill New Box Qty or New Piece Qty correctly');
      return;
    }

    const hide = message.loading('Updating...', 0);
    setLoading(true);
    stockStableCorrection({
      ...row,
      ean_id: props.ean?.id,
    })
      .then((res) => {
        hide();
        if (res.message === false) {
          message.info('Nothing to update! Please adjust new Qty.');
        } else {
          message.success('Successfully updated');
          props.reload?.();
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  const handleCreate = (row: StockStableCorrectionParamType) => {
    if (sn(row?.box_qty_edit) < 0 && sn(row?.piece_qty_edit) < 0) {
      message.error('Invalid qtys! Please fill New Box Qty or New Piece Qty correctly');
      return;
    }

    const hide = message.loading('Adding...', 0);
    setLoading(true);
    addStockStable({
      ...row,
      ean_id: props.ean?.id,
    })
      .then((res) => {
        hide();
        if (res.message === false) {
          message.info('Nothing to update! Please adjust new Qty.');
        } else {
          message.success('Successfully added');
          props.reload?.();
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  const handleOpenBox = (row: StockStableCorrectionParamType & { box_qty?: number }) => {
    if (!isEditable) {
      message.error('Please switch "Include All" off!');
      return;
    }

    if (sn(row?.box_qty) < 0) {
      message.error('No box is available to open!');
      return;
    }

    const hide = message.loading('Opening a box...', 0);
    setLoading(true);
    stockStableCorrection({
      ...row,
      ean_id: props.ean?.id,
    })
      .then((res) => {
        hide();
        if (res.message === false) {
          message.info('Nothing to update!');
        } else {
          message.success('Successfully opened!');
          props.reload?.();
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  return (
    <>
      <Spin spinning={loading}>
        <EditableProTable
          rowKey={'key'}
          headerTitle={
            <>
              Qty by Location & Exp.Date & IBO&nbsp;
              <InfoCircleOutlined title="Light yellow background: Min Exp.date of Single. Light red background: Min Exp.date of Multi." />
            </>
          }
          cardProps={{ bodyStyle: { padding: 0 } }}
          columns={columnsAll}
          dataSource={data}
          search={false}
          actionRef={actionRef}
          editableFormRef={editableFormRef}
          value={data}
          onChange={setData}
          toolbar={{
            search: false,
            actions: undefined,
            menu: undefined,
            // settings: undefined,
          }}
          scroll={{ x: '100%' }}
          // todo
          pagination={{ defaultPageSize: 20 }}
          className="w-full"
          rowClassName={(record) => {
            let cls = record.is_single ? 'row-single' : 'row-multi';
            if (record.is_single) {
              const minExp = data.reduce((prev, x) => {
                return x.is_single && x.exp_date && prev > x.exp_date ? x.exp_date : prev;
              }, '9999-99-99');
              if (minExp == record.exp_date) cls += ' bg-light-orange2';
            } else {
              const minExp = data.reduce((prev, x) => {
                return !x.is_single && x.exp_date && prev > x.exp_date ? x.exp_date : prev;
              }, '9999-99-99');
              if (minExp == record.exp_date) cls += ' bg-light-red2';
            }
            return cls;
          }}
          size="small"
          controlled
          recordCreatorProps={{
            newRecordType: 'dataSource',
            position: 'top',
            creatorButtonText: 'Add new stock',
            record: (index: number, dataSource2: StockStableRowType[]) => {
              return { key: 'newKey' + Date.now().toString() + '-' + index };
            },
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              const isMovable = sn(props.ean?.is_single ? row.single_piece_qty : row.box_qty) > 0;
              const isCorrectEan = props?.ean?.id == row?.item_ean?.id;

              if (`${row.key}`?.startsWith?.('newKey')) {
                return [
                  <Button
                    key="create"
                    type="primary"
                    size="small"
                    className=""
                    title="Create"
                    onClick={() => {
                      const wlId = sn(row.warehouse_location?.id);
                      if (!wlId) {
                        message.info('Please select Location');
                        return;
                      }

                      if (!row.exp_date) {
                        message.info('Please fill Exp.Date');
                        return;
                      }

                      handleCreate({
                        ...row,
                        reason: StockMovementReason.In,
                        warehouse_location: { id: wlId },
                      });
                    }}
                    disabled={!isEditable}
                  >
                    Add
                  </Button>,
                  defaultDoms.delete,
                ];
              }

              return [
                <Space key={'actions'}>
                  <Button
                    type="primary"
                    size="small"
                    className=""
                    title="Correction"
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Correction });
                    }}
                    disabled={!isEditable || !isCorrectEan}
                    icon={<SaveOutlined />}
                  />
                  {sn(row.case_qty) > 1 && !row.is_single && sn(row.box_qty) >= 1 && (
                    <Button
                      type="primary"
                      size="small"
                      className=""
                      title="Open a box for single item"
                      ghost
                      onClick={() => {
                        handleOpenBox({ ...row, reason: StockMovementReason.MoveOutToSingle });
                      }}
                      disabled={!isEditable}
                    >
                      Open Box
                    </Button>
                  )}
                  {isEditable && isMovable && (
                    <Button
                      type="primary"
                      size="small"
                      className=""
                      title="Move stock to another location"
                      ghost
                      onClick={() => {
                        setCurrentRow({ ...row });
                        handleMoveModalVisible(true);
                      }}
                      disabled={!isEditable || !isMovable}
                    >
                      Move
                    </Button>
                  )}
                  <Button
                    type="default"
                    size="small"
                    className=""
                    title="Correction by damage"
                    danger
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Damage });
                    }}
                    disabled={!isEditable || !isCorrectEan}
                    icon={<AlertOutlined />}
                  />
                  <Button
                    type="dashed"
                    size="small"
                    className=""
                    title="Correction by sample"
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Sample });
                    }}
                    disabled={!isEditable || !isCorrectEan}
                  >
                    S
                  </Button>
                  {/* <Button
                    type="primary"
                    size="small"
                    className=""
                    title="Correction by sold"
                    ghost
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Out });
                    }}
                    disabled={!isEditable}
                  >
                    Sold
                  </Button> */}
                </Space>,
              ];
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
            deleteText: (
              <Button type="default" size="small" danger>
                Delete
              </Button>
            ),
          }}
        />
      </Spin>
      <StockStableMoveSettingForm
        modalVisible={moveModalVisible}
        handleModalVisible={handleMoveModalVisible}
        ean={props.ean}
        initialData={currentRow || {}}
        onSubmit={async () => {
          props.reload?.();
        }}
        onCancel={() => {
          handleMoveModalVisible(false);
        }}
      />
      <StockStableUpdateExpForm
        modalVisible={expDateModalVisible}
        handleModalVisible={handleExpDateModalVisible}
        ean={props.ean}
        initialData={currentRow || {}}
        onSubmit={async () => {
          props.reload?.();
        }}
        onCancel={() => {
          handleExpDateModalVisible(false);
        }}
      />
      <SelectIboModal
        modalVisible={openSelectIboModalVisible}
        handleModalVisible={setOpenSelectIboModalVisible}
        initialValue={{
          id: sn(currentRow?.id),
          ibo_id: currentRow?.ibo_id,
          itemEan: currentRow?.item_ean,
        }}
        selectCallback={(cbData: API.StockStable) => {
          setOpenSelectIboModalVisible(false);
          props.reload?.();
        }}
      />
    </>
  );
};

export default StockStableCorrectionEditableTable;
