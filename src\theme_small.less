.ant-page-header {
  padding: 8px 24px;
  list-style: none;
}

/*
Table customization
-------------------------------------------------------------------
*/
.ant-table.ant-table-small .ant-table-title,
.ant-table.ant-table-small .ant-table-footer,
.ant-table.ant-table-small .ant-table-thead > tr > th,
.ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
.ant-table.ant-table-small tfoot > tr > th,
.ant-table.ant-table-small tfoot > tr > td {
  padding: 3px 5px !important;
  font-size: 12px;

  &.p-0 {
    padding: 0 !important;
  }
}
.ant-pro-table td.ant-table-cell > a {
  font-size: 12px !important;
  line-height: 20px;
}
.ant-radio-wrapper {
  font-size: 12px;
}

/** Form
-------------------------------------- */
.ant-form-inline .ant-form-item {
  margin-right: 8px;
}
.search-form .ant-space.ant-space-horizontal {
  align-items: start;
}

/* .ant-form-item-label > label {
  font-size: 12px;
  line-height: 20px;
}

.ant-input-affix-wrapper {
  position: relative;
  display: inline-block;
  display: inline-flex;
  width: 100%;
  min-width: 0;
  padding: 4px 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  line-height: 20px;
  line-height: 1.5715;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s;
}

.ant-select {
  font-size: 12px;
}

.ant-select-item {
  min-height: 30px;
  padding: 3px 8px;
  font-size: 12px;
  line-height: 20px;
}

.ant-select-multiple .ant-select-selection-item {
  height: 22px;
  line-height: 20px;
} */

.ant-back-top {
  position: fixed;
  right: 0;
  bottom: 30px;
  font-size: 12px;
}

/** button
-------------------------------------- */
.ant-btn-sm.ant-btn-xs {
  height: 16px;
  padding: 0 3px 0 3px;
  font-size: 11px;
  line-height: 1.1;
  border-radius: 2px;
}

/** tabs
-------------------------------------- */
.ant-tabs-small.ant-tabs-xs > .ant-tabs-nav .ant-tabs-tab {
  padding: 8px 0;
  font-size: 12px;
}
.ant-tabs-small.ant-tabs-xs .ant-tabs-tab + .ant-tabs-tab {
  margin: 0 0 0 16px;
}

.form-list-sm {
  .ant-pro-form-list-action {
    margin-bottom: 0;
  }
}

/** Notification
----------------------------------- */
.ant-notification-notice {
  padding: 10px 12px 6px 12px;

  .ant-notification-notice-close {
    top: 8px;
    right: 11px;
  }

  .ant-notification-notice-icon {
    font-size: 16px;
    line-height: 24px;
  }

  .ant-notification-notice-message {
    line-height: 20px;
  }
  .ant-notification-notice-with-icon .ant-notification-notice-message {
    margin-left: 32px;
    font-size: 14px;
  }
}

/** Styles Global components
----------------------------------------- */
.ant-tag.website-icon {
  margin-right: 0;
  padding: 0 4px 0 3px;
  font-size: 10px;
  line-height: 12px;
  border-radius: 100%;
}

/** Narrow menu 
----------------------------------------- */
.ant-menu-vertical > .ant-menu-item,
.ant-menu-vertical-left > .ant-menu-item,
.ant-menu-vertical-right > .ant-menu-item,
.ant-menu-inline > .ant-menu-item,
.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  height: 32px;
  line-height: 32px;
}
