/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/basic-data/vat';

/** rule GET /api/basic-data/vat */
export async function getVatList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/basic-data/vat */
export async function updateVat(data: API.Vat, options?: { [key: string]: any }) {
  return request<API.Vat>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/vat */
export async function addVat(data: API.Vat, options?: { [key: string]: any }) {
  return request<API.Vat>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/vat/{id} */
export async function deleteVat(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
