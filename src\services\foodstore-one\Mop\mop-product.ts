/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/lib/select';

const urlPrefix = '/api/mop/mop-product';

/** get GET /api/mop/mop-product */
export async function getMopProductList(params: API.ItemSupplierPageParams & Partial<API.MopProduct>, sort?: any, filter?: any) {
  const newSorter: any = { ...sort };

  return request<API.ResultList<API.MopProduct>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort: newSorter,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getMopProductWithAllImages(id: string, params?: { [key: string]: string }) {
  return request<DefaultOptionType>(`${urlPrefix}/${id}/images-all`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/** 
 * Create a new entity
 * 
 * POST /api/mop/mop-product 
 */
export async function addMopProduct(data: API.MopProduct, options?: Record<string, any>) {
  return request<API.ResultObject<API.MopProduct>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** 
 * Update a new entity
 * 
 * PUT /api/mop/mop-product/{id} 
 */
export async function updateMopProduct(id: number, data: API.MopProduct, options?: Record<string, any>) {
  const url = `${urlPrefix}/${id}`;
  const config: any = {
    method: data instanceof FormData ? 'POST' : 'PUT',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config['body'] = data;
  } else {
    config['data'] = data;
  }

  return request<API.ResultObject<API.MopProduct & { upSyncMessage?: string }>>(url, config).then((res: any) => res.message);
}

/**
 * Update mop product image
 * 
 * PUT /api/mop/mop-product/{id}/images 
 */
export async function updateMopSysImage(id: number, data?: { [key: string]: string }) {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/images`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}


/** 
 * Delete products info.
 * 
 * DELETE /api/mop/mop-product */
export async function deleteMopProduct(options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}


/** delete DELETE /api/mop/mop-product */
export async function deleteMopProductFile(options?: Record<string, any>) {
  return request<Record<string, any>>(
    `${urlPrefix}/${options ? options['id'] : ''}/${options ? options['fileId'] : ''}`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}



/** 
 * Down sync Mop Products from Magento
 * 
 * GET /api/mop/mop-product/ds */
export async function dsMopProductList(params?: API.ItemSupplierPageParams) {
  return request<API.ResultList<any>>(`${urlPrefix}/ds`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** 
 * UpSync Mop Products to Magento
 * 
 * POST /api/mop/mop-product/{id}/us/product-full */
export async function usMopProductFull(id: number, data?: Record<string, any>): Promise<API.MopProduct> {
  /* return new Promise((resolve) => {
    setTimeout(() => resolve({}), 20000);
  }); */

  return request<API.AppApiResponse>(`${urlPrefix}/${id}/us/product-full`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}

/** post POST /api/mop/mop-product/{id}/us-images */
export async function usMopAllImages(id: number, data?: Record<string, any>): Promise<Shop.Product> {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/us-images`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}

