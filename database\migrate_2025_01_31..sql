drop table if exists offer_item_ibo_pre_pack_ready_map;

CREATE TABLE `offer_item_ibo_pre_pack_ready_map`
(
    `offer_item_id` bigint(20) unsigned NOT NULL,
    `ibo_pre_id`    bigint(20) unsigned NOT NULL,
    `ean_id`        bigint(20) unsigned NOT NULL COMMENT 'EAN ID of IBO Pre filled',
    `exp_date`      date                NOT NULL COMMENT 'Exp.Date of IBO Pre filled',
    `case_qty`      int(11)                      DEFAULT NULL COMMENT 'Case Qty of IBO Pre filled',
    `qty`           int(11)             NOT NULL DEFAULT 0 COMMENT 'Box qty of IBO Pre filled',
    `updated_on`    datetime                     DEFAULT NULL,
    PRIMARY KEY (`offer_item_id`, `ibo_pre_id`, `ean_id`, `exp_date`),
    KEY `FK_offer_item_ibo_pre_pack_ready_map_ibo_pre_id` (`ibo_pre_id`),
    KEY `FK_offer_item_ibo_pre_pack_ready_map_ean_id` (`ean_id`),
    CONSTRAINT `FK_offer_item_ibo_pre_pack_ready_map_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_ibo_pre_pack_ready_map_ibo_pre_id` FOREIGN KEY (`ibo_pre_id`) REFERENCES `ibo_pre` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_ibo_pre_pack_ready_map_offer_item_id` FOREIGN KEY (`offer_item_id`) REFERENCES `offer_item` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='Offer Item''s qty is base and allocated IBO Pre''s qty is qty column of this table.';
