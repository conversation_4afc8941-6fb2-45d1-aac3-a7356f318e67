DELETE
FROM sys_dict
WHERE code = 'EMAIL_BG_BY_CRM_CASE_STATUS';

INSERT INTO `sys_dict` (`code`, `type`, `label`, `desc`)
VALUES ('EMAIL_BG_BY_CRM_CASE_STATUS', 'Email Config', 'Email background color by CRM Case Status. Use `Code` in Config.',
        'e.g. Case Status 1 Code [#ff00ff], Case Status x Code [#ff0000]');

UPDATE sys_dict
SET `value` = (SELECT GROUP_CONCAT(CONCAT(`code`, ' ', '[#ffffff]') ORDER BY sort) FROM sys_dict WHERE type = 'CRM Case Status')
WHERE code = 'EMAIL_BG_BY_CRM_CASE_STATUS';
