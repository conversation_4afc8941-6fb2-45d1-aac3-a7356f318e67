<?php

use App\Lib\Func;
use App\Models\ItemEan;
use App\Models\Magento\MagProductAttributeSet;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';


echo PHP_EOL;
var_dump(120 - NULL);

echo PHP_EOL;
$nums = [
    1,
    2.2,
    -0.1,
    -4,
    "123.45 $",
    "123.00 $",
    "123.00$",
    "123$",
    "-123$",
    // German format
    "1,200.39$",
    "1.200,39$",
    "1,200.39",
    "1.200,39",

    "1,231,200.39 $",
    "1,231,200.39$",

    "1.231.200,39 $",
    "1.231.200,39$",

    "1.231.200,39",
    "1.231.200,39",

    "2,3$",
    "2,39",
];

echo "------------------------------------------------------------------" . PHP_EOL;
echo sprintf("% 15s     % 15s   %5s", 'Input', "Output", "is_numeric") . P<PERSON>_E<PERSON>;
echo "------------------------------------------------------------------" . PHP_EOL;
foreach ($nums as $num) {
    echo sprintf("% 15s --> % 15s   %5s", $num, Func::safeDouble($num), is_numeric($num)) . PHP_EOL;
}

