ALTER TABLE `ean_price`
    ADD COLUMN `created_on` <PERSON>ATETIME NULL AFTER `end_date`,
    ADD COLUMN `updated_on` DATETIME NULL AFTER `created_on`;

drop table if exists item_ean_price_history;

CREATE TABLE `item_ean_price_history`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `ean_id`        BIGINT unsigned DEFAULT NULL,
    `price_type_id` int             DEFAULT NULL,
    `price`         double          DEFAULT 0,
    `created_on`    datetime        DEFAULT NULL,
    `deleted_on`    datetime        DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_item_ean_price_history_ean_id_type` (`ean_id`, `price_type_id`),
    KEY `FK_item_ean_price_history_ean_id` (`ean_id`),
    CONSTRAINT `FK_item_ean_price_history_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


-- ========================================================================
-- After Insert trigger
-- ========================================================================
drop trigger if exists ean_price_after_insert;

DELIMITER $$

CREATE
    TRIGGER `ean_price_after_insert`
    AFTER INSERT
    ON `ean_price`
    FOR EACH ROW
BEGIN
    INSERT into item_ean_price_history(ean_id, price_type_id, price, created_on)
    values (NEW.ean_id, NEW.price_type_id, NEW.price, NOW());
END$$

DELIMITER ;


-- ========================================================================
-- After Update trigger
-- ========================================================================
drop trigger if exists ean_price_after_update;

DELIMITER $$

CREATE
    TRIGGER `ean_price_after_update`
    AFTER UPDATE
    ON `ean_price`
    FOR EACH ROW
BEGIN
    if NEW.price != OLD.price then
        INSERT into item_ean_price_history(ean_id, price_type_id, price, created_on)
        values (NEW.ean_id, NEW.price_type_id, NEW.price, NOW());
    end if;

    if NEW.price_type_id != OLD.price_type_id then
        update import_supplier_data_price
        set price_type_id = NEW.price_type_id
        where ean_id = OLD.ean_id
          AND price_type_id = OLD.price_type_id;
    end if;
END$$

DELIMITER ;


-- ========================================================================
-- After Delete trigger
-- ========================================================================
drop trigger if exists ean_price_after_delete;

DELIMITER $$

CREATE
    TRIGGER `ean_price_after_delete`
    AFTER DELETE
    ON `ean_price`
    FOR EACH ROW
BEGIN
    INSERT into item_ean_price_history(ean_id, price_type_id, price, created_on, deleted_on)
    values (OLD.ean_id, OLD.price_type_id, OLD.price, NOW(), NOW());
END$$

DELIMITER ;