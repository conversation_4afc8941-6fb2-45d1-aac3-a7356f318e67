import { skuToItemId } from '@/util';
import { Typography } from 'antd';
import { CSSProperties } from 'react';

type SkuCompProps = {
  sku?: string;
  style?: CSSProperties;
  className?: string;
  noCopyable?: boolean;
};
const SkuComp: React.FC<SkuCompProps> = ({ sku, style, className, noCopyable, ...rest }) => {
  return sku ? (
    <Typography.Link
      href={`/item/ean-all-summary?sku=${skuToItemId(sku)}_`}
      target="_blank"
      copyable={!noCopyable}
      style={{ ...style }}
      {...{ className }}
    >
      {sku}
    </Typography.Link>
  ) : null;
};

export default SkuComp;
