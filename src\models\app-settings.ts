import { DictCode } from '@/constants';
import { getAppSettings } from '@/services/foodstore-one/api';
import Util from '@/util';
import { useCallback, useMemo, useState } from 'react';

const defaultAppSetting: API.AppSettings = {
  vats: [],
  priceTypes: [],
  dict: {},
  drSelection: {},
};

export default () => {
  const [appSettings, setAppSettings] = useState<API.AppSettings>(defaultAppSetting);
  const [gSearch, setGSearch] = useState<Record<string, any>>({});

  const loadAppSetting = useCallback(() => {
    getAppSettings()
      .then((res) => setAppSettings(res))
      .catch((e) => Util.error('Failed to fetch app settings. Please try to reload a page!'));
  }, []);

  const getDictOptions = useCallback(
    (type, orderBy?: string, orderDir?: 'asc' | 'desc') => {
      const dict = appSettings.dict || {};
      const keys = Object.keys(dict);
      let result = keys
        .filter((k) => dict[k].type == type)
        .map((k) => ({
          ...dict[k],
          label: dict[k].value,
          value: dict[k].value,
        }));
      if (orderBy) {
        result = result.sort((a: any, b: any) => (a[orderBy] < b[orderBy] ? -1 : a[orderBy] > b[orderBy] ? 1 : 0));
      }

      return result;
    },
    [appSettings.dict],
  );

  const getDictOptionsCV = useCallback(
    (type, orderBy?: string, orderDir?: 'asc' | 'desc', labelField = 'value') => {
      const dict = appSettings.dict || {};
      const keys = Object.keys(dict);
      let result = keys
        .filter((k) => dict[k].type == type)
        .map((k) => ({
          ...dict[k],
          label: dict[k][labelField],
          value: dict[k].code,
        }));
      if (orderBy) {
        result = result.sort((a: any, b: any) => (a[orderBy] < b[orderBy] ? -1 : a[orderBy] > b[orderBy] ? 1 : 0));
      }

      return result;
    },
    [appSettings.dict],
  );

  const getDictByCode = useCallback(
    (code, key = 'value') => {
      const dict = appSettings.dict || {};
      if (!code) return undefined;
      return !key ? dict[`${code}`] : dict[`${code}`]?.[key];
    },
    [appSettings.dict],
  );

  const storeWebsiteKv: Record<number, string> = useMemo(() => {
    return appSettings.storeWebsites?.reduce?.((prev: any, current) => {
      prev[current?.id || 0] = current.name;
      return prev;
    }, {});
  }, [appSettings.storeWebsites]);

  const getParcelUrl = useCallback((parcel_no?: string, serviceType?: 'DPD' | 'DHL' | 'GLS') => {
    if (serviceType == 'DPD') {
      return `${getDictByCode(DictCode.PARCEL_TRACKING_DPD)}${parcel_no}`;
    } else if (serviceType == 'DHL') {
      return `${getDictByCode(DictCode.PARCEL_TRACKING_DHL)}${parcel_no}`;
    } else {
      return `${getDictByCode(DictCode.PARCEL_TRACKING_GLS)}${parcel_no}`;
    }
    // return '#'
  }, [getDictByCode]);

  return {
    appSettings,
    setAppSettings,
    gSearch,
    setGSearch,
    loadAppSetting,
    getDictOptions,
    getDictOptionsCV,
    getDictByCode,
    storeWebsiteKv,
    getParcelUrl,
  };
};
