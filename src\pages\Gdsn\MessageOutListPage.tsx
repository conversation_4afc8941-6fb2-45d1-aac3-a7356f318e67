import { <PERSON><PERSON>, <PERSON>, Tag, Typography } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, sEllipsed, sn } from '@/util';

import { DEFAULT_PER_PAGE_PAGINATION, DT_FORMAT_DMY } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormCheckbox, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { getGdsnMessageProviderList, getGdsnSourceProviderList } from '@/services/foodstore-one/Item/ean';
// import XmlViewer from './components/XmlViewer';
import useGdsnPageContainerTitle from './components/useGdsnPageContainerTitle';
import { PlusOutlined } from '@ant-design/icons';
import CreateGdsnRequestFormModal from './components/CreateGdsnRequestFormModal';
import UpdateGdsnProviderFormModal from './components/UpdateGdsnProviderFormModal';

const MessageOutListPage: React.FC<{ route?: any }> = (props) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<Partial<API.GdsnMessageProvider>>>();

  const [currentGdsnProvider, setCurrentGdsnProvider] = useState<API.GdsnProvider>();
  const [openGdsnCreateRequestFormModal, setOpenGdsnCreateRequestFormModal] = useState<boolean>(false);
  const [openGdsnProviderUpdateModal, setOpenGdsnProviderUpdateModal] = useState<boolean>(false);

  const columns: ProColumns<API.GdsnMessageProvider>[] = [
    {
      title: 'Message ID',
      dataIndex: 'msg_id',
      sorter: false,
      render: (dom, record) => {
        return record.file_path ? (
          <a
            href={`${API_URL_EAN}/api/download?type=xml&key=${record.file_path}`}
            target="_blank"
            rel="noreferrer"
            title={record.msg_id}
          >
            <Typography.Text copyable={{ text: record.msg_id }}>{sEllipsed(record.msg_id, 7, 7)}</Typography.Text>
          </a>
        ) : (
          dom
        );
      },
      width: 140,
    },
    {
      title: 'Requester',
      dataIndex: 'content_owner',
      sorter: true,
      copyable: true,
      className: 'text-sm',
      width: 120,
    },
    {
      title: 'Provider Name',
      dataIndex: 'provider_name',
      sorter: true,
      copyable: true,
      width: 200,
      className: 'text-sm',
    },
    {
      title: 'Provider Name (Sys)',
      dataIndex: ['gdsn_provider', 'name'],
      width: 200,
      className: 'text-sm cursor-pointer',
      tooltip: 'Click to edit.',
      onCell: (record) => {
        return {
          onClick: (e) => {
            setCurrentGdsnProvider({
              ...record.gdsn_provider,
              provider_gln: record.data_source,
              trademark_ids: record.gdsn_provider?.trademarks?.map((x) => sn(x.id)),
              notes: record.gdsn_provider?.notes,
            });
            setOpenGdsnProviderUpdateModal(true);
          },
        };
      },
    },
    {
      title: 'Provider GLN',
      dataIndex: 'data_source',
      sorter: true,
      copyable: true,
      tooltip: 'Data Srouce',
      className: 'text-sm',
      width: 130,
    },
    {
      title: 'Recipient Data Pool',
      dataIndex: 'recipient_data_pool',
      sorter: true,
      copyable: true,
      className: 'text-sm',
      width: 130,
    },
    {
      title: 'Trademarks',
      dataIndex: 'trademark_names',
      width: 250,
      render(__, record) {
        return record.trademark_names ? (
          <div style={{ maxHeight: 120, overflowY: 'auto', overflowX: 'hidden' }}>
            {record.trademark_names?.split('^').map((x) => (
              <Tag key={x} style={{ marginTop: 1, marginBottom: 1 }}>
                {x}
              </Tag>
            ))}
          </div>
        ) : null;
      },
    },
    {
      title: 'Trademarks (sys)',
      dataIndex: 'trademark_names_sys',
      width: 250,
      className: 'text-sm cursor-pointer',
      tooltip: 'Click to edit.',
      render(__, record) {
        return record.gdsn_provider?.trademarks ? (
          <div style={{ maxHeight: 120, overflowY: 'auto', overflowX: 'hidden' }}>
            {record.gdsn_provider?.trademarks?.map((x) => (
              <Tag key={x.id} style={{ marginTop: 1, marginBottom: 1 }}>
                {x.name}
              </Tag>
            ))}
          </div>
        ) : null;
      },
      onCell: (record) => {
        return {
          onClick: (e) => {
            setCurrentGdsnProvider({
              ...record.gdsn_provider,
              provider_gln: record.data_source,
              trademark_ids: record.gdsn_provider?.trademarks?.map((x) => sn(x.id)),
              notes: record.gdsn_provider?.notes,
            });
            setOpenGdsnProviderUpdateModal(true);
          },
        };
      },
    },
    {
      title: 'Requested Count',
      dataIndex: 'requested_count',
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.requested_count);
      },
    },
    {
      title: 'GDSN EAN Count',
      dataIndex: 'gtin_count',
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.gtin_count);
      },
    },
    {
      title: 'EAN Count (sys)',
      dataIndex: ['gdsn_provider', 'ean_count'],
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.gdsn_provider?.ean_count);
      },
    },
    {
      title: 'File Size',
      dataIndex: 'file_size',
      width: 50,
      className: 'text-sm c-grey',
      render: (_, record) => (
        <span title={`${ni(record.file_size || 0)} bytes`}>{Util.humanFileSize(record.file_size || 0)}</span>
      ),
    },
    {
      title: 'Submitted At',
      sorter: true,
      dataIndex: 'submittedAt',
      valueType: 'dateTime',
      search: false,
      width: 90,
      className: 'text-sm',
      defaultSortOrder: 'descend',
      fieldProps: {
        format: DT_FORMAT_DMY,
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
      className: 'text-sm c-grey',
    },
    /* {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 50,
      render: (_, record) => (
        <Row gutter={8}>
          <Col flex={'30px'}>
            <Button
              type="link"
              size="small"
              title="Show item information in xml..."
              onClick={() => {
                setCurrentRow(record);
                setShowXmlDetail(true);
              }}
            >
              XML
            </Button>
          </Col>
        </Row>
      ),
    }, */
  ];

  const { pageTitle } = useGdsnPageContainerTitle(props.route);

  return (
    <PageContainer title={pageTitle}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.GdsnMessageProvider>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_gdsn_message_provider', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          {/* <ProFormSelect
            key="Kind"
            name="kind"
            label="Kind"
            width={300}
            placeholder="Kind"
            fieldProps={{ dropdownMatchSelectWidth: false }}
            formItemProps={{ style: { marginBottom: 0 } }}
            options={GdsnMessageTypeOptions}
          /> */}
          {/* <ProFormText name={'remoteParty'} label="Remote party" width={'sm'} placeholder={'Remote party'} /> */}
          <ProFormText name={'gtin'} label="GTIN" width={'sm'} placeholder={'GTIN'} tooltip="single EAN" />
          {/* <ProFormText
            name={'parent_gtin'}
            label="Parent GTIN"
            width={'sm'}
            placeholder={'Parent GTIN'}
            tooltip="Pkg. EAN"
          /> */}
          <ProFormSelect
            showSearch
            placeholder="Select an Item"
            request={async (params) => {
              const res = await getGdsnSourceProviderList(
                { ...params, pageSize: 100, ...params },
                { provider_gln: 'ascend' },
                {},
              );
              if (res && res.data) {
                const tmp = res.data.map((x: API.GdsnMessageProvider) => ({
                  label: `${x.provider_name || 'N/A'} | ${x.provider_gln}`,
                  value: x.provider_gln,
                }));
                return tmp;
              }
              return [];
            }}
            fieldProps={{ dropdownMatchSelectWidth: false }}
            width="sm"
            name="provider_gln"
            label="Provider"
          />
          {/* <ProFormText name={'provider_gln'} label="Provider GLN" width={'sm'} placeholder={'GLN'} /> */}
          <ProFormText name={'provider_name'} label="Provider name" width={'sm'} placeholder={'Provider name'} />
          <ProFormText name={'trademark_name'} label="Trademark" width={'sm'} placeholder={'Trademark'} />
          <ProFormCheckbox name={'is_no_data'} label="No XML?" placeholder={'No XML?'} />
          <ProFormCheckbox
            name={'is_no_gtin'}
            label="No Received EANs"
            placeholder={'No Received EANs?'}
            initialValue={true}
          />
        </ProForm>
      </Card>
      <ProTable<API.GdsnMessageProvider, API.PageParams>
        headerTitle={'GDSN Providers List'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_gdsn_message_provider_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_gdsn_message_provider', sfValues);
          Util.setSfValues('sf_gdsn_message_provider_p', params);

          const newSort = { ...sort };
          return getGdsnMessageProviderList(
            {
              ...sfValues,
              ...params,
              providerMode: 1,
              with: 'gdsnMessage.file_url,gdsnProvider',
            },
            newSort,
            filter,
          );
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            size="small"
            onClick={() => {
              setOpenGdsnCreateRequestFormModal(true);
            }}
          >
            <PlusOutlined /> New Request
          </Button>,
        ]}
      />

      {/* <XmlViewer xml={currentRow?.info_xml ?? ''} modalVisible={showXmlDetail} handleModalVisible={setShowXmlDetail} /> */}

      <CreateGdsnRequestFormModal
        modalVisible={openGdsnCreateRequestFormModal}
        handleModalVisible={setOpenGdsnCreateRequestFormModal}
        onSubmit={async (value) => {
          setOpenGdsnCreateRequestFormModal(false);
          actionRef.current?.reload();
        }}
      />

      <UpdateGdsnProviderFormModal
        initialValues={currentGdsnProvider}
        modalVisible={openGdsnProviderUpdateModal}
        handleModalVisible={setOpenGdsnProviderUpdateModal}
        onSubmit={async (value) => {
          setOpenGdsnProviderUpdateModal(false);
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default MessageOutListPage;
