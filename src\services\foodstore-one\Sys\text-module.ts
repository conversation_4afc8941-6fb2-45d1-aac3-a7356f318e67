/* eslint-disable */
import { AC_PER_PAGE_PAGINATION } from '@/constants';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/text-module';

/** rule GET /api/sys/text-module */
export async function getSysTextModuleList(
  params: API.PageParams &
    Partial<API.SysTextModule> & {
      is_date_valid?: number;
      numberExact?: number;
      numbers?: any[] | string;
    },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysTextModule>> {
  return request<API.Result<API.SysTextModule>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/sys/text-module */
export async function updateSysTextModule(
  id?: number,
  data?: Partial<API.SysTextModule>,
  options?: { [key: string]: any },
) {
  return request<API.SysTextModule>(`${urlPrefix}/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/sys/text-module */
export async function addSysTextModule(data: API.SysTextModule, options?: { [key: string]: any }) {
  return request<API.SysTextModule>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/sys/text-module/{id} */
export async function deleteSysTextModule(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export async function getSysTextModuleACList(
  params: API.PageParams & { number?: number; numberExact?: number; numbers?: any[] | string; keyWord?: string },
  sort?: any,
  filter?: any,
): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize ?? AC_PER_PAGE_PAGINATION,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.SysTextModule) => ({
      ...x,
      value: `${x.number}`,
      label: x.number + ' - ' + (x.text || '')?.substring(0, 50),
    })),
  );
}
