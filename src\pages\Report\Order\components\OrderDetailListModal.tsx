import { Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2, ni, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import moment from 'moment';
import type { ResizeCallbackData } from 'react-resizable';
import { DT_FORMAT_TIME_MAX_S, DT_FORMAT_YMD } from '@/constants';
import { getOrderTrademarkProducerList } from '@/services/foodstore-one/Report/order-report';
import type { SearchFormValueType } from '../OrderTrademarkProducerList';
import { ResizableTitle } from '../OrderTrademarkProducerList';

type RecordType = API.OrderItem & Record<string, any>;

export type OrderDetailListModalProps = {
  sku: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  dateRanges?: { from?: string; to?: string }[];
  searchFormRef?: React.MutableRefObject<ProFormInstance<SearchFormValueType> | undefined>;
};

const OrderDetailListModal: React.FC<OrderDetailListModalProps> = (props) => {
  const { sku, modalVisible, handleModalVisible, dateRanges, searchFormRef } = props;

  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  const handleResize: any = useCallback(
    (index: number) =>
      (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
        setColumns((prev) => {
          const newColumns = [...prev];
          newColumns[index] = {
            ...newColumns[index],
            width: size.width,
          };
          return newColumns;
        });
      },
    [],
  );

  const defaultColumns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      /* {
        title: 'AVG BP',
        dataIndex: 'ibo_avg_bp',
        sorter: true,
        align: 'right',
        width: 80,
        className: 'br2 b-grey',
        render: (dom, record) => {
          return nf2(record.ibo_avg_bp * record.attr_case_qty);
        },
        onCell: (record) => {
          if (record.item_ean) {
            return {
              className: 'cursor-pointer',
              onClick: (e) => {
                // setCurrentRow(record);
                // setOpenIBOModal(true);
              }
            }
          }
          return {};
        }
      }, */
      {
        title: 'Order Info',
        dataIndex: 'order_info',
        sorter: true,
        children: [
          {
            title: 'Order ID',
            dataIndex: 'order_id',
            sorter: true,
            align: 'center',
            width: 80,
          },
          {
            title: 'Increment ID',
            dataIndex: 'increment_id',
            sorter: true,
            align: 'center',
            className: 'text-sm',
            width: 110,
          },
          {
            title: 'Total Qty',
            dataIndex: 'total_qty_ordered',
            sorter: true,
            align: 'right',
            width: 80,
            render: (dom, record) => {
              return ni(record.total_qty_ordered);
            },
          },
          {
            title: 'Gross Total',
            dataIndex: 'grand_total',
            sorter: true,
            align: 'right',
            width: 90,
            render: (dom, record) => {
              return nf2(record.grand_total);
            },
          },
          {
            title: 'Net Total',
            dataIndex: 'subtotal',
            sorter: true,
            align: 'right',
            width: 90,
            render: (dom, record) => {
              return nf2(record.subtotal);
            },
          },
          {
            title: 'Shipping Cost',
            dataIndex: 'shipping_incl_tax',
            sorter: false,
            align: 'right',
            width: 80,
            render: (dom, record) => {
              return nf2(record.shipping_incl_tax);
            },
          },
        ],
      },
      {
        title: 'AVG Order Turnover',
        dataIndex: 'avg_turnover',
        sorter: true,
        align: 'right',
        width: 90,
        className: 'bl2 b-gray',
        render: (dom, record) => {
          return nf2(record.turnover / record.qty);
        },
      },
      {
        title: 'AVG Turnover',
        dataIndex: 'avg_cturnover',
        sorter: true,
        align: 'right',
        width: 90,
        className: 'br2 b-grey',
        render: (dom, record) => {
          return nf2(record.cturnover / record.qty);
        },
      },
      {
        title: 'Grand Total Qty',
        dataIndex: 'qty_ordered',
        sorter: true,
        align: 'right',
        width: 80,
        className: 'br2 b-grey',
      },
    ],
    [],
  );

  const buildColumns = useCallback(() => {
    if (!modalVisible) return;

    const searchValues = searchFormRef?.current?.getFieldsValue();
    const lastInterval = searchValues?.lastInterval ?? 7;
    const intervalType = searchValues?.intervalType ?? 'd';

    const cols = [...defaultColumns];
    const ranges: { from?: string; to?: string }[] = [];

    const newCols = [];
    for (let ind = 0; ind < lastInterval; ind++) {
      let groupColumnTitle = '';
      if (intervalType == 'd') {
        ranges.push({
          from: moment().subtract(ind, 'days').format(DT_FORMAT_YMD),
          to: moment().subtract(ind, 'days').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
        });
        groupColumnTitle = moment().subtract(ind, 'days').format('DD.MM.');
      } else if (intervalType == 'w') {
        ranges.push({
          from: moment().startOf('isoWeek').subtract(ind, 'w').format(DT_FORMAT_YMD),
          to: moment().endOf('isoWeek').subtract(ind, 'w').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
        });
        groupColumnTitle =
          moment().startOf('isoWeek').subtract(ind, 'w').format('DD.MM.') +
          '~' +
          moment().endOf('isoWeek').subtract(ind, 'w').format('DD.MM.');
      } else {
        ranges.push({
          from: moment().startOf('month').subtract(ind, 'months').format(DT_FORMAT_YMD),
          to: moment().endOf('month').subtract(ind, 'months').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
        });
        groupColumnTitle = moment().startOf('month').subtract(ind, 'months').format('MMM `YY');
      }

      const newCol: ProColumns<RecordType> = {
        title: groupColumnTitle,
        dataIndex: [`colGroup${ind}`],
        align: 'center',
        ellipsis: true,
        className: 'bl2 b-gray',
        children: [
          {
            title: 'Qty',
            dataIndex: `qty${ind}`,
            align: 'right',
            className: 'bl2 b-gray',
            width: intervalType == 'w' ? 100 : 80,
            sorter: true,
            render: (dom, record) => Util.numberFormat(record[`qty${ind}`], false),
          },
          {
            title: 'Gross Turnover',
            dataIndex: `turnover${ind}`,
            align: 'right',
            width: intervalType == 'w' ? 100 : 80,
            sorter: true,
            render: (dom, record) => Util.numberFormat(record[`turnover${ind}`], false, 2),
          },
          {
            title: 'Net Turnover',
            dataIndex: `net_turnover${ind}`,
            align: 'right',
            width: intervalType == 'w' ? 100 : 80,
            sorter: true,
            render: (dom, record) => Util.numberFormat(record[`net_turnover${ind}`], false, 2),
          },
          {
            title: 'Turnover',
            dataIndex: `cturnover${ind}`,
            align: 'right',
            width: intervalType == 'w' ? 100 : 80,
            sorter: true,
            render: (dom, record) => Util.numberFormat(record[`cturnover${ind}`], false, 2),
          },
          {
            title: 'Provision',
            dataIndex: `ebay_fee${ind}`,
            align: 'right',
            width: intervalType == 'w' ? 100 : 80,
            sorter: true,
            render: (dom, record) => Util.numberFormat(record[`ebay_fee${ind}`], false, 2),
          },
          {
            title: 'GP',
            dataIndex: `gp${ind}`,
            align: 'right',
            width: intervalType == 'w' ? 100 : 80,
            sorter: true,
            render: (dom, record) => Util.numberFormat(record[`gp${ind}`], false, 2),
          },
        ],
      };
      newCols.push(newCol);
    }

    const totalCol: ProColumns<RecordType> = {
      title: 'Total',
      dataIndex: [`colGroup`],
      align: 'center',
      ellipsis: true,
      // className: 'bg-green3',
      className: 'bl2 b-gray',
      children: [
        {
          title: 'Qty',
          dataIndex: `qty`,
          align: 'right',
          width: 80,
          sorter: true,
          render: (dom, record) => Util.numberFormat(record.qty, false),
        },
        {
          title: 'Gross Turnover',
          dataIndex: `turnover`,
          align: 'right',
          width: 80,
          sorter: true,
          render: (dom, record) => Util.numberFormat(record.turnover, false, 2),
        },
        {
          title: 'Net Turnover',
          dataIndex: `net_turnover`,
          align: 'right',
          width: 80,
          sorter: true,
          render: (dom, record) => Util.numberFormat(record.net_turnover, false, 2),
        },
        {
          title: 'Turnover',
          dataIndex: `cturnover`,
          align: 'right',
          width: 80,
          sorter: true,
          render: (dom, record) => Util.numberFormat(record.cturnover, false, 2),
        },
        {
          title: 'ebay Fee',
          dataIndex: `ebay_fee`,
          sorter: true,
          align: 'right',
          width: 80,
          render: (dom, record) => Util.numberFormat(record.ebay_fee, false, 2),
        },
        {
          title: 'GP',
          dataIndex: `gp`,
          sorter: true,
          align: 'right',
          width: 80,
          render: (dom, record) => Util.numberFormat(record.gp, false, 2),
        },
      ],
    };

    setColumns([...cols, totalCol, ...newCols]);
  }, [defaultColumns, searchFormRef, modalVisible]);

  useEffect(() => {
    buildColumns();
  }, [buildColumns]);

  useEffect(() => {
    if (modalVisible) {
      actionRef.current?.reload();
    }
  }, [columns, modalVisible]);

  return (
    <Modal
      title={`Turnover details - ${sku}`}
      width={1400}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      footer={false}
      bodyStyle={{ padding: 0 }}
    >
      <ProTable<RecordType, API.PageParams>
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef?.current?.getFieldsValue();
          setLoading(true);
          return getOrderTrademarkProducerList(
            {
              ...params,
              ...searchFormValues,
              sku,
              listMode: 'detail',
              dateRanges,
            },
            sort,
            filter,
          )
            .then((res) => {
              const totalRow: RecordType = {
                uid: 'total',
                trademark_name: 'Total',
                qty_ordered: 0,
                mix_qty: 0,
                mag_qty: 0,
                avg_turnover: 0,
                avg_net_turnover: 0,
                avg_cturnover: 0,
              };
              const qtyColCount = dateRanges?.length || 0;
              res.data.forEach((row: RecordType) => {
                totalRow.qty_ordered = sn(totalRow.qty_ordered) + sn(row.qty_ordered);
                totalRow.mix_qty = sn(totalRow.mix_qty) + sn(row?.mix_qty);
                totalRow.mag_qty = sn(totalRow.mag_qty) + sn(row?.mag_qty);

                for (let i = -1; i < qtyColCount; i++) {
                  const suffix = i >= 0 ? i : '';
                  totalRow[`qty${suffix}`] = sn(totalRow[`qty${suffix}`]) + sn(row[`qty${suffix}`]);
                  totalRow[`gp${suffix}`] = sn(totalRow[`gp${suffix}`]) + sn(row[`gp${suffix}`]);
                  totalRow[`turnover${suffix}`] = sn(totalRow[`turnover${suffix}`]) + sn(row[`turnover${suffix}`]);
                  totalRow[`cturnover${suffix}`] = sn(totalRow[`cturnover${suffix}`]) + sn(row[`cturnover${suffix}`]);
                  totalRow[`net_turnover${suffix}`] =
                    sn(totalRow[`net_turnover${suffix}`]) + sn(row[`net_turnover${suffix}`]);

                  totalRow[`ebay_fee${suffix}`] = sn(totalRow[`ebay_fee${suffix}`]) + sn(row[`ebay_fee${suffix}`]);
                }
              });

              res.data.splice(0, 0, totalRow);

              return res;
            })
            .finally(() => setLoading(false));
        }}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: 10000,
        }}
        columns={columns}
        columnEmptyText=""
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        rowSelection={false}
        rowClassName={(record) => {
          if (record.uid == 'total') {
            return 'total-row';
          } else return record.ean_id ? (record?.is_single ? 'row-single' : 'row-multi') : '';
        }}
      />
    </Modal>
  );
};

export default OrderDetailListModal;
