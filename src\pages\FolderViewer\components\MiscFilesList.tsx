import { useEffect, useRef, useState } from 'react';
import type { FormValueType } from '../MiscFileBrowser';
import { deleteFile, getFileList } from '@/services/foodstore-one/File/file';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import Util, { sEllipsed } from '@/util';
import { Popconfirm, message } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import SFileIcon from '@/components/SFileIcon';

type RecordType = API.File;

type MiscFilesListType = FormValueType & {
  category?: string;
  tableTitle?: string;
  refreshTick?: number;
};

const MiscFilesList: React.FC<MiscFilesListType> = (props) => {
  const { cat1, order_in_order_no, order_out_order_no, cat2, category, tableTitle, refreshTick } = props;

  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    actionRef?.current?.reload();
  }, [cat1, cat2, order_in_order_no, order_out_order_no]);

  useEffect(() => {
    if (refreshTick) {
      actionRef?.current?.reload();
    }
  }, [refreshTick]);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'File name',
      dataIndex: 'file_name',
      sorter: true,
      ellipsis: true,
      render: (dom, entity) => {
        return (
          <>
            <SFileIcon fileName={entity.file_name} /> &nbsp;
            <a href={entity.url} target="_blank" rel="noreferrer" title={entity.file_name}>
              {sEllipsed(entity.file_name, 30, 10)}
            </a>
          </>
        );
      },
    },
    {
      title: '',
      valueType: 'option',
      width: 20,
      render(dom, record) {
        return (
          <Popconfirm
            className="c-red"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              if (record.id) {
                const hide = message.loading('Deleting...', 0);
                deleteFile(record.id)
                  .then((res: any) => {
                    message.success('Deleted successfully.', 2);
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(hide);
              }
            }}
          >
            <DeleteOutlined title="Delete file" />
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <ProTable<RecordType, API.PageParams>
      headerTitle={<span className="text-sm">{tableTitle ?? 'Files list'}</span>}
      actionRef={actionRef}
      rowKey="id"
      revalidateOnFocus={false}
      options={{ fullScreen: false, setting: false, density: false, search: false }}
      size="small"
      search={false}
      pagination={{
        showSizeChanger: true,
        hideOnSinglePage: true,
        defaultPageSize: 10,
      }}
      request={(params, sort, filter) => {
        if (!cat1) return Promise.resolve([]);
        if ((cat1 == 'OrderIn' && !order_in_order_no) || (cat1 == 'OrderOut' && !order_out_order_no)) {
          return Promise.resolve([]);
        }

        const newParams = {
          ...params,
          cat1,
          order_in_order_no,
          order_out_order_no,
          category,
        };
        return getFileList(newParams, sort, filter);
      }}
      showHeader={false}
      cardProps={{ bodyStyle: { padding: 0 } }}
      columns={columns}
      tableAlertRender={false}
      tableAlertOptionRender={false}
      columnEmptyText=""
      defaultSize="small"
      locale={{ emptyText: <></> }}
    />
  );
};

export default MiscFilesList;
