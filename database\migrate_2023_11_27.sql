CREATE TABLE `trademark_supplier_price_setting`
(
    `trademark_id`     int(11)             NOT NULL,
    `supplier_id`      bigint(20) unsigned NOT NULL,
    `price_percentage` double DEFAULT NULL,
    KEY `FK_trademark_supplier_price_setting_trademark_id` (`trademark_id`),
    <PERSON><PERSON>Y `FK_trademark_supplier_price_setting_supplier_id` (`supplier_id`),
    CONSTRAINT `FK_trademark_supplier_price_setting_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_trademark_supplier_price_setting_trademark_id` FOREIGN KEY (`trademark_id`) REFERENCES `trademark` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;