import { Space } from 'antd';
import React, { useEffect } from 'react';
// @ts-ignore
import { useModel } from 'umi';
import Avatar from './AvatarDropdown';
// import HeaderSearch from '../HeaderSearch';
import styles from './index.less';
import DownSyncDropdown from './DownSyncDropdown';
import { getAppSettings } from '@/services/foodstore-one/api';
import Util from '@/util';
import SessionTimeout from '../SessionTimout';
import { UserRole } from '@/constants';
export type SiderTheme = 'light' | 'dark';

const GlobalHeaderRight: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { setAppSettings, setGSearch } = useModel('app-settings');

  // const [openGSearch, setOpenGSearch] = useState(false);

  useEffect(() => {
    if (initialState?.currentUser) {
      getAppSettings()
        .then((res) => setAppSettings((prev) => ({ ...prev, ...res })))
        .catch((e) => Util.error('Failed to fetch app settings. Please try to reload a page!'));
    }
  }, [initialState?.currentUser, setAppSettings]);

  useEffect(() => {
    // We disable G search
    // setGSearch(Util.getSfValues('sf_gSearch', {}));

    // So we erase all G Search filters
    Util.setSfValues('sf_gSearch', {});
    setGSearch({});
  }, []);

  // const noGlobalFilter = Util.isEmptyValueObject(gSearch);
  /* const filterTitle = useMemo(() => {
    const list = [];
    if (gSearch.sku) {
      list.push(`SKU: ${gSearch.sku}`);
    }
    if (gSearch.ean) {
      list.push(`EAN: ${gSearch.ean}`);
    }
    if (gSearch.name) {
      list.push(`Name: ${gSearch.name}`);
    }
    if (gSearch.ean_type_search) {
      list.push(`Type: ${gSearch.ean_type_search == 'base' ? 'Single' : 'Multi'}`);
    }

    if (gSearch['trademarks[]']?.length) {
      list.push(`${gSearch['trademarks[]'].length} trademarks`);
    }

    return list.join(', ');
  }, [gSearch]); */

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout } = initialState.settings;
  let className = styles.right;

  if ((navTheme === 'dark' && layout === 'top') || layout === 'mix') {
    className = `${styles.right}  ${styles.dark}`;
  }

  const isAuthenticated = !!initialState?.currentUser?.user_id;
  const isEditor = initialState?.currentUser?.role == UserRole.EDITOR;
  const isWarehouseUser = initialState?.currentUser?.role == UserRole.WAREHOUSE;

  return !initialState || !initialState.settings || !isAuthenticated ? null : (
    <>
      <Space className={className}>
        {/* {!isEditor && (
          <Popover
            content={<HeaderGlobalFilter hide={() => setOpenGSearch(false)} />}
            trigger="click"
            open={openGSearch}
            onOpenChange={setOpenGSearch}
          >
            <Button type={'text'} icon={<SearchOutlined title={filterTitle} />} danger={!noGlobalFilter}>
              {noGlobalFilter ? undefined : <span className="c-red">{filterTitle}</span>}
            </Button>
          </Popover>
        )} */}

        {/* <HeaderSearch
        className={`${styles.action} ${styles.search}`}
        placeholder="Search"
        defaultValue=""
        options={[
          {
            label: <a href="https://prolayout.ant.design/">Pro Layout</a>,
            value: 'Pro Layout',
          },
        ]} // onSearch={value => {
        //   console.log('input', value);
        // }}
      /> */}
        {/* <span
        className={styles.action}
        onClick={() => {
          window.open('https://pro.ant.design/docs/getting-started');
        }}
      >
        <QuestionCircleOutlined />
      </span> */}
        {!isEditor && !isWarehouseUser && <DownSyncDropdown />}
        <Avatar />
      </Space>
      <SessionTimeout />
    </>
  );
};

export default GlobalHeaderRight;
