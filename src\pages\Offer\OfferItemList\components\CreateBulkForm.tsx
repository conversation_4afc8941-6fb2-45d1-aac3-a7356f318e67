import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, message, notification } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import { addOfferItemBulk } from '@/services/foodstore-one/Offer/offer-item';
import Util from '@/util';

export type FormValueType = Partial<API.OfferItem> & { list_str?: string };

const handleCreateBulk = async (fields: FormValueType) => {
  const hide = message.loading('Bulk Creating...', 0);

  try {
    const res = await addOfferItemBulk(fields);
    hide();
    return res.message;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type CreateBulkFormProps = {
  modalVisible: boolean;
  offer_id: number;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItem) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const CreateBulkForm: React.FC<CreateBulkFormProps> = (props) => {
  const { offer_id, modalVisible, handleModalVisible, onSubmit } = props;
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
    }
  }, [modalVisible]);

  return (
    <ModalForm
      title={<div>Bulk Create Offer Items</div>}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      grid
      onFinish={async (value) => {
        if (!offer_id) {
          message.error('Please select an Offer first!');
          return;
        }

        const data = { ...value, offer_id };

        const list = await handleCreateBulk(data).catch(Util.error);

        if (list) {
          if (onSubmit) onSubmit(value);

          notification.success({
            duration: 0,
            // placement: 'top',
            description: list.map((x) => (
              <div key={x.id}>
                {x.sku} / {x.ean} / {x.qty}
              </div>
            )),
            message: 'Bulk created successfully!',
          });
        }
      }}
    >
      <Col span={12}>
        <ProFormTextArea
          width="lg"
          name="ean_list_str"
          label="EAN List"
          required
          rules={[
            {
              required: true,
              message: 'EAN-Qty List is required',
            },
          ]}
          fieldProps={{ rows: 10 }}
        />
      </Col>
      <Col span={12}>
        <ProFormTextArea
          width="lg"
          name="qty_list_str"
          label="Qty List"
          required
          rules={[
            {
              required: true,
              message: 'EAN-Qty List is required',
            },
          ]}
          fieldProps={{ rows: 10 }}
        />
      </Col>

      <ProFormTextArea width="lg" name="customer_note" label="Customer Note" />
    </ModalForm>
  );
};

export default CreateBulkForm;
