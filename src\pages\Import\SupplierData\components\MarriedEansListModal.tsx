import type { Dispatch, SetStateAction } from 'react';
import { useMemo } from 'react';
import React, { useRef } from 'react';
import { Card, Modal } from 'antd';
import { message, Typography } from 'antd';
import Util from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { CloseOutlined, SplitCellsOutlined } from '@ant-design/icons';
import { deleteMarriedTwoEANs, getMarriedList } from '@/services/foodstore-one/Import/import';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProForm, ProFormText } from '@ant-design/pro-form';

export type SearchFormValueType = Partial<API.Ean>;

export type ModalInitialValueType = {
  // import_id: number; // import table ID
  // imported_ean: string;
  // default filter in IBO table
  // ibom_id: number;
  // imported_table_name?: string;
};

export type MarriedEansListModalProps = {
  initialValue: ModalInitialValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onUnlinkCallback?: () => void;
};

type ColumnDataType = API.ImportSupplierMarry;

const MarriedEansListModal: React.FC<MarriedEansListModalProps> = (props) => {
  const { modalVisible, handleModalVisible, onUnlinkCallback } = props;

  const actionRef = useRef<ActionType>();

  // search form
  const searchFormRef = useRef<ProFormInstance>();

  const columns: ProColumns<ColumnDataType>[] = useMemo(() => {
    const newColumns: ProColumns<ColumnDataType>[] = [
      {
        title: 'EAN (xls)',
        dataIndex: ['imported_ean'],
        sorter: false,
        ellipsis: true,
        copyable: true,
        width: 130,
        render: (dom) => {
          return <>{dom}</>;
        },
      },
      {
        title: 'Name (xls)',
        dataIndex: ['name'],
        sorter: false,
        ellipsis: true,
        width: 250,
        render: (dom) => {
          return <>{dom}</>;
        },
      },
      {
        title: 'Item Name',
        dataIndex: ['item_ean', 'item', 'name'],
        sorter: false,
        ellipsis: true,
        width: 250,
        render: (dom) => {
          return <>{dom}</>;
        },
      },
      {
        title: 'EAN Name',
        dataIndex: ['item_ean', 'ean_text_de'],
        sorter: true,
        width: 250,
        ellipsis: true,
        render: (dom, record) => {
          const eanName = record?.item_ean?.ean_text_de?.name;
          return (
            <Typography.Text type={eanName ? undefined : 'warning'}>
              {eanName ?? record?.item_ean?.item?.name ?? <CloseOutlined style={{ color: '#cc2200' }} />}
            </Typography.Text>
          );
        },
      },
      {
        title: 'EAN',
        dataIndex: ['item_ean', 'ean'],
        key: 'ean',
        sorter: true,
        copyable: true,
        width: 130,
      },
      {
        title: 'SKU',
        dataIndex: ['item_ean', 'sku'],
        sorter: true,
        ellipsis: true,
        copyable: true,
        width: 80,
      },
      {
        title: 'IBOM',
        dataIndex: ['ibo', 'ibom_id'],
        sorter: true,
        ellipsis: true,
        copyable: true,
        width: 120,
        render: (dom, record) => {
          return record.ibo?.ibom && `#${record.ibo.ibom?.order_no} | ${record.ibo.ibom?.supplier?.name}`;
        },
      },
      {
        title: '',
        dataIndex: 'option',
        valueType: 'option',
        width: 50,
        align: 'center',
        fixed: 'right',
        render: (dom, record) => (
          <SplitCellsOutlined
            title="Unlink 2 EANs."
            className="c-lightred"
            onClick={async () => {
              if (!record.ibo_id || !record.xls_id) {
                message.info('Cannot unlink!');
                return Promise.resolve();
              }
              const hide = message.loading('Unlinking EANs between IBO and XLS', 0);
              return deleteMarriedTwoEANs(record.import_id, [
                {
                  ibo_id: record.ibo_id,
                  xls_id: record.xls_id,
                },
              ])
                .then(() => {
                  message.success('Successfully unlinked!');
                  actionRef.current?.reload();
                  onUnlinkCallback?.();
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          />
        ),
      },
    ];
    return newColumns;
  }, [onUnlinkCallback]);

  return (
    <>
      <Modal
        title={'Linked EANs'}
        width="1300px"
        open={modalVisible}
        onCancel={() => handleModalVisible(false)}
        footer={false}
      >
        <Card bordered={false} bodyStyle={{ padding: 0 }}>
          <ProForm<SearchFormValueType>
            layout="inline"
            formRef={searchFormRef}
            isKeyPressSubmit
            className="search-form"
            submitter={{
              submitButtonProps: { htmlType: 'submit' },
              onSubmit: () => actionRef.current?.reload(),
              onReset: () => actionRef.current?.reload(),
              render: (form, dom) => {
                return [...dom];
              },
            }}
          >
            <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
            <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          </ProForm>
        </Card>
        <ProTable<ColumnDataType, API.PageParams>
          headerTitle={'Married EANs'}
          actionRef={actionRef}
          rowKey="sid"
          revalidateOnFocus={false}
          options={{ fullScreen: true }}
          sticky
          scroll={{ x: 800 }}
          size="small"
          bordered
          cardProps={{ bodyStyle: { padding: 0 } }}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: 20,
          }}
          search={false}
          request={async (params, sort, filter) => {
            const searchFormValues = searchFormRef.current?.getFieldsValue();
            const formValues = {
              ...params,
              ...searchFormValues,
              // imported_ean: initialValue.imported_ean,
            };

            return getMarriedList(formValues, sort, filter).finally(() => {});
          }}
          onRequestError={Util.error}
          columns={columns}
        />
      </Modal>
    </>
  );
};

export default MarriedEansListModal;
