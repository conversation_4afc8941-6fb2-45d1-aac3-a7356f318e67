import { Typography } from 'antd';

type EanTitleCompProps = {
  itemEan?: Partial<API.Ean>;
};
const EanTitleComp: React.FC<EanTitleCompProps> = ({ itemEan, ...rest }) => {
  const { sku, ean } = itemEan || {};
  return (
    <>
      <Typography.Paragraph
        copyable={{
          text: ean || '',
          tooltips: 'Copy EAN ' + (ean || ''),
        }}
        style={{ display: 'inline-block', marginBottom: 0 }}
      >
        &nbsp;{'-'}&nbsp;
        {ean || ''}
      </Typography.Paragraph>
      <Typography.Paragraph
        copyable={{
          text: sku || '',
          tooltips: 'Copy SKU ' + (sku || ''),
        }}
        style={{ display: 'inline-block', marginBottom: 0 }}
      >
        &nbsp;&nbsp;&nbsp;&nbsp;
        {sku || ''}
      </Typography.Paragraph>
    </>
  );
};

export default EanTitleComp;
