<?php
/**
 *
 * Down syncing GDSN message items aggregated from WHC GDSN server.
 *
 * - WHC GDSN API endpoint: /rest/gdsn/messageItem/aggregated
 * - has a feature of singleton of cron. Lock file name which contains PID is "ds-whc-gdsn-sync.lock"
 *
 * - fetch 500 items and parse & save EAN data from xml_content field.
 *
 * @package     Cron job script.
 * @since       2023-10-11
 */

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Magento\MagSyncLog;
use App\Service\EanApi\EanApiBaseService;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

$lockFilePath = PRIVATE_DATA_PATH . DS.  "ds-whc-gdsn-sync.lock";
$pageSize = 200;
$maxLoop = 2 * 13; // 2 loops per min. For 15 min scripts.

//Func::getLogger()->error("106 ===!!===");
// Func::getLogger()->error("Sync GDSN PID: " . getmypid());

/** @var \Slim\Container $container */
/** @var EanApiBaseService $eanApiBaseService */
$eanApiBaseService = $container->get(EanApiBaseService::class);

// make logs to error with long processing status
MagSyncLog::query()
    ->where('name', MagSyncLog::NAME_WHC_GDSN_SYNC)
    ->whereIn('status', [MagSyncLog::STATUS_STARTED, MagSyncLog::STATUS_PROCESSING])
    ->where("created_on", '<', Func::dtDbDatetimeStr(Func::dtDateByOffset(time(), -1, true)))
    ->update(['status' => MagSyncLog::STATUS_ERROR]);

// singleton check from lock file.
if (DIRECTORY_SEPARATOR !== '\\') {
    try {
        if( $pidsOrFalse = Func::isProcessLocked($lockFilePath) ) {
            // die("Already running.\n");
            FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_DOWN, MagSyncLog::NAME_GDSN_SYNC_PROCESS_RUNNING, json_encode($pidsOrFalse));
            die();
        }
    } catch (Exception $exception) {
        Func::getLogger()->error($exception->getMessage());
        throw $exception;
    }
}

// Get the aggregated messages items
try {
    $eanApiBaseService->dsMessageItemsAggregated(['pageSize' => $pageSize, 'maxLoop' => $maxLoop]);
} catch (Exception $exception) {
    Func::getLogger()->error($exception->getMessage() . $exception->getTraceAsString());
}








