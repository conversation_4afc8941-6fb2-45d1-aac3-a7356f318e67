/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/report/order/sales';

/**
 *
 * GET /api/report/order/sales */
export async function getOrderTrademarkProducerList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderItem>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.data.length,
    // totalRows: res.message.totalRows,
  }));
}

export type OrderShippingByProviderReportRowType = {
  shipping_provider_name?: string;
  order_date?: string;
  new?: any;
} & {
  children?: OrderShippingByProviderReportRowType[];
  level?: number;
  uid?: string;
};
/**
 *
 * GET /api/report/order/shipping-by-provider */
export async function getOrderShippingByProviderReport(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<OrderShippingByProviderReportRowType> & { orderCountWithErrors?: number; }> {
  return request<API.BaseResult>(`/api/report/order/shipping-by-provider`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.data.length,
    orderCountWithErrors: res.message.orderCountWithErrors,
  }));
}

/**
 *
 * GET /api/report/order/orders-for-shipping-by-provider */
export async function getOrdersListForShippingByProviderReport(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
) {
  return request<API.ResultList<API.Order>>(`/api/report/order/orders-for-shipping-by-provider`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/**
 *
 * GET /api/report/order/pre-picklist-summary */
export async function getPrePicklistSummary(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; },
  sort: any,
  filter: any,
) {
  return request<API.ResultList<API.Order>>(`/api/report/order/pre-picklist-summary`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** export as PDF GET /api/report/order/pre-picklist-summary-pdf */
export async function getPrePicklistSummaryPdf(params?: { mode?: 'groupByOrder'; }) {
  return request<API.ResultDownloadable>(`/api/report/order/pre-picklist-summary-pdf`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 *
 * GET /api/report/order/create-picklist-from-orders-for-shipping-by-provider */
export async function createPicklistFromOrdersListForShippingByProviderReport(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
) {
  return request<API.ResultObject<API.WarehousePicklist>>(
    `/api/report/order/create-picklist-from-orders-for-shipping-by-provider`,
    {
      method: 'POST',
      data: params,
      withToken: true,
      paramsSerializer,
    },
  ).then((res) => res.message);
}
