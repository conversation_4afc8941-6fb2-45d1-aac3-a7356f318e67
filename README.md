# Foodstore Dashboard frontend

This project is initialized with [Ant Design Pro](https://pro.ant.design). Follow is the quick guide for how to use.

## Environment Prepare

Install `node_modules`:

```bash
npm install
```

or

```bash
yarn
```

### Start project

```bash
yarn start
```

### Build project

```bash
yarn build
```

### Check code style

```bash
yarn lint
```

You can also use script to auto fix some lint error:

```bash
yarn lint:fix
```

### Test code

```bash
yarn test
```

## More

You can view full document on our [official website](https://pro.ant.design). And welcome any feedback in our [github](https://github.com/ant-design/ant-design-pro).

```
export NODE_OPTIONS=--openssl-legacy-provider
```

```javascript
onInputKeyDown: (e: any) => {
    if (Util.isTabPressed(e)) {
    const dropdownWrapper = document.getElementById(
        e.target.getAttribute('aria-controls'),
    );
    if (dropdownWrapper?.children.length == 1) {
        const id = e.target.getAttribute('aria-activedescendant');
        const value = sn(document.getElementById(id)?.innerText);
        // Note: not working! later will check.
        // form.setFieldValue(['warehouse_location', 'id'], value);

        setDataSource((prev) => {
        const newList = [...prev];
        const current = newList.find((x) => x.uid == record?.uid);
        if (current) {
            (current as any).warehouse_location = { id: value };
        }
        return newList;
        });
    }
    }
},
```
