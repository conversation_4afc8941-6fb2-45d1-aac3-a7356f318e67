import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormDependency, ProFormRadio, ProFormText } from '@ant-design/pro-form';
import { addEanSimple } from '@/services/foodstore-one/Item/ean';
import { message, Modal } from 'antd';
import Util, { sn } from '@/util';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import SkuComp from '@/components/SkuComp';

export type FormValueType = Partial<API.Ean> & {
  is_single?: string;
  parent_ean?: string;
  ean_name?: string;
};

const handleAdd = async (fields: FormValueType) => {
  const data = { ...fields };

  if (data.is_single == '1') {
    data.ean = data.parent_ean;
    data.attr_case_qty = 1;
  }

  if (!data.ean) {
    message.error('Fill EAN correctly!');
    return;
  }

  if (data.is_single == '1' && !data.ean_name) {
    message.error('Fill EAN Name correctly!');
    return;
  } else if (data.is_single == '2') {
    if (!sn(data.attr_case_qty)) {
      message.error('Fill Case Qty correctly!');
      return;
    }
    if (!data.parent_ean) {
      message.error('Fill Single EAN correctly!');
      return;
    }
  }

  const hide = message.loading('Adding...', 0);

  try {
    const res = await addEanSimple(data);
    return res;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateItemEanFormModalProps = {
  values?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
};

const CreateItemEanFormModal: React.FC<CreateItemEanFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, values } = props;

  return (
    <ModalForm
      title={'New Item EAN'}
      width="700px"
      size="large"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const res = await handleAdd(value as FormValueType);
        if (res) {
          handleModalVisible(false);
          Modal.success({
            title: 'Added successfully!',
            content: (
              <div>
                <h3>
                  SKU: <SkuComp sku={res.sku} />
                </h3>
                <h3>EAN: {res.ean}</h3>
                <h3>Name: {value.ean_name}</h3>
              </div>
            ),
            onOk: async () => {
              if (formRef.current) formRef.current.resetFields();
              if (onSubmit) await onSubmit(res);
            },
            maskClosable: false,
            okButtonProps: { size: 'large' },
          });
        }
      }}
    >
      <ProFormRadio.Group
        name="is_single"
        initialValue={'1'}
        label="Single or Multi?"
        options={[
          { value: '1', label: 'Single' },
          { value: '2', label: 'Multi' },
        ]}
      />

      <ProFormDependency name={[`is_single`]}>
        {(depValues) => {
          return depValues.is_single == '2' ? (
            <ProForm.Item name="ean" label="Multi EAN">
              <NumpadExtSelector
                inputProps={{ style: { width: 140 }, inputMode: 'none' }}
                isMobile
                value={values?.ean || ''}
              />
            </ProForm.Item>
          ) : null;
        }}
      </ProFormDependency>

      <ProFormDependency name={[`is_single`]}>
        {(depValues) => {
          return depValues.is_single == '2' ? (
            <ProForm.Item name="attr_case_qty" label="Case Qty">
              <NumpadExtSelector inputProps={{ style: { width: 140 }, inputMode: 'none' }} isMobile />
            </ProForm.Item>
          ) : null;
        }}
      </ProFormDependency>

      <ProForm.Item name="parent_ean" label="Single EAN">
        <NumpadExtSelector
          inputProps={{ style: { width: 140 }, inputMode: 'none' }}
          isMobile
          value={values?.ean || ''}
        />
      </ProForm.Item>

      {/* <ProFormDependency name={[`is_single`]}>
        {(depValues) => {
          return depValues.is_single ? (
            <ProFormSelect
              showSearch
              placeholder="Select an Item"
              request={async (params) => {
                const res = await getItemList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
                if (res && res.data) {
                  const tmp = res.data.map((x: API.Item) => ({
                    value: x.id,
                    label: `${x.name}`,
                  }));
                  return tmp;
                }
                return [];
              }}
              name="item_id"
              label="Item"
            />
          ) : null;
        }}
      </ProFormDependency> */}

      <ProFormDependency name={[`is_single`]}>
        {(depValues) => {
          return <ProFormText name="ean_name" label={depValues.is_single == '1' ? 'Name' : 'Multi Name'} />;
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default CreateItemEanFormModal;
