/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


drop table if exists xmag_inventory_stock;

CREATE TABLE `xmag_inventory_stock` (
                                        `sku` varchar(100) NOT NULL COMMENT 'PK',
                                        `source_code` varchar(20) NOT NULL COMMENT 'PK',
                                        `item_id` bigint(20) unsigned DEFAULT NULL COMMENT 'Item ID',
                                        `ean_id` bigint(20) unsigned DEFAULT NULL COMMENT 'Ean ID',
                                        `case_qty` int(11) DEFAULT NULL COMMENT 'Ean''s case Qty',
                                        `quantity` int(11) DEFAULT NULL COMMENT 'Magento stock Qty',
                                        `res_quantity` int(11) DEFAULT NULL COMMENT 'Reserved Qty. Calculated by quantity-salable qty on down syncing',
                                        `res_cal` int(11) DEFAULT NULL COMMENT 'Reserved Qty. Calculated differently by single or multi on system.',
                                        `status` int(11) DEFAULT NULL,
                                        `batch_code` bigint(20) DEFAULT NULL,
                                        PRIMARY KEY (`sku`,`source_code`),
                                        KEY `IDX_xmag_inventory_stock_ean_id` (`ean_id`),
                                        KEY `IDX_xmag_inventory_stock_item_id` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

drop table if exists xmag_sync_log;
CREATE TABLE `xmag_sync_log` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
                                 `sync_type` smallint(6) NOT NULL COMMENT '0: down sync 1: upsync',
                                 `category` varchar(255) DEFAULT NULL COMMENT 'Sync category',
                                 `name` varchar(255) DEFAULT NULL COMMENT 'Sync name',
                                 `note` text DEFAULT NULL COMMENT 'Sync description',
                                 `status` varchar(10) DEFAULT NULL COMMENT 'Sync status: started,processing,success, error',
                                 `req_method` varchar(10) DEFAULT NULL COMMENT 'Request method: GET,POST,DELETE, etc',
                                 `detail` longtext DEFAULT NULL COMMENT 'Detail Data in JSON',
                                 `batch_code` bigint(20) DEFAULT NULL COMMENT 'Batch code',
                                 `action_type` smallint(6) NOT NULL COMMENT 'Action type: 0: system UI, 1: cron',
                                 `created_by` bigint(20) unsigned DEFAULT NULL COMMENT 'User ID',
                                 `created_on` datetime DEFAULT NULL COMMENT 'Created time',
                                 `updated_on` datetime DEFAULT NULL,
                                 PRIMARY KEY (`id`),
                                 KEY `IDX_xmag_sync_log_name` (`name`),
                                 KEY `IDX_xmag_sync_log_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


drop table if exists xmag_sync_processing;

CREATE TABLE `xmag_sync_processing`
(
    `sync_type`   smallint(6) NOT NULL COMMENT '0: down sync 1: upsync',
    `name`        varchar(255)        DEFAULT NULL COMMENT 'Sync name',
    `note`        text                DEFAULT NULL COMMENT 'Sync description',
    `detail`      longtext            DEFAULT NULL COMMENT 'Detail Data in JSON',
    `batch_code`  bigint(20)          DEFAULT NULL COMMENT 'Batch code',
    `action_type` smallint(6) NOT NULL COMMENT 'Action type: 0: system UI, 1: cron',
    `uuid`        varchar(255)        DEFAULT NULL COMMENT 'UUID',
    `created_by`  bigint(20) unsigned DEFAULT NULL COMMENT 'User ID',
    `created_on`  datetime            DEFAULT NULL COMMENT 'Created time',
    PRIMARY KEY (`sync_type`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


-- Foreign Key fix
-- Foreign Key fix
ALTER TABLE `ean_text` DROP CONSTRAINT FK_ean_text_ean_id;

ALTER TABLE `ean_text`
    ADD CONSTRAINT `FK_ean_text_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `ean_supplier` DROP CONSTRAINT FK_ean_supplier_ean_id;

ALTER TABLE `ean_supplier`
    ADD CONSTRAINT `FK_ean_supplier_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=1 */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
