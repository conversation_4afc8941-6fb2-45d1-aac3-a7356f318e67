import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateOffer } from '@/services/foodstore-one/Offer/offer';
import Util, { sn } from '@/util';
import SProFormDigit from '@/components/SProFormDigit';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOffer(sn(fields.id), fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Offer>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Offer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Offer) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  return (
    <ModalForm
      title={'Update Offer'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: props.initialValues?.id,
        };
        const success = await handleUpdate(data);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormDigit
        rules={[
          {
            required: true,
            message: 'Offer no is required',
          },
        ]}
        width="md"
        name="offer_no"
        label="Offer No"
      />

      <ProFormTextArea width="lg" name="note" label="Notes" />
      <ProFormTextArea width="lg" name="offer_customer_note" label="Customer Notes" />
      <SProFormDigit width="md" name="percentage" label="Percentage" fieldProps={{ precision: 2 }} />
    </ModalForm>
  );
};

export default UpdateForm;
