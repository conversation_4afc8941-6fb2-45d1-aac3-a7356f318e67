/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/stock/stock-stable-booked';

/** 
 * Get stock stable booked list
 * 
 * GET /api/stock/stock-stable-booked */
export async function getStockStableBookedList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** post POST /api/stock/stock-stable-booked/createOrUpdate */
export async function createOrUpdateStockStableBooked(data: API.StockStableBooked & { qty_picked?: number }, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/createOrUpdate`, {
    method: 'POST',
    data: data,
    ...(options || {}),
    paramsSerializer,
  }).then(res => res.message);
}

/**
 * update stockStableBooked data
 *
 * PUT /api/stock/stock-stable-booked/{id}
 */
export async function updateStockStableBooked(id: number, data: Partial<API.StockStableBooked>, options?: { [key: string]: any }) {
  return request<API.StockStableBooked>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/**
 * Increment qty_packed by orderItemId in stock_stable_booked
 *
 * PUT /api/stock/stock-stable-booked/incQtyPacked/{orderItemId}
 */
export async function incStockStableBookedPackedQty(orderItemId: number, qtyPackedIncremented: number, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.StockStableBooked[]>>(`${urlPrefix}/incQtyPacked/${orderItemId}`, {
    method: 'PUT',
    data: {
      qtyPackedIncremented
    },
    ...(options || {}),
  }).then(res => res.message);
}


/**
 * set qty_packed by orderItemId in stock_stable_booked
 *
 * PUT /api/stock/stock-stable-booked/incQtyPacked/{orderItemId}
 */
export async function setStockStableBookedQtyPacked(orderItemId: number, qtyPacked: number, extraData?: Record<string, any>, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.StockStableBooked[]>>(`${urlPrefix}/setQtyPacked/${orderItemId}`, {
    method: 'PUT',
    data: {
      qtyPacked,
      ...extraData
    },
    ...(options || {}),
  }).then(res => res.message);
}





/** delete DELETE /api/stock/stock-stable-booked/{id} */
export async function deleteStockStableBooked(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}



