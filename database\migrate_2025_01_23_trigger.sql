ALTER TABLE `ibo_pre`
    ADD COLUMN `sent_date`     DATE NULL AFTER `detail`,
    ADD COLUMN `invoiced_date` DATE NULL AFTER `sent_date`;

drop trigger if exists ibo_pre_status_change;

DELIMITER $$
CREATE
    TRIGGER `ibo_pre_status_change`
    B<PERSON>OR<PERSON> update
    ON `ibo_pre`
    FOR EACH ROW
BEGIN
    if NEW.status = 'sent' then
        SET NEW.sent_date = NOW();
    end if;
    if NEW.status = 'invoiced' then
        SET NEW.invoiced_date = NOW();
    end if;
END$$

DELIMITER ;
