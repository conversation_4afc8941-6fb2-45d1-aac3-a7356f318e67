drop table if exists import_supplier_data;
drop table if exists import_supplier_data_price;


DELIMITER $$

DROP TRIGGER /*!50032 IF EXISTS */ `ean_price_after_update`$$

CREATE
    TRIGGER `ean_price_after_update` AFTER UPDATE ON `ean_price`
    FOR EACH ROW
BEGIN
    IF NEW.price != OLD.price THEN
        INSERT INTO item_ean_price_history(ean_id, price_type_id, price, created_on)
        VALUES (NEW.ean_id, NEW.price_type_id, NEW.price, NOW());
    END IF;
END;
$$

DELIMITER ;



