import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Alert, message } from 'antd';
import React, { useState } from 'react';
import { ProFormText, LoginForm } from '@ant-design/pro-form';
// @ts-ignore
import { history, useModel } from 'umi';
import Footer from '@/components/Footer';
import { login } from '@/services/foodstore-one/login';
import styles from './index.less';
import { LS_TOKEN_NAME, UserRole } from '@/constants';

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login: React.FC = () => {
  const [errorMsg, setErrorMsg] = useState('');
  const { initialState, setInitialState } = useModel('@@initialState');

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();

    if (userInfo) {
      await setInitialState((s: any) => ({ ...s, currentUser: userInfo }));
    }

    return userInfo;
  };

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      const res = await login({ ...values, type: 'account' });

      const defaultLoginSuccessMessage = 'Login successful!';
      message.success(defaultLoginSuccessMessage);
      localStorage.setItem(LS_TOKEN_NAME, res.Authorization || '');
      const userInfo = await fetchUserInfo();

      if (!history) return;
      const { query } = history.location;
      const { redirect } = query as {
        redirect: string;
      };

      if (userInfo?.role == UserRole.EDITOR) {
        history.push(redirect || '/item/ean-detail');
      } else if (userInfo?.role == UserRole.WAREHOUSE) {
        history.push(redirect || '/orders/order-detail');
      } else {
        history.push(redirect || '/');
      }

      return;
    } catch (error: any) {
      console.error(error);
      setErrorMsg('Incorrect username or password');
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <LoginForm
          logo={<img src="/images/favicon.png" />}
          title="FoodStore.one"
          subTitle={' '}
          initialValues={{
            autoLogin: true,
          }}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
          submitter={{ searchConfig: { submitText: 'Login' } }}
        >
          {errorMsg && <LoginMessage content={errorMsg} />}

          <ProFormText
            name="username"
            fieldProps={{
              size: 'large',
              prefix: <UserOutlined className={styles.prefixIcon} />,
            }}
            placeholder={'Username'}
            rules={[
              {
                required: true,
                message: 'Please input your username!',
              },
            ]}
          />
          <ProFormText.Password
            name="password"
            fieldProps={{
              size: 'large',
              prefix: <LockOutlined className={styles.prefixIcon} />,
            }}
            placeholder={'Password'}
            rules={[
              {
                required: true,
                message: 'Please input your password!',
              },
            ]}
          />

          <div
            style={{
              marginBottom: 24,
            }}
          >
            {/* <ProFormCheckbox noStyle name="autoLogin">
              Remember me
            </ProFormCheckbox> */}
            {/*<a
              style={{
                float: 'right',
              }}
            >
              Forgot Password ?
            </a>*/}
          </div>
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
