/* eslint-disable @typescript-eslint/dot-notation */
import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Card, Col, Row, Space, Spin, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import _ from 'lodash';
import SocialLinks from './SocialIcons';
import { getStockByEanId } from '@/services/foodstore-one/Stock/stock-stable';
import Util, { ni, sn } from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import StockStableCorrectionEditableTable from './StockStableCorrectionEditableTable';
import { useModel } from 'umi';
import { InfoCircleOutlined } from '@ant-design/icons';
import StockMovementListModal from '@/pages/Stock/StockMovement/StockMovementListModal';
import EanTitleComp from '@/components/EanTitleComp';

export type FormValueType = Partial<API.Ean>;
export type StockQtyDataType = {
  total_row?: Partial<API.StockStable>;
  rows_by_exp_date?: Partial<API.StockStable & { oldest_ibo?: API.Ibo }>[];
  rows?: Partial<API.StockStable>[];
  qty_processing: number;
};

export type StockStableQtyModalProps = {
  initialValues?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean, isParent?: boolean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const StockStableQtyModal: React.FC<StockStableQtyModalProps> = (props) => {
  const itemEan = props.initialValues;

  const [loading, setLoading] = useState<boolean>(false);
  const { appSettings } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const [data, setData] = useState<StockQtyDataType>({ qty_processing: 0 });
  const [refresh, setRefresh] = useState<boolean>(false);
  const [itemMode, setItemMode] = useState<boolean>(false);

  // open stock movement modal
  const [openStockMovementModal, setOpenStockMovementModal] = useState<boolean>(false);

  useEffect(() => {
    if (!props.modalVisible || !props.initialValues?.id) return;
    setLoading(true);
    getStockByEanId(props.initialValues?.id, {
      item_id: props.initialValues.item_id,
      item_mode: formRef.current?.getFieldValue('include_all') ? 1 : 0,
      show_zero: formRef.current?.getFieldValue('show_zero') ? 1 : 0,
    })
      .then((res) => setData(res))
      .finally(() => setLoading(false));
  }, [props.initialValues?.id, props.modalVisible, refresh, props.initialValues?.item_id]);

  const columns: ProColumns<API.StockStable & { oldest_ibo?: API.Ibo }>[] = useMemo(
    () => [
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 100,
        render: (dom, record) => {
          return Util.dtToDMY(record?.exp_date);
        },
      },
      {
        title: 'Recv. Date',
        dataIndex: ['oldest_ibo', 'ibom', 'received_date'],
        width: 100,
        tooltip: 'IBOM Received Date of the first connected IBO.',
        render: (dom, record) => {
          return Util.dtToDMY(record?.oldest_ibo?.ibom?.received_date);
        },
      },
      {
        title: 'IBOM',
        dataIndex: ['oldest_ibo', 'ibom', 'order_no'],
        tooltip: 'IBOM info of the first connected IBO.',
        render: (dom, record) => {
          const ibom = record?.oldest_ibo?.ibom;
          return `#${ibom?.order_no ?? '-'} | ${ibom?.supplier?.name ?? '-'}`;
        },
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 90,
        align: 'right',
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 90,
        align: 'right',
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 120,
        align: 'right',
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
    ],
    [],
  );

  return (
    <>
      <ModalForm
        title={
          <>
            Stock Detail
            <EanTitleComp itemEan={props.initialValues} />
            <SocialLinks
              ean={props.initialValues?.ean || ''}
              title={props.initialValues?.ean_texts?.[0]?.name}
              style={{ marginLeft: 50 }}
            />
            <Button
              type="primary"
              ghost
              size="small"
              style={{ float: 'right', marginRight: 40 }}
              title="View stock movements..."
              onClick={() => setOpenStockMovementModal(true)}
            >
              Stock Movements...
            </Button>
          </>
        }
        width={1350}
        visible={props.modalVisible}
        onVisibleChange={props.handleModalVisible}
        layout="inline"
        modalProps={{
          maskClosable: true,
          className: props.initialValues?.is_single ? 'm-single' : 'm-multi',
        }}
        formRef={formRef}
        onFinish={async (values) => {
          if (props.onSubmit) {
            props.onSubmit(values);
          }
          props.handleModalVisible(false);
        }}
        submitter={false}
      >
        <div>
          <Space size={16} style={{ margin: '0 0 12px 0' }}>
            <label>EAN Name: </label>
            <Typography.Paragraph
              copyable={{
                text: props.initialValues?.ean_texts?.[0]?.name ?? props.initialValues?.ean_text_de?.name ?? '-',
                tooltips: 'Copy EAN Name',
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {props.initialValues?.ean_texts?.[0]?.name ?? props.initialValues?.ean_text_de?.name ?? '-'}
            </Typography.Paragraph>
          </Space>
        </div>
        <Spin spinning={loading}>
          <ProFormGroup>
            {!props.initialValues?.is_single && (
              <ProFormSwitch
                width="xs"
                name={['include_all']}
                label="Include All"
                tooltip="Include All EANs of this item"
                fieldProps={{
                  onChange: (value) => {
                    setItemMode(value);
                    setRefresh((prev) => !prev);
                  },
                }}
              />
            )}

            <ProFormSwitch
              width="xs"
              name={['show_zero']}
              label="Show finished?"
              tooltip="Show finished stocks."
              fieldProps={{
                onChange: (value) => {
                  setRefresh((prev) => !prev);
                },
              }}
            />
          </ProFormGroup>
          <Card title="Summary" bordered={false} size="small" headStyle={{ fontSize: 16, paddingLeft: 0 }}>
            <Row>
              <Col span={10}>
                <Space>
                  <div>Total Qty:</div>
                  <div>
                    <span className="text-md bold">{ni(data?.total_row?.piece_qty, true)}</span> pcs
                  </div>
                  <div>
                    + <span className="text-md bold">{ni(data?.total_row?.box_qty, true)}</span> boxes
                  </div>
                  <div>
                    <span className="text-md bold"> = {ni(data?.total_row?.total_piece_qty, true)}</span> pcs
                  </div>
                </Space>
              </Col>
              <Col span={10}>
                <Space>
                  <div>
                    Magento Stock Qty{' '}
                    <InfoCircleOutlined
                      title={`Last synced at ${Util.dtToDMYHHMM(
                        appSettings?.magDsStat?.['xmag_inventory_stockbase']?.['last_sync_at'],
                        '-',
                      )}\n\n Formula: Stock Qty=Salable Qty + Reserved Qty`}
                    />
                  </div>
                  <div>
                    <span className="text-md bold" title="Salable Qty + Reserved Qty">
                      {ni(itemEan?.mag_inventory_stocks_sum_quantity, true)}
                      {sn(itemEan?.mag_inventory_stocks_sum_res_quantity)
                        ? ` = ${
                            sn(itemEan?.mag_inventory_stocks_sum_quantity) -
                            sn(itemEan?.mag_inventory_stocks_sum_res_quantity)
                          } + ${itemEan?.mag_inventory_stocks_sum_res_quantity}`
                        : ''}
                    </span>
                    {itemEan?.is_single ? ' pcs' : ' boxes'}
                  </div>
                </Space>
              </Col>
              <Col span={4}>
                Processing Qty: <span className="text-md bold">{ni(data.qty_processing, true)}</span>
              </Col>
            </Row>
          </Card>

          <ProTable
            headerTitle={'Qty by Exp.Date'}
            rowKey={'exp_date'}
            columns={columns}
            dataSource={data?.rows_by_exp_date || []}
            search={false}
            cardProps={{ bodyStyle: { padding: 0, marginBottom: 16 } }}
            style={{ width: 800 }}
            toolbar={{
              search: false,
              actions: [],
              settings: undefined,
            }}
            pagination={false}
            size="small"
          />
          <StockStableCorrectionEditableTable
            ean={{
              id: sn(props.initialValues?.id),
              item_id: sn(props.initialValues?.item_id),
              ean: props.initialValues?.ean,
              sku: props.initialValues?.sku,
              is_single: props.initialValues?.is_single,
              parent_id: props.initialValues?.parent_id,
            }}
            reload={() => setRefresh((prev) => !prev)}
            itemMode={itemMode}
            initialData={data?.rows}
          />
        </Spin>
      </ModalForm>
      <StockMovementListModal
        searchParams={{
          sku: props.initialValues?.sku,
          ean: props.initialValues?.ean,
          item_id: props.initialValues?.item_id,
          is_single: props.initialValues?.is_single,
        }}
        modalVisible={openStockMovementModal}
        handleModalVisible={setOpenStockMovementModal}
      />
    </>
  );
};

export default StockStableQtyModal;
