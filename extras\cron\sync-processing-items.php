<?php
/**
 *
 * Get SKUs which are sold in processing orders and update their salable Qty.
 *
 * @package     Cron job script.
 * @since       2023-01-31
 * @deprecated  2023-11-15    No meaning??? because we don't use salable qty!!! Later check again.
 *
 * @recommendedPeriod: 15 min
 */

use App\Service\Magento\Stock\MagInventoryService;

error_reporting(E_ALL);

require __DIR__ . '/../../extras/cron/_cron_before.php';

/** @var \Slim\Container $container */
/** @var \App\Service\Magento\Order\MagOrderService $mOrderService */
$mOrderService = $container->get('mag_order_service');

$skus = $mOrderService->dsSkusInProcessingOrder();
// $skus = ['105_186', '105_187']; // To do
echo 'Sync processing items: ' . count($skus ?? []) . PHP_EOL;
if ($skus) {
    /** @var \App\Service\Magento\Stock\MagInventoryService $magInventoryService */
    $magInventoryService = $container->get(MagInventoryService::class);
    $magInventoryService->dsSalableQuantity(compact('skus'));
}
echo "OK" . PHP_EOL;

require __DIR__ . '/../../extras/cron/_cron_after.php';



