import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { But<PERSON>, Col, message, Modal, Popover, Row, Space, Typography, List, Divider } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import { ProFormList } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormField, ProFormGroup, ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { getEanDetail, getShopProduct, updateEanAttribute, usProduct } from '@/services/foodstore-one/Item/ean';
import Util, { nf2 } from '@/util';
import _ from 'lodash';
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit from '@/components/SProFormDigit';
import SocialLinks from './SocialIcons';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { useModel } from 'umi';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import type { HandleNavFuncType } from '../hooks/useModalNavigation';
import ModalNavigation from './ModalNavigation';
import GdsnItemButton from './GdsnItemButton';
import useGdsnItem from '../hooks/useGdsnItem';
import useUpdateModalActions from '../hooks/useUpdateModalActions';

export enum UsAllowanceSetting {
  CHECK_ALL = '0',
  NUTRITION_CHECK = '2',
  NUTRITION_MATERIAL_CHECK = '4',
  NO_CHECK = '8',
}

export const UsAllowanceSettingKv = {
  [UsAllowanceSetting.CHECK_ALL]: 'Full Set needed',
  [UsAllowanceSetting.NUTRITION_CHECK]: 'All, except Nährwerte',
  [UsAllowanceSetting.NUTRITION_MATERIAL_CHECK]: 'All, except Zutaten & Nährwerte',
  [UsAllowanceSetting.NO_CHECK]: 'Always, no check made',
};

// export const UpSyncAllowanceSettingOptions = Object.keys(UsAllowanceSettingKv).map(x => ({ value: x, label: UsAllowanceSettingKv[x] }));

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    const res = await updateEanAttribute(fields);
    hide();
    message.success('Updated successfully.');
    if (res.upSyncMessage) {
      message.error('Up sync error: ' + res.upSyncMessage);
    }
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Ean>;

const getSmallItemBaseText = (depValues: FormValueType) => {
  let smallUnit = '';
  const item_base_unit = depValues.item_base_unit ?? 'kg';
  if (item_base_unit == 'kg') {
    smallUnit = 'g';
  } else if (item_base_unit == 'l') {
    smallUnit = 'ml';
  }
  return smallUnit && depValues.item_base ? (
    <>
      <span className={(depValues?.item_base ?? 0) >= 1 ? 'red' : ''} style={{ paddingLeft: 5 }}>
        {' '}
        = {Util.numberFormat((depValues?.item_base || 0) * 1000, true, 5, true) + ' ' + smallUnit}
      </span>
    </>
  ) : (
    <></>
  );
};

export type UpdateAttributeFormProps = {
  initialValues?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  reloadList?: (updatedRow: Partial<API.Ean>) => Promise<boolean | void>;
  handleNavigation?: HandleNavFuncType;
  gdsn?: boolean;
};

const UpdateAttributeForm: React.FC<UpdateAttributeFormProps> = (props) => {
  const { initialValues, modalVisible } = props;

  const [loading, setLoading] = useState(false);
  const [loadingLive, setLoadingLive] = useState(false);

  const { appSettings } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const suppliersListActionRef = useRef<FormListActionType>();

  // GDSN data
  // GDSN data
  const {
    gdsnItem,
    fetchGdsnItem,
    renderTakeButton,
    loading: loadingGdsn,
  } = useGdsnItem(props.initialValues?.ean, props.modalVisible);

  useEffect(() => {
    if (formRef.current && props.modalVisible) {
      const newFormValues = { ...(props.initialValues || {}) };
      if (!newFormValues.item_base_unit) newFormValues.item_base_unit = 'kg';
      if (newFormValues.note?.usAllowance == null) {
        newFormValues.note = { ...newFormValues.note, usAllowance: UsAllowanceSetting.CHECK_ALL };
      }

      // newFormValues.m_status = newFormValues.m_status == 1;

      formRef.current.setFieldsValue(newFormValues);
    }
  }, [props.initialValues, props.modalVisible]);

  const [parentEanSuppliers, setParentEanSuppliers] = useState<API.Supplier[]>([]);
  useEffect(() => {
    if (modalVisible && initialValues?.parent_id && !initialValues?.is_single) {
      getEanDetail({ id: `${initialValues.parent_id}`, with: 'eanSuppliers' })
        .then((res) => {
          setParentEanSuppliers(res.ean_suppliers ?? []);
        })
        .catch(() => {
          message.error('Failed to load Suppliers & Product Nos list of parent EAN!');
        })
        .finally(() => {});
    }
  }, [modalVisible, initialValues?.is_single, initialValues?.parent_id]);

  const [gdsnItemsDiff, setGdsnItemsDiff] = useState<any>({});
  const validateGdsnItemsDiff = useCallback(() => {
    const values = formRef.current?.getFieldsValue();
    setGdsnItemsDiff({
      item_base: values.item_base == gdsnItem?.item_base,
      item_base_unit: values.item_base_unit == gdsnItem?.item_base_unit,
      weight: values.weight == gdsnItem?.gross_weight,
      width: values.width == gdsnItem?.width,
      height: values.height == gdsnItem?.height,
      length: values.length == gdsnItem?.length,
    });
  }, [
    gdsnItem?.gross_weight,
    gdsnItem?.height,
    gdsnItem?.item_base,
    gdsnItem?.item_base_unit,
    gdsnItem?.length,
    gdsnItem?.width,
  ]);

  useEffect(() => {
    if (modalVisible && props.initialValues?.ean) {
      validateGdsnItemsDiff();
    }
  }, [modalVisible, props.initialValues?.ean, validateGdsnItemsDiff]);

  const gdsnTemperature = gdsnItem?.detail?.temp?.length
    ? gdsnItem?.detail?.temp.find((x) => x.code == 'STORAGE_HANDLING')
    : null;

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useUpdateModalActions(
    props.initialValues?.id ?? 0,
    props.initialValues?.sku ?? '',
    formRef.current,
  );

  return (
    <ModalForm<Partial<API.Ean>>
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update EAN attributes</span>
          <Typography.Paragraph
            copyable={{
              text: props.initialValues?.ean || '',
              tooltips: 'Copy EAN ' + (props.initialValues?.ean || ''),
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {' '}
            - {props.initialValues?.ean || ''}
          </Typography.Paragraph>
          <SocialLinks
            ean={props.initialValues?.ean || ''}
            title={props.initialValues?.ean_texts?.[0]?.name}
            style={{ marginLeft: 50 }}
          />
          <ModalNavigation
            modalName="attribute"
            eanId={props.initialValues?.id}
            itemId={props.initialValues?.item_id}
            handleNavigation={props.handleNavigation}
            style={{ marginLeft: 50 }}
          />
          {props.gdsn && (
            <GdsnItemButton
              ean={props.initialValues?.ean}
              eanId={props.initialValues?.id}
              itemId={props.initialValues?.item_id}
              style={{ marginLeft: 50 }}
              fetchGdsnItem={fetchGdsnItem}
            />
          )}
          {actionButtons}
        </div>
      }
      disabled={loadingGdsn}
      width={930}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      formRef={formRef}
      onFinish={async (value) => {
        if (!formRef.current) return Promise.resolve(false);
        setLoading(true);
        if (formRef.current?.isFieldsTouched()) {
          const newData = { ...value, id: props.initialValues?.id };

          const success = await handleUpdate(newData);
          if (success) {
            await runActionsCallback();

            setLoading(false);
            if ((value as any).closeModal) props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          setLoading(false);
          props.handleModalVisible(false);
        }
        formRef.current.getFieldInstance('up-sync').value = '';
        return Promise.resolve(true);
      }}
      modalProps={{
        confirmLoading: loading,
        className: props.initialValues?.is_single ? 'm-single' : 'm-multi',
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {actionButtons}
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Save & Close
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
        {hiddenFormElements}
      </div>
      <ProFormGroup rowProps={{ gutter: 0 }}>
        <SProFormDigit
          colProps={{ span: 'auto' }}
          name="item_base"
          label="Item Base"
          width={120}
          fieldProps={{
            precision: 5,
            onChange(value) {
              validateGdsnItemsDiff();
            },
          }}
          formItemProps={{
            tooltip: 'This is Weight/1000 (Kg)',
            help: (
              <div>
                <div>
                  {gdsnItem &&
                    renderTakeButton(
                      Util.numberFormat(gdsnItem.item_base, true, 5, true),
                      () => {
                        formRef.current?.setFieldValue('item_base', gdsnItem.item_base);
                        validateGdsnItemsDiff();
                      },
                      gdsnItemsDiff.item_base,
                    )}
                </div>
                <div>
                  {props.initialValues?.is_single ? null : (
                    <ProFormDependency key={'item_base_alt2'} name={['item_base_unit', 'item_base', 'attr_case_qty']}>
                      {(depValues) => {
                        return (
                          <>
                            <InfoCircleOutlined title="Based on parent item." /> &nbsp;
                            {`${depValues.attr_case_qty} x ${Util.numberFormat(
                              props.initialValues?.parent?.item_base,
                              true,
                              5,
                              true,
                            )} ${depValues.item_base_unit} = ${Util.numberFormat(
                              Util.safeNumber(props.initialValues?.parent?.item_base) * depValues.attr_case_qty,
                              true,
                              5,
                              true,
                            )} ${depValues.item_base_unit}`}
                          </>
                        );
                      }}
                    </ProFormDependency>
                  )}
                </div>
              </div>
            ),
          }}
        />
        <ProFormSelect
          name={'item_base_unit'}
          label="Item Base Unit"
          colProps={{ span: 'auto' }}
          width={90}
          rules={[
            {
              required: true,
              message: 'Item Base Unit is required',
            },
          ]}
          initialValue={'kg'}
          convertValue={(value) => (value === null ? 'kg' : value)}
          options={[
            { value: 'kg', label: 'kg' },
            { value: 'l', label: 'L' },
          ]}
          fieldProps={{
            onChange(value) {
              validateGdsnItemsDiff();
            },
          }}
          help={
            gdsnItem && (
              <>
                {renderTakeButton(
                  gdsnItem.item_base_unit,
                  () => {
                    formRef.current?.setFieldValue(['item_base_unit'], gdsnItem.item_base_unit);
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.item_base_unit,
                )}{' '}
                <div className="text-xs" style={{ textAlign: 'right' }}>
                  ({`${gdsnItem.detail?.net_content} ${gdsnItem.detail?.net_content_unit ?? 'N/A'}`})
                </div>
              </>
            )
          }
        />
        <ProFormDependency key={'item_base_alt'} name={['item_base_unit', 'item_base']}>
          {(depValues) => {
            return (
              <>
                <ProFormField label=" " colProps={{ span: 6 }}>
                  {getSmallItemBaseText(depValues)}
                </ProFormField>
              </>
            );
          }}
        </ProFormDependency>
        <ProFormField colProps={{ span: 'auto' }}>
          <Space direction="vertical">
            <Space size={16}>
              <label>Item Name: </label>
              <Typography.Paragraph
                copyable={{
                  text: props.initialValues?.item?.name || '',
                  tooltips: 'Copy',
                }}
                style={{ display: 'inline-block', marginBottom: 0 }}
              >
                {props.initialValues?.item?.name || '-'}
              </Typography.Paragraph>
            </Space>
            <Space size={16}>
              <label>EAN Name: </label>
              <Typography.Paragraph
                copyable={{
                  text: props.initialValues?.ean_texts?.[0]?.name || '',
                  tooltips: 'Copy EAN Name',
                }}
                style={{ display: 'inline-block', marginBottom: 0 }}
              >
                {props.initialValues?.ean_texts?.[0]?.name || '-'}
              </Typography.Paragraph>
            </Space>
          </Space>
        </ProFormField>
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText
          rules={[
            {
              required: true,
              message: 'EAN is required',
            },
          ]}
          width="md"
          name="ean"
          label="EAN"
          colProps={{ xl: 7 }}
        />
        <ProFormSwitch name={'ean_visible'} label="EAN visible" colProps={{ xl: 3 }} />
        <SProFormDigit
          width="xs"
          name="attr_case_qty"
          label="Qty per Case"
          min={1}
          colProps={{ xl: 4 }}
          fieldProps={{ precision: 0 }}
        />
        <ProFormText
          rules={[
            {
              required: true,
              message: 'SKU is required',
            },
          ]}
          width="md"
          name="sku"
          label="SKU"
          colProps={{ xl: 6 }}
        />
        <SProFormDigit
          width="xs"
          name="minimum_order_qty"
          label="Min. Order Qty"
          min={0}
          colProps={{ xl: 4 }}
          fieldProps={{ precision: 0 }}
        />
        <ProFormSwitch name="ean_ebay" label="Use this EAN in Ebay." colProps={{ xl: 7 }} />
        <ProFormSelect
          name={['note', 'usAllowance']}
          label={
            <>
              Upsync allowance &nbsp;
              <Popover
                content={
                  <Row gutter={16} style={{ width: 550 }}>
                    <Col span={24} style={{ marginBottom: 12 }}>
                      <b>Required fields checking in upsync.</b>
                    </Col>
                    <Col span={11} style={{ marginBottom: 8, borderBottom: '1px solid #ddd' }}>
                      Up sync option
                    </Col>
                    <Col span={13} style={{ marginBottom: 8, borderBottom: '1px solid #ddd' }}>
                      Attributes
                    </Col>
                    <Col span={11}>
                      <div style={{ marginBottom: 8 }}>Full Set needed: Check all</div>
                      <div style={{ marginBottom: 8 }}>All, except Nährwerte</div>
                      <div style={{ marginBottom: 8 }}>All, except Zutaten & Nährwerte</div>
                      <div style={{ marginBottom: 8 }}>Always, no check made</div>
                    </Col>
                    <Col span={13}>
                      <div style={{ marginBottom: 8 }}>ItemBase</div>
                      <div style={{ marginBottom: 8 }}>ItemBaseUnit</div>
                      <div style={{ marginBottom: 8 }}>Weight</div>
                      <div style={{ marginBottom: 8 }}>Official Titel (from Update EAN Text)</div>
                      <div style={{ marginBottom: 8 }}>Official Producer (from Update EAN Text)</div>
                      <div style={{ marginBottom: 8 }}>Zutaten (from Update EAN Text)</div>
                      <div style={{ marginBottom: 8 }}>Nährwerte (from Update EAN Text)</div>
                    </Col>
                  </Row>
                }
              >
                <InfoCircleOutlined />
              </Popover>
            </>
          }
          valueEnum={UsAllowanceSettingKv}
          colProps={{ xl: 7 }}
        />
        <ProFormSwitch name="not_deliverable" label="Not Deliverable?" colProps={{ xl: 4 }} />
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <SProFormDigit
          width="xs"
          name="weight"
          label="Weight"
          min={0}
          addonAfter="g"
          fieldProps={{
            precision: 3,
            onChange(value) {
              validateGdsnItemsDiff();
            },
          }}
          colProps={{ span: 'auto' }}
          help={
            gdsnItem &&
            renderTakeButton(
              Util.numberFormat(gdsnItem.gross_weight, true, 3, true),
              () => {
                formRef.current?.setFieldValue('weight', gdsnItem.gross_weight);
                validateGdsnItemsDiff();
              },
              gdsnItemsDiff.weight,
            )
          }
        />
        <SProFormDigit
          width="xs"
          name="width"
          label="Width"
          min={0}
          addonAfter="cm"
          colProps={{ span: 'auto' }}
          fieldProps={{
            precision: 2,
            onChange(value) {
              validateGdsnItemsDiff();
            },
          }}
          help={
            gdsnItem &&
            renderTakeButton(
              nf2(gdsnItem.width),
              () => {
                formRef.current?.setFieldValue(['width'], gdsnItem.width);
                validateGdsnItemsDiff();
              },
              gdsnItemsDiff.width,
            )
          }
        />
        <SProFormDigit
          width="xs"
          name="height"
          label="Height"
          addonAfter="cm"
          fieldProps={{
            precision: 2,
            onChange(value) {
              validateGdsnItemsDiff();
            },
          }}
          min={0}
          colProps={{ span: 'auto' }}
          help={
            gdsnItem &&
            renderTakeButton(
              nf2(gdsnItem.height),
              () => {
                formRef.current?.setFieldValue(['height'], gdsnItem.height);
                validateGdsnItemsDiff();
              },
              gdsnItemsDiff.height,
            )
          }
        />
        <SProFormDigit
          width="xs"
          name="length"
          label="Length"
          min={0}
          addonAfter="cm"
          colProps={{ span: 'auto' }}
          fieldProps={{
            precision: 2,
            onChange(value) {
              validateGdsnItemsDiff();
            },
          }}
          help={
            gdsnItem &&
            renderTakeButton(
              nf2(gdsnItem.length),
              () => {
                formRef.current?.setFieldValue(['length'], gdsnItem.length);
                validateGdsnItemsDiff();
              },
              gdsnItemsDiff.length,
            )
          }
        />
        {gdsnItem?.detail ? (
          <div style={{ width: 260, marginLeft: 16 }} className="ant-col ant-col-auto text-sm">
            {gdsnItem.detail.net_content ? (
              <Row>
                <Col span={11}>Net content:</Col>{' '}
                <Col>
                  {gdsnItem.detail.net_content ?? ''} {gdsnItem.detail.net_content_unit}
                </Col>
              </Row>
            ) : null}
            <Row>
              <Col span={11}>Arrival Lifespan:</Col> <Col>{gdsnItem.detail.life_span_arrival ?? ''} </Col>
            </Row>
            <Row>
              <Col span={11}>Production Lifespan:</Col> <Col>{gdsnItem.detail.life_span_production ?? ''}</Col>
            </Row>
            {gdsnTemperature && gdsnTemperature.min_unit ? (
              <Row>
                <Col span={11}>Storage temperature:</Col>
                <Col>
                  {gdsnTemperature.min ?? '-'} {gdsnTemperature?.min_unit} ~ {gdsnTemperature.max ?? '-'}
                  {gdsnTemperature?.max_unit}
                </Col>
              </Row>
            ) : null}
          </div>
        ) : null}
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }} title="Additional info">
        <ProFormSelect
          name="attribute_set_code"
          label="Attribute Set"
          colProps={{ xl: 6 }}
          required
          options={appSettings.productAttributeSet?.map((x) => ({
            value: x.attribute_set_id,
            label: x.attribute_set_name,
          }))}
          rules={[
            {
              required: true,
              message: 'Attribute Set is required',
            },
          ]}
        />

        <ProFormSelect
          name="product_type"
          label="Product Type"
          colProps={{ xl: 6 }}
          required
          initialValue={'simple'}
          options={[
            {
              value: 'simple',
              label: 'Simple Product',
            },
            /* {
              "value": "virtual",
              "label": "Virtual Product"
            },
            {
              "value": "downloadable",
              "label": "Downloadable Product"
            },
            {
              "value": "bundle",
              "label": "Bundle Product"
            },
            {
              "value": "grouped",
              "label": "Grouped Product"
            },
            {
              "value": "configurable",
              "label": "Configurable Product"
            } */
          ]}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
          rules={[
            {
              required: true,
              message: 'Product Type is required',
            },
          ]}
        />

        <ProFormSelect
          name="visibility"
          label="Visibility"
          colProps={{ xl: 6 }}
          required
          initialValue={4}
          options={appSettings.productAttributes?.visibility?.options}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
          rules={[
            {
              required: true,
              message: 'Visibility is required',
            },
          ]}
        />
        <ProFormSelect
          name="product_websites"
          label="Product Websites"
          colProps={{ xl: 6 }}
          mode="multiple"
          options={appSettings.storeWebsites
            ?.filter((x) => x.code != 'admin')
            ?.map((x) => ({
              value: `${x.id}`,
              label: x.name,
            }))}
        />

        <SDatePicker name="new_from_date" label="New Start Date" colProps={{ xl: 6 }} />
        <SDatePicker name="new_to_date" label="New End Date" colProps={{ xl: 6 }} />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }} title="Product status">
        <ProFormSwitch
          name={'status'}
          label="Status"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_kaufland'}
          label="Export to Kaufland?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_idealo'}
          label="Export to Idealo?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_billiger'}
          label="Export to Billiger?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_google'}
          label="Export to Google?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />

        <ProFormSwitch
          name={'m_status'}
          label={
            <Space>
              <span>Live in Magento?</span>
              <Button
                type="link"
                loading={loadingLive}
                disabled={loadingLive}
                style={{ padding: 0, margin: 0, height: 20 }}
                onClick={() => {
                  setLoadingLive(true);
                  getShopProduct(props.initialValues?.sku ?? '')
                    .then((productPartial) => {
                      formRef.current?.setFieldsValue({ m_status: productPartial.status });
                    })
                    .catch(Util.error)
                    .finally(() => setLoadingLive(false));
                }}
                icon={<ReloadOutlined />}
              />
            </Space>
          }
          getValueProps={(value) => ({ value: value == 1 })}
          checkedChildren="Live"
          unCheckedChildren="Offline"
          colProps={{ span: 'auto' }}
          fieldProps={{
            loading: loadingLive,
            onClick: (status, e) => {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to change?',
                  onOk: async () => {
                    resolve(true);
                    setLoadingLive(true);
                    usProduct(props.initialValues?.sku ?? '', { status: status ? 1 : 2 })
                      .then((productPartial) => {
                        formRef.current?.setFieldsValue({ m_status: productPartial.status });
                      })
                      .catch(Util.error)
                      .finally(() => setLoadingLive(false));
                    return true;
                  },
                  onCancel: () => {
                    reject(true);
                    formRef.current?.setFieldsValue({ m_status: !status });
                  },
                });
              });
            },
          }}
        />
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }} title="Suppliers & Product No">
        <List
          key={'id'}
          style={{ width: 500, padding: '0 12px' }}
          dataSource={parentEanSuppliers}
          renderItem={(item: API.Supplier) => (
            <div key={item.id}>
              <Row gutter={8}>
                <Col span={12} style={{ padding: '4px 4px' }}>
                  {item.name}
                </Col>
                <Col span={12} style={{ padding: '4px 8px' }}>
                  {item.pivot?.product_no}
                </Col>
              </Row>
            </div>
          )}
        />
        <Divider style={{ marginTop: 12, marginBottom: 12, width: 500, minWidth: 500 }} />
        <ProFormList
          actionRef={suppliersListActionRef}
          name={['ean_suppliers']}
          creatorButtonProps={{
            position: 'bottom',
            creatorButtonText: 'Add',
          }}
          deleteIconProps={{ tooltipText: 'Remove' }}
          copyIconProps={{ tooltipText: 'Copy row' }}
          actionRender={(field, action, doms) => [doms[1]]}
          style={{ width: 500 }}
        >
          <Row gutter={8} key="id">
            <Col span={12}>
              <ProFormSelect
                key="id"
                showSearch
                placeholder="Select a supplier"
                request={async (params) => {
                  const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
                  if (res && res.data) {
                    const tmp = res.data.map((x: API.Item) => ({
                      label: `${x.id} - ${x.name}`,
                      value: x.id,
                    }));
                    return tmp;
                  }
                  return [];
                }}
                name="id"
                label="Supplier"
              />
            </Col>
            <Col span={12}>
              <ProFormText name={['pivot', 'product_no']} label="Product No" />
            </Col>
          </Row>
        </ProFormList>
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateAttributeForm;
