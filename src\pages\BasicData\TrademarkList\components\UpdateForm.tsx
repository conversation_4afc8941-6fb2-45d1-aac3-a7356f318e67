import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import { updateTrademark } from '@/services/foodstore-one/BasicData/trademark';
import SProFormDigit from '@/components/SProFormDigit';
import useTrademarkGroupOptions from '@/hooks/BasicData/useTrademarkGroupOptions';

const handleUpdate = async (fieldsParam: FormValueType) => {
  const hide = message.loading('Updating...', 0);
  const fields = { ...fieldsParam };
  console.log(fields, fields.producers);
  if (fields?.producers?.length) {
    // @ts-ignore
    fields.producers = fields.producers.map((x) => x.id);
  }

  try {
    console.log(fields);
    await updateTrademark({ ...fields, group_id: fields.group_id ?? null });
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = {} & Partial<API.Trademark>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Trademark>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Trademark) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  // Trademark group
  const { formElements } = useTrademarkGroupOptions(formRef);

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Trademark & Assign Producers'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      {formElements}
      <ProFormSelect
        name="producers"
        label="Producers"
        placeholder="Please select producers"
        mode="multiple"
        request={getProducerListSelectOptions}
      />
      <ProFormText width="md" name="name_pdf" label="Name in PDF" tooltip="Name in catalogue PDF" />
      <SProFormDigit width="xs" name="sort_pdf" label="sort in PDF" tooltip="Sort in catalogue PDF" />
    </ModalForm>
  );
};

export default UpdateForm;
