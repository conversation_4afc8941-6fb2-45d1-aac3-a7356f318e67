import { updateEanAttributePartial, type EanPriceRecordType } from '@/services/foodstore-one/Item/ean';
import { ProTable, type ActionType, type ProColumns } from '@ant-design/pro-table';
import type { CSSProperties, SetStateAction } from 'react';
import { useCallback, useMemo, useState, useRef, useEffect } from 'react';
import { calcCheapestXlsPrice } from '../EanAllPrices';
import Util, { nf2, ni, sn } from '@/util';
import { Row, Col, Popover, message, Button } from 'antd';
import SPrices from '@/components/SPrices';
import { useLocation, useModel } from 'umi';
import _ from 'lodash';
import { HighlightOutlined } from '@ant-design/icons';
import { EURO } from '@/constants';
import useModalNavigation from '../hooks/useModalNavigation';
import UpdatePriceAttributeForm from './UpdatePriceAttributeForm';

export type EanSummaryMiniTableProps = {
  itemEan?: EanPriceRecordType;
  reloadList?: () => void;
  xlsImports: API.Import[];
};

const EanSummaryMiniTable: React.FC<EanSummaryMiniTableProps> = (props) => {
  const { itemEan, reloadList, xlsImports } = props;

  const { appSettings } = useModel('app-settings');
  const { priceTypes } = appSettings;

  const actionRef = useRef<ActionType>();

  // dummy state
  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const { handleNavigation, handleUpdatePricesModalVisible, updatePricesModalVisible } = useModalNavigation([], {
    modals: ['price'],
    setCurrentRow: setCurrentRow,
  });

  const getCheapestXlsPrice = useCallback(
    (record: EanPriceRecordType) => {
      const arr = calcCheapestXlsPrice(record, xlsImports);
      return arr[0] ? arr[0] : 0;
    },
    [xlsImports],
  );

  const [columns, setColumns] = useState<ProColumns<EanPriceRecordType>[]>([]);

  const pricesColDefs = useMemo<ProColumns<EanPriceRecordType>[]>(() => {
    if (priceTypes.length) {
      const cols: ProColumns<EanPriceRecordType>[] = priceTypes
        // .filter((x) => x.id == 1)
        .map(
          (pt: any): ProColumns<EanPriceRecordType> => ({
            title: pt.name,
            dataIndex: ['ean_prices', pt?.id ?? 0],
            valueType: 'digit',
            sorter: false,
            align: 'center',
            width: 70,
            hideInSearch: true,
            className: 'cursor-pointer',
            tooltip: pt.id == 2 ? 'FS_ONE < GFC Price --> Red' : '',
            render: (dom, record) => {
              const vat = record.item?.vat?.value || 0;
              const priceSingle =
                Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0)) /
                (record?.attr_case_qty ? record?.attr_case_qty : 1);

              const supXlsFileId = 0; //sn(searchFormRef.current?.getFieldValue('supplierXlsFileId'));
              const gfcStyle: CSSProperties = {};

              // GFC styling
              if (record.idInXlsFile) {
                if (supXlsFileId && pt.id == 2) {
                  if (sn(priceSingle, 2) <= sn(record.priceInXlsFile, 2)) {
                    gfcStyle.color = 'red';
                  } else {
                    if ((sn(priceSingle, 2) < sn(sn(record.priceInXlsFile, 2) * 1.1), 2)) {
                      gfcStyle.color = 'orange';
                    }
                  }
                }
              }

              const cheapestXlsPrice = priceSingle ? getCheapestXlsPrice(record) : 0;

              return (
                <>
                  {cheapestXlsPrice > 0 && (
                    <div
                      className="text-sm c-grey italic"
                      style={{ position: 'absolute', top: 0, left: 0 }}
                      title={`${nf2((priceSingle * 100) / cheapestXlsPrice)}% | ${nf2(cheapestXlsPrice)}${EURO}`}
                    >
                      {ni((priceSingle * 100) / cheapestXlsPrice)}%
                    </div>
                  )}
                  <Row gutter={4}>
                    <Col span={24}>
                      <SPrices price={priceSingle} vat={vat} style={{ ...gfcStyle }} />
                    </Col>
                  </Row>
                </>
              );
            },
            onCell(record) {
              let cls = '';
              const unitPriceSingle = sn(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0), 2);
              const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

              if (pt.id == 1) {
                if (!(unitPriceSingle > 0)) cls += ' bg-lightgrey';
              } else {
                if (!(unitPriceGfc > 0)) cls += ' bg-lightgrey';
              }

              if (pt.id == 2) {
                if (unitPriceGfc > unitPriceSingle) {
                  cls += ' bg-light-red1';
                }
              }

              return {
                className: cls,
                onClick() {
                  handleUpdatePricesModalVisible(true);
                },
              };
            },
          }),
        );

      const multiPriceCol: ProColumns<EanPriceRecordType> = {
        title: 'FS_ONE Multi ',
        dataIndex: 'ean_prices_multi',
        valueType: 'digit',
        sorter: false,
        align: 'center',
        width: 70,
        hideInSearch: true,
        tooltip: 'FS_ONE Multi < GFC --> Green',
        render: (dom, record) => {
          // Get first sibling's unit price
          const sibEan = record.siblings_multi?.[0];
          if (!sibEan) return null;

          const vat = record.item?.vat?.value || 0;
          const caseQty = sn(sibEan?.attr_case_qty ? sibEan?.attr_case_qty : 1);
          const unitPriceSibling = caseQty
            ? sn(sn(_.get(_.find(sibEan?.ean_prices, { price_type_id: 1 }), 'price', 0)) / caseQty, 2)
            : 0;

          const cheapestXlsPrice = unitPriceSibling ? getCheapestXlsPrice(record) : 0;

          const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

          return (
            <>
              {cheapestXlsPrice > 0 && (
                <div
                  className="text-sm c-grey italic"
                  style={{ position: 'absolute', top: 0, left: 0 }}
                  title={`${nf2((unitPriceSibling * 100) / cheapestXlsPrice)}% | ${nf2(cheapestXlsPrice)}${EURO}`}
                >
                  {ni((unitPriceSibling * 100) / cheapestXlsPrice)}%
                </div>
              )}
              <Row gutter={4}>
                <Col span={24}>
                  <SPrices price={unitPriceSibling} vat={vat} />
                </Col>
              </Row>
              {sn(record.siblings_multi?.length) > 1 && (
                <div
                  className="text-sm c-grey"
                  style={{ position: 'absolute', top: -5, right: 0 }}
                  title={`${record.siblings_multi?.length} multies.`}
                >
                  {record.siblings_multi?.length}
                </div>
              )}
              {unitPriceSibling > 0 && (!unitPriceGfc || unitPriceGfc > unitPriceSibling) && (
                <div className="" style={{ position: 'absolute', bottom: 0, left: 0 }}>
                  <Button
                    type="link"
                    size="small"
                    icon={<HighlightOutlined />}
                    title={`Set price to GFC on all family EANs`}
                    onClick={() => {
                      const hide = message.loading('Updating GFC price...', 0);
                      updateEanAttributePartial({
                        mode: 'price',
                        id: record.id,
                        price_type_id: 2,
                        price: unitPriceSibling,
                        // all_family: true,
                      } as any)
                        .then((res) => {
                          message.success('Updated successfully.');
                          actionRef.current?.reload();
                        })
                        .catch(Util.error)
                        .finally(hide);
                    }}
                  />
                </div>
              )}
            </>
          );
        },
        onCell: (record) => {
          const attr: React.HTMLAttributes<EanPriceRecordType> = {};

          let cls = '';

          // Get first sibling's unit price
          const sibEan = record.siblings_multi?.[0];
          if (!sibEan) return attr;

          const caseQty = sn(sibEan?.attr_case_qty ? sibEan?.attr_case_qty : 1);
          const unitPriceSibling = caseQty
            ? sn(sn(_.get(_.find(sibEan?.ean_prices, { price_type_id: 1 }), 'price', 0)) / caseQty, 2)
            : 0;
          const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

          if (!(unitPriceSibling > 0)) cls += ' bg-lightgrey';

          if (unitPriceGfc && unitPriceSibling && unitPriceGfc > unitPriceSibling) {
            cls += ' bg-green3';
          }

          attr.className = cls;
          return attr;
        },
      };

      cols.splice(1, 0, multiPriceCol);
      return cols;
    } else return [];
  }, [getCheapestXlsPrice, handleUpdatePricesModalVisible, priceTypes]);

  const orgColumns: ProColumns<EanPriceRecordType>[] = useMemo(() => {
    return [
      {
        title: 'Latest BP',
        dataIndex: ['latest_ibo', 'price'],
        width: 60,
        align: 'center',
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.latest_ibo, prevRecord.latest_ibo),
        tooltip: 'Click to view history. Lates BP > XLS Price: Red, Lates BP < XLS Price: Green',
        render: (__, record) => {
          const vat = record.item?.vat?.value || 0;

          const priceStyle: CSSProperties = {};
          const priceXls = sn(record.priceInXlsFile, 2);
          const latestIboPrice = sn(record?.latest_ibo?.price, 2);

          let priceColTooltip: string = '';
          if (record.idInXlsFile) {
            if (priceXls > latestIboPrice) {
              priceStyle.color = 'red';
            } else {
              if (priceXls < latestIboPrice) {
                priceStyle.color = 'green';
              }
            }

            priceColTooltip = `EAN pcs price: ${nf2(priceXls)}${EURO}`;
          }

          return (
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                // setCurrentRow({ ...record });
                // setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={24} title={priceColTooltip}>
                <SPrices price={latestIboPrice} vat={vat} style={priceStyle} />
              </Col>
            </Row>
          );
        },
      },
      {
        title: 'Avg. Exp. Days',
        dataIndex: ['avg_exp_days'],
        width: 60,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return ni(record.avg_exp_days);
        },
      },
      {
        title: 'Last Sale (pcs)',
        dataIndex: ['last30_sales_qty'],
        width: 140,
        align: 'center',
        hideInSearch: true,
        tooltip: (
          <table style={{ textAlign: 'center' }} className="text-sm">
            <tr>
              <td>Sold pcs last 30 /365 days</td>
              <td>AVG Net Price Last 30 days</td>
            </tr>
            <tr>
              <td>
                <b>Stock Available Qty + Stock blocked Qty - processing orders Qty</b>
              </td>
              <td>Discount %</td>
            </tr>
            <tr>
              <td colSpan={2} className="text-sm">
                Stock qty includes available and blocked ones
              </td>
            </tr>
          </table>
        ),
        render(dom, record) {
          const newStockQty = sn(record?.stock_mix_qty) + sn(record?.stock_mix_qty_b) - sn(record.processing_qty);
          const last30_sales_qty = sn(record.last30_sales_qty);
          const last30AvgPrice = last30_sales_qty ? sn(record.last30_cturover) / last30_sales_qty : 0;

          const last365_sales_qty = sn(record.last365_sales_qty);
          const last365AvgPrice = last365_sales_qty ? sn(record.last365_cturover) / last365_sales_qty : 0;

          // styles
          let clsStock = '';
          if (newStockQty && newStockQty < last30_sales_qty) {
            clsStock += ' bg-light-orange1';
          }
          const avgExpDays = sn(record.avg_exp_days);

          let clsLast30Sales = '';
          if (avgExpDays && avgExpDays < 90) {
            if ((newStockQty / last30_sales_qty) * 30 < avgExpDays - 30) {
              clsLast30Sales += ' c-orange';
            }
          }

          return (
            <div style={{ textAlign: 'right' }}>
              <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
                <Col
                  span={14}
                  className="text-right cursor-pointer"
                  title="Show weekly stats in 365days."
                  onClick={() => {
                    // setOpenWeeklyStatsModal(true);
                  }}
                >
                  {!!last365_sales_qty || !!last30_sales_qty ? (
                    <>
                      <span className={clsLast30Sales}>{ni(record.last30_sales_qty, !!last365_sales_qty)}</span> /{' '}
                      <span>{ni(record.last365_sales_qty)}</span>
                    </>
                  ) : null}
                </Col>
                {last30AvgPrice || last365AvgPrice ? (
                  <Col span={10} title={`Net Turnover last 30 days: ${nf2(record.last30_cturover)}${EURO}`}>
                    <Popover
                      content={
                        <table style={{ textAlign: 'left' }} className="text-sm">
                          <tr>
                            <td style={{ width: 80 }}>AVG (30)</td>
                            <td>
                              {last30AvgPrice ? (
                                <SPrices
                                  price={last30AvgPrice}
                                  vat={record.item?.vat?.value}
                                  direction="horizontal"
                                  showZero
                                  showCurrency
                                  noTextSmallCls
                                />
                              ) : null}
                            </td>
                            <td style={{ paddingLeft: 16 }}>
                              Avg. GP: {nf2(record.gp_single_gp_avg_30, true)} / {nf2(record.gp_multi_gp_avg_30, true)}
                            </td>
                          </tr>
                          <tr>
                            <td>AVG (365)</td>
                            <td className="text-right">
                              {last365AvgPrice ? (
                                <SPrices
                                  price={last365AvgPrice}
                                  vat={record.item?.vat?.value}
                                  direction="horizontal"
                                  showZero
                                  showCurrency
                                  noTextSmallCls
                                />
                              ) : null}
                            </td>
                            <td style={{ paddingLeft: 12 }}>
                              Avg. GP: {nf2(record.gp_single_gp_avg_365, true)} /{' '}
                              {nf2(record.gp_multi_gp_avg_365, true)}
                            </td>
                          </tr>
                        </table>
                      }
                      trigger="hover"
                    >
                      &nbsp;
                      <SPrices
                        price={last30AvgPrice != 0 ? last30AvgPrice : last365AvgPrice}
                        vat={record.item?.vat?.value}
                        direction="horizontal"
                        showZero
                        showCurrency
                        noTextSmallCls
                        hideGross
                      />
                    </Popover>
                  </Col>
                ) : null}
              </Row>
              <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
                <Col
                  span={14}
                  title={`${ni(record?.stock_mix_qty, true)} + ${ni(record?.stock_mix_qty_b, true)} - ${ni(
                    record.processing_qty,
                    true,
                  )} = ${ni(newStockQty, true)}`}
                  style={{ fontWeight: 'bold', textAlign: 'left' }}
                  className={`${clsStock}`}
                >
                  {ni(newStockQty)}
                </Col>
                <Col span={10} className={record.fs_special_discount ? 'c-red' : ''}>
                  {record.fs_special_discount}
                </Col>
              </Row>
            </div>
          );
        },
      },
      ...pricesColDefs,
      {
        title: `AVG GPs`,
        dataIndex: ['last_avg_gps'],
        width: 100,
        align: 'center',
        className: 'bl2',
        hideInSearch: true,
        tooltip: (
          <table className="text-sm">
            <tr>
              <td>Single</td>
              <td>Multi</td>
            </tr>
            <tr>
              <td>Avg. GP for last 30 days</td>
              <td>Avg. GP for last 30 days</td>
            </tr>
            <tr>
              <td>Avg. GP for last 365 days</td>
              <td>Avg. GP for last 365 days</td>
            </tr>
          </table>
        ),
        render(__, entity) {
          return (
            <>
              <Row style={{ textAlign: 'right', minHeight: 18 }}>
                <Col span={12} style={{ fontSize: 11 }}>
                  {entity.gp_single_gp_avg_30 ? nf2(entity.gp_single_gp_avg_30) : null}
                </Col>
                {/* <Col span={6} className="text-sm c-grey align-middle">
                  {ni(entity.gp_single_qty_ordered_sum_30)}
                </Col> */}
                <Col span={12} style={{ fontSize: 11 }}>
                  {entity.gp_multi_gp_avg_30 ? nf2(entity.gp_multi_gp_avg_30) : null}
                </Col>
                {/* <Col span={6} className="text-sm c-grey align-middle">
                  {ni(entity.gp_multi_qty_ordered_sum_30)}
                </Col> */}
              </Row>
              <Row style={{ textAlign: 'right', minHeight: 18, borderTop: '1px solid #eee', lineHeight: '18px' }}>
                <Col span={12} style={{ fontSize: 11, color: '#999' }}>
                  {entity.gp_single_gp_avg_365 ? nf2(entity.gp_single_gp_avg_365) : null}
                </Col>
                {/* <Col span={6} className="text-sm c-grey align-middle">
                  {ni(entity.gp_single_qty_ordered_sum_365)}
                </Col> */}
                <Col span={12} style={{ fontSize: 11, color: '#999' }}>
                  {entity.gp_multi_gp_avg_365 ? nf2(entity.gp_multi_gp_avg_365) : null}
                </Col>
                {/* <Col span={6} className="text-sm c-grey align-middle">
                  {entity.gp_multi_qty_ordered_sum_365 ? ni(entity.gp_multi_qty_ordered_sum_365) : null}
                </Col> */}
              </Row>
            </>
          );
        },
      },
    ];
  }, [pricesColDefs]);

  useEffect(() => {
    setColumns([...orgColumns]);
  }, [orgColumns]);

  return (
    <>
      <ProTable<EanPriceRecordType, API.PageParams>
        headerTitle={'EAN Prices List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={false}
        size="small"
        bordered
        columnEmptyText=""
        dataSource={[itemEan || {}]}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: 5,
        }}
        rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        columns={columns}
        tableAlertRender={false}
        cardProps={{ bodyStyle: { padding: 0 } }}
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={itemEan}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          reloadList?.();
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          reloadList?.();
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />
    </>
  );
};

export default EanSummaryMiniTable;
