import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';

import Util, { nf3, ni, sn, sShortImportDbTableName } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSwitch, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Card, Space, message } from 'antd';
import { useRef, useState } from 'react';
import { IRouteComponentProps } from 'umi';

import { getEanPriceStableList } from '@/services/foodstore-one/Import/ean-price-stable';
import SkuComp from '@/components/SkuComp';
import { updateEanPriceStable } from '@/services/foodstore-one/Import/import';
import { CloseCircleOutlined, ReconciliationOutlined } from '@ant-design/icons';

export type RecordType = Partial<API.EanPriceStable>;
export type SearchFormValueType = Partial<API.EanPriceStable>;

export type EanPriceStableListPageProps = {
  refreshTick?: number;
  filterType?: 'light' | 'query';
} & IRouteComponentProps;

const EanPriceStableListPage: React.FC<EanPriceStableListPageProps> = (props) => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'EAN',
      dataIndex: ['ean'],
      width: 200,
      copyable: true,
      sorter: true,
    },
    {
      title: 'Import ID',
      dataIndex: ['cur_import'],
      width: 150,
      copyable: true,
      render(dom, entity) {
        return sShortImportDbTableName(entity.cur_import?.table_name);
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['cur_import', 'supplier', 'name'],
      width: 100,
    },
    {
      title: 'SUPPLIER_ADD',
      dataIndex: ['cur_import', 'supplier_add'],
      width: 100,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      width: 100,
      ellipsis: true,
      copyable: true,
      render(__, entity) {
        return entity?.item_ean ? <SkuComp sku={entity?.item_ean?.sku} /> : null;
      },
    },
    {
      title: 'Item Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      width: 300,
      ellipsis: true,
    },
    {
      title: 'Cur. Price',
      dataIndex: ['cur_price'],
      width: 80,
      align: 'right',
      render(dom, entity) {
        return nf3(entity.cur_price, false, true);
      },
    },
    {
      title: 'Last. Price',
      dataIndex: ['last_price'],
      width: 80,
      align: 'right',
      render(dom, entity) {
        return nf3(entity.last_price, false, true);
      },
    },
    {
      title: 'Qty / Pkg',
      dataIndex: ['case_qty'],
      width: 80,
      align: 'right',
      render(__, entity) {
        return ni(entity.case_qty);
      },
    },
    {
      title: 'Pkg / Pallet',
      dataIndex: ['ve_pallet'],
      width: 80,
      align: 'right',
      render(__, entity) {
        return ni(entity.ve_pallet);
      },
    },

    {
      title: 'Shelflife',
      dataIndex: ['shelf_life'],
      width: 80,
      align: 'right',
      render(__, entity) {
        return ni(entity.shelf_life);
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      width: 100,
      align: 'center',
      render(__, entity) {
        return Util.dtToDMY(entity.exp_date);
      },
    },
    {
      title: 'HS Code',
      dataIndex: ['hs_code'],
      width: 90,
      render(__, entity) {
        return entity.hs_code;
      },
    },
    {
      title: 'Deleted?',
      dataIndex: ['is_deleted'],
      width: 50,
      align: 'center',
      render(__, entity) {
        return entity.is_deleted ? <CloseCircleOutlined style={{ color: 'red' }} /> : null;
      },
    },
    {
      title: 'Updated On',
      dataIndex: ['updated_on'],
      width: 100,
      align: 'center',
      sorter: true,
      render(__, entity) {
        return Util.dtToDMYHHMM(entity.updated_on);
      },
    },

    /* {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({
              ...record,
            });
          }}
        >
          Edit
        </a>,
      ],
    }, */
  ];

  return (
    <PageContainer
      title={
        <Space size={24}>
          <div>{props.route.name}</div>
        </Space>
      }
    >
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ean_price_stable', {
            ean_search_mode: 'contain_siblings',
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          {/* <ProFormSelect
            name={'ibo_pre_management_id'}
            showSearch
            label="Pre Order"
            width={400}
            options={iboPreManagementOptions}
            request={searchIboPreManagementOptions}
            fieldProps={{
              filterOption: (inputValue: string, option?: any) => true,
              loading: loadingIboPreManagement,
              dropdownMatchSelectWidth: false,
              onChange(value, option) {
                setCurrentIboPreManagement(option as API.IboPreManagement);
                actionRef.current?.reload();
              },
            }}
          /> */}
          <ProFormText name={'ean'} label="EAN" width={150} placeholder={'EAN'} />
          <ProFormText name={'sku'} label="SKU" width={120} placeholder={'SKU'} />
          <ProFormText name={'supplier_add'} label="SUPPLIER_ADD" width={180} placeholder={'SUPPLIER_ADD'} />
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          <ProFormSwitch
            name={'only_existing_ean'}
            label="Only existing EAN"
            initialValue={true}
            fieldProps={{
              onChange: () => {
                searchFormRef.current?.setFieldsValue({ only_not_existing_ean: false });
                actionRef.current?.reload();
              },
            }}
          />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        style={{ marginTop: 20 }}
        headerTitle="EAN Price Stable"
        actionRef={actionRef}
        rowKey="id"
        bordered
        revalidateOnFocus={false}
        sticky
        scroll={{ x: 800 }}
        size="small"
        onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_price_stable_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
        options={{ fullScreen: true }}
        search={false}
        request={(params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_price_stable', searchValues);
          Util.setSfValues('sf_ean_price_stable_p', params);

          const newParams = {
            ...params,
            ...searchValues,
            with: 'itemEan,curImport',
          };

          return getEanPriceStableList(newParams, { ...newSort }, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        toolBarRender={() => [
          <Button
            key="esp-update"
            type="primary"
            icon={<ReconciliationOutlined />}
            style={{ marginRight: 24 }}
            className="btn-green"
            title="Update ean price stable data of EANs from the active imported data."
            onClick={() => {
              const hide = message.loading('Updating the data...', 0);
              updateEanPriceStable()
                .then((res) => {
                  message.success('Updated successfully.');
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          >
            Update Ean Price Stable
          </Button>,
        ]}
        rowSelection={{
          selectedRowKeys: selectedRowsState.map((x) => sn(x.id)),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
        tableAlertRender={false}
      />
      {/* <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={async (value) => {
          handleCreateModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      /> */}
    </PageContainer>
  );
};

export default EanPriceStableListPage;
