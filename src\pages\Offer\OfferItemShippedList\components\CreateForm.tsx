import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDatePicker, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addOfferItemShipped } from '@/services/foodstore-one/Offer/offer-item-shipped';
import Util, { sn } from '@/util';
import SkuComp from '@/components/SkuComp';
import SProFormDigit from '@/components/SProFormDigit';
import SDatePicker from '@/components/SDatePicker';

export type FormValueType = Partial<API.OfferItemShipped>;

const handleCreate = async (fields: FormValueType) => {
  const hide = message.loading('Creating...', 0);

  try {
    await addOfferItemShipped(fields);
    hide();
    message.success('Created successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type CreateFormProps = {
  initialValues?: Partial<API.OfferItemIboPrePackReadyMap>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItemShipped) => void;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      const newValues: API.OfferItemShipped = { ...(initialValues || {}) };
      newValues.wa_date = new Date().toISOString().slice(0, 10);
      formRef.current?.setFieldsValue(newValues);
    }
  }, [initialValues, modalVisible]);

  return (
    <ModalForm<API.OfferItemShipped>
      title={
        <Space size={24}>
          <div>Create a Shipment</div>
          <div>
            <SkuComp sku={initialValues?.item_ean?.sku} />
          </div>
          <Typography.Paragraph
            copyable={{
              text: initialValues?.item_ean?.ean || '',
              tooltips: 'Copy EAN ' + (initialValues?.item_ean?.ean || ''),
            }}
            style={{ marginBottom: 0 }}
          >
            {initialValues?.item_ean?.ean || ''}
          </Typography.Paragraph>
        </Space>
      }
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          offer_item_id: initialValues?.offer_item_id,
          ibo_pre_id: initialValues?.ibo_pre_id,
          ean_id: initialValues?.ean_id,
          exp_date: initialValues?.exp_date,
          ...value,
        };
        const success = await handleCreate(data);

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProForm.Item name="case_qty" label="Case Qty">
        {initialValues?.case_qty}
      </ProForm.Item>

      <SProFormDigit name="qty" label="Shipped Qty" width="xs" />

      <ProFormText name="wa_no" label="WA No" width="xs" />

      <SDatePicker name="wa_date" label="WA Date" />

      <ProFormTextArea name="note" label="Comment" fieldProps={{ rows: 5 }} />
    </ModalForm>
  );
};

export default CreateForm;
