CREATE TABLE `offer_item_delivered`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `offer_item_id` bigint(20) unsigned DEFAULT NULL,
    `item_id`       bigint(20) unsigned DEFAULT NULL,
    `ean_id`        bigint(20) unsigned DEFAULT NULL,
    `case_qty`      int(11)             DEFAULT NULL,
    `qty`           int(11)             DEFAULT NULL,
    `exp_date`      date                DEFAULT NULL,
    `created_on`    datetime            DEFAULT NULL,
    `created_by`    int(11)             DEFAULT NULL,
    `updated_on`    datetime            DEFAULT NULL,
    `updated_by`    int(11)             DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `FK_offer_item_delivered_offer_item_id` (`offer_item_id`),
    KEY `FK_offer_item_delivered_item_id` (`item_id`),
    KEY `FK_offer_item_delivered_ean_id` (`ean_id`),
    CONSTRAINT `FK_offer_item_delivered_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_delivered_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_delivered_offer_item_id` FOREIGN KEY (`offer_item_id`) REFERENCES `offer_item` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

