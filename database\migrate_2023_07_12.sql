CREATE TABLE `import_supplier_ean`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `ean`           varchar(100) NOT NULL COMMENT 'Single EAN',
    `multi_ean`     varchar(100) NOT NULL COMMENT 'Multi EAN',
    `case_qty`      int(11)      DEFAULT NULL COMMENT 'Case Qty',
    `price`         double       DEFAULT NULL COMMENT 'Price',
    `name`          varchar(255) DEFAULT NULL COMMENT 'EAN Name',
    `import_id`     bigint(20)   DEFAULT NULL COMMENT 'Ref table ID: PK in import table',
    `import_ref_id` bigint(20)   DEFAULT NULL COMMENT 'PK in ref_table',
    PRIMARY KEY (`id`),
    KEY `UQ_import_supplier_ean_both` (`ean`, `multi_ean`),
    KEY `UQ_import_supplier_ean_multi_ean` (`multi_ean`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='Imported EANs.\r\nRepresent the relations between single EAN & Multi-EANs in Import EANs. This relationship will be used in IBO Register.'
;
