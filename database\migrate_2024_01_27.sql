CREATE TABLE `xmag_quote`
(
    `id`                    int(11) NOT NULL,
    `store_id`              int(11)      DEFAULT NULL,
    `status`                int(11)      DEFAULT NULL,
    `customer_firstname`    varchar(255) DEFAULT NULL COMMENT 'Customer First Name',
    `customer_lastname`     varchar(255) DEFAULT NULL COMMENT 'Customer Last Name',
    `customer_email`        varchar(255) DEFAULT NULL COMMENT 'Customer Email',
    `quote_customer_note`   text         DEFAULT NULL,
    `customer_is_guest`     smallint(6)  DEFAULT NULL,
    `customer_note_notify`  smallint(6)  DEFAULT NULL,
    `customer_tax_class_id` int(11)      DEFAULT NULL,
    `items_count`           int(11)      DEFAULT NULL,
    `items_qty`             int(11)      DEFAULT NULL,
    `orig_order_id`         int(11)      DEFAULT NULL,
    `is_virtual`            smallint(6)  DEFAULT NULL,
    `is_active`             smallint(6)  DEFAULT NULL,
    `shipping_configure`    smallint(6)  DEFAULT NULL,
    `created_at`            datetime     DEFAULT NULL,
    `updated_at`            datetime     DEFAULT NULL,
    `currency`              text         DEFAULT NULL,
    `customer`              text         DEFAULT NULL,
    `billing_address`       text         DEFAULT NULL,
    `extension_attributes`  longtext     DEFAULT NULL,

    PRIMARY KEY (`id`),
    FULLTEXT KEY `IDX_xmag_quote_name_address` (`customer_firstname`, `customer_lastname`, `quote_customer_note`),
    KEY `IDX_xmag_quote_updated_at` (`updated_at`),
    KEY `IDX_xmag_quote_store_id` (`store_id`),

    CONSTRAINT `FK_IDX_xmag_quote_store_id` FOREIGN KEY (`store_id`) REFERENCES `xmag_store_config` (`id`) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `xmag_quote_item`
(
    `item_id`      int(11) NOT NULL,
    `quote_id`     int(11) NOT NULL,
    `sku`          varchar(50)    DEFAULT NULL,
    `name`         varchar(255)   DEFAULT NULL,
    `qty`          int(11)        DEFAULT NULL,
    `price`        decimal(20, 4) DEFAULT NULL,
    `product_type` varchar(255)   default null,

    PRIMARY KEY (`item_id`),
    KEY `IDX_xmag_quote_item_quote_id` (`quote_id`),
    KEY `IDX_xmag_quote_item_sku` (`sku`),

    CONSTRAINT `FK_IDX_xmag_quote_item_quote_id` FOREIGN KEY (`quote_id`) REFERENCES `xmag_quote` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;