export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './user/Login',
      },
    ],
  },
  {
    path: '/welcome',
    name: 'Dashboard',
    icon: 'dashboard',
    component: './Welcome',
    access: 'user',
  },
  {
    path: '/ibo',
    name: 'I<PERSON>',
    icon: 'ShopOutlined',
    // layout: false,
    // component: '../layouts/BasicLayout',
    routes: [
      {
        path: '/ibo/ibo-management',
        name: 'Overview',
        // icon: 'tab',
        component: './IBO/IBOManagement',
        parentKeys: ['/ibo'],
        access: 'user',
      },
      {
        path: '/ibo/ibo-register',
        name: 'IBO Register',
        icon: 'FormOutlined',
        component: './IBO/IBORegister',
        parentKeys: ['/ibo'],
        access: 'roleRouteFilter',
      },
      {
        path: '/ibo/pre-ibo-register',
        name: 'Wholesale (Pre IBO)',
        icon: 'FormOutlined',
        component: './IBO/IboPreRegister',
        access: 'roleRouteFilter',
        hideInMenu: true,
      },
      /* {
        path: '/ibo/ibo-register-wholesale-mobile',
        name: 'Wholesale (Mobile)',
        icon: 'FormOutlined',
        component: './IBO/IboRegisterWholeSaleMobile',
        access: 'roleRouteFilter',
        hideInMenu: true,
      },
       */

      {
        path: '/ibo/ibo-register-mobile',
        name: 'IBO Register (Mobile)',
        icon: 'FormOutlined',
        component: './IBO/IBORegister/IBORegisterMobile',
        access: 'roleRouteFilter',
      },
      {
        path: '/ibo/item-buying-order',
        name: 'Item Buying Orders',
        icon: 'table',
        component: './IBO/IBOList',
        access: 'user',
      },
      {
        path: '/ibo/ibo-compare',
        name: 'Pre Orders & IBOM Compare',
        component: './IBO/IboCompare',
        access: 'user',
      },
      /* { // Bug page and deprecated
        path: '/ibo/ibo-draft',
        name: 'IBO Register',
        icon: 'tab',
        component: './IBO/IboDraft',
      }, */
      {
        path: '/ibo/ibo-pre',
        name: 'Pre Orders',
        // icon: 'tab',
        component: './IBO/IboPreList',
        access: 'user',
      },
      {
        path: '/ibo/ibo-pre-management',
        name: 'Pre Order Overview',
        component: './IBO/IboPreManagementList',
        access: 'user',
      },

    ],
  },

  {
    path: '/quotes',
    name: 'Quotes',
    icon: 'ShoppingCartOutlined',
    routes: [
      {
        path: '/quotes/offer',
        name: 'Offers',
        icon: 'table',
        component: './Offer/OfferList',
        access: 'roleRouteFilter',
      },
      {
        path: '/quotes/offer-matrix',
        name: 'Offer Matrix',
        icon: 'table',
        component: './Offer/OfferMatrix',
        access: 'user',
      },
      {
        path: '/quotes/offer-item',
        name: 'Offer Items',
        icon: 'table',
        component: './Offer/OfferItemList',
        access: 'roleRouteFilter',
      }, {
        path: '/quotes/offer-item-mobile',
        name: 'Offer Items (Mobile)',
        icon: 'table',
        component: './Offer/OfferItemList/OfferItemListMobile',
        access: 'roleRouteFilter',
      },
      {
        path: '/quotes/master',
        name: 'Quotes',
        icon: 'ShoppingCartOutlined',
        component: './Magento/Quote',
        access: 'roleRouteFilter',
      },


      /* Don't use this anymore.
      {
        path: '/quotes/offer-supplier-data-matrix',
        name: 'Offer x Supplier Data Matrix',
        icon: 'table',
        hideInMenu: true,
        component: './Offer/Offer2SupplierDataMatrix',
        access: 'user',
      }, */

    ],
  },
  {
    path: '/item',
    name: 'Item',
    icon: 'BarcodeOutlined',
    routes: [
      {
        path: '/item/ean-all-summary',
        name: 'EAN ALL',
        icon: 'BarcodeOutlined',
        component: './Item/EanList/EanAllSummary',
        access: 'user',
      },
      {
        path: '/item/ean-all-prices',
        name: 'EAN Prices',
        icon: 'EuroCircleOutlined',
        component: './Item/EanList/EanAllPrices',
        access: 'user',
      },
      {
        path: '/item/ean-all-special',
        name: 'EAN Specials/Labels',
        icon: 'FireOutlined',
        component: './Item/EanList/EanAllSpecial',
        access: 'user',
      },
      {
        path: '/item/ean-all-bio',
        name: 'EAN BIO',
        icon: 'table',
        component: './Item/EanList/EanAllBio',
        access: 'user',
      },

      {
        path: '/item/list',
        name: 'Item',
        icon: 'table',
        component: './Item/ItemList',
        access: 'user',
      },
      {
        path: '/item/category',
        name: 'Item Category',
        icon: 'GroupOutlined',
        component: './Item/CategoryList',
        access: 'user',
      }, {
        path: '/item/ean-all-files',
        name: 'Item All Files',
        icon: 'FileImageOutlined',
        component: './Item/EanList/ItemAllFiles',
        access: 'roleRouteFilter',
      },
      {
        path: '/item/ean-all-gdsn',
        name: 'EAN GDSN',
        icon: 'BarcodeOutlined',
        hideInMenu: true,
        component: './Item/EanList/EanAllGdsn',
        access: 'user',
      },
      {
        path: '/item/ean-detail',
        name: 'Ean WebText',
        icon: 'FormOutlined',
        component: './Item/EanList/EanDetail',
        access: 'roleRouteFilter',
      },
      /* {
        path: '/item/ean-all-edit',
        name: 'EAN Edit',
        icon: 'BarcodeOutlined',
        component: './Item/EanList/EanAllEdit',
      }, */
      {
        path: '/item/ean-all-pic',
        name: 'EAN Picture',
        icon: 'BarcodeOutlined',
        hideInMenu: true,
        component: './Item/EanList/EanAllPic',
        access: 'user',
      },
      /* {
        path: '/item/ean-all-prices',
        name: 'EAN Prices',
        icon: 'EuroCircleOutlined',
        hideInMenu: true,
        component: './Item/EanList/EanAllPrices',
        access: 'user',
      }, */
      {
        path: '/item/ean-all-summary-todo',
        name: 'Todo',
        icon: 'CheckSquareOutlined',
        component: './Item/EanList/EanAllSummaryTodo',
        access: 'user',
      },

      {
        path: '/item/gdsn/message-items',
        name: 'GDSN Items',
        icon: 'BarcodeOutlined',
        component: '@/pages/Gdsn/MessageItemListPage',
        access: 'user',
      },
      {
        path: '/item/gdsn/message-provider',
        name: 'GDSN Provider',
        icon: 'BarcodeOutlined',
        component: '@/pages/Gdsn/MessageOutListPage',
        hideInMenu: true,
        access: 'user',
      },
      {
        path: '/item/stocklot-products',
        name: 'Stocklot Page',
        component: './Mop/MopProductPage',
        access: 'roleRouteFilter',
      },
      /* {
        path: '/item/ean-all',
        name: 'EAN (All)',
        icon: 'BarcodeOutlined',
        component: './Item/EanList/eanAll',
      },
      {
        path: '/item/ean',
        name: 'EAN (Single)',
        icon: 'BarcodeOutlined',
        component: './Item/EanList',
      },
      {
        path: '/item/ean-ve',
        name: 'EAN (Multi)',
        icon: 'BarcodeOutlined',
        component: './Item/EanList/eanVE',
      }, */
    ],
  },
  {
    path: '/stock',
    name: 'Stock',
    icon: 'ContainerOutlined',
    access: 'user',
    routes: [
      {
        path: '/stock/stock-warehouse',
        name: 'Stock Warehouse',
        component: './Stock/StockStable',
      },
      {
        path: '/stock/stock-warehouse-mobile',
        name: 'Stock Warehouse (Mobile)',
        component: './Stock/StockStable/StockStableListMobile',
      },
      {
        path: '/stock/stock-movement',
        name: 'Stock Movements',
        component: './Stock/StockMovement',
      },
      {
        path: '/stock/stock-problem',
        name: 'Stock Problems',
        component: './Stock/StockStableProblem',
      },
      {
        path: '/stock/stock-compare',
        name: 'Live Stock Compare',
        component: './Stock/StockCompare',
      },

    ],
  },
  {
    path: '/orders',
    name: 'Orders',
    icon: 'ShoppingCartOutlined',
    routes: [
      {
        path: '/orders/order-detail',
        name: 'Order Detail',
        component: './Magento/Order/OrderDetail',
        access: 'roleRouteFilter',
      },
      {
        path: '/orders/order-detail-packing',
        name: 'Order Detail (WH)',
        component: './Magento/Order/OrderDetailPacking',
        access: 'roleRouteFilter',
      },
      {
        path: '/orders/master',
        name: 'Orders',
        icon: 'ShoppingCartOutlined',
        component: './Magento/Order',
        access: 'user',
      },
      {
        path: '/orders/picklist',
        name: 'Picklist Details',
        component: './Warehouse/PicklistDetail',
        access: 'user',
      },
      {
        path: '/orders/picklist-summary',
        name: 'Picklists Summary',
        component: './Warehouse/Picklist',
        access: 'roleRouteFilter',
      },
      {
        path: '/orders/picklist-ean-check',
        name: 'Picklist EAN Check',
        icon: 'table',
        component: './Warehouse/PicklistCheck',
        access: 'user',
      },
      {
        path: '/orders/shipping-report',
        name: 'Shipping Report',
        icon: 'table',
        component: './Report/OrderShipping/OrderShippingReport',
        access: 'roleRouteFilter',
      },
      {
        path: '/orders/create-shipping-label',
        name: 'Create Shipping Label',
        icon: 'FormOutlined',
        component: './Magento/Order/CreateShippingLabel',
        access: 'roleRouteFilter',
      },

      {
        path: '/orders/parcels',
        name: 'Order Parcels',
        icon: 'table',
        component: './Magento/OrderParcel',
        access: 'roleRouteFilter',
      },
    ],
  },
  {
    path: '/report',
    name: 'Statistics',
    icon: 'table',
    access: 'user',
    routes: [
      {
        path: '/report/order/sales',
        name: 'Sales Report',
        component: './Report/Order/OrderTrademarkProducerList',
      },
      {
        path: '/report/order/sales-stat',
        name: 'Sales Statistics',
        icon: 'BarChartOutlined',
        component: './Report/Order/SalesStat',
      },
      {
        path: '/report/order/sales-stat-order-list',
        name: 'Sales (Order List)',
        icon: 'BarChartOutlined',
        component: './Report/Order/SalesStatOrderList',
      },

      {
        path: '/report/order/weekly-sales-stat',
        name: 'Weekly Sales Statistics',
        icon: 'BarChartOutlined',
        component: './Report/Order/SalesStatWeekly',
      },
      {
        path: '/report/order/monthly-sales-stat',
        name: 'Monthly Sales Statistics',
        icon: 'BarChartOutlined',
        component: './Report/Order/SalesStatTotalMonthly',
      },
      {
        path: '/report/google-analytics',
        name: 'Reports - GA',
        icon: 'GoogleOutlined',
        component: './Audit/GoogleAnalytics',
      },
      {
        path: '/report/ean-tasks',
        name: 'EAN Tasks List',
        icon: 'table',
        component: './Item/EanTaskList/EanTaskListPage',
      },
      {
        path: '/report/picking-time',
        name: 'Picking Time ',
        icon: 'table',
        component: './Report/PicklistTimeTrack',
      },
      {
        path: '/report/pick-value',
        name: 'Pick-Value ',
        icon: 'table',
        component: './Report/PickValue',
      },
      {
        path: '/report/order/sales-shipping-stat',
        name: 'Shipping Statistics',
        icon: 'BarChartOutlined',
        component: './Report/Order/SalesShippingStats',
      },
    ],
  },
  {
    path: '/import',
    name: 'Import',
    icon: 'ImportOutlined',
    access: 'user',
    routes: [
      {
        path: '/import/supplier-data',
        name: 'Supplier Data',
        component: './Import/SupplierData',
      },
      {
        path: '/import/compare-supplier-data',
        name: 'Supplier Data Compare',
        icon: 'table',
        component: './Import/SupplierDataCompare',
      },
      {
        path: '/import/import-ean-disabled',
        name: 'Disabled EANs',
        icon: 'table',
        component: './Import/ImportEanDisabled',
      },
      {
        path: '/import/ean-price-stable',
        name: 'EAN Price Stable',
        icon: 'EuroCircleOutlined',
        component: './Import/EanPriceStable',
      },


    ],
  },
  {
    path: '/email',
    name: 'Email',
    icon: 'MailOutlined',
    access: 'user',
    routes: [
      {
        path: '/email/list',
        name: 'Emails',
        icon: 'MailOutlined',
        component: './Email/EmailList',
      },
      {
        path: '/email/accounts-setting',
        name: 'IMAP Accounts',
        icon: 'SettingOutlined',
        access: 'canAdmin',
        component: './Email/EmailAccountList',
      },
      {
        path: '/email/server',
        name: 'Email Servers',
        icon: 'CloudServerOutlined',
        access: 'canAdmin',
        component: './Email/EmailServerList',
      },
      {
        name: 'Email Detail',
        path: '/email/detail/:emailId',
        icon: 'MailOutlined',
        hideInMenu: true,
        layout: false,
        component: './Email/EmailList/EmailDetail',
      },
    ],
  },
  {
    path: '/basic-data',
    name: 'Basic data',
    icon: 'form',
    routes: [
      {
        path: '/basic-data/supplier',
        name: 'Suppliers',
        icon: 'table',
        component: './BasicData/SupplierList',
        access: 'user',

      },
      {
        path: '/basic-data/warehouse-locations',
        name: 'Warehouse locations',
        icon: 'table',
        component: './BasicData/WarehouseLocationList',
        access: 'user',
      },
      {
        path: '/basic-data/users',
        name: 'Users',
        icon: 'user',
        component: './UsersList',
        access: 'user',
      },
      {
        path: '/basic-data/order-shipping-setting',
        name: 'Order Shipping Settings',
        icon: 'SettingOutlined',
        component: './BasicData/OrderShippingSetting',
        access: 'user',
      },
      {
        path: '/basic-data/vat',
        name: 'VAT',
        icon: 'table',
        component: './BasicData/VatList',
        access: 'user',
      },
      {
        path: '/basic-data/price-type',
        name: 'Price type',
        icon: 'table',
        component: './BasicData/PriceTypeList',
        access: 'user',
      },
      {
        path: '/basic-data/trademark-group',
        name: 'Trademark Group',
        icon: 'table',
        component: './BasicData/TrademarkGroupList',
        access: 'roleRouteFilter',
      },
      {
        path: '/basic-data/trademark',
        name: 'Trademark',
        icon: 'table',
        component: './BasicData/TrademarkList',
        access: 'roleRouteFilter',
      },
      {
        path: '/basic-data/producer',
        name: 'Producer',
        icon: 'table',
        component: './BasicData/ProducerList',
        access: 'user',
      },
      {
        path: '/basic-data/text-module',
        name: 'Text Module',
        icon: 'FileTextOutlined',
        component: './Sys/TextModule',
        access: 'user',
      },
      {
        path: '/basic-data/system-config',
        name: 'System Config',
        icon: 'SettingOutlined',
        component: './Sys/Dict',
        access: 'user',
      },
      {
        path: '/basic-data/sys-import-rw-col-map',
        name: 'Import RW Column Map',
        icon: 'table',
        component: './Sys/SysImportRwColMap',
        access: 'user',
      },
    ],
  },
  {
    path: '/monitor',
    name: 'Monitor',
    icon: 'MonitorOutlined',
    access: 'user',
    routes: [
      {
        path: '/monitor/magento-sync-log',
        name: 'Sync Log',
        icon: 'table',
        component: './Audit/MagSyncLog',
      },
      {
        path: '/monitor/file-log',
        name: 'File Log',
        icon: 'table',
        component: './Audit/FileLog',
      },
      {
        path: '/monitor/order-user-action-log',
        name: 'Order Detail Action Log',
        icon: 'table',
        component: './Audit/MagOrderUserActionLog',
      },
      {
        path: '/monitor/sys-log',
        name: 'System Log',
        icon: 'table',
        component: './Audit/SysLog',
      },
    ],
  },

  /* {
    path: '/file-browser',
    name: 'FileBrowser',
    // icon: 'MonitorOutlined',
    hideInMenu: false,
    routes: [
      {
        path: '/file-browser/main',
        name: 'FileBrowser',
        component: './FolderViewer',
        hideInMenu: false,
      },
    ],
  }, */

  {
    path: '/',
    redirect: '/welcome',
  },

  { component: './404' },
];
