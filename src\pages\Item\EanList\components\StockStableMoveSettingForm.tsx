import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import type { StockStableRowType } from './StockStableCorrectionEditableTable';
import type { StockStableMoveParamType } from '@/services/foodstore-one/Stock/stock-stable';
import { stockStableMove } from '@/services/foodstore-one/Stock/stock-stable';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import type { DefaultOptionType } from 'antd/lib/select';
import { getEanACList } from '@/services/foodstore-one/Item/ean';

export type FormValueType = Partial<StockStableMoveParamType>;

const handleMove = async (fields: FormValueType) => {
  const hide = message.loading('Moving stocks...');

  try {
    await stockStableMove(fields);
    hide();
    message.success('Moving is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type StockStableMoveSettingFormProps = {
  ean?: Partial<API.Ean>;
  initialData?: Partial<StockStableRowType>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: StockStableRowType) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const StockStableMoveSettingForm: React.FC<StockStableMoveSettingFormProps> = (props) => {
  const qtyField = props.ean?.is_single ? 'piece_qty_edit' : 'box_qty_edit';
  const labelPrefix = props.ean?.is_single ? 'Pcs' : 'Box';
  const maxQty = sn(props.ean?.is_single ? props.initialData?.single_piece_qty : props.initialData?.[qtyField]);

  const formRef = useRef<ProFormInstance>();

  // Reference data
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);
  const [siblings, setSiblings] = useState<DefaultOptionType[]>([]);
  const [loading, setLoading] = useState<{ wl?: boolean; siblings?: boolean }>({});

  useEffect(() => {
    setLoading((prev) => ({ ...prev, wl: true }));
    getWarehouseLocationACList({})
      .then((res) => {
        setWarehouseLocations(res);
      })
      .catch(Util.error)
      .finally(() => setLoading((prev) => ({ ...prev, wl: false })));
  }, []);

  useEffect(() => {
    if (props.modalVisible) {
      formRef.current?.setFieldsValue({
        [qtyField]: maxQty,
        new_ean_id: props.ean?.id,
      });
    }
  }, [props.modalVisible, qtyField, props.ean?.id, maxQty]);

  useEffect(() => {
    if (props.modalVisible && props.ean?.item_id) {
      setLoading((prev) => ({ ...prev, siblings: true }));
      getEanACList({ item_id: sn(props.ean?.item_id) }, { attr_case_qty: 'descend' })
        .then((res) => {
          setSiblings(
            res
              .filter((x) => x.id == props.ean?.id || (props.ean?.is_single ? true : x.is_single))
              .map((x: API.Ean) => ({
                value: x.id,
                label:
                  x.id == props.ean?.id
                    ? '-- Self ---'
                    : `${x.is_single ? ' [Single] ' : `${x.attr_case_qty}x - `}${x.sku} - ${x.ean}`,
              })),
          );
        })
        .catch(Util.error)
        .finally(() => setLoading((prev) => ({ ...prev, siblings: false })));
    }
  }, [props.modalVisible, props.ean?.item_id, props.ean?.is_single, props.ean?.id]);

  return (
    <ModalForm<FormValueType>
      title={'Stock Movement'}
      width={500}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      className={props.ean?.is_single ? 'm-single' : 'm-multi'}
      onFinish={async (value) => {
        const success = await handleMove({
          ...value,
          ean_id: props.ean?.id,
          id: props.initialData?.id, // stock stable id
          old_wl_id: props.initialData?.warehouse_location?.id,
          exp_date: props.initialData?.exp_date,
          ibo_id: props.initialData?.ibo_id,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProForm.Item label="Old Location">{props.initialData?.warehouse_location?.name}</ProForm.Item>
      <ProForm.Item label="Exp. Date">
        {props.initialData?.exp_date ? Util.dtToDMY(props.initialData?.exp_date) : '-'}
      </ProForm.Item>
      <ProForm.Item label={`Available ${labelPrefix} Qty`}>{maxQty}</ProForm.Item>
      <ProFormSelect
        name="new_wl_id"
        rules={[
          {
            required: true,
            message: 'New Warehouse Location is required',
          },
        ]}
        options={warehouseLocations}
        disabled={loading.wl}
        fieldProps={{
          loading: loading.wl,
          onInputKeyDown: (e: any) => {
            if (Util.isTabPressed(e)) {
              const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
              if (dropdownWrapper?.children.length == 1) {
                const id = e.target.getAttribute('aria-activedescendant');
                const value = sn(document.getElementById(id)?.innerText);
                formRef.current?.setFieldValue(['new_wl_id'], value);
              }
            }
          },
        }}
        width="md"
        label="New Location"
        placeholder="New Location"
        showSearch
      />
      <ProFormDigit
        width="sm"
        name={qtyField}
        label={`${labelPrefix} Qty`}
        min={1}
        initialValue={maxQty}
        max={maxQty}
        rules={[
          {
            required: true,
            message: 'Qty is required',
          },
          /* {
            min: 1,
            message: 'Qty minium should be 1',
          },
          {
            max: maxQty,
            message: 'Qty cannot be greater than ' + maxQty,
          }, */
        ]}
        fieldProps={{ precision: 0 }}
      />
      <ProFormSelect
        name="new_ean_id"
        rules={[
          {
            required: true,
            message: 'New EAN is required',
          },
        ]}
        disabled={loading.siblings}
        options={siblings}
        width="md"
        label="Target EAN"
        placeholder="Target EAN"
        initialValue={props.ean?.id}
        fieldProps={{
          loading: loading.siblings,
          onInputKeyDown: (e: any) => {
            if (Util.isTabPressed(e)) {
              const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
              if (dropdownWrapper?.children.length == 1) {
                const id = e.target.getAttribute('aria-activedescendant');
                const value = sn(document.getElementById(id)?.innerText);
                formRef.current?.setFieldValue(['new_ean_id'], value);
              }
            }
          },
        }}
        showSearch
      />
    </ModalForm>
  );
};

export default StockStableMoveSettingForm;
