import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, Space, Spin } from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { dsReportFirstUserMediumByDate, getGoogleAnalytics } from '@/services/foodstore-one/Audit/google-analytics';
import Util, { nf2, ni, sn } from '@/util';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import SProFormDateRange, { DRSelection, DRSelectionKv } from '@/components/SProFormDateRange';
import { useModel } from 'umi';

const metricLabelKv: Record<string, string> = {
  date: 'Date',

  activeUsers: 'Active Users',
  newUsers: 'New Users',
  userEngagementDuration: 'Average engagement time',
  eventCount: 'Event Count',
};

const metricKeys = ['activeUsers', 'newUsers', 'userEngagementDuration', 'eventCount'];

type StatDataType = {
  summary: any[];
  detail: any[];
};

/**
 * Google analytics stats page component
 *
 * @returns
 */
const GoogleAnalytics: React.FC = () => {
  const {
    appSettings: { drSelection },
  } = useModel('app-settings');

  const [statData, setStatData] = useState<StatDataType>({
    summary: [],
    detail: [],
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [dateRangeTitle, setDateRangeTitle] = useState<string>('');

  const formRef = useRef<ProFormInstance>();

  const loadStatData = () => {
    const sfValues = formRef.current?.getFieldsValue();

    setLoading(true);
    getGoogleAnalytics(sfValues)
      .then((data) => setStatData(data))
      .catch(Util.error)
      .finally(() => setLoading(false));

    const drShortcut: DRSelection = formRef.current?.getFieldValue('dr_selection') as DRSelection;

    setDateRangeTitle(
      drShortcut == DRSelection.DR_CUSTOM
        ? `${Util.dtToDMY(formRef.current?.getFieldValue('start_date'))} ~ ${Util.dtToDMY(
            formRef.current?.getFieldValue('end_date'),
          )}`
        : DRSelectionKv[drShortcut] ?? '',
    );
  };

  const summary = statData.summary;

  // detail table data
  const [detailTableColumns, setDetailTableColumns] = useState<ProColumns[]>([]);
  const [detailTableDS, setDetailTableDS] = useState<any[]>([]);

  useEffect(() => {
    const proCols: ProColumns[] = [
      {
        dataIndex: 'firstUserSource',
        width: 300,
        title: 'First user source / medium',
        render: (dom, record) =>
          record.uid != 'total' ? `${record.firstUserSource} / ${record.firstUserMedium}` : 'Total',
      },
      {
        dataIndex: 'newUsers',
        width: 100,
        title: 'New Users',
        align: 'right',
        render: (dom, record) => ni(record.newUsers),
      },
      {
        dataIndex: 'engagedSessions',
        width: 100,
        title: 'Engaged Sessions',
        align: 'right',
        render: (dom, record) => ni(record.engagedSessions),
      },
      {
        dataIndex: 'engagementRate',
        width: 100,
        title: 'Engaged Rate',
        align: 'right',
        render: (dom, record) => nf2(100 * record.engagementRate, true) + '%',
      },
      {
        dataIndex: 'eventCount',
        width: 100,
        title: 'Event Count',
        align: 'right',
        render: (dom, record) => ni(record.eventCount),
      },
    ];
    const totalRow = {
      uid: 'total',
      newUsers: 0,
      sessions: 0,
      engagedSessions: 0,
      engagementRate: 0,
      eventCount: 0,
    };
    statData.detail.forEach((x) => {
      totalRow.newUsers += sn(x.newUsers);
      totalRow.sessions += sn(x.sessions);
      totalRow.engagedSessions += sn(x.engagedSessions);
      totalRow.eventCount += sn(x.eventCount);
    });
    totalRow.engagementRate = totalRow.sessions ? totalRow.engagedSessions / totalRow.sessions : 0;

    setDetailTableDS([totalRow, ...statData.detail]);
    setDetailTableColumns(proCols);
  }, [statData.detail]);

  useEffect(() => {
    const initialDrSelection = DRSelection.DR_LAST_30_DAYS;
    const range = drSelection[initialDrSelection as any];
    formRef?.current?.setFieldValue('dr_selection', initialDrSelection);
    if (range) {
      formRef?.current?.setFieldValue('start_date', range[0] ? range[0] : null);
      formRef?.current?.setFieldValue('end_date', range[1] ? range[1] : null);
      loadStatData();
    }
  }, [drSelection]);

  return (
    <PageContainer>
      <Card>
        <ProForm layout="inline" formRef={formRef} submitter={false}>
          <SProFormDateRange label="Date" formRef={formRef} />
          <Button type="primary" icon={<ReloadOutlined />} onClick={() => loadStatData()}>
            Search
          </Button>
          <Button
            type="primary"
            ghost
            icon={<DownloadOutlined />}
            style={{ marginLeft: 32 }}
            onClick={() => {
              setLoading(true);
              dsReportFirstUserMediumByDate()
                .catch(Util.error)
                .then((res) => {
                  loadStatData();
                })
                .finally(() => setLoading(false));
            }}
          >
            Down Sync
          </Button>
        </ProForm>
      </Card>

      <Spin spinning={loading}>
        <Space size={16} wrap>
          {metricKeys.map((x: any, index: number) => {
            const k = x.name ?? x;
            let value: any = 0;
            if (summary.length) {
              value = summary[0][k] ?? 0;
              const activeUsers = sn(summary[0].activeUsers);
              if (k == 'userEngagementDuration' && activeUsers) {
                value = (
                  <>
                    {ni(sn(value) / activeUsers)}{' '}
                    <span
                      style={
                        {
                          /* fontSize: 16, fontWeight: 'normal' */
                        }
                      }
                    >
                      s
                    </span>
                  </>
                );
              } else {
                value = ni(value, true);
              }
            }

            return (
              <Card
                key={k}
                size="small"
                title={<label>{metricLabelKv[k]}</label>}
                className="card-desc"
                style={{ width: 200 }}
              >
                <span style={{ fontSize: 28, fontWeight: 'bold' }}>{value}</span>
              </Card>
            );
          })}
        </Space>
      </Spin>

      <ProTable<any, API.PageParams>
        rowKey="uid"
        size="small"
        headerTitle={
          <>
            First user source / medium{' '}
            <span className="text-sm" style={{ paddingLeft: 48 }}>
              {dateRangeTitle}
            </span>
          </>
        }
        revalidateOnFocus={false}
        search={false}
        params={{}}
        style={{ width: 800, marginTop: 16 }}
        dataSource={detailTableDS}
        columns={detailTableColumns}
        pagination={{ showSizeChanger: false, hideOnSinglePage: true, defaultPageSize: 20 }}
        columnEmptyText=""
        rowClassName={(record) => (record.uid == 'total' ? 'total-row' : '')}
        loading={loading}
      />
      {/* {process.env.NODE_ENV === 'development' && (
        <pre>{JSON.stringify(statData, undefined, 4)}</pre>
      )} */}
    </PageContainer>
  );
};

export default GoogleAnalytics;
