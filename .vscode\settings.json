{
  "editor.formatOnSave": true,
  "prettier.requireConfig": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "git.enableSmartCommit": true,
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  // "scss.lint.unknownAtRules": "ignore",
  "editor.fontFamily": "JetBrains Mono, Tahoma, Consolas, 'Courier New', monospace",
  "editor.fontSize": 13,
  "editor.lineHeight": 21,
  "editor.fontWeight": "300",
  "antdRush.defaultAntdMajorVersion": "^4",
  "antdRush.language": "English",
  "jest.autoRun": "false",
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "cSpell.words": [
    "antd",
    "Bezugsweg",
    "billiger",
    "Carrior",
    "cturnover",
    "datetime",
    "DMYHHMM",
    "EAN's",
    "eans",
    "firstname",
    "foodstore",
    "Füllmatarial",
    "fullname",
    "gclid",
    "gdsn",
    "HHMM",
    "holded",
    "ibom",
    "ibos",
    "idealo",
    "Kartons",
    "kaufland",
    "lable",
    "Laktosefrei",
    "lastname",
    "Lebensmittelunternehmen",
    "logprobs",
    "Magento",
    "Multipacke",
    "Nein",
    "Openai",
    "orderlist",
    "picklist",
    "Picklists",
    "Popconfirm",
    "Qtys",
    "Shelflife",
    "shqtracker",
    "sider",
    "Skus",
    "sqls",
    "stocklot",
    "Strecke",
    "syncable",
    "tzoffset",
    "unhide",
    "upsync",
    "VCOL",
    "VCOLS",
    "vstock",
    "Zuckerfrei"
  ],
  "workbench.tree.indent": 16
}