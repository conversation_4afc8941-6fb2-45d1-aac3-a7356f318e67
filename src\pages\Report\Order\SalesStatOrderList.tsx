import { Drawer } from 'antd';
import { Card, Typography } from 'antd';
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import { skuToItemId, sn } from '@/util';
import { ni } from '@/util';
import Util, { nf2 } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import IboDetailModal from './components/IboDetailModal';
import SProFormDateRange, { DRSelection } from '@/components/SProFormDateRange';
import type { DefaultOptionType } from 'antd/lib/select';
import OrderListModal from '@/pages/Magento/Order/components/OrderListModal';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import UpdatePriceAttributeForm from '@/pages/Item/EanList/components/UpdatePriceAttributeForm';
import UpdateTextsForm from '@/pages/Item/EanList/components/UpdateTextsForm';
import type { TrademarkChangeCallbackHandlerTypeParamType } from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import { getOrderItemsList } from '@/services/foodstore-one/Magento/order-item';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { useLocation } from 'umi';

type SearchFormValueType = {
  trademark?: DefaultOptionType;
  sku?: string;
  name?: string;
};

export type OrderModalSearchParamsType = SearchFormValueType & {
  dateRange?: DateRangeType;
} & { filtered_only?: boolean };

type RecordType = API.OrderItem & Record<string, any>;

const defaultSearchFormValues = {
  sku: '',
};

const SalesStatOrderList: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();
  const location: any = useLocation();

  const [loading, setLoading] = useState<boolean>(false);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
  });

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);

  // trademarks dropdown options
  const [trademarks, setTrademarks] = useState<DefaultOptionType[]>([]);

  // const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [openIBOModal, setOpenIBOModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RecordType>();

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});
  // EAN Text modal
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);

  const [dataSource, setDatasource] = useState<RecordType[]>([]);

  const defaultColumns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        title: 'Date',
        dataIndex: ['created_at'],
        width: 80,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return Util.dtToDMY(record.created_at);
        },
      },
      {
        title: 'Time',
        dataIndex: ['created_at_time'],
        width: 50,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return Util.dtToHHMM(record.created_at);
        },
      },
      {
        title: 'Increment ID',
        dataIndex: ['increment_id'],
        width: 120,
        hideInSearch: true,
        copyable: true,
      },
      {
        title: 'Order ID',
        dataIndex: ['order_id'],
        width: 100,
        hideInSearch: true,
        copyable: true,
      },

      {
        title: 'SKU',
        dataIndex: ['sku'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 80,
        copyable: true,
        defaultSortOrder: 'descend',
        render: (dom, record) => {
          return record.uid == 'total' ? null : (
            <Typography.Link href={`/item/ean-all-summary?sku=${skuToItemId(record.sku)}_`} target="_blank" copyable>
              {record.sku}
            </Typography.Link>
          );
        },
      },
      {
        title: 'Name',
        dataIndex: ['item_ean', 'ean_text_de', 'internal_short_name'],
        align: 'left',
        ellipsis: false,
        width: 350,
        tooltip: 'Internal Short Name or Full Name. Click to edit.',
        render: (dom, record, index) => {
          const shortName = record.item_ean?.ean_text_de?.internal_short_name;
          const name = shortName ? shortName : record.item_ean?.ean_text_de?.name ?? record.name;

          return (
            <Typography.Text
              title={shortName ? record.item_ean?.ean_text_de?.name ?? record.name : ''}
              ellipsis={{ tooltip: name }}
            >
              {name}
            </Typography.Text>
          );
        },
        onCell: (record) => {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleUpdateTextsModalVisible(true);
            },
          };
        },
      },
      {
        title: 'S / M',
        dataIndex: ['item_ean', 'is_single'],
        width: 50,
        align: 'center',
        hideInSearch: true,
        render(__, record) {
          return record.item_ean?.is_single ? 'S' : 'M';
        },
      },
      {
        title: 'Discount?',
        dataIndex: ['item_ean', 'fs_special_discount'],
        width: 50,
        align: 'center',
        className: 'c-red',
        hideInSearch: true,
      },
      {
        title: 'Qty',
        dataIndex: ['qty_ordered'],
        width: 40,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return ni(record.qty_ordered);
        },
      },
      {
        title: 'G. Turnover',
        dataIndex: ['turnover'],
        width: 90,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return nf2(sn(record.qty_ordered) * sn(record.price_incl_tax));
        },
      },
      {
        title: 'G. Turnover / pcs',
        dataIndex: ['turnover_pcs'],
        width: 90,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return sn(record.item_ean?.attr_case_qty)
            ? nf2(sn(record.price_incl_tax) / sn(record.item_ean?.attr_case_qty))
            : null;
        },
      },
      {
        title: 'BP',
        dataIndex: `bp`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          return <span>{nf2(record.bp)}</span>;
        },
      },
      {
        title: 'GP (S)',
        dataIndex: `gp_s`,
        align: 'right',
        width: 70,
        sorter: false,
        tooltip: 'Click to edit price',
        className: 'cursor-pointer',
        render: (__, record) => {
          if (!record.item_ean?.is_single) return null;
          let cls = '';
          const gp = sn(record.gp);
          if (gp >= 5) cls += 'c-green';
          else if (gp >= 1) {
          } else if (gp >= 0) cls += 'c-orange';
          else cls += 'c-red';

          return <span className={cls}>{nf2(record.gp)}</span>;
        },
        onCell: (record) => {
          return {
            onClick: () => {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      {
        title: 'GP (M)',
        dataIndex: `gp_m`,
        align: 'right',
        width: 70,
        sorter: false,
        tooltip: 'Click to edit price',
        className: 'cursor-pointer',
        render: (__, record) => {
          if (record.item_ean?.is_single) return null;
          let cls = '';
          const gp = sn(record.gp);
          if (gp >= 5) cls += 'c-green';
          else if (gp >= 1) {
          } else if (gp >= 0) cls += 'c-orange';
          else cls += 'c-red';

          return <span className={cls}>{nf2(record.gp)}</span>;
        },
        onCell: (record) => {
          return {
            onClick: () => {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      { title: '', dataIndex: 'option1', valueType: 'option' },
    ],
    [],
  );

  useEffect(() => {
    setColumns(defaultColumns);
  }, [defaultColumns]);

  useEffect(() => {
    if (location?.query?.start_date && location?.query?.end_date)
      searchFormRef?.current?.setFieldValue('dr_selection', DRSelection.DR_CUSTOM);

    searchFormRef?.current?.setFieldValue('start_date', location?.query?.start_date);
    searchFormRef?.current?.setFieldValue('end_date', location?.query?.end_date);
    actionRef.current?.reload();
  }, [location?.query?.start_date, location?.query?.end_date]);

  return (
    <PageContainer
    /* extra={
        <Button
          key="exportXls"
          type="primary"
          size="small"
          icon={<FileExcelOutlined />}
          onClick={() => {
            const hide = message.loading('Downloading list in Xls...', 0);
            exportSalesStatsList({ listMode: 'detail' }, {}, {})
              .then((res) => {
                hide();
                if (res.url) {
                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                }
              })
              .catch(Util.error)
              .finally(() => {
                hide();
              });
          }}
        >
          Export as Xls
        </Button>
      } */
    >
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_order_sales_stat_orderlist', { has_dicount: '' })}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue(defaultSearchFormValues as any);
              actionRef.current?.reload();
            },
          }}
        >
          <SProFormDateRange label="Date" formRef={searchFormRef} style={{ marginLeft: 16 }} disabled={loading} />
          {formElements}

          <ProFormText name={'sku'} label="SKU" width={100} placeholder={'SKU'} disabled={loading} />
          <ProFormText name={'name'} label="Name" width={150} placeholder={'Name'} disabled={loading} />
          <ProFormSelect
            name="has_discount"
            label="Discount"
            disabled={loading}
            options={[
              { value: '', label: 'All' },
              { value: 'Y', label: 'Yes' },
              { value: 'N', label: 'No' },
            ]}
          />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={'Sales Statistics (Orderlist)'}
        actionRef={actionRef}
        size="small"
        rowKey="item_id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        bordered
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_order_sales_stat_orderlist', searchFormValues);
          Util.setSfValues('sf_order_sales_stat_orderlist_p', params);

          setLoading(true);
          return getOrderItemsList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
              with: 'itemEan.forPriceUpdate',
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data.map((x) => ({ item_id: x.item_id, order_id: x.order_id })));
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: sn(
            Util.getSfValues('sf_order_sales_stat_orderlist_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        columns={columns}
        columnEmptyText=""
        rowSelection={false}
        rowClassName={(record, index: number, indent: number) => {
          let cls = '';
          if (record.uid == 'total') {
            cls = 'total-row';
          } else cls = record?.item_ean?.is_single ? 'row-single' : 'row-multi';

          if (index > 0) {
            if (dataSource[index - 1].order_id != record.order_id) {
              cls += ' bt';
            }
          }

          return cls;
        }}
      />

      {/* <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_qty,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_res_qty,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      /> */}
      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
      />
      {currentRow?.item_ean?.id && (
        <IboDetailModal
          eanId={currentRow?.item_ean?.id}
          modalVisible={openIBOModal}
          handleModalVisible={setOpenIBOModal}
        />
      )}

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow?.item_ean || {}}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        initialValues={currentRow?.item_ean || {}}
        isRefetchInitialValues
        onSubmit={async (values) => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
      />

      <Drawer
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        width={700}
        open={showImportedPrices}
        className={currentRow?.item_ean?.is_single ? 'm-single' : 'm-multi'}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
        // closable={false}
      >
        {currentRow?.item_ean && (
          <ImportedPrices
            itemEan={currentRow.item_ean}
            iboListProps={{
              filters: {
                filterMode: 'latest',
                pageSize: 8,
              },
            }}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default SalesStatOrderList;
