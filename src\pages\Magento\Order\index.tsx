import { <PERSON><PERSON>, <PERSON><PERSON>, Card, message, Space, Tag, Typography } from 'antd';
import React, { CSSProperties, useEffect, useMemo, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { getOrdersList, getOrderStatusACList, updateOrderExtra } from '@/services/foodstore-one/Magento/order';
import type { MagentoOrderStatusType, ShippingServiceNameType } from '@/constants';
import {
  DEFAULT_PER_PAGE_PAGINATION,
  DictCode,
  MagentoOrderStatusOptions,
  MagOrderExtraStatus,
  MagOrderExtraStatusOptions,
  ShippingAddressStatus,
} from '@/constants';
import Util, { nf2, nf3, skuToItemId, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { IRouteComponentProps, useLocation, useModel } from 'umi';
import { EditOutlined, EyeOutlined, LinkOutlined, WarningOutlined } from '@ant-design/icons';
import UpdateAddressesModal from './components/UpdateAddressesModal';
import { isArray } from 'lodash';
import EditableCell from '@/components/EditableCell';
import { getOrderShippingProviderList } from '@/services/foodstore-one/BasicData/order-shipping-provider';
import UpdateOrderExtraFormModal from './components/UpdateOrderExtraFormModal';
import SEbayOrderNo from './components/SEbayOrderNo';
const Text = Typography.Text;

export const isOrderAddressEditable = (orderStatus?: MagentoOrderStatusType, addressEntityId?: number) => {
  return orderStatus == 'pending' || orderStatus == 'processing' || orderStatus == 'canceled';
};

export const SOrderId: React.FC<{
  order?: API.Order;
  target?: React.HTMLAttributeAnchorTarget;
  style?: CSSProperties;
}> = ({ order, target, style }) => {
  return order?.entity_id ? (
    <Typography.Link
      title="Open a detail page"
      copyable
      href={`/orders/order-detail?entity_id=${order?.entity_id}`}
      target={target || '_blank'}
      style={style}
    >
      {order?.entity_id}
    </Typography.Link>
  ) : null;
};

export const SParcelLink: React.FC<{
  service_name?: ShippingServiceNameType | string;
  parcel_no?: string;
  track_id?: string;
  target?: string;
  showTrackId?: boolean;
  style?: CSSProperties;
}> = ({ parcel_no, track_id, style, target, showTrackId, service_name }) => {
  const { getParcelUrl } = useModel('app-settings');

  const newParcelNo = showTrackId ? track_id : parcel_no ?? track_id;
  return newParcelNo ? (
    <Typography.Link
      title={`${service_name} | ${parcel_no}`}
      copyable
      href={getParcelUrl(parcel_no, service_name as ShippingServiceNameType)}
      target={target || '_blank'}
      style={style}
    >
      {newParcelNo}
    </Typography.Link>
  ) : null;
};

export const FullAddress: React.FC<{ order: API.Order; type?: 'shipping' | 'invoice' }> = ({ order, type }) => {
  if (type == 'shipping') {
    let addr = <></>;
    // if (order.sa_fullname) addr += order.sa_fullname;
    if (order.sa_street)
      addr = (
        <>
          {addr}{' '}
          <Text mark={order.warn_sa_street || order.warn_sa_street_long || order.warn_sa_street_no}>
            {order.sa_street}
          </Text>
        </>
      );
    if (order.sa_city)
      addr = (
        <>
          {addr}, <Text>{order.sa_city}</Text>
        </>
      );
    if (order.sa_zip)
      addr = (
        <>
          {addr}, <Text mark={order.warn_sa_zip || order.warn_sa_zip_de_wrong}>{order.sa_zip}</Text>
        </>
      );
    if (order.sa_country_code)
      addr = (
        <>
          {addr}, <Text mark={order.warn_sa_zip || order.warn_sa_zip_de_wrong}>{order.country?.name || ''}</Text>
        </>
      );
    return addr;
  } else {
    let addr = '';
    if (order?.detail?.billing_address) {
      const addrObj = order.detail.billing_address;
      // if (addrObj.sa_fullname) addr += addrObj.sa_fullname;
      if (addrObj.street) addr += ' ' + (isArray(addrObj.street) ? addrObj.street.join(', ') : addrObj.street);
      if (addrObj.city) addr += ', ' + addrObj.city;
      if (addrObj.postcode) addr += ', ' + addrObj.postcode;
      if (addrObj.country_id) addr += ', ' + addrObj.country_id;
    }
    return <>{addr}</>;
  }
};

export const MagentoOrderStatus: React.FC<{ status?: MagentoOrderStatusType }> = ({ status }) => {
  let color = 'default';
  switch (`${status}`) {
    case 'complete':
    case 'closed':
      color = 'success';
      break;
    case 'processing':
      color = 'blue';
      break;
    case 'pending':
      color = 'orange';
      break;
    case 'canceled':
      color = 'red';
      break;
  }

  return <Tag color={color as any}>{MagentoOrderStatusOptions[status || '-'] ?? '-'}</Tag>;
};

export type SearchFormValueType = Partial<API.Order>;

const OrderList: React.FC<IRouteComponentProps> = (props) => {
  const { getDictByCode } = useModel('app-settings');
  const location = props.location;
  const extra_note1_status_in_param = location.query.extra_note1_status;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [statusACList, setStatusACList] = useState<(DefaultOptionType & { cnt?: number })[]>([]);

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.Order>();
  const [openUpdateAddressModal, setOpenUpdateAddressModal] = useState<boolean>(false);

  const [openUpdateOrderExtraModal, setOpenUpdateOrderExtraModal] = useState<boolean>(false);

  const columns: ProColumns<API.Order>[] = [
    {
      title: 'Order ID',
      dataIndex: 'entity_id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      defaultSortOrder: 'descend',
      width: 80,
      render(dom, entity) {
        return (
          <>
            <SOrderId order={entity} />
            <a
              href={`/orders/order-detail-packing?entity_id=${entity.entity_id}`}
              target="_blank"
              style={{ marginLeft: 8 }}
              title="Open Order Detail(WH) in new tab"
            >
              <LinkOutlined />
            </a>
          </>
        );
      },
    },
    {
      title: 'Store',
      dataIndex: ['store', 'code'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 60,
    },
    {
      title: 'Increment ID',
      dataIndex: ['increment_id'],
      sorter: true,
      align: 'left',
      width: 120,
      render(dom, record) {
        return record?.entity_id ? (
          <Typography.Link
            href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${record?.entity_id}/`}
            target="_blank"
            copyable
          >
            {record.increment_id}
          </Typography.Link>
        ) : (
          dom
        );
      },
    },
    {
      title: 'status',
      dataIndex: ['status'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 120,
      render: (_, record) => <MagentoOrderStatus status={record.status || ''} />,
    },
    {
      title: 'Note 1 Status',
      dataIndex: ['extra', 'note1_status'],
      align: 'center',
      ellipsis: true,
      className: 'p-0',
      width: 20,
      render: (_, record) => {
        const note1_status = record.extra?.note1_status;
        if (note1_status == MagOrderExtraStatus.OnHold) {
          return <WarningOutlined style={{ color: 'orange' }} title={note1_status} />;
        } else if (note1_status == MagOrderExtraStatus.OnHoldCleared) {
          return <WarningOutlined style={{ color: 'green' }} title={note1_status} />;
        }

        return null;
      },
    },

    {
      title: 'Items Qty',
      dataIndex: 'total_item_count',
      align: 'center',
      width: 50,
      tooltip: 'Qty of included items',
      showSorterTooltip: false,
    },
    {
      title: 'Ordered Qty',
      dataIndex: 'total_qty_ordered',
      align: 'center',
      width: 50,
    },

    {
      title: 'Grand Total',
      dataIndex: 'grand_total',
      sorter: true,
      align: 'right',
      width: 70,
      render: (dom, record) => <span style={{ paddingRight: 12 }}>{nf2(record.grand_total)}</span>,
    },
    {
      title: 'Name',
      dataIndex: 'sa_fullname',
      width: 100,
      tooltip: 'Red colored rows are in warnings list. Highlighted parts may be wrong!',
      render: (dom, record) => (
        <Typography.Text mark={record?.warn_sa_fullname || record?.warn_sa_fullname_wrong}>
          {record.sa_fullname}
        </Typography.Text>
      ),
    },
    {
      title: 'Company',
      dataIndex: 'sa_company',
      align: 'left',
      width: 120,
      ellipsis: true,
    },
    {
      dataIndex: 'sa_full_warning',
      align: 'left',
      width: 30,
      className: 'p-0',
      tooltip: 'Delivery address warning',
      render(dom, record) {
        const warning_def = record.warnings_def || '';
        if (warning_def) {
          const title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
          return <WarningOutlined style={{ color: 'white' }} title={title} />;
        }
        return null;
      },
    },
    {
      title: 'Delivery Address',
      dataIndex: 'sa_full',
      align: 'left',
      width: 400,
      tooltip: 'Red colored rows are in warnings list. Highlighted parts may be wrong!',
      ellipsis: true,
      render(dom, record) {
        return (
          <>
            <FullAddress order={record} type="shipping" />
            <Button
              type="link"
              size="small"
              icon={isOrderAddressEditable(record.status) ? <EditOutlined /> : <EyeOutlined />}
              style={{
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 12,
                color: isOrderAddressEditable(record.status) ? 'auto' : 'grey',
              }}
              title={isOrderAddressEditable(record.status) ? 'Update addresses' : 'View addresses'}
              onClick={() => {
                setCurrentRow(record);
                setOpenUpdateAddressModal(true);
              }}
            />
          </>
        );
      },
      onCell(record) {
        return { style: { paddingRight: 16 } };
      },
    },
    /* {
      title: 'City',
      dataIndex: 'sa_city',
      sorter: true,
      align: 'left',
      width: 120,
      showSorterTooltip: false,
    },
    {
      title: 'Zip',
      dataIndex: 'sa_zip',
      align: 'left',
      width: 50,
    },
    {
      title: 'Country',
      dataIndex: ['country', 'name'],
      align: 'left',
      width: 80,
      ellipsis: true,
    }, */
    {
      title: 'Weight (kg)',
      dataIndex: ['weight_map'],
      align: 'right',
      width: 80,
      render(dom, record) {
        return nf3(sn(record.weight_map?.weight) / 1000.0, false, true);
      },
      onCell: (record) => {
        return {
          className: record.weight_map?.zeroCnt ? 'bg-light-red1' : '',
        };
      },
    },
    {
      title: 'Ebay Order No',
      dataIndex: ['detail', 'payment', 'additional_information', 2],
      width: 120,
      showSorterTooltip: false,
      copyable: true,
      render: (__, record) => <SEbayOrderNo order={record} />,
    },
    {
      title: 'Ebay Customer',
      dataIndex: ['detail', 'billing_address'],
      width: 110,
      showSorterTooltip: false,
      copyable: true,
      render: (dom, record) => {
        const ba = record.detail?.billing_address;
        return record.increment_id?.startsWith('EBD') ? (ba ? ba.firstname + ' ' + ba.lastname : null) : null;
      },
    },
    {
      title: 'Created on',
      dataIndex: ['created_at'],
      sorter: true,
      ellipsis: true,
      width: 80,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMMTz(record.created_at),
    },
    {
      title: 'Picklist Nos',
      dataIndex: ['picklist_nos'],
      sorter: true,
      width: 80,
      ellipsis: true,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      render: (dom, record) => {
        const ids: any[] = [];
        record.picklist_details?.forEach((x) => {
          if (!ids.includes(x.picklist_id)) {
            ids.push(x.picklist_id);
          }
        });
        return ids.join(', ');
      },
    },
    {
      title: 'Address Status',
      dataIndex: ['extra', 'shipping_address_status'],
      align: 'left',
      width: 100,
      tooltip: 'Click to edit.',
      className: 'bl2',
      render: (dom, record) => (
        <EditableCell
          dataType="select"
          defaultValue={record.extra?.shipping_address_status}
          options={Object.values(ShippingAddressStatus).map(
            (x) => ({ value: x, label: x } as unknown as DefaultOptionType),
          )}
          triggerUpdate={async (newValue: any, cancelEdit) => {
            if (!record.entity_id) return Promise.reject();
            return updateOrderExtra(record.entity_id, {
              shipping_address_status: newValue,
            })
              .then((res) => {
                message.destroy();
                message.success('Updated successfully.');
                actionRef.current?.reload();
              })
              .catch(Util.error)
              .finally(() => cancelEdit?.());
          }}
        >
          {dom}
        </EditableCell>
      ),
    },
    /* {
      title: 'Shipping provider (Old)',
      dataIndex: ['extra', 'shipping_provider_name_old'],
      align: 'left',
      width: 100,
      tooltip: 'Original shipping provider',
    }, */
    {
      title: 'Shipping provider',
      dataIndex: ['extra', 'shipping_provider_name'],
      align: 'left',
      width: 100,
      tooltip: 'Click to edit.',
      render(dom, record) {
        return (
          <EditableCell
            dataType="select"
            defaultValue={record.extra?.shipping_provider_name || ''}
            style={{ marginRight: 0 }}
            fieldProps={{ style: { lineHeight: 1 } }}
            request={async (params) => {
              return getOrderShippingProviderList(params, {}, {}).then((res) =>
                res.data.map((x) => ({ value: x.name, label: x.name })),
              );
            }}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!record.entity_id) return Promise.reject();

              return updateOrderExtra(record.entity_id, {
                shipping_provider_name: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => cancelEdit?.());
            }}
          >
            {dom}
          </EditableCell>
        );
      },
    },
    {
      title: 'Shipping notes',
      dataIndex: ['extra', 'shipping_provider_change_notes'],
      align: 'left',
      width: 150,
    },
    {
      title: 'Parcel',
      dataIndex: ['shipping_imported_list'],
      align: 'left',
      width: 150,
      tooltip: 'Green indicates a processed shipping on Magento',
      render(dom, record) {
        return record.latest_shipping
          ? [record.latest_shipping].map((x) => {
              let cls = 'text-sm';
              if (x.mag_ship_id) {
                cls += ' c-green';
              }
              const title = x.carrier_code || x.title ? `${x.title} | ${x.carrier_code}` : '';
              return (
                <div key={x.id} className={cls} title={title}>
                  <a
                    href={`${getDictByCode(DictCode.MAG_ADMIN_URL_TRACKING)}${x.parcel_no}`}
                    target="_blank"
                    rel="noreferrer"
                    className={cls}
                  >
                    {x.parcel_no}
                  </a>
                </div>
              );
            })
          : null;
      },
    },
    {
      dataIndex: 'option_shipping',
      valueType: 'option',
      width: 40,
      fixed: 'right',
      render(dom, record) {
        return (
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            title="Edit shipping setting..."
            onClick={() => {
              setCurrentRow(record);
              setOpenUpdateOrderExtraModal(true);
            }}
          />
        );
      },
    },
  ];

  const expandedRowRender = (record: API.Order) => {
    return (
      <div style={{ paddingLeft: 150 }}>
        <ProTable<API.OrderItem>
          cardProps={{ bodyStyle: { padding: 0 } }}
          bordered
          columns={[
            {
              title: 'SKU',
              dataIndex: 'sku',
              width: 120,
              render: (dom, recordIn) => {
                return (
                  <Typography.Link
                    href={`/item/ean-all-summary?sku=${skuToItemId(recordIn.sku)}_`}
                    target="_blank"
                    copyable
                  >
                    {recordIn.sku}
                  </Typography.Link>
                );
              },
            },
            { title: 'EAN', dataIndex: ['item_ean', 'ean'], width: 150 },
            { title: 'Name', dataIndex: 'name' },
            { title: 'Ordered Qty', dataIndex: 'qty_ordered', align: 'right', width: 100 },
            {
              title: 'Net Price',
              dataIndex: ['price'],
              align: 'right',
              width: 80,
              render: (dom, recordIn) => nf2(recordIn?.price),
            },
            {
              title: 'BP / pcs',
              dataIndex: ['oi_idx', 'bp'],
              align: 'right',
              width: 80,
              render: (dom, recordIn) => {
                const idx = recordIn.oi_idx ?? {};
                const typeStr = idx.bp_type == 1 ? 'By stock booking' : idx.bp_type == 2 ? 'By latest IBO' : '';
                return (
                  <span title={`${typeStr}${idx.ibo_id ? ` #${idx.ibo_id}` : ''}`}>
                    {nf2(sn(idx.bp) * sn(idx.case_qty))}
                  </span>
                );
              },
            },
            {
              title: 'Weight (kg)',
              dataIndex: 'weight',
              align: 'right',
              width: 80,
              showSorterTooltip: false,
              render(dom, recordIn) {
                return nf3(sn(recordIn.weight) / 1000.0, false, true);
              },
              onCell: (recordIn) => {
                return {
                  className: recordIn.weight ? '' : 'bg-light-red1',
                };
              },
            },
            {
              title: 'Order Item ID',
              dataIndex: 'item_id',
              align: 'center',
              width: 90,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
            },
            {
              title: 'Product ID',
              dataIndex: 'product_id',
              align: 'center',
              width: 90,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
            },
            {
              title: 'Updated on',
              dataIndex: ['updated_at'],
              sorter: true,
              defaultSortOrder: 'descend',
              width: 110,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
              render: (dom, recordIn) => Util.dtToDMYHHMMTz(recordIn.updated_at),
            },
          ]}
          style={{ width: 1200 }}
          rowKey="item_id"
          headerTitle={false}
          search={false}
          options={false}
          pagination={false}
          size="small"
          dataSource={record.mag_order_items ?? []}
          columnEmptyText={''}
          rowClassName={(recordIn) => (recordIn?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        />
      </div>
    );
  };

  const loadStatusACList = () => {
    getOrderStatusACList().then((res) => setStatusACList(res));
  };

  useEffect(() => {
    loadStatusACList();
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_orders', {}));
  }, []);

  // hook by URL params change
  useEffect(() => {
    if (location.query?.hasWarnings) {
      // applying params in URL.
      searchFormRef.current?.setFieldValue('has_warnings', location.query?.hasWarnings ?? false);
      searchFormRef.current?.setFieldValue('status', 'processing');
      actionRef.current?.reload();
    }
  }, [location.query?.hasWarnings]);

  // hook by URL params change
  useEffect(() => {
    if (location.query?.extra_note1_status) {
      // ignore all filters and reload page
      searchFormRef.current?.resetFields();
      searchFormRef.current?.setFieldValue('extra_note1_status', location.query?.extra_note1_status);
      actionRef.current?.reload();
    }
  }, [location.query?.extra_note1_status]);

  const pendingCount = useMemo(() => {
    const pending = statusACList.find((x) => x.value == 'pending');
    return pending ? pending?.cnt : 0;
  }, [statusACList]);

  const [warningsDefCount, setWarningsDefCount] = useState<number>(0);

  // const [selectedRows, setSelectedRows] = useState<API.Order[]>([]);

  return (
    <PageContainer
      extra={
        pendingCount ? (
          <div style={{ position: 'absolute', top: 10, left: '50%', marginLeft: -150 }}>
            <Alert
              message={`WARNING: You have ${pendingCount} pending orders.`}
              type="error"
              style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
            />
          </div>
        ) : undefined
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name={'status'}
            label="Status"
            allowClear
            options={statusACList}
            placeholder={'Status'}
            width={'sm'}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={140} placeholder={'EAN'} />
          <ProFormText name={'entity_id'} label="Order ID" width={'xs'} placeholder={'Order ID'} />
          <ProFormText
            name={'increment_id_suffix'}
            label="Increment ID"
            width={150}
            placeholder={'Increment ID'}
            tooltip="Can search by suffix number. e.g. 137 -> 000000137 or EBDE000000137"
          />
          <ProFormText name={'name_address'} label="Name & Address" width={'xs'} placeholder={'Name / Address'} />
          <ProFormText name={'ebay_order_id'} label="Ebay/KL Order" width={150} placeholder={'Ebay/KL Order'} />
          <ProFormCheckbox name="has_warnings" label="Error?" tooltip="Show orders with wrong delivery address" />
          <ProFormSelect name="extra_note1_status" label="Note 1 Status" options={MagOrderExtraStatusOptions} />
        </ProForm>
      </Card>
      <ProTable<API.Order, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Orders List</span>{' '}
          </Space>
        }
        toolBarRender={() =>
          warningsDefCount
            ? [
                <Alert
                  key="warn-def"
                  message={`WARNING: ${warningsDefCount} invalid delivery addresses detected in red colored rows!`}
                  type="error"
                  style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
                />,
              ]
            : []
        }
        actionRef={actionRef}
        rowKey="entity_id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{ defaultPageSize: sn(Util.getSfValues('sf_orders_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_orders', searchFormValues);
          Util.setSfValues('sf_orders_p', params);

          setLoading(true);
          return getOrdersList(
            {
              ...params,
              with: 'warnings,warnings_def,extra,latestShipping,picklistDetails,ext',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              // calc weight info
              res.data.forEach((record) => {
                const map = { weight: 0, zeroCnt: 0 };
                record?.mag_order_items?.reduce((prev, x) => {
                  x.weight = sn(x.item_ean?.weight) * sn(x.qty_ordered);
                  if (x.weight) prev.weight += x.weight;
                  else prev.zeroCnt++;
                  return prev;
                }, map);
                record.weight_map = map;
              });

              if (currentRow?.entity_id) {
                setCurrentRow(res.data.find((x) => x.entity_id == currentRow.entity_id));
              }
              // validate selected rows
              /* if (selectedRows?.length) {
                const ids = res.data.map((x: API.Order) => x.entity_id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.entity_id) >= 0));
              } */
              setWarningsDefCount(res.data.reduce((prev, current) => prev + (current.warnings_def ? 1 : 0), 0));
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        onRow={(record) => {
          let cls = '',
            title = '';
          const warning_def = record.warnings_def || '';
          if (warning_def) {
            cls += ' reset-tds-bg bg-red';
            title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
          }
          return { title: title, className: cls };
        }}
        expandable={{
          expandedRowRender,
          expandRowByClick: false,
          indentSize: 200,
          rowExpandable(record) {
            return sn(record?.total_qty_ordered) > 0;
          },
        }}
        /* rowSelection={{
          columnWidth: 30,

          selectedRowKeys: selectedRows.map((x) => x.entity_id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }} */
        columnEmptyText=""
      />
      {/* {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Button
            type="primary"
            onClick={() => {
              handleVisibleUpdatePriceFormBulk(true);
            }}
          >
            Set Prices
          </Button>
          </FooterToolbar>} */}
      <UpdateAddressesModal
        modalVisible={openUpdateAddressModal}
        handleModalVisible={setOpenUpdateAddressModal}
        initialValues={currentRow}
        onSubmit={async (value) => actionRef.current?.reload()}
      />

      <UpdateOrderExtraFormModal
        modalVisible={openUpdateOrderExtraModal}
        handleModalVisible={setOpenUpdateOrderExtraModal}
        order={{
          shipping_description: currentRow?.shipping_description,
          weight: currentRow?.weight,
          entity_id: currentRow?.entity_id,
          shipping_imported_list: currentRow?.shipping_imported_list,
          latest_shipping: currentRow?.latest_shipping,
          sa_fullname: currentRow?.sa_fullname,
          sa_full: currentRow?.sa_full,
          sa_company: currentRow?.sa_company,
        }}
        values={{ ...currentRow?.extra }}
        onSubmit={async (values) => {
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default OrderList;
