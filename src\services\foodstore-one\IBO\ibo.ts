/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/ibo/list';

/** rule GET /api/ibo/list */
export async function getIboList(params: API.PageParams, sort: any, filter: any) {
  const newParams = { ...params };

  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...newParams,
      perPage: newParams.pageSize,
      page: newParams.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/ibo/list/{id} */
export async function updateIbo(data: API.Ibo, options?: { [key: string]: any }) {
  return request<API.Ibo>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/ibo/list */
export async function updateIboAll(
  data: { ibos: API.Ibo[]; mode?: 'stockStatus'; data?: Record<string, any> },
  options?: { [key: string]: any },
) {
  return request<API.Ibo>(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/ibo/list */
export async function addIbo(data: API.Ibo, options?: { [key: string]: any }) {
  return request<API.Ibo>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/ibo/list/{id} */
export async function deleteIbo(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** export as PDF GET /api/ibo/list/export-xls */
export async function exportIboListInXls(params?: any): Promise<{ url?: string }> {
  return request<API.BaseResult>(`${urlPrefix}/export-xls`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}
