import { getGdsnMessageItemXml } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import { Col, Modal, Row, Spin } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import XMLViewer from 'react-xml-viewer';

type XmlViewerProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  messageItemId?: number;
  mode?: string;
};
const XmlViewer: React.FC<XmlViewerProps> = (props) => {
  const { modalVisible, handleModalVisible, messageItemId, mode, ...rest } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const [xml, setXml] = useState<string>('');

  useEffect(() => {
    if (modalVisible && messageItemId) {
      setLoading(true);
      getGdsnMessageItemXml(messageItemId, { mode })
        .then((res) => {
          setXml(res?.xml || '');
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [modalVisible, messageItemId, mode]);

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={'85%'}
      style={{ top: 20 }}
      bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 160px)' }}
      maskClosable={false}
      footer={false}
      title={
        <Row gutter={32}>
          <Col>XML viewer</Col>
        </Row>
      }
    >
      <Spin spinning={loading}>
        <XMLViewer xml={xml || ''} />
      </Spin>
    </Modal>
  );
};

export default XmlViewer;
