/* eslint-disable */
import { request } from 'umi';

/** rule GET /api/rule */
export async function users(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>('/api/users-list', {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      // sort_fields: Object.keys(sort).join(','),
      // sort_dirs: Object.values(sort).join(','),
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** Put PUT /api/users */
export async function updateUser(data: API.UserListItem, options?: { [key: string]: any }) {
  return request<API.UserListItem>('/api/users/' + data.user_id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** Put PUT /api/users/{id}/change-password */
export async function changeUserPassword(data: API.UserListItem, options?: { [key: string]: any }) {
  return request<API.UserListItem>(`/api/users/${data.user_id}/change-password`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/users-list */
export async function addUser(data: API.UserListItem, options?: { [key: string]: any }) {
  return request<API.UserListItem>('/api/users-list', {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/rule */
export async function removeUser(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/users-list/' + (options ? options['user_id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
