/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/warehouse/picklist-detail';

/** GET /api/warehouse/picklist-detail */
export async function getPickListDetails(
  params: API.PageParams & { listMode?: 'byOrder' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.WarehousePicklistDetail & API.Order>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** get summary by SKU or EAN.
 * GET /api/warehouse/picklist-detail/summary */
export async function getPickListDetailsSummary(
  params?: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<API.ResultObject<API.WarehousePicklistDetail | API.Order>> {
  return request<API.BaseResult>(`${urlPrefix}/summary`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}




