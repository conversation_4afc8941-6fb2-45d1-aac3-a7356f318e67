INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`)
VALUES ('PL_MAX_ORDERS_IN_BOX', 'Picklist Grouping', '50', 'Picklist Orders Max')
     , ('PL_ORDERS_IN_BOX', 'Picklist Grouping', '8', 'Picklist Orders')
     , ('PL_MAX_ORDER_ITEMS_IN_BOX', 'Picklist Grouping', '60', 'Picklist Order Items Max')
;


ALTER TABLE `warehouse_picklist_detail`
    ADD COLUMN `box_step`       INT      NULL COMMENT 'Step of Box selection' AFTER `stock_stable_updated_on`,
    ADD COLUMN `box_no`         INT      NULL COMMENT 'Box No in box steps' AFTER `box_step`,
    ADD COLUMN `box_updated_on` DATETIME NULL COMMENT 'Box updated On' AFTER `box_no`,
    ADD COLUMN `box_updated_by` INT      NULL COMMENT 'Box creator' AFTER `box_updated_on`;
