import { filedName2OutputLabel } from '@/pages/Item/EanList/components/OpenAiTextGenerator';
import { getSysTextModuleACList } from '@/services/foodstore-one/Sys/text-module';
import { HighlightOutlined } from '@ant-design/icons';
import type { ProFormInstance, ProFormItemProps } from '@ant-design/pro-form';
import { ProFormItem } from '@ant-design/pro-form';
import type { MentionProps } from 'antd';
import { Button, Mentions, Space } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useRef, useState } from 'react';

type SMentionsSysProps = MentionProps & {
  formRef: React.MutableRefObject<ProFormInstance | undefined>;
  prepareList?: (res: any[]) => any[];
  generateHandler?: (name: string) => void;
  addSuffixHandler?: (name: string) => void;
  copyOutputHandler?: (name: string) => void;
} & ProFormItemProps;

const SMentionsSys = ({
  formRef,
  name,
  prepareList,
  generateHandler,
  addSuff<PERSON><PERSON>and<PERSON>,
  copyOutputHandler,
  disabled,
  ...rest
}: SMentionsSysProps) => {
  const refKey = useRef<string>();

  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [texts, setTexts] = useState<(API.SysTextModule & { label?: string; value?: string | number })[]>([]);

  const loadTextModules = (key: string) => {
    if (!key) {
      setTexts([]);
      return;
    }

    getSysTextModuleACList({ keyWord: key }).then((res) => {
      if (refKey.current !== key) return;
      setLoadingSearch(false);

      if (prepareList) {
        setTexts(prepareList(res));
      } else {
        setTexts(res);
      }
    });
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceSearchTextModule = useCallback(debounce(loadTextModules, 500), []);

  const onSearch = (search: string) => {
    refKey.current = search;
    setLoadingSearch(!!search);
    setTexts([]);

    debounceSearchTextModule(search);
  };

  return (
    <ProFormItem
      name={name}
      label={rest.label}
      style={{ marginBottom: 4 }}
      extra={
        <div style={{ position: 'absolute', right: 0, top: -28 }}>
          <Space size={8}>
            {addSuffixHandler ? (
              <Button
                size="small"
                onClick={() => addSuffixHandler?.(`${name}`)}
                title="Add pre-defined text"
                disabled={disabled}
              >
                S
              </Button>
            ) : null}
            {generateHandler ? (
              <Button
                className="btn-green"
                size="small"
                title="Generate AI text"
                onClick={() => generateHandler?.(`${name}`)}
                disabled={disabled}
              >
                G
              </Button>
            ) : null}
            {copyOutputHandler ? (
              <Button
                type="primary"
                ghost
                size="small"
                onClick={() => copyOutputHandler?.(`${name}`)}
                title={`Copy & past generated output of ${filedName2OutputLabel[name || '']}`}
                disabled={disabled}
                icon={<HighlightOutlined />}
              />
            ) : null}
          </Space>
        </div>
      }
    >
      <Mentions
        rows={rest.rows || 6}
        onSearch={onSearch}
        loading={loadingSearch}
        prefix={['#']}
        placeholder={rest.placeholder}
        options={
          texts.map((x) => ({
            key: `${x.number}`,
            value: x.text,
            label: <span>{x.label}</span>,
          })) as any
        }
        filterOption={(input, option) => {
          return `${option.key} - ${option.value}`.includes(input);
        }}
        onSelect={(option, prefix) => {
          const lastSelectedText = `${prefix}${option.value}`;
          if (lastSelectedText) {
            const text = formRef.current?.getFieldValue(name || 'na') || '';
            formRef.current?.setFieldValue(
              name || 'na',
              text.replaceAll(lastSelectedText, lastSelectedText.substring(1)),
            );
          }
        }}
        disabled={disabled}
      />
    </ProFormItem>
  );
};

export default SMentionsSys;
