import React, { Dispatch, SetStateAction, useEffect, useRef } from 'react';
import { Button, Card, Modal, Space, Tag } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import Util from '@/util';
import moment from 'moment';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProForm } from '@ant-design/pro-form';
import { ReloadOutlined } from '@ant-design/icons';
import { getSysLogList } from '@/services/foodstore-one/Sys/sys-log';
import SkuComp from '@/components/SkuComp';

type SysLogModalType = {
  searchParams: { ibo_pre_management_id?: number; offer_id?: number; categories?: string[] };
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const SysLogModal: React.FC<SysLogModalType> = (props) => {
  const { searchParams, modalVisible, handleModalVisible } = props;

  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.SysLog>[] = [
    /* {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>,
    }, */
    /* {
      title: 'Category',
      dataIndex: 'category',
      ellipsis: true,
      width: 150,
    }, */
    {
      title: 'Title',
      dataIndex: 'name',
      width: 270,
      ellipsis: true,
    },

    {
      title: 'Note',
      dataIndex: 'note',
      copyable: true,
      width: 550,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      copyable: true,
      width: 100,
      render(dom, entity, index, action, schema) {
        return <SkuComp sku={entity.item_ean?.sku} />;
      },
    },
    {
      title: 'EAN Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      copyable: true,
      width: 200,
    },
    {
      title: 'Date',
      dataIndex: 'created_on',
      width: 110,
      render: (dom, record) =>
        record.created_on ? (
          <div title={moment(record.created_on).fromNow()}>{Util.dtToDMYHHMM(record.created_on)}</div>
        ) : undefined,
      /* record.created_on ? (
          <div title={Util.dtToDMYHHMM(record.created_on)}>{moment(record.created_on).fromNow()}</div>
        ) : undefined, */
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
      width: 60,
    },
  ];

  const loadData = () => {
    actionRef.current?.reload();
  };

  useEffect(() => {
    if (modalVisible) {
      loadData();
    }
  }, [searchParams, modalVisible]);

  return (
    <Modal
      title={
        <Space size={12}>
          <div>User Action Logs</div>
        </Space>
      }
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="1400px"
      footer={false}
    >
      <Card bordered={false} bodyStyle={{ padding: '0 0 16px 0' }}>
        <ProForm layout="inline" formRef={formRef} submitter={false} size="large">
          <ProFormSelect
            name="statuses"
            placeholder="Select status"
            label="Status"
            mode="multiple"
            options={[
              { value: '', label: 'All' },
              { value: 'success', label: 'Success' },
              { value: 'started', label: 'Started' },
              { value: 'error', label: 'Error' },
              { value: 'processing', label: 'Processing' },
            ]}
            width={150}
          />
          <ProFormText name="nameLike" width={150} label="Name" />
          <ProFormText name="note" width={150} label="Note" />
          <Button htmlType="submit" type="primary" icon={<ReloadOutlined />} onClick={() => loadData()}>
            Search
          </Button>
        </ProForm>
      </Card>

      <ProTable<API.SysLog, API.PageParams>
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        search={false}
        options={false}
        params={{ with: 'user' }}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        actionRef={actionRef}
        request={async (params, sort, filter) => {
          const sfValues = formRef.current?.getFieldsValue();
          Util.setSfValues('sf_log', sfValues);
          return getSysLogList(
            {
              ...params,
              ...sfValues,
              categories: searchParams?.categories,
              ibo_pre_management_id: searchParams?.ibo_pre_management_id,
              with: 'itemEan,itemEan.eanTextDe,user',
            },
            Object.keys(sort)?.length < 1 ? { id: 'descend' } : sort,
            filter,
          );
        }}
        columns={columns}
        pagination={{ showSizeChanger: true, defaultPageSize: 20 }}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />
    </Modal>
  );
};

export default SysLogModal;
