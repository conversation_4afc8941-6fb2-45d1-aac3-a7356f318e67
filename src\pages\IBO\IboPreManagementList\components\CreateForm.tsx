import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { createIboPreManagement } from '@/services/foodstore-one/IBO/ibo-pre-management';
import { message } from 'antd';
import Util from '@/util';
import useSupplierOptions from '@/hooks/BasicData/useSupplierOptions';

const handleAdd = async (fields: API.IboPreManagement) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    const res = await createIboPreManagement(data);
    message.success('Added successfully');
    return res;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.IboPreManagement>;

export type CreateFormProps = {
  values?: Partial<API.IboPreManagement>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPreManagement) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  const { formElements } = useSupplierOptions(undefined, formRef, { required: true });

  return (
    <ModalForm
      title={'New Ibo Pre Management'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const res = await handleAdd(value as API.IboPreManagement);
        if (res) {
          handleModalVisible(false);
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(res);
        }
      }}
    >
      {formElements}
      <ProFormText name="note" label="Note" width="md" />
      <ProFormText name="inbound_no" label="InboundNo" width="md" />
      <ProFormText name="note_supplier" label="Notes (Supplier)" width="md" />
      <ProFormTextArea
        name="note_customer"
        label="Notes PreOrder"
        fieldProps={{ maxLength: 255 }}
        help="Max 255 chars."
      />
    </ModalForm>
  );
};

export default CreateForm;
