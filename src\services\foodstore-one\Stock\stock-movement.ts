/* eslint-disable */
import qs from 'qs';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/stock/stock-movement';

/** rule GET /api/stock/stock-movement */
export async function getStockMovementList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** rule GET /api/stock/stock-movement/ean/{id} */
export async function getStockByEanId(eanId?: number, options?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/ean/${eanId}`, {
    method: 'GET',
    params: {
      ...options,
    },
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res?.message);
}

/** put PUT /api/stock/stock-movement */
export async function updateStockMovement(data: API.StockMovement, options?: { [key: string]: any }) {
  return request<API.StockMovement>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}
/** put PUT /api/stock/stock-movement/import-ibo */
export async function ImportStockMovementIbo(data: API.StockMovement, options?: { [key: string]: any }) {
  return request<API.StockMovement>(`${urlPrefix}/import-ibo`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/stock/stock-movement */
export async function addStockMovement(data: API.StockMovement, options?: { [key: string]: any }) {
  return request<API.StockMovement>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/stock/stock-movement/{id} */
export async function deleteStockMovement(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
