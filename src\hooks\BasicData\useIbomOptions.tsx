import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion list of IBOM
 */
export default (defaultParams?: Record<string, any>, formRef?: React.MutableRefObject<ProFormInstance | undefined>) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [ibomOptions, setIbomOptions] = useState<DefaultOptionType[]>([]);
  // selected ibom
  const [ibom, setIbom] = useState<DefaultOptionType>();

  const searchIbomOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getIBOManagementACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setIbomOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  useEffect(() => {
    searchIbomOptions().then((res) => {
      const ibom_id = formRef?.current?.getFieldValue('ibom_id');
      if (ibom_id) {
        const found = res.find((x) => x.id == ibom_id);
        setIbom(found);
      }
    });
  }, [formRef, searchIbomOptions]);

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        name="ibom_id"
        label={'IBOM'}
        placeholder="Please select IBOM"
        mode="single"
        showSearch
        options={ibomOptions}
        request={(params) => {
          return searchIbomOptions(params);
        }}
        fieldProps={{
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          onChange: (value, option) => {
            setIbom(option as any);
          },
        }}
        width={130}
        disabled={loading}
      />
    );
  }, [ibomOptions, loading, searchIbomOptions]);

  return { ibomOptions, searchIbomOptions, loading, ibom, setIbom, formElements };
};
