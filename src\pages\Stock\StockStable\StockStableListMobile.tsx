import { But<PERSON>, Col, message, Row, Space, Typography } from 'antd';
import React, { useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';

import {
  getStockStable,
  stockStableBulkMove2TargetByName,
  stockStableBulkMoveAll,
} from '@/services/foodstore-one/Stock/stock-stable';
import Util, { ni, sEllipsed, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import _ from 'lodash';
import { IRouteComponentProps } from 'umi';
import StockStableQtyUpdateFormModal from './components/StockStableQtyUpdateFormModal';
import StockStableUpdateExpForm from '@/pages/Item/EanList/components/StockStableUpdateExpForm';
import SkuComp from '@/components/SkuComp';
import WarehouseLocationSelector from '@/components/WarehouseLocationSelector';
import ProList from '@ant-design/pro-list';
import EanFilesComp from '@/components/EanFilesComp';
import styles from './StockStableListMobile.less';
// import StockStableBulkMoveFormModal from './components/StockStableBulkMoveFormModal';

export type SearchFormValueType = Partial<API.StockStable>;

const StockStableListMobile: React.FC<IRouteComponentProps> = (props) => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<SearchFormValueType & { openParent?: boolean }>();
  const [selectedRows, setSelectedRows] = useState<API.StockStable[]>([]);

  // bulk stock movements
  // const [openBulkStockMoveForm, setOpenBulkStockMoveForm] = useState<boolean>(false);
  // const bulkStockMoveFormRef = useRef<ProFormInstance>();

  // Qty Edit modal
  const [openStockStableQtyUpdateFormModal, setOpenStockStableQtyUpdateFormModal] = useState<boolean>(false);

  const [expDateModalVisible, handleExpDateModalVisible] = useState<boolean>(false);

  const handleBulkMove = (locationName: string, currentRow?: API.StockStable, cb?: any) => {
    if (!locationName) {
      message.error('Please select Warehouse Location!');
      return;
    }

    const newRows = [...selectedRows];

    // If clicked on current row, we should select this row first.
    if (currentRow) {
      const foundIndex = newRows.findIndex((x) => x.id == currentRow.id);
      if (foundIndex >= 0) {
        // No need to remove.
        // newRows.splice(foundIndex, 1);
      } else {
        newRows.push({ ...currentRow });
      }
      setSelectedRows(newRows);
    }

    // No selection, we perform "Move all"
    if (newRows.length < 1) {
      const currentLocationName = searchFormRef.current?.getFieldValue('wl_name');
      if (!currentLocationName) {
        message.error('Please select Location to filter stocks');
        return;
      }

      const hide = message.loading(`Moving all stocks...`, 0);
      stockStableBulkMoveAll(currentLocationName, locationName)
        .then((res) => {
          message.success('Moved successfully.');
          setSelectedRows([]);
          actionRef.current?.reload();
          cb?.();
        })
        .catch(Util.error)
        .finally(hide);
    } else {
      const hide = message.loading('Moving stocks...', 0);
      stockStableBulkMove2TargetByName(
        newRows.map((x) => ({
          id: x.id,
          ean_id: x.ean_id,
          wl_id: x.wl_id,
          exp_date: x.exp_date,
          ibo_id: x.ibo_id,
          piece_qty: x.piece_qty,
          box_qty: x.box_qty,
        })),
        locationName,
      )
        .then((res) => {
          message.success('Moved successfully.');
          setSelectedRows([]);
          cb?.();
          actionRef.current?.reload();
        })
        .catch(Util.error)
        .finally(hide);
    }
  };

  return (
    <div style={{ margin: -24 }} className={styles.ssListMobile}>
      <ProList<API.StockStable, API.PageParams>
        headerTitle={
          <Space size={48}>
            <div>{props.route.name}</div>
            <div>
              <WarehouseLocationSelector
                defaultValue={Util.getSfValues('sf_stock_stable_mobile')?.wl_name || ''}
                onChange={(value) => {
                  if (value.length >= 4) {
                    searchFormRef.current?.setFieldValue('wl_name', value);
                    actionRef.current?.reload();
                  }
                }}
              />
            </div>

            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              size="large"
              style={{ display: 'none' }}
              initialValues={Util.getSfValues('sf_stock_stable_mobile')}
              submitter={{
                searchConfig: { submitText: 'Search' },
                submitButtonProps: { loading, htmlType: 'submit' },
                onSubmit: (values) => actionRef.current?.reload(),
                onReset: (values) => actionRef.current?.reload(),
              }}
            >
              <ProFormText
                name={'wl_name'}
                label="Location"
                width={'sm'}
                placeholder={'Location. e.g. B0001'}
                requiredMark
              />
            </ProForm>
          </Space>
        }
        rowKey="id"
        actionRef={actionRef as any}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          if (!searchFormValues.wl_name) {
            return Promise.resolve([]);
          }

          Util.setSfValues('sf_stock_stable_mobile', searchFormValues);

          if (searchFormValues.exp_date_start) {
            searchFormValues.exp_date_start = Util.dtToYMD(searchFormValues.exp_date_start);
          } else {
            searchFormValues.exp_date_start = undefined;
          }
          if (searchFormValues.exp_date_end) {
            searchFormValues.exp_date_end = Util.dtToYMD(searchFormValues.exp_date_end);
          } else {
            searchFormValues.exp_date_end = undefined;
          }
          setLoading(true);
          return getStockStable(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              sort_mode: 'stock_stable_mobile',
            },
            sort,
            filter,
          )
            .then((res) => {
              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.StockStable) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        grid={{ gutter: 16, column: 3 }}
        rowSelection={{
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
        onItem={(entity) => {
          return {
            className: selectedRows.map((x) => `${x.id}`).includes(`${entity.id}`) ? 'ant-pro-checkcard-checked' : '',
          };
        }}
        tableAlertRender={false}
        pagination={{ defaultPageSize: 60 }}
        itemCardProps={{ bodyStyle: { padding: 0 }, cover: true, size: 'small' }}
        metas={{
          type: {},
          title: {
            fieldProps: { style: { padding: 0 } },
            render(dom, entity) {
              return null;
            },
          },
          actions: {
            cardActionProps: 'actions',
            render: (dom, entity, index, action) => {
              return (
                <Row style={{ display: 'flex' }} wrap={false}>
                  <Col
                    span={6}
                    style={{ alignSelf: 'center' }}
                    className="cursor-pointer"
                    onClick={() => {
                      setSelectedRows((prev) => {
                        const newRows = [...prev];
                        const foundIndex = newRows.findIndex((x) => x.id == entity.id);
                        if (foundIndex >= 0) {
                          newRows.splice(foundIndex, 1);
                        } else {
                          newRows.push({ ...entity });
                        }

                        return newRows;
                      });
                    }}
                  >
                    {entity.warehouse_location?.name}
                  </Col>
                  <Col span={8}>
                    <Button
                      type="link"
                      onClick={() => {
                        /* setCurrentRow(entity);
                        setOpenStockStableQtyUpdateFormModal(true); */
                      }}
                    >
                      Inventory
                    </Button>
                  </Col>
                  <Col span={10}>
                    <WarehouseLocationSelector
                      disableAutoClose
                      showBodyScroll
                      onChange={function (value: string): void {
                        // On change event
                      }}
                      buttonRender={(value) => (
                        <Button
                          type="link"
                          onClick={() => {
                            console.log('Clicked on Move Location');
                            setSelectedRows((prev) => {
                              const newRows = [...prev];

                              // If clicked on current row, we should select this row first.
                              const foundIndex = newRows.findIndex((x) => x.id == entity.id);
                              if (foundIndex >= 0) {
                                // No need to remove.
                                // newRows.splice(foundIndex, 1);
                              } else {
                                newRows.push({ ...entity });
                              }

                              return newRows;
                            });
                          }}
                        >
                          Move Location
                        </Button>
                      )}
                      actionsRender={(value, setOpen) => {
                        return (
                          <>
                            <Button
                              key="btn"
                              type="primary"
                              className="btn-green"
                              disabled={!(value && (value.length == 4 || value.length == 5))}
                              style={{
                                width: 180,
                                height: 140,
                                fontSize: 56,
                                borderRadius: 4,
                                marginRight: 48,
                              }}
                              onClick={() => {
                                handleBulkMove(value, { ...entity }, () => setOpen?.(false));
                              }}
                            >
                              Move
                            </Button>
                          </>
                        );
                      }}
                    />

                    {/* <Button
                      key="btn"
                      type="link"
                      onClick={() => {
                        setCurrentRow(entity);

                        // bulk action
                        setSelectedRows((prev) => {
                          const newRows = [...prev];
                          const foundIndex = newRows.findIndex((x) => x.id == entity.id);
                          if (foundIndex >= 0) {
                            // No need to remove.
                            // newRows.splice(foundIndex, 1);
                          } else {
                            newRows.push({ ...entity });
                          }

                          return newRows;
                        });
                        setOpenBulkStockMoveForm(true);
                      }}
                    >
                      Move Location
                    </Button> */}
                  </Col>
                </Row>
              );
            },
          },
          content: {
            fieldProps: { style: { padding: 0 } },
            render: (dom, entity, index) => {
              return (
                <div style={{ padding: '0 8px' }}>
                  <Row wrap={false} gutter={12}>
                    <Col flex="90px">
                      <div style={{ width: 80, height: 80 }}>
                        <EanFilesComp files={entity.item_ean?.files} width={80} />
                      </div>
                      <div>
                        <SkuComp sku={entity.item_ean?.sku} noCopyable={true} />
                      </div>
                    </Col>
                    <Col flex="auto">
                      <Row style={{ height: 70 }}>
                        <Col span={24}>
                          {sEllipsed(entity.item_ean?.ean_text_de?.name ?? entity.item_ean?.item?.name ?? '', 75)}
                        </Col>
                      </Row>
                      <Row wrap={false} gutter={8}>
                        <Col flex="100px" style={{ paddingTop: 13, fontSize: 13 }}>
                          <Space direction="vertical" size={0} style={{ textAlign: 'left', width: '100%' }}>
                            <Typography.Text style={{ color: entity.item_ean?.is_single ? 'blue' : '#000' }}>
                              {entity.parent_item_ean?.ean}
                            </Typography.Text>

                            {!entity.item_ean?.is_single && (
                              <Typography.Text style={{ color: 'blue' }}>{entity.item_ean?.ean}</Typography.Text>
                            )}
                          </Space>
                        </Col>
                        <Col flex="auto">
                          <Space direction="vertical" size={0} style={{ width: '100%' }}>
                            <div
                              className="text-md bold cursor-pointer"
                              style={{ fontSize: 24 }}
                              onClick={(e) => {
                                setCurrentRow(entity);
                                setOpenStockStableQtyUpdateFormModal(true);
                                e.stopPropagation();
                              }}
                            >
                              {entity.case_qty == 1
                                ? `${ni(entity.piece_qty)}`
                                : `${ni(entity.box_qty)} x ${entity.case_qty}`}
                            </div>
                            <div style={{ fontSize: 12 }}>
                              (
                              <span
                                style={{ verticalAlign: 'middle' }}
                                className="cursor-pointer"
                                onClick={(e) => {
                                  setCurrentRow({ ...entity });
                                  handleExpDateModalVisible(true);
                                  e.stopPropagation();
                                }}
                              >
                                {Util.dtToDMY(entity?.exp_date)}
                              </span>
                              )
                            </div>
                          </Space>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </div>
              );
            },
          },
        }}
      />

      <div style={{ marginLeft: 24, marginTop: -60 }}>
        <WarehouseLocationSelector
          disableAutoClose
          showBodyScroll
          onChange={function (value: string): void {
            // On change event
          }}
          buttonRender={(value) => (
            <Button type="primary" size="large">
              {selectedRows?.length > 0 ? 'Move Selected' : 'Move All'}
            </Button>
          )}
          actionsRender={(value, setOpen) => {
            return (
              <>
                <Button
                  key="btn"
                  type="primary"
                  className="btn-green"
                  disabled={!(value && (value.length == 4 || value.length == 5))}
                  style={{
                    width: 180,
                    height: 140,
                    fontSize: 56,
                    borderRadius: 4,
                    marginRight: 48,
                  }}
                  onClick={() => {
                    handleBulkMove(value, undefined, () => setOpen?.(false));
                  }}
                >
                  Move
                </Button>
              </>
            );
          }}
        />
      </div>

      {/* NOT USED <StockStableBulkMoveFormModal
        stockStableList={selectedRows}
        modalVisible={openBulkStockMoveForm}
        handleModalVisible={setOpenBulkStockMoveForm}
        onSubmit={(value) => {
          setSelectedRows([]);
          actionRef.current?.reload();
        }}
      /> */}

      {/* <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      /> */}

      {/* <SelectIboModal
        modalVisible={openSelectIboModalVisible}
        handleModalVisible={setOpenSelectIboModalVisible}
        initialValue={{
          id: sn(currentRow?.id),
          ibo_id: currentRow?.ibo_id,
          itemEan: currentRow?.item_ean,
        }}
        selectCallback={(data: API.StockStable) => {
          actionRef.current?.reload();
          setOpenSelectIboModalVisible(false);
        }}
      /> */}

      {/* <ExportPdfStockStableSettingFormModal
        offer={currentRow}
        modalVisible={openExportPdfForm}
        handleModalVisible={setOpenExportPdfForm}
        getParentParams={() => {
          return {
            ssIds: selectedRows?.length ? selectedRows.map((x) => x.id) : null,
            ...searchFormRef.current?.getFieldsValue(),
          };
        }}
      /> */}

      <StockStableQtyUpdateFormModal
        initialValues={currentRow}
        modalVisible={openStockStableQtyUpdateFormModal}
        handleModalVisible={setOpenStockStableQtyUpdateFormModal}
        onSubmit={(__) => {
          actionRef.current?.reload();
          setOpenStockStableQtyUpdateFormModal(false);
        }}
        formSize="large"
        showNumpad
        isMobile
      />

      <StockStableUpdateExpForm
        modalVisible={expDateModalVisible}
        handleModalVisible={handleExpDateModalVisible}
        ean={currentRow?.item_ean}
        initialData={currentRow || {}}
        onSubmit={async () => {
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleExpDateModalVisible(false);
        }}
        formSize="large"
      />
    </div>
  );
};

export default StockStableListMobile;
