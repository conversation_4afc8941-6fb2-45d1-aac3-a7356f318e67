<?php

declare(strict_types=1);

namespace App\Repository\BasicData;

use App\Models\Sys\SysImportRwColMap;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class SysImportRwColMapRepository extends BaseRepositoryORM
{
    public function getQuerySysImportRwColMapsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSysImportRwColMapsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySysImportRwColMapsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SysImportRwColMap::query();
    }
}