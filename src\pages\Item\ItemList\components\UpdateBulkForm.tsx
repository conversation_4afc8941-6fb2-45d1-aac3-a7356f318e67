import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import _ from 'lodash';
import { updateItemAll } from '@/services/foodstore-one/Item/item';
import { getTrademarkList } from '@/services/foodstore-one/BasicData/trademark';

export type FormValueType = {
  selectedRows: API.Item[];
};

export type UpdateBulkFormProps = {
  initialValues?: FormValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateBulkForm: React.FC<UpdateBulkFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      title={'Batch update item trademark'}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      modalProps={{
        maskClosable: false,
      }}
      formRef={formRef}
      onFinish={async (value) => {
        if (formRef.current?.isFieldsTouched()) {
          const hide = message.loading('Bulk updating item categories...');
          const data = {
            items: props.initialValues?.selectedRows.map((item) => ({
              id: item.id,
              trademark_id: value.trademark_id ?? null,
            })),
          };

          try {
            await updateItemAll(data as any);
            hide();
            message.success('Updated successfully.');
            props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          } catch (error) {
            hide();
            Util.error(error);
            return false;
          }
        } else {
          props.handleModalVisible(false);
        }
        return true;
      }}
    >
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSelect
          showSearch
          placeholder="Select a trademark"
          request={async (params) => {
            const res = await getTrademarkList({ ...params, pageSize: 100, current: 1 }, { name: 'ascend' }, {});
            if (res && res.data) {
              const tmp = res.data.map((x: API.Trademark) => ({
                label: `${x.name}`,
                value: x.id,
              }));
              return tmp;
            }
            return [];
          }}
          proFieldProps={{}}
          width="md"
          name="trademark_id"
          label="Trademark"
          colProps={{ xl: 6 }}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateBulkForm;
