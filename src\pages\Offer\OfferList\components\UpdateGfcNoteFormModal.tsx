import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateOffer } from '@/services/foodstore-one/Offer/offer';
import Util, { sn } from '@/util';
import moment from 'moment';
import { DT_FORMAT_DMY } from '@/constants';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOffer(sn(fields.id), fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Offer>;

export type UpdateGfcNoteFormModalProps = {
  initialValues?: Partial<API.Offer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Offer) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateGfcNoteFormModal: React.FC<UpdateGfcNoteFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  const handleClickOnShortcut = (mode: number) => {
    const oldNote = formRef.current?.getFieldValue('gfc_note') ?? '';
    let newNote = oldNote;
    switch (mode) {
      case 1: {
        newNote = `${moment().format(DT_FORMAT_DMY)}:\n\n` + oldNote;
        break;
      }
      case 2: {
        newNote = `${moment().format(DT_FORMAT_DMY)}: Mail sent\n\n` + oldNote;
        break;
      }
    }

    formRef.current?.setFieldValue('gfc_note', newNote);
  };

  return (
    <ModalForm
      title={'Update GFC Note'}
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: props.initialValues?.id,
        };
        const success = await handleUpdate(data);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormTextArea
        width="lg"
        name="gfc_note"
        label={
          <Space size={24}>
            <span>GFC Notes</span>
            <div>
              <Button type="default" size="small" onClick={(e) => handleClickOnShortcut(1)}>
                1
              </Button>
              <Button type="default" size="small" onClick={(e) => handleClickOnShortcut(2)}>
                2
              </Button>
            </div>
          </Space>
        }
        fieldProps={{ rows: 8 }}
      />
    </ModalForm>
  );
};

export default UpdateGfcNoteFormModal;
