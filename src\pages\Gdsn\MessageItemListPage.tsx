import { <PERSON><PERSON>, <PERSON>, <PERSON>, Row } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2, ni, sn } from '@/util';

import { DEFAULT_PER_PAGE_PAGINATION, DT_FORMAT_DMY } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { getGdsnMessageItemList } from '@/services/foodstore-one/Item/ean';
import XmlViewer from './components/XmlViewer';
import useGdsnPageContainerTitle from './components/useGdsnPageContainerTitle';

const MessageItemListPage: React.FC<{ route?: any }> = (props) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<Partial<API.GdsnMessageItem>>>();

  const [currentRow, setCurrentRow] = useState<API.GdsnMessageItem>();
  const [showXmlDetail, setShowXmlDetail] = useState<boolean>(false);

  const columns: ProColumns<API.GdsnMessageItem>[] = [
    {
      title: 'Transaction ID',
      dataIndex: 'transaction_id',
      sorter: false,
      ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a
            href={`${API_URL_EAN}/api/download?type=xml&key=${record.gdsn_message?.file_url}`}
            target="_blank"
            rel="noreferrer"
          >
            {dom}
          </a>
        );
      },
      width: 200,
    },
    {
      title: 'Content owner',
      dataIndex: 'content_owner',
      sorter: true,
      // ellipsis: true,
      copyable: true,
      width: 120,
    },
    {
      title: 'GTIN',
      dataIndex: 'gtin',
      sorter: true,
      tooltip: 'Single EAN',
      copyable: true,
      width: 130,
    },
    {
      title: 'Parent GTIN',
      dataIndex: 'parent_gtin',
      sorter: true,
      tooltip: 'Pkg. EAN',
      copyable: true,
      width: 130,
    },
    {
      title: 'Case Qty',
      dataIndex: 'case_qty',
      sorter: true,
      align: 'center',
      width: 80,
    },
    {
      title: 'Provider GLN',
      dataIndex: 'provider_gln',
      sorter: true,
      // ellipsis: true,
      copyable: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'Name',
      dataIndex: 'desc',
      sorter: true,
      // ellipsis: true,
      copyable: true,
      width: 250,
      className: 'text-sm',
    },
    {
      title: 'Provider Name',
      dataIndex: 'provider_name',
      sorter: true,
      // ellipsis: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'Category',
      dataIndex: 'gpc_category_name',
      sorter: true,
      ellipsis: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'Ref Item',
      dataIndex: 'ref_item_gtin',
      sorter: true,
      ellipsis: true,
      copyable: true,
      className: 'text-sm bl2 b-gray',
      width: 100,
    },
    {
      title: 'Ref Type',
      dataIndex: 'ref_item_type_code',
      sorter: true,
      ellipsis: true,
      width: 100,
      className: 'text-xs',
    },
    {
      title: 'State',
      dataIndex: 'item_state',
      ellipsis: true,
      width: 100,
      className: 'text-xs',
    },
    {
      title: 'Brand',
      dataIndex: 'brand',
      width: 120,
      className: 'text-sm bl2 b-gray',
    },
    {
      title: 'Trademark',
      dataIndex: 'trademark_name',
      // ellipsis: true,
      width: 150,
    },
    {
      title: 'Maker',
      dataIndex: 'maker_name',
      // ellipsis: true,
      width: 150,
    },

    {
      title: 'Child Count',
      dataIndex: 'children_cnt',
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.children_cnt);
      },
    },
    {
      title: 'Item base',
      dataIndex: 'item_base',
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.item_base);
      },
    },
    {
      title: 'Item base unit',
      dataIndex: 'item_base_unit',
      align: 'center',
      width: 60,
      className: 'text-sm',
    },
    {
      title: 'W',
      dataIndex: 'width',
      align: 'right',
      width: 70,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.width);
      },
    },
    {
      title: 'H',
      dataIndex: 'height',
      align: 'right',
      width: 70,
      className: 'text-sm',
      render(__, record) {
        return ni(record?.height);
      },
    },
    {
      title: 'D',
      dataIndex: 'depth',
      align: 'right',
      width: 70,
      className: 'text-sm',
      render(__, record) {
        return ni(record?.depth);
      },
    },

    {
      title: 'Net weight',
      dataIndex: 'net_weight',
      align: 'right',
      width: 80,
      className: 'text-sm',
      render(__, record) {
        return nf2(record?.net_weight);
      },
    },
    {
      title: 'Gross weight',
      dataIndex: 'gross_weight',
      align: 'right',
      width: 80,
      className: 'text-sm',
      render(__, record) {
        return nf2(record?.gross_weight);
      },
    },
    {
      title: 'Contact Name',
      dataIndex: 'contact_name',
      // ellipsis: true,
      width: 150,
      className: 'text-sm bl2 b-gray',
    },
    {
      title: 'Contact Addr.',
      dataIndex: 'contact_address',
      // ellipsis: true,
      width: 200,
      className: 'text-sm',
    },
    {
      title: 'Msg ID',
      dataIndex: 'gdsn_message_id',
      align: 'center',
      width: 60,
      className: 'text-sm',
    },
    {
      title: 'Last change',
      sorter: true,
      tooltip: 'Last sync changed date',
      dataIndex: 'sync_last_change_dt',
      valueType: 'dateTime',
      search: false,
      width: 90,
      fieldProps: {
        format: DT_FORMAT_DMY,
      },
    },
    {
      title: 'Sync effective date',
      sorter: true,
      dataIndex: 'sync_effective_dt',
      valueType: 'dateTime',
      search: false,
      width: 90,
      className: 'text-sm',
      fieldProps: {
        format: DT_FORMAT_DMY,
      },
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 50,
      render: (_, record) => (
        <Row gutter={8}>
          <Col flex={'30px'}>
            <Button
              type="link"
              size="small"
              title="Show item information in xml..."
              onClick={() => {
                setCurrentRow(record);
                setShowXmlDetail(true);
              }}
            >
              XML
            </Button>
          </Col>
        </Row>
      ),
    },
  ];

  const { pageTitle } = useGdsnPageContainerTitle(props.route);

  return (
    <PageContainer title={pageTitle}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.GdsnMessageItem>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_gdsn_message_items', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          {/* <ProFormSelect
            key="Kind"
            name="kind"
            label="Kind"
            width={300}
            placeholder="Kind"
            fieldProps={{ dropdownMatchSelectWidth: false }}
            formItemProps={{ style: { marginBottom: 0 } }}
            options={GdsnMessageTypeOptions}
          /> */}
          {/* <ProFormText name={'remoteParty'} label="Remote party" width={'sm'} placeholder={'Remote party'} /> */}
          <ProFormText name={'gtin'} label="GTIN" width={'sm'} placeholder={'GTIN'} tooltip="single EAN" />
          <ProFormText
            name={'parent_gtin'}
            label="Parent GTIN"
            width={'sm'}
            placeholder={'Parent GTIN'}
            tooltip="Pkg. EAN"
          />
          <ProFormText name={'provider_gln'} label="Provider GLN" width={'sm'} placeholder={'GLN'} />
          <ProFormText name={'provider_name'} label="Provider name" width={'sm'} placeholder={'Provider name'} />
        </ProForm>
      </Card>
      <ProTable<API.GdsnMessageItem, API.PageParams>
        headerTitle={'GDSN items list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_gdsn_message_items_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_gdsn_message_items', sfValues);
          Util.setSfValues('sf_gdsn_message_items_p', params);

          const newSort = { ...sort };
          return getGdsnMessageItemList(
            {
              ...sfValues,
              ...params,
              with: 'gdsnMessage.file_url',
            },
            newSort,
            filter,
          );
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText=""
      />

      <XmlViewer messageItemId={currentRow?.id} modalVisible={showXmlDetail} handleModalVisible={setShowXmlDetail} />
    </PageContainer>
  );
};

export default MessageItemListPage;
