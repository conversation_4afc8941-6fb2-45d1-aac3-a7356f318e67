import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addItemSupplier } from '@/services/foodstore-one/Item/item-supplier';
import { message } from 'antd';
import Util from '@/util';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import { getItemList } from '@/services/foodstore-one/Item/item';

const handleAdd = async (fields: API.ItemSupplier) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addItemSupplier(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error('Adding failed, please try again!');
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.ItemSupplier>;

export type CreateFormProps = {
  values?: Partial<API.ItemSupplier>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.ItemSupplier) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Item Supplier'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.ItemSupplier);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        showSearch
        placeholder="Select an Item"
        request={async (params) => {
          const res = await getItemList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Item) => ({
              label: `${x.id} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        rules={[
          {
            required: true,
            message: 'Item is required',
          },
        ]}
        width="md"
        name="item_id"
        label="Item"
      />
      <ProFormSelect
        showSearch
        placeholder="Select a supplier"
        request={async (params) => {
          const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Supplier) => ({
              label: `${x.supplier_no} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        rules={[
          {
            required: true,
            message: 'Supplier is required',
          },
        ]}
        width="md"
        name="supplier_id"
        label="Supplier"
      />
    </ModalForm>
  );
};

export default CreateForm;
