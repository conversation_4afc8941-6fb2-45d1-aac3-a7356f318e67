/*
SQLyog Ultimate v13.1.1 (64 bit)
MySQL - 10.4.19-MariaDB : Database
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

/*Table structure for table `sys_country` */

DROP TABLE IF EXISTS `sys_country`;

CREATE TABLE `sys_country` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `code` varchar(2) NOT NULL COMMENT 'Country code',
  `iso3_code` varchar(3) DEFAULT NULL COMMENT 'ISO 3166 Alpha03',
  `name` varchar(255) NOT NULL COMMENT 'Country name',
  `dial_code` varchar(255) NOT NULL COMMENT 'Prefix',
  `gs1_code` varchar(3) DEFAULT NULL COMMENT 'GS1 Code-ISO 3166',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_sys_country_code` (`code`),
  KEY `IDX_sys_country_gs1_code` (`gs1_code`)
) ENGINE=InnoDB AUTO_INCREMENT=242 DEFAULT CHARSET=utf8 COMMENT='country table';

/*Data for the table `sys_country` */

insert  into `sys_country`(`id`,`code`,`iso3_code`,`name`,`dial_code`,`gs1_code`) values 
(1,'IL','ISR','Israel','+972','376'),
(2,'AF','AFG','Afghanistan','+93','004'),
(3,'AL','ALB','Albania','+355','008'),
(4,'DZ','DZA','Algeria','+213','012'),
(5,'AS','ASM','AmericanSamoa','+1 684','016'),
(6,'AD','AND','Andorra','+376','020'),
(7,'AO','AGO','Angola','+244','024'),
(8,'AI','AIA','Anguilla','+1 264','660'),
(9,'AG','ATG','Antigua and Barbuda','+1268','028'),
(10,'AR','ARG','Argentina','+54','032'),
(11,'AM','ARM','Armenia','+374','051'),
(12,'AW','ABW','Aruba','+297','533'),
(13,'AU','AUS','Australia','+61','036'),
(14,'AT','AUT','Austria','+43','040'),
(15,'AZ','AZE','Azerbaijan','+994','031'),
(16,'BS','BHS','Bahamas','+1 242','044'),
(17,'BH','BHR','Bahrain','+973','048'),
(18,'BD','BGD','Bangladesh','+880','050'),
(19,'BB','BRB','Barbados','+1 246','052'),
(20,'BY','BLR','Belarus','+375','112'),
(21,'BE','BEL','Belgium','+32','056'),
(22,'BZ','BLZ','Belize','+501','084'),
(23,'BJ','BEN','Benin','+229','204'),
(24,'BM','BMU','Bermuda','+1 441','060'),
(25,'BT','BTN','Bhutan','+975','064'),
(26,'BA','BIH','Bosnia and Herzegovina','+387','070'),
(27,'BW','BWA','Botswana','+267','072'),
(28,'BR','BRA','Brazil','+55','076'),
(29,'IO','IOT','British Indian Ocean Territory','+246','086'),
(30,'BG','BGR','Bulgaria','+359','100'),
(31,'BF','BFA','Burkina Faso','+226','854'),
(32,'BI','BDI','Burundi','+257','108'),
(33,'KH','KHM','Cambodia','+855','116'),
(34,'CM','CMR','Cameroon','+237','120'),
(35,'CA','CAN','Canada','+1','124'),
(36,'CV','CPV','Cape Verde','+238','132'),
(37,'KY','CYM','Cayman Islands','+ 345','136'),
(38,'CF','CAF','Central African Republic','+236','140'),
(39,'TD','TCD','Chad','+235','148'),
(40,'CL','CHL','Chile','+56','152'),
(41,'CN','CHN','China','+86','156'),
(42,'CX','CXR','Christmas Island','+61','162'),
(43,'CO','COL','Colombia','+57','170'),
(44,'KM','COM','Comoros','+269','174'),
(45,'CG','COG','Congo','+242','178'),
(46,'CK','COK','Cook Islands','+682','184'),
(47,'CR','CRI','Costa Rica','+506','188'),
(48,'HR','HRV','Croatia','+385','191'),
(49,'CU','CUB','Cuba','+53','192'),
(50,'CY','CYP','Cyprus','+537','196'),
(51,'CZ','CZE','Czech Republic','+420','203'),
(52,'DK','DNK','Denmark','+45','208'),
(53,'DJ','DJI','Djibouti','+253','262'),
(54,'DM','DMA','Dominica','+1 767','212'),
(55,'DO','DOM','Dominican Republic','+1 849','214'),
(56,'EC','ECU','Ecuador','+593','218'),
(57,'EG','EGY','Egypt','+20','818'),
(58,'SV','SLV','El Salvador','+503','222'),
(59,'GQ','GNQ','Equatorial Guinea','+240','226'),
(60,'ER','ERI','Eritrea','+291','232'),
(61,'EE','EST','Estonia','+372','233'),
(62,'ET','ETH','Ethiopia','+251','231'),
(63,'FO','FRO','Faroe Islands','+298','234'),
(64,'FJ','FJI','Fiji','+679','242'),
(65,'FI','FIN','Finland','+358','246'),
(66,'FR','FRA','France','+33','250'),
(67,'GF','GUF','French Guiana','+594','254'),
(68,'PF','PYF','French Polynesia','+689','258'),
(69,'GA','GAB','Gabon','+241','266'),
(70,'GM','GMB','Gambia','+220','270'),
(71,'GE','GEO','Georgia','+995','268'),
(72,'DE','DEU','Deutschland','+49','276'),
(73,'GH','GHA','Ghana','+233','288'),
(74,'GI','GIB','Gibraltar','+350','292'),
(75,'GR','GRC','Greece','+30','300'),
(76,'GL','GRL','Greenland','+299','304'),
(77,'GD','GRD','Grenada','+1 473','308'),
(78,'GP','GLP','Guadeloupe','+590','312'),
(79,'GU','GUM','Guam','+1 671','316'),
(80,'GT','GTM','Guatemala','+502','320'),
(81,'GN','GIN','Guinea','+224','324'),
(82,'GW','GNB','Guinea-Bissau','+245','624'),
(83,'GY','GUY','Guyana','+595','328'),
(84,'HT','HTI','Haiti','+509','332'),
(85,'HN','HND','Honduras','+504','340'),
(86,'HU','HUN','Hungary','+36','348'),
(87,'IS','ISL','Iceland','+354','352'),
(88,'IN','IND','India','+91','356'),
(89,'ID','IDN','Indonesia','+62','360'),
(90,'IQ','IRQ','Iraq','+964','368'),
(91,'IE','IRL','Ireland','+353','372'),
(93,'IT','ITA','Italy','+39','380'),
(94,'JM','JAM','Jamaica','+1 876','388'),
(95,'JP','JPN','Japan','+81','392'),
(96,'JO','JOR','Jordan','+962','400'),
(97,'KZ','KAZ','Kazakhstan','+7 7','398'),
(98,'KE','KEN','Kenya','+254','404'),
(99,'KI','KIR','Kiribati','+686','296'),
(100,'KW','KWT','Kuwait','+965','414'),
(101,'KG','KGZ','Kyrgyzstan','+996','417'),
(102,'LV','LVA','Latvia','+371','428'),
(103,'LB','LBN','Lebanon','+961','422'),
(104,'LS','LSO','Lesotho','+266','426'),
(105,'LR','LBR','Liberia','+231','430'),
(106,'LI','LIE','Liechtenstein','+423','438'),
(107,'LT','LTU','Lithuania','+370','440'),
(108,'LU','LUX','Luxembourg','+352','442'),
(109,'MG','MDG','Madagascar','+261','450'),
(110,'MW','MWI','Malawi','+265','454'),
(111,'MY','MYS','Malaysia','+60','458'),
(112,'MV','MDV','Maldives','+960','462'),
(113,'ML','MLI','Mali','+223','466'),
(114,'MT','MLT','Malta','+356','470'),
(115,'MH','MHL','Marshall Islands','+692','584'),
(116,'MQ','MTQ','Martinique','+596','474'),
(117,'MR','MRT','Mauritania','+222','478'),
(118,'MU','MUS','Mauritius','+230','480'),
(119,'YT','MYT','Mayotte','+262','175'),
(120,'MX','MEX','Mexico','+52','484'),
(121,'MC','MCO','Monaco','+377','492'),
(122,'MN','MNG','Mongolia','+976','496'),
(123,'ME','MNE','Montenegro','+382','499'),
(124,'MS','MSR','Montserrat','+1664','500'),
(125,'MA','MAR','Morocco','+212','504'),
(126,'MM','MMR','Myanmar','+95','104'),
(127,'NA','NAM','Namibia','+264','516'),
(128,'NR','NRU','Nauru','+674','520'),
(129,'NP','NPL','Nepal','+977','524'),
(130,'NL','NLD','Netherlands','+31','528'),
(131,'AN','ANT','Netherlands Antilles','+599','530'),
(132,'NC','NCL','New Caledonia','+687','540'),
(133,'NZ','NZL','New Zealand','+64','554'),
(134,'NI','NIC','Nicaragua','+505','558'),
(135,'NE','NER','Niger','+227','562'),
(136,'NG','NGA','Nigeria','+234','566'),
(137,'NU','NIU','Niue','+683','570'),
(138,'NF','NFK','Norfolk Island','+672','574'),
(139,'MP','MNP','Northern Mariana Islands','+1 670','580'),
(140,'NO','NOR','Norway','+47','578'),
(141,'OM','OMN','Oman','+968','512'),
(142,'PK','PAK','Pakistan','+92','586'),
(143,'PW','PLW','Palau','+680','585'),
(144,'PA','PAN','Panama','+507','591'),
(145,'PG','PNG','Papua New Guinea','+675','598'),
(146,'PY','PRY','Paraguay','+595','600'),
(147,'PE','PER','Peru','+51','604'),
(148,'PH','PHL','Philippines','+63','608'),
(149,'PL','POL','Poland','+48','616'),
(150,'PT','PRT','Portugal','+351','620'),
(151,'PR','PRI','Puerto Rico','+1 939','630'),
(152,'QA','QAT','Qatar','+974','634'),
(153,'RO','ROU','Romania','+40','642'),
(154,'RW','RWA','Rwanda','+250','646'),
(155,'WS','WSM','Samoa','+685','882'),
(156,'SM','SMR','San Marino','+378','674'),
(157,'SA','SAU','Saudi Arabia','+966','682'),
(158,'SN','SEN','Senegal','+221','686'),
(159,'RS','SRB','Serbia','+381','688'),
(160,'SC','SYC','Seychelles','+248','690'),
(161,'SL','SLE','Sierra Leone','+232','694'),
(162,'SG','SGP','Singapore','+65','702'),
(163,'SK','SVK','Slovakia','+421','703'),
(164,'SI','SVN','Slovenia','+386','705'),
(165,'SB','SLB','Solomon Islands','+677','090'),
(166,'ZA','ZAF','South Africa','+27','710'),
(167,'GS','SGS','South Georgia and the South Sandwich Islands','+500','239'),
(168,'ES','ESP','Spain','+34','724'),
(169,'LK','LKA','Sri Lanka','+94','144'),
(170,'SD','SDN','Sudan','+249','729'),
(171,'SR','SUR','Suriname','+597','740'),
(172,'SZ','SWZ','Swaziland','+268','748'),
(173,'SE','SWE','Sweden','+46','752'),
(174,'CH','CHE','Switzerland','+41','756'),
(175,'TJ','TJK','Tajikistan','+992','762'),
(176,'TH','THA','Thailand','+66','764'),
(177,'TG','TGO','Togo','+228','768'),
(178,'TK','TKL','Tokelau','+690','772'),
(179,'TO','TON','Tonga','+676','776'),
(180,'TT','TTO','Trinidad and Tobago','+1 868','780'),
(181,'TN','TUN','Tunisia','+216','788'),
(182,'TR','TUR','Turkey','+90','792'),
(183,'TM','TKM','Turkmenistan','+993','795'),
(184,'TC','TCA','Turks and Caicos Islands','+1 649','796'),
(185,'TV','TUV','Tuvalu','+688','798'),
(186,'UG','UGA','Uganda','+256','800'),
(187,'UA','UKR','Ukraine','+380','804'),
(188,'AE','ARE','United Arab Emirates','+971','784'),
(189,'GB','GBR','United Kingdom','+44','826'),
(190,'US','USA','United States','+1','840'),
(191,'UY','URY','Uruguay','+598','858'),
(192,'UZ','UZB','Uzbekistan','+998','860'),
(193,'VU','VUT','Vanuatu','+678','548'),
(194,'WF','WLF','Wallis and Futuna','+681','876'),
(195,'YE','YEM','Yemen','+967','887'),
(196,'ZM','ZMB','Zambia','+260','894'),
(197,'ZW','ZWE','Zimbabwe','+263','716'),
(198,'AX','ALA','land Islands','','248'),
(199,'BO','BOL','Bolivia, Plurinational State of','+591','068'),
(200,'BN','BRN','Brunei Darussalam','+673','096'),
(201,'CC','CCK','Cocos (Keeling) Islands','+61','166'),
(202,'CD','COD','Congo, The Democratic Republic of the','+243','180'),
(203,'CI','CIV','Cote d\'Ivoire','+225','384'),
(204,'FK','FLK','Falkland Islands (Malvinas)','+500','238'),
(205,'GG','GGY','Guernsey','+44','831'),
(206,'VA','VAT','Holy See (Vatican City State)','+379','336'),
(207,'HK','HKG','Hong Kong','+852','344'),
(208,'IR','IRN','Iran, Islamic Republic of','+98','364'),
(209,'IM','IMN','Isle of Man','+44','833'),
(210,'JE','JEY','Jersey','+44','832'),
(211,'KP','PRK','Korea, Democratic People\'s Republic of','+850','408'),
(212,'KR','KOR','Korea, Republic of','+82','410'),
(213,'LA','LAO','Lao People\'s Democratic Republic','+856','418'),
(214,'LY','LBY','Libyan Arab Jamahiriya','+218','434'),
(215,'MO','MAC','Macao','+853','446'),
(216,'MK','MKD','Macedonia, The Former Yugoslav Republic of','+389','807'),
(217,'FM','FSM','Micronesia, Federated States of','+691','583'),
(218,'MD','MDA','Moldova, Republic of','+373','498'),
(219,'MZ','MOZ','Mozambique','+258','508'),
(220,'PS','PSE','Palestinian Territory, Occupied','+970','275'),
(221,'PN','PCN','Pitcairn','+872','612'),
(222,'RE','REU','Réunion','+262','638'),
(223,'RU','RUS','Russia','+7','643'),
(224,'BL','BLM','Saint Barthélemy','+590','652'),
(225,'SH','SHN','Saint Helena, Ascension and Tristan Da Cunha','+290','654'),
(226,'KN','KNA','Saint Kitts and Nevis','+1 869','659'),
(227,'LC','LCA','Saint Lucia','+1 758','662'),
(228,'MF','MAF','Saint Martin','+590','663'),
(229,'PM','SPM','Saint Pierre and Miquelon','+508','666'),
(230,'VC','VCT','Saint Vincent and the Grenadines','+1 784','670'),
(231,'ST','STP','Sao Tome and Principe','+239','678'),
(232,'SO','SOM','Somalia','+252','706'),
(233,'SJ','SJM','Svalbard and Jan Mayen','+47','744'),
(234,'SY','SYR','Syrian Arab Republic','+963','760'),
(235,'TW','TWN','Taiwan, Province of China','+886','158'),
(236,'TZ','TZA','Tanzania, United Republic of','+255','834'),
(237,'TL','TLS','Timor-Leste','+670','626'),
(238,'VE','VEN','Venezuela, Bolivarian Republic of','+58','862'),
(239,'VN','VNM','Viet Nam','+84','704'),
(240,'VG','VGB','Virgin Islands, British','+1 284','092'),
(241,'VI','VIR','Virgin Islands, U.S.','+1 340','850');

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
