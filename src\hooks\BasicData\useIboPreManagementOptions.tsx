import { getIBOPreManagementACList } from '@/services/foodstore-one/IBO/ibo-pre-management';
import Util from '@/util';
import type { DefaultOptionType } from 'antd/lib/select';
import { useCallback, useState } from 'react';

/**
 * Auto completion list of Pre IBO Management
 */
export default (defaultParams?: Record<string, any>) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [iboPreManagementOptions, setIboPreManagementOptions] = useState<(DefaultOptionType & API.IboPreManagement)[]>(
    [],
  );

  const searchIboPreManagementOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getIBOPreManagementACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setIboPreManagementOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  return { iboPreManagementOptions, searchIboPreManagementOptions, loading };
};
