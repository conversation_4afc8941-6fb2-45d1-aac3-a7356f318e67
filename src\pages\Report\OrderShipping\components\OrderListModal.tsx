/* eslint-disable @typescript-eslint/dot-notation */
import { Button, Col, InputNumber, Modal, Row, Space, Tag, Typography, message, notification } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, sn } from '@/util';
// import { FooterToolbar } from '@ant-design/pro-layout';
import { FullAddress, MagentoOrderStatus, isOrderAddressEditable } from '@/pages/Magento/Order';
import {
  createPicklistFromOrdersListForShippingByProviderReport,
  getOrdersListForShippingByProviderReport,
} from '@/services/foodstore-one/Report/order-report';
import type { OrderModalSearchParamsType } from './OrderShippingReportByProvider';
import { sectionLabels } from './OrderShippingReportByProvider';
import { EditOutlined, EyeOutlined, InfoCircleOutlined, WarningOutlined } from '@ant-design/icons';
import UpdateOrderExtraFormModal from '@/pages/Magento/Order/components/UpdateOrderExtraFormModal';
import UpdateAddressesModal from '@/pages/Magento/Order/components/UpdateAddressesModal';

type RowType = API.Order &
  API.OrderItem & {
    entity_id: number;
    qty_ordered: number;

    turnover: number;
    net_turnover: number;
    cturnover: number;
    ebay_fee: number;
    gp: number;
    bp: number;

    turnover_pcs: number;
    net_turnover_pcs: number;
    cturnover_pcs: number;
    ebay_fee_pcs: number;
    gp_pcs: number;
    bp_pcs: number;
  };

export type SearchFormValueType = Partial<API.Order>;

type OrderListModalProps = {
  searchParams?: OrderModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  reloadParent?: () => any;
};

const OrderListModal: React.FC<OrderListModalProps> = (props) => {
  const { searchParams } = props;
  const actionRef = useRef<ActionType>();
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

  // const [warningsDefCount, setWarningsDefCount] = useState<number>(0);
  const [totalOrderCnt, setTotalOrderCnt] = useState<number>(0);

  const [currentRow, setCurrentRow] = useState<API.Order>();
  const [openUpdateAddressModal, setOpenUpdateAddressModal] = useState<boolean>(false);

  const [openUpdateOrderExtraModal, setOpenUpdateOrderExtraModal] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Order ID',
      dataIndex: 'entity_id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      defaultSortOrder: 'descend',
      width: 100,
      copyable: true,
    },
    {
      title: 'Store',
      dataIndex: ['store', 'code'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 70,
      className: 'text-sm p-0',
      render: (_, record) => {
        if (record?.entity_id <= 0) return 'Total';
        else return (record as any)?.store?.code;
      },
    },
    {
      title: 'Increment ID',
      dataIndex: ['increment_id'],
      sorter: true,
      align: 'left',
      ellipsis: true,
      className: 'text-sm p-0',
      copyable: true,
      width: 100,
    },
    {
      title: 'status',
      dataIndex: ['status'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 100,
      render: (_, record) => {
        if (!record?.entity_id || record?.entity_id <= 0) return <></>;
        return <MagentoOrderStatus status={record.status} />;
      },
    },
    {
      title: 'Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      width: 200,
      sorter: false,
      className: 'p-0 text-xs',
      render(dom, record) {
        return record.mag_order_items?.length ? null : (
          <Typography.Text ellipsis copyable={{ text: record.item_ean?.ean_text_de?.name ?? record.name ?? '' }}>
            {record.item_ean?.ean_text_de?.name ?? record.name}
          </Typography.Text>
        );
      },
      /* render(dom, record) {
        const name = record?.item_ean?.ean_text_de?.name;
        return record.mag_order_items?.length && name ? (
          <Typography.Text ellipsis copyable>
            {dom}
          </Typography.Text>
        ) : null;
      }, */
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      width: 90,
      sorter: false,
      copyable: true,
    },
    {
      title: 'Items Qty',
      dataIndex: 'total_item_count',
      width: 65,
      align: 'right',
      sorter: false,
      copyable: true,
      render: (dom, recordIn) => {
        return ni(recordIn[`total_item_count`]);
      },
    },
    {
      title: 'Ordered Qty',
      dataIndex: 'qty_ordered',
      width: 65,
      align: 'right',
      sorter: false,
      render: (dom, recordIn) => {
        return ni(recordIn.mag_order_items?.length ? recordIn[`total_qty_ordered`] : recordIn[`qty_ordered`]);
      },
    },
    {
      title: 'Open Qty / Stock',
      dataIndex: 'open_qty_ordered',
      width: 100,
      tooltip:
        'Open orders qty pcs & Stock qty pcs. If Open > Stock Qty, RED background and you need to pay attention to control stocks .',
      render: (dom, r) => {
        if (r?.entity_id) return <></>;
        const openQtyPcs = sn(r.open_qty_ordered);
        const stockQtyPcs = sn(
          r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
        );
        return (
          <Row gutter={8} className="c-grey text-right">
            <Col span={10}>{ni(openQtyPcs, true)}</Col>
            <Col span={14}>{ni(stockQtyPcs, true)}</Col>
          </Row>
        );
      },
      onCell: (r) => {
        const openQtyPcs = sn(r.open_qty_ordered);
        const stockQtyPcs = sn(
          r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
        );
        return {
          className: openQtyPcs > stockQtyPcs ? 'bg-red' : '',
        };
      },
    },
    {
      title: 'Name',
      dataIndex: 'sa_firstname',
      width: 100,
      render: (dom, record) => (
        <Typography.Text mark={record?.warn_sa_fullname || record?.warn_sa_fullname_wrong}>
          {record.sa_fullname}
        </Typography.Text>
      ),
    },
    {
      title: 'Company',
      dataIndex: 'sa_company',
      align: 'left',
      width: 120,
      ellipsis: true,
    },
    {
      dataIndex: 'sa_full_warning',
      align: 'left',
      width: 20,
      className: 'p-0',
      tooltip: 'Delivery address warning',
      render(dom, record) {
        if (!record.mag_order_items?.length) return null;
        const warning_def = record.warnings_def || '';
        if (warning_def) {
          const title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
          return <WarningOutlined style={{ color: 'white' }} title={title} />;
        }
        return null;
      },
    },
    {
      title: 'Delivery Address',
      dataIndex: 'sa_full',
      align: 'left',
      width: 400,
      tooltip: 'Red colored rows are in warnings list. Highlighted parts may be wrong!',
      ellipsis: true,
      render(dom, record) {
        if (!record.mag_order_items?.length) return null;
        return (
          <>
            <FullAddress order={record} type="shipping" />
            <Button
              type="link"
              size="small"
              icon={isOrderAddressEditable(record.status) ? <EditOutlined /> : <EyeOutlined />}
              style={{
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 12,
                color: isOrderAddressEditable(record.status) ? 'auto' : 'grey',
              }}
              title={isOrderAddressEditable(record.status) ? 'Update addresses' : 'View addresses'}
              onClick={() => {
                setCurrentRow(record);
                setOpenUpdateAddressModal(true);
              }}
            />
          </>
        );
      },
      onCell(record) {
        return { style: { paddingRight: 16 } };
      },
    },
    {
      title: 'Picklist?',
      dataIndex: ['picklist_detail'],
      width: 70,
      className: 'text-sm c-grey',
      tooltip: 'Green indicates packed status',
      render: (dom, recordIn) =>
        recordIn.mag_order_items?.length ? null : recordIn.picklist_detail?.picklist_id ? (
          <span className={recordIn.picklist_detail?.is_stock_stable_updated ? 'c-green' : ''}>
            #{recordIn.picklist_detail?.picklist_id}
          </span>
        ) : null,
    },
    {
      title: 'Order Item ID',
      dataIndex: ['item_id'],
      width: 70,
      className: 'text-sm c-grey',
      render: (dom, recordIn) => (recordIn.mag_order_items?.length ? null : recordIn.item_id),
    },
    {
      title: 'Created on',
      dataIndex: ['created_at'],
      sorter: true,
      defaultSortOrder: 'descend',
      width: 100,
      className: 'text-sm c-grey',
      render: (dom, record) => Util.dtToDMYHHMMTz(record.created_at),
    },
    {
      title: 'Shipping notes',
      dataIndex: ['extra', 'shipping_provider_change_notes'],
      align: 'left',
      width: 150,
    },
    {
      title: 'User notes',
      dataIndex: ['extra', 'note1'],
      align: 'left',
      width: 200,
      render: (dom, record) => (
        <div className="text-sm">
          <span className="c-red" style={{ marginRight: 8 }}>
            {record.extra?.note1_status || ''}
          </span>
          {record.extra?.note1}
        </div>
      ),
    },
    {
      dataIndex: 'option_shipping',
      valueType: 'option',
      width: 40,
      fixed: 'right',
      render(dom, record) {
        return record.mag_order_items?.length ? (
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            title="Edit shipping setting..."
            onClick={() => {
              setCurrentRow(record);
              setOpenUpdateOrderExtraModal(true);
            }}
          />
        ) : null;
      },
    },
  ];

  useEffect(() => {
    if (props.modalVisible) {
      actionRef.current?.reload();
    }
  }, [props.modalVisible]);

  let modalTitle = '';
  if (searchParams?.dateRange && (searchParams?.dateRange?.from || searchParams?.dateRange?.to)) {
    modalTitle += (modalTitle ? ', ' : '') + `${Util.dtToDMY(searchParams?.dateRange.from) ?? ' '}`;
  }

  return (
    <Modal
      title={
        <Space size={16}>
          <span>Orders List ({sectionLabels[props.searchParams?.section || ''] ?? '-'})</span>
          <Tag>{props.searchParams?.shipping_provider_name || 'N/A'}</Tag>
          <span>{modalTitle}</span>
        </Space>
      }
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      width="90%"
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <ProTable<RowType, API.PageParams>
        actionRef={actionRef}
        rowKey="entity_id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, reload: true, density: false, search: false }}
        search={false}
        sticky
        scroll={{ x: 1300 }}
        cardProps={{
          headStyle: { padding: 0 },
          bodyStyle: { padding: 0 },
        }}
        request={async (params, sort, filter) => {
          // const searchFormValues = searchFormRef.current?.getFieldsValue();
          const searchFormValues = {};

          return getOrdersListForShippingByProviderReport(
            {
              ...params,
              ...props.searchParams,
              with: 'warnings,warnings_def,extra,latestShipping',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.entity_id) {
                setCurrentRow(res.data?.find((x) => x.entity_id == currentRow.entity_id));
              }
              // validate selected rows
              /* if (selectedRows?.length) {
                const ids = res.data.map((x: API.Order) => x.entity_id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.entity_id) >= 0));
              } */
              // setWarningsDefCount(res.data.reduce((prev, current) => prev + (current.warnings_def ? 1 : 0), 0));
              setExpandedRowKeys(res?.data?.map?.((x_1) => x_1.entity_id as any));

              setTotalOrderCnt(res.total);
              return res;
            })
            .finally() as any;
        }}
        onRequestError={Util.error}
        columns={columns}
        expandable={{
          childrenColumnName: 'mag_order_items',
          expandRowByClick: false,
          defaultExpandAllRows: true,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            setExpandedRowKeys(expandedKeys as any);
          },
        }}
        tableAlertRender={false}
        rowSelection={false}
        rowClassName={(record) => {
          // const defaultCls = !!record.mag_order_items?.length ? '' : 'disable-selection ';
          const defaultCls = '';
          let rowCls = '';
          if (record.entity_id <= 0) {
            rowCls = defaultCls + 'total-row';
          } else
            rowCls =
              defaultCls +
              (record.item_ean?.id && record.item_ean
                ? record?.item_ean?.is_single
                  ? 'row-single'
                  : 'row-multi'
                : '');
          if (!record.mag_order_items?.length) {
            // rowCls += ' bg-grey';
          } else {
            rowCls += ' bg-light-blue2';
          }

          return rowCls;
        }}
        pagination={{
          pageSize: 200,
        }}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            key="create-picklist"
            type="primary"
            size="small"
            onClick={() => {
              const func = async (limit?: number) => {
                const hide = message.loading('Creating a picklist from the lists below...', 0);
                createPicklistFromOrdersListForShippingByProviderReport({
                  ...props.searchParams,
                  with: 'warnings,warnings_def,extra',
                  limit,
                  ...Util.mergeGSearch({}),
                })
                  .then((res) => {
                    const picklistName = `#${res?.id}-${res?.username}-${res?.date} ${res?.note ?? ''}`;
                    notification.success({
                      description: `${picklistName} created successfully.`,
                      duration: 0,
                      placement: 'top',
                      message: undefined,
                    });
                    actionRef.current?.reload();
                    props.reloadParent?.();
                  })
                  .catch(Util.error)
                  .finally(hide);
              };

              if (totalOrderCnt > 10) {
                Modal.confirm({
                  title: <>Create a picklist (No shipment)</>,
                  content: (
                    <Row gutter={16} style={{ marginTop: 16, alignItems: 'center' }}>
                      <Col flex={'120px'}>
                        Max Orders{' '}
                        <InfoCircleOutlined title="If empty, new picklist with all processing orders will be created." />{' '}
                      </Col>
                      <Col>
                        <InputNumber id="orderLimit" width="xs" />
                      </Col>
                      <Col flex="auto">
                        <span>/ {totalOrderCnt} orders </span>
                      </Col>
                    </Row>
                  ),
                  onOk: async () => {
                    const limit = sn((document.getElementById('orderLimit') as HTMLInputElement)?.value);
                    func(limit);
                  },
                });
              } else {
                func();
              }
            }}
          >
            Create Picklist
          </Button>,
        ]}
      />

      <UpdateAddressesModal
        modalVisible={openUpdateAddressModal}
        handleModalVisible={setOpenUpdateAddressModal}
        initialValues={currentRow}
        onSubmit={async (value) => actionRef.current?.reload()}
      />

      <UpdateOrderExtraFormModal
        modalVisible={openUpdateOrderExtraModal}
        handleModalVisible={setOpenUpdateOrderExtraModal}
        order={{
          shipping_description: currentRow?.shipping_description,
          weight: currentRow?.weight,
          entity_id: currentRow?.entity_id,
          shipping_imported_list: currentRow?.shipping_imported_list,
          latest_shipping: currentRow?.latest_shipping,
          sa_fullname: currentRow?.sa_fullname,
          sa_full: currentRow?.sa_full,
          sa_company: currentRow?.sa_company,
        }}
        values={{ ...currentRow?.extra }}
        onSubmit={async (values) => {
          actionRef.current?.reload();
        }}
      />
    </Modal>
  );
};

export default OrderListModal;
