import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { Button, message, TreeSelect } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormGroup, ProFormTreeSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import _ from 'lodash';
import type { DataNode } from 'antd/lib/tree';
import { updateItemAll } from '@/services/foodstore-one/Item/item';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';

export type FormValueType = {
  ids: number[];
  overrideEans?: boolean;
};

export type UpdateCategoriesBulkFormProps = {
  initialValues?: FormValueType;
  treeData: DataNode[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateCategoriesBulkForm: React.FC<UpdateCategoriesBulkFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      title={'Batch update item categories'}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      modalProps={{
        maskClosable: false,
      }}
      formRef={formRef}
      submitter={{
        render: (__, doms) => {
          return (
            <>
              <Button
                key="submit"
                type="primary"
                onClick={() => {
                  formRef.current?.setFieldsValue({ mode: 'add' });
                  formRef.current?.submit();
                }}
                icon={<PlusOutlined />}
              >
                Add
              </Button>
              <Button
                type="default"
                danger
                key="delete"
                onClick={() => {
                  formRef.current?.setFieldsValue({ mode: 'delete' });
                  formRef.current?.submit();
                }}
                icon={<MinusOutlined />}
              >
                Delete
              </Button>
              ,
              <Button type="default" key="close" onClick={() => props.handleModalVisible(false)}>
                Close
              </Button>
            </>
          );
        },
      }}
      onFinish={async (value) => {
        const categories = value?.categories?.map((x: any) => x.value);
        const data = {
          mode: value.mode,
          items: props.initialValues?.ids.map((id) => ({
            id,
            categories,
          })),
        };
        const hide = message.loading('Bulk updating item categories...');
        try {
          await updateItemAll(data as any);
          hide();
          message.success('Updated successfully.');
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        } catch (error) {
          hide();
          Util.error(error);
          return false;
        }
        return true;
      }}
    >
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText name="mode" formItemProps={{ style: { display: 'none' } }} />
        <ProFormTreeSelect
          placeholder="Select categories"
          request={async () => props.treeData}
          allowClear
          name={'categories'}
          label="Select categories"
          // tree-select args
          fieldProps={{
            showCheckedStrategy: TreeSelect.SHOW_ALL,
            filterTreeNode: true,
            showSearch: true,
            dropdownMatchSelectWidth: false,
            autoClearSearchValue: true,
            multiple: true,
            labelInValue: true,
            showArrow: true,
            treeLine: true,
            treeNodeFilterProp: 'title',
            fieldNames: {
              label: 'title',
            },
          }}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateCategoriesBulkForm;
