<?php


use App\Lib\Func;
use PhpImap\Mailbox;
use Webklex\PHPIMAP\ClientManager;

require __DIR__ . '/../../src/App/App.php';

/** @var \Slim\Container $container */

$m365Setting = $container->get('settings')['m365'];

$provider = new \League\OAuth2\Client\Provider\GenericProvider([
    // Required
    'clientId' => $m365Setting['clientId'],
    'clientSecret' => $m365Setting['clientSecret'],
    'redirectUri' => 'http://localhost',
    // Optional
    'urlAuthorize' => "https://login.microsoftonline.com/{$m365Setting['tenant']}/oauth2/v2.0/authorize",
    'urlAccessToken' => "https://login.microsoftonline.com/{$m365Setting['tenant']}/oauth2/v2.0/token",
    'urlResourceOwnerDetails' => 'https://outlook.office.com/api/v1.0/me'
]);

/** @var \App\Models\Sys\SysOAuth $obj */
$obj = \App\Models\Sys\SysOAuth::query()
    ->where('type', \App\Models\Sys\SysOAuth::TYPE_M365)
    ->firstOrFail();

if ($obj->token && $obj->token_expired_at > time() - 3 * 60) {
    // Get request
    echo "Token exists!" . PHP_EOL;

    $tmp = 1;
} else {

    if ($obj->refresh_token) {
        $tokens = $provider->getAccessToken('refresh_token', [
            'refresh_token' => $obj->refresh_token,
            'scope' => 'https://outlook.office365.com/IMAP.AccessAsUser.All',
        ]);

        $obj->refresh_token = $tokens->getRefreshToken();
        $obj->token = $tokens->getToken();
        $obj->token_expired_at = $tokens->getExpires();
        $obj->save();
    }
}

$cm = new ClientManager();
$client = $cm->make([
    'host' => 'outlook.office365.com',
    'port' => 993,
    'encryption' => 'ssl',
    'validate_cert' => true,
    'username' => '<EMAIL>',
    'password' => $obj->token,
    'protocol' => 'imap',
    'authentication' => "oauth"
]);

//Connect to the IMAP Server
$client->connect();

//Get all Mailboxes
/** @var \Webklex\PHPIMAP\Support\FolderCollection $folders */
$folders = $client->getFolders();

//Loop through every Mailbox
/** @var \Webklex\PHPIMAP\Folder $folder */
foreach ($folders as $folder) {
    if (!in_array($folder->name, ['INBOX', 'Sent Items'/*, 'Outbox'*/])) {
        continue;
    }

    echo PHP_EOL . "==========================" . ($folder->name) . "==========================" . PHP_EOL;
    echo "Full Name: " . ($folder->full_name) . PHP_EOL;
    echo "Path:  " . ($folder->path) . PHP_EOL;


    //Get all Messages of the current Mailbox $folder
    $query = $folder->messages();
    // $query->setFetchOrderDesc();
    $query->since('22.01.2025');
    $query->setFetchBody(false);

    $query->setPage(1);
    $query->setLimit(3);

    $messages = $query->get();


    /** @var \Webklex\PHPIMAP\Message $message */
    foreach ($messages as $ind => $message) {
        echo PHP_EOL . "---------------------" . (++$ind) . "---------------" . PHP_EOL;
        echo $message->getSubject() . PHP_EOL;
        echo "From: " . $message->getFrom() . PHP_EOL;
        echo "To: " . $message->getTo() . PHP_EOL;
        echo "Sender: " . $message->getSender() . PHP_EOL;
        echo "Date: " . $message->getDate()->toString() . PHP_EOL;
        echo "Uid: " . $message->getUid() . PHP_EOL;
        echo "MessageId: " . $message->getMessageId() . PHP_EOL;
        echo "Msgn: " . $message->getMsgn() . PHP_EOL;
        echo "Subject: " . $message->getSubject() . PHP_EOL;
        echo "Content Type: " . $message->get('content_type') . PHP_EOL;
        // print_r($message->getAttributes());


        echo " ---> sender " . PHP_EOL;
        var_dump($message->getSender()->count());
        var_dump($message->getSender()->toArray());


        echo " ---> to " . PHP_EOL;
        var_dump($message->getTo()->toArray());
        // var_dump($message->get('content_type')->toArray());

        break;

        echo 'Attachments: ' . $message->getAttachments()->count() . PHP_EOL;
        // print_r($message->getAttributes());
        // echo $message->getHTMLBody();
    }
        break;
}
