import React, { useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, Tag, Typography } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { getMagSyncLogList } from '@/services/foodstore-one/Magento/sync-log';
import Util from '@/util';
import moment from 'moment';
import { SyncTypeIcon } from '@/pages/Welcome';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProForm } from '@ant-design/pro-form';
import { ReloadOutlined } from '@ant-design/icons';

const MagSyncLogList: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const columns2: ProColumns<API.MagSyncLog>[] = [
    {
      dataIndex: 'sync_type',
      width: 15,
      render: (dom: any, record: any) => <SyncTypeIcon type={record.sync_type} />,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 250,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      ellipsis: true,
    },
    {
      title: 'Date',
      dataIndex: 'updated_on',
      width: 110,
      render: (dom, record) =>
        record.updated_on ? (
          <div title={Util.dtToDMYHHMM(record.updated_on)}>{moment(record.updated_on).fromNow()}</div>
        ) : undefined,
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
      width: 120,
    },
    {
      title: 'Batch Code',
      dataIndex: 'batch_code',
      width: 100,
    },
    {
      title: 'Detail',
      dataIndex: 'detail',
      ellipsis: true,
      copyable: true,
      render: (dom, record) =>
        record.detail ? (
          <Typography.Text copyable ellipsis>
            {JSON.stringify(record.detail)}
          </Typography.Text>
        ) : null,
    },
  ];

  const loadData = () => {
    actionRef.current?.reload();
  };

  return (
    <PageContainer>
      <Card>
        <ProForm layout="inline" formRef={formRef} submitter={false} initialValues={Util.getSfValues('sf_log', {}, {})}>
          <ProFormSelect
            name="action_type"
            placeholder="Select type"
            label="Type"
            options={[
              { value: '', label: 'All' },
              { value: 0, label: 'System UI' },
              { value: 1, label: 'Server Cron' },
            ]}
            width="xs"
            fieldProps={{ onChange: (e) => formRef.current?.submit() }}
          />
          <ProFormSelect
            name="statuses"
            placeholder="Select status"
            label="Status"
            mode="multiple"
            options={[
              { value: '', label: 'All' },
              { value: 'success', label: 'Success' },
              { value: 'started', label: 'Started' },
              { value: 'error', label: 'Error' },
              { value: 'processing', label: 'Processing' },
            ]}
            width={150}
            fieldProps={{ onChange: (e) => formRef.current?.submit() }}
          />
          <ProFormText name="nameLike" width={150} label="Name" />
          <ProFormText name="note" width={150} label="Note" />
          {/* <SProFormDateRange label="Date" formRef={formRef} /> */}
          <ProFormSelect
            name="notNames"
            placeholder="Names"
            label="Excludes"
            mode="multiple"
            options={[
              { value: '', label: '' },
              { value: 'Stock Down Sync', label: 'Stock Down Sync' },
              { value: 'Latest Orders Sync', label: 'Latest Orders Sync' },
              { value: 'DB Backup', label: 'DB Backup' },
            ]}
            width={'sm'}
            fieldProps={{ onChange: (e) => formRef.current?.submit() }}
          />
          <Button type="primary" icon={<ReloadOutlined />} onClick={() => loadData()}>
            Search
          </Button>
        </ProForm>
      </Card>
      <ProTable<API.MagSyncLog, API.PageParams>
        rowKey="id"
        size="small"
        headerTitle="Magento & GDSN Sync history"
        revalidateOnFocus={false}
        search={false}
        params={{ with: 'user' }}
        actionRef={actionRef}
        request={async (params, sort, filter) => {
          const sfValues = formRef.current?.getFieldsValue();
          Util.setSfValues('sf_log', sfValues);
          return getMagSyncLogList({ ...params, ...sfValues }, sort, filter);
        }}
        columns={columns2}
        pagination={{ showSizeChanger: true, defaultPageSize: 20 }}
        columnEmptyText=""
      />
    </PageContainer>
  );
};

export default MagSyncLogList;
