/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/offer-item-packed-ready';

/** get GET /api/offer-item-packed-ready */
export async function getOfferItemIboPrePackReadyMapList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** post POST /api/offer-item-packed-ready */
export async function addOfferItemIboPrePackReadyMapBulk(rows: API.OfferItemIboPrePackReadyMap[], params?: { reset?: boolean }, options?: { [key: string]: any }) {
  return request<API.ResultObject<boolean>>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: {
      rows,
      ...params,
    },
    ...(options || {}),
  }).then(res => res.message);
}


/** delete DELETE /api/offer-item-packed-ready */
export async function deleteOfferItemIboPrePackReadyMap(data?: {
  offer_item_id?: number;
  ibo_pre_id?: number;
  ean_id?: number;
  exp_date?: string;
}, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}`, {
    method: 'DELETE',
    data,
    ...(options || {}),
  });
}


