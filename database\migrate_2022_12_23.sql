
ALTER TABLE `item_ean` DROP INDEX `idx_item_ean_sku`, ADD UNIQUE INDEX `idx_item_ean_sku` (`sku`);

CREATE SQL SECURITY DEFINER VIEW `v_stock_stable_aggregated` AS (
select
    `s`.`item_id`       AS `item_id`,
    `s`.`ean_id`        AS `ean_id`,
    `s`.`parent_ean_id` AS `parent_ean_id`,
    if(`s`.`parent_ean_id` = `s`.`ean_id`,(select sum(`stock_stable`.`total_piece_qty`) from `stock_stable` where `stock_stable`.`item_id` = `s`.`item_id`),sum(`s`.`box_qty`)) AS `mix_qty`,
    sum(`s`.`piece_qty`) AS `piece_qty`,
    sum(`s`.`box_qty`)  AS `box_qty`,
    sum(`s`.`total_piece_qty`) AS `total_piece_qty`,
    min(`s`.`exp_date`) AS `min_exp_date`,
    if(`s`.`parent_ean_id` = `s`.`ean_id`,(select min(`stock_stable`.`exp_date`) from `stock_stable` where `stock_stable`.`total_piece_qty` <> 0 and `stock_stable`.`item_id` = `s`.`item_id`),min(`s`.`exp_date`)) AS `mix_min_exp_date`,
    (select
         sum(`m`.`quantity`)
     from (`xmag_inventory_stock` `m`
          join `item_ean` `e`)
     where `e`.`sku` = `m`.`sku`
           and `e`.`id` = `s`.`ean_id`) AS `mag_mix_qty`
from `stock_stable` `s`
where `s`.`total_piece_qty` <> 0
group by `s`.`ean_id`);





