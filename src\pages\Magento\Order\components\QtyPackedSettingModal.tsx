/* eslint-disable @typescript-eslint/dot-notation */
import SNumpad from '@/components/Numpad';
import { setStockStableBookedQtyPacked } from '@/services/foodstore-one/Stock/stock-stable-booked';
import Util, { sn } from '@/util';
import { Button, message, Modal, Space } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';

type QtyPackedSettingModalProps = {
  initialValues: API.StockStableBooked;
  id: number; // ID of stock_stable_booked
  orderItemId: number;
  maxQtyPacked: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: () => Promise<boolean | void>;
};

const QtyPackedSettingModal: React.FC<QtyPackedSettingModalProps> = (props) => {
  const { id, orderItemId, maxQtyPacked, modalVisible, handleModalVisible, onSubmit } = props;

  // Numpad
  const numpadFieldRef = useRef<HTMLInputElement>();

  useEffect(() => {
    if (modalVisible && numpadFieldRef.current) {
      numpadFieldRef.current.value = '';
      numpadFieldRef.current?.focus();
    }
  }, [modalVisible]);

  return (
    <Modal
      title={<>Update Packed Qty</>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="300px"
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <Space size={8}>
        <input
          ref={numpadFieldRef as any}
          className="ant-input ant-input-lg"
          style={{ marginTop: 16, marginBottom: 16, width: 180 }}
        />
        <div className="ant-form-item-explain ant-form-item-explain-connected">{`Max: ${maxQtyPacked}`}</div>
      </Space>
      <SNumpad fieldRef={numpadFieldRef} />
      <Space style={{ justifyContent: 'space-between', width: '100%', marginTop: 16 }}>
        <Button
          type="primary"
          size="large"
          onClick={() => {
            const qty = sn(numpadFieldRef.current?.value);

            if (qty < 0) {
              message.error('Invalid Qty!');
              numpadFieldRef.current?.focus();
              return;
            }

            const hide = message.loading(`Setting packed qty ...`);
            setStockStableBookedQtyPacked(orderItemId, qty, { id: id })
              .then((res) => {
                message.success('Set packed qty successfully.');
                onSubmit?.();
                handleModalVisible(false);
              })
              .catch(Util.error)
              .finally(() => {
                hide();
              });
          }}
        >
          OK
        </Button>
        <Button size="large" onClick={() => handleModalVisible(false)}>
          Cancel
        </Button>
      </Space>
    </Modal>
  );
};

export default QtyPackedSettingModal;
