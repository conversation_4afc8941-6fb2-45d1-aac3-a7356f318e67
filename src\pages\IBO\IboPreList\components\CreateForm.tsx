import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { Col, message, Row } from 'antd';
import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';
import { IboPreStatusOptions } from '..';
import { addIboPre } from '@/services/foodstore-one/IBO/ibo-pre';
import useIboPreManagementOptions from '@/hooks/BasicData/useIboPreManagementOptions';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import useSearchEan from '@/hooks/BasicData/useSearchEan';

const handleAdd = async (fields: API.IboPre) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addIboPre(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.IboPre>;

export type CreateFormProps = {
  initialValues?: Partial<API.IboPre>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPre) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, initialValues } = props;

  const { iboPreManagementOptions, searchIboPreManagementOptions } = useIboPreManagementOptions();

  const { formElements, itemEan } = useSearchEan(undefined, formRef, {
    required: true,
    rules: [
      {
        required: true,
        message: 'Supplier is required',
      },
    ],
  });

  return (
    <ModalForm<FormValueType>
      title={'Create Pre IBO'}
      width="600px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      initialValues={initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      modalProps={{ okText: 'Create' }}
      onFinish={async (value) => {
        if (!itemEan) {
          message.error('Please search EAN!');
          return;
        }

        const success = await handleAdd({ ean_id: itemEan.id, ...value });

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <Row gutter={32}>
        <Col>
          <ProFormSelect
            name={'ibo_pre_management_id'}
            showSearch
            label="Pre Order"
            width={400}
            options={iboPreManagementOptions}
            request={searchIboPreManagementOptions}
            rules={[
              {
                required: true,
                message: 'Supplier is required',
              },
            ]}
          />
        </Col>
        <Col>
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            request={async (params) => {
              const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
              if (res && res.data) {
                const tmp = res.data.map((x: API.Supplier) => ({
                  label: `${x.supplier_no} - ${x.name}`,
                  value: x.id,
                }));
                return tmp;
              }
              return [];
            }}
            width="sm"
            name="supplier_id"
            label="Supplier"
            required
            rules={[
              {
                required: true,
                message: 'Supplier is required',
              },
            ]}
          />
        </Col>
        <Col>
          <ProFormSelect
            name="status"
            label="Status"
            options={IboPreStatusOptions}
            rules={[
              {
                required: true,
                message: 'Supplier is required',
              },
            ]}
          />
        </Col>
      </Row>
      <Row gutter={32}>
        <Col>{formElements}</Col>
      </Row>
      <Row gutter={32}>
        <Col>
          <SProFormDigit
            width={150}
            name="qty"
            label="Qty"
            required
            rules={[
              {
                required: true,
                message: 'Qty is required',
              },
            ]}
            fieldProps={{ precision: 0 }}
          />
        </Col>
        <Col>
          <SProFormDigit
            width={150}
            name="price_xls"
            label="Order price"
            addonAfter="€"
            required
            rules={[
              {
                required: true,
                message: 'Order price is required',
              },
            ]}
            fieldProps={{ precision: 3 }}
          />
        </Col>
      </Row>
      <Row gutter={32}>
        {/* <Col>
          <SProFormDigit width={150} name="qty_pkg" label="Qty / Pkg" />
        </Col> */}
        <Col>
          <SProFormDigit
            width={150}
            name="price_pallet"
            label="Pallet Price"
            fieldProps={{ precision: 3 }}
            addonAfter="€"
          />
        </Col>

        <Col style={{ opacity: 0.3 }}>
          <SProFormDigit width={150} name="qty_pallet" label="Pallet (pcs)" />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default CreateForm;
