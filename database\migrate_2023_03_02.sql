INSERT INTO `sys_dict` (`code`, `type`, `label`, `desc`)
VALUES ('BA<PERSON><PERSON><PERSON>_PATH', 'sys config', 'DB Backup Path',
        'specify the absolute path. e.g. /home/<USER>/yyyy. If not specified, path will be {document root}/data/backup');


ALTER TABLE `warehouse_picklist_detail`
    CHANGE `sku` `sku` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'SKU',
    ADD COLUMN `created_on`              DATETIME NULL COMMENT 'Creation date time' AFTER `stock_stable_dump`,
    ADD COLUMN `created_by`              INT      NULL COMMENT 'Create user ID' AFTER `created_on`,
    ADD COLUMN `updated_on`              DATETIME NULL COMMENT 'Update date time' AFTER `created_by`,
    ADD COLUMN `updated_by`              INT      NULL COMMENT 'Update user ID' AFTER `updated_on`,
    ADD COLUMN `stock_stable_updated_on` DATETIME NULL COMMENT 'Stock update date time' AFTER `updated_on`;


ALTER TABLE `warehouse_picklist`
    <PERSON>AN<PERSON> `created_at` `created_on` DATETIME NULL COMMENT 'Created date time',
    CHANGE `created_by` `created_by` INT(11) NULL COMMENT 'Create user ID',
    ADD COLUMN `updated_on`                   DATETIME NULL COMMENT 'Updated date time' AFTER `created_by`,
    ADD COLUMN `updated_by`                   INT      NULL COMMENT 'Update user ID' AFTER `updated_on`,
    ADD COLUMN `last_stock_stable_updated_on` DATETIME NULL COMMENT 'Last booking date time' AFTER `updated_by`,
    ADD COLUMN `is_full_stock_stable_updated` BOOL     NULL COMMENT 'All details are booked in stock stable? Note: Booking can be done partially. All of details\' is_stock_stable_is updated, we set it to 1' AFTER `updated_by`,
    ADD COLUMN `order_shipment_created_on`    DATETIME NULL COMMENT 'In export CSV, we create a magento ship. And Magento order will be completed immediately if possible.' AFTER `is_full_stock_stable_updated`;

ALTER TABLE `file`
    ADD COLUMN `category` VARCHAR(31) NULL COMMENT 'File Category' AFTER `id`;

ALTER TABLE `warehouse_picklist`
    ADD COLUMN `final_pdf_id` BIGINT UNSIGNED NULL COMMENT 'Final PDF file. Created when full booking' AFTER `order_shipment_created_on`,
    ADD COLUMN `final_csv_id` BIGINT UNSIGNED NULL COMMENT 'Final CSV file.' AFTER `final_pdf_id`,
    ADD CONSTRAINT `FK_warehouse_picklist_final_pdf_id` FOREIGN KEY (`final_pdf_id`) REFERENCES `file` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT,
    ADD CONSTRAINT `FK_warehouse_picklist_final_csv_id` FOREIGN KEY (`final_csv_id`) REFERENCES `file` (`id`) ON UPDATE CASCADE ON DELETE RESTRICT;
