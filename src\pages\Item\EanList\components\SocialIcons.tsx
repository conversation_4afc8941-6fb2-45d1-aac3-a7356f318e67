import { SearchOutlined } from '@ant-design/icons';
import React, { useRef } from 'react';
import { Space } from 'antd';

type SocialLinkKeyType = 'google' | 'AMZ' | 'ebay' | 'idealo' | 'WoS' | 'SwO' | 'kfld';

interface ISocialLink {
  key: string | SocialLinkKeyType;
  name?: string;
  link: string;
  title?: string;
  icon?: any;
}

export const socialLinks: ISocialLink[] = [
  {
    key: 'google',
    name: '',
    title: 'Google DE',
    link: 'https://www.google.de/search?q={title}',
    // icon: <img src={'/icons/google.ico'} width={16} height={16} />,
    icon: <img src={'/images/google.png'} width={16} height={16} />,
  },
  {
    key: 'WoS',
    name: 'WoS',
    title: 'World of Sweets | Süßigkeiten Shop | Süßwaren online kaufen',
    link: 'https://www.worldofsweets.de/index.php?lang=0&queryFromSuggest=&userInput=&listorderby=relevance&listorder=desc&cl=fcfatsearch_productlist&searchparam={ean}',
    // icon: <img src={'/icons/wos.ico'} width={16} height={16} />,
    icon: <img src={'/images/wos.png'} width={16} height={16} />,
  },
  {
    key: 'SwO',
    name: 'SwO',
    title: 'Süßigkeiten Online Shop & Süßwaren Großhandel | sweets-online.com',
    link: 'https://www.sweets-online.com/search?query={ean}',
    // icon: <img src={'/icons/swo.ico'} width={16} height={16} style={{ borderRadius: 100 }} />,
    icon: <img src={'/images/swo.png'} width={16} height={16} style={{ borderRadius: 100 }} />,
  },
  {
    key: 'AMZ',
    name: 'AMZ',
    title: 'Amazon',
    link: 'https://www.amazon.de/s?k={ean}',
    // icon: <img src={'/icons/amazon.ico'} width={16} height={16} />,
    icon: <img src={'/images/amazon.png'} width={16} height={16} />,
  },
  {
    key: 'ebay',
    name: 'Ebay',
    title: 'Ebay',
    link: 'https://www.ebay.de/sch/i.html?_from=R40&_trksid=p2380057.m570.l1313&_nkw={ean}&_sacat=0',
    // icon: <img src={'/icons/ebay.ico'} width={16} height={16} />,
    icon: <img src={'/images/ebay.png'} width={16} height={16} />,
  },
  {
    key: 'idealo',
    name: 'Idealo',
    title: 'Idealo',
    link: 'https://www.idealo.de/preisvergleich/MainSearchProductCategory.html?q={ean}',
    icon: <img src={'/images/idealo.png'} width={16} height={16} />,
  },
];

export type SocialLinksProps = {
  ean: string;
  title?: string;
  style?: any;
  isSameTab?: boolean;
  excludes?: string[];
  prevWindowRef?: React.MutableRefObject<Window | null | undefined>;
};

export const SocialLinkItem: React.FC<SocialLinksProps & { k: SocialLinkKeyType }> = ({
  k: k,
  ean,
  title,
  isSameTab,
  prevWindowRef,
}) => {
  const a = socialLinks.find((x) => x.key == k);
  const url = a ? a.link.replace('{ean}', ean).replace('{title}', title || ean || '') : null;
  return a && url ? (
    isSameTab ? (
      <a
        key={a.key}
        href="javascript:void(0)"
        title={a.title || ''}
        onClick={(e) => {
          e.preventDefault();
          window.open(url);
          /* console.log('url=', url);
          console.log('prevWindow=', prevWindowRef);
          if (prevWindowRef?.current) {
            console.log('location changing...', 'old URL=', prevWindowRef.current.location.href);
            prevWindowRef.current.location.href = url;
          } else {
            if (prevWindowRef) {
              prevWindowRef.current = window.open(url);
            }
          } */
        }}
      >
        {a.icon || a.name}
      </a>
    ) : (
      <a key={a.key} href={url} title={a.title || ''} target={isSameTab ? '_self' : '_blank'} rel="noreferrer">
        {a.icon || a.name}
      </a>
    )
  ) : null;
};

const SocialLinks: React.FC<SocialLinksProps> = ({ ean, style, title, isSameTab, excludes }) => {
  return (
    <Space style={style}>
      {socialLinks
        .filter((x) => !excludes?.includes(x.key))
        .map((a) => (
          <a
            key={a.key}
            href={a.link.replace('{ean}', ean).replace('{title}', title || ean || '')}
            title={a.title || ''}
            target={isSameTab ? '_self' : '_blank'}
            rel="noreferrer"
          >
            {a.icon || a.name}
          </a>
        ))}
      {!excludes?.includes('all') ? (
        <SearchOutlined
          className="btn-gray"
          title="Open all links"
          style={{ verticalAlign: 'middle' }}
          onClick={() => {
            socialLinks.forEach((a) => {
              if (!excludes?.includes(a.key)) {
                window.open(a.link.replace('{ean}', ean).replace('{title}', title || ''), '_blank');
              }
            });
          }}
        />
      ) : null}
    </Space>
  );
};

export default SocialLinks;
