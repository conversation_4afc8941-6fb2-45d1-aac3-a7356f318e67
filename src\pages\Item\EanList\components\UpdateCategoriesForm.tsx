import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, Space, Tag, Typography, Row, Col, Button } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormDependency, ProFormSwitch } from '@ant-design/pro-form';
import { ProFormGroup, ProFormTreeSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanAttribute } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _ from 'lodash';
import type { DataNode } from 'antd/lib/tree';
import SocialLinks from './SocialIcons';
import { useState } from 'react';
import { getItem, updateItem } from '@/services/foodstore-one/Item/item';
import { Link } from 'umi';
import { LinkOutlined } from '@ant-design/icons';
import type { HandleNavFuncType } from '../hooks/useModalNavigation';
import ModalNavigation from './ModalNavigation';
import GdsnItemButton from './GdsnItemButton';
import useUpdateModalActions from '../hooks/useUpdateModalActions';

export type FormValueType = {
  p_item?: API.Item;
} & Partial<API.Ean>;

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  const dataParent = {
    id: fields.item_id,
    mode: 'categories',
    categories: fields?.p_item?.categories?.map((x: any) => x.value),
  };

  const data = {
    id: fields.id,
    mode: 'categories',
    note: fields.note,
    categories: fields?.categories?.map((x: any) => x.value),
  };

  try {
    await updateEanAttribute(data as API.Ean);
    await updateItem(dataParent as API.Item);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateCategoriesFormProps = {
  initialValues?: Partial<API.Ean>;
  treeData: DataNode[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  handleNavigation?: HandleNavFuncType;
  gdsn?: boolean;
};

const UpdateCategoriesForm: React.FC<UpdateCategoriesFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (props.modalVisible && formRef.current) {
      const newValues = { ...(props.initialValues || { item: {} }) };
      formRef.current.setFieldsValue(newValues);

      setLoading(true);
      getItem(props.initialValues?.item_id)
        .then((item) => {
          formRef.current?.setFieldsValue({ p_item: item });
        })
        .catch((error) => {
          message.error('Failed to fetch item data. Please close model and re-open!');
        })
        .finally(() => setLoading(false));
    }
  }, [props.modalVisible, props.initialValues]);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useUpdateModalActions(
    props.initialValues?.id ?? 0,
    props.initialValues?.sku ?? '',
    formRef.current,
  );

  return (
    <ModalForm
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update EAN categories</span>
          <Typography.Paragraph
            copyable={{
              text: props.initialValues?.ean || '',
              tooltips: 'Copy EAN ' + (props.initialValues?.ean || ''),
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {' '}
            - {props.initialValues?.ean || ''}
          </Typography.Paragraph>
          <SocialLinks
            ean={props.initialValues?.ean || ''}
            title={props.initialValues?.ean_texts?.[0]?.name}
            style={{ marginLeft: 50 }}
          />
          {props.handleNavigation ? (
            <ModalNavigation
              modalName="category"
              eanId={props.initialValues?.id}
              itemId={props.initialValues?.item_id}
              handleNavigation={props.handleNavigation}
              style={{ marginLeft: 50 }}
            />
          ) : null}
          {props.gdsn && (
            <GdsnItemButton
              ean={props.initialValues?.ean}
              eanId={props.initialValues?.id}
              itemId={props.initialValues?.item_id}
              style={{ marginLeft: 50 }}
            />
          )}
          {actionButtons}
        </div>
      }
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      modalProps={{
        confirmLoading: loading,
        maskClosable: false,
        className: props.initialValues?.is_single ? 'm-single' : 'm-multi',
      }}
      formRef={formRef}
      onFinish={async (value) => {
        if (formRef.current?.isFieldsTouched()) {
          const success = await handleUpdate({
            ...value,
            id: props.initialValues?.id,
            item_id: props.initialValues?.item_id,
          });

          if (success) {
            await runActionsCallback();

            if (value.closeModal) props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          props.handleModalVisible(false);
        }
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {actionButtons}
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Save & Close
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
        {hiddenFormElements}
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormTreeSelect
          label="Categories global (for all EANs)"
          placeholder="Select categories"
          request={async () => props.treeData}
          allowClear
          name={['p_item', 'categories']}
          // tree-select args
          fieldProps={{
            filterTreeNode: true,
            showSearch: true,
            dropdownMatchSelectWidth: false,
            autoClearSearchValue: true,
            multiple: true,
            labelInValue: true,
            showArrow: true,
            treeLine: true,
            treeNodeFilterProp: 'title',
            fieldNames: {
              label: 'title',
            },
          }}
        />
        <ProFormDependency key={'p_item-item_eans'} name={[['p_item', 'item_eans']]}>
          {(depsValues) => {
            return (
              <Space style={{ width: '100%', paddingLeft: 50, alignItems: 'start' }} size={24}>
                <div>
                  <b>Used by: </b>
                </div>
                <div style={{ flex: '1' }}>
                  {(depsValues?.p_item?.item_eans || []).map((ean: API.Ean) => {
                    return (
                      <Row key={ean.id} gutter={16}>
                        <Col span={1}>
                          <Link
                            to={`/item/ean-all?ean_id=${ean.id}&sku=${ean.sku}`}
                            target="_blank"
                            title="Search EAN grid"
                          >
                            <LinkOutlined className="green" />
                          </Link>
                        </Col>
                        <Col span={5}>
                          <Typography.Paragraph
                            copyable={{
                              text: ean.sku || '',
                            }}
                            style={{ marginBottom: 0 }}
                          >
                            {ean.sku}
                          </Typography.Paragraph>
                        </Col>
                        <Col span={7}>
                          <Typography.Paragraph
                            copyable={{
                              text: ean.ean || '',
                            }}
                            style={{ marginBottom: 0 }}
                          >
                            {ean.ean}
                          </Typography.Paragraph>
                        </Col>
                        <Col span={11}>{ean.ean_texts?.[0]?.name}</Col>
                      </Row>
                    );
                  })}
                </div>
              </Space>
            );
          }}
        </ProFormDependency>

        <ProFormSwitch
          name={['note', 'catMode']}
          checkedChildren="Local only"
          unCheckedChildren="Local only"
          tooltip={'If unchecked, both of categories will be used.'}
          initialValue={false}
          colProps={{ span: 'auto' }}
        />
        <ProFormTreeSelect
          placeholder="Select categories"
          request={async () => props.treeData}
          allowClear
          name={'categories'}
          label={
            <Space>
              <span>Categories local only &nbsp;&nbsp;</span>
              <Tag>{props.initialValues?.sku}</Tag>{' '}
              <Tag>{props.initialValues?.ean_texts?.[0]?.name ?? props.initialValues?.item?.name}</Tag>
            </Space>
          }
          // tree-select args
          fieldProps={{
            filterTreeNode: true,
            showSearch: true,
            dropdownMatchSelectWidth: false,
            autoClearSearchValue: true,
            multiple: true,
            labelInValue: true,
            showArrow: true,
            treeLine: true,
            treeNodeFilterProp: 'title',
            fieldNames: {
              label: 'title',
            },
          }}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateCategoriesForm;
