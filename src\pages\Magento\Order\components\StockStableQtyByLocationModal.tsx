/* eslint-disable @typescript-eslint/dot-notation */
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Card, Col, Row, Space, Spin, Typography, Image, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormGroup } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import _ from 'lodash';
import { getStockByEanId } from '@/services/foodstore-one/Stock/stock-stable';
import Util, { ni, sn } from '@/util';
import StockMovementListModal from '@/pages/Stock/StockMovement/StockMovementListModal';
import EanTitleComp from '@/components/EanTitleComp';
import SocialLinks from '@/pages/Item/EanList/components/SocialIcons';
import StockStableCorrectionEditableTableByLocation from './StockStableCorrectionEditableTableByLocation';
import Draggable from 'react-draggable';
import SProFormDigit from '@/components/SProFormDigit';
import {
  createOrUpdateStockStableProblem,
  getStockStableProblemByParam,
} from '@/services/foodstore-one/Stock/stock-stable-problem';
import { StockStableProblemStatus } from '@/pages/Stock/StockStableProblem';

export type FormValueType = Partial<API.Ean>;
export type StockQtyDataType = {
  total_row?: Partial<API.StockStable>;
  rows_by_exp_date?: Partial<API.StockStable & { oldest_ibo?: API.Ibo }>[];
  rows?: Partial<API.StockStable>[];
  qty_processing: number;
};

export type StockStableQtyByLocationModalProps = {
  initialValues?: Partial<API.StockStable>;
  wl?: Partial<API.WarehouseLocation>;
  modalVisible: boolean;
  orderItem?: API.OrderItem;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean, isParent?: boolean) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const StockStableQtyByLocationModal: React.FC<StockStableQtyByLocationModalProps> = (props) => {
  const { initialValues, wl, orderItem } = props;

  const { item_ean: itemEan } = initialValues || {};

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingSsProblem, setLoadingSsProblem] = useState<boolean>(false);

  const formRef = useRef<ProFormInstance>();
  const [data, setData] = useState<StockQtyDataType>({ qty_processing: 0 });
  const [ssProblem, setSsProblem] = useState<API.StockStableProblem>();
  const [refresh, setRefresh] = useState<boolean>(false);
  const [itemMode, setItemMode] = useState<boolean>(false);

  // open stock movement modal
  const [openStockMovementModal, setOpenStockMovementModal] = useState<boolean>(false);

  // draggable
  const draggleRef = useRef<HTMLDivElement>(null);

  const loadStockStableProblem = useCallback(
    (ssId?: number) => {
      if (ssId) {
        setLoadingSsProblem(false);
        getStockStableProblemByParam({ stock_stable_id: ssId, order_item_id: orderItem?.item_id })
          .then((res) => {
            setSsProblem(res);
            if (res) {
              formRef.current?.setFieldValue(
                'qty_picked',
                sn(itemEan?.attr_case_qty) > 1 ? res.box_qty : res.piece_qty,
              );
            } else {
              formRef.current?.setFieldValue('qty_picked', orderItem?.qty_ordered);
            }
          })
          .catch(Util.error)
          .finally(() => {
            setLoadingSsProblem(false);
          });
      } else {
        setSsProblem(undefined);
      }
    },
    [itemEan?.attr_case_qty, orderItem?.qty_ordered, orderItem?.item_id],
  );

  useEffect(() => {
    if (props.modalVisible) {
      loadStockStableProblem(initialValues?.id);
    }
  }, [initialValues?.id, props.modalVisible]);

  useEffect(() => {
    if (!props.modalVisible || !itemEan?.id) return;
    setLoading(true);
    getStockByEanId(itemEan?.id, {
      item_id: itemEan.item_id,
      item_mode: formRef.current?.getFieldValue('include_all') ? 1 : 0,
      show_zero: formRef.current?.getFieldValue('show_zero') ? 1 : 0,
      wl_id: wl?.id,
    })
      .then((res) => setData(res))
      .finally(() => setLoading(false));
  }, [itemEan?.id, props.modalVisible, refresh, itemEan?.item_id]);

  return (
    <>
      <ModalForm
        title={
          <>
            Stock Detail on Location <Typography.Text copyable>{wl?.name}</Typography.Text>
            <EanTitleComp itemEan={itemEan} />
            <SocialLinks ean={itemEan?.ean || ''} title={itemEan?.ean_texts?.[0]?.name} style={{ marginLeft: 50 }} />
            <Button
              type="primary"
              ghost
              size="small"
              style={{ float: 'right', marginRight: 40 }}
              title="View stock movements..."
              onClick={() => setOpenStockMovementModal(true)}
            >
              Stock Movements...
            </Button>
          </>
        }
        width={1350}
        visible={props.modalVisible}
        onVisibleChange={props.handleModalVisible}
        layout="inline"
        modalProps={{
          maskClosable: false,
          className: itemEan?.is_single ? 'm-single' : 'm-multi',
          modalRender: (modal) => (
            <Draggable
              disabled={true}
              // bounds={bounds}
              // onStart={(event, uiData) => onStart(event, uiData)}
            >
              <div ref={draggleRef}>{modal}</div>
            </Draggable>
          ),
        }}
        formRef={formRef}
        onFinish={async (values) => {
          if (props.onSubmit) {
            props.onSubmit(values);
          }
          props.handleModalVisible(false);
        }}
        submitter={false}
      >
        <Spin spinning={loading}>
          <Card bordered={false} size="small" headStyle={{ fontSize: 16, paddingLeft: 0 }}>
            <Row>
              <Col flex="0 0 200px">
                <Image.PreviewGroup>
                  {itemEan?.files &&
                    itemEan.files.map((file, ind) => (
                      <Image
                        key={file.id}
                        src={file.thumb_url}
                        preview={{
                          src: file.url,
                        }}
                        wrapperStyle={{
                          display: ind > 0 ? 'none' : 'inline-block',
                          border: '1px solid #ddd',
                          overflow: 'hidden',
                        }}
                        height={150}
                      />
                    ))}
                </Image.PreviewGroup>
              </Col>
              <Col flex="auto">
                <Space size={4} direction="vertical">
                  <Space size={16}>
                    <label>Ean Name</label>
                    <Typography.Paragraph
                      className="bold"
                      copyable={{
                        text: itemEan?.ean_texts?.[0]?.name || '',
                        tooltips: 'Copy EAN Name',
                      }}
                      style={{ display: 'inline-block', marginBottom: 0 }}
                    >
                      {itemEan?.ean_texts?.[0]?.name ?? itemEan?.ean_text_de?.name ?? '-'}
                    </Typography.Paragraph>
                  </Space>
                  <Space size={16}>
                    <label>EAN</label>
                    <Typography.Paragraph
                      className="bold"
                      copyable={{
                        text: itemEan?.ean || '',
                        tooltips: 'Copy EAN',
                      }}
                      style={{ display: 'inline-block', marginBottom: 0 }}
                    >
                      {itemEan?.ean ?? '-'}
                    </Typography.Paragraph>
                  </Space>
                  <Space size={16}>
                    <label>SKU</label>
                    <Typography.Paragraph
                      className="bold"
                      copyable={{
                        text: itemEan?.sku || '',
                        tooltips: 'Copy SKU',
                      }}
                      style={{ display: 'inline-block', marginBottom: 0 }}
                    >
                      {itemEan?.sku ?? '-'}
                    </Typography.Paragraph>
                  </Space>

                  <Space size={16} style={{ marginTop: 16 }}>
                    <label>Location:</label>
                    <Typography.Text className="bold" copyable>
                      {wl?.name}
                    </Typography.Text>
                  </Space>
                  {/* <Space size={16}>
                    <label>Stock Qty:</label>
                    <div>
                      <span className="text-md bold">{ni(data?.total_row?.piece_qty, true)}</span> pcs
                    </div>
                    <div>
                      + <span className="text-md bold">{ni(data?.total_row?.box_qty, true)}</span> boxes
                    </div>
                    <div>
                      <span className="text-md bold"> = {ni(data?.total_row?.total_piece_qty, true)}</span> pcs
                    </div>
                  </Space> */}
                </Space>
              </Col>

              <Col flex="0 0 300px">
                <label>Order ID: </label>
                <Typography.Text className="bold" copyable>
                  {orderItem?.order_id}
                </Typography.Text>
              </Col>
              <Col flex="0 0 200px">
                <ProFormGroup>
                  {!itemEan?.is_single && (
                    <ProFormSwitch
                      width="xs"
                      name={['include_all']}
                      label="Include All"
                      tooltip="Include All EANs of this item"
                      fieldProps={{
                        onChange: (value) => {
                          setItemMode(value);
                          setRefresh((prev) => !prev);
                        },
                      }}
                    />
                  )}

                  <ProFormSwitch
                    width="xs"
                    name={['show_zero']}
                    label="Show finished?"
                    tooltip="Show finished stocks."
                    fieldProps={{
                      onChange: (value) => {
                        setRefresh((prev) => !prev);
                      },
                    }}
                  />
                </ProFormGroup>
              </Col>
            </Row>
            <Row>
              <Col flex="0 0 200px"></Col>
              <Col flex="0 0 300px">
                <label>Original Qty: </label>
                <span className="bold">
                  {orderItem?.qty_ordered}
                  {itemEan?.is_single ? ' pcs' : ' boxes'}
                  {!itemEan?.is_single ? ` (${sn(itemEan?.attr_case_qty) * sn(orderItem?.qty_ordered)} pcs)` : ''}
                </span>
              </Col>
              <Col>
                <SProFormDigit
                  name="qty_picked"
                  label={`${itemEan?.is_single ? 'Pieces' : 'Boxes'} picked`}
                  width="xs"
                  disabled={loadingSsProblem}
                  max={sn(orderItem?.qty_ordered)}
                />
              </Col>
              <Col flex="auto">
                <Button
                  type="primary"
                  onClick={() => {
                    setLoadingSsProblem(true);
                    const hide = message.loading('Updating...', 0);
                    const qty_picked = sn(formRef?.current?.getFieldValue('qty_picked'));
                    createOrUpdateStockStableProblem({
                      stock_stable_id: initialValues?.id,
                      sku: itemEan?.sku,
                      qty_ordered: orderItem?.qty_ordered,
                      qty_missing: sn(orderItem?.qty_ordered) - qty_picked,
                      order_id: orderItem?.order_id,
                      order_item_id: orderItem?.item_id,
                      status: StockStableProblemStatus.New,
                      ...formRef.current?.getFieldsValue(),
                    })
                      .then((res) => {
                        setSsProblem(res);
                        message.success('Updated successfully.');
                      })
                      .catch(Util.error)
                      .finally(() => {
                        hide();
                        setLoadingSsProblem(false);
                      });
                  }}
                >
                  Save
                </Button>
              </Col>
            </Row>
            <Row>
              <Col flex="0 0 200px"></Col>
              <Col flex="0 0 300px"></Col>
              <Col>
                <label style={{ paddingRight: 20 }}>Missing Qty:</label>
                <ProFormDependency name={['qty_picked']}>
                  {(depValues) => {
                    const qty_ordered = sn(orderItem?.qty_ordered);
                    const qty_missing = qty_ordered - sn(depValues.qty_picked);

                    return qty_missing > 0 ? (
                      <>
                        <span className="bold c-red">
                          {ni(qty_missing, true)}
                          {itemEan?.is_single ? ' pcs' : ' boxes'}
                          {!itemEan?.is_single ? ` (${sn(itemEan?.attr_case_qty) * sn(qty_missing)} pcs)` : ''}
                        </span>
                      </>
                    ) : (
                      <span className="c-green">None</span>
                    );
                  }}
                </ProFormDependency>
              </Col>
            </Row>
          </Card>

          {/* <Card title="Summary" bordered={false} size="small" headStyle={{ fontSize: 16, paddingLeft: 0 }}>
            <Row>
              <Col span={10}>
                <Space>
                  <div>Total Qty:</div>
                  <div>
                    <span className="text-md bold">{ni(data?.total_row?.piece_qty, true)}</span> pcs
                  </div>
                  <div>
                    + <span className="text-md bold">{ni(data?.total_row?.box_qty, true)}</span> boxes
                  </div>
                  <div>
                    <span className="text-md bold"> = {ni(data?.total_row?.total_piece_qty, true)}</span> pcs
                  </div>
                </Space>
              </Col>
              <Col span={10}>
                <Space>
                  <div>
                    Magento Stock Qty{' '}
                    <InfoCircleOutlined
                      title={`Last synced at ${Util.dtToDMYHHMM(
                        appSettings?.magDsStat?.['xmag_inventory_stockbase']?.['last_sync_at'],
                        '-',
                      )}\n\n Formula: Stock Qty=Salable Qty + Reserved Qty`}
                    />
                  </div>
                  <div>
                    <span className="text-md bold" title="Salable Qty + Reserved Qty">
                      {ni(itemEan?.mag_inventory_stocks_sum_quantity, true)}
                      {sn(itemEan?.mag_inventory_stocks_sum_res_quantity)
                        ? ` = ${
                            sn(itemEan?.mag_inventory_stocks_sum_quantity) -
                            sn(itemEan?.mag_inventory_stocks_sum_res_quantity)
                          } + ${itemEan?.mag_inventory_stocks_sum_res_quantity}`
                        : ''}
                    </span>
                    {itemEan?.is_single ? ' pcs' : ' boxes'}
                  </div>
                </Space>
              </Col>
              <Col span={4}>
                Processing Qty: <span className="text-md bold">{ni(data.qty_processing, true)}</span>
              </Col>
            </Row>
          </Card> */}

          {/* <ProTable
            headerTitle={'Qty by Exp.Date'}
            rowKey={'exp_date'}
            columns={columns}
            dataSource={data?.rows_by_exp_date || []}
            search={false}
            cardProps={{ bodyStyle: { padding: 0, marginBottom: 16 } }}
            style={{ width: 800 }}
            toolbar={{
              search: false,
              actions: [],
              settings: undefined,
            }}
            pagination={false}
            size="small"
          /> */}

          <StockStableCorrectionEditableTableByLocation
            ean={{
              id: sn(itemEan?.id),
              item_id: sn(itemEan?.item_id),
              ean: itemEan?.ean,
              sku: itemEan?.sku,
              is_single: itemEan?.is_single,
              parent_id: itemEan?.parent_id,
            }}
            wl_id={wl?.id}
            reload={() => setRefresh((prev) => !prev)}
            ssProblem={ssProblem}
            orderItem={{ order_id: orderItem?.order_id, item_id: orderItem?.item_id }}
            itemMode={itemMode}
            initialData={data?.rows}
          />
        </Spin>
      </ModalForm>

      <StockMovementListModal
        searchParams={{
          sku: itemEan?.sku,
          ean: itemEan?.ean,
          is_single: itemEan?.is_single,
          wl_id: wl?.id,
        }}
        modalVisible={openStockMovementModal}
        handleModalVisible={setOpenStockMovementModal}
      />
    </>
  );
};

export default StockStableQtyByLocationModal;
