<?php
exit;
/**
 *
 * Down syncing orders from Magento. Changed orders will be synced into xmag_order table.
 *      Order shipments history will be updated.
 *
 * @package     Cron job script.
 * @since       2023-01-31
 */
error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

/** @var \Slim\Container $container */
/** @var \App\Service\Magento\Order\MagOrderService $mOrderService */
$mOrderService = $container->get('mag_order_service');

$mOrderService->dsLatestOrders();