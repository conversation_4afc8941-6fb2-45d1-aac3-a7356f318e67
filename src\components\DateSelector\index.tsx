import type { ProFormFieldProps } from '@ant-design/pro-form';

import styles from './index.less';
import { CloseOutlined, EllipsisOutlined } from '@ant-design/icons';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Button, Modal, Popover, Row, Space } from 'antd';
import ProCard from '@ant-design/pro-card';
import { numTo2Digits } from '@/util';
import moment from 'moment';

const THIS_YEAR = new Date().getFullYear();

export const SELECTION_STEPS = [1, 2, 3];

export const KEYPAD_LETTERS: Record<number, number[]> = {
  1: [THIS_YEAR, THIS_YEAR + 1, THIS_YEAR + 2, THIS_YEAR + 3],
  2: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
  3: [],
};

const YEARS_EXTRA = [...Array(21)].map((x, ind) => ind - 10 + THIS_YEAR).filter((x) => !KEYPAD_LETTERS[1].includes(x));

export const getDaysArray = (year: number, month: number) => {
  const lastDate = new Date(year, month, 0).getDate();
  return [...Array(lastDate)].map((x, ind) => ind + 1);
};

export const INITIAL_OPEN_OTHERS = SELECTION_STEPS.reduce((prev: Record<number, boolean>, x) => {
  prev[x] = false;
  return prev;
}, {});

type DateSelectorType = {
  defaultValue?: string;
  onChange: (value: string) => void;
  eleOptions?: ProFormFieldProps & { showDecimal?: boolean };
  showBodyScroll?: boolean;
  isMobile?: boolean;
  actionsRender?: (value: string, setOpen?: Dispatch<SetStateAction<boolean>>) => any;
  buttonRender?: (value: string, open?: boolean, setOpen?: Dispatch<SetStateAction<boolean>>) => any;
  value?: any; // from antd pro form
};

/**
 * Numpad
 */
const DateSelector: React.FC<DateSelectorType> = ({
  defaultValue,
  onChange,
  showBodyScroll,
  isMobile,
  actionsRender,
  buttonRender,
  value,
  eleOptions,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [openOthers, setOpenOthers] = useState<Record<number, boolean>>(INITIAL_OPEN_OTHERS);

  const [year, setYear] = useState<number>(THIS_YEAR);
  const [month, setMonth] = useState<number>();
  const [date, setDate] = useState<number>();

  const eraseValues = () => {
    setYear(THIS_YEAR);
    setMonth(undefined);
    setDate(undefined);
  };

  useEffect(() => {
    if (defaultValue && defaultValue.length >= 10) {
      const m = moment(defaultValue);
      if (m.isValid()) {
        setYear(m.year());
        setMonth(m.month());
        setDate(m.date());
      }
    } else {
      eraseValues();
    }
  }, [defaultValue]);

  useEffect(() => {
    if (value && value.length >= 10) {
      const m = moment(value);
      if (m.isValid()) {
        setYear(m.year());
        setMonth(m.month() + 1);
        setDate(m.date());
      } else {
        eraseValues();
      }
    } else {
      eraseValues();
    }
  }, [value]);

  useEffect(() => {
    if (year && month && date && `${year}`.length == 4) {
      onChange?.(`${year}-${numTo2Digits(month)}-${numTo2Digits(date)}`);
      setOpen(false);
      setOpenOthers(INITIAL_OPEN_OTHERS);
    }
  }, [year, month, date]);

  const formattedDate = useMemo(() => {
    if (year && month && date && `${year}`.length == 4) {
      return `${numTo2Digits(date)}.${numTo2Digits(month)}.${year}`;
    } else {
      return null;
    }
  }, [year, month, date]);

  return (
    <>
      {/* <Popover
        placement="bottom"
        open={open}
        onOpenChange={setOpen}
        trigger={['click']}
        overlayStyle={{ zIndex: 1052 }}
        content={
          <ProCard
            className={styles.content}
            title={
              <Space>
                <div style={{ width: eleOptions?.width ?? 360 }}>{formattedDate ?? 'Select'}</div>
              </Space>
            }
            headerBordered
            bodyStyle={{
              padding: 0,
              ...(showBodyScroll ? { maxHeight: 'calc(100vh - 250px)', overflowY: 'auto' } : {}),
            }}
            headStyle={{
              padding: 0,
            }}
            extra={
              <Space>
                {actionsRender ? (
                  actionsRender(value, setOpen)
                ) : (
                  <Button
                    type="primary"
                    size="large"
                    disabled={!formattedDate}
                    className="btn-green"
                    onClick={() => {
                      setOpenOthers(INITIAL_OPEN_OTHERS);
                      setOpen(false);
                      onChange(formattedDate ? `${year}-${numTo2Digits(month)}-${numTo2Digits(date)}` : '');
                    }}
                  >
                    OK
                  </Button>
                )}
                <Button
                  icon={<CloseOutlined />}
                  size="large"
                  style={{ marginLeft: 32 }}
                  onClick={() => {
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                    setOpen(false);
                  }}
                />
              </Space>
            }
          >
            <Row className={`step step1`}>
              {KEYPAD_LETTERS[1].map((letter) => {
                return (
                  <Button
                    key={`${1}_${letter}`}
                    size="large"
                    type={letter == year ? 'primary' : 'default'}
                    className={`keypad-btn${letter == year ? ' selected' : ''}`}
                    onClick={() => {
                      setYear(letter);
                      setOpenOthers(INITIAL_OPEN_OTHERS);
                    }}
                  >
                    {letter}
                  </Button>
                );
              })}
              <Popover
                placement="bottom"
                trigger={[]}
                open={openOthers[1]}
                onOpenChange={(visible) => setOpenOthers((prev) => ({ ...prev, [1]: visible }))}
                overlayStyle={{ zIndex: 1053 }}
                content={
                  <div className={styles.contentSmall}>
                    <Row className={`step step${1}`}>
                      {YEARS_EXTRA.map((letter) => (
                        <Button
                          key={`${1}_${letter}`}
                          size="large"
                          type={letter == year ? 'primary' : 'default'}
                          className={`keypad-btn keypad-btn-sm`}
                          onClick={() => {
                            setYear(letter);
                            setOpenOthers(INITIAL_OPEN_OTHERS);
                          }}
                        >
                          {letter}
                        </Button>
                      ))}
                    </Row>
                  </div>
                }
              >
                <Button
                  size="large"
                  icon={<EllipsisOutlined />}
                  className={`other`}
                  onClick={(e) => {
                    setOpenOthers((prev) => ({ ...prev, [1]: !prev[1] }));
                  }}
                />
              </Popover>
            </Row>

            <Row className={`step step2`}>
              {KEYPAD_LETTERS[2].map((letter) => {
                return (
                  <Button
                    key={`${1}_${letter}`}
                    size="large"
                    type={letter == month ? 'primary' : 'default'}
                    className={`keypad-btn keypad-btn-month${letter == month ? ' selected' : ''}`}
                    onClick={() => {
                      setMonth(letter);
                      setOpenOthers(INITIAL_OPEN_OTHERS);
                    }}
                  >
                    {letter}
                  </Button>
                );
              })}
            </Row>

            {year && month ? (
              <Row className={`step step2`}>
                {getDaysArray(year, month).map((letter) => {
                  return (
                    <Button
                      key={`${1}_${letter}`}
                      size="large"
                      type={letter == date ? 'primary' : 'default'}
                      className={`keypad-btn keypad-btn-month${letter == date ? ' selected' : ''}`}
                      onClick={() => {
                        setDate(letter);
                        setOpenOthers(INITIAL_OPEN_OTHERS);
                      }}
                    >
                      {letter}
                    </Button>
                  );
                })}
              </Row>
            ) : null}
          </ProCard>
        }
      >
        {buttonRender ? (
        buttonRender(value)
      ) : (
        <Button
          type="primary"
          size="large"
          onClick={() => setOpen(true)}
          style={{ width: eleOptions?.width ?? (isMobile ? 160 : 130) }}
        >
          {formattedDate ? `${formattedDate}` : 'Select Date'}
        </Button>
      )}
      </Popover> */}
      {buttonRender ? (
        buttonRender(value, open, setOpen)
      ) : (
        <Button
          type="primary"
          size="large"
          onClick={() => setOpen(true)}
          style={{ width: eleOptions?.width ?? (isMobile ? 160 : 130) }}
        >
          {formattedDate ? `${formattedDate}` : 'Select Date'}
        </Button>
      )}

      <Modal open={open} footer={false} width={980} closable={false} maskClosable={false} title={null}>
        <ProCard
          className={styles.content}
          title={
            <Space>
              <div style={{ width: eleOptions?.width ?? 360 }}>{formattedDate ?? 'Select'}</div>
            </Space>
          }
          headerBordered
          bodyStyle={{
            padding: 0,
            ...(showBodyScroll ? { maxHeight: 'calc(100vh - 250px)', overflowY: 'auto' } : {}),
          }}
          headStyle={{
            padding: 0,
          }}
          extra={
            <Space>
              {actionsRender ? (
                actionsRender(value, setOpen)
              ) : (
                <Button
                  type="primary"
                  size="large"
                  disabled={!formattedDate}
                  className="btn-green"
                  onClick={() => {
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                    setOpen(false);
                    onChange(formattedDate ? `${year}-${numTo2Digits(month)}-${numTo2Digits(date)}` : '');
                  }}
                >
                  OK
                </Button>
              )}
              <Button
                icon={<CloseOutlined />}
                size="large"
                style={{ marginLeft: 32 }}
                onClick={() => {
                  setOpenOthers(INITIAL_OPEN_OTHERS);
                  setOpen(false);
                }}
              />
            </Space>
          }
        >
          <Row className={`step step1`}>
            {KEYPAD_LETTERS[1].map((letter) => {
              return (
                <Button
                  key={`${1}_${letter}`}
                  size="large"
                  type={letter == year ? 'primary' : 'default'}
                  className={`keypad-btn${letter == year ? ' selected' : ''}`}
                  onClick={() => {
                    setYear(letter);
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                  }}
                >
                  {letter}
                </Button>
              );
            })}
            <Popover
              placement="bottom"
              trigger={[]}
              open={openOthers[1]}
              onOpenChange={(visible) => setOpenOthers((prev) => ({ ...prev, [1]: visible }))}
              overlayStyle={{ zIndex: 1053 }}
              content={
                <div className={styles.contentSmall}>
                  <Row className={`step step${1}`}>
                    {YEARS_EXTRA.map((letter) => (
                      <Button
                        key={`${1}_${letter}`}
                        size="large"
                        type={letter == year ? 'primary' : 'default'}
                        className={`keypad-btn keypad-btn-sm`}
                        onClick={() => {
                          setYear(letter);
                          setOpenOthers(INITIAL_OPEN_OTHERS);
                        }}
                      >
                        {letter}
                      </Button>
                    ))}
                  </Row>
                </div>
              }
            >
              <Button
                size="large"
                icon={<EllipsisOutlined />}
                className={`other`}
                onClick={(e) => {
                  setOpenOthers((prev) => ({ ...prev, [1]: !prev[1] }));
                }}
              />
            </Popover>
          </Row>

          <Row className={`step step2`}>
            {KEYPAD_LETTERS[2].map((letter) => {
              return (
                <Button
                  key={`${1}_${letter}`}
                  size="large"
                  type={letter == month ? 'primary' : 'default'}
                  className={`keypad-btn keypad-btn-month${letter == month ? ' selected' : ''}`}
                  onClick={() => {
                    setMonth(letter);
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                  }}
                >
                  {letter}
                </Button>
              );
            })}
          </Row>

          {year && month ? (
            <Row className={`step step2`}>
              {getDaysArray(year, month).map((letter) => {
                return (
                  <Button
                    key={`${1}_${letter}`}
                    size="large"
                    type={letter == date ? 'primary' : 'default'}
                    className={`keypad-btn keypad-btn-month${letter == date ? ' selected' : ''}`}
                    onClick={() => {
                      setDate(letter);
                      setOpenOthers(INITIAL_OPEN_OTHERS);
                    }}
                  >
                    {letter}
                  </Button>
                );
              })}
            </Row>
          ) : null}
        </ProCard>
      </Modal>
    </>
  );
};

export default DateSelector;
