DROP TABLE IF EXISTS `gdsn_code`;

CREATE TABLE `gdsn_code`
(
    `type`  varchar(255) NOT NULL,
    `code`  varchar(255) NOT NULL,
    `value` varchar(1024) DEFAULT NULL,
    PRIMARY KEY (`type`, `code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

/*Data for the table `gdsn_code` */

insert into `gdsn_code`(`type`, `code`, `value`)
values ('measurementUnitCode', 'E14', 'Kilokalorien (kcal)'),
       ('measurementUnitCode', 'GRM', 'g'),
       ('measurementUnitCode', 'KJO', 'Kilojoule (kJ)'),
       ('measurementUnitCode', 'MC', 'µg'),
       ('measurementUnitCode', 'MGM', 'mg'),
       ('measurementUnitCode', 'MLT', 'ml'),
       ('nutrientTypeCode', 'BIOT', 'Biotin'),
       ('nutrientTypeCode', 'CA', 'Calcium'),
       ('nutrientTypeCode', 'CHOAVL', 'Kohlenhydrate'),
       ('nutrientTypeCode', 'CLD', 'Chlor'),
       ('nutrientTypeCode', 'CR', 'Chromium'),
       ('nutrientTypeCode', 'CU', 'Copper'),
       ('nutrientTypeCode', 'ENER-', 'Brennwert'),
       ('nutrientTypeCode', 'FAMSCIS', 'einfach ungesättigte Fettsäuren'),
       ('nutrientTypeCode', 'FAPUCIS', 'mehrfach ungesättigte Fettsäuren'),
       ('nutrientTypeCode', 'FASAT', 'gesättigte Fettsäuren'),
       ('nutrientTypeCode', 'FAT', 'Fett'),
       ('nutrientTypeCode', 'FD', 'Fluoride'),
       ('nutrientTypeCode', 'FE', 'Iron'),
       ('nutrientTypeCode', 'FIBTG', 'Balaststoffe'),
       ('nutrientTypeCode', 'FOLDFE', 'Folic Acid'),
       ('nutrientTypeCode', 'ID', 'Iodine'),
       ('nutrientTypeCode', 'K', 'Potassium'),
       ('nutrientTypeCode', 'MG', 'agnesium'),
       ('nutrientTypeCode', 'MN', 'Mangan'),
       ('nutrientTypeCode', 'MO', 'Molybdenum'),
       ('nutrientTypeCode', 'NIA', 'Niacin'),
       ('nutrientTypeCode', 'P', 'PhosphorusZN Zink'),
       ('nutrientTypeCode', 'PANTAC', 'Pantothenic acid'),
       ('nutrientTypeCode', 'POLYL', ' mehrwertige Alkohole'),
       ('nutrientTypeCode', 'PRO-', 'Eiweiß'),
       ('nutrientTypeCode', 'RIBF', 'Riboflavin'),
       ('nutrientTypeCode', 'SALTEQ', 'Salz'),
       ('nutrientTypeCode', 'SE', 'Selenium'),
       ('nutrientTypeCode', 'STARCH', 'Stärke'),
       ('nutrientTypeCode', 'SUGAR-', 'Zucker'),
       ('nutrientTypeCode', 'THIA', 'Thiamin'),
       ('nutrientTypeCode', 'VITA-', 'Vitamin A'),
       ('nutrientTypeCode', 'VITB12', 'Vitamin B12'),
       ('nutrientTypeCode', 'VITB6-', 'Vitamin B6'),
       ('nutrientTypeCode', 'VITC-', 'Vitamin C'),
       ('nutrientTypeCode', 'VITD-', 'Vitamin D'),
       ('nutrientTypeCode', 'VITE-', 'Vitamin E'),
       ('nutrientTypeCode', 'VITK', 'Vitamin K');
