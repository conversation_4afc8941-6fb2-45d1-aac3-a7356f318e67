import Util from '@/util';
import { LoadingOutlined } from '@ant-design/icons';
import { Modal, Progress, message } from 'antd';
import { useCallback, useMemo, useState } from 'react';

export type ModalDataType = { title: string; desc?: string; abortOnError?: boolean };

export type ProcessFnType = (...args: any[]) => Promise<any>;

const useBatchProcess = (defaultModalData?: ModalDataType) => {
  const { title, desc, abortOnError } = defaultModalData || { abortOnError: true };

  const [batchUpSyncModalVisible, handleBatchUpSyncModalVisible] = useState<boolean>(false);
  const [batchUpSyncProgress, setBatchUpSyncProgress] = useState(0);

  const initialBatchModalData = useMemo(() => ({
    title: title ?? 'Batch Up Syncing...',
    desc: desc ?? 'Batch UpSync is in progress. Please wait...',
  }), [title, desc]);

  const [batchModalData, setBatchModalData] = useState<ModalDataType>(initialBatchModalData);

  // processing states
  // const [processes, setProcesses] = useState<any[]>([]);

  const run = useCallback(
    async (processes: ProcessFnType[], args?: any[][]) => {
      if (!processes?.length) return Promise.resolve();

      const totalCount = processes.length;
      const skip = 100 / totalCount;

      setBatchUpSyncProgress(0);
      setBatchModalData({ ...initialBatchModalData, title: `${initialBatchModalData.title} ${0} of ${totalCount} items.` });

      handleBatchUpSyncModalVisible(true);

      let ind = 0;
      for (const x of processes) {
        await x()
          // eslint-disable-next-line @typescript-eslint/no-loop-func
          .then((res: any) => {
            setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
            setBatchModalData({ ...initialBatchModalData, title: `${initialBatchModalData.title} ${++ind} of ${totalCount} items.` });
          })
          .catch((error: any) => {
            Util.error(error);
            if (abortOnError) {
              handleBatchUpSyncModalVisible(false);
            }
            return Promise.reject();
          });
      }
      setBatchUpSyncProgress(100);
      setBatchModalData({ ...initialBatchModalData, title: `${initialBatchModalData.title} ${totalCount} of ${totalCount} items.` });
      setTimeout(() => {
        handleBatchUpSyncModalVisible(false);
        message.success('Completed');
      }, 800);
    },
    [abortOnError, initialBatchModalData],
  );

  const modalElement = useMemo(() => {
    return (
      <Modal title={batchModalData.title} centered visible={batchUpSyncModalVisible} closable={false} footer={null} >
        {batchUpSyncProgress >= 100 ? (
          <p>Completed!</p>
        ) : (
          <p>
            <LoadingOutlined /> {batchModalData.desc ?? 'Batch UpSync is in progress. Please wait...'}
          </p>
        )}
        <Progress
          percent={batchUpSyncProgress}
          style={{ width: '100%' }}
          status={batchUpSyncProgress >= 100 ? 'success' : 'active'}
        />
      </Modal>
    );
  }, [batchModalData.desc, batchModalData.title, batchUpSyncModalVisible, batchUpSyncProgress]);

  return { modalElement, setBatchModalData, handleBatchUpSyncModalVisible, setBatchUpSyncProgress, run };
};

export default useBatchProcess;
