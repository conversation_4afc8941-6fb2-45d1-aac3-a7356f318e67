import Util from '@/util';
import { LoadingOutlined } from '@ant-design/icons';
import type { ProFormFieldProps } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';
import SDatePicker from '@/components/SDatePicker';

type HtmlFieldType =
  | 'number'
  | 'text'
  | 'textarea'
  | 'select'
  | 'switch'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'date'
  | 'divider';

type EditableCellProps = {
  dataType: HtmlFieldType;
  defaultValue?: any;
  children: React.ReactNode;
  precision?: number; // Number only
  dataOptions?: any; // Select only
  fieldProps?: ProFormFieldProps;
  style?: any;
  triggerUpdate: (value: any, cancelEdit?: () => void) => Promise<void>;
} & Partial<ProFormSelectProps> &
  Partial<ProFormFieldProps>;

const EditableCell: React.FC<EditableCellProps> = ({
  dataType,
  defaultValue,
  triggerUpdate,
  children,

  precision,
  dataOptions,
  fieldProps,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [changedValue, setChangedValue] = useState<any>(defaultValue);

  const inputRef = useRef<any>(null);

  useEffect(() => {
    if (editing) {
      inputRef?.current?.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing((prev) => !prev);
  };

  let editorNode = null;
  const defaultFormItemProps = { style: { ...restProps.style, marginBottom: 0 } };
  const defaultEditorFieldProps: any = {
    ...fieldProps,
    ref: inputRef,
    defaultValue: defaultValue,
    size: 'small',
    allowClear: false,
    onChange: (value: any) => {
      if (dataType == 'date') {
        setChangedValue(Util.dtToYMD(value));
      } else if (dataType == 'switch' || dataType == 'select') {
        setLoading(true);
        triggerUpdate(value, () => setEditing(false));
      } else {
        setChangedValue(value);
      }
    },
    onKeyDown: (e: any) => {
      if (Util.isEnterKey(e)) {
        inputRef.current.blur();
      }
    },
    onBlur: (e: any) => {
      let newValue = null;
      if (dataType == 'number') {
        newValue = changedValue;
      } else if (dataType == 'date') {
        newValue = changedValue;
      } else if (dataType == 'select') {
        newValue = changedValue;
      } else {
        newValue = e.target.value;
      }

      if (defaultValue == newValue) {
        setEditing(false);
        return;
      }
      setLoading(true);
      triggerUpdate(newValue, () => setEditing(false)).finally(() => setLoading(false));
    },
  };
  switch (dataType) {
    case 'date':
      editorNode = (
        <SDatePicker
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    case 'number':
      editorNode = (
        <SProFormDigit
          fieldProps={{
            ...defaultEditorFieldProps,
            precision: precision ?? 0,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    case 'select':
      editorNode = (
        <ProFormSelect
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
          options={dataOptions}
          {...restProps}
        />
      );
      break;
    default:
      editorNode = (
        <ProFormText
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
  }

  const childNode = editing ? (
    <Spin spinning={loading} size="small" indicator={<LoadingOutlined />}>
      {editorNode}
    </Spin>
  ) : (
    <div className="editable-cell-value-wrap" style={{ ...(restProps.style ?? {}) }} onClick={toggleEdit}>
      {children}
    </div>
  );

  return childNode;
};

export default EditableCell;
