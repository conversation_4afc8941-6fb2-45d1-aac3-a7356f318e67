-- ===========================================================
-- 0. PRE-Filling IBO in Stock_stable_booked
-- Note: This change could be override by IBO selection in Stock Warehouse page.
-- ===========================================================
UPDATE `stock_stable_booked` AS ssb
SET ibo_id = IF(parent_ean_id = ean_id, (SELECT MAX(id) FROM ibo WHERE ibo.item_id = ssb.item_id),
                (SELECT MAX(id) FROM ibo WHERE ibo.ean_id = ssb.ean_id))
WHERE ssb.`ibo_id` IS NULL
;

-- ===========================================================
-- 1. BP setting from stock_stable_booked ---
-- ===========================================================
INSERT INTO `xmag_order_item_idx` (order_item_id, sku, case_qty, bp, bp_type, qty_ordered)
SELECT `order_item_id`,
       oi.sku,
       b.case_qty,
       ROUND(SUM(total_piece_qty * IFNULL(ibo.`price`, 0)) / SUM(`total_piece_qty`), 2) AS avg_price,
       1,
       oi.qty_ordered
FROM xmag_order_item oi
         INNER JOIN `stock_stable_booked` AS b ON b.order_item_id = oi.item_id
         LEFT JOIN ibo ON ibo.id = b.`ibo_id`
GROUP BY `order_item_id`
ON DUPLICATE KEY UPDATE case_qty    = VALUES(case_qty),
                        sku         = VALUES(sku),
                        bp          = VALUES(bp),
                        bp_type     = VALUES(bp_type),
                        qty_ordered = VALUES(qty_ordered)
;

-- ===========================================================
-- 2. BP setting from latest IBO for old order items ---
-- ===========================================================
INSERT INTO `xmag_order_item_idx` (order_item_id, sku, case_qty, bp, ibo_id, bp_type, qty_ordered)
SELECT item_id,
       oi.sku,
       t.attr_case_qty,
       t.price,
       t.ibo_id,
       2,
       oi.qty_ordered
FROM xmag_order_item oi
         LEFT JOIN (
    SELECT item_ean.id,
           sku,
           item_ean.attr_case_qty,
           ibo.id AS ibo_id,
           ibo.price
    FROM item_ean
             LEFT JOIN (
        SELECT id,
               IF(parent_id = id, (SELECT MAX(id) FROM ibo WHERE ibo.item_id = e2.item_id),
                  (SELECT MAX(id) FROM ibo WHERE ibo.ean_id = e2.id)) AS max_ibo_id
        FROM item_ean e2
    ) t ON t.id = item_ean.id
             LEFT JOIN ibo ON ibo.id = t.max_ibo_id
) AS t ON t.sku = oi.sku
WHERE NOT EXISTS(SELECT order_item_id FROM stock_stable_booked b WHERE b.order_item_id = oi.item_id)
ON DUPLICATE KEY UPDATE case_qty    = VALUES(case_qty),
                        sku         = VALUES(sku),
                        bp          = VALUES(bp),
                        bp_type     = VALUES(bp_type),
                        qty_ordered = VALUES(qty_ordered),
                        ibo_id      = VALUES(ibo_id)
;
