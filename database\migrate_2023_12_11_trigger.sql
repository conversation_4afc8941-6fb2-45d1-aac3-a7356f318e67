-- After Insert trigger

drop trigger if exists import_supplier_data_after_insert;

DELIMITER $$

CREATE
    TRIGGER `import_supplier_data_after_insert`
    AFTER INSERT
    ON `import_supplier_data`
    FOR EACH ROW
BEGIN
    INSERT into import_supplier_data_price(supplier_id, ean, price, uvp, vat, created_on, created_by, updated_on,
                                           updated_by, ref_id)
    values (NEW.supplier_id, NEW.ean, NEW.price, NEW.uvp, NEW.vat, NEW.created_on, NEW.created_by, NEW.updated_on,
            NEW.updated_by, NEW.id);
END$$

DELIMITER ;

-- After Update trigger
drop trigger if exists import_supplier_data_after_update;

DELIMITER $$

CREATE
    TRIGGER `import_supplier_data_after_update`
    AFTER update
    ON `import_supplier_data`
    FOR EACH ROW
BEGIN
    if NEW.price != OLD.price || NEW.uvp != OLD.uvp || NEW.vat != OLD.vat then
        INSERT into import_supplier_data_price(supplier_id, ean, price, uvp, vat, created_on, created_by, updated_on,
                                               updated_by, ref_id)
        values (NEW.supplier_id, NEW.ean, NEW.price, NEW.uvp, NEW.vat, NOW(), NEW.created_by, NEW.updated_on,
                NEW.updated_by, NEW.id);
    end if;

    if NEW.ean != OLD.ean || NEW.supplier_id != OLD.supplier_id then
        update import_supplier_data_price
        set ean         = NEW.ean,
            supplier_id = NEW.supplier_id,
            updated_on  = NEW.updated_on,
            updated_by  = NEW.updated_by
        where ref_id = OLD.id;
    end if;

END$$

DELIMITER ;

-- After Delete trigger
drop trigger if exists import_supplier_data_after_delete;

DELIMITER $$

CREATE
    TRIGGER `import_supplier_data_after_delete`
    AFTER DELETE
    ON `import_supplier_data`
    FOR EACH ROW
BEGIN
    update import_supplier_data_price set deleted_on=NOW(), ref_id=NULL where ref_id = OLD.id;

END$$

DELIMITER ;