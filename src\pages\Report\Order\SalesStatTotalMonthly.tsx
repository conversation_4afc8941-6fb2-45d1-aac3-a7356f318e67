import { <PERSON><PERSON>, <PERSON>, Col, Row, Typography, message, Popover } from 'antd';
import React, { useRef, useState, useMemo, useCallback, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType, ColumnsState } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import { ni, sUrlByTpl, sn } from '@/util';
import Util, { nf2 } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormCheckbox, ProFormRadio, ProFormText } from '@ant-design/pro-form';
import { getSalesStatsMonthlyList } from '@/services/foodstore-one/Report/sales-stat';
import SProFormDateRange, { DRSelection } from '@/components/SProFormDateRange';
import type { DefaultOptionType } from 'antd/lib/select';
import type { TrademarkChangeCallbackHandlerTypeParamType } from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import ExpDate from './components/ExpDate';
import UpdatePriceAttributeForm from '@/pages/Item/EanList/components/UpdatePriceAttributeForm';
import SPrices from '@/components/SPrices';
import {
  CheckCircleOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import ImportSearchFiltersModalForm from './components/ImportSearchFiltersModalForm';
import { DictCode, ItemEANStatus, ItemEANStatusOptions } from '@/constants';
import style from './SalesStatTotalMonthly.less';
import moment from 'moment';
import { round } from 'lodash';
import useImagesGroupPreview from '@/pages/Item/EanList/hooks/useImagesGroupPreview';
import OrderListModal from '@/pages/Magento/Order/components/OrderListModal';
import { useModel } from 'umi';
import WebsiteIcons from '../../Item/EanList/components/WebsiteIcons';
// import UpdatePriceAttributeFormBulk from '@/pages/Item/EanList/components/UpdatePriceAttributeFormBulk';

type RecordType = API.Ean & API.OrderItem & Record<string, any>;

type OrderModalSearchParamsType = SearchFormValueType & {
  dateRange?: DateRangeType;
  source?: string;
  columnSection?: string;
} & { filtered_only?: boolean } & { trademark_name?: string };

const getCheapestXlsPrice = (record: RecordType, xlsImports: API.Import[]) => {
  let cheapestPrice = 99999999;
  xlsImports.forEach((x2) => {
    const p = record[`xls_bp_${x2.id}`] ?? record[`xls_bp2_${x2.id}`] ?? 0;
    if (p && p < cheapestPrice) {
      cheapestPrice = p;
    }
  });

  return cheapestPrice;
};

type SearchFormValueType = {
  trademark?: DefaultOptionType;
  ean?: string;
  sku?: string;
  ean_type_search?: string;

  // Date range selector
  start_date?: string;
  end_date?: string;
  dr_selection?: string;
};

const defaultSearchFormValues = {
  sku: '',
  ean: '',
  trademark: { value: '', label: `All` },
};

const SalesStatTotalMonthly: React.FC<{ route?: any }> = (props) => {
  const { getDictByCode } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [totalRow, setTotalRow] = useState<RecordType>();

  const [currentOrderItemRow, setCurrentOrderItemRow] = useState<RecordType>();

  // stock qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);

  // images preview
  const { imagePreviewGroupBody, renderPreviewItem } = useImagesGroupPreview();

  // import search filters modal
  const [currentImport, setCurrentImport] = useState<API.Import>();
  const [openImportSearchFiltersModal, setOpenImportSearchFiltersModal] = useState<boolean>(false);

  const [xlsImports, setXlsImports] = useState<API.Import[]>([]);

  // Update Actions
  // ------------------------------------------------------------------------- //
  // to do later come back???
  /* const [currentRow, setCurrentRow] = useState<RecordType>();
  const [selectedRows, setSelectedRows] = useState<RecordType[]>([]);
  const [visibleUpdatePriceFormBulk, handleVisibleUpdatePriceFormBulk] = useState<boolean>(false); */
  // ------------------------------------------------------------------------- //

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});

  /**
   * Handler to open the orders list modal.
   *
   * @param columnField
   * @param intervalType
   * @param dateRange
   * @param options
   */
  const handleQuantityOnClick = useCallback((extraFilters?: Record<string, any>) => {
    const sfValues = searchFormRef.current?.getFieldsValue() || {};
    const params: OrderModalSearchParamsType = {
      source: 'salesReport',
      filtered_only: true,
      ...sfValues,
      trademark: sfValues.trademark,
      sku: sfValues.sku,
      ...extraFilters,
    };
    console.log(params);
    setModalSearchParams(params);
    setOpenOrderListModal(true);
  }, []);

  // table column states
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>(() => {
    return {
      [`ibo_earliest3`]: { show: false },
      [`fs_special_discount`]: { show: false },
      [`ibo_age`]: { show: false },
    };
  });

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements, trademark } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
  });

  const handleUnhideAll = useCallback(
    (newStatus) => {
      const mode = searchFormRef.current?.getFieldValue('mode') ?? 'a';
      const newColStates: Record<string, ColumnsState> = {};
      if (mode == 'a') {
        newColStates.ibo_earliest3 = { show: newStatus };
        newColStates.fs_special_discount = { show: newStatus };
        newColStates.ibo_age = { show: newStatus };
        xlsImports.forEach((x) => {
          newColStates[`xls_article_no_${x.id}`] = { show: newStatus };
        });

        newColStates.ym = { show: true };
        newColStates.qty_box = { show: true };
        newColStates.qty_pcs = { show: true };
        newColStates.order_cnt = { show: true };
        newColStates.net_turnover = { show: true };
        newColStates.gp = { show: true };
        newColStates.gp_pcs = { show: true };
        newColStates.price_pcs = { show: true };

        newColStates.price1 = { show: false };
        newColStates.price2 = { show: false };
        newColStates.price_multi = { show: false };
      } else {
        newColStates.ibo_earliest3 = { show: newStatus };
        newColStates.fs_special_discount = { show: newStatus };
        newColStates.ibo_age = { show: newStatus };
        xlsImports.forEach((x) => {
          newColStates[`xls_article_no_${x.id}`] = { show: newStatus };
        });

        newColStates.ym = { show: newStatus };
        newColStates.qty_box = { show: newStatus };
        newColStates.qty_pcs = { show: newStatus };
        newColStates.order_cnt = { show: newStatus };
        newColStates.net_turnover = { show: newStatus };
        newColStates.gp = { show: newStatus };
        newColStates.gp_pcs = { show: newStatus };
        newColStates.price_pcs = { show: newStatus };

        newColStates.price1 = { show: true };
        newColStates.price2 = { show: true };
        newColStates.price_multi = { show: true };
      }

      setColStates((prev) => newColStates);
    },
    [xlsImports],
  );

  const handleViewMode = useCallback(
    (newMode?: 'a' | 'b') => {
      const unhideAll = searchFormRef.current?.getFieldValue('unhideAll') ?? false;
      handleUnhideAll(unhideAll);
    },
    [handleUnhideAll],
  );

  useEffect(() => {
    const unhideAll = searchFormRef.current?.getFieldValue('unhideAll') ?? false;
    handleUnhideAll(unhideAll);
  }, [handleUnhideAll, xlsImports]);

  const xlsColumns = useMemo<ProColumns<RecordType>[]>(() => {
    const cols: ProColumns<RecordType>[] = [];

    xlsImports.forEach((x, ind) => {
      cols.push({
        title: (
          <>
            <div style={{ textAlign: 'left' }}>
              {trademark ? (
                <Button
                  type="link"
                  size="small"
                  icon={<SearchOutlined title="Define search terms for XLS data." className="cursor-pointer" />}
                  onClick={() => {
                    setCurrentImport({
                      id: x.id,
                      supplier_id: x.supplier_id,
                      supplier: x.supplier,
                      import_search_filters: x.import_search_filters || [],
                    });
                    setOpenImportSearchFiltersModal(true);
                  }}
                  style={{ marginRight: 8 }}
                />
              ) : null}
              {x.supplier?.name}
            </div>
            <div style={{ textAlign: 'left', fontSize: 10 }}>{x.table_name?.substring(18)}</div>
          </>
        ),
        dataIndex: [`xls_bp_${x.id}`],
        sorter: false,
        align: 'right',
        width: 100,
        className: ind == 0 ? 'bl2' : '',
        render: (__, record) => {
          const price = record[`xls_bp_${x.id}`] ?? record[`xls_bp2_${x.id}`];
          const uvp = record[`xls_uvp_${x.id}`] ?? record[`xls_uvp2_${x.id}`];
          return !record.ym ? (
            <>
              <div style={{ lineHeight: 1.5, minHeight: 12 }}>{nf2(price)}</div>
              <div style={{ lineHeight: 1.5, minHeight: 12 }} className="italic text-sm" title="UVP">
                {nf2(uvp)}
              </div>
            </>
          ) : null;
        },
        onCell: (record) => {
          if (!!record.ym) return {};
          const price = record[`xls_bp_${x.id}`] ?? record[`xls_bp2_${x.id}`] ?? 0;

          let cheapestPrice = 99999999;
          xlsImports.forEach((x2) => {
            const p = record[`xls_bp_${x2.id}`] ?? record[`xls_bp2_${x2.id}`] ?? 0;
            if (p && p < cheapestPrice) {
              cheapestPrice = p;
            }
          });
          let cls = '';
          if (cheapestPrice == price && price > 0) {
            const bp_pcs = record.qty_total ? record.bp / record.qty_total : 0;
            if (price > bp_pcs) cls = 'bg-light-orange2';
            else if (price < bp_pcs) cls = 'bg-green3';
          }
          return {
            className: cls,
          };
        },
      } as ProColumnType<RecordType>);
    });

    return cols;
  }, [trademark, xlsImports]);

  const xlsColumns2 = useMemo<ProColumns<RecordType>[]>(() => {
    const cols: ProColumns<RecordType>[] = [];

    xlsImports.forEach((x, ind) => {
      cols.push({
        title: (
          <>
            <div
              style={{ textAlign: 'left' }}
              title="First Row: Item No in EAN. 2nd Row: Item No in Xls by exact EAN match"
            >
              {x.supplier?.name} - Item No
            </div>
            <div style={{ textAlign: 'left', fontSize: 10 }}>{x.table_name?.substring(18)}</div>
          </>
        ),
        dataIndex: [`xls_article_no_${x.id}`],
        sorter: false,
        align: 'left',
        width: 100,
        className: ind == 0 ? 'bl2' : '',
        render: (__, record) => {
          const no = record[`xls_article_no_${x.id}`] ?? '';
          const no2 = record[`xls_article_no2_${x.id}`] ?? '';
          return !record.ym ? (
            <>
              <div
                className="text-sm"
                style={{ minHeight: 11, lineHeight: 1.1 }}
                onClick={() => {
                  message.info('Copied.', 1);
                  navigator.clipboard.writeText(no);
                }}
              >
                {no}
              </div>
              <div
                className="text-sm italic c-grey"
                style={{ minHeight: 11, lineHeight: 1.1 }}
                onClick={() => {
                  message.info('Copied.', 1);
                  navigator.clipboard.writeText(no2);
                }}
              >
                {no2}
              </div>
            </>
          ) : null;
        },
        onCell: (record) => {
          if (!!record.ym) return {};
          return {
            className: 'text-sm',
          };
        },
      } as ProColumnType<RecordType>);
    });

    return cols;
  }, [xlsImports]);

  const columns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        title: '',
        dataIndex: ['uid'],
        width: 30,
        fixed: 'left',
        render: (dom, record) => {
          return null;
        },
      },
      {
        title: 'Item',
        dataIndex: ['ean_text_de', 'name'],
        align: 'left',
        width: 300,
        render: (dom, record) => {
          return !record.ym ? (
            <Row gutter={8} wrap={false}>
              <Col flex="auto">
                <Typography.Text ellipsis title={`${record?.ean} | ${record?.ean_text_de?.name || ''}`}>
                  <a
                    href={sUrlByTpl(getDictByCode(DictCode.MAG_SEARCH_URL), {
                      q: record.item_id,
                    })}
                    target="_blank"
                    rel="noreferrer"
                    title="Search on website"
                  >
                    {`${record.item_id || ''}_`}
                  </a>
                  {`--- ${record?.ean_text_de?.name || ''}`}
                </Typography.Text>
              </Col>
              <Col flex="0 0 20px">
                <Typography.Text
                  title={`${record?.ean} | ${record?.ean_text_de?.name || ''}`}
                  copyable={{ text: `${record?.ean} ${record?.ean_text_de?.name || ''}` }}
                >
                  {''}
                </Typography.Text>
              </Col>
              <Col flex="0 0 20px">
                <Typography.Link
                  href={`/item/ean-all-summary?sku=${record.item_id}_`}
                  title="Search EANs on new tab."
                  target="_blank"
                >
                  <LinkOutlined />
                </Typography.Link>
              </Col>
            </Row>
          ) : null;
        },
      },
      {
        dataIndex: 'gdsn',
        width: 20,
        align: 'center',
        className: 'p-0',
        title: <InfoCircleOutlined title="EAN exists in GDSN?" />,
        render: (__, record) => {
          return record.gdsn_item || record.gdsn ? (
            <CheckCircleOutlined
              // title="Exists in GDSN. Click to view details..."
              className="green"
            />
          ) : null;
        },
      },
      {
        title: 'GDSN Image',
        dataIndex: ['gdsn_item', 'detail', 'images'],
        valueType: 'image',
        sorter: false,
        align: 'center',
        width: 50,
        render: (__, record: any) => {
          return !record.ym ? renderPreviewItem(record?.gdsn_item?.detail?.images ?? record.gdsn_images ?? []) : null;
        },
      },
      {
        title: 'Exp Age',
        dataIndex: ['ibo_age'],
        align: 'right',
        width: 40,
        render: (dom, record) => {
          if (!record.id || record.ym) return null;
          const list = (record.is_single ? record?.ibo_earliest3_item : record?.ibo_earliest3) ?? [];

          let qty = 0;
          let age = 0;
          for (const x of list) {
            if (x.exp_date && x.booking_date) {
              const days = sn(moment(x.exp_date).diff(moment(x.booking_date), 'days'));
              qty += sn(x.qty);
              age += sn(x.qty) * days;
            }
          }
          if (qty) {
            age /= qty;
          }

          return ni(age);
        },
      },
      {
        title: 'Exp (Qty)',
        dataIndex: ['ibo_earliest3'],
        width: 120,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        tooltip: (
          <>
            Qty in italic means `pcs`. <br />
            <br />
            Click to view stock detail...
          </>
        ),
        className: 'cursor-pointer',
        render: (dom, record) => {
          const list = (record.is_single ? record?.ibo_earliest3_item : record?.ibo_earliest3) ?? [];

          const clsExt: Record<string, any> = {};
          const mode = searchFormRef.current?.getFieldValue('dr_selection');
          if (mode == DRSelection.DR_LAST_30_DAYS && list?.length) {
            const qtyPerDay = round(sn(record.qty_pcs) / 30, 2);
            if (qtyPerDay) {
              let qty = 0;
              for (const x of list) {
                if (!x.exp_date) continue;
                const m = moment(x.exp_date);
                if (!m.isValid()) continue;
                const days = m.subtract(30, 'days').diff(moment(), 'days');
                qty += sn(x.qty);

                const tmp = days ? qty / (days * qtyPerDay) : 0;
                clsExt[x.exp_date] = {
                  qtyPerDay,
                  qty,
                  estQty: days * qtyPerDay,
                  days,
                  value: tmp,
                  cls: '',
                  title: `Qty/day=${nf2(qtyPerDay)}, Estimated qty = ${nf2(qtyPerDay)} pcs x ${days} days = ${ni(
                    qtyPerDay * days,
                  )} pcs, value: ${nf2(tmp)}`,
                };
                if (tmp > 2) {
                  clsExt[x.exp_date].cls = 'c-red';
                } else if (tmp > 1.3) {
                  clsExt[x.exp_date].cls = 'c-orange';
                }
              }
            }
          }

          return list.map((x) => (
            <Row key={x.exp_date} gutter={8}>
              <Col>
                <ExpDate date={x.exp_date} booking_date={x.booking_date} />
              </Col>
              {x.mix_qty ? (
                <Col
                  className={record.is_single ? 'italic' : ''}
                  flex="auto"
                  style={{ textAlign: (x.statuses || '').includes('1') ? 'right' : 'left' }}
                >
                  {clsExt[`${x.exp_date}`] ? (
                    <span className={clsExt[`${x.exp_date}`].cls || ''} title={clsExt[`${x.exp_date}`].title || ''}>
                      ({ni(x.mix_qty)})
                    </span>
                  ) : (
                    `(${ni(x.mix_qty)})`
                  )}
                </Col>
              ) : null}
            </Row>
          ));
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentOrderItemRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Stock Qty',
        dataIndex: ['parent_stock_stables_sum_total_piece_qty'],
        align: 'right',
        width: 60,
        tooltip: 'Click to view stock detail. Includes available qty and blocked qty.',
        className: 'cursor-pointer bl2',
        render: (dom, record) => {
          return !record.ym ? (
            <span title={`${ni(record.stock_mix_qty, true)} + ${ni(record.stock_mix_qty_b, true)}`}>
              {ni(sn(record.stock_mix_qty) + sn(record.stock_mix_qty_b))}
            </span>
          ) : null;
        },
        onCell: (recordOI) => {
          if (!recordOI.id || recordOI.ym) return {};
          let cls = '';
          const dr_selection = searchFormRef.current?.getFieldValue('dr_selection');
          if (dr_selection == DRSelection.DR_LAST_30_DAYS) {
            const stockQty = sn(recordOI.parent_stock_stables_sum_total_piece_qty);
            if (stockQty && stockQty <= sn(recordOI.qty_total)) {
              cls += ' bg-light-green';
            }
          }
          return {
            className: cls,
            onClick: (e) => {
              setCurrentOrderItemRow(recordOI);
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Month',
        dataIndex: ['ym'],
        width: 55,
        render: (dom, record) => {
          return record.ym ? Util.dtToDMY(record.ym + '-01', 'MMM `YY', '') : '';
        },
      },
      {
        title: 'Qty Sold (Total)',
        tooltip: 'Click to view details.',
        dataIndex: 'qty_total',
        sorter: false,
        align: 'right',
        width: 60,
        className: '',
        render: (dom, record) => {
          return ni(record.qty_total);
        },
        onCell: (record) => {
          if (record.sku)
            return {
              className: 'cursor-pointer',
              onClick: () => {
                const filterOptions: any = {
                  item_id: record.item_id,
                };
                handleQuantityOnClick(filterOptions);
              },
            };
          else return {};
        },
      },
      {
        title: 'Qty Sold (Multi)',
        dataIndex: 'qty_box',
        sorter: false,
        align: 'right',
        width: 60,
        className: '',
        render: (dom, record) => {
          return ni(record.qty_box);
        },
      },
      {
        title: 'Qty Sold (Single)',
        dataIndex: 'qty_pcs',
        sorter: false,
        align: 'right',
        width: 60,
        className: '',
        render: (dom, record) => {
          return ni(record.qty_pcs);
        },
      },
      {
        title: 'Order Ccount',
        dataIndex: 'order_cnt',
        sorter: false,
        align: 'right',
        width: 60,
        render: (dom, record) => {
          return ni(record.order_cnt);
        },
      },
      {
        title: 'Net Turnover (Total)',
        dataIndex: 'net_turnover',
        align: 'right',
        width: 90,
        render: (__, record) => {
          return !record.ym ? nf2(record.net_turnover) : null;
        },
      },
      {
        title: 'GP (Total)',
        dataIndex: 'gp',
        align: 'right',
        width: 80,
        render: (__, record) => {
          return !record.ym ? nf2(record.gp) : null;
        },
        className: 'bg-green3',
      },
      {
        title: 'Price/pcs',
        dataIndex: 'price_pcs',
        sorter: false,
        align: 'right',
        width: 70,
        className: 'cursor-pointer',
        tooltip: 'Click to edit prices',
        render: (dom, record) => {
          return record.qty_total ? nf2(record.net_turnover / record.qty_total) : null;
        },
        onCell(record, index) {
          return {
            onClick(e) {
              setCurrentOrderItemRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Discount',
        dataIndex: ['fs_special_discount'],
        width: 50,
        align: 'right',
        /* tooltip: 'Edit...',
        className: 'cursor-pointer',
        onCell(record, index) {
          return {
            onClick(e) {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        }, */
      },
      {
        title: 'BP/pcs',
        dataIndex: 'bp_pcs',
        sorter: false,
        align: 'right',
        width: 70,
        render: (dom, record) => {
          return record.qty_total ? nf2(record.bp / record.qty_total) : null;
        },
      },
      {
        title: 'GP/pcs',
        dataIndex: 'gp_pcs',
        sorter: false,
        align: 'right',
        width: 70,
        className: 'bl2',
        render: (dom, record) => {
          const gpPerPcs = record.qty_total ? record.gp / record.qty_total : 0;
          return gpPerPcs ? <span className={gpPerPcs < 0 ? 'c-red' : ''}>{nf2(gpPerPcs)}</span> : null;
        },
        onCell: (record) => {
          const gpPerPcs = record.qty_total ? record.gp / record.qty_total : 0;
          let cls = '';
          if (gpPerPcs < 0) cls = 'bg-light-red';
          else if (gpPerPcs > 1.5) cls = 'bg-green3';
          return {
            className: cls,
          };
        },
      },
      {
        title: 'Price FS_ONE',
        dataIndex: 'price1',
        sorter: false,
        align: 'right',
        tooltip: 'Click to edit prices',
        width: 120,
        render: (dom, record) => {
          const priceObj = record.ean_prices?.find((x) => x.price_type_id == 1);
          const xlsCheapestPrice = getCheapestXlsPrice(record, xlsImports);
          return !record.ym ? (
            <Row style={{ lineHeight: 1.5 }}>
              <Col span={24}>
                <SPrices
                  price={priceObj?.price}
                  vat={record.item?.vat?.value}
                  direction="horizontal"
                  style={{ fontSize: 12 }}
                />
                {xlsCheapestPrice && priceObj?.price ? (
                  <div className="italic text-sm" title="% of XLS cheapest price">
                    {nf2((100 * sn(priceObj?.price)) / xlsCheapestPrice)}%
                  </div>
                ) : null}
              </Col>
            </Row>
          ) : null;
        },
        onCell(record) {
          return {
            className: 'cursor-pointer',
            onClick(e) {
              setCurrentOrderItemRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Price FS_ONE Multi',
        dataIndex: 'price_multi',
        sorter: false,
        align: 'center',
        tooltip: 'Click to edit prices',
        width: 120,
        render: (dom, record) => {
          return !record.ym
            ? record?.siblings?.map((sib, ind) => {
                const priceObj = sib.ean_prices?.find((x) => x.price_type_id == 1);
                const xlsCheapestPrice = getCheapestXlsPrice(record, xlsImports);

                return (
                  <Row
                    key={sib.id}
                    style={{ lineHeight: 1.5, width: '100%', borderTop: ind > 0 ? '1px solid #ddd' : 'none' }}
                    wrap={false}
                  >
                    <Col
                      flex="auto"
                      style={{ textAlign: 'right', cursor: 'pointer' }}
                      onClick={() => {
                        setCurrentOrderItemRow(record);
                        handleUpdatePricesModalVisible(true);
                      }}
                    >
                      <div>
                        <SPrices
                          price={sn(priceObj?.price) / sn(sib.attr_case_qty ?? 1)}
                          vat={record.item?.vat?.value}
                          direction="horizontal"
                          style={{ fontSize: 12 }}
                        />
                      </div>
                      <div>
                        <SPrices
                          price={priceObj?.price}
                          vat={record.item?.vat?.value}
                          direction="horizontal"
                          style={{ fontSize: 12 }}
                        />
                      </div>
                      {xlsCheapestPrice && priceObj?.price ? (
                        <div className="italic text-sm" title="% of XLS cheapest price">
                          {nf2((100 * sn(sn(priceObj?.price) / sn(sib.attr_case_qty ?? 1))) / xlsCheapestPrice)}%
                        </div>
                      ) : null}
                    </Col>
                  </Row>
                );
              })
            : null;
        },
      },
      {
        title: 'Price GFC',
        dataIndex: 'price2',
        sorter: false,
        align: 'right',
        width: 110,
        render: (dom, record) => {
          const priceObj = record.ean_prices?.find((x) => x.price_type_id == 2);
          const xlsCheapestPrice = getCheapestXlsPrice(record, xlsImports);
          return !record.ym ? (
            <Row style={{ lineHeight: 1.5 }}>
              <Col span={24}>
                <SPrices
                  price={priceObj?.price}
                  vat={record.item?.vat?.value}
                  direction="horizontal"
                  style={{ fontSize: 12 }}
                  hideGross
                />
                {xlsCheapestPrice && priceObj?.price ? (
                  <div className="italic text-sm" title="% of XLS cheapest price">
                    {nf2((100 * sn(priceObj?.price)) / xlsCheapestPrice)}%
                  </div>
                ) : null}
              </Col>
            </Row>
          ) : null;
        },
      },

      ...xlsColumns,
      ...xlsColumns2,
      {
        title: 'Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        filters: false,
        align: 'center',
        ellipsis: true,
        width: 50,
        showSorterTooltip: false,
        valueEnum: ItemEANStatusOptions as any,
        render: (__, record) => {
          if (record.ym || !record.id) return null;

          let ele = null;
          if (record.status == ItemEANStatus.ACTIVE) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          } else {
            ele = <CloseOutlined style={{ color: 'gray' }} />;
          }
          return ele;
        },
      },
      {
        title: 'Shops',
        dataIndex: 'product_websites',
        hideInForm: false,
        sorter: false,
        filters: false,
        width: 50,
        showSorterTooltip: false,
        render: (__, record) => (
          <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
        ),
      },
      {
        title: 'EAN Single',
        dataIndex: 'ean',
        sorter: false,
        copyable: true,
        width: 120,
      },
    ],
    [getDictByCode, handleQuantityOnClick, renderPreviewItem, xlsColumns, xlsColumns2, xlsImports],
  );

  return (
    <PageContainer
      className={style.monthlySalesStyle}
      title={
        <>
          <span>{props.route.name}</span>&nbsp;{' '}
          <Popover
            title="Row coloring definition"
            content={
              <div style={{ width: 550 }}>
                <div>
                  <span style={{ background: '#fafafa' }}>Grey</span>:{' '}
                  <span>If item is disabled + has Zero Stock + has no entry in XLS</span>
                </div>
                <div>
                  <span style={{ background: '#f5f2d2' }}>Light yellow orange</span>:{' '}
                  <span>If item is enabled + has Zero Stock + has no entry in XLS.</span>
                  <br />
                  <span style={{ paddingLeft: 130 }}>If item is enabled + has no entry in XLS + GFC is ON.</span>
                </div>
                <div>
                  <span style={{ background: '#ffcccb' }}>Light red</span>:{' '}
                  <span>If item is disabled, but has Stock.</span>
                </div>
                <div>
                  <span style={{ background: '#aeffae' }}>Light green</span>:{' '}
                  <span>If item is disabled, but has entry in XLS.</span>
                </div>
              </div>
            }
          >
            <InfoCircleOutlined />
          </Popover>
        </>
      }
    >
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_monthly_sales_stat', defaultSearchFormValues)}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue(defaultSearchFormValues as any);
              actionRef.current?.reload();
            },
          }}
        >
          {formElements}
          <SProFormDateRange label="Date" formRef={searchFormRef} style={{ marginLeft: 16 }} disabled={loading} />
          <ProFormText label="SKU" name="sku" width="xs" placeholder="SKU" />
          {/* <ProFormCheckbox name="include_valid_stock" label="Include Valid Stock?" />
          <ProFormCheckbox name="include_past_sale" label="Include Past Sale?" /> */}
          <ProFormCheckbox
            name="include_all"
            label="Include All?"
            tooltip="Include all EANs with/without sale and stocks"
            fieldProps={{
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormCheckbox
            name="unhideAll"
            label="Unhide all?"
            fieldProps={{
              onChange(e) {
                handleUnhideAll(e.target.checked);
              },
            }}
          />
          <ProFormRadio.Group
            name="mode"
            label="Mode"
            options={[
              { value: 'a', label: 'A' },
              { value: 'b', label: 'B' },
            ]}
            initialValue={'a'}
            formItemProps={{ style: { marginLeft: 36 } }}
            fieldProps={{
              onChange(e) {
                handleViewMode(e.target.value);
              },
            }}
          />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={'Sales Stats Monthly'}
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        bordered
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_monthly_sales_stat', searchFormValues);

          if (!searchFormValues.trademark) {
            setTotalRow([]);
            return Promise.resolve([]);
          }

          setLoading(true);
          return getSalesStatsMonthlyList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademark: searchFormValues.trademark?.value,
            },
            sort,
            filter,
          )
            .then((res) => {
              setXlsImports(res.imports || []);
              setTotalRow(res.summary);
              if (currentImport) {
                setCurrentImport(res.imports?.find((x) => x.id == currentImport.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        summary={(dataParam) => {
          const totalRowLocal = totalRow ?? {};

          return (
            <ProTable.Summary fixed="top">
              <ProTable.Summary.Row style={{ fontWeight: 'bold' }}>
                {columns
                  .filter((c, ind) => {
                    if (c.hideInTable) return false;
                    const tmpKey =
                      typeof c.dataIndex === 'string' ? c.dataIndex : ((c.dataIndex || []) as any[]).join('.');
                    return colStates[tmpKey]?.show ?? true;
                  })
                  .map((c, index) => {
                    const tmpKey =
                      typeof c.dataIndex === 'string' ? c.dataIndex : ((c.dataIndex || []) as any[]).join('.');
                    let value: any = null;
                    let align: any = 'left';

                    if (tmpKey == 'ean_text_de.name') value = 'Total';
                    else if (
                      tmpKey == 'order_cnt' ||
                      tmpKey == 'qty_box' ||
                      tmpKey == 'qty_pcs' ||
                      tmpKey == 'qty_total'
                    ) {
                      value = ni(totalRowLocal[tmpKey]);
                      align = 'right';
                    } else if (tmpKey == 'gp' || tmpKey == 'net_turnover') {
                      value = nf2(totalRowLocal[tmpKey]);
                      align = 'right';
                    }

                    // CSS class
                    const cls = c.className;
                    return (
                      <ProTable.Summary.Cell key={tmpKey} index={index} align={align} className={cls}>
                        {value}
                      </ProTable.Summary.Cell>
                    );
                  })}
              </ProTable.Summary.Row>
            </ProTable.Summary>
          );
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: false,
          hideOnSinglePage: false,
          showQuickJumper: false,
          defaultPageSize: 10000,
        }}
        columns={columns}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
        columnEmptyText=""
        tableAlertRender={false}
        /* rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),

          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }} */
        onRow={(record, index) => {
          if (record.ym) {
            return {
              style: { background: sn(index) % 2 ? '#f5f5f5' : '#eee' },
            };
          }

          let cls = 'reset-tds-bg';
          if (record.sku) {
            cls += ' bg-light-blue';
            const stockQty = sn(record.parent_stock_stables_sum_total_piece_qty);
            let existsInXls = false;
            for (const x of xlsImports) {
              if (record[`xls_bp_${x.id}`] != null) {
                existsInXls = true;
                break;
              }
            }

            if (!record.status && !stockQty && !existsInXls) {
              cls += ' tr-bg-grey';
            } else if (record.status && !stockQty && !existsInXls) {
              cls += ' tr-bg-lightyellow';
            } else if (record.status && !existsInXls && (record.product_websites as string[])?.includes('2')) {
              cls += ' tr-bg-lightyellow';
            } else if (!record.status && (!!stockQty || existsInXls)) {
              if (!!stockQty) cls += ' tr-bg-green2';
              else cls += ' tr-bg-green3';
            }
          } else {
            cls += ' bg-light-blue2';
          }

          return {
            className: cls,
          };
        }}
      />

      {/* {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Button
            type="primary"
            icon={<DollarOutlined />}
            onClick={() => {
              handleVisibleUpdatePriceFormBulk(true);
            }}
          >
            Set Prices
          </Button>
        </FooterToolbar>
      )} */}

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentOrderItemRow?.id,
          item_id: currentOrderItemRow?.item_id,
          parent_id: currentOrderItemRow?.parent_id,
          is_single: currentOrderItemRow?.is_single,
          sku: currentOrderItemRow?.sku,
          ean: currentOrderItemRow?.ean,
          mag_inventory_stocks_sum_quantity: currentOrderItemRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentOrderItemRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentOrderItemRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentOrderItemRow || {}}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentOrderItemRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />

      <ImportSearchFiltersModalForm
        trademark={trademark}
        modalVisible={openImportSearchFiltersModal}
        handleModalVisible={setOpenImportSearchFiltersModal}
        values={{
          id: currentImport?.id,
          supplier_id: currentImport?.supplier_id,
          supplier: currentImport?.supplier,
          import_search_filters: currentImport?.import_search_filters || [],
        }}
        onSubmit={(value) => {
          setOpenImportSearchFiltersModal(false);
          actionRef.current?.reload();
        }}
      />

      {imagePreviewGroupBody}

      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
      />

      {/* <UpdatePriceAttributeFormBulk
        modalVisible={visibleUpdatePriceFormBulk}
        handleModalVisible={handleVisibleUpdatePriceFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        supplierXlsFileId={supplierXlsFileId}
        onSubmit={async (values) => {
          // we skip upsync...
          // if (actionRef.current) {
          // await handleBatchUpSync();
          actionRef.current?.reload();
          // }
        }}
        onCancel={() => {
          handleVisibleUpdatePriceFormBulk(false);
        }}
      /> */}
    </PageContainer>
  );
};

export default SalesStatTotalMonthly;
