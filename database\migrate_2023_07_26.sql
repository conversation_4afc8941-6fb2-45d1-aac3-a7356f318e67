ALTER TABLE `item_ean`
    ADD COLUMN `gdsn` B<PERSON><PERSON><PERSON>N DEFAULT 0 NULL COMMENT 'Exist in GDSN?' AFTER `virtual_stock_qty_gfc`;


CREATE TABLE `item_ean_gdsn`
(
    `ean`            varchar(50) NOT NULL,
    `single_ean`     varchar(50)  DEFAULT NULL,
    `case_qty`       int(11)      DEFAULT NULL,
    `weight`         double       DEFAULT NULL,
    `height`         double       DEFAULT NULL,
    `width`          double       DEFAULT NULL,
    `length`         double       DEFAULT NULL,
    `item_base`      double       DEFAULT NULL,
    `item_base_unit` varchar(31)  DEFAULT NULL,
    `name`           varchar(255) DEFAULT NULL,
    `detail`         longtext     DEFAULT NULL COMMENT 'Detail in JSON',
    PRIMARY KEY (`ean`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
