ALTER TABLE `ean_task`
    ADD COLUMN `category_code` VA<PERSON>HAR(32) NULL COMMENT 'FK: PK in sys_dict' AFTER `ean`,
    ADD CONSTRAINT `FK_ean_task_category_code` FOREIGN KEY (`category_code`) REFERENCES `sys_dict` (`code`) ON UPDATE CASCADE ON DELETE SET NULL;


INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`)
VALUES ('MAG_SEARCH_URL', 'Magento Admin URL', 'Magento Search URL',
        'https://foodstore.one/catalogsearch/result/?q={q}');