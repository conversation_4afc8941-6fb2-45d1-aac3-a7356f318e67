CREATE TABLE `ibo_pre`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `ean_id`      bigint(20) unsigned DEFAULT NULL,
    `item_id`     bigint(20) unsigned DEFAULT NULL,
    `case_qty`    int(11)             DEFAULT NULL,
    `qty`         decimal(10, 0)      DEFAULT NULL,
    `price_xls`   double              DEFAULT NULL,
    `supplier_id` bigint(20) unsigned DEFAULT NULL,
    `import_id`   bigint(20) unsigned DEFAULT NULL,
    `created_on`  datetime            DEFAULT NULL,
    `created_by`  bigint(20) unsigned DEFAULT NULL,
    `updated_on`  datetime            DEFAULT NULL,
    `updated_by`  bigint(20) unsigned DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `FK_ibo_pre_ean_id` (`ean_id`),
    KEY `FK_ibo_pre_item_id` (`item_id`),
    KEY `FK_ibo_pre_supplier_id` (`supplier_id`),
    KEY `FK_ibo_pre_import_id` (`import_id`),
    CONSTRAINT `FK_ibo_pre_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_ibo_pre_import_id` FOREIGN KEY (`import_id`) REFERENCES `import` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_ibo_pre_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_ibo_pre_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

alter table xmag_order_ext add column gclid text null after utm_content;

