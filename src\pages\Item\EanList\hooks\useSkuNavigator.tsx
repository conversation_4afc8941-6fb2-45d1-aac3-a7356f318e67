import { useCallback, useMemo, useState } from 'react';
import { type ProFormInstance } from '@ant-design/pro-form';
import Util from '@/util';
import ButtonGroup from 'antd/lib/button/button-group';
import type { ButtonProps } from 'antd';
import { Button } from 'antd';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';
import { getNextSku } from '@/services/foodstore-one/Item/ean';

const SkukNavButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  style: { width: 24, height: 24, fontSize: 14 },
};

export type SkuChangeCallbackHandlerTypeParamType = 'reload';
export type SkuChangeCallbackHandlerType = (type?: SkuChangeCallbackHandlerTypeParamType, sku?: string) => void;

const useSkuNavigator = (
  formRefCur?: ProFormInstance,
  skuCallbackHandler?: SkuChangeCallbackHandlerType,
  selectProps?: ProFormSelectProps & { renderMode?: string; parentLoading?: boolean },
) => {
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * Prev / Next navigation in SKU filter
   * @param mode
   */
  const hanldeSkuNavigation = useCallback(
    (mode: 'prev' | 'next') => {
      const curTrademark = formRefCur?.getFieldValue('trademark');
      const sku = formRefCur?.getFieldValue('sku');
      const dir: number = mode == 'prev' ? -1 : 1;

      setLoading(true);
      getNextSku(dir, sku, { trademark: curTrademark })
        .then((nextSku) => {
          console.log('nextSku', nextSku);
          if (nextSku != sku) {
            formRefCur?.setFieldValue('sku', nextSku);
            skuCallbackHandler?.('reload', nextSku);
          }
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    },
    [formRefCur, skuCallbackHandler],
  );

  const size = selectProps?.fieldProps?.size; // Form element size
  const parentLoading = selectProps?.parentLoading ?? false; // parent component's loading

  const formElements = useMemo(() => {
    return formRefCur ? (
      <ButtonGroup style={{ marginRight: 8 }}>
        <Button
          {...(size == 'small' ? SkukNavButtonProps : {})}
          onClick={() => hanldeSkuNavigation('prev')}
          icon={<ArrowLeftOutlined />}
          disabled={loading || parentLoading}
        />
        <Button
          {...(size == 'small' ? SkukNavButtonProps : {})}
          onClick={() => hanldeSkuNavigation('next')}
          icon={<ArrowRightOutlined />}
          disabled={loading || parentLoading}
        />
      </ButtonGroup>
    ) : null;
  }, [formRefCur, size, loading, parentLoading, hanldeSkuNavigation]);

  return { formElements, loading, hanldeSkuNavigation };
};

export default useSkuNavigator;
