import { <PERSON>, <PERSON><PERSON>, <PERSON>, Popover, Popconfirm } from 'antd';
import { Card } from 'antd';
import { Button, message, Space, Typography } from 'antd';
import type { CSSProperties } from 'react';
import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { <PERSON>er<PERSON>oolbar, PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType, ColumnsState } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { casePrice, nf2, ni, sn, sShortImportDbTableName, sUrlByTpl } from '@/util';
import type { EanPriceRecordType } from '@/services/foodstore-one/Item/ean';
import {
  dsGetCustomAttribute,
  getAllEanPricesList,
  updateEanAttributePartial,
  updateEanBatch,
  usProductFull,
  usProductPrice,
} from '@/services/foodstore-one/Item/ean';
import { DictC<PERSON>, DT_MAX_DATE, DT_MIN_DATE, EURO, ItemEANStatus, ScrapSystemIds } from '@/constants';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormDigit } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
import { IRouteComponentProps, useLocation, useModel } from 'umi';
import { SocialLinkItem } from './components/SocialIcons';
import StockStableQtyModal from './components/StockStableQtyModal';
import SPrices from '@/components/SPrices';
import ImportedPrices from './components/ImportedPrices';
import usePageContainerTitle from './hooks/usePageContainerTitle';
import type { TrademarkChangeCallbackHandlerTypeParamType } from './hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from './hooks/useTrademarkFormFilter';
import useModalNavigation from './hooks/useModalNavigation';
import UpdatePriceAttributeForm from './components/UpdatePriceAttributeForm';
import {
  CheckCircleOutlined,
  CloseOutlined,
  DollarOutlined,
  HighlightOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  PlusOutlined,
  SaveOutlined,
  SettingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import EditableCell from '@/components/EditableCell';
import ExportIboPreFormModal from './components/ExportIboPreFormModal';
import useBatchProcess from './hooks/useBatchProcess';
import useIbomOptions from '@/hooks/BasicData/useIbomOptions';
import WebsiteIcons from './components/WebsiteIcons';
import type { FormValueType } from './components/GfcPriceSettingFormModal';
import GfcPriceSettingFormModal from './components/GfcPriceSettingFormModal';
import UpdateAttributesFormBulk from './components/UpdateAttributesFormBulk';
import useEanPriceSpecialFilter from './hooks/useEanPriceSpecialFilter';
import Compact from 'antd/lib/space/Compact';
import SProFormDigit from '@/components/SProFormDigit';
import EanFilesComp from '@/components/EanFilesComp';
import useEanPriceSpecialFilter2 from './hooks/useEanPriceSpecialFilter2';
import { createIboPre } from '@/services/foodstore-one/IBO/ibo-pre';
import useSupplierAddOptions from '@/hooks/BasicData/useSupplierAddOptions';
import { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';

export const calcCheapestXlsPrice = (
  record: EanPriceRecordType,
  xlsImports?: API.Import[],
): [price: number, file: API.Import | null] => {
  // xls cheapest price
  let cheapestPrice = 99999999;
  let importFile: API.Import | null = null;
  xlsImports?.forEach((x2) => {
    const p = sn(record[`xls_bp_${x2.id}`] ?? record[`xls_bp2_${x2.id}`] ?? 0);
    if (p && p < cheapestPrice) {
      cheapestPrice = p;
      importFile = x2;
    }
  });
  return [cheapestPrice == 99999999 ? 0 : cheapestPrice, importFile];
};

export type SearchFormValueType = Partial<API.Ean>;

export type EanSummaryComponentProps = IRouteComponentProps & {
  eanType?: 'default' | 'base' | 've';
};

const getColClassByPrices = (
  record: API.Ean,
  priceTypes?: API.PriceType[],
  expectedType?: string | number,
  okClassName?: string,
) => {
  const vat = sn(record.item?.vat?.value || 0);
  const priceLists: any = {};
  let minValue = Number.MAX_VALUE;
  let typeWithMinValue = null;
  ScrapSystemIds.forEach((scrapName) => {
    const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });
    if (scrapPriceObj) {
      const price = casePrice(scrapPriceObj?.price, record.attr_case_qty);
      if (price) {
        priceLists[scrapName] = price;
        if (minValue > price) {
          minValue = price;
          typeWithMinValue = scrapName;
        }
      }
    }
  });
  (priceTypes ?? [])
    .filter((x) => x.id == 1)
    .forEach((pt) => {
      let price = sn(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0));
      price = casePrice(price, record.attr_case_qty) * (1 + vat / 100);
      if (price) {
        priceLists[`${pt.id}`] = price;
        if (minValue > price) {
          minValue = price;
          typeWithMinValue = `${pt.id}`;
        }
      }
    });

  if (Object.keys(priceLists).length >= 2 && typeWithMinValue == expectedType) {
    return okClassName ?? 'bg-green3';
  }
  return '';
};

const EanAllPrices: React.FC<EanSummaryComponentProps> = (eanComponentProps) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const prevWindowRef = useRef<Window | null>();
  /* useEffect(() => {
    notifySuccess(
      'I will never close automatically. This is a purposely very very long description that has many many characters and words.',
    );
    notifySuccess('Success synced.');
  }, []); */
  const { appSettings } = useModel('app-settings');
  const { priceTypes } = appSettings;
  const { getDictByCode } = useModel('app-settings');
  const location: any = useLocation();

  const [gfcPriceSettings, setGfcPriceSettings] = useState<Record<any, any>>({});

  const [loading, setLoading] = useState<boolean>(false);
  // const [totalRow, setTotalRow] = useState<EanPriceRecordType>();

  // Ean Tasks model
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [visibleUpdateAttributesFormBulk, handleVisibleUpdateAttributesFormBulk] = useState<boolean>(false);

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<EanPriceRecordType>();
  const [selectedRows, setSelectedRows] = useState<EanPriceRecordType[]>([]);

  // datasource for inline editing
  const [datasource, setDatasource] = useState<EanPriceRecordType[]>([]);

  // column states control
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>({});

  // hook for modal navigation
  // ------------------------------------------------------------- //
  const { handleNavigation, handleUpdatePricesModalVisible, updatePricesModalVisible } = useModalNavigation(
    datasource,
    {
      modals: ['price'],
      setCurrentRow,
    },
  );

  // ibom filter
  const { ibom, formElements: formElementsIbom } = useIbomOptions(undefined, searchFormRef);

  // Trademark search filter
  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
    ibom_id: ibom?.id,
  });

  useEffect(() => {
    if (location.query?.trademarkId) {
      searchFormRef.current?.resetFields();
      searchFormRef.current?.setFieldValue('trademark', { value: sn(location.query?.trademarkId) });
      actionRef.current?.reload();
    }
  }, [location.query?.trademarkId]);

  // If page is rendered by URL params, will disable form filters.
  const urlParamFilterMode = !!location.query?.trademarkId;

  // import search filters modal
  const [currentImport, setCurrentImport] = useState<API.Import>();
  // const [openImportSearchFiltersModal, setOpenImportSearchFiltersModal] = useState<boolean>(false);
  const [xlsImports, setXlsImports] = useState<API.Import[]>([]);

  const [openExportPreOrderModal, setOpenExportPreOrderModal] = useState<boolean>(false);

  // price setting up form
  const [gfcPercentage, setGfcPercentage] = useState<number>(100);
  const [openGfcPriceSettingModal, setOpenGfcPriceSettingModal] = useState<boolean>(false);

  const [isShowCols, setIsShowCols] = useState<boolean>(true);
  const [isOpenNewTab, setIsOpenNewTab] = useState<boolean>(true);

  // Batch processing UI hook.
  const { modalElement, run } = useBatchProcess();

  const handleShowOrHideCols = useCallback((shown: boolean) => {
    setColStates((prev) => ({
      ...prev,
      ['scrap_prices,0,price']: { show: shown },
      ['scrap_prices,1,price']: { show: shown },
      ['other_prices_de_ebay']: { show: shown },
      ['other_prices_de_google']: { show: shown },
      ['other_prices_de_amz']: { show: shown },
      ['other_prices_de_kfld']: { show: shown },
      ['other_prices_de_idealo']: { show: shown },
    }));
  }, []);

  const getCheapestXlsPrice = useCallback(
    (record: EanPriceRecordType) => {
      const arr = calcCheapestXlsPrice(record, xlsImports);
      return arr[0] ? arr[0] : 0;
    },
    [xlsImports],
  );

  const [columns, setColumns] = useState<ProColumns<EanPriceRecordType>[]>([]);
  const pricesColDefs = useMemo<ProColumns<EanPriceRecordType>[]>(() => {
    if (priceTypes.length) {
      const cols: ProColumns<EanPriceRecordType>[] = priceTypes
        // .filter((x) => x.id == 1)
        .map(
          (pt: any): ProColumns<EanPriceRecordType> => ({
            title: pt.name,
            dataIndex: ['ean_prices', pt?.id ?? 0],
            valueType: 'digit',
            sorter: false,
            align: 'center',
            width: 70,
            hideInSearch: true,
            className: 'cursor-pointer',
            tooltip:
              pt.id == 2 ? 'Red -> % of GFC price to Stable Price is not between SUPPLIER_PRICE_PERCENTAGE +/- 3%' : '',
            render: (dom, record) => {
              const vat = record.item?.vat?.value || 0;
              const priceSingle =
                Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0)) /
                (record?.attr_case_qty ? record?.attr_case_qty : 1);

              const supXlsFileId = sn(searchFormRef.current?.getFieldValue('supplierXlsFileId'));
              const gfcStyle: CSSProperties = {};

              // GFC styling
              if (record.idInXlsFile) {
                if (supXlsFileId && pt.id == 2) {
                  if (sn(priceSingle, 2) <= sn(record.priceInXlsFile, 2)) {
                    gfcStyle.color = 'red';
                  } else {
                    if ((sn(priceSingle, 2) < sn(sn(record.priceInXlsFile, 2) * 1.1), 2)) {
                      gfcStyle.color = 'orange';
                    }
                  }
                }
              }

              const cheapestXlsPrice = priceSingle ? getCheapestXlsPrice(record) : 0;

              return (
                <>
                  {cheapestXlsPrice > 0 && (
                    <div
                      className="text-sm c-grey italic"
                      style={{ position: 'absolute', top: 0, left: 0, zIndex: 1 }}
                      title={`${nf2((priceSingle * 100) / cheapestXlsPrice)}% | ${nf2(cheapestXlsPrice)}${EURO}`}
                    >
                      {ni((priceSingle * 100) / cheapestXlsPrice)}%
                    </div>
                  )}
                  <Row gutter={4}>
                    <Col span={24}>
                      <SPrices price={priceSingle} vat={vat} style={{ ...gfcStyle }} />
                    </Col>
                  </Row>
                </>
              );
            },
            onCell(record) {
              let cls = '';
              const unitPriceSingle = sn(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0), 2);
              const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

              if (pt.id == 1) {
                if (!(unitPriceSingle > 0)) cls += ' bg-lightgrey';
              } else {
                if (!(unitPriceGfc > 0)) cls += ' bg-lightgrey';
              }

              if (pt.id == 2) {
                // todo
                // ----------------------------------------------
                /* const cheapestXlsPrice = unitPriceSingle ? getCheapestXlsPrice(record) : 0;
                if (cheapestXlsPrice) {

                } */
                if (unitPriceGfc > 0) {
                  const priceStable = record.ean_price_stable;
                  const unitStablePrice = sn(priceStable?.cur_price);

                  if (unitStablePrice > 0 && gfcPriceSettings) {
                    if (priceStable && priceStable.cur_import) {
                      let basePercent = sn(gfcPriceSettings.default);
                      if (
                        record?.item?.trademark_id &&
                        priceStable.cur_import?.supplier_id &&
                        gfcPriceSettings[record?.item?.trademark_id]?.[priceStable.cur_import?.supplier_id]
                      ) {
                        basePercent = sn(
                          gfcPriceSettings[record?.item?.trademark_id][priceStable.cur_import?.supplier_id],
                        );
                      }

                      if (basePercent > 0) {
                        const percent = (unitPriceGfc * 100) / unitStablePrice;
                        if (percent > basePercent + 3 || percent < basePercent - 3) {
                          cls += ' bg-light-red1';
                        }
                      }
                    }
                  }
                }
                // ----------------------------------------------

                // old thing
                /* if (unitPriceGfc > unitPriceSingle) {
                  cls += ' bg-light-red1';
                } */
              }

              return {
                className: cls,
                onClick() {
                  setCurrentRow(record);
                  handleUpdatePricesModalVisible(true);
                },
              };
            },
          }),
        );

      const multiPriceCol: ProColumns<EanPriceRecordType> = {
        title: 'FS_ONE Multi ',
        dataIndex: 'ean_prices_multi',
        valueType: 'digit',
        sorter: false,
        align: 'center',
        width: 70,
        hideInSearch: true,
        tooltip: 'FS_ONE Multi < GFC --> Green',
        render: (dom, record) => {
          // Get first sibling's unit price
          const sibEan = record.siblings_multi?.[0];
          if (!sibEan) return null;

          const vat = record.item?.vat?.value || 0;
          const caseQty = sn(sibEan?.attr_case_qty ? sibEan?.attr_case_qty : 1);
          const unitPriceSibling = caseQty
            ? sn(sn(_.get(_.find(sibEan?.ean_prices, { price_type_id: 1 }), 'price', 0)) / caseQty, 2)
            : 0;

          const cheapestXlsPrice = unitPriceSibling ? getCheapestXlsPrice(record) : 0;

          const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

          return (
            <>
              {cheapestXlsPrice > 0 && (
                <div
                  className="text-sm c-grey italic"
                  style={{ position: 'absolute', top: 0, left: 0 }}
                  title={`${nf2((unitPriceSibling * 100) / cheapestXlsPrice)}% | ${nf2(cheapestXlsPrice)}${EURO}`}
                >
                  {ni((unitPriceSibling * 100) / cheapestXlsPrice)}%
                </div>
              )}
              <Row gutter={4}>
                <Col span={24}>
                  <SPrices price={unitPriceSibling} vat={vat} />
                </Col>
              </Row>
              {sn(record.siblings_multi?.length) > 1 && (
                <div
                  className="text-sm c-grey"
                  style={{ position: 'absolute', top: -5, right: 0 }}
                  title={`${record.siblings_multi?.length} multies.`}
                >
                  {record.siblings_multi?.length}
                </div>
              )}
              {unitPriceSibling > 0 && (!unitPriceGfc || unitPriceGfc > unitPriceSibling) && (
                <div className="" style={{ position: 'absolute', bottom: 0, left: 0 }}>
                  <Button
                    type="link"
                    size="small"
                    icon={<HighlightOutlined />}
                    title={`Set price to GFC on all family EANs`}
                    onClick={() => {
                      const hide = message.loading('Updating GFC price...', 0);
                      updateEanAttributePartial({
                        mode: 'price',
                        id: record.id,
                        price_type_id: 2,
                        price: unitPriceSibling,
                        // all_family: true,
                      } as any)
                        .then((res) => {
                          message.success('Updated successfully.');
                          actionRef.current?.reload();
                        })
                        .catch(Util.error)
                        .finally(hide);
                    }}
                  />
                </div>
              )}
            </>
          );
        },
        onCell: (record) => {
          const attr: React.HTMLAttributes<EanPriceRecordType> = {};

          let cls = '';

          // Get first sibling's unit price
          const sibEan = record.siblings_multi?.[0];
          if (!sibEan) return attr;

          const caseQty = sn(sibEan?.attr_case_qty ? sibEan?.attr_case_qty : 1);
          const unitPriceSibling = caseQty
            ? sn(sn(_.get(_.find(sibEan?.ean_prices, { price_type_id: 1 }), 'price', 0)) / caseQty, 2)
            : 0;
          const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

          if (!(unitPriceSibling > 0)) cls += ' bg-lightgrey';

          if (unitPriceGfc && unitPriceSibling && unitPriceGfc > unitPriceSibling) {
            cls += ' bg-green3';
          }

          attr.className = cls;
          return attr;
        },
      };

      cols.splice(1, 0, multiPriceCol);
      return cols;
    } else return [];
  }, [getCheapestXlsPrice, handleUpdatePricesModalVisible, priceTypes, gfcPriceSettings]);

  const otherPricesColumns = useMemo<ProColumns<EanPriceRecordType>[]>(() => {
    const cols: ProColumns<EanPriceRecordType>[] = [];
    cols.push({
      title: 'Idealo',
      dataIndex: ['other_prices_de_idealo'],
      valueType: 'digit',
      sorter: false,
      align: 'center',
      width: 70,
      hideInSearch: true,
      render(__, record) {
        /* const priceSingle =
          Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0)) /
          (record?.attr_case_qty ? record?.attr_case_qty : 1); */

        return (
          <Row gutter={4}>
            <Col flex="16px">
              <SocialLinkItem
                k="idealo"
                ean={record.ean || ''}
                title={record.ean_text_de?.name}
                isSameTab={!isOpenNewTab}
                prevWindowRef={prevWindowRef}
              />
            </Col>
            <Col flex={'auto'} className="text-right">
              {/* {nf2(priceSingle * 1.15)} */}
            </Col>
          </Row>
        );
      },
    });

    cols.push({
      title: 'Google',
      dataIndex: ['other_prices_de_google'],
      valueType: 'digit',
      sorter: false,
      align: 'center',
      width: 70,
      hideInSearch: true,
      // className: 'cursor-pointer',
      render(__, record) {
        const priceSingle =
          Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0)) /
          (record?.attr_case_qty ? record?.attr_case_qty : 1);

        return (
          <Row gutter={4}>
            <Col flex="16px">
              <SocialLinkItem
                k="google"
                ean={record.ean || ''}
                title={record.ean_text_de?.name}
                isSameTab={!isOpenNewTab}
                prevWindowRef={prevWindowRef}
              />
            </Col>
            <Col flex={'auto'} className="text-right">
              {nf2(0 * priceSingle * 1.15)}
            </Col>
          </Row>
        );
      },
    });

    cols.push({
      title: 'Ebay',
      dataIndex: ['other_prices_de_ebay'],
      valueType: 'digit',
      sorter: false,
      align: 'center',
      width: 70,
      hideInSearch: true,
      // className: 'cursor-pointer',
      render(__, record) {
        const priceSingle =
          Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0)) /
          (record?.attr_case_qty ? record?.attr_case_qty : 1);

        return (
          <Row gutter={4}>
            <Col flex="16px">
              <SocialLinkItem
                k="ebay"
                ean={record.ean || ''}
                title={record.ean_text_de?.name}
                isSameTab={!isOpenNewTab}
              />
            </Col>
            <Col flex={'auto'} className="text-right">
              {nf2(0 * priceSingle * 1.15)}
            </Col>
          </Row>
        );
      },
    });

    cols.push({
      title: 'AMZ',
      dataIndex: ['other_prices_de_amz'],
      valueType: 'digit',
      sorter: false,
      align: 'right',
      width: 70,
      hideInSearch: true,
      // className: 'cursor-pointer',
      render(__, record) {
        const priceSingle =
          Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0)) /
          (record?.attr_case_qty ? record?.attr_case_qty : 1);

        return (
          <Row gutter={4}>
            <Col flex="16px">
              <SocialLinkItem
                k="AMZ"
                ean={record.ean || ''}
                title={record.ean_text_de?.name}
                isSameTab={!isOpenNewTab}
              />
            </Col>
            <Col flex={'auto'} className="text-right">
              {nf2(0 * priceSingle * 1.15)}
            </Col>
          </Row>
        );
      },
    });

    cols.push({
      title: 'KFLD',
      dataIndex: ['other_prices_de_kfld'],
      valueType: 'digit',
      sorter: false,
      align: 'right',
      width: 70,
      hideInSearch: true,
      // className: 'cursor-pointer',
      render(__, record) {
        const priceSingle =
          Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0)) /
          (record?.attr_case_qty ? record?.attr_case_qty : 1);

        return (
          <Row gutter={4}>
            <Col flex="16px">
              <SocialLinkItem
                k="kfld"
                ean={record.ean || ''}
                title={record.ean_text_de?.name}
                isSameTab={!isOpenNewTab}
              />
            </Col>
            <Col flex={'auto'} className="text-right">
              {nf2(0 * priceSingle * 1.15)}
            </Col>
          </Row>
        );
      },
    });
    return cols;
  }, [isOpenNewTab]);

  const orgColumns: ProColumns<EanPriceRecordType>[] = useMemo(
    () => [
      {
        title: 'Item',
        dataIndex: ['ean_text_de', 'name'],
        align: 'left',
        width: 300,
        fixed: 'left',
        render: (dom, record) => {
          return !record.ym ? (
            <Row gutter={8} wrap={false}>
              <Col flex="auto">
                <Typography.Text ellipsis title={`${record?.ean_text_de?.name || ''}`}>
                  <a
                    href={`/item/ean-all-summary?sku=${record.item_id}_`}
                    target="_blank"
                    rel="noreferrer"
                    title="Search on website"
                  >
                    {`${record.item_id || ''}_`}
                  </a>
                  {`--- ${record?.ean_text_de?.name || ''}`}
                </Typography.Text>
              </Col>
              <Col flex="0 0 20px">
                <Typography.Text
                  title={`${record?.ean_text_de?.name || ''}`}
                  copyable={{ text: `${record?.ean_text_de?.name || ''}` }}
                >
                  {''}
                </Typography.Text>
              </Col>
              <Col flex="0 0 20px">
                <Typography.Link
                  href={sUrlByTpl(getDictByCode(DictCode.MAG_SEARCH_URL), {
                    q: record.item_id,
                  })}
                  className="text-sm"
                  title="Search EANs on new tab."
                  target="_blank"
                >
                  <LinkOutlined />
                </Typography.Link>
              </Col>
            </Row>
          ) : null;
        },
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 50,
        render: (dom, record) => <EanFilesComp files={record.files} />,
      },
      {
        title: 'VAT',
        dataIndex: ['item', 'vat', 'value'],
        sorter: false,
        width: 55,
        ellipsis: true,
        hideInSearch: true,
        className: 'p-0',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.item?.vat, prevRecord.item?.vat),
        render: (dom, record) => {
          return (
            <>
              <Row>
                <Col flex="auto">{sn(record?.item?.vat?.value) ? `${record?.item?.vat?.value}%` : ''}</Col>
                <Col flex="12px" style={{ textAlign: 'right' }}>
                  {record.status == ItemEANStatus.ACTIVE ? (
                    <CheckCircleOutlined style={{ color: 'green' }} />
                  ) : (
                    <CloseOutlined style={{ color: 'gray' }} />
                  )}
                </Col>
              </Row>
              <Row>
                <Space size={8}>
                  <WebsiteIcons
                    product_websites={record.product_websites as number[]}
                    website_ids={record.website_ids}
                  />
                </Space>
              </Row>
            </>
          );
        },
      },

      {
        title: 'EAN',
        dataIndex: 'ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
        render: (dom, record) => {
          return (
            <a
              onClick={async () => {
                let urlKey = record?.mag_url?.value;
                if (!urlKey)
                  urlKey = await dsGetCustomAttribute(record?.id || 0, {
                    force_update: 0,
                    attribute_code: 'url_key',
                  }).catch(() => {
                    message.error('Not found SKU on the shop.');
                  });

                if (urlKey) {
                  window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                }
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Latest BP',
        dataIndex: ['latest_ibo', 'price'],
        width: 60,
        align: 'center',
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.latest_ibo, prevRecord.latest_ibo),
        tooltip: 'Click to view history. Lates BP > XLS Price: Red, Lates BP < XLS Price: Green',
        render: (__, record) => {
          const vat = record.item?.vat?.value || 0;

          const priceStyle: CSSProperties = {};
          const priceXls = sn(record.priceInXlsFile, 2);
          const latestIboPrice = sn(record?.latest_ibo?.price, 2);

          let priceColTooltip: string = '';
          if (record.idInXlsFile) {
            if (priceXls > latestIboPrice) {
              priceStyle.color = 'red';
            } else {
              if (priceXls < latestIboPrice) {
                priceStyle.color = 'green';
              }
            }

            priceColTooltip = `EAN pcs price: ${nf2(priceXls)}${EURO}`;
          }

          return (
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={24} title={priceColTooltip}>
                <SPrices price={latestIboPrice} vat={vat} style={priceStyle} />
              </Col>
            </Row>
          );
        },
      },
      {
        title: 'Avg. Exp. Days',
        dataIndex: ['avg_exp_days'],
        width: 60,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return ni(record.avg_exp_days);
        },
      },
      {
        title: '',
        dataIndex: ['stock_related'],
        width: 60,
        align: 'center',
        tooltip: 'Round(Stock.Qty / Qty.Sold(30), 1) : Round(Result*30 / (AVG(EXP) - 30),1)',
        render(dom, record) {
          const allStockQty = sn(record?.stock_mix_qty) + sn(record?.stock_mix_qty_b);
          const sale30Qty = sn(record.last30_sales_qty);
          /* const allStockQty = sn(record?.stock_mix_qty) + sn(record?.stock_mix_qty_b);
          const val1 = sn(record.last30_sales_qty) ? allStockQty / sn(record.last30_sales_qty) : null;

          const a = sn(record.avg_exp_days) - 30;
          const val2 = !!val1 && !!a ? (val1 * 30) / a : null; */
          return (
            <>
              {/* <Row gutter={4} className="text-right">
                <Col span={12} title={`${val1 ? val1.toPrecision(2) : val1}`}>
                  {val1 ? Util.numberFormat(val1, false, 1, true) : null}
                </Col>
                <Col span={12} title={`${val2 ? val2.toPrecision(2) : val2}`}>
                  {val2 ? Util.numberFormat(val2, false, 1, true) : null}
                </Col>
              </Row> */}
              <Row gutter={2} className="text-right text-sm">
                <Col span={12}>{sale30Qty ? Util.numberFormat(record.stockRelated1, false, 1, true) : null}</Col>
                <Col span={12}>
                  {sale30Qty ? (
                    Util.numberFormat(record.stockRelated2, false, 1, true)
                  ) : allStockQty ? (
                    <CloseOutlined color="red" />
                  ) : null}
                </Col>
              </Row>
            </>
          );
        },
      },
      {
        title: 'Last Sale (pcs)',
        dataIndex: ['last30_sales_qty'],
        width: 140,
        align: 'center',
        hideInSearch: true,
        tooltip: (
          <table style={{ textAlign: 'center' }} className="text-sm">
            <tr>
              <td>Sold pcs last 30 /365 days</td>
              <td>AVG Net Price Last 30 days</td>
            </tr>
            <tr>
              <td>
                <b>Stock Available Qty + Stock blocked Qty - processing orders Qty</b>
              </td>
              <td>Discount %</td>
            </tr>
            <tr>
              <td colSpan={2} className="text-sm">
                Stock qty includes available and blocked ones
              </td>
            </tr>
          </table>
        ),
        render(dom, record) {
          const newStockQty = sn(record?.stock_mix_qty) + sn(record?.stock_mix_qty_b) - sn(record.processing_qty);
          const last30_sales_qty = sn(record.last30_sales_qty);
          const last30AvgPrice = last30_sales_qty ? sn(record.last30_cturover) / last30_sales_qty : 0;

          const last365_sales_qty = sn(record.last365_sales_qty);
          const last365AvgPrice = last365_sales_qty ? sn(record.last365_cturover) / last365_sales_qty : 0;

          // styles
          let clsStock = '';
          if (newStockQty && newStockQty < last30_sales_qty) {
            clsStock += ' bg-light-orange1';
          }
          const avgExpDays = sn(record.avg_exp_days);

          let clsLast30Sales = '';
          if (avgExpDays && avgExpDays < 90) {
            if ((newStockQty / last30_sales_qty) * 30 < avgExpDays - 30) {
              clsLast30Sales += ' c-orange';
            }
          }

          return (
            <div style={{ textAlign: 'right' }}>
              <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
                <Col span={14} className="text-right">
                  {!!last365_sales_qty || !!last30_sales_qty ? (
                    <>
                      <span className={clsLast30Sales}>{ni(record.last30_sales_qty, !!last365_sales_qty)}</span> /{' '}
                      <span>{ni(record.last365_sales_qty)}</span>
                    </>
                  ) : null}
                </Col>
                {last30AvgPrice || last365AvgPrice ? (
                  <Col span={10} title={`Net Turnover last 30 days: ${nf2(record.last30_cturover)}${EURO}`}>
                    <Popover
                      content={
                        <table style={{ textAlign: 'left' }} className="text-sm">
                          <tr>
                            <td style={{ width: 80 }}>AVG (30)</td>
                            <td>
                              {last30AvgPrice ? (
                                <SPrices
                                  price={last30AvgPrice}
                                  vat={record.item?.vat?.value}
                                  direction="horizontal"
                                  showZero
                                  showCurrency
                                  noTextSmallCls
                                />
                              ) : null}
                            </td>
                            <td style={{ paddingLeft: 16 }}>
                              Avg. GP: {nf2(record.gp_single_gp_avg_30, true)} / {nf2(record.gp_multi_gp_avg_30, true)}
                            </td>
                          </tr>
                          <tr>
                            <td>AVG (365)</td>
                            <td className="text-right">
                              {last365AvgPrice ? (
                                <SPrices
                                  price={last365AvgPrice}
                                  vat={record.item?.vat?.value}
                                  direction="horizontal"
                                  showZero
                                  showCurrency
                                  noTextSmallCls
                                />
                              ) : null}
                            </td>
                            <td style={{ paddingLeft: 12 }}>
                              Avg. GP: {nf2(record.gp_single_gp_avg_365, true)} /{' '}
                              {nf2(record.gp_multi_gp_avg_365, true)}
                            </td>
                          </tr>
                        </table>
                      }
                      trigger="hover"
                    >
                      &nbsp;
                      <SPrices
                        price={last30AvgPrice != 0 ? last30AvgPrice : last365AvgPrice}
                        vat={record.item?.vat?.value}
                        direction="horizontal"
                        showZero
                        showCurrency
                        noTextSmallCls
                        hideGross
                      />
                    </Popover>
                  </Col>
                ) : null}
              </Row>
              <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
                <Col
                  span={14}
                  title={`${ni(record?.stock_mix_qty, true)} + ${ni(record?.stock_mix_qty_b, true)} - ${ni(
                    record.processing_qty,
                    true,
                  )} = ${ni(newStockQty, true)}`}
                  style={{ fontWeight: 'bold', textAlign: 'left' }}
                  className={`${clsStock}`}
                >
                  {ni(newStockQty)}
                </Col>
                <Col span={10} className={record.fs_special_discount ? 'c-red' : ''}>
                  {record.fs_special_discount}
                </Col>
              </Row>
            </div>
          );
        },
      },
      ...pricesColDefs,
      /* {
        title: `AVG GPs`,
        dataIndex: ['last_avg_gps'],
        width: 130,
        align: 'center',
        className: 'bl2',
        hideInSearch: true,
        tooltip: (
          <table className="text-sm">
            <tr>
              <td>Single</td>
              <td>Multi</td>
            </tr>
            <tr>
              <td>Avg. GP / Ordered pcs for last 30 days</td>
              <td>Avg. GP / Ordered boxes for last 30 days</td>
            </tr>
            <tr>
              <td>Avg. GP / Ordered pcs for last 365 days</td>
              <td>Avg. GP / Ordered boxes for last 365 days</td>
            </tr>
          </table>
        ),
        render(__, entity) {
          return (
            <>
              <Row style={{ textAlign: 'right', minHeight: 18 }}>
                <Col span={6} style={{ fontSize: 11 }}>
                  {entity.gp_single_gp_avg_30 ? nf2(entity.gp_single_gp_avg_30) : null}
                </Col>
                <Col span={6} className="text-sm c-grey align-middle">
                  {ni(entity.gp_single_qty_ordered_sum_30)}
                </Col>
                <Col span={6} style={{ fontSize: 11 }}>
                  {entity.gp_multi_gp_avg_30 ? nf2(entity.gp_multi_gp_avg_30) : null}
                </Col>
                <Col span={6} className="text-sm c-grey align-middle">
                  {ni(entity.gp_multi_qty_ordered_sum_30)}
                </Col>
              </Row>
              <Row style={{ textAlign: 'right', minHeight: 18, borderTop: '1px solid #eee', lineHeight: '18px' }}>
                <Col span={6} style={{ fontSize: 11, color: '#999' }}>
                  {entity.gp_single_gp_avg_365 ? nf2(entity.gp_single_gp_avg_365) : null}
                </Col>
                <Col span={6} className="text-sm c-grey align-middle">
                  {ni(entity.gp_single_qty_ordered_sum_365)}
                </Col>
                <Col span={6} style={{ fontSize: 11, color: '#999' }}>
                  {entity.gp_multi_gp_avg_365 ? nf2(entity.gp_multi_gp_avg_365) : null}
                </Col>
                <Col span={6} className="text-sm c-grey align-middle">
                  {entity.gp_multi_qty_ordered_sum_365 ? ni(entity.gp_multi_qty_ordered_sum_365) : null}
                </Col>
              </Row>
            </>
          );
        },        
      }, */
      {
        title: `GGP / GOGP`,
        dataIndex: ['ggp'],
        width: 200,
        align: 'center',
        className: 'bl2',
        hideInSearch: true,
        tooltip: {
          title: (
            <table className="text-sm">
              <tr>
                <td>GGP (30)</td>
                <td>GOGP (30)</td>
                <td>Qty Single (30)</td>
                <td>Qty Multi (30)</td>
              </tr>
              <tr>
                <td>GGP (365)</td>
                <td>GOGP (365)</td>
                <td>Qty Single (365)</td>
                <td>Qty Multi (365)</td>
              </tr>
            </table>
          ),
          overlayStyle: { width: 400, maxWidth: 400 },
        },
        render(__, entity) {
          return (
            <>
              <Row style={{ textAlign: 'right', minHeight: 18, fontSize: 11 }}>
                <Col span={6} title={`${nf2(entity.gp_sum_30, true)} / ${ni(entity.order_count_30, true)}`}>
                  {nf2(entity.ggp_avg_30)}
                </Col>
                <Col span={6} title={`${nf2(entity.gogp_sum_30, true)} / ${ni(entity.gogp_order_count_30, true)}`}>
                  {nf2(entity.gogp_avg_30)}
                </Col>
                <Col span={6}>{ni(entity.gp_single_qty_ordered_sum_30)}</Col>
                <Col span={6}>{ni(entity.gp_multi_qty_ordered_sum_30)}</Col>
              </Row>
              <Row
                style={{
                  textAlign: 'right',
                  minHeight: 18,
                  borderTop: '1px solid #eee',
                  lineHeight: '18px',
                  fontSize: 11,
                }}
              >
                <Col span={6} title={`${nf2(entity.gp_sum_365, true)} / ${ni(entity.order_count_365, true)}`}>
                  {nf2(entity.ggp_avg_365)}
                </Col>
                <Col
                  span={6}
                  title={`${nf2(entity.gogp_sum_365, true)} / ${ni(entity.gogp_order_count_365, true)} --> ${
                    entity.gogp_order_ids
                  }`}
                >
                  {nf2(entity.gogp_avg_365)}
                </Col>
                <Col span={6}>{ni(entity.gp_single_qty_ordered_sum_365)}</Col>
                <Col span={6}>{ni(entity.gp_multi_qty_ordered_sum_365)}</Col>
              </Row>
            </>
          );
        },
      },
      ...(ScrapSystemIds.map((scrapName, ind) => ({
        title: `${scrapName}`,
        dataIndex: ['scrap_prices', ind, 'price'],
        width: 60,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer' + (ind == 0 ? ' bl2' : ''),
        shouldCellUpdate: (record: API.Ean, prevRecord: API.Ean) =>
          !_.isEqual(record.scrap_prices, prevRecord.scrap_prices),
        render: (__, record: API.Ean) => {
          const vat = record.item?.vat?.value || 0;
          const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });

          return (
            <a href={scrapPriceObj?.link || ''} target={!isOpenNewTab ? '_self' : '_blank'} rel="noreferrer">
              <Row gutter={4} style={{ minHeight: 24 }}>
                <Col span={12} className="text-sm italic c-lightgrey" style={{ paddingTop: 3 }}>
                  {!record?.is_single && nf2(scrapPriceObj?.price)}
                </Col>
                <Col span={12}>
                  <SPrices
                    price={casePrice(scrapPriceObj?.price, record.attr_case_qty)}
                    vat={vat}
                    hideNet
                    isGross
                    direction="horizontal"
                  />
                </Col>
              </Row>
            </a>
          );
        },
        onCell: (record: EanPriceRecordType) => {
          // const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });
          return {
            /* onClick: () => {
              if (scrapPriceObj?.link) {
                if (isOpenNewTab) window.open(scrapPriceObj?.link, '_blank');
                else window.location.href = scrapPriceObj?.link;
              }
            }, */
            className: 'cursor-pointer ' + getColClassByPrices(record, [{ id: 1 }], scrapName),
          };
        },
      })) as ProColumns<EanPriceRecordType>[]),
      ...otherPricesColumns,
      /* {
        title: 'Links',
        dataIndex: 'social_ean_search',
        width: 200,
        search: false,
        align: 'center',
        className: 'text-xs c-grey',
        render(dom, record) {
          return record.ean ? (
            <SocialLinks
              ean={record.ean}
              title={record.ean_text_de?.name}
              isSameTab={!isOpenNewTab}
              excludes={['WoS', 'SwO', 'all']}
            />
          ) : null;
        },
      }, */

      /* {
        title: 'Wishing Qty',
        dataIndex: ['wish_qty'],
        align: 'center',
        className: 'bl2',
        width: 80,
        render: (__, record, index) => {
          return (
            <EditableCell
              dataType="number2"
              isDefaultEditing
              defaultValue={record.wish_qty}
              fieldProps={{
                placeholder: '',
                width: 'xs',
                allowClear: true,
                allowEmpty: true,
                tabIndex: 100 + index,
                onKeyDown: (e: any) => {
                  if (Util.isTabPressed(e)) {
                    setCurrentRow(record);
                    setOpenExportPreOrderModal(true);
                  }
                },
              }}
              triggerUpdate={async (newValue: any, cancelEdit) => {
                if (newValue == record.wish_qty) return;
                const hide = message.loading('Updating wishing Qty...', 0);
                updateEanAttributePartial({ id: record.id, wish_qty: newValue ? newValue : null, mode: 'general' })
                  .then((res) => {
                    setDatasource((prev) => {
                      const newDs = [...prev];
                      const row = prev.find((x) => x.id == record.id);
                      if (row) {
                        row.wish_qty = res.wish_qty;
                      }
                      if (currentRow?.id == record.id) {
                        setCurrentRow((prev2) => ({ ...prev2, wish_qty: res.wish_qty }));
                      }
                      // validate selected rows
                      if (selectedRows?.length) {
                        setSelectedRows((prev3) =>
                          [...prev3].map((x) => ({ ...x, wish_qty: x.id == res.id ? res.wish_qty : x.wish_qty })),
                        );
                      }
                      return newDs;
                    });
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              }}
            >
              {record.wish_qty}
            </EditableCell>
          );
        },
      }, */
      {
        title: 'Quick Pre Order',
        dataIndex: ['pre_order_qty'],
        align: 'center',
        className: 'bl2',
        width: 80,
        render: (__, record, index) => {
          return (
            <EditableCell
              dataType="number2"
              isDefaultEditing
              defaultValue={record.ibo_pre_open_qty1}
              fieldProps={{
                placeholder: '',
                width: 'xs',
                allowClear: true,
                allowEmpty: true,
                tabIndex: 100 + index,
                /* onKeyDown: (e: any) => {
                  if (Util.isTabPressed(e)) {
                    setCurrentRow(record);
                    setOpenExportPreOrderModal(true);
                  }
                }, */
              }}
              triggerUpdate={async (newValue: any, cancelEdit, rollbackChange) => {
                if (newValue == record.ibo_pre_open_qty1) return;
                const eps = record.ean_price_stable;

                if (!eps?.cur_import) {
                  message.error('Not Price Stable exists!');
                  rollbackChange?.();
                  return;
                }

                const hide = message.loading('Creating or updating Pre Order Qty...', 0);
                createIboPre({
                  ean_id: record.id,
                  supplier_id: eps.cur_import?.supplier_id,
                  import_id: eps.cur_import?.id,
                  price_xls: eps.cur_price,
                  qty: newValue,
                })
                  .then((res) => {
                    message.success('Added successfully!');

                    setDatasource((prev) => {
                      const newDs = [...prev];
                      const row = prev.find((x) => x.id == record.id);
                      if (row) {
                        row.ibo_pre_open_qty1 = res.qty;
                      }
                      if (currentRow?.id == record.id) {
                        setCurrentRow((prev2) => ({ ...prev2, ibo_pre_open_qty1: res.qty }));
                      }
                      // validate selected rows
                      if (selectedRows?.length) {
                        setSelectedRows((prev3) =>
                          [...prev3].map((x) => ({
                            ...x,
                            ibo_pre_open_qty1: x.id == res.id ? res.qty : x.ibo_pre_open_qty1,
                          })),
                        );
                      }
                      return newDs;
                    });
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              }}
            >
              {record.wish_qty}
            </EditableCell>
          );
        },
      },
      {
        title: 'VE',
        dataIndex: ['ean_price_stable', 'case_qty'],
        width: 40,
        className: 'text-sm',
        render: (__, record) => {
          return ni(record.ean_price_stable?.case_qty);
        },
      },
      {
        title: 'Avg. Shelflife',
        dataIndex: ['avg_shelflife'],
        width: 40,
        className: 'text-sm',
        render: (__, record, index) => {
          const importedLen = sn(xlsImports.length);
          if (importedLen && xlsImports) {
            let len = 0;
            let total = 0;
            for (const x of xlsImports) {
              if (record[`xls_shelf_life_${x.id}`]) {
                total += sn(record[`xls_shelf_life_${x.id}`]);
                len++;
              }
            }

            return len ? ni(total / len) : null;
          }
          return null;
        },
      },
      {
        dataIndex: ['wish_qty_option'],
        valueType: 'option',
        width: 30,
        render: (__, record, index) => {
          return (
            <Button
              type="link"
              size="small"
              icon={
                <PlusOutlined
                  title="Export IBO Pre order..."
                  onClick={() => {
                    setCurrentRow(record);
                    setOpenExportPreOrderModal(true);
                  }}
                />
              }
            />
          );
        },
      },
      {
        title: 'IBO Pre Qty',
        dataIndex: 'id',
        width: 60,
        align: 'right',
        tooltip: 'Open Qty + Sent Qty',
        render(__, record) {
          return (
            <>
              <div>&nbsp;{ni(record.ibo_pres_sum_qty)}</div>
              {sn(record.open_quotation_qty) > 0 && <div>-({ni(record.open_quotation_qty)})</div>}
            </>
          );
        },
      },
      {
        title: 'Price Stable',
        dataIndex: ['ean_price_stable', 'cur_import', 'supplier'],
        width: 60,
        align: 'right',
        tooltip: (
          <span>
            Price Stable & Supplier
            <br /> <br />
            {`Green: < Last BP, Red: > Last BP`}
          </span>
        ),
        className: 'bl2',
        render(__, record) {
          const priceStable = record.ean_price_stable;
          return priceStable ? (
            <>
              <Space style={{ lineHeight: 1 }} direction="vertical" size={6}>
                <div>{nf2(priceStable?.cur_price)}</div>
                <div
                  className="c-grey text-sm"
                  title={`${priceStable.cur_import?.supplier_add}, ${priceStable.cur_import?.table_name}, ID: ${priceStable.cur_import?.id}`}
                >
                  &nbsp;{priceStable.cur_import?.supplier_add ? '' : priceStable.cur_import?.supplier?.name}
                </div>
              </Space>
              {!!record.import_ean_disabled_list?.length && (
                <div className="absolute" style={{ top: 3, left: 3 }}>
                  <Popover
                    trigger={['click', 'hover']}
                    style={{ zIndex: 1052 }}
                    title="Price disabled"
                    content={record.import_ean_disabled_list?.map((x) => (
                      <Row key={`${x.ean}_${x.supplier_add}`} style={{ width: 260 }}>
                        <Col flex="auto">{x.supplier_add}</Col>
                        <Col flex="0 0 80px" className="text-center">
                          {x.start_date == DT_MIN_DATE ? null : Util.dtToDMY(x.start_date)}
                        </Col>
                        <Col flex="0 0 10px" className="text-center">
                          ~
                        </Col>
                        <Col flex="0 0 80px" className="text-center">
                          {x.end_date == DT_MAX_DATE ? null : Util.dtToDMY(x.end_date)}
                        </Col>
                      </Row>
                    ))}
                  >
                    <InfoCircleOutlined style={{ color: 'red' }} />
                  </Popover>
                </div>
              )}
            </>
          ) : null;
        },
        onCell: (record) => {
          let cls = '';
          const cur_price = sn(record.ean_price_stable?.cur_price);
          const latest_bp = sn(record?.latest_ibo?.price);
          if (cur_price < latest_bp) {
            cls += ' bg-green2';
          } else if (cur_price > latest_bp) {
            cls += ' bg-red2';
          }

          return {
            className: cls,
          };
        },
      },
      {
        title: 'SUPPLIER_ADD',
        dataIndex: ['ean_price_stable', 'cur_import', 'supplier_add'],
        width: 60,
      },
    ],
    [currentRow?.id, getDictByCode, isOpenNewTab, otherPricesColumns, pricesColDefs, selectedRows?.length, xlsImports],
  );

  const xlsColumns = useMemo<ProColumns<EanPriceRecordType>[]>(() => {
    const cols: ProColumns<EanPriceRecordType>[] = [];

    xlsImports?.forEach((x, ind) => {
      if (!x.id) return;
      cols.push({
        title: (
          <>
            <div style={{ textAlign: 'left' }}>
              {/* {trademark ? (
                <Button
                  type="link"
                  size="small"
                  icon={<SearchOutlined title="Define search terms for XLS data." className="cursor-pointer" />}
                  onClick={() => {
                    setCurrentImport({
                      id: x.id,
                      supplier_id: x.supplier_id,
                      supplier: x.supplier,
                      import_search_filters: x.import_search_filters || [],
                    });
                    setOpenImportSearchFiltersModal(true);
                  }}
                  style={{ marginRight: 8 }}
                />
              ) : null} */}
              {x.supplier?.name}
            </div>
            <div style={{ textAlign: 'left', fontSize: 10 }}>
              {x.supplier_add ? x.supplier_add : sShortImportDbTableName(x.table_name, true)}
            </div>
          </>
        ),
        dataIndex: [`xls_bp_${x.id}`],
        sorter: false,
        align: 'right',
        width: 80,
        className: ind == 0 ? 'bl2' : '',
        render: (__, record) => {
          const price = record[`xls_bp_${x.id}`] ?? record[`xls_bp2_${x.id}`];
          const uvp = record[`xls_uvp_${x.id}`] ?? record[`xls_uvp2_${x.id}`];

          /* const gfcPricePercentage =
            gfcPriceSettings && record.item?.trademark_id && x.supplier_id
              ? gfcPriceSettings?.[record.item?.trademark_id]?.[x.supplier_id] ??
                getDictByCode(DictCode.SUPPLIER_PRICE_PERCENTAGE)
              : null; */

          let productNo = '';
          if (!!record.ean_suppliers?.find((s) => s.id == x.supplier_id)) {
            productNo = record.ean_suppliers?.find((s) => s.id == x.supplier_id)?.pivot?.product_no || '';
          }

          /* const gfcSingle =
            Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0)) /
            (record?.attr_case_qty ? record?.attr_case_qty : 1); */

          const disabledPrice = record.import_ean_disabled_list?.find((x2) => x2.supplier_add == x.supplier_add);

          return !record.ym ? (
            <>
              {/* {!!gfcPricePercentage && (
                <div
                  className="text-sm c-grey italic"
                  style={{ position: 'absolute', top: 0, left: 0 }}
                  title={price ? `Expected GFC price: ${nf2((sn(price) * gfcPricePercentage) / 100, true)}${EURO}` : ''}
                >
                  {ni(gfcPricePercentage)}%
                </div>
              )}
              {!!gfcPricePercentage && !!gfcSingle && (
                <div
                  className="text-xs c-grey italic"
                  style={{ position: 'absolute', top: 16, left: 0 }}
                  title={price ? `% of GFC Price: ${nf2((sn(price) * 100) / gfcSingle, true)}%` : ''}
                >
                  {nf2((sn(price) * 100) / gfcSingle, true)}%
                </div>
              )} */}
              <div style={{ lineHeight: 1.5, minHeight: 12 }}>{nf2(price)}</div>
              <div style={{ lineHeight: 1.5, minHeight: 12 }} className="italic text-sm" title="UVP">
                {nf2(uvp)}
              </div>
              {!!productNo && (
                <div className="text-xs c-grey italic" style={{ position: 'absolute', bottom: 0, left: 0 }}>
                  {/* <Typography.Text
                    copyable={{ icon: <></>, text: productNo, tooltips: `${productNo} -> click to copy.` }}
                  >
                    {productNo}
                  </Typography.Text> */}
                  <span
                    className="cursor-pointer"
                    title={`${productNo} -> click to copy.`}
                    onClick={() => {
                      navigator.clipboard.writeText(productNo as string);
                      message.success('Copied', 2);
                    }}
                  >
                    {productNo}
                  </span>
                </div>
              )}

              {!!disabledPrice && (
                <div className="absolute" style={{ top: 3, left: 3 }}>
                  <Popover
                    trigger={['click', 'hover']}
                    style={{ zIndex: 1052 }}
                    title="Price disabled"
                    content={
                      <Row key={`${disabledPrice?.ean}_${disabledPrice?.supplier_add}`} style={{ width: 170 }}>
                        <Col flex="0 0 80px" className="text-center">
                          {disabledPrice?.start_date == DT_MIN_DATE ? null : Util.dtToDMY(disabledPrice?.start_date)}
                        </Col>
                        <Col flex="0 0 10px" className="text-center">
                          ~
                        </Col>
                        <Col flex="0 0 80px" className="text-center">
                          {disabledPrice?.end_date == DT_MAX_DATE ? null : Util.dtToDMY(disabledPrice?.end_date)}
                        </Col>
                      </Row>
                    }
                  >
                    <InfoCircleOutlined style={{ color: 'red' }} />
                  </Popover>
                </div>
              )}
            </>
          ) : null;
        },
        onCell: (record) => {
          if (!!record.ym) return {};
          const price = record[`xls_bp_${x.id}`] ?? record[`xls_bp2_${x.id}`] ?? 0;

          let cheapestPrice = 99999999;
          xlsImports.forEach((x2) => {
            const p = record[`xls_bp_${x2.id}`] ?? record[`xls_bp2_${x2.id}`] ?? 0;
            if (p && p < cheapestPrice) {
              cheapestPrice = p;
            }
          });
          let cls = '';
          if (cheapestPrice == price && price > 0) {
            const bp_pcs = record.qty_total ? record.bp / record.qty_total : 0;
            if (price > bp_pcs) cls = 'bg-light-orange2';
            else if (price < bp_pcs) cls = 'bg-green3';
          }
          return {
            className: cls,
          };
        },
      } as ProColumnType<EanPriceRecordType>);
    });

    return cols;
  }, [getDictByCode, gfcPriceSettings, xlsImports]);

  useEffect(() => {
    // form initialization
    const formValues = Util.getSfValues('sf_ean_all_prices', {});
    searchFormRef.current?.setFieldsValue(formValues);
    setIsShowCols(!!formValues.is_show_cols);
  }, []);

  useEffect(() => {
    searchFormRef.current?.setFieldsValue(
      urlParamFilterMode
        ? {}
        : Util.getSfValues(
            'sf_ean_all_prices',
            {},
            { sku: location.query?.sku ?? undefined, priceNDaysChangeOpt: '>' },
          ),
    );
  }, [location.query?.sku]);

  useEffect(() => {
    handleShowOrHideCols(isShowCols);
  }, [handleShowOrHideCols, isShowCols]);

  useEffect(() => {
    setColumns([...orgColumns, ...xlsColumns]);
  }, [orgColumns, xlsColumns]);

  useEffect(() => {
    if (location.query.special_filter2) {
      searchFormRef.current?.resetFields();
      const formValues = searchFormRef.current?.getFieldsValue();
      for (const key in formValues) {
        formValues[key] = null;
      }
      searchFormRef.current?.setFieldsValue({
        ...formValues,
        special_filter2: location.query.special_filter2,
      });

      actionRef.current?.reload();
    } else {
      searchFormRef.current?.setFieldsValue({
        special_filter2: false,
      });
    }
  }, [location.query.special_filter2]);

  const {
    renderedEle,
    filterId: filter_id,
    eleInsertedToForm,
  } = useEanPriceSpecialFilter(actionRef, searchFormRef, loading, 'price');

  const { renderedEle: renderedEle2, filterId: filter_id2 } = useEanPriceSpecialFilter2(
    actionRef,
    searchFormRef,
    loading,
    'price',
  );

  const eleOptions = useMemo(
    () =>
      ({
        width: 120,
        name: 'supplier_add',
        showSearch: true,
      } as ProFormSelectProps),
    [],
  );
  const { formElements: formElementsSupplierAdd } = useSupplierAddOptions(undefined, eleOptions);

  const { pageTitle } = usePageContainerTitle(eanComponentProps.route);

  return (
    <PageContainer
      className={styles.eanListContainer}
      title={
        <>
          {pageTitle}
          {renderedEle}
          {renderedEle2}
        </>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ean_all_prices')}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          {/* <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} /> */}
          {/* <ProFormText name={'ft_name1'} label="Name" width={180} placeholder={'Search by keywords'} /> */}
          <ProFormText name={'name2'} label="Name" width={180} placeholder={'Search by keywords'} />
          {formElementsIbom}
          {formElements}
          {eleInsertedToForm}
          <ProFormCheckbox name="noTrademark" label="No Trademark?" />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 2, label: 'Active or Exist Stable Price' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          {/* <ProFormGroup size="small">
            <ProFormSelect
              name="product_websites"
              label="Websites"
              width={130}
              mode="multiple"
              placeholder={'Websites'}
              options={appSettings.storeWebsites
                ?.filter((x) => x.code != 'admin')
                ?.map((x) => ({
                  value: `${x.id}`,
                  label: x.name,
                }))}
            />
          </ProFormGroup> */}
          {/* <ProFormCheckbox
            name="is_new_tab"
            label="New Tab?"
            tooltip="Open links on new tab?"
            initialValue={true}
            fieldProps={{
              onChange(e) {
                setIsOpenNewTab(e.target.checked);
              },
              checked: isOpenNewTab,
            }}
          /> */}
          <ProFormCheckbox
            name="is_wish_qty"
            label="Wishing Qty?"
            tooltip="Search EANs with valid wishing qty"
            fieldProps={{
              onChange(e) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormCheckbox
            name="is_show_cols"
            label="Show cols?"
            tooltip="Show/hide 3rd party prices"
            fieldProps={{
              checked: isShowCols,
              onChange(e) {
                setIsShowCols(e.target.checked);
              },
            }}
          />
          <div style={{ width: '100%', height: 1 }}>&nbsp;</div>
          <ProFormText
            name={'offer_no'}
            label="Offer No"
            width={'xs'}
            placeholder={'Offer No'}
            tooltip="Show items only in System Offer"
          />
          <ProFormText
            name={'quote_no'}
            label="Quote No"
            width={'xs'}
            placeholder={'Quote No'}
            tooltip="Show items only in Magento Quote"
          />
          <ProFormSelect
            name="product_website_filter"
            label="Websites"
            width={130}
            placeholder={'Websites'}
            options={[
              { value: 'FS_ONE_YES', label: 'FS_ONE (Yes)' },
              { value: 'FS_ONE_NO', label: 'FS_ONE (No)' },
              { value: 'GFC_YES', label: 'GFC (Yes)' },
              { value: 'GFC_NO', label: 'GFC (No)' },
              { value: 'RS_YES', label: 'RS (Yes)' },
              { value: 'RS_NO', label: 'RS (No)' },
            ]}
          />
          {/* <ProFormSelect
            name="fsOneActiveOrNot"
            placeholder="Select"
            label="FS_ONE Active?"
            colon={false}
            options={[
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
          />
          <ProFormSelect
            name="gfcActiveOrNot"
            placeholder="Select"
            label="GFC Active?"
            colon={false}
            options={[
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
          /> */}
          <ProFormSelect
            name="singlePicHasOrNot"
            placeholder="Select"
            label="Has Pic?"
            colon={false}
            options={[
              { value: 'yes', label: 'Has picture' },
              { value: 'no', label: 'No picture' },
            ]}
          />

          {/* <ProFormCheckbox name="changedPriceOnly" label="Price Changed?" initialValue={false} /> */}

          <div className="d-none">
            <ProFormCheckbox name="noStablePriceOnly" label="No Current Price?" />
          </div>
          <ProFormSelect
            name="priceNDaysChangeOpt"
            label="Last changed"
            tooltip="Show items which last change is < or > N days"
            colon={false}
            width={60}
            options={[
              { value: '<', label: '<' },
              { value: '>', label: '>' },
            ]}
          />
          <ProFormDigit
            name={'priceNDaysChange'}
            width={60}
            placeholder={''}
            fieldProps={{ controls: false }}
            addonAfter="days"
          />
          <div style={{ width: '100%', height: 1 }}>&nbsp;</div>

          {/* <ProFormText name={'supplier_add'} label="SUPPLIER_ADD" width={'xs'} placeholder={'SUPPLIER_ADD'} /> */}
          {formElementsSupplierAdd}

          <ProFormCheckbox
            name={'null_supplier_add'}
            tooltip="Search EANs without SUPPLIER_ADD"
            label=" "
            colon={false}
          />
          <ProFormText name={'nsupplier_add'} label="Not SUPPLIER_ADD" width={'xs'} placeholder={'Not SUPPLIER_ADD'} />
          <div style={{ width: '100%', height: 1 }}>&nbsp;</div>
          <ProFormCheckbox name="noStock" label="No Stock?" />

          <SProFormDigit
            name={'stockRelated1'}
            label={'Value 1 <='}
            width={60}
            placeholder={''}
            colon={false}
            min={Number.MIN_SAFE_INTEGER}
            zeroShow
            tooltip="0 possible. Show NULL values"
            fieldProps={{ controls: false, precision: 1 }}
          />
          <SProFormDigit
            name={'stockRelated2'}
            label={'Value 2 >='}
            width={60}
            placeholder={''}
            colon={false}
            min={Number.MIN_SAFE_INTEGER}
            zeroShow
            fieldProps={{ controls: false, precision: 1 }}
          />

          <Compact style={{ marginLeft: 32 }}>
            <SProFormDigit
              name={'ggp_avg_30_min'}
              label={'GGP (30)'}
              width={60}
              placeholder={'Min'}
              colon={true}
              fieldProps={{ controls: false, precision: 2 }}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              addonAfter={'~'}
            />
            <SProFormDigit
              name={'ggp_avg_30_max'}
              label={''}
              width={60}
              placeholder={'Max'}
              colon={false}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              fieldProps={{ controls: false, precision: 2 }}
            />
          </Compact>

          <Compact style={{ marginLeft: 32 }}>
            <SProFormDigit
              name={'gogp_avg_30_min'}
              label={'GOGP (30)'}
              width={60}
              placeholder={'Min'}
              colon={true}
              fieldProps={{ controls: false, precision: 2 }}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              addonAfter={'~'}
            />
            <SProFormDigit
              name={'gogp_avg_30_max'}
              label={''}
              width={60}
              placeholder={'Max'}
              colon={false}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              fieldProps={{ controls: false, precision: 2 }}
            />
          </Compact>

          <Compact style={{ marginLeft: 32 }}>
            <SProFormDigit
              name={'gfc_price_percent_min'}
              label={'GFC %'}
              width={60}
              placeholder={'Min'}
              colon={true}
              fieldProps={{ controls: false, precision: 2 }}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              addonAfter={'~'}
              tooltip="GFC Price / EAN Stable Price"
            />
            <SProFormDigit
              name={'gfc_price_percent_max'}
              label={''}
              width={60}
              placeholder={'Max'}
              colon={false}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              fieldProps={{ controls: false, precision: 2 }}
            />
          </Compact>

          {/* <Compact>
            <SProFormDigit
              name={'last_avg_gp_30_min'}
              label={'AVG GP (30) Single'}
              width={60}
              placeholder={'Min'}
              colon={true}
              fieldProps={{ controls: false, precision: 2 }}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              addonAfter={'~'}
            />
            <SProFormDigit
              name={'last_avg_gp_30_max'}
              label={''}
              width={60}
              placeholder={'Max'}
              colon={false}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              fieldProps={{ controls: false, precision: 2 }}
            />
          </Compact>

          <Compact>
            <SProFormDigit
              name={'last_avg_gp_30_multi_min'}
              label={'AVG GP (30) Multi'}
              width={60}
              placeholder={'Min'}
              colon={true}
              fieldProps={{ controls: false, precision: 2 }}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              addonAfter={'~'}
            />
            <SProFormDigit
              name={'last_avg_gp_30_multi_max'}
              label={''}
              width={60}
              placeholder={'Max'}
              colon={false}
              min={Number.MIN_SAFE_INTEGER}
              zeroShow
              fieldProps={{ controls: false, precision: 2 }}
            />
          </Compact> */}

          {/* <ProFormCheckbox
            name="special_filter2"
            label="Special 2"
            tooltip="Filter by XLS existence & Price Stable <> XLS Price"
          /> */}
        </ProForm>
      </Card>

      <ProTable<EanPriceRecordType, API.PageParams>
        headerTitle={'EAN Prices List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        dataSource={datasource}
        onDataSourceChange={setDatasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_all_prices_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        /* toolBarRender={() => [
          <Popconfirm
            key="remove_wishing_qty"
            title={<h3>Are you sure you want to remove all Wishing Qtys?</h3>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const postData = {
                mode: 'removeAllWishingQty',
              };

              const hide = message.loading('Selected EANs are getting active because of no active XLS data...', 0);
              updateEanBatch(postData as any)
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  setLoading(false);
                  hide();
                });
            }}
          >
            <Button type="primary" danger ghost icon={<SettingOutlined />} size="small">
              Remove all Wishing.Qty
            </Button>
          </Popconfirm>,
        ]} */
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          console.log(' ---> Search', searchFormValues);

          if (!urlParamFilterMode) {
            Util.setSfValues('sf_ean_all_prices', searchFormValues);
            Util.setSfValues('sf_ean_all_prices_p', params);
          }

          if (!searchFormValues.special_filter2) {
            searchFormValues.special_filter2_import_id = '';
          } else {
            searchFormValues.special_filter2_import_id = location.query.import_id;
          }

          setLoading(true);
          return getAllEanPricesList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
              price_special_filter_id: filter_id,
              price_special_filter_id2: filter_id2,
              ean_type: 'default',
              sortType: 'priceList',
              // with: 'fullMode,item,vats,eanTextDe,eanPrices,latestIbo,files,wosPrice,scrapPrices,usHashSummary,last30_sales_qty,last365_sales_qty,magInventoryStocksQty,siblingsMulti,iboPres.open_qty,gfcPriceSettings,eanSuppliers,avg_exp_days,last_avg_gps,includeOpenQuotationQty,stockStablesQty,stockRelated,eanPriceStable,ggp,gogp,ibo_pre_open_qty1',
              with: 'item,vats,eanTextDe,eanPrices,latestIbo,files,wosPrice,scrapPrices,usHashSummary,last30_sales_qty,last365_sales_qty,magInventoryStocksQty,siblingsMulti,iboPres.open_qty,gfcPriceSettings,eanSuppliers,avg_exp_days,last_avg_gps,includeOpenQuotationQty,stockStablesQty,stockRelated,eanPriceStable,ggp,gogp,ibo_pre_open_qty1,importEanDisabledList',
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);

              setXlsImports(res.imports || []);

              setGfcPriceSettings(res.gfcPriceSettings);

              // setTotalRow(res.summary);
              if (currentImport) {
                setCurrentImport(res.imports?.find((x) => x.id == currentImport.id));
              }

              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Ean) => x.id == currentRow.id));
                console.log(res.data.find((x: API.Ean) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
        tableAlertRender={false}
        rowSelection={{
          fixed: 'left',
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            title={<>Are you sure you want to up sync prices of selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const fnLists = [];
              for (const x of selectedRows) {
                fnLists.push(async () => {
                  return usProductPrice(Number(x.id));
                });
                // Add multi-EANs
                if (x.siblings_multi?.length) {
                  for (const x2 of x.siblings_multi) {
                    fnLists.push(async () => {
                      return usProductPrice(Number(x2.id));
                    });
                  }
                }
              }
              await run(fnLists);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync (Price)
            </Button>
          </Popconfirm>

          <Popconfirm
            title={<>Are you sure you want to up sync EANs of selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const fnLists = [];
              for (const x of selectedRows) {
                fnLists.push(async () => {
                  return usProductFull(Number(x.id));
                });
                // Add multi-EANs
                if (x.siblings_multi?.length) {
                  for (const x2 of x.siblings_multi) {
                    fnLists.push(async () => {
                      return usProductPrice(Number(x2.id));
                    });
                  }
                }
              }
              await run(fnLists);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync
            </Button>
          </Popconfirm>

          <Button
            type="primary"
            icon={<DollarOutlined />}
            style={{ marginRight: 24 }}
            onClick={() => {
              const selectedTrademarkId = searchFormRef.current?.getFieldValue('trademark')?.value;

              let percentage = 0;
              if (selectedTrademarkId) {
                if (gfcPriceSettings && gfcPriceSettings[selectedTrademarkId]) {
                  percentage = sn(Object.values(gfcPriceSettings[selectedTrademarkId])?.[0]);
                  console.log('p1', percentage);
                } else {
                  percentage = sn(gfcPriceSettings.default);
                  console.log('p2', percentage);
                }
              }

              setOpenGfcPriceSettingModal(true);
              setGfcPercentage(percentage);
            }}
          >
            Set GFC Price
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateAttributesFormBulk(true);
            }}
          >
            Status & Websites
          </Button>

          <Popconfirm
            title={<h3>Are you sure you want to make selected EANs active?</h3>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const postData = {
                mode: 'makeActiveBatch',
                ids: selectedRows.map((x) => x.id),
              };

              const hide = message.loading('Selected EANs are getting active because of no active XLS data...', 0);
              updateEanBatch(postData as any)
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  setLoading(false);
                  hide();
                });
            }}
          >
            <Button type="primary" ghost icon={<SettingOutlined />}>
              Make Active
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                <h3>Are you sure you want to make selected EANs passive?</h3>
                <div>
                  <ul>
                    <li>{'a) Remove GFC immediately'}</li>
                    <li>{'b) Remove EX_RS immediately.'}</li>
                    <li>{'c) Remove FS_ONE --> If no stock.'}</li>
                    <li>{'d) Set Passive --> if no stock.'}</li>
                  </ul>
                </div>
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const postData = {
                mode: 'makePassiveBatch',
                ids: selectedRows.map((x) => x.id),
              };

              const hide = message.loading('Selected EANs are getting passive because of no active XLS data...', 0);
              updateEanBatch(postData as any)
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  setLoading(false);
                  hide();
                });
            }}
          >
            <Button type="primary" danger ghost icon={<SettingOutlined />}>
              Make Passive
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}
      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          parent_id: currentRow?.parent_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />

      <ExportIboPreFormModal
        initialValues={currentRow || {}}
        modalVisible={openExportPreOrderModal}
        handleModalVisible={setOpenExportPreOrderModal}
        xlsImports={xlsImports}
        onSubmit={async (values) => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
      />

      {modalElement}

      <GfcPriceSettingFormModal
        initialValues={{ percentage: gfcPercentage }}
        modalVisible={openGfcPriceSettingModal}
        handleModalVisible={setOpenGfcPriceSettingModal}
        onSubmit={(formData: FormValueType) => {
          const postData = {
            mode: 'priceGfcBatch',
            percentage: formData.percentage,
            ids: selectedRows.map((x) => x.id),
          };

          const hide = message.loading('Batch GFC price updating from active supplier XLS data...', 0);
          updateEanBatch(postData as any)
            .then((res) => {
              setOpenGfcPriceSettingModal(false);
              message.success('Updated successfully.');
              actionRef.current?.reload();
            })
            .catch(Util.error)
            .finally(() => {
              setLoading(false);
              hide();
            });
        }}
      />

      <UpdateAttributesFormBulk
        modalVisible={visibleUpdateAttributesFormBulk}
        handleModalVisible={handleVisibleUpdateAttributesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateAttributesFormBulk(false);
        }}
      />

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EanAllPrices;
