import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Divider, Image, message, Modal, Popconfirm, Space, Spin, Tooltip, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import {
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormItem,
  ProFormList,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-form';
import { ProFormUploadDragger } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import {
  deleteEanFile,
  deleteShopProductMedia,
  dsExternalProductImage,
  getEanWithAllImages,
  getShopProduct,
  updateShopProductMedia,
  updateSysImage,
  usAllImages,
  usSetSmallImage,
} from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _, { isArray } from 'lodash';
import { LS_TOKEN_NAME } from '@/constants';
import type { RcFile, UploadChangeParam, UploadFile } from 'antd/lib/upload';
import { DeleteOutlined, LinkOutlined, SaveFilled, StopFilled, DownloadOutlined } from '@ant-design/icons';
import SocialLinks from './SocialIcons';
import ModalNavigation from './ModalNavigation';
import type { HandleNavFuncType } from '../hooks/useModalNavigation';
import GdsnItemButton from './GdsnItemButton';
import useGdsnItem from '../hooks/useGdsnItem';
import useUpdateModalActions from '../hooks/useUpdateModalActions';

export const PRODUCT_IMAGE_TYPES = [
  {
    value: 'image',
    label: 'Base',
  },
  {
    value: 'small_image',
    label: 'Small Image',
  },
  {
    value: 'thumbnail',
    label: 'Thumbnail',
  },
];

export const PRODUCT_IMAGE_TYPES_TEXT = [
  {
    value: 'official_ingredients_pic',
    label: 'Zutaten',
  },
  {
    value: 'official_nutrition_pic',
    label: 'Nährwerte',
  },
];

export type FormValueType = {
  files?: API.File[];
  shopProduct?: Shop.Product;
  // formFiles?: API.File[];
} & Partial<API.Ean>;

const defaultInitialValues: FormValueType = {
  files: [],
};

export type UpdatePicturesFormProps = {
  initialValues?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  handleNavigation?: HandleNavFuncType;
  gdsn?: boolean;
};

const UpdatePicturesForm: React.FC<UpdatePicturesFormProps> = (props) => {
  const itemEanProp = props.initialValues;

  const [loadings, setLoadings] = useState<Record<string, boolean>>({});
  // Image preview
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  // Form ref
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    setLoadings({});
  }, [props.modalVisible]);

  // Fetch item EAN images via REST Api
  const fetchItemEanImages = useCallback(() => {
    if (props.initialValues?.id && props.modalVisible) {
      setLoadings((prev) => ({ ...prev, [`sysImages`]: true }));
      getEanWithAllImages(`${props.initialValues?.id}`).then((res) => {
        const parentData = res.parent;
        res.parent.files = res.parent?.files
          ?.map?.((x: API.File) => {
            const mappedFile: API.File = _.find(res.files, { id: x.id });
            return {
              ...x,
              pivot: {
                ...(x.pivot || {}),
                position: 100,
                types: '',
                ...mappedFile?.pivot,
              },
            };
          })
          ?.sort((a: API.File, b: API.File) =>
            a.pivot.position < b.pivot.position ? -1 : a.pivot.position > b.pivot.position ? 1 : 0,
          );
        formRef.current?.setFieldsValue({
          files: res.files,
          parent: parentData,
          shopProduct: {
            media_gallery_entries: res.mag_files,
          },
        });
        setLoadings((prev) => ({ ...prev, [`sysImages`]: false }));
      });
    }
  }, [props.initialValues?.id, props.modalVisible]);

  useEffect(() => {
    fetchItemEanImages();
  }, [fetchItemEanImages]);

  // Fetch shop images via REST Api
  const fetchShopImages = useCallback(() => {
    formRef.current?.setFieldsValue({ shopProduct: {} });
    if (props.initialValues?.sku && props.modalVisible) {
      setLoadings((prev) => ({ ...prev, [`shopImages`]: true }));
      getShopProduct(props.initialValues?.sku, { fields: 'media_gallery_entries' })
        .then((res) => {
          formRef.current?.setFieldsValue({ shopProduct: res });
        })
        .finally(() => setLoadings((prev) => ({ ...prev, [`shopImages`]: false })));
    }
  }, [props.initialValues?.sku, props.modalVisible]);

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || defaultInitialValues) };
      if (!newValues.files || newValues.files.length < 1) {
        newValues.files = defaultInitialValues.files;
      }
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  const handleSetSmallImage = async () => {
    const eanId = props.initialValues?.id;
    const fileId = formRef.current?.getFieldValue('small_image') || formRef.current?.getFieldValue(['files', 0, 'id']);

    if (!eanId) {
      return;
    }

    if (!fileId) {
      message.error('Image unavailable!');
      return;
    }

    const hide = message.loading('Setting the image in shop...');

    try {
      const res = await usSetSmallImage(eanId, fileId, {
        position: formRef.current?.getFieldValue('position'),
        disabled: formRef.current?.getFieldValue('disabled'),
        types: formRef.current?.getFieldValue('types'),
      });
      hide();
      message.success('Updated successfully.');
      if (res.upSyncMessage) {
        message.error('Up sync error: ' + res.upSyncMessage);
      } else {
        fetchShopImages();
      }
      return true;
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  /**
   * Update the current row data of an image.
   *
   * @param currentRow Row data
   * @returns
   */
  const handleImageUpdate = (currentRow: any): void => {
    {
      if (!props.initialValues?.id || !currentRow.id) {
        message.error('Something is wrong! Please reload the list.');
        return;
      }
      setLoadings((prev) => ({
        ...prev,
        [`sysImageUpdate${currentRow.id}`]: true,
      }));
      updateSysImage(props.initialValues?.id, currentRow)
        .then((newFiles) => {
          if (newFiles) {
            message.success('Updated successfully.');
            fetchItemEanImages();
          } else {
            message.error('Failed to update.');
          }
        })
        .catch((e) => {
          message.error(e.message);
        })
        .finally(() =>
          setLoadings((prev) => ({
            ...prev,
            [`sysImageUpdate${currentRow.id}`]: false,
          })),
        );
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await Util.getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const handleDeleteFile = async (file: API.File) => {
    const hide = message.loading(
      `${file.pivot.is_parent_file ? 'Unlinking' : 'Deleting'} a file '${file.file_name}'.`,
      0,
    );
    const res = await deleteEanFile({
      id: props.initialValues?.id,
      fileId: file.uid,
    });
    hide();
    fetchItemEanImages();
    if (res.message) {
      message.success(`${file.pivot.is_parent_file ? 'Unlinked' : 'Deleted'} successfully!`);
    } else {
      Util.error(`${file.pivot.is_parent_file ? 'Unlinking' : 'Delete'} failed, please try again!`);
    }

    return res as any;
  };

  // GDSN data
  const { gdsnItem, fetchGdsnItem, loading: loadingGdsn } = useGdsnItem(props.initialValues?.ean, props.modalVisible);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useUpdateModalActions(
    props.initialValues?.id ?? 0,
    props.initialValues?.sku ?? '',
    formRef.current,
    { hideSaveButton: true },
  );

  return (
    <ModalForm<FormValueType>
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update EAN pictures</span>
          <Typography.Paragraph
            copyable={{
              text: props.initialValues?.ean || '',
              tooltips: 'Copy EAN ' + (props.initialValues?.ean || ''),
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {' '}
            - {props.initialValues?.ean || ''}
          </Typography.Paragraph>
          <SocialLinks
            ean={props.initialValues?.ean || ''}
            title={props.initialValues?.ean_texts?.[0]?.name}
            style={{ marginLeft: 50 }}
          />
          <ModalNavigation
            modalName="picture"
            eanId={props.initialValues?.id}
            itemId={props.initialValues?.item_id}
            handleNavigation={props.handleNavigation}
            style={{ marginLeft: 50 }}
          />
          {props.gdsn && (
            <GdsnItemButton
              ean={props.initialValues?.ean}
              eanId={props.initialValues?.id}
              itemId={props.initialValues?.item_id}
              style={{ marginLeft: 50 }}
              fetchGdsnItem={fetchGdsnItem}
            />
          )}
          {actionButtons}
        </div>
      }
      width={900}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      modalProps={{
        maskClosable: true,
        className: props.initialValues?.is_single ? 'm-single' : 'm-multi',
      }}
      /* submitter={{
        searchConfig: { submitText: 'Close & Reload' },
        resetButtonProps: false,
      }} */
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {actionButtons}
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Close & Reload
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
      formRef={formRef}
      onFinish={async (value) => {
        await runActionsCallback();

        props.handleModalVisible(false);
        if (props.onSubmit) props.onSubmit(value);
      }}
      disabled={loadingGdsn}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
        {hiddenFormElements}
      </div>
      <div>
        <Space size={16} style={{ margin: '0 0 12px 0' }}>
          <label>EAN Name: </label>
          <Typography.Paragraph
            copyable={{
              text: itemEanProp?.ean_texts?.[0]?.name || '',
              tooltips: 'Copy EAN Name',
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {itemEanProp?.ean_texts?.[0]?.name ?? itemEanProp?.ean_text_de?.name ?? '-'}
          </Typography.Paragraph>
        </Space>
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormUploadDragger
          max={8}
          name="files"
          title="EAN pictures"
          description="Please select files or drag & drop"
          accept="image/*"
          fieldProps={{
            multiple: true,
            listType: 'picture-card',
            name: 'file',
            action: `${API_URL}/api/item/ean/${props.initialValues?.id}/upload-file`,
            headers: {
              Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
            },
            data: {
              mode: 'pre-files',
            },
            onPreview: handlePreview,
            onChange: (info: UploadChangeParam, updateState = true) => {
              if (info.file.status == 'done') {
                info.file.url = info.file.response.url;
                info.file.uid = info.file.response.uid;
                (info.file as any).id = info.file.response.uid;
                (info.file as any).file_name = info.file.response.file_name;
                (info.file as any).clean_file_name = info.file.response.clean_file_name;
                (info.file as any).path = info.file.response.path;
                (info.file as any).org_path = info.file.response.org_path;
                (info.file as any).pivot = info.file.response.pivot;

                const newFiles = [...info.fileList];
                formRef.current?.setFieldsValue({ files: newFiles });
              }
            },
            style: { marginBottom: 24 },
            onRemove: async (file: API.File) => {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: file.pivot.is_parent_file
                    ? 'Are you sure you want to unlink?'
                    : 'Are you sure you want to delete?',
                  onOk: async () => {
                    resolve(true);
                    const res = await handleDeleteFile(file);
                    return res.message;
                  },
                  onCancel: () => {
                    reject(true);
                  },
                });
              });
            },
          }}
        />
      </ProFormGroup>

      {gdsnItem?.detail?.images?.length && (
        <>
          <Divider />
          <ProFormGroup rowProps={{ gutter: 24 }} title="GDSN images">
            {gdsnItem?.detail?.images?.map((file) => (
              <div
                key={file}
                style={{
                  backgroundColor: 'white',
                  padding: '3px',
                  width: 88,
                  display: 'inline-block',
                  margin: '4px 4px',
                  overflow: 'hidden',
                }}
                className={`image-card`}
              >
                <Image
                  src={`${file}`}
                  preview={{
                    src: `${file}`,
                  }}
                  width={80}
                  height={80}
                />
                <div style={{ textAlign: 'center' }}>
                  <Popconfirm
                    title={<>Are you sure you want to download image?</>}
                    okText="Yes"
                    cancelText="No"
                    onConfirm={() => {
                      if (props.initialValues?.id && file) {
                        const hide = message.loading('Downloading file...', 0);
                        dsExternalProductImage(props.initialValues.id, file)
                          .then((res) => {
                            console.log(res);
                            fetchItemEanImages();
                          })
                          .catch(Util.error)
                          .finally(() => hide());
                      }
                    }}
                  >
                    <Button
                      type="link"
                      icon={<DownloadOutlined />}
                      title="Download image..."
                      className="text-xs"
                      size="small"
                    />
                  </Popconfirm>
                  <Typography.Text copyable={{ text: file, tooltips: 'Copy image URL.' }} />
                  <a
                    href={file}
                    title="Open image in new tab."
                    target="_blank"
                    rel="noreferrer"
                    style={{ marginLeft: 8 }}
                  >
                    <LinkOutlined />
                  </a>
                </div>
              </div>
            ))}
          </ProFormGroup>
        </>
      )}

      <Divider />

      <ProFormGroup
        rowProps={{ gutter: 24 }}
        title={
          <>
            <Space>
              <span>System Images</span>
              <Button
                type="link"
                size="small"
                loading={loadings.sysImages}
                onClick={() => {
                  fetchItemEanImages();
                }}
              >
                Reload
              </Button>
              <Popconfirm
                title={
                  <>
                    Attention! Magento shop images will be removed! <br />
                    Are you sure you wanna up sync all of images？
                  </>
                }
                okText="Yes"
                cancelText="No"
                onConfirm={() => {
                  if (!props.initialValues?.id) return;
                  const hide = message.loading('Up syncing all images into shop...');
                  setLoadings((prev) => ({
                    ...prev,
                    [`shopImages`]: true,
                    [`sysImages`]: true,
                  }));
                  usAllImages(props.initialValues?.id)
                    .then((res) => {
                      message.success('Successfully uploaded!');
                      formRef.current?.setFieldsValue({ shopProduct: res });
                      fetchItemEanImages();
                    })
                    .catch((e) => {
                      message.error(e.message);
                    })
                    .finally(() => {
                      hide();
                      setLoadings((prev) => ({
                        ...prev,
                        [`shopImages`]: false,
                        [`sysImages`]: false,
                      }));
                    });
                }}
              >
                <Button type="primary" color="green" size="small" disabled={loadings.sysImages}>
                  Up sync all Images
                </Button>
              </Popconfirm>
            </Space>
          </>
        }
      >
        <ProFormDependency key={'files'} name={['files', 'parent']}>
          {() => {
            return (
              <>
                <Spin spinning={loadings.sysImages ?? false}>
                  <ProFormList name={['files']} key="uid" creatorButtonProps={false} actionRender={() => []}>
                    {(f, index, action) => {
                      const currentRow: API.File = action.getCurrentRowData();
                      // If multi EAN, we exclude inherited parent images
                      if (props.initialValues?.id != props.initialValues?.parent_id) {
                        if (currentRow.pivot?.is_parent_file) return undefined;
                      }
                      return (
                        <>
                          <Space style={{ borderLeft: '4px solid #f8f8f8', paddingLeft: 10 }}>
                            <ProFormItem name="id" label="ID" style={{ width: 30 }}>
                              {currentRow.uid}
                            </ProFormItem>
                            <ProFormItem name="id" label="Image" style={{ width: 50 }}>
                              {(currentRow.url ?? currentRow.thumbUrl) && (
                                <Image src={`${currentRow.url ?? currentRow.thumbUrl}`} height={30} />
                              )}
                            </ProFormItem>
                            <ProFormSwitch
                              width="xs"
                              name={['pivot', 'disabled']}
                              label="Hide?"
                              tooltip="Hide on frontend?"
                              formItemProps={{ style: { width: 60 } }}
                            />
                            <ProFormDigit
                              name={['pivot', 'position']}
                              label="Position"
                              tooltip="Image display position."
                              formItemProps={{ style: { width: 70 } }}
                            />
                            <ProFormText
                              name={['pivot', 'label']}
                              width="sm"
                              label="Alt Text"
                              tooltip="If blank, image file name will be used."
                              initialValue={currentRow.clean_file_name}
                              formItemProps={{ style: { width: 150 } }}
                            />
                            <ProFormCheckbox.Group
                              name={['pivot', 'us_modes']}
                              label="Setting"
                              layout="vertical"
                              convertValue={(value) => {
                                if (isArray(value)) return value;
                                const newValue = value || '';
                                return newValue.split(',');
                              }}
                              options={[
                                { value: '1', label: 'Own Shop' },
                                { value: '2', label: 'Ebay' },
                              ]}
                            />
                            <ProFormCheckbox.Group
                              name={['pivot', 'types']}
                              label="Types"
                              layout="vertical"
                              convertValue={(value) => {
                                if (isArray(value)) return value;
                                const newValue = value || '';
                                return newValue.split(',');
                              }}
                              options={PRODUCT_IMAGE_TYPES}
                            />
                            <ProFormItem name="action" label="Action">
                              <Space size={0}>
                                <Button
                                  type="link"
                                  size="small"
                                  loading={loadings[`sysImageUpdate${currentRow.id}`]}
                                  onClick={() => handleImageUpdate(currentRow)}
                                >
                                  <SaveFilled />
                                </Button>
                                <Popconfirm
                                  title="Are you sure you want to delete？"
                                  okText="Yes"
                                  cancelText="No"
                                  onConfirm={async () => {
                                    if (!props.initialValues?.sku || !currentRow.id) return;
                                    setLoadings((prev) => ({
                                      ...prev,
                                      [`sysImageDelete${currentRow.id}`]: true,
                                    }));
                                    await handleDeleteFile(currentRow);
                                    setLoadings((prev) => ({
                                      ...prev,
                                      [`sysImageDelete${currentRow.id}`]: false,
                                    }));
                                  }}
                                >
                                  <Button
                                    type="text"
                                    danger
                                    size="small"
                                    loading={loadings[`sysImageDelete${currentRow.id}`]}
                                  >
                                    <DeleteOutlined />
                                  </Button>
                                </Popconfirm>
                              </Space>
                            </ProFormItem>
                          </Space>
                        </>
                      );
                    }}
                  </ProFormList>
                </Spin>
              </>
            );
          }}
        </ProFormDependency>
        {props.initialValues?.id != props.initialValues?.parent_id && (
          <ProFormDependency key={'parent-files'} name={['files', ['parent']]}>
            {(depValues) => {
              return (
                <>
                  <Spin spinning={loadings.sysImages ?? false}>
                    <ProFormList
                      name={['parent', 'files']}
                      key="uid"
                      creatorButtonProps={false}
                      actionRender={() => []}
                    >
                      {(f, index, action) => {
                        const currentRow: API.File = action.getCurrentRowData();
                        const fileIds = depValues?.files?.map?.((x: API.File) => x.id) || [];

                        const existInMap = fileIds.findIndex((x: number) => x == currentRow.id) >= 0;
                        return (
                          <>
                            <Space
                              style={{
                                borderLeft: existInMap ? '4px solid #9c5fe3' : '4px solid #af8cd7',
                                paddingLeft: 10,
                              }}
                            >
                              <ProFormItem name="id" label="ID" style={{ width: 30, opacity: existInMap ? 1 : 0.5 }}>
                                {currentRow.uid}
                              </ProFormItem>
                              <ProFormItem name="id" label="Image" style={{ width: 50, opacity: existInMap ? 1 : 0.5 }}>
                                {(currentRow.url ?? currentRow.thumbUrl) && (
                                  <Image src={`${currentRow.url ?? currentRow.thumbUrl}`} height={30} />
                                )}
                              </ProFormItem>
                              <ProFormSwitch
                                width="xs"
                                name={['pivot', 'disabled']}
                                label="Hide?"
                                tooltip="Hide on frontend?"
                                disabled={!existInMap}
                                formItemProps={{ style: { width: 60 } }}
                              />
                              <ProFormDigit
                                name={['pivot', 'position']}
                                label="Position"
                                tooltip="Image display position."
                                disabled={!existInMap}
                                formItemProps={{ style: { width: 70 } }}
                              />
                              <ProFormText
                                name={['pivot', 'label']}
                                width="sm"
                                label="Alt Text"
                                tooltip="If blank, image file name will be used."
                                initialValue={currentRow.clean_file_name}
                                disabled={!existInMap}
                                formItemProps={{ style: { width: 150 } }}
                              />
                              <ProFormCheckbox.Group
                                name={['pivot', 'types']}
                                label="Types"
                                layout="vertical"
                                convertValue={(value) => {
                                  if (isArray(value)) return value;
                                  const newValue = value || '';
                                  return newValue.split(',');
                                }}
                                disabled={!existInMap}
                                options={PRODUCT_IMAGE_TYPES}
                              />
                              <ProFormItem name="action" label="Action">
                                <Space size={0}>
                                  <Tooltip title={existInMap ? 'Save' : 'Add parent image'}>
                                    <Button
                                      type="link"
                                      size="small"
                                      loading={loadings[`sysImageUpdate${currentRow.id}`]}
                                      onClick={() => handleImageUpdate(currentRow)}
                                      icon={existInMap ? <SaveFilled /> : <LinkOutlined />}
                                    />
                                  </Tooltip>
                                  <Popconfirm
                                    title="Are you sure you want to unlink？"
                                    okText="Yes"
                                    cancelText="No"
                                    disabled={!existInMap}
                                    onConfirm={async () => {
                                      if (!props.initialValues?.sku || !currentRow.id) return;
                                      setLoadings((prev) => ({
                                        ...prev,
                                        [`sysImageDelete${currentRow.id}`]: true,
                                      }));
                                      // const tmp = action.getCurrentRowData();
                                      const hide = message.loading(`Unlinking a file '${currentRow.file_name}'.`, 0);
                                      const res = await deleteEanFile({
                                        id: props.initialValues?.id,
                                        fileId: currentRow.uid,
                                      });
                                      hide();
                                      if (res.message) {
                                        message.success('Unlinked successfully!');
                                      } else {
                                        Util.error('Unlink failed, please try again!');
                                      }
                                      setLoadings((prev) => ({
                                        ...prev,
                                        [`sysImageDelete${currentRow.id}`]: false,
                                      }));
                                      fetchItemEanImages();
                                    }}
                                  >
                                    <Tooltip title="Unlink parent image">
                                      <Button
                                        type="text"
                                        danger
                                        size="small"
                                        disabled={!existInMap}
                                        loading={loadings[`sysImageDelete${currentRow.id}`]}
                                        icon={<StopFilled />}
                                      />
                                    </Tooltip>
                                  </Popconfirm>
                                </Space>
                              </ProFormItem>
                            </Space>
                          </>
                        );
                      }}
                    </ProFormList>
                  </Spin>
                </>
              );
            }}
          </ProFormDependency>
        )}
      </ProFormGroup>

      <Divider />
      <ProFormGroup
        rowProps={{ gutter: 24 }}
        title={
          <>
            <Space>
              <span>Magento Shop Images</span>
              <Button
                type="link"
                size="small"
                loading={loadings.shopImages}
                onClick={() => {
                  fetchShopImages();
                }}
              >
                Reload
              </Button>
            </Space>
          </>
        }
      >
        <Spin spinning={loadings.shopImages ?? false}>
          <ProFormList
            name={['shopProduct', 'media_gallery_entries']}
            key="id"
            creatorButtonProps={false}
            actionRender={() => []}
          >
            {(f, index, action) => {
              const currentRow: Shop.MediaGalleryEntry = action.getCurrentRowData();
              return (
                <>
                  <Space size={24}>
                    <ProFormItem name="id" label="ID" style={{ width: 50 }}>
                      {currentRow.id}
                    </ProFormItem>
                    <ProFormItem name="id" label="Image" style={{ width: 50 }}>
                      <Image src={`${MEDIA_BASE_URL}${currentRow.file}`} height={30} />
                    </ProFormItem>
                    <ProFormSwitch
                      width="xs"
                      name="disabled"
                      label="Hide?"
                      tooltip="Hide on frontend?"
                      formItemProps={{ style: { width: 60 } }}
                    />
                    <ProFormDigit
                      name="position"
                      label="Position"
                      tooltip="Image display position."
                      formItemProps={{
                        style: { width: 70 },
                      }}
                    />
                    <ProFormText
                      width="sm"
                      name="label"
                      label="Alt text"
                      tooltip="If blank, image file name will be used."
                      formItemProps={{ style: { width: 150 } }}
                    />
                    <ProFormCheckbox.Group
                      name="types"
                      label="Types"
                      layout="vertical"
                      options={[...PRODUCT_IMAGE_TYPES, ...PRODUCT_IMAGE_TYPES_TEXT]}
                    />
                    <ProFormItem name="action" label="Action">
                      <Space size={0}>
                        <Button
                          type="link"
                          size="small"
                          loading={loadings[`mediaUpdate${currentRow.id}`]}
                          onClick={() => {
                            if (!props.initialValues?.sku || !currentRow.id) return;
                            setLoadings((prev) => ({
                              ...prev,
                              [`mediaUpdate${currentRow.id}`]: true,
                            }));
                            updateShopProductMedia(props.initialValues?.sku, currentRow.id, action.getCurrentRowData())
                              .then((res) => {
                                message.success('Updated successfully.');
                              })
                              .finally(() =>
                                setLoadings((prev) => ({
                                  ...prev,
                                  [`mediaUpdate${currentRow.id}`]: false,
                                })),
                              );
                          }}
                        >
                          <SaveFilled />
                        </Button>
                        <Popconfirm
                          title="Are you sure？"
                          okText="Yes"
                          cancelText="No"
                          onConfirm={() => {
                            if (!props.initialValues?.sku || !currentRow.id) return;
                            setLoadings((prev) => ({
                              ...prev,
                              [`mediaDelete${currentRow.id}`]: true,
                            }));
                            const tmp = action.getCurrentRowData();
                            deleteShopProductMedia(props.initialValues?.sku, currentRow.id)
                              .then(() => {
                                message.success('Deleted successfully.');
                                if ((tmp.types || []).findIndex((x: string) => x === 'image') >= 0) {
                                  action.setCurrentRowData({ ...tmp, types: [] });
                                } else {
                                  action.remove(index);
                                }
                              })
                              .finally(() =>
                                setLoadings((prev) => ({
                                  ...prev,
                                  [`mediaDelete${currentRow.id}`]: false,
                                })),
                              );
                          }}
                        >
                          <Button type="text" danger size="small" loading={loadings[`mediaDelete${currentRow.id}`]}>
                            <DeleteOutlined />
                          </Button>
                        </Popconfirm>
                      </Space>
                    </ProFormItem>
                  </Space>
                </>
              );
            }}
          </ProFormList>
        </Spin>
      </ProFormGroup>

      <Divider />
      <Space>
        <ProFormDependency key={'files'} name={['files']}>
          {(depValues) => {
            return (
              <ProFormSelect
                placeholder="Select an image"
                options={(depValues.files || []).map((x: API.File) => ({
                  label: (
                    <>
                      <Image src={x.url} height={40} /> {x.clean_file_name} ({x.uid})
                    </>
                  ),
                  value: x.uid,
                }))}
                width="md"
                name="small_image"
                label="Small Image"
              />
            );
          }}
        </ProFormDependency>
        <ProFormDigit
          width="xs"
          name="position"
          label="Position"
          tooltip="Image display position."
          formItemProps={{ style: { width: 70 } }}
        />
        <ProFormSwitch width="xs" name="disabled" label="Hide?" tooltip="Hide on frontend?" initialValue={false} />
        <ProFormCheckbox.Group name="types" label="Types" layout="vertical" options={PRODUCT_IMAGE_TYPES} />
        <Button type="primary" size="small" onClick={handleSetSmallImage}>
          Add image to Shop
        </Button>
      </Space>
      <Modal open={previewVisible} title={previewTitle} footer={null} onCancel={() => setPreviewVisible(false)}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </ModalForm>
  );
};

export default UpdatePicturesForm;
