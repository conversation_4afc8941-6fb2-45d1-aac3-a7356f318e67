import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, Row, Typography, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormItem, ProFormTextArea } from '@ant-design/pro-form';
import { updateOfferItem } from '@/services/foodstore-one/Offer/offer-item';
import Util, { sn } from '@/util';
import SProFormDigit from '@/components/SProFormDigit';

export type FormValueType = Partial<API.OfferItem>;

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOfferItem(sn(fields?.id), fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateFormProps = {
  initialValues?: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItem) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  const itemEan = props.initialValues?.item_ean ?? {};

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  return (
    <ModalForm
      title={
        <Row gutter={16}>
          <Col>Update Offer Item -&nbsp;</Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.ean || '',
                tooltips: 'Copy EAN ' + (itemEan?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.ean || ''}
            </Typography.Paragraph>
          </Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.sku || '',
                tooltips: 'Copy SKU ' + (itemEan?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.sku || ''}
            </Typography.Paragraph>
          </Col>
        </Row>
      }
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 5 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: props.initialValues?.id,
        };
        const success = await handleUpdate(data);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormItem label="Name" ignoreFormItem>
        <Typography.Text
          copyable={{
            text: itemEan?.ean_text_de?.name || '',
            tooltips: 'Copy Name ' + (itemEan?.ean_text_de?.name || ''),
          }}
          style={{ display: 'inline-block', marginBottom: 16, fontSize: 14 }}
        >
          {itemEan?.ean_text_de?.name || ''}
        </Typography.Text>
      </ProFormItem>
      <SProFormDigit width="sm" name="qty" label="Qty" />
      {/* <SProFormDigit width="sm" name="price" label="Price" fieldProps={{ precision: 3 }} /> */}
      <SProFormDigit width="sm" name="case_qty" label="Qty / Pkg" readonly />
      <ProFormTextArea width="lg" name="customer_note" label="Customer Note" />
      <SProFormDigit
        width="sm"
        name="price_special"
        label="Special Price"
        tooltip="Used to be a base price for offer. e.g. Offer Price = percentage * Special Price"
        fieldProps={{ precision: 3 }}
      />
      <SProFormDigit
        width="sm"
        name="price"
        label="Fixed Price"
        tooltip="Used to be a base price for offer. e.g. Offer Price = Fixed Price"
        fieldProps={{ precision: 3 }}
      />
    </ModalForm>
  );
};

export default UpdateForm;
