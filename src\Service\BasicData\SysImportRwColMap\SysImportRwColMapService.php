<?php

declare(strict_types=1);

namespace App\Service\BasicData\SysImportRwColMap;

use App\Models\Sys\SysImportRwColMap;

final class SysImportRwColMapService extends Base
{
    public function create(array $input): SysImportRwColMap
    {
        return SysImportRwColMap::create($input);
    }

    public function update($id, $input): SysImportRwColMap
    {
        $row = SysImportRwColMap::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): SysImportRwColMap
    {
        return $this->sysImportRwColMapRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSysImportRwColMapsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->sysImportRwColMapRepository->getSysImportRwColMapsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->sysImportRwColMapRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}
