import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/openai';

/** post POST /api/openai/completion */
export async function createTextCompletion(data: Partial<API.OpenapiCallLog>, options?: Record<string, any>) {
  return request<API.ResultObject<Partial<API.OpenapiCallLog>>>(`${urlPrefix}/completion`, {
    method: 'POST',
    data: data,
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}
