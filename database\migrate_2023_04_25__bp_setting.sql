-- ===========================================================
-- Order item index table. Holds the BP info
-- ===========================================================
-- DROP TABLE IF EXISTS `xmag_order_item_idx`;

CREATE TABLE IF NOT EXISTS `xmag_order_item_idx`
(
    `order_item_id` int(11)     NOT NULL COMMENT 'Order item ID',
    `sku`           varchar(50) NOT NULL COMMENT 'SKU',
    `case_qty`      int(11)             DEFAULT NULL COMMENT 'Case Qty',
    `qty_ordered`   int(20)             DEFAULT NULL COMMENT 'Ordered Item Qty',
    `bp`            decimal(20, 4)      DEFAULT 0.0000 COMMENT 'BP (Buying Price)',
    `bp_type`       smallint(6) NOT NULL COMMENT 'BP calculation type. 1: from stock_stable_booked, 2: from direct IBO, 3: manual setting',
    `ibo_id`        bigint(20) UNSIGNED DEFAULT NULL COMMENT 'Direct IBO ID',
    PRIMARY KEY (`order_item_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='Index table of xmag_order_item table.\r\nStore buying price per order item';


-- ===========================================================
-- 1. BP setting from stock_stable_booked ---
-- ===========================================================
INSERT INTO `xmag_order_item_idx` (order_item_id, sku, case_qty, bp, bp_type, qty_ordered)
SELECT `order_item_id`,
       oi.sku,
       b.case_qty,
       ROUND(SUM(total_piece_qty * IFNULL(ibo.`price`, 0)) / SUM(`total_piece_qty`), 2) AS avg_price,
       1,
       oi.qty_ordered
FROM xmag_order_item oi
         INNER JOIN `stock_stable_booked` AS b ON b.order_item_id = oi.item_id
         LEFT JOIN ibo ON ibo.id = b.`ibo_id`
GROUP BY `order_item_id`
ON DUPLICATE KEY UPDATE case_qty    = VALUES(case_qty),
                        sku         = VALUES(sku),
                        bp          = VALUES(bp),
                        bp_type     = VALUES(bp_type),
                        qty_ordered = VALUES(qty_ordered)
;

-- ===========================================================
-- 2. BP setting from latest IBO for old order items ---
-- ===========================================================
INSERT INTO `xmag_order_item_idx` (order_item_id, sku, case_qty, bp, ibo_id, bp_type, qty_ordered)
SELECT item_id,
       oi.sku,
       t.attr_case_qty,
       t.price,
       t.ibo_id,
       2,
       oi.qty_ordered
FROM xmag_order_item oi
         LEFT JOIN (
    SELECT item_ean.id,
           sku,
           item_ean.attr_case_qty,
           ibo.id AS ibo_id,
           ibo.price
    FROM item_ean
             LEFT JOIN (
        SELECT id,
               IF(parent_id = id, (SELECT MAX(id) FROM ibo WHERE ibo.item_id = e2.item_id),
                  (SELECT MAX(id) FROM ibo WHERE ibo.ean_id = e2.id)) AS max_ibo_id
        FROM item_ean e2
    ) t ON t.id = item_ean.id
             LEFT JOIN ibo ON ibo.id = t.max_ibo_id
) AS t ON t.sku = oi.sku
WHERE NOT EXISTS(SELECT order_item_id FROM stock_stable_booked b WHERE b.order_item_id = oi.item_id)
ON DUPLICATE KEY UPDATE case_qty    = VALUES(case_qty),
                        sku         = VALUES(sku),
                        bp          = VALUES(bp),
                        bp_type     = VALUES(bp_type),
                        qty_ordered = VALUES(qty_ordered),
                        ibo_id      = VALUES(ibo_id)
;
