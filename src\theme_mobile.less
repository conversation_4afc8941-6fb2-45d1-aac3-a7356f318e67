.mobile {
  .ant-form-item-label > label {
    height: 56px;
    font-size: 20px;
  }
  input.ant-input-lg {
    font-size: 32px;
  }

  .ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector {
    height: 56px;
  }
  .ant-select-single.ant-select-lg:not(.ant-select-customize-input):not(.ant-select-customize-input)
    .ant-select-selection-search-input {
    font-size: 26px;
  }

  .ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector::after,
  .ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-item,
  .ant-select-single.ant-select-lg:not(.ant-select-customize-input)
    .ant-select-selector
    .ant-select-selection-placeholder {
    font-size: 26px;
    line-height: 56px;
  }

  .ant-select-single.ant-select-lg:not(.ant-select-customize-input):not(.ant-select-customize-input)
    .ant-select-selection-search-input {
    height: 56px;
  }

  .ant-form label {
    font-size: 20px;
  }

  .ant-checkbox {
    font-size: 36px;
  }

  .ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item input[type='checkbox'] {
    width: 48px;
    height: 48px;
  }

  .ant-checkbox-inner {
    width: 48px;
    height: 48px;
  }

  .ant-checkbox-checked .ant-checkbox-inner::after {
    margin-top: 8px;
    margin-left: 8px;
    transform: rotate(45deg) scale(3) translate(-50%, -50%);
  }

  .ant-btn-lg {
    height: 56px;
    font-size: 20px;
  }
}
