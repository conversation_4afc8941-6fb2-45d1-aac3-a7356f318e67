update sys_dict
set `type`='Openai Text Module'
where `code` in ('CE_OPENAI_PRESET_TEXT_MODULE', 'CE_OPENAI_PRESET_TEXT_MODULE2');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_MD', 'Openai Text Module', 'Default Meta Desc', '9',
        'Preset text number for Meta Desc');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_MT', 'Openai Text Module', 'Default Meta Title', '9',
        'Preset text number for Meta Title');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_SD', 'Openai Text Module', 'Default Short Desc', '9',
        'Preset text number for Short Desc');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_LD', 'Openai Text Module', 'Default Long Desc', '9',
        'Preset text number for Long Desc');



INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_MODULE_S', 'Openai Text Module', 'Suffix Prompt 1', '9',
        'Suffix text number for Prompt 1');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_MODULE2_S', 'Openai Text Module', 'Suffix Prompt 2', '9',
        'Suffix text number for Prompt 2');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_MD_S', 'Openai Text Module', 'Suffix Meta Desc', '9',
        'Suffix text number for Meta Desc');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_MT_S', 'Openai Text Module', 'Suffix Meta Title', '9',
        'Suffix text number for Meta Title');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_SD_S', 'Openai Text Module', 'Suffix Short Desc', '9',
        'Suffix text number for Short Desc');

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('CE_OPENAI_PRESET_TEXT_LD_S', 'Openai Text Module', 'Suffix Long Desc', '9',
        'Suffix text number for Long Desc');

