import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import React from 'react';
import { Col, Modal, Row } from 'antd';
import IboList from './IboList';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import { getEanList } from '@/services/foodstore-one/Item/ean';

export type FormValueType = Partial<API.OrderItem>;

export type IboDetailModalProps = {
  eanId: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const IboDetailModal: React.FC<IboDetailModalProps> = (props) => {
  const { eanId, modalVisible, handleModalVisible } = props;

  const [itemEan, setItemEan] = useState<API.Ean>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (eanId) {
      setLoading(true);
      getEanList({ id: eanId, with: 'item,latestIbo,vats,prices,texts,iboTotalQty,parent' })
        .then((res) => setItemEan(res.data?.[0]))
        .finally(() => setLoading(false));
    }
  }, [eanId]);

  return (
    <Modal
      title={`BP information ${
        itemEan?.ean ? `- ${itemEan?.ean} | ${itemEan?.sku} | ${itemEan?.ean_texts?.[0]?.name ?? ''}` : ''
      }`}
      width={1300}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      footer={false}
    >
      <Row gutter={36}>
        <Col span={12}>
          <IboList eanId={eanId} />
        </Col>
        <Col span={12}>
          {itemEan?.ean && (
            <ImportedPrices
              loading={loading}
              itemEan={{
                ean: itemEan?.ean,
                parent: itemEan?.parent,
                item: {
                  vat: itemEan?.item?.vat,
                },
                ean_prices: itemEan?.ean_prices,
                is_single: itemEan?.is_single,
                latest_ibo: itemEan?.latest_ibo,
                attr_case_qty: itemEan?.attr_case_qty,
              }}
            />
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default IboDetailModal;
