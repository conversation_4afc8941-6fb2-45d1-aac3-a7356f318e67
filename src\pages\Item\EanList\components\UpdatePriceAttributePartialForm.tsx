import React, { useEffect, useMemo } from 'react';
import { Col, Row, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormGroup, ProFormText } from '@ant-design/pro-form';
import { <PERSON><PERSON><PERSON><PERSON>ield, ProFormList, ProFormSelect } from '@ant-design/pro-form';
import Util, { nf2, sn } from '@/util';
import _ from 'lodash';
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit from '@/components/SProFormDigit';
import { InfoCircleOutlined } from '@ant-design/icons';

export type UpdatePriceAttributePartialFormProps = {
  index: number;
  initialValues: Partial<API.Ean>;
  vatValue: number;
  formRef: React.MutableRefObject<ProFormInstance | undefined>;
  priceTypes: API.PriceType[];
  latestIboPrices: string[] | number[];
};

const UpdatePriceAttributePartialForm: React.FC<UpdatePriceAttributePartialFormProps> = (props) => {
  const { index, initialValues, vatValue, priceTypes, latestIboPrices, formRef } = props;

  const caseQty = sn(initialValues?.attr_case_qty || 1);

  const parentPath = useMemo(() => ['siblings', index], [index]);

  useEffect(() => {
    if (initialValues.ean_prices) {
      const newEanPrices = [...initialValues.ean_prices];
      for (const p of newEanPrices) {
        const prices = Util.fPrices(p.price, vatValue);
        (p as any).price_gross = prices[1];
      }
      formRef.current?.setFieldValue([...parentPath, 'ean_prices'], newEanPrices);
    } else {
      formRef.current?.setFieldValue([...parentPath, 'ean_prices'], []);
    }
  }, [formRef, initialValues.ean_prices, parentPath, vatValue]);

  return (
    <ProFormGroup
      title={
        <Space size={16} style={{ position: 'relative' }}>
          <div>{`${initialValues.is_single ? 'Single' : 'Multi'} EAN Price:`}</div>
          <div style={{ fontWeight: 'normal' }}>{initialValues?.sku}</div>
          <div style={{ fontWeight: 'normal' }}>{initialValues?.ean}</div>
          <div style={{ fontWeight: 'normal' }}>Case Qty: {caseQty}</div>
          {latestIboPrices ? (
            <div
              style={{ fontWeight: 'normal', position: 'absolute', bottom: -20, left: 150 }}
              className="dark-blue text-sm"
            >
              {`${nf2(sn(latestIboPrices[0]) * caseQty)} / ${nf2(sn(latestIboPrices[1]) * caseQty)}`}&nbsp;
              <InfoCircleOutlined title="Latest IBO Price" />
            </div>
          ) : null}
        </Space>
      }
    >
      <div className="d-none">
        <ProFormText name={[...parentPath, 'id']} />
      </div>
      <ProFormList
        key={'price_type_id'}
        name={[...parentPath, 'ean_prices']}
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: 'Add a price',
        }}
        creatorRecord={(ind: number) => {
          const list = formRef.current?.getFieldValue([...parentPath, 'ean_prices']);
          const priceType = priceTypes.find((x) => !_.find(list, { price_type_id: x.id }));
          return {
            price_type_id: priceType?.id ?? new Date().getTime(),
          };
        }}
        deleteIconProps={{ tooltipText: 'Remove' }}
        copyIconProps={{ tooltipText: 'Copy row' }}
        max={priceTypes.length}
      >
        <Row gutter={6} key={'price_type_id'}>
          <Col>
            <ProFormSelect
              key="price_type_id"
              width={100}
              options={priceTypes?.map((x) => ({
                label: `${x.name}`,
                value: x.id,
              }))}
              name="price_type_id"
              label="Price name"
              rules={[
                {
                  required: true,
                  message: '',
                },
              ]}
            />
          </Col>
          <Col>
            <ProFormDependency key={'price_dep'} name={['price', 'price_gross', 'price_type_id']}>
              {(depValues) => {
                const singleEanPrices = formRef.current?.getFieldValue('ean_prices') || [];
                const singlePrice = sn(
                  _.get(_.find(singleEanPrices, { price_type_id: depValues.price_type_id }), 'price', 0),
                );
                const singleGross = Util.fPrices(singlePrice, sn(vatValue), true)?.[1];

                return (
                  <ProFormField label=" ">
                    <div style={{ width: 140 }} className="dark-blue">
                      <span>{` ${nf2(depValues.price / caseQty, true)} / ${nf2(
                        sn(depValues.price_gross) / caseQty,
                        true,
                      )}`}</span>
                      {sn(latestIboPrices[0]) && caseQty && (
                        <span className="text-sm" style={{ paddingLeft: 16 }}>
                          {Util.numberFormat((depValues.price * 100) / caseQty / sn(latestIboPrices[0]), true, 1, true)}{' '}
                          %
                        </span>
                      )}

                      <span className="text-sm" style={{ position: 'absolute', left: 0, bottom: -13 }}>
                        {' '}
                        Single: {nf2(singleGross)}
                      </span>
                    </div>
                  </ProFormField>
                );
              }}
            </ProFormDependency>
          </Col>
          <Col>
            <ProFormDependency key={'price_dep2'} name={['price', 'price_type_id']}>
              {(depValues) => {
                return (
                  <SProFormDigit
                    name="price"
                    label={'Price (€)'}
                    placeholder="Price"
                    width={105}
                    rules={[
                      {
                        required: true,
                        message: '',
                      },
                    ]}
                    fieldProps={{
                      precision: 7,
                      onChange: (value) => {
                        const prices = Util.fPrices(value, vatValue, true, true);

                        const rows = [...(formRef.current?.getFieldValue([...parentPath, 'ean_prices']) || [])];
                        for (const row of rows) {
                          if (row.price_type_id == depValues.price_type_id) {
                            row.price_gross = prices[1];
                          }
                        }
                        formRef.current?.setFieldValue([...parentPath, 'ean_prices'], rows);
                      },
                    }}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col>
            <ProFormDependency key={'price_gross_dep'} name={['price', 'price_type_id']}>
              {(depValues) => {
                return (
                  <SProFormDigit
                    name="price_gross"
                    label={'Gross Price(€)'}
                    placeholder="Gross Price"
                    initialValue={Util.fPrices(depValues.price, vatValue)[1]}
                    width={105}
                    fieldProps={{
                      precision: 7,
                      onChange: (value) => {
                        const prices = Util.fPrices(value, vatValue, true, false);

                        const rows = [...(formRef.current?.getFieldValue([...parentPath, 'ean_prices']) || [])];
                        for (const row of rows) {
                          if (row.price_type_id == depValues.price_type_id) {
                            row.price = prices[0];
                          }
                        }
                        formRef.current?.setFieldValue([...parentPath, 'ean_prices'], rows);
                      },
                    }}
                  />
                );
              }}
            </ProFormDependency>
          </Col>

          <Col>
            <ProFormDependency key={'diff_net_price_deps'} name={['price', 'price_type_id']}>
              {(depValues) => {
                const lastIboPrice = sn(latestIboPrices?.[0]) * sn(initialValues?.attr_case_qty);
                return (
                  <ProFormField label={<InfoCircleOutlined title="Net Price - Last IBO" />}>
                    <div style={{ width: 40 }} className="dark-blue">
                      {nf2(depValues.price - lastIboPrice)}
                    </div>
                  </ProFormField>
                );
              }}
            </ProFormDependency>
          </Col>

          <Col style={{ opacity: 0.7 }}>
            <SProFormDigit
              name="min_qty"
              label="Qty"
              tooltip="Minimum order quantity."
              placeholder="Min. Qty"
              initialValue={1}
              width={60}
              className="c-grey"
              rules={[
                {
                  required: true,
                  message: '',
                },
              ]}
              fieldProps={{ precision: 0 }}
            />
          </Col>
          <Col style={{ opacity: 0.7 }}>
            <SDatePicker name="start_date" label="Start date" placeholder="Start date" width={110} />
          </Col>
          <Col style={{ opacity: 0.7 }}>
            <SDatePicker name="end_date" label="End date" placeholder="End date" width={110} />
          </Col>
        </Row>
      </ProFormList>
    </ProFormGroup>
  );
};

export default UpdatePriceAttributePartialForm;
