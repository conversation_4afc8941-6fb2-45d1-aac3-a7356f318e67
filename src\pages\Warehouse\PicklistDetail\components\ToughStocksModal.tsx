import ExpDate from '@/pages/Report/Order/components/ExpDate';
import { stockStableBulkMove } from '@/services/foodstore-one/Stock/stock-stable';
import Util, { ni } from '@/util';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal, Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';

export type ToughStocksModalProps = {
  stocks?: API.StockStable[];
  tmpFile?: API.Downloadable | null; // downloadable file which had been generated on server.
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  successCb?: (res?: any) => void;
};

export type SearchFormValueType = Partial<API.OrderShipmentComment>;

const ToughStocksModal: React.FC<ToughStocksModalProps> = ({
  stocks,
  tmpFile,
  modalVisible,
  handleModalVisible,
  successCb,
}) => {
  const columns: ProColumns<API.StockStable>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      align: 'left',
      copyable: true,
      width: 90,
    },
    {
      title: 'Name',
      dataIndex: 'ean_text_de',
      align: 'left',
      ellipsis: true,
      width: 150,
    },
    {
      title: 'Current WL',
      dataIndex: ['warehouse_location', 'name'],
      align: 'center',
      width: 70,
    },
    {
      title: 'New WL',
      dataIndex: ['warehouse_location', 'name'],
      align: 'center',
      width: 70,
      tooltip: 'Stocks will be moved to this WL by Move action',
      render(dom, entity) {
        const wlName = entity.warehouse_location?.name;
        if (wlName) {
          return `B000${wlName.charAt(1)}`;
        } else {
          return null;
        }
      },
    },
    {
      title: 'Priority',
      dataIndex: ['warehouse_location', 'priority'],
      align: 'right',
      width: 70,
      render(dom, entity) {
        return ni(entity.warehouse_location?.priority);
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      align: 'center',
      width: 70,
      render(dom, entity) {
        return <ExpDate date={entity.exp_date} />;
      },
    },
    {
      title: 'Current Qty',
      dataIndex: ['qty_old'],
      align: 'right',
      width: 70,
      className: 'c-grey',
      render(dom, entity) {
        const isSingle = entity.case_qty == 1;
        return `${ni(isSingle ? entity.piece_qty_old : entity.box_qty_old)} ${isSingle ? 'pcs' : 'boxes'}`;
      },
    },
    {
      title: 'Qty',
      dataIndex: ['qty'],
      align: 'right',
      width: 70,
      tooltip: 'Movable or Adjustable Qty',
      render(dom, entity) {
        const isSingle = entity.case_qty == 1;
        return `${ni(isSingle ? entity.piece_qty : entity.box_qty)} ${isSingle ? 'pcs' : 'boxes'}`;
      },
    },
  ];

  return (
    <Modal
      title={
        <Space size={32}>
          <span>Move Stocks to Easier Warehouse Location</span>
          <InfoCircleOutlined title="Red background row --> Insufficient stock" />
        </Space>
      }
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={830}
      okText="Move stocks to B000(?)"
      onOk={() => {
        //
        console.log(stocks);
        if (!stocks?.length) return;
        const hide = message.loading('Moving...', 0);
        stockStableBulkMove(stocks || [], tmpFile)
          .then((res) => {
            message.success('Moved successfully.');
            handleModalVisible(false);
            successCb?.(res);
          })
          .catch(Util.error)
          .finally(hide);
      }}
    >
      <ProTable<API.StockStable, API.PageParams>
        rowKey="id"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        size="small"
        scroll={{ x: 600 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        dataSource={stocks}
        columns={columns}
        pagination={{ defaultPageSize: 200, hideOnSinglePage: true }}
        rowClassName={(record) => {
          let cls = record.case_qty == 1 ? 'row-single' : 'row-multi';
          if (record.is_no_stock) cls += ' bg-light-red2 reset-tds-bg';
          return cls;
        }}
        rowSelection={false}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default ToughStocksModal;
