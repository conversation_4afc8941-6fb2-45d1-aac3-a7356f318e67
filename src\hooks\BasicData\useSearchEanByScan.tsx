import { getEanDetail } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import { ProFormItem } from '@ant-design/pro-form';
import { InputProps, message, Typography } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useMemo, useRef, useState } from 'react';

/**
 * Searching EAN.
 */
export default (
  eleOptions?: InputProps & {
    with?: string;
    skipEanInfo?: boolean;
    cbSearched?: (ean: API.Ean) => void;
    cbNotFound?: (eanFilled?: string) => void;
  },
) => {
  const [loading, setLoading] = useState<boolean>(false);

  const [itemEan, setItemEan] = useState<API.Ean | null>(null);

  const fieldRef = useRef<HTMLInputElement>();

  // ---------------------------------------------------------------------------------
  // Search EAN
  // ---------------------------------------------------------------------------------
  const handleSearchEan = async (v: string, cb: any) => {
    if (!v) {
      cb(null);
      return;
    }
    message.destroy();
    setLoading(true);
    const hide = message.loading('Searching EAN...', 0);

    return getEanDetail({ skuOrEan: v, with: eleOptions?.with || '' })
      .then((res) => {
        cb(res);
        return res;
      })
      .catch(() => {
        cb(null);
      })
      .finally(() => {
        setLoading(false);
        hide();
      });
  };

  const debouncedHandleSearchEan = useCallback(
    debounce((newValue, cb) => handleSearchEan(newValue, cb), 330),
    [],
  );

  const formElements = useMemo(() => {
    return (
      <>
        <input
          className="ant-input ant-input-lg"
          ref={fieldRef as any}
          style={{ ...eleOptions?.style }}
          placeholder="Please scan EAN..."
          onChange={(e) => {
            const value = e.target.value;

            debouncedHandleSearchEan(value, (eanData: API.Ean) => {
              if (eanData) {
                setItemEan(eanData);
                eleOptions?.cbSearched?.(eanData);
              } else {
                setItemEan(null);
                eleOptions?.cbNotFound?.(value);
              }
            })?.catch((err) => {
              eleOptions?.cbNotFound?.(value);
              Util.error(err);
            });
          }}
        />

        {itemEan?.sku && !eleOptions?.skipEanInfo && (
          <>
            <ProFormItem label=" " style={{ marginBottom: 0 }} colon={false}>
              <Typography.Text
                style={{ display: 'inline-block', fontSize: 14 }}
                copyable={{ text: itemEan?.sku || '' }}
              >
                {itemEan?.sku} {itemEan?.is_single ? '' : `(Qty.Pkg: ${itemEan?.attr_case_qty ?? '-'})`}
              </Typography.Text>
            </ProFormItem>
            <ProFormItem label=" " style={{ marginBottom: 0 }} colon={false}>
              <Typography.Text
                style={{ display: 'inline-block', fontSize: 14 }}
                copyable={{ text: itemEan?.ean || '' }}
              >
                {itemEan?.ean}
              </Typography.Text>
            </ProFormItem>
          </>
        )}
      </>
    );
  }, [eleOptions, loading, debouncedHandleSearchEan, itemEan?.sku]);

  return { itemEan, setItemEan, fieldRef, loading, eleOptions, formElements, debouncedHandleSearchEan };
};
