import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, message, Modal, Row, Typography, Input, Button, Space, Popover, Divider } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormField, ProFormUploadButton } from '@ant-design/pro-form';
import ProForm, {
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormSwitch,
} from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { getEanList, updateEanAttribute, updateEanAttributePartial } from '@/services/foodstore-one/Item/ean';
import Util, { sn } from '@/util';
import _ from 'lodash';
import HtmlEditor from '@/components/HtmlEditor';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import type { UploadFile } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/lib/upload';
import { getCountriesDEOptions } from '@/services/foodstore-one/countries';
import SocialLinks from './SocialIcons';
import { HighlightOutlined, InfoCircleOutlined, LinkOutlined } from '@ant-design/icons';
import { Link } from 'umi';
import ModalNavigation from './ModalNavigation';
import type { HandleNavFuncType } from '../hooks/useModalNavigation';
import type { ValidateStatus } from 'antd/es/form/FormItem';
import GdsnItemButton from './GdsnItemButton';
import useGdsnItem from '../hooks/useGdsnItem';
import useUpdateModalActions from '../hooks/useUpdateModalActions';
import XMLViewer from 'react-xml-viewer';

export type FormValueType = {
  upSync?: string;
  texts?: API.EanText[];
} & Partial<API.Ean> & { closeModal?: boolean };

const handleUpdate = async (fields: FormValueType) => {
  const data = new FormData();
  const texts: API.EanText[] = [];
  if (fields?.ean_texts && fields?.ean_texts.length) {
    let ind = 0;
    for (const tmp of fields.ean_texts) {
      const i_pic_obj = tmp.i_pic?.[0]?.originFileObj ?? tmp.i_pic?.[0]?.id ?? '';
      const n_pic_obj = tmp.n_pic?.[0]?.originFileObj ?? tmp.n_pic?.[0]?.id ?? '';

      const tmpClone = { ...tmp };
      data.append(`textsFiles[${ind}][i_pic]`, i_pic_obj);
      delete tmpClone.i_pic;

      data.append(`textsFiles[${ind}][n_pic]`, n_pic_obj);
      delete tmpClone.n_pic;

      texts.push({
        ...tmpClone,
        // @ts-ignore
        official_producer: tmp?.official_producer?.id ?? tmp?.official_producer ?? '',
      });
      ind++;
    }
  }
  const hide = message.loading('Updating...', 0);

  data.append('id', `${fields.id}`);
  data.append('mode', 'texts');
  // data.append('upSync', `${fields.upSync || ''}`);
  data.append('texts', JSON.stringify(texts));

  try {
    const res = await updateEanAttribute(data as API.Ean);
    hide();
    message.success('Updated successfully.');
    if (res.upSyncMessage) {
      message.error('Up sync error: ' + res.upSyncMessage);
    }
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!');
    return false;
  }
};

const defaultInitialValues: API.Ean = {
  ean_texts: [
    {
      lang: 'DE',
      i_pic: [],
      n_pic: [],
    },
  ],
};

export type UpdateTextsFormProps = {
  initialValues?: Partial<API.Ean> & { openParent?: boolean };
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean, isParent?: boolean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  handleNavigation?: HandleNavFuncType;
  isRefetchInitialValues?: boolean; // if true, we should reload initialValues from backend.
  gdsn?: boolean;
};

const UpdateTextsForm: React.FC<UpdateTextsFormProps> = (props) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);

  // "Internal Short Name": Count Letters as usual. Mark yellow Len31..40 and Red >40.
  const [shortNameStatus, setShortNameStatus] = useState<ValidateStatus>('');

  // For stacked modal. Used to open a parent EAN's texts modal.
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);

  // refetch initial values
  const [refetchedInitialValues, setRefetchedInitialValues] = useState<
    (API.Ean & { openParent?: boolean }) | undefined
  >(props.initialValues);
  const [loadingRefetch, setLoadingRefetch] = useState<boolean>(false);

  // GDSN data
  const {
    gdsnItem,
    fetchGdsnItem,
    renderTakeButton,
    loading: loadingGdsn,
  } = useGdsnItem(props.initialValues?.ean, props.modalVisible);

  useEffect(() => {
    if (props.modalVisible && props.initialValues?.id) {
      setRefetchedInitialValues(props.initialValues);

      if (props.isRefetchInitialValues) {
        setLoadingRefetch(true);
        getEanList({ id: props.initialValues?.id })
          .then((res) => {
            setRefetchedInitialValues(res.data[0]);
          })
          .catch(Util.error)
          .finally(() => setLoadingRefetch(false));
      }
    }
  }, [props.isRefetchInitialValues, props.modalVisible, props.initialValues]);

  const isSingleEan = !!refetchedInitialValues?.id && refetchedInitialValues?.id == refetchedInitialValues?.parent_id;

  useEffect(() => {
    if (!props.modalVisible) return;
    if (formRef.current) {
      const newValues: API.Ean = { ...(refetchedInitialValues || defaultInitialValues) };
      if (!newValues.ean_texts || newValues.ean_texts.length < 1) {
        newValues.ean_texts = defaultInitialValues.ean_texts;
      }

      if (!isSingleEan) {
        const parentText = newValues.parent?.ean_texts?.[0];
        for (const text of newValues.ean_texts || []) {
          text.official_country = parentText?.official_country;
          text.official_ingredients = parentText?.official_ingredients;
          text.official_nutrition = parentText?.official_nutrition;
          text.official_producer = parentText?.official_producer;
          text.official_producer_obj = parentText?.official_producer_obj;
          text.official_title = parentText?.official_title;
          text.official_title_ebay = parentText?.official_title_ebay;
          text.official_usage = parentText?.official_usage;
          text.official_warning = parentText?.official_warning;
          text.ingredients_pic = parentText?.ingredients_pic;
          text.nutrition_pic = parentText?.nutrition_pic;

          // Check override mode
          if (!text.settings?.useOwnText) {
            text.short_description = parentText?.short_description;
            text.description1 = parentText?.description1;
            text.meta_title = parentText?.meta_title;
            text.meta_keywords = parentText?.meta_keywords;
            text.meta_description = parentText?.meta_description;
            text.fs_export_google_description = parentText?.fs_export_google_description;
          }
        }
      }

      if (newValues.ean_texts) {
        for (const eanText of newValues.ean_texts) {
          eanText.i_pic = eanText.ingredients_pic ? [eanText.ingredients_pic] : [];
          eanText.n_pic = eanText.nutrition_pic ? [eanText.nutrition_pic] : [];
        }
        const len = (newValues.ean_texts[0].internal_short_name ?? '').length;
        if (len > 30 && len < 41) {
          setShortNameStatus('warning');
        } else if (len > 40) {
          setShortNameStatus('error');
        } else {
          setShortNameStatus('');
        }
      }
      formRef.current.setFieldsValue(newValues);
    }
  }, [refetchedInitialValues, props.modalVisible, isSingleEan]);

  useEffect(() => {
    if (!isSingleEan && props.modalVisible && refetchedInitialValues?.openParent) {
      handleUpdateTextsModalVisible(true);
    }
  }, [refetchedInitialValues?.openParent, props.modalVisible, isSingleEan]);

  // Image preview
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await Util.getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const [gdsnItemsDiff, setGdsnItemsDiff] = useState<any>({});
  const validateGdsnItemsDiff = useCallback(() => {
    const values = formRef.current?.getFieldsValue() ?? {};
    setGdsnItemsDiff({
      name: values.ean_texts?.[0]?.name == gdsnItem?.detail?.short_description,
      short_description: values.ean_texts?.[0]?.short_description == gdsnItem?.name,
      description1: values.ean_texts?.[0]?.description1 == gdsnItem?.detail?.description1,
      meta_keywords: values.ean_texts?.[0]?.meta_keywords == gdsnItem?.detail?.tradeItemKeyWords,
      official_usage: values.ean_texts?.[0]?.official_usage == gdsnItem?.detail?.storage_instruction,
      official_warning: values.ean_texts?.[0]?.official_warning == gdsnItem?.detail?.health_compulsory,
      official_country: values.ean_texts?.[0]?.official_country == gdsnItem?.detail?.country_code,
      official_ingredients:
        Util.removeTags(values.ean_texts?.[0]?.official_ingredients) ==
        Util.removeTags(gdsnItem?.detail?.ean_text?.official_ingredients),
      official_title: values.ean_texts?.[0]?.official_title == gdsnItem?.detail?.ean_text?.official_title,
      official_nutrition:
        Util.removeTags(values.ean_texts?.[0]?.official_nutrition) ==
        Util.removeTags(gdsnItem?.detail?.ean_text?.official_nutrition),
    });
  }, [
    gdsnItem?.name,
    gdsnItem?.detail?.short_description,
    gdsnItem?.detail?.description1,
    gdsnItem?.detail?.tradeItemKeyWords,
    gdsnItem?.detail?.storage_instruction,
    gdsnItem?.detail?.health_compulsory,
    gdsnItem?.detail?.country_code,
    gdsnItem?.detail?.ean_text?.official_ingredients,
    gdsnItem?.detail?.ean_text?.official_title,
    gdsnItem?.detail?.ean_text?.official_nutrition,
  ]);

  useEffect(() => {
    if (props.modalVisible && props.initialValues?.ean) {
      validateGdsnItemsDiff();
    }
  }, [props.modalVisible, props.initialValues?.ean, validateGdsnItemsDiff]);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useUpdateModalActions(
    props.initialValues?.id ?? 0,
    props.initialValues?.sku ?? '',
    formRef.current,
  );

  const isTextEditable = props.initialValues?.is_single || !!props.initialValues?.ean_texts?.[0]?.settings?.useOwnText;

  return (
    <>
      <ModalForm
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>Update EAN texts -&nbsp;</span>
            <Typography.Paragraph
              copyable={{
                text: refetchedInitialValues?.ean || '',
                tooltips: 'Copy EAN ' + (refetchedInitialValues?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {refetchedInitialValues?.ean || ''}
            </Typography.Paragraph>
            <SocialLinks
              ean={refetchedInitialValues?.ean || ''}
              title={refetchedInitialValues?.ean_texts?.[0]?.name}
              style={{ marginLeft: 50 }}
            />
            {props.handleNavigation ? (
              <ModalNavigation
                modalName="text"
                eanId={refetchedInitialValues?.id}
                itemId={refetchedInitialValues?.item_id}
                handleNavigation={props.handleNavigation}
                style={{ marginLeft: 50 }}
              />
            ) : null}
            {props.gdsn && (
              <GdsnItemButton
                ean={props.initialValues?.ean}
                eanId={props.initialValues?.id}
                itemId={props.initialValues?.item_id}
                style={{ marginLeft: 30 }}
                fetchGdsnItem={fetchGdsnItem}
              />
            )}
            {actionButtons}
          </div>
        }
        width={900}
        visible={props.modalVisible}
        onVisibleChange={props.handleModalVisible}
        grid
        readonly={props.isRefetchInitialValues && loadingRefetch}
        modalProps={{
          maskClosable: true,
          className:
            (refetchedInitialValues?.is_single ? 'm-single' : 'm-multi') +
            (props.isRefetchInitialValues && loadingRefetch ? ' m-masked' : ''),
        }}
        formRef={formRef}
        onFinish={async (values) => {
          if (formRef.current?.isFieldsTouched()) {
            const success = await handleUpdate({ ...values, id: refetchedInitialValues?.id });
            // Important: We immediately reset upSync field.
            // formRef.current.setFieldsValue({ ...values, upSync: '' });
            if (success) {
              await runActionsCallback();

              if (values.closeModal) props.handleModalVisible(false);
              if (props.onSubmit) props.onSubmit(values, isSingleEan);
            }
          } else {
            props.handleModalVisible(false);
          }
        }}
        submitter={{
          render: (p, dom) => {
            return (
              <Space>
                {actionButtons}
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    formRef.current?.setFieldValue('closeModal', 1);
                    p.submit();
                  }}
                >
                  Save & Close
                </Button>
                <Button
                  type="default"
                  size="small"
                  onClick={() => {
                    props.handleModalVisible(false);
                  }}
                >
                  Cancel
                </Button>
              </Space>
            );
          },
        }}
        disabled={loadingGdsn || loading}
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="closeModal" />
          {hiddenFormElements}
        </div>
        {/* <ProFormField name="upSync" style={{ display: 'none' }} formItemProps={{ style: { display: 'none' } }} /> */}
        <ProFormGroup rowProps={{ gutter: 24 }}>
          <ProFormList
            key={'ean_texts'}
            name="ean_texts"
            creatorButtonProps={{
              position: 'bottom',
              creatorButtonText: 'Add a language',
            }}
            max={1}
            deleteIconProps={{ tooltipText: 'Remove' }}
            copyIconProps={{ tooltipText: 'Copy row' }}
          >
            <ProFormSelect
              name={'lang'}
              label="Language"
              rules={[
                {
                  required: true,
                  message: 'Language is required',
                },
              ]}
              initialValue={'DE'}
              options={[{ value: 'DE', label: 'German' } /* , { value: 'EN', label: 'English' } */]}
            />

            {refetchedInitialValues?.id && !refetchedInitialValues?.is_single && (
              <Space size={56}>
                <ProFormField label="ProductName of Single EAN">
                  <Space>
                    <Typography.Title
                      copyable={{
                        text:
                          refetchedInitialValues?.parent?.ean_texts?.[0]?.name ??
                          refetchedInitialValues?.item?.name ??
                          '',
                      }}
                      level={5}
                      style={{ marginBottom: 0 }}
                    >
                      {refetchedInitialValues?.parent?.ean_texts?.[0]?.name ?? refetchedInitialValues?.item?.name}
                    </Typography.Title>
                    <Link
                      to={`/item/ean-all?ean_id=${refetchedInitialValues?.parent?.id}&sku=${refetchedInitialValues?.parent?.sku}`}
                      target="_blank"
                    >
                      <LinkOutlined />
                    </Link>
                  </Space>
                </ProFormField>
                <ProFormField label="Qty / package">
                  <Typography.Title level={5} style={{ marginBottom: 0 }}>
                    {refetchedInitialValues?.attr_case_qty}
                  </Typography.Title>
                </ProFormField>
              </Space>
            )}

            {/* 
            // disabled since @2024-02-23
            <ProFormText
              name={'internal_short_name'}
              label="Internal Short Name"
              tooltip="Internal usage only!"
              colProps={{ span: 24 }}
              // status={shortNameStatus}
              // status={'warning'}
              fieldProps={{
                maxLength: 255,
                showCount: true,
                status: shortNameStatus as any,
                onChange: (e: any) => {
                  const value = e.target.value ?? '';
                  const len = (value ?? '').length;
                  if (len > 30 && len <= 40) {
                    setShortNameStatus('warning');
                  } else if (len > 40) {
                    setShortNameStatus('error');
                  } else {
                    setShortNameStatus('');
                  }
                },
              }}
            /> */}

            <Row gutter={8}>
              <Col span={12}>
                <ProFormSelect
                  name={['official_producer']}
                  label="Lebensmittelunternehmer"
                  tooltip="Official Producer"
                  placeholder="Please select producer"
                  mode="single"
                  showSearch
                  readonly={!isSingleEan}
                  convertValue={(value, namePath) => (value ? sn(value) : value)}
                  request={getProducerListSelectOptions}
                  formItemProps={{ style: { marginBottom: 4 } }}
                  fieldProps={{
                    onChange: (value) => {
                      if (value) {
                        formRef.current?.setFieldValue(['ean_texts', 0, 'official_producer_gdsn'], null);
                      }
                    },
                  }}
                />
                <Divider style={{ marginTop: 2, marginBottom: 2 }} plain>
                  <span className="text-sm c-grey">Or</span>&nbsp;&nbsp;
                  <Popover
                    trigger="click"
                    title="Click to view XML"
                    content={<XMLViewer xml={gdsnItem?.contact_xml || ''} collapsible />}
                  >
                    <InfoCircleOutlined />
                  </Popover>
                </Divider>
                <ProFormText
                  name={['official_producer_gdsn']}
                  placeholder="Official Producer (GDSN)"
                  readonly={!isSingleEan}
                  help={
                    isSingleEan && (
                      <div className="text-sm" style={{ marginBottom: 16 }}>
                        {gdsnItem?.contacts?.map?.((x, ind) => {
                          const fullAddr = `${x.name || ''} ${x.address || ''}`.trim();
                          return (
                            // eslint-disable-next-line react/no-array-index-key
                            <Row key={`${x.code}_${ind}`}>
                              <Col span={3}>{x.code}</Col>
                              <Col span={21}>
                                {x.name || x.address ? (
                                  <>
                                    <Typography.Text
                                      copyable={{
                                        text: fullAddr,
                                      }}
                                      ellipsis
                                    >
                                      {fullAddr}
                                    </Typography.Text>
                                    <Button
                                      type="link"
                                      size="small"
                                      title="Take this address."
                                      style={{ marginLeft: 6 }}
                                      onClick={() => {
                                        formRef.current?.setFieldValue(['ean_texts', 0, 'official_producer'], null);
                                        formRef.current?.setFieldValue(
                                          ['ean_texts', 0, 'official_producer_gdsn'],
                                          fullAddr,
                                        );
                                      }}
                                      icon={<HighlightOutlined />}
                                    />
                                    {x.channels?.map?.((x2) => {
                                      return (
                                        <div key={`${x2.code}_${x2.value}`}>
                                          {x2.code}: {x2.value}
                                        </div>
                                      );
                                    })}
                                  </>
                                ) : (
                                  x.channels?.map?.((x2) => {
                                    return (
                                      <div key={`${x2.code}_${x2.value}`}>
                                        {x2.code}: {x2.value}
                                      </div>
                                    );
                                  })
                                )}
                              </Col>
                            </Row>
                          );
                        })}
                      </div>
                    )
                  }
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  name="official_country"
                  label="Herkunftsland"
                  tooltip="Official Country"
                  options={getCountriesDEOptions()}
                  showSearch
                  readonly={!isSingleEan}
                  formItemProps={{ initialValue: 'Deutschland' }}
                  fieldProps={{
                    onChange(value) {
                      validateGdsnItemsDiff();
                    },
                  }}
                  help={
                    isSingleEan &&
                    gdsnItem &&
                    renderTakeButton(
                      getCountriesDEOptions().find((x) => x.value == gdsnItem?.detail?.country_code)?.label,
                      () => {
                        formRef.current?.setFieldValue(
                          ['ean_texts', 0, 'official_country'],
                          gdsnItem?.detail?.country_code,
                        );
                        validateGdsnItemsDiff();
                      },
                      gdsnItemsDiff.official_country,
                    )
                  }
                />
              </Col>
            </Row>

            <Row gutter={8}>
              <Col span={14}>
                <ProFormText
                  name={'name'}
                  label={
                    <>
                      <span>Product Name &nbsp;</span>
                      <ProFormDependency key={'name_length'} name={['name']}>
                        {(depValues) => {
                          return (
                            <span className={depValues.name?.length > 65 ? 'red' : ''}>
                              {depValues.name ? '(' + depValues.name.length + ')' : ''}
                            </span>
                          );
                        }}
                      </ProFormDependency>

                      {sn(props.initialValues?.attr_case_qty) > 1 && (
                        <Typography.Text
                          copyable={{
                            text: `Multipack: ${props.initialValues?.attr_case_qty}x `,
                            tooltips: `Copy "Multipack: ${props.initialValues?.attr_case_qty}x "`,
                          }}
                          style={{ marginLeft: 16 }}
                        />
                      )}

                      {props.initialValues?.item?.trademark?.name ? (
                        <Button
                          type="link"
                          size="small"
                          title="Add Trademark Prefix."
                          style={{ marginLeft: 6 }}
                          onClick={() => {
                            formRef.current?.setFieldValue(
                              ['ean_texts', 0, 'name'],
                              `${props.initialValues?.item?.trademark?.name} ${formRef.current?.getFieldValue([
                                'ean_texts',
                                0,
                                'name',
                              ])}`,
                            );
                            validateGdsnItemsDiff();
                          }}
                          icon={<HighlightOutlined />}
                        />
                      ) : null}
                    </>
                  }
                  formItemProps={{ initialValue: refetchedInitialValues?.item?.name }}
                  fieldProps={{
                    onChange(value) {
                      validateGdsnItemsDiff();
                    },
                  }}
                  help={
                    gdsnItem &&
                    renderTakeButton(
                      gdsnItem.detail?.short_description,
                      () => {
                        formRef.current?.setFieldValue(['ean_texts', 0, 'name'], gdsnItem.detail?.short_description);
                        validateGdsnItemsDiff();
                      },
                      gdsnItemsDiff.name,
                      {
                        showCopyable: true,
                        renderExtra: () => {
                          return (
                            <Button
                              type="link"
                              size="small"
                              title="Take item value."
                              style={{ marginLeft: 6 }}
                              onClick={() => {
                                formRef.current?.setFieldValue(
                                  ['ean_texts', 0, 'name'],
                                  props.initialValues?.item?.name || '',
                                );
                                validateGdsnItemsDiff();
                              }}
                              icon={<HighlightOutlined />}
                            />
                          );
                        },
                      },
                    )
                  }
                />
              </Col>
              <Col span={10}>
                <ProFormText
                  name={'name_cat'}
                  label="Product Name Category"
                  help={
                    gdsnItem && (
                      <div className="text-xs" title="GDSN net content">
                        {`${gdsnItem.detail?.net_content} ${gdsnItem.detail?.net_content_unit ?? 'N/A'}`}
                      </div>
                    )
                  }
                />
              </Col>
            </Row>

            <ProFormText
              name={'fs_ebay_title_special'}
              label="Titel Sonderaktion (Ebay)"
              tooltip="Titel Sonderaktion (Ebay). Max 65"
              colProps={{ span: 24 }}
              fieldProps={{ maxLength: 65, showCount: true }}
            />

            <ProFormText
              name={'fs_ebay_title_special_alt'}
              label="Alternative Titel Sonderaktion (Ebay)"
              tooltip="Used in case `Length(badge + Titel Sonderaktion (Ebay)) > 65`. Max 45"
              colProps={{ span: 24 }}
              fieldProps={{ maxLength: 45, showCount: true }}
            />

            {/* <ProCard
              title={
                isSingleEan ? undefined : (
                  <>
                    <Typography.Paragraph
                      copyable={{
                        text: refetchedInitialValues?.parent?.ean || '',
                        tooltips: 'Copy EAN ' + (refetchedInitialValues?.parent?.ean || ''),
                      }}
                      style={{ display: 'inline-block', marginBottom: 0 }}
                    >
                      Inherited from parent EAN -{' '}
                      <Button type="link" onClick={() => handleUpdateTextsModalVisible(true)}>
                        {' '}
                        {refetchedInitialValues?.parent?.ean || ''}{' '}
                      </Button>
                    </Typography.Paragraph>
                  </>
                )
              }
              style={isSingleEan ? {} : { marginLeft: 4, marginRight: 4 }}
              bodyStyle={isSingleEan ? { padding: 0 } : { padding: 24 }}
              headerBordered
              bordered={!isSingleEan}
              collapsible={!isSingleEan}
              defaultCollapsed={!isSingleEan}
            > */}
            {refetchedInitialValues?.is_single ? null : (
              <ProFormGroup labelLayout="inline">
                <ProFormSwitch
                  name={['settings', 'useOwnText']}
                  label="No inherit?"
                  fieldProps={{
                    onChange(value: boolean) {
                      const hide = message.loading('Updating inheritance mode...', 0);
                      setLoading(true);
                      updateEanAttributePartial({
                        id: props.initialValues?.id,
                        mode: 'settings.useOwnText',
                        ean_text_de: { lang: 'DE', ean_id: props.initialValues?.id, settings: { useOwnText: value } },
                      })
                        .then((res) => {
                          props?.onSubmit?.({}, refetchedInitialValues?.is_single);
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                          setLoading(false);
                        });
                    },
                  }}
                />
              </ProFormGroup>
            )}
            <ProFormTextArea
              name={'short_description'}
              label="Short Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              colProps={{ span: 24 }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.name,
                  () => {
                    formRef.current?.setFieldValue(['ean_texts', 0, 'short_description'], gdsnItem?.name);
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.short_description,
                  { showCopyable: true },
                )
              }
            />
            <ProFormTextArea
              name={'description1'}
              label="Description"
              tooltip="Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                rows: 6,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              colProps={{ span: 24 }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.description1,
                  () => {
                    formRef.current?.setFieldValue(['ean_texts', 0, 'description1'], gdsnItem?.detail?.description1);
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.description1,
                  { showCopyable: true },
                )
              }
            />

            <ProFormText
              name={'meta_title'}
              label="Meta Title"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              colProps={{ span: 24 }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.meta_title,
                  () => {
                    formRef.current?.setFieldValue(['ean_texts', 0, 'meta_title'], gdsnItem?.detail?.meta_title);
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.meta_title,
                  { showCopyable: true },
                )
              }
            />
            <ProFormTextArea
              name={'meta_keywords'}
              label="Meta Keywords"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                maxLength: 255,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              colProps={{ span: 24 }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.tradeItemKeyWords,
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'meta_keywords'],
                      gdsnItem?.detail?.tradeItemKeyWords,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.meta_keywords,
                  { showCopyable: true },
                )
              }
            />
            <ProFormTextArea
              name={'meta_description'}
              label="Meta Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              colProps={{ span: 24 }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.meta_description,
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'meta_description'],
                      gdsnItem?.detail?.meta_description,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.meta_description,
                  { showCopyable: true },
                )
              }
            />
            <ProFormTextArea
              name={'fs_export_google_description'}
              label="Google Export Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
              }}
              colProps={{ span: 24 }}
            />
            <ProFormText
              name={'official_title'}
              label="Juristische Produktbezeichnung"
              tooltip="Official title"
              readonly={!isSingleEan}
              fieldProps={{
                showCount: true,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              colProps={{ span: 24 }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.ean_text?.official_title,
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'official_title'],
                      gdsnItem?.detail?.ean_text?.official_title,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.official_title,
                )
              }
            />
            <ProFormDependency name={['official_title', 'official_title_ebay']}>
              {(depsValues) => {
                const len = depsValues.official_title?.length;
                const lenSelf = depsValues.official_title_ebay?.length;
                return (
                  <ProFormText
                    name={'official_title_ebay'}
                    label="Max. 65 Zeichen Ebay"
                    tooltip="Max. 65 Zeichen Ebay"
                    readonly={!isSingleEan}
                    colProps={{ span: 24 }}
                    fieldProps={{ maxLength: 65, showCount: true }}
                    formItemProps={{
                      style: { display: len > 65 || lenSelf > 0 ? 'block' : 'none' },
                    }}
                  />
                );
              }}
            </ProFormDependency>

            <ProForm.Item
              name={'official_ingredients'}
              label="Zutaten"
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  Util.removeTags(gdsnItem?.detail?.ean_text?.official_ingredients),
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'official_ingredients'],
                      gdsnItem?.detail?.ean_text?.official_ingredients,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.official_ingredients,
                )
              }
            >
              {isSingleEan ? (
                <HtmlEditor
                  initialValue={refetchedInitialValues?.ean_texts?.[0]?.official_ingredients}
                  onChangeHtml={(value) => {
                    validateGdsnItemsDiff();
                  }}
                />
              ) : (
                <>
                  <Input.TextArea
                    value={refetchedInitialValues?.parent?.ean_texts?.[0]?.official_ingredients || ''}
                    disabled={!isSingleEan}
                  />
                </>
              )}
            </ProForm.Item>
            <ProFormUploadButton
              max={1}
              name="i_pic"
              label="Zutaten (Pic)"
              title="Select File"
              accept="image/*"
              listType="picture-card"
              formItemProps={{ wrapperCol: { span: 12 } }}
              disabled={!isSingleEan}
              fieldProps={{
                beforeUpload: (file) => {
                  return false;
                },
                onPreview: handlePreview,
                style: { marginBottom: 24 },
              }}
            />
            <ProFormTextArea
              name={'official_usage'}
              label="Gebrauch, Aufbewahrung und Verwendung"
              readonly={!isSingleEan}
              fieldProps={{
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.storage_instruction,
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'official_usage'],
                      gdsnItem?.detail?.storage_instruction,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.official_usage,
                )
              }
            />
            <ProFormTextArea
              name={'official_warning'}
              label="Warnhinweise"
              readonly={!isSingleEan}
              fieldProps={{
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  gdsnItem?.detail?.health_compulsory,
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'official_warning'],
                      gdsnItem?.detail?.health_compulsory,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.official_warning,
                )
              }
            />
            {sn(gdsnItem?.detail?.diets?.length) > 0 && (
              <div style={{ marginBottom: 12 }} className="">
                Diet information: {gdsnItem?.detail?.diets?.map((x: any) => x.code)?.join(', ')}
              </div>
            )}
            <ProForm.Item
              name={'official_nutrition'}
              label="Nährwerte"
              help={
                isSingleEan &&
                gdsnItem &&
                renderTakeButton(
                  Util.removeTags(gdsnItem?.detail?.ean_text?.official_nutrition),
                  () => {
                    formRef.current?.setFieldValue(
                      ['ean_texts', 0, 'official_nutrition'],
                      gdsnItem?.detail?.ean_text?.official_nutrition,
                    );
                    validateGdsnItemsDiff();
                  },
                  gdsnItemsDiff.official_nutrition,
                )
              }
            >
              {isSingleEan ? (
                <HtmlEditor initialValue={refetchedInitialValues?.ean_texts?.[0]?.official_nutrition} />
              ) : (
                <>
                  <Input.TextArea
                    value={refetchedInitialValues?.parent?.ean_texts?.[0]?.official_nutrition || ''}
                    disabled={!isSingleEan}
                  />
                </>
              )}
            </ProForm.Item>
            <ProFormUploadButton
              max={1}
              name="n_pic"
              label="Nährwerte (Pic)"
              title="Select File"
              accept="image/*"
              listType="picture-card"
              formItemProps={{ wrapperCol: { span: 12 } }}
              disabled={!isSingleEan}
              fieldProps={{
                beforeUpload: (file) => {
                  return false;
                },
                onPreview: handlePreview,
                style: { marginBottom: 24 },
              }}
            />
            {/* </ProCard> */}
          </ProFormList>
        </ProFormGroup>
        <Modal open={previewVisible} title={previewTitle} footer={null} onCancel={() => setPreviewVisible(false)}>
          <img alt="example" style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </ModalForm>

      {!isSingleEan && props.modalVisible && (
        <>
          <UpdateTextsForm
            modalVisible={updateTextsModalVisible}
            handleModalVisible={handleUpdateTextsModalVisible}
            initialValues={refetchedInitialValues?.parent}
            isRefetchInitialValues={props.isRefetchInitialValues}
            onSubmit={async (value, isParent) => {
              if (isParent) {
                props?.onSubmit?.({
                  parent: {
                    ...refetchedInitialValues?.parent,
                    ...value,
                  },
                });
              }
            }}
            onCancel={() => {
              handleUpdateTextsModalVisible(false);
            }}
          />
        </>
      )}
    </>
  );
};

export default UpdateTextsForm;
