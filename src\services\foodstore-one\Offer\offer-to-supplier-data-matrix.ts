import { request } from "umi";
import { paramsSerializer } from "../api";

const urlPrefix = '/api/offer';

export type Offer2SupplierDataMatrixResultType = {
    importIds: number[];
    offers: (API.Offer)[];
    importAssoc: Record<number, API.Import>;
}

/** get GET /api/offer/getOffer2SupplierDataMatrix */
export async function getOffer2SupplierDataMatrix(params?: API.PageParams) {
    return request<API.ResultObject<Offer2SupplierDataMatrixResultType>>(`${urlPrefix}/getOffer2SupplierDataMatrix`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params?.pageSize || 100,
            page: params?.current || 1,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => res.message);
}

/** 
 * Update matrix to Offer
 * 
 * PUT /api/offer/updateOffer2SupplierDataMatrix */
export async function updateOffer2SupplierDataMatrix(data: { importIds: number[], ds?: Record<number, any> }, options?: { [key: string]: any }) {
    return request<API.ResultObject<API.OfferItemIboPreMap>>(`${urlPrefix}/updateOffer2SupplierDataMatrix`, {
        method: 'PUT',
        data: data,
        ...(options || {}),
    }).then(res => res.message);
}
