import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { message } from 'antd';
import Util from '@/util';
import { addOrderShippingDescProviderMap } from '@/services/foodstore-one/BasicData/order-shipping-desc-provider-map';
import { getOrderShippingProviderList } from '@/services/foodstore-one/BasicData/order-shipping-provider';

const handleAdd = async (fields: API.ShippingDescProvider) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addOrderShippingDescProviderMap(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.ShippingDescProvider>;

export type CreateOrderShippingDescProviderMapFormProps = {
  values?: Partial<API.ShippingDescProvider>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.ShippingDescProvider) => Promise<boolean | void>;
};

const CreateOrderShippingDescProviderMapForm: React.FC<CreateOrderShippingDescProviderMapFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  return (
    <ModalForm
      title={'New Shipping Provider Map'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.ShippingDescProvider);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          handleModalVisible(false);
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Shipping Description is required',
          },
        ]}
        width="lg"
        name="shipping_desc"
        label="Shipping Description"
      />

      <ProFormSelect
        width="sm"
        name="provider_name"
        label="Provider Name"
        request={async (params) => {
          return getOrderShippingProviderList(params, {}, {}).then((res) =>
            res.data.map((x) => ({ value: x.name, lable: x.name })),
          );
        }}
      />
    </ModalForm>
  );
};

export default CreateOrderShippingDescProviderMapForm;
