CREATE TABLE `ean_task` (
                            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                            `ean` varchar(50) DEFAULT NULL,
                            `task` text DEFAULT NULL,
                            `created_by` bigint(20) unsigned DEFAULT NULL,
                            `created_on` datetime DEFAULT NULL,
                            `updated_by` bigint(20) DEFAULT NULL,
                            `updated_on` datetime DEFAULT NULL,
                            PRIMARY KEY (`id`),
                            <PERSON>EY `FK_ean_task_ean` (`ean`),
                            KEY `FK_ean_task_created_by` (`created_by`),
                            CONSTRAINT `FK_ean_task_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`) ON DELETE SET NULL ON UPDATE CASCADE,
                            CONSTRAINT `FK_ean_task_ean` FOREIGN KEY (`ean`) REFERENCES `item_ean` (`ean`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


