import { PlusOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Tag } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import { getEmailAccountList, deleteEmailAccount } from '@/services/foodstore-one/Email/email-account';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.EmailAccount[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEmailAccount({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const EmailAccountList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.EmailAccount>();
  const [selectedRowsState, setSelectedRows] = useState<API.EmailAccount[]>([]);

  const columns: ProColumns<API.EmailAccount>[] = [
    {
      title: 'Email',
      dataIndex: 'email',
      sorter: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: true,
      width: 100,
      align: 'center',
      render: (dom, record) => {
        return record.status ? <Tag color="success">Active</Tag> : <Tag color="default">Inactive</Tag>;
      },
    },
    {
      title: 'Mail Server',
      dataIndex: ['email_server', 'domain'],
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: 'IMAP Host',
      dataIndex: ['email_server', 'imap_host'],
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: 'SMTP Host',
      dataIndex: ['email_server', 'smtp_host'],
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: 'Default Sender Name',
      dataIndex: 'sender_name',
      sorter: true,
      width: 150,
    },
    {
      title: 'Since date',
      dataIndex: ['settings', 'imapSince'],
      sorter: true,
      width: 150,
      align: 'center',
      render: (dom, record) => {
        return Util.dtToDMY(record.settings?.imapSince || '');
      },
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.EmailAccount, API.PageParams>
        headerTitle={'Email Account list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        params={{ with: 'emailServer' }}
        request={getEmailAccountList}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={<SFooterToolbarExtra title={'account'} selectedRowsState={selectedRowsState} actionRef={actionRef} />}
        >
          <BatchDeleteAction
            title="account"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      {/* {selectedRowsState?.length > 0 && (
        <SFooterToolbar
          title="supplier"
          selectedRowsState={selectedRowsState}
          setSelectedRows={setSelectedRows}
          actionRef={actionRef}
          handleRemove={handleRemove}
        />
      )} */}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.email && (
          <ProDescriptions<API.EmailAccount>
            column={2}
            title={currentRow?.email}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.email,
            }}
            columns={columns as ProDescriptionsItemProps<API.EmailAccount>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default EmailAccountList;
