import {
  <PERSON>ff<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Button,
  Col,
  Modal,
  Popover,
  Row,
  Space,
  Spin,
  Switch,
  Tag,
  Typography,
  notification,
} from 'antd';
import { Card } from 'antd';
import { message } from 'antd';
import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';

import Util, { nf3, ni, skuToItemId, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText, ProFormTextArea } from '@ant-design/pro-form';

import styles from './OrderDetailPacking.less';

import { IRouteComponentProps, useLocation, useModel } from 'umi';
import {
  CheckOutlined,
  CommentOutlined,
  EditOutlined,
  EyeOutlined,
  FilePdfOutlined,
  LinkOutlined,
  PrinterOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { exportPicklistDeliveryNoteByEntityId } from '@/services/foodstore-one/Warehouse/picklist';
import {
  DictCode,
  LS_PREFIX,
  MagentoOrderStatusOptions,
  MagOrderExtraStatus,
  OrderUserActionLogNote,
  OrderUserActionLogType,
  UserRole,
} from '@/constants';
import ExpDate from '@/pages/Report/Order/components/ExpDate';
import ProDescriptions from '@ant-design/pro-descriptions';
import { FullAddress, isOrderAddressEditable } from '.';
import OrderShipmentCommentsList from '@/pages/Warehouse/PicklistDetail/components/OrderShipmentCommentsList';
import { useMediaQuery } from 'react-responsive';
import UpdateOrderExtraFormModal from './components/UpdateOrderExtraFormModal';
import { getOrdersList, updateOrderExtra } from '@/services/foodstore-one/Magento/order';
import useFileDownloadAndPrintApi from '@/hooks/BasicData/useFileDownloadAndPrintApi';
import UpdateAddressesModal from './components/UpdateAddressesModal';
import SEbayOrderNo from './components/SEbayOrderNo';
import StockStableQtyByLocationModal from './components/StockStableQtyByLocationModal';
import EanFilesComp from '@/components/EanFilesComp';
import useSearchEanByScan from '@/hooks/BasicData/useSearchEanByScan';
import { setStockStableBookedQtyPacked } from '@/services/foodstore-one/Stock/stock-stable-booked';
import SNumpad from '@/components/Numpad';
import QtyPackedSettingModal from './components/QtyPackedSettingModal';
import UpdateOrderExtraWeightFormModal from './components/UpdateOrderExtraWeightFormModal';
import { createOrderUserActionLog } from '@/services/foodstore-one/Magento/order-user-action-log';

export const isBookReturned = (order?: API.Order) => {
  const returnQtys = order?.mag_order_items?.reduce((prev, x) => prev + sn(x.detail?.qty), 0);
  return sn(order?.total_qty_ordered) == returnQtys;
};

export const isBookReturnedByItem = (orderItem?: API.OrderItem) => {
  return sn(orderItem?.qty_ordered) == sn(orderItem?.detail?.qty);
};

type SearchFormValueType = Partial<API.Order>;
type Note1FormValueType = Partial<API.OrderExtra>;

const OrderDetailPacking: React.FC<IRouteComponentProps> = (props) => {
  const location: any = useLocation();
  const { initialState } = useModel('@@initialState');

  const isTabletOrMobile = useMediaQuery({ query: '(max-width: 1224px)' });

  const { getDictByCode, getParcelUrl } = useModel('app-settings');
  const { downloadB64AndCallDirectPrintApi, callDirectPrintApi } = useFileDownloadAndPrintApi();

  const searchFormRef = useRef<ProFormInstance>();
  const note1FormRef = useRef<ProFormInstance<Note1FormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingP, setLoadingP] = useState<boolean>(false);

  // order
  const [order, setOrder] = useState<API.Order>();

  // stock qty modal
  /* const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [currentOrderItemRow, setCurrentOrderItemRow] = useState<API.OrderItem>();  */
  const [openUpdateOrderExtraModal, setOpenUpdateOrderExtraModal] = useState<boolean>(false);

  // comments modal
  const [openCommentsModal, setOpenCommentsModal] = useState<boolean>(false);

  const [warningsDefCount, setWarningsDefCount] = useState<number>(0);

  const [openUpdateAddressModal, setOpenUpdateAddressModal] = useState<boolean>(false);

  const [confirmUser, setConfirmUser] = useState<string>('');

  // automatic printing
  const [allowAutoPrint, setAllowAutoPrint] = useState<boolean>(false);

  useEffect(() => {
    setConfirmUser(localStorage.getItem(`${LS_PREFIX}order_packing_confirm_user`) ?? '');
    setAllowAutoPrint(!!localStorage.getItem(`${LS_PREFIX}allow_auto_print`));
  }, []);

  useEffect(() => {
    localStorage.setItem(`${LS_PREFIX}order_packing_confirm_user`, confirmUser || '');
  }, [confirmUser]);

  useEffect(() => {
    localStorage.setItem(`${LS_PREFIX}allow_auto_print`, allowAutoPrint ? '1' : '');
  }, [allowAutoPrint]);

  // Numpad
  const numpadFieldRef = useRef<HTMLInputElement>();

  // setting modal for qty_packed
  const [openQtyPackedSettingModal, setOpenQtyPackedSettingModal] = useState<boolean>(false);
  const [openWeightConfirmModal, setOpenWeightConfirmModal] = useState<boolean>(false);

  const clearFormData = () => {
    setOrder(undefined);
    Util.setSfValues('sf_order_detail_packing', null);
    searchFormRef.current?.setFieldValue('entity_id', '');
    searchFormRef.current?.scrollToField('entity_id', { behavior: 'smooth' });
  };

  const loadOrderDetail = useCallback(async (params?: Record<string, any>) => {
    const searchFormValues = searchFormRef.current?.getFieldsValue();
    Util.setSfValues('sf_order_detail_packing', searchFormValues);
    if (searchFormValues.entity_id || searchFormValues.increment_id_suffix) {
      setLoading(true);

      return getOrdersList(
        {
          ...params,
          ...searchFormValues,
          with: 'itemEan.magInventoryStocksQty,itemEan.files,itemEan.stockStables,warnings,warnings_def,extra,ext,latestShipping,labels,orderDetail,emailStat,orderItem.stockStableBookedList,orderItem.picklistDetail,stockStablesBookedCount,packedCount,userActionLogsCount',
          limit: 1,
        },
        {},
        {},
      )
        .then((res) => {
          if (res) {
            const firstRow = res.data[0];
            let isAllBookedCheck = true;
            for (const x of res?.data ?? []) {
              for (const item of x?.mag_order_items ?? []) {
                // console.log(item?.picklist_detail?.is_stock_stable_updated == 1);
                isAllBookedCheck &&= item?.picklist_detail?.is_stock_stable_updated == 1;
              }
            }

            setOrder(firstRow);
            // return { success: true, data: firstRow?.mag_order_items, total: firstRow?.mag_order_items || 0 };
            return firstRow;
          }
          return null;
        })
        .catch((err) => {
          Util.error(err);
          return null;
        })
        .finally(() => setLoading(false));
    } else {
      setOrder(undefined);
      return null;
    }
  }, []);

  useEffect(() => {
    loadOrderDetail();
  }, [loadOrderDetail]);

  useEffect(() => {
    if (location.query.entity_id) {
      searchFormRef.current?.setFieldValue('entity_id', location.query.entity_id);
      loadOrderDetail();
    }
  }, [loadOrderDetail, location.query]);

  useEffect(() => {
    if (order?.entity_id) {
      createOrderUserActionLog({
        type: OrderUserActionLogType.OrderDetailWH,
        note: `Search Order #${order?.entity_id}`,
        order_id: order?.entity_id,
      });
    }
  }, [order?.entity_id]);

  useEffect(() => {
    if (order?.warnings_def) {
      setWarningsDefCount(1);
    } else {
      setWarningsDefCount(0);
    }
  }, [order?.warnings_def]);

  // Stock stable row
  const [currentSSRow, setCurrentSSRow] = useState<API.StockStable>();
  // Stock detail modal on location
  const [openStockStableQtyByLocationModal, setOpenStockStableQtyByLocationModal] = useState<boolean>(false);

  // Indicates items have been booked in order, otherwise WH worked cannot work on this page.
  const isFullBooked = order && order?.stock_stables_booked_count == order?.total_qty_ordered;

  const {
    itemEan: itemEanScanned,
    setItemEan: setItemEanScanned,
    fieldRef: itemEanScannedFieldRef,
    formElements: formElementsScanned,
  } = useSearchEanByScan({
    name: 'ean_scanned',
    style: { marginTop: 8 },
    placeholder: 'Scan EAN',
    with: 'siblingsMulti',
  });

  const smColumns: ProColumns<Partial<API.StockStable | API.StockStableBooked>>[] = useMemo(
    () => [
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'name'],
        editable: false,
        width: 40,
        align: 'center',
        render: (dom, entity) => {
          return (
            <span
              className="cursor-pointer c-blue"
              onClick={() => {
                // Check if stock_stable or stock_stable_booked?
                setCurrentSSRow(
                  (entity as any)?.stock_stable_id ? { ...entity, id: (entity as any)?.stock_stable_id } : entity,
                );
                setOpenStockStableQtyByLocationModal(true);
              }}
            >
              {entity?.warehouse_location?.name}
            </span>
          );
        },
      },
      {
        title: 'Priority',
        dataIndex: ['warehouse_location', 'priority'],
        editable: false,
        width: 60,
        align: 'right',
        render: (dom, record) => {
          return ni(record.warehouse_location?.priority);
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 50,
        align: 'center',
        editable: false,
        render: (dom, record) => {
          return <ExpDate date={record.exp_date} />;
        },
      },
      {
        title: 'Qty',
        dataIndex: ['qty_packed'],
        width: 55,
        align: 'center',
        tooltip: 'Click to Edit',
        editable: false,
        render: (__, record) => {
          const qtyBooked = sn(record?.case_qty) == 1 ? record?.piece_qty : record?.box_qty;
          const cls =
            qtyBooked && qtyBooked == (record as API.StockStableBooked).qty_packed
              ? 'c-green bold text-md'
              : 'c-red bold text-md';
          return (
            <div>
              <span className={cls}>{ni((record as API.StockStableBooked)?.qty_packed, true)}</span> /{' '}
              <span>{ni(qtyBooked, true)}</span>
            </div>
          );
        },
        onCell: (record) => {
          return {
            onClick: () => {
              if (isFullBooked) {
                setCurrentSSRow(record);
                setOpenQtyPackedSettingModal(true);
              }
            },
            className: isFullBooked ? 'cursor-pointer' : 'initial',
          };
        },
      },
      {
        title: 'Qty (pcs)',
        dataIndex: ['total_piece_qty'],
        width: 60,
        align: 'center',
        editable: false,
        render: (__, record) => {
          return ni(record?.total_piece_qty);
        },
        responsive: ['xl'],
      },
      {
        title: '',
        dataIndex: ['actions'],
        width: 100,
        editable: false,
        render: (dom, record) => {
          return (
            <Space size={24}>
              <Button
                type="primary"
                icon={<CheckOutlined />}
                className="btn-green"
                disabled={
                  !isFullBooked ||
                  (!!(record as API.StockStableBooked).qty_packed &&
                    (record.case_qty == 1 ? record.piece_qty : record.box_qty) ==
                      (record as API.StockStableBooked).qty_packed)
                }
                onClick={() => {
                  const hide = message.loading(`Setting packed qty ...`);
                  setStockStableBookedQtyPacked(
                    (record as API.StockStableBooked).order_item_id as number,
                    (record.case_qty == 1 ? record.piece_qty : record.box_qty) as number,
                    { id: (record as API.StockStableBooked).id },
                  )
                    .then((res) => {
                      message.success('Set packed qty successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                    });
                }}
              />
              <Button
                type="primary"
                danger
                disabled
                onClick={() => {
                  //
                }}
              >
                Problem
              </Button>
            </Space>
          );
        },
      },
    ],
    [getDictByCode],
  );

  // Order Item columns
  const columns: ProColumns<API.OrderItem>[] = useMemo(
    () => [
      {
        title: 'Image',
        dataIndex: ['item_ean', 'files'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 80,
        render: (__, record) => <EanFilesComp files={record.item_ean?.files} width={80} />,
      },
      isTabletOrMobile
        ? {
            title: 'SKU',
            dataIndex: 'sku_mobile',
            width: 200,
            copyable: true,
            render: (__, entity) => {
              const isUserOrAdmin =
                initialState?.currentUser?.role == UserRole.USER || initialState?.currentUser?.role == UserRole.ADMIN;

              return (
                <Space size={8} direction="vertical">
                  {isUserOrAdmin ? (
                    <Typography.Link
                      href={`/item/ean-all-summary?sku=${skuToItemId(entity.sku)}_`}
                      target="_blank"
                      copyable
                    >
                      {entity.sku}
                    </Typography.Link>
                  ) : (
                    <Typography.Text copyable>{entity.sku}</Typography.Text>
                  )}
                  <Typography.Text copyable>{entity.item_ean?.ean}</Typography.Text>
                  <Typography.Text>{entity.name}</Typography.Text>
                </Space>
              );
            },
            responsive: ['sm'],
          }
        : {
            title: 'SKU',
            dataIndex: 'sku',
            width: 100,
            copyable: true,
            render: (__, entity) => {
              const isUserOrAdmin =
                initialState?.currentUser?.role == UserRole.USER || initialState?.currentUser?.role == UserRole.ADMIN;

              return isUserOrAdmin ? (
                <Typography.Link
                  href={`/item/ean-all-summary?sku=${skuToItemId(entity.sku)}_`}
                  target="_blank"
                  copyable
                >
                  {entity.sku}
                </Typography.Link>
              ) : (
                <Typography.Text copyable>{entity.sku}</Typography.Text>
              );
            },
            responsive: ['xl'],
          },
      {
        title: 'EAN',
        dataIndex: ['item_ean', 'ean'],
        width: 140,
        copyable: true,
        responsive: ['xl'],
      },
      { title: 'Name', dataIndex: 'name', width: 200, responsive: ['xl'] },
      { title: 'Ordered Qty', dataIndex: 'qty_ordered', width: 60, align: 'center' },
      {
        title: 'Stocks',
        dataIndex: 'stock_stables',
        align: 'center',
        width: 500,
        render: (__, orderItem) => {
          const stocksListDataSource = orderItem.picklist_detail?.is_stock_stable_updated
            ? orderItem?.stock_stable_booked_list ?? []
            : orderItem?.item_ean?.stock_stables ?? [];

          if (stocksListDataSource.length) {
            const eanObj = orderItem.item_ean;
            stocksListDataSource.forEach((x) => {
              x.item_ean = {
                id: eanObj?.id,
                item_id: eanObj?.item_id,
                parent_id: eanObj?.parent_id,
                is_single: eanObj?.is_single,
                ean: eanObj?.ean,
                sku: eanObj?.sku,
                ean_text_de: eanObj?.ean_text_de,
                attr_case_qty: eanObj?.attr_case_qty,
                item_base: eanObj?.item_base,
                item_base_unit: eanObj?.item_base_unit,
                files: eanObj?.files,
              };

              x.order_item = {
                item_id: orderItem.item_id,
                order_id: orderItem.order_id,
                qty_ordered: orderItem.qty_ordered,
                sku: orderItem.sku,
              };
            });
          }

          if (!stocksListDataSource || !stocksListDataSource?.length) return <></>;
          return (
            <ProTable
              columns={smColumns}
              cardProps={{ bodyStyle: { padding: 0 } }}
              rowKey="id"
              headerTitle={false}
              search={false}
              options={false}
              pagination={false}
              dataSource={stocksListDataSource}
              columnEmptyText={''}
              locale={{ emptyText: <></> }}
              size="middle"
              onRow={(record: API.StockStableBooked) => {
                let cls = 'reset-tds-bg',
                  title = '';
                const qty = record.case_qty == 1 ? record.piece_qty : record.box_qty;
                if (qty) {
                  if (record.qty_packed == qty) {
                    cls += ' bg-light-green';
                  } else if (record.qty_packed == 0) {
                    cls += ' bg-light-red';
                  } else {
                    cls += ' bg-lightyellow';
                  }
                }

                /* const warning_def = (record as API.Order).warnings_def || '';
                  if (warning_def) {
                    cls += ' reset-tds-bg bg-red';
                    title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
                  } */
                cls += ' ' + (record.item_ean?.is_single ? 'row-single' : 'row-multi');

                return { title: title, className: cls };
              }}
            />
          );
        },
      },
    ],
    [smColumns, isTabletOrMobile],
  );

  /**
   * Direct Printing label & delivery notes
   */
  const directPrintLabelAndDeliveryNote = async (order?: API.Order) => {
    if (!order) {
      return;
    }
    if (!order?.labels?.length) {
      message.info('No created label yet.');
    } else {
      setLoadingP(true);
      const hide2 = message.loading('Direct Printing last labels...', 0);
      const lastLabel = order.labels[0];
      const maxPos = sn(lastLabel.pos);
      for (let i = 0; i < maxPos; i++) {
        if (order.labels[i]) {
          await downloadB64AndCallDirectPrintApi(order.labels[i], 'printerApi', {
            logType: OrderUserActionLogType.OrderDetailWH,
          }).catch(Util.error);
        }
      }
      hide2();
      setLoadingP(false);
    }

    setLoadingP(true);
    const hide = message.loading('Direct Printing delivery note...', 0);
    await exportPicklistDeliveryNoteByEntityId(order?.entity_id, { includeB64: true })
      .then(async (res) => {
        hide();
        if (res.length) {
          for (const file of res) {
            if (file.url) {
              // window.open(`${API_URL}/api/${file.url}`, '_blank');
            }
            if (file.b64) {
              await callDirectPrintApi({ service_name: 'Delivery Note', entity_id: order?.entity_id }, file.b64, {
                logType: OrderUserActionLogType.OrderDetailWH,
              });
            }
          }
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoadingP(false);
      });
  };

  useEffect(() => {
    /* note1_status: order?.extra?.note1_status || '',
                note1: order?.extra?.note1 || '', */
    note1FormRef.current?.setFieldsValue({
      note1_status: order?.extra?.note1_status || '',
      note1: order?.extra?.note1 || '',
    });
  }, [order]);

  const orderItemsRef = useRef<API.OrderItem[]>([]);
  const sku2OrderItemRef = useRef({});

  useEffect(() => {
    if (order?.mag_order_items?.length) {
      sku2OrderItemRef.current = order.mag_order_items.reduce((prev, x) => {
        const newData = { ...prev, [`${x.sku}`]: x.item_id };
        return newData;
      }, {});
    } else {
      sku2OrderItemRef.current = {};
    }

    orderItemsRef.current = order?.mag_order_items || [];
  }, [order?.mag_order_items]);

  /**
   * Getting selected orderItemId
   */
  useEffect(() => {
    if (itemEanScanned?.sku) {
      const orderItemId = (sku2OrderItemRef.current as any)[itemEanScanned.sku];

      if (orderItemId) {
        const hide = message.loading(`Incrementing packed qty for ${itemEanScanned.sku} ...`);
        /* incStockStableBookedPackedQty(orderItemId, 1)
          .then((res) => {
            message.success('Incremented packed qty successfully.');
            actionRef.current?.reload();
            // todo below code does not render table
            /* setOrder((prev) => {
              const newOrder = { ...prev };
              if (newOrder && newOrder.mag_order_items?.length) {
                const foundItem = newOrder?.mag_order_items?.find((x) => x.sku == itemEanScanned.sku);
                if (foundItem) {
                  foundItem.stock_stable_booked_list = res;
                }
              }

              return newOrder;
            }); * /
          })
          .catch(Util.error)
          .finally(() => {
            hide();
          }); */
        const qty = sn(numpadFieldRef.current?.value);
        setStockStableBookedQtyPacked(orderItemId, qty ? qty : 1)
          .then((res) => {
            message.success('Incremented packed qty successfully.');
            actionRef.current?.reload();
          })
          .catch(Util.error)
          .finally(() => {
            hide();
          });
      } else {
        // Cannot find order item by SKU and scanned EAN is multi?
        if (!itemEanScanned.is_single) {
          const parentSku = itemEanScanned.parent?.sku || '';
          const oiId = (sku2OrderItemRef.current as any)[parentSku];
          if (oiId) {
            const oi = orderItemsRef.current.find((x) => x.item_id == oiId);
            if (oi) {
              notification.info({
                message: `${oi.qty_ordered} pcs of ${itemEanScanned.sku} / ${itemEanScanned.parent?.ean}`,
                duration: 0,
                placement: 'top',
              });
            }
          } else {
            notification.info({
              message: `EAN ${itemEanScanned.ean} belongs to ${itemEanScanned.sku} / ${itemEanScanned.ean_text_de?.name}`,
              duration: 0,
              placement: 'top',
            });
          }
        }
      }

      // erase a filed of Scanned EAN
      if (itemEanScannedFieldRef.current) {
        itemEanScannedFieldRef.current.value = '';
      }

      // Erase qty field
      if (numpadFieldRef.current) {
        numpadFieldRef.current.value = '';
      }

      setItemEanScanned(null);
    }
  }, [itemEanScanned?.sku]);

  return (
    <div className={styles.orderDetailContainer} style={{ marginTop: -24 }}>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingTop: 16 }} bordered={false}>
        <>
          <Row gutter={24}>
            <Col flex="420px">
              <div className="ant-page-header-heading-title">{props.route.name}</div>
              <Row gutter={16} wrap={false} style={{ marginTop: 24 }}>
                <Col>
                  <ProForm<SearchFormValueType>
                    layout="inline"
                    size="large"
                    formRef={searchFormRef}
                    isKeyPressSubmit
                    className="search-form"
                    initialValues={Util.getSfValues('sf_order_detail_packing', {}, {})}
                    submitter={{
                      submitButtonProps: { htmlType: 'submit', disabled: loading },
                      searchConfig: { submitText: 'Search' },
                      onSubmit: () => {
                        loadOrderDetail().then((res) => {
                          if (allowAutoPrint && res) {
                            console.log('--->', res);
                            directPrintLabelAndDeliveryNote(res);
                          }
                          itemEanScannedFieldRef.current?.scrollTo({ behavior: 'smooth' });
                          itemEanScannedFieldRef.current?.focus();
                        });
                      },
                      render: (form, dom) => {
                        return [dom[1]];
                      },
                    }}
                  >
                    <ProFormText
                      name={'entity_id'}
                      label="Order ID"
                      width="xs"
                      placeholder={'Order ID'}
                      disabled={loading}
                    />
                  </ProForm>
                </Col>
                <Col>
                  <Switch
                    title="Allow automatic printing label & delivery note"
                    checked={allowAutoPrint}
                    onChange={(checked) => setAllowAutoPrint(checked)}
                    style={{ marginTop: 8 }}
                  />
                </Col>
              </Row>
              <Row wrap={false} style={{ marginTop: 16 }}>
                <Col flex="150px">
                  {/* <h3 className="booking-cnt">Booked: {ni(order?.stock_stables_booked_count, true)}</h3> */}
                  <h3 className="booking-cnt" style={{ marginBottom: 0 }}>
                    {ni(order?.packed_count, true)} / {ni(order?.total_qty_ordered, true)}
                  </h3>
                  {order ? (
                    <Space size={0} direction="vertical" style={{ lineHeight: 1 }}>
                      <div>
                        <span>Delivery Note Printed: </span>
                        <span style={{ fontSize: 16, fontWeight: 'bold' }}>{`${
                          order?.order_user_action_log_count_delivery_note_printed ?? 0
                        }`}</span>
                      </div>
                      <div>
                        <span> Label Printed: </span>
                        <span style={{ fontSize: 16, fontWeight: 'bold' }}>{`${
                          order?.order_user_action_log_count_label_printed ?? 0
                        }`}</span>
                      </div>
                    </Space>
                  ) : null}
                </Col>
                <Col flex="auto" className="shipping-warning">
                  {!!warningsDefCount && (
                    <Alert
                      key="warn-def"
                      message={order?.warnings_def?.split(',').map((x, ind) => (
                        <div key={ind}>{x}</div>
                      ))}
                      type="error"
                      style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
                    />
                  )}
                </Col>
              </Row>
            </Col>
            <Col flex="400px">
              <ProForm<Note1FormValueType>
                layout="vertical"
                formRef={note1FormRef}
                isKeyPressSubmit
                size="small"
                className="note1-form"
                initialValues={{
                  note1_status: order?.extra?.note1_status || '',
                  note1: order?.extra?.note1 || '',
                }}
                submitter={false}
              >
                <ProFormTextArea
                  name={'note1'}
                  label={
                    <Row justify={'space-between'} align={'middle'} style={{ width: '100%' }}>
                      <Col>Note</Col>
                      <Col>
                        <Button
                          type="link"
                          size="large"
                          icon={<SaveOutlined />}
                          style={{ marginLeft: 32 }}
                          onClick={() => {
                            if (order?.entity_id) {
                              const hide = message.loading('Updating...', 0);
                              updateOrderExtra(order?.entity_id, note1FormRef.current?.getFieldsValue() ?? {})
                                .then((res) => {
                                  message.success('Updated successfully.');
                                  setOrder((prev) => ({ ...prev, extra: res.extra }));
                                })
                                .catch(Util.error)
                                .finally(hide);
                            }
                          }}
                        />
                      </Col>
                    </Row>
                  }
                  placeholder={'Note'}
                  fieldProps={{ rows: 4 }}
                />
              </ProForm>

              <ProDescriptions<API.Order>
                column={{ sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
                dataSource={{ ...order }}
                labelStyle={{ width: 80, padding: 4 }}
                style={{ width: '100%', paddingBottom: 16 }}
                contentStyle={{ padding: 4 }}
                bordered
                colon={false}
                size="middle"
              >
                <ProDescriptions.Item
                  title="Parcel"
                  dataIndex="shipping_imported_list"
                  tooltip="Green indicates a processed shipping on Magento"
                  render={(dom, record) => {
                    return record.latest_shipping
                      ? [record.latest_shipping]?.map((x) => {
                          let cls = 'text-sm';
                          if (x.mag_ship_id) {
                            cls += ' c-green';
                          }
                          const title = x.carrier_code || x.title ? `${x.title} | ${x.carrier_code}` : '';
                          return (
                            <a
                              key={x.id}
                              className={cls}
                              title={title}
                              style={{ display: 'inline-block' }}
                              href={`${getDictByCode(DictCode.MAG_ADMIN_URL_TRACKING)}${x.parcel_no}`}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {x.parcel_no}
                            </a>
                          );
                        })
                      : null;
                  }}
                />
                <ProDescriptions.Item
                  title="Weight"
                  dataIndex="weight"
                  tooltip="Weight in Magento order"
                  contentStyle={{ textAlign: 'center' }}
                  render={(dom, record) => {
                    return `${nf3(record.weight, true, true)}kg`;
                  }}
                />
              </ProDescriptions>
            </Col>
            <Col flex="250px">
              <ProFormText
                label="User"
                name="confirm_user"
                fieldProps={{
                  value: confirmUser,
                  onChange: (e) => {
                    setConfirmUser(e.target.value);
                  },
                }}
              />
            </Col>
            <Col flex="auto">
              <Space direction="vertical" size={16} style={{ width: '100%' }}>
                <Space size={24}>
                  {order && (
                    <>
                      <Typography.Link
                        copyable={{
                          text: `${order?.entity_id || ''}`,
                          tooltips: 'Copy Order No ' + (order?.entity_id || ''),
                        }}
                        style={{ display: 'inline-block', marginBottom: 0 }}
                        href={`/monitor/order-user-action-log?order_id=${order?.entity_id || ''}`}
                        target="_blank"
                        title="View logs in the new tab"
                      >
                        {order?.entity_id || ''}
                      </Typography.Link>

                      <Typography.Link
                        style={{ display: 'inline-block', marginBottom: 0 }}
                        href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${
                          order?.entity_id
                        }/`}
                        target="_blank"
                        copyable
                      >
                        {order.increment_id}
                      </Typography.Link>
                      <div style={{ display: 'inline-block', marginBottom: 0 }}>{Util.dtToDMY(order?.created_at)}</div>
                      <Tag color="green" style={{ marginLeft: 16, marginRight: 16 }} title="Shipping provider">
                        {order?.extra?.shipping_provider_name ?? 'N/A'}
                      </Tag>
                      {order.increment_id?.startsWith('EB') || order.increment_id?.startsWith('KL') ? (
                        <SEbayOrderNo order={order} />
                      ) : null}
                      {sn(order?.mag_order_shipment_comments_count) > 0 ? (
                        <Badge size="small" offset={[6, -2]} count={sn(order?.mag_order_shipment_comments_count)}>
                          <CommentOutlined
                            style={{ color: 'red' }}
                            className="cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              setOpenCommentsModal(true);
                            }}
                          />{' '}
                        </Badge>
                      ) : (
                        <></>
                      )}
                    </>
                  )}
                </Space>
                <Spin spinning={loading}>
                  <ProDescriptions<API.Order>
                    column={{ sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
                    dataSource={order}
                    labelStyle={{ width: 140, padding: 8 }}
                    contentStyle={{ padding: 4 }}
                    bordered
                    colon={false}
                    size="middle"
                  >
                    <ProDescriptions.Item
                      title="Name"
                      dataIndex="sa_fullname"
                      render={(dom, record) => {
                        return (
                          <Typography.Text mark={record?.warn_sa_fullname || record?.warn_sa_fullname_wrong}>
                            {record.sa_fullname}
                          </Typography.Text>
                        );
                      }}
                    />
                    <ProDescriptions.Item
                      title={'Status'}
                      dataIndex="status"
                      render={(dom, record) => {
                        const status = record.status;
                        let color = 'default';
                        switch (status ?? '') {
                          case 'complete':
                          case 'closed':
                            color = 'success';
                            break;
                          case 'processing':
                            color = 'blue';
                            break;
                          case 'pending':
                            color = 'orange';
                            break;
                          case 'canceled':
                            color = 'red';
                            break;
                        }

                        return <Tag color={color as any}>{MagentoOrderStatusOptions[status || '-'] ?? '-'}</Tag>;
                      }}
                    />
                    <ProDescriptions.Item
                      title="Delivery Address"
                      dataIndex="sa_full"
                      tooltip="Orange colored rows are in warnings list. Highlighted parts may be wrong!"
                      span={2}
                      render={(dom, record) => {
                        return (
                          <Row>
                            <Col flex="auto">
                              <FullAddress order={record} type="shipping" />
                            </Col>
                            <Col flex="40px">
                              <Button
                                type="link"
                                size="small"
                                icon={isOrderAddressEditable(record.status) ? <EditOutlined /> : <EyeOutlined />}
                                style={{
                                  display: 'block',
                                  position: 'absolute',
                                  top: 0,
                                  right: 12,
                                  color: isOrderAddressEditable(record.status) ? 'auto' : 'grey',
                                }}
                                title={isOrderAddressEditable(record.status) ? 'Update addresses' : 'View addresses'}
                                onClick={() => {
                                  setOpenUpdateAddressModal(true);
                                }}
                              />
                            </Col>
                          </Row>
                        );
                      }}
                    />
                  </ProDescriptions>

                  <ProDescriptions<API.Order>
                    column={{ sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
                    dataSource={{ ...order }}
                    labelStyle={{ width: 140, padding: 8 }}
                    contentStyle={{ padding: 4 }}
                    bordered
                    colon={false}
                    style={{ marginTop: -1 }}
                    size="small"
                  >
                    <ProDescriptions.Item
                      title="Mails"
                      dataIndex="emails_count"
                      contentStyle={{ textAlign: 'center', fontSize: 14 }}
                      render={(dom, r) => {
                        return (
                          <a href={`/email/list?order_id=${r?.entity_id}`} target="_blank" rel="noreferrer">
                            {ni(order?.emails_count)}
                          </a>
                        );
                      }}
                      style={{ border: order?.emails_count ? '1px solid red' : 'initial' }}
                    />
                    <ProDescriptions.Item
                      title="Cases"
                      dataIndex="crm_cases_count"
                      contentStyle={{ textAlign: 'center', fontSize: 14 }}
                      style={{ border: order?.crm_cases_count ? '1px solid red' : 'initial' }}
                      render={(dom, r) => {
                        return (
                          <a href={`/email/list?order_id=${r?.entity_id}`} target="_blank" rel="noreferrer">
                            {ni(r?.crm_cases_count)}
                          </a>
                        );
                      }}
                    />
                  </ProDescriptions>
                </Spin>
                <Space size={8}>
                  <Button
                    type="primary"
                    icon={<PrinterOutlined />}
                    title="Download delivery notes in PDF"
                    disabled={loadingP || !order}
                    onClick={async () => {
                      directPrintLabelAndDeliveryNote(order);
                    }}
                  >
                    Print Label & Delivery note
                  </Button>
                  <Button
                    type="primary"
                    icon={<PrinterOutlined />}
                    title="Download delivery notes in PDF"
                    disabled={loadingP || !order}
                    onClick={() => {
                      const hide = message.loading('Direct Printing delivery note...', 0);
                      setLoadingP(true);
                      exportPicklistDeliveryNoteByEntityId(order?.entity_id, { includeB64: true })
                        .then(async (res) => {
                          hide();
                          if (res.length) {
                            for (const file of res) {
                              if (file.url) {
                                // window.open(`${API_URL}/api/${file.url}`, '_blank');
                              }
                              if (file.b64) {
                                await callDirectPrintApi(
                                  { service_name: 'Delivery Note', entity_id: order?.entity_id },
                                  file.b64,
                                  { logType: OrderUserActionLogType.OrderDetailWH },
                                );
                              }
                            }
                          }
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                          setLoadingP(false);
                        });
                    }}
                  >
                    Print Delivery note
                  </Button>
                  <Button
                    type="primary"
                    icon={<PrinterOutlined />}
                    title="Download delivery notes in PDF"
                    disabled={loadingP || !order || !order?.labels?.length}
                    onClick={async () => {
                      if (!order?.labels?.length) return Promise.reject();

                      const hide = message.loading('Direct Printing last labels...', 0);
                      setLoadingP(true);
                      const lastLabel = order.labels[0];
                      const maxPos = sn(lastLabel.pos);
                      for (let i = 0; i < maxPos; i++) {
                        if (order.labels[i]) {
                          await downloadB64AndCallDirectPrintApi(order.labels[i], 'printerApi', {
                            logType: OrderUserActionLogType.OrderDetailWH,
                          });
                        }
                      }
                      setLoadingP(false);
                      hide();
                    }}
                  >
                    Print Last Created Label
                  </Button>
                </Space>
              </Space>
            </Col>
          </Row>
        </>
      </Card>

      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingTop: 1 }} bordered={false}>
        <Row wrap={false} gutter={24}>
          <Col flex="auto">
            <ProTable<API.OrderItem, API.PageParams>
              actionRef={actionRef}
              rowKey="entity_id"
              headerTitle="Items List"
              revalidateOnFocus={false}
              options={{ fullScreen: true, density: false, setting: false, reload: false }}
              search={false}
              sticky
              bordered={true}
              size="middle"
              scroll={{ x: 600 }}
              cardProps={{ bodyStyle: { padding: 0 } }}
              onRequestError={Util.error}
              dataSource={[...(order?.mag_order_items ?? [])]}
              request={(params, sort, filter) => {
                return loadOrderDetail(params).then((res) => ({
                  success: true,
                  data: res?.mag_order_items || [],
                  total: res?.mag_order_items?.length || 0,
                }));
              }}
              pagination={{ defaultPageSize: 500 }}
              columns={columns}
              toolBarRender={(action, rows) => [
                <Button
                  type="primary"
                  key="export-delivery-note"
                  size="small"
                  icon={<FilePdfOutlined />}
                  title="Download delivery notes in PDF"
                  style={{ marginRight: 24 }}
                  disabled={loadingP || !order}
                  onClick={() => {
                    const hide = message.loading('Downloading delivery note as PDF format...', 0);
                    setLoadingP(true);
                    exportPicklistDeliveryNoteByEntityId(order?.entity_id)
                      .then((res) => {
                        hide();
                        if (res.length) {
                          res.forEach((file) => {
                            if (file.url) {
                              window.open(`${API_URL}/api/${file.url}`, '_blank');
                            }
                          });
                        }
                      })
                      .catch(Util.error)
                      .finally(() => {
                        hide();
                        setLoadingP(false);
                      });
                  }}
                >
                  Delivery note
                </Button>,

                <Popover
                  key="re-print"
                  trigger={['click']}
                  content={order?.labels?.map((x) => (
                    <Row key={x.id} gutter={12} style={{ width: 320, alignItems: 'center' }}>
                      <Col className="text-sm" span={4}>
                        <Typography.Text ellipsis>{x.service_name}</Typography.Text>
                      </Col>
                      <Col className="text-sm" span={14}>
                        <Typography.Link
                          copyable
                          href={getParcelUrl(x.parcel_no)}
                          target="_blank"
                          // style={{ textDecoration: x.ref_no?.endsWith(',Return') ? 'line-through' : '' }}
                        >
                          {x.track_id}
                        </Typography.Link>
                      </Col>
                      <Col span={1} className="text-sm c-red">
                        <span title="Return Label">{x.ref_no?.endsWith(',Return') ? 'R' : ''}</span>
                      </Col>
                      <Col span={5}>
                        <Space size={3}>
                          <Typography.Link href={`${API_URL}/api/${x.url}`} title="Open Label PDF" target="_blank">
                            <LinkOutlined />
                          </Typography.Link>
                          <Button
                            type="link"
                            size="small"
                            icon={<PrinterOutlined />}
                            title="Print Label"
                            style={{ height: 20 }}
                            onClick={() => {
                              downloadB64AndCallDirectPrintApi(x, 'browserPrinter', {
                                logType: OrderUserActionLogType.OrderDetailWH,
                              });
                            }}
                          />
                          <Button
                            type="link"
                            size="small"
                            icon={<PrinterOutlined style={{ color: 'green' }} />}
                            title="Print label directly"
                            style={{ height: 20 }}
                            onClick={() => {
                              downloadB64AndCallDirectPrintApi(x, 'printerApi', {
                                logType: OrderUserActionLogType.OrderDetailWH,
                              });
                            }}
                          />
                        </Space>
                      </Col>
                    </Row>
                  ))}
                >
                  <Button
                    type="primary"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Re-print Labels"
                    disabled={!order?.labels?.length || !order}
                  >
                    {`Print existing label ${order?.labels?.length ? ` (${order?.labels?.length})` : ''}`}
                  </Button>
                </Popover>,

                <div key="hidden-ele-wrap">
                  {/* <Button
                  type="primary"
                  key="export-labels-GLS"
                  size="small"
                  icon={<PrinterOutlined />}
                  title="Print GLS Labels in PDF"
                  disabled={loadingP || !order}
                  onClick={() => {
                    handlePrintLabel('GLS');
                  }}
                >
                  GLS
                </Button>,

                <Popconfirm
                  key="export-labels-GLS2"
                  title={<>Are you sure to create a Guaranteed NextDay label at extra costs? </>}
                  okText="Yes"
                  cancelText="No"
                  overlayStyle={{ maxWidth: 300 }}
                  onConfirm={() => {
                    handlePrintLabel('GLS', { guaranteed24Service: true });
                  }}
                >
                  <Button
                    type="default"
                    key="export-labels-GLS2"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print GLS2  Labels in PDF"
                    disabled={loadingP || !order}
                  >
                    GLS 24H
                  </Button>
                </Popconfirm>,

                <Button
                  type="primary"
                  key="export-labels-DHL"
                  size="small"
                  icon={<PrinterOutlined />}
                  title="Print DHL Labels in PDF"
                  disabled={loadingP || !order}
                  onClick={() => {
                    handlePrintLabel('DHL');
                  }}
                >
                  DHL
                </Button>,
                <Button
                  type="primary"
                  key="export-labels-DPD"
                  size="small"
                  icon={<PrinterOutlined />}
                  title="Print DPD Labels in PDF"
                  disabled={loadingP || !order}
                  onClick={() => {
                    handlePrintLabel('DPD');
                  }}
                >
                  DPD
                </Button>, */}
                </div>,
              ]}
              onRow={(record) => {
                let cls = '',
                  title = '';
                /* const warning_def = (record as API.Order).warnings_def || '';
                  if (warning_def) {
                    cls += ' reset-tds-bg bg-red';
                    title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
                  } */
                cls += ' ' + (record.item_ean?.is_single ? 'row-single' : 'row-multi');

                return { title: title, className: cls };
              }}
              rowSelection={false}
              columnEmptyText=""
            />
          </Col>
          <Col flex="280px">
            <Affix style={{ minHeight: 400 }}>
              <div>
                <Row wrap={false} gutter={36} style={{ padding: '48px 0 0 0' }}>
                  <Col flex="280px">
                    <input
                      ref={numpadFieldRef as any}
                      className="ant-input ant-input-lg"
                      disabled={!order || !isFullBooked}
                      placeholder="Qty"
                    />
                    {formElementsScanned}
                    <div style={{ marginTop: 8 }}>
                      <SNumpad fieldRef={numpadFieldRef} />
                    </div>
                  </Col>
                  {/* <Col flex="auto">
                <Space direction="vertical" size={8}>
                  <Button
                    type="primary"
                    size="large"
                    disabled={!order || !isFullBooked}
                    onClick={() => {
                      const qty = sn(numpadFieldRef.current?.value);
                      if (!orderItemIdScanned) {
                        message.error('Please scan EAN!');
                        return;
                      }
                      if (qty < 0) {
                        message.error('Invalid Qty!');
                        numpadFieldRef.current?.focus();
                        return;
                      }

                      const hide = message.loading(`Setting packed qty ...`);
                      setStockStableBookedQtyPacked(orderItemIdScanned, qty)
                        .then((res) => {
                          message.success('Set packed qty successfully.');
                          actionRef.current?.reload();
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                        });
                    }}
                  >
                    OK
                  </Button>
                </Space>
              </Col> */}
                </Row>
                <Row wrap={false} gutter={12} style={{ padding: '8px 0 0px 0' }} justify={'space-between'}>
                  <Col>
                    <Button
                      type="primary"
                      className="btn-yellow"
                      disabled={!order}
                      onClick={() => {
                        if (!order?.entity_id) return;
                        const hide = message.loading('Make On Holding...', 0);
                        updateOrderExtra(order?.entity_id, { note1_status: 'On Hold' })
                          .then((res) => {
                            message.success('Updated order status successfully.');
                            clearFormData();

                            createOrderUserActionLog({
                              type: OrderUserActionLogType.OrderDetailWH,
                              note: OrderUserActionLogNote.OnHold,
                              order_id: order?.entity_id,
                            });
                          })
                          .catch(Util.error)
                          .finally(() => {
                            hide();
                          });
                      }}
                      size="large"
                    >
                      {order?.extra?.note1_status == MagOrderExtraStatus.OnHold ? 'Keep On Hold' : 'On Hold'}
                    </Button>
                  </Col>
                  <Col>
                    {confirmUser ? (
                      <Button
                        type="primary"
                        className="btn-green"
                        disabled={!order || !isFullBooked}
                        onClick={() => {
                          const tmpFinalConfirm = () => {
                            setOpenWeightConfirmModal(true);
                          };
                          if (order?.packed_count && order?.packed_count == order?.total_qty_ordered) {
                            tmpFinalConfirm();
                          } else {
                            Modal.confirm({
                              title:
                                'It seems items are missing. Are you sure you want to close and confirm the order?',
                              onOk: async () => {
                                tmpFinalConfirm();
                              },
                            });
                          }
                        }}
                        size="large"
                      >
                        Finish
                      </Button>
                    ) : (
                      <div>&nbsp;</div>
                    )}
                  </Col>
                </Row>
              </div>
            </Affix>
          </Col>
        </Row>
      </Card>

      <Modal
        title={`Order shipment comments`}
        width={600}
        open={openCommentsModal}
        onCancel={() => setOpenCommentsModal(false)}
        footer={false}
      >
        <OrderShipmentCommentsList orderId={sn(order?.entity_id)} />
      </Modal>

      <UpdateOrderExtraFormModal
        modalVisible={openUpdateOrderExtraModal}
        handleModalVisible={setOpenUpdateOrderExtraModal}
        order={{
          shipping_description: order?.shipping_description,
          weight: order?.weight,
          entity_id: order?.entity_id,
          shipping_imported_list: order?.shipping_imported_list,
          latest_shipping: order?.latest_shipping,
          sa_fullname: order?.sa_fullname,
          sa_full: order?.sa_full,
          sa_company: order?.sa_company,
        }}
        values={{ ...order?.extra }}
        onSubmit={async (values) => {
          actionRef.current?.reload();
        }}
      />

      <StockStableQtyByLocationModal
        modalVisible={openStockStableQtyByLocationModal}
        handleModalVisible={setOpenStockStableQtyByLocationModal}
        wl={currentSSRow?.warehouse_location}
        initialValues={currentSSRow}
        orderItem={currentSSRow?.order_item}
      />

      <UpdateAddressesModal
        modalVisible={openUpdateAddressModal}
        handleModalVisible={setOpenUpdateAddressModal}
        initialValues={order}
        onSubmit={async (value) => actionRef.current?.reload()}
      />

      <QtyPackedSettingModal
        modalVisible={openQtyPackedSettingModal}
        handleModalVisible={setOpenQtyPackedSettingModal}
        id={sn(currentSSRow?.id)}
        orderItemId={sn((currentSSRow as API.StockStableBooked)?.order_item_id)}
        maxQtyPacked={
          sn((currentSSRow as API.StockStableBooked)?.case_qty) == 1
            ? sn((currentSSRow as API.StockStableBooked)?.piece_qty)
            : sn((currentSSRow as API.StockStableBooked)?.box_qty)
        }
        onSubmit={async () => actionRef.current?.reload()}
        initialValues={currentSSRow as API.StockStableBooked}
      />

      <UpdateOrderExtraWeightFormModal
        order={order}
        modalVisible={openWeightConfirmModal}
        handleModalVisible={setOpenWeightConfirmModal}
        onSubmit={async (values) => {
          createOrderUserActionLog({
            type: OrderUserActionLogType.OrderDetailWH,
            note: OrderUserActionLogNote.Finish,
            order_id: order?.entity_id,
            detail: values,
          });
          clearFormData();
        }}
      />
    </div>
  );
};

export default OrderDetailPacking;
