import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { Spin } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import type { DragItem } from './ImageCard';
import { ImageCard } from './ImageCard';
import { updateImageSort, updateImageType } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';

export type ImagesCellProps = {
  itemEan?: Partial<API.Ean>;
};

const ImagesCell: React.FC<ImagesCellProps> = (props) => {
  const { itemEan } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const [files, setFiles] = useState<API.File[]>([]);

  useEffect(() => {
    setFiles(itemEan?.files || []);
  }, [itemEan?.files]);

  const moveFile = useCallback((dragIndex: number, hoverIndex: number) => {
    // console.log('index -->', dragIndex, hoverIndex);
    // console.log('id -->', files[dragIndex]?.id, files[hoverIndex]?.id);
    setFiles((prev: API.File[]) =>
      update(prev, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prev[dragIndex] as API.File],
        ],
      }),
    );
  }, []);

  const dropFileCallback = useCallback(
    async (item: DragItem) => {
      // console.log(' ==> dropped', item);
      setLoading(true);
      updateImageSort(itemEan?.id, +item.id, { index: item.index, orgIndex: item.orgIndex })
        .then((res) => {
          setFiles(res);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    },
    [itemEan?.id],
  );

  const updateTypeCallback = useCallback(
    async (file: API.File, afftectedType: string, isRemove: boolean) => {
      setLoading(true);
      updateImageType(itemEan?.id, file.id, { type: afftectedType, isRemove })
        .then((res) => {
          setFiles(res);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    },
    [itemEan?.id],
  );

  return (
    <Spin spinning={loading}>
      <DndProvider backend={HTML5Backend}>
        {files.map((file: API.File, ind: number) => {
          return (
            <ImageCard
              key={file.id}
              index={ind}
              id={file.id}
              file={file}
              isSingleEan={itemEan?.is_single ?? false}
              moveCard={moveFile}
              dropFileCallback={dropFileCallback}
              updateTypeCallback={updateTypeCallback}
            />
          );
        })}
      </DndProvider>
    </Spin>
  );
};

export default ImagesCell;
