CREATE TABLE `import_current_bp_aggregated` (
                                                `ean` varchar(50) NOT NULL COMMENT 'EAN',
                                                `date` datetime DEFAULT NULL COMMENT 'imported datte',
                                                `import_id` bigint(20) unsigned DEFAULT NULL COMMENT 'FK for PK in import table',
                                                `price` double DEFAULT NULL COMMENT 'price column',
                                                `table_name` varchar(255) DEFAULT NULL COMMENT 'table name of the imported table',
                                                `sid` bigint(20) unsigned DEFAULT NULL COMMENT 'PK in imported tables. related to import_id',
                                                PRIMARY KEY (`ean`),
                                                KEY `FK_import_current_bp_aggregated_import_id` (`import_id`),
                                                CONSTRAINT `FK_import_current_bp_aggregated_import_id` FOREIGN KEY (`import_id`) REFERENCES `import` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Latest buying price aggregation from imported supplier tables'
;

