<?php

declare(strict_types=1);

namespace App\Service\BasicData\SysImportRwColMap;

use App\Service\BaseService;
use App\Repository\BasicData\SysImportRwColMapRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'BasicData:%s';
    protected SysImportRwColMapRepository $sysImportRwColMapRepository;

    public function __construct(Container $container)
    {
        $this->sysImportRwColMapRepository = $container->get(SysImportRwColMapRepository::class);
    }

    public function getSysImportRwColMapRepository()
    {
        return $this->sysImportRwColMapRepository;
    }
}

