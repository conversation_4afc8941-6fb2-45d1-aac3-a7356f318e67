import { But<PERSON>, Col, message, Row, Space, Typography } from 'antd';
import React, { useState, useRef, useEffect, useMemo } from 'react';
import type { ActionType } from '@ant-design/pro-table';

import Util, { nf2, nf3, sEllipsed, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormGroup } from '@ant-design/pro-form';
import useOfferOptions from '@/hooks/BasicData/useOfferOptions';
import { IRouteComponentProps, useLocation } from 'umi';
import { DefaultOptionType } from 'antd/lib/select';
import styles from './OfferItemListMobile.less';
import SkuComp from '@/components/SkuComp';
import EanFilesComp from '@/components/EanFilesComp';
import { getOfferItem, getOfferItemList } from '@/services/foodstore-one/Offer/offer-item';
import ProList from '@ant-design/pro-list';
import useSearchEanByScan from '@/hooks/BasicData/useSearchEanByScan';
import CreateOrUpdateQtyDeliveredModalForm from './components/CreateOrUpdateQtyDeliveredModalForm';
import QtyComp from '@/components/QtyComp';
import { OfferIboStatus, PageSizeOptionsOnCard3 } from '@/constants';
import PalletFormModal from './components/PalletFormModal';

export type SearchFormValueType = Partial<API.OfferItem> & { includeSubTable?: boolean; trademark?: DefaultOptionType };

const OfferItemListMobile: React.FC<IRouteComponentProps> = (props) => {
  const location: any = useLocation();
  const offerIdInUrl = location.query?.offer_id;

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  // local states
  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.OfferItem>();
  const [matchedOfferItem, setMatchedOfferItem] = useState<API.OfferItem | null>(); // After scanning EAN, we find the exact offer item
  const [selectedRows, setSelectedRows] = useState<API.OfferItem[]>([]);

  const [openCreateOrUpdateQtyDeliveredForm, setOpenCreateOrUpdateQtyDeliveredForm] = useState<boolean>(false);
  const [openPalletModal, setOpenPalletModal] = useState<boolean>(false);

  // custom hooks
  const offerFilterExtra = useMemo(() => {
    return { nin_ibo_status: [OfferIboStatus.Closed], with: 'offerRecvWeightSummary' };
  }, []);
  const { formElements, offer, searchOfferOptions } = useOfferOptions(offerFilterExtra, searchFormRef);

  const {
    itemEan: itemEanScanned,
    setItemEan: setItemEanScanned,
    fieldRef: itemEanScannedFieldRef,
    formElements: formElementsScanned,
  } = useSearchEanByScan({
    name: 'ean_scanned',
    style: { marginTop: 0, width: 150 },
    placeholder: 'Scan EAN',
    with: 'siblingsMulti',
    skipEanInfo: true,
    cbSearched: (ean: API.Ean) => {
      setOpenCreateOrUpdateQtyDeliveredForm(true);
    },
    cbNotFound: () => {
      message.error('Not found EAN!');
    },
  });

  useEffect(() => {
    const formValues = searchFormRef.current?.getFieldsValue();
    if (formValues?.offer_id) {
      searchFormRef.current?.setFieldValue('offer_id', formValues.offer_id);
      actionRef.current?.reload();
    }
  }, []);

  useEffect(() => {
    if (offerIdInUrl) {
      searchFormRef.current?.setFieldValue('offer_id', sn(offerIdInUrl));
      actionRef.current?.reload();
    }
  }, [offerIdInUrl]);

  useEffect(() => {
    actionRef.current?.reload();
    if (offer?.id) {
      setItemEanScanned(null);
      setMatchedOfferItem(null);
    }
  }, [offer?.id]);

  /**
   * Search if selected EAN exists in this offer
   */
  useEffect(() => {
    console.log('offerId:', offer?.id, 'itemId:', itemEanScanned?.item_id);
    if (offer?.id && itemEanScanned?.item_id) {
      getOfferItem({ offer_id: offer.id, item_id: itemEanScanned.item_id })
        .then((res) => {
          if (res) {
            setMatchedOfferItem(res);
          } else {
            message.error('EAN does not exist in this offer! Please scan another one.');
          }
        })
        .catch((err) => {
          Util.error(err);
          setMatchedOfferItem(undefined);
        });
    }
  }, [itemEanScanned?.item_id, offer?.id]);

  useEffect(() => {
    if (matchedOfferItem?.id && itemEanScanned?.id) {
      setOpenCreateOrUpdateQtyDeliveredForm(true);
    }
  }, [matchedOfferItem?.id, itemEanScanned?.id]);

  const handleClickOnTile = (entity: API.IboPre, e?: any) => {
    setCurrentRow(entity);
    setMatchedOfferItem(entity);
    setItemEanScanned(entity.item_ean ?? null);
    setOpenCreateOrUpdateQtyDeliveredForm(true);
    e.stopPropagation();
  };

  const offerShortcut = offer as API.Offer;

  return (
    <div className={styles.offerItemListMobile}>
      <ProList<API.OfferItem, API.PageParams>
        headerTitle={
          <Space size={48}>
            <div>{props.route.name}</div>

            <ProForm<SearchFormValueType>
              layout="inline"
              size="large"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              initialValues={Util.getSfValues('sf_offer_item_mobile', { includeSubTable: false })}
              submitter={false}
            >
              <ProFormGroup size="small" rowProps={{ gutter: 0 }}>
                {formElements}
              </ProFormGroup>

              <ProFormGroup
                size="small"
                rowProps={{ gutter: 0 }}
                spaceProps={{ style: { marginLeft: 36, alignItems: 'center' } }}
              >
                <label className="" title="EAN">
                  EAN:
                </label>
                {formElementsScanned}
              </ProFormGroup>
            </ProForm>
          </Space>
        }
        toolBarRender={(action, rows) => [
          <Space key="toolbar" size={16}>
            {/* <Typography.Link href="/quotes/offer-supplier-data-matrix" target="_blank" className="text-sm">
              Matrix
            </Typography.Link> */}
            <Button type="link" size="large" onClick={() => setOpenPalletModal(true)}>
              {`${offerShortcut?.offer_recv_weights_count ? `${offerShortcut?.offer_recv_weights_count} ` : ''}Pallets${
                offerShortcut?.offer_recv_weights_sum_pallet_weight
                  ? ` (${nf3(offerShortcut?.offer_recv_weights_sum_pallet_weight, true, true)} kg)`
                  : ''
              }`}
            </Button>
          </Space>,
        ]}
        actionRef={actionRef as any}
        rowKey="id"
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_offer_item_mobile', searchValues);
          Util.setSfValues('sf_offer_item_mobile_p', params);

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            with:
              'itemEan,itemEan.eanTextDe,itemEan.eanPriceGfc,offer,itemEan.item,itemEan.files,itemEan.parent,itemEan.parentStockStables,itemEan.eanPriceStable,itemEan.magInventoryStocksQty,offerItemPackedReadySummary' +
              (searchValues?.includeSubTable ? ',iboPresOpenSent' : ''),
          };

          return getOfferItemList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        grid={{ gutter: 16, column: 3 }}
        rowSelection={false}
        /* onItem={(entity) => {
          return {
            className: selectedRows.map((x) => `${x.id}`).includes(`${entity.id}`) ? 'ant-pro-checkcard-checked' : '',
          };
        }} */
        tableAlertRender={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_offer_item_mobile_p')?.pageSize ?? 60),
          pageSizeOptions: PageSizeOptionsOnCard3,
        }}
        itemCardProps={{ bodyStyle: { padding: 0 }, cover: true, size: 'small' }}
        metas={{
          type: {},
          title: {
            fieldProps: { style: { padding: 0 } },
            render(dom, entity) {
              return null;
            },
          },
          content: {
            fieldProps: { style: { padding: 0 } },
            render: (dom, entity, index) => {
              return (
                <div style={{ padding: '0 8px', width: '100%' }}>
                  <Row wrap={false} gutter={12}>
                    <Col flex="90px">
                      <div style={{ width: 80, height: 80 }}>
                        <EanFilesComp files={entity.item_ean?.files} width={80} />
                      </div>
                      <div>
                        <SkuComp sku={entity.item_ean?.sku} noCopyable={true} />
                      </div>
                    </Col>
                    <Col
                      flex="auto"
                      onClick={(e) => {
                        handleClickOnTile(entity, e);
                      }}
                    >
                      <Row style={{ height: 70 }}>
                        <Col span={24}>
                          {sEllipsed(entity.item_ean?.ean_text_de?.name ?? entity.item_ean?.item?.name ?? '', 75)}
                        </Col>
                      </Row>
                      <Row wrap={false} gutter={8}>
                        <Col flex="100px" style={{ paddingTop: 13, fontSize: 13 }}>
                          <Space direction="vertical" size={0} style={{ textAlign: 'left', width: '100%' }}>
                            <Typography.Text style={{ color: entity.item_ean?.is_single ? 'blue' : '#000' }}>
                              {entity.item_ean?.parent?.ean}
                            </Typography.Text>

                            {!entity.item_ean?.is_single && (
                              <Typography.Text style={{ color: 'blue' }}>{entity.item_ean?.ean}</Typography.Text>
                            )}
                          </Space>
                        </Col>
                        <Col flex="auto">
                          {!!entity.qty && (
                            <Space direction="vertical" size={0} style={{ width: '100%' }}>
                              <div
                                className="text-md bold cursor-pointer"
                                style={{ fontSize: 16 }}
                                onClick={(e) => {
                                  /* setCurrentRow(entity);
                                  setMatchedOfferItem(entity);
                                  setItemEanScanned(entity.item_ean ?? null);
                                  setOpenCreateOrUpdateQtyDeliveredForm(true);
                                  e.stopPropagation(); */
                                }}
                              >
                                <QtyComp case_qty={1} qty={entity.delivered_qty_pcs} show_zero title="Delivered Qty" />
                                /
                                <QtyComp case_qty={1} qty={entity.packed_ready_qty_pcs} show_zero title="Packed Qty" />
                                /
                                <QtyComp case_qty={entity.case_qty} qty={entity.qty} show_zero />
                              </div>
                            </Space>
                          )}
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </div>
              );
            },
          },
        }}
      />

      {itemEanScanned && matchedOfferItem ? (
        <CreateOrUpdateQtyDeliveredModalForm
          itemEan={itemEanScanned}
          offerItem={matchedOfferItem}
          modalVisible={openCreateOrUpdateQtyDeliveredForm}
          handleModalVisible={setOpenCreateOrUpdateQtyDeliveredForm}
          onSubmit={async (deliveredRow) => {
            setItemEanScanned(null);
            setMatchedOfferItem(null);
            actionRef.current?.reload();
          }}
          onCancel={() => {
            setItemEanScanned(null);
            setMatchedOfferItem(null);
            actionRef.current?.reload();
          }}
        />
      ) : null}

      {offer?.id && (
        <PalletFormModal
          modalVisible={openPalletModal}
          handleModalVisible={setOpenPalletModal}
          offerId={offer?.id}
          onSubmit={(value) => {
            setOpenPalletModal(false);
            searchOfferOptions();
          }}
        />
      )}
    </div>
  );
};

export default OfferItemListMobile;
