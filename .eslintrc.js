module.exports = {
  // extends: [require.resolve('@umijs/fabric/dist/eslint')],
  extends: [require.resolve('@umijs/lint/dist/config/eslint')],
  globals: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: false,
    page: true,
    REACT_APP_ENV: true,
  },
  plugins: ['react-hooks'],
  rules: {
    // 'no-unused-vars': 'off',
    // '@typescript-eslint/no-unused-vars': 'off',
    // '@typescript-eslint/dot-notation': 'off',
    // 'react/react-in-jsx-scope': 'on', // <---- replace false into off
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'error',
  },
};
