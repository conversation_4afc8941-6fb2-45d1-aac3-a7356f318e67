import { DictCode } from '@/constants';
import { sUrlByTpl } from '@/util';
import { Typography } from 'antd';
import { useModel } from 'umi';

type SEbayOrderNoProps = {
  order?: Partial<API.Order>;
};

const SEbayOrderNo: React.FC<SEbayOrderNoProps> = ({ order }) => {
  const incrementId = order?.increment_id || '';

  const { getDictByCode } = useModel('app-settings');

  let orderId = '';
  if (incrementId?.startsWith('EBD')) {
    orderId = order?.detail?.payment?.additional_information?.[2] || '';
    return orderId ? (
      <Typography.Link
        href={sUrlByTpl(getDictByCode(DictCode.EBAY_ORDER_ADMIN_URL), {
          orderId: orderId,
        })}
        target="_blank"
        rel="noreferrer"
        copyable
        title="Ebay Order No"
      >
        {orderId}
      </Typography.Link>
    ) : null;
  } else if (incrementId?.startsWith('KL')) {
    orderId = order?.ext?.kaufland_order_id || '';
    return orderId ? (
      <Typography.Link
        href={sUrlByTpl(getDictByCode(DictCode.KL_ORDER_ADMIN_URL), {
          orderId: orderId,
        })}
        target="_blank"
        rel="noreferrer"
        title="KL Order No"
        copyable
      >
        {orderId}
      </Typography.Link>
    ) : null;
  }
  return null;
};

export default SEbayOrderNo;
