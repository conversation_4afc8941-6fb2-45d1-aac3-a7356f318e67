<?php
/**
 *
 * Full up sync of stocks.
 *
 * 1. down sync magento stock
 * 2. Determine the difference
 * 3. Up sync Multi quantities from system into magento.
 * 4. Up sync Single quantities from system into magento.  (Note: calculated based on Multi-EANs and own Singles in Stable Warehouse)
 *
 * @deprecated  2023-03-xx
 * @package     Cron job script.
 * @since       2023-03-06
 *
 * @recommendedPeriod: a day
 */

use App\Service\Stock\StockStable\StockStableFullUpSyncService;

error_reporting(E_ALL);

require __DIR__ . '/../../extras/cron/_cron_before.php';
/** @var \Slim\Container $container */

/** @var StockStableFullUpSyncService $service */
$service = $container->get(StockStableFullUpSyncService::class);

$service->upSync();

require __DIR__ . '/../../extras/cron/_cron_after.php';