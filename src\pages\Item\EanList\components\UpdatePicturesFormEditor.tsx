import { useCallback, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Divider, Image, message, Modal, Popconfirm, Space, Spin, Tooltip } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormItem,
  ProFormList,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-form';
import { ProFormUploadDragger } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import {
  deleteEanFile,
  dsExternalProductImage,
  getEanWithAllImages,
  updateSysImage,
} from '@/services/foodstore-one/Item/ean';
import Util, { sn } from '@/util';
import _, { isArray } from 'lodash';
import { LS_TOKEN_NAME } from '@/constants';
import type { RcFile, UploadChangeParam, UploadFile } from 'antd/lib/upload';
import { DeleteOutlined, DownloadOutlined, LinkOutlined, SaveFilled, StopFilled } from '@ant-design/icons';

import { PRODUCT_IMAGE_TYPES } from './UpdatePicturesForm';

export type FormValueType = {
  files?: API.File[];
  shopProduct?: Shop.Product;
} & Partial<API.Ean>;

const defaultInitialValues: FormValueType = {
  files: [],
};

export type UpdatePicturesFormEditorProps = {
  initialValues?: Partial<API.Ean>;
  gdsnItem?: API.ItemEanGdsn;
};

const UpdatePicturesFormEditor: React.FC<UpdatePicturesFormEditorProps> = (props) => {
  const [loadings, setLoadings] = useState<Record<string, boolean>>({});
  // Image preview
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  // Form ref
  const formRef = useRef<ProFormInstance>();

  // Fetch item EAN images via REST Api
  const fetchItemEanImages = useCallback(() => {
    if (props.initialValues?.id) {
      setLoadings((prev) => ({ ...prev, [`sysImages`]: true }));
      getEanWithAllImages(`${props.initialValues?.id}`).then((res) => {
        const parentData = res.parent;
        res.parent.files = res.parent?.files
          ?.map?.((x: API.File) => {
            const mappedFile: API.File = _.find(res.files, { id: x.id });
            return {
              ...x,
              pivot: {
                ...(x.pivot || {}),
                position: 100,
                types: '',
                ...mappedFile?.pivot,
              },
            };
          })
          ?.sort((a: API.File, b: API.File) =>
            a.pivot.position < b.pivot.position ? -1 : a.pivot.position > b.pivot.position ? 1 : 0,
          );
        formRef.current?.setFieldsValue({
          files: res.files,
          parent: parentData,
          shopProduct: {
            media_gallery_entries: res.mag_files,
          },
        });
        setLoadings((prev) => ({ ...prev, [`sysImages`]: false }));
      });
    }
  }, [props.initialValues?.id]);

  useEffect(() => {
    fetchItemEanImages();
  }, [fetchItemEanImages]);

  /**
   * Update the current row data of an image.
   *
   * @param currentRow Row data
   * @returns
   */
  const handleImageUpdate = (currentRow: any): void => {
    {
      if (!props.initialValues?.id || !currentRow.id) {
        message.error('Something is wrong! Please reload the list.');
        return;
      }
      setLoadings((prev) => ({
        ...prev,
        [`sysImageUpdate${currentRow.id}`]: true,
      }));
      updateSysImage(props.initialValues?.id, currentRow)
        .then((newFiles) => {
          if (newFiles) {
            message.success('Updated successfully.');
            fetchItemEanImages();
          } else {
            message.error('Failed to update.');
          }
        })
        .catch((e) => {
          message.error(e.message);
        })
        .finally(() =>
          setLoadings((prev) => ({
            ...prev,
            [`sysImageUpdate${currentRow.id}`]: false,
          })),
        );
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await Util.getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const handleDeleteFile = async (file: API.File) => {
    const hide = message.loading(
      `${file.pivot.is_parent_file ? 'Unlinking' : 'Deleting'} a file '${file.file_name}'.`,
      0,
    );
    const res = await deleteEanFile({
      id: props.initialValues?.id,
      fileId: file.uid,
    });
    hide();
    fetchItemEanImages();
    if (res.message) {
      message.success(`${file.pivot.is_parent_file ? 'Unlinked' : 'Deleted'} successfully!`);
    } else {
      Util.error(`${file.pivot.is_parent_file ? 'Unlinking' : 'Delete'} failed, please try again!`);
    }

    return res as any;
  };

  const msgRef = useRef<any>();

  const gdsnItem = props.gdsnItem;

  return (
    <ProForm<FormValueType>
      formRef={formRef}
      submitter={{
        render: (form, dom) => {
          return [];
        },
      }}
    >
      <ProFormUploadDragger
        max={10}
        name="files"
        title="EAN pictures"
        description="Please select files or drag & drop"
        accept="image/*"
        fieldProps={{
          multiple: true,
          listType: 'picture-card',
          name: 'file',
          action: `${API_URL}/api/item/ean/${props.initialValues?.id}/upload-file`,
          headers: {
            Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
          },
          data: {
            mode: 'pre-files',
          },
          onPreview: handlePreview,
          onChange: (info: UploadChangeParam, updateState = true) => {
            console.log(info, updateState);
            if (info.file.status == 'done') {
              msgRef.current?.();
              msgRef.current = null;

              message.success('Uploaded successfully.');
              info.file.url = info.file.response.url;
              info.file.uid = info.file.response.uid;
              (info.file as any).id = info.file.response.uid;
              (info.file as any).file_name = info.file.response.file_name;
              (info.file as any).clean_file_name = info.file.response.clean_file_name;
              (info.file as any).path = info.file.response.path;
              (info.file as any).org_path = info.file.response.org_path;
              (info.file as any).pivot = info.file.response.pivot;

              const newFiles = [...info.fileList];
              formRef.current?.setFieldsValue({ files: newFiles.filter((x: any) => x?.id == x?.uid) });
            } else if (info.file.status == 'uploading') {
              if (!msgRef.current) msgRef.current = message.loading('Uploading ...', 0);
            } else if (info.file.status == 'error') {
              msgRef.current?.();
              msgRef.current = null;
              message.error(info.file.response?.message || 'Failed to upload file.');
              const newFiles = [...info.fileList];
              formRef.current?.setFieldsValue({ files: newFiles.filter((x: any) => x?.id == x?.uid) });
            }
          },
          showUploadList: false,
          width: '100%',
          onRemove: async (file: API.File) => {
            const { confirm } = Modal;
            return new Promise((resolve, reject) => {
              confirm({
                title: file.pivot.is_parent_file
                  ? 'Are you sure you want to unlink?'
                  : 'Are you sure you want to delete?',
                onOk: async () => {
                  resolve(true);
                  const res = await handleDeleteFile(file);
                  return res.message;
                },
                onCancel: () => {
                  reject(true);
                },
              });
            });
          },
        }}
      />

      <Divider />

      <ProFormGroup
        rowProps={{ gutter: 24 }}
        title={
          <>
            {/* <Space>
              <span>Images Detail</span>
              <Button
                type="link"
                size="small"
                loading={loadings.sysImages}
                onClick={() => {
                  fetchItemEanImages();
                }}
              >
                Reload
              </Button>
              <Popconfirm
                title={
                  <>
                    Attention! Magento shop images will be removed! <br />
                    Are you sure you wanna up sync all of images？
                  </>
                }
                okText="Yes"
                cancelText="No"
                onConfirm={() => {
                  if (!props.initialValues?.id) return;
                  const hide = message.loading('Up syncing all images into shop...');
                  setLoadings((prev) => ({
                    ...prev,
                    [`shopImages`]: true,
                    [`sysImages`]: true,
                  }));
                  usAllImages(props.initialValues?.id)
                    .then((res) => {
                      message.success('Successfully uploaded!');
                      formRef.current?.setFieldsValue({ shopProduct: res });
                      fetchItemEanImages();
                    })
                    .catch((e) => {
                      message.error(e.message);
                    })
                    .finally(() => {
                      hide();
                      setLoadings((prev) => ({
                        ...prev,
                        [`shopImages`]: false,
                        [`sysImages`]: false,
                      }));
                    });
                }}
              >
                <Button type="default" size="small" disabled={loadings.sysImages}>
                  Up sync all Images
                </Button>
              </Popconfirm>
            </Space> */}
          </>
        }
      >
        <ProFormDependency key={'files'} name={['files', 'parent']}>
          {() => {
            return (
              <>
                <Spin spinning={loadings.sysImages ?? false}>
                  <ProFormList name={['files']} key="uid" creatorButtonProps={false} actionRender={() => []}>
                    {(f, index, action) => {
                      const currentRow: API.File = action.getCurrentRowData();
                      // If multi EAN, we exclude inherited parent images
                      if (props.initialValues?.id != props.initialValues?.parent_id) {
                        if (currentRow.pivot?.is_parent_file) return undefined;
                      }
                      return (
                        <>
                          <Space style={{ borderLeft: '4px solid #f8f8f8', paddingLeft: 10 }}>
                            <ProFormItem name="id" label="ID" style={{ width: 60 }}>
                              {currentRow.uid}
                            </ProFormItem>
                            <ProFormItem name="id" label="Image" style={{ width: 100 }}>
                              {(currentRow.url ?? currentRow.thumbUrl) && (
                                <Image src={`${currentRow.url ?? currentRow.thumbUrl}`} height={70} />
                              )}
                            </ProFormItem>
                            <ProFormSwitch
                              width="xs"
                              name={['pivot', 'disabled']}
                              label="Hide?"
                              tooltip="Hide on frontend?"
                              formItemProps={{ style: { width: 60 } }}
                            />
                            <ProFormDigit
                              name={['pivot', 'position']}
                              label="Position"
                              tooltip="Image display position."
                              formItemProps={{ style: { width: 70 } }}
                            />
                            <ProFormText
                              name={['pivot', 'label']}
                              width="md"
                              label="Alt Text"
                              tooltip="If blank, image file name will be used."
                              initialValue={currentRow.clean_file_name}
                              formItemProps={{ style: { width: 350 } }}
                            />
                            <ProFormCheckbox.Group
                              name={['pivot', 'us_modes']}
                              label="Setting"
                              layout="vertical"
                              convertValue={(value) => {
                                if (isArray(value)) return value;
                                const newValue = value || '';
                                return newValue.split(',');
                              }}
                              options={[
                                { value: '1', label: 'Own Shop' },
                                { value: '2', label: 'Ebay' },
                              ]}
                            />
                            <ProFormCheckbox.Group
                              name={['pivot', 'types']}
                              label="Types"
                              layout="vertical"
                              convertValue={(value) => {
                                if (isArray(value)) return value;
                                const newValue = value || '';
                                return newValue.split(',');
                              }}
                              options={PRODUCT_IMAGE_TYPES}
                            />
                            <ProFormItem name="action" label="Action">
                              <Space size={0}>
                                <Button
                                  type="link"
                                  size="small"
                                  loading={loadings[`sysImageUpdate${currentRow.id}`]}
                                  onClick={() => handleImageUpdate(currentRow)}
                                >
                                  <SaveFilled />
                                </Button>
                                <Popconfirm
                                  title="Are you sure you want to delete？"
                                  okText="Yes"
                                  cancelText="No"
                                  onConfirm={async () => {
                                    if (!props.initialValues?.sku || !currentRow.id) return;
                                    setLoadings((prev) => ({
                                      ...prev,
                                      [`sysImageDelete${currentRow.id}`]: true,
                                    }));
                                    await handleDeleteFile(currentRow);
                                    setLoadings((prev) => ({
                                      ...prev,
                                      [`sysImageDelete${currentRow.id}`]: false,
                                    }));
                                  }}
                                >
                                  <Button
                                    type="text"
                                    danger
                                    size="small"
                                    loading={loadings[`sysImageDelete${currentRow.id}`]}
                                  >
                                    <DeleteOutlined />
                                  </Button>
                                </Popconfirm>
                              </Space>
                            </ProFormItem>
                          </Space>
                        </>
                      );
                    }}
                  </ProFormList>
                </Spin>
              </>
            );
          }}
        </ProFormDependency>
        {props.initialValues?.id != props.initialValues?.parent_id && (
          <ProFormDependency key={'parent-files'} name={['files', ['parent']]}>
            {(depValues) => {
              return (
                <>
                  <Spin spinning={loadings.sysImages ?? false}>
                    <ProFormList
                      name={['parent', 'files']}
                      key="uid"
                      creatorButtonProps={false}
                      actionRender={() => []}
                    >
                      {(f, index, action) => {
                        const currentRow: API.File = action.getCurrentRowData();
                        const fileIds = depValues?.files?.map?.((x: API.File) => x.id) || [];

                        const existInMap = fileIds.findIndex((x: number) => x == currentRow.id) >= 0;
                        return (
                          <>
                            <Space
                              style={{
                                borderLeft: existInMap ? '4px solid #9c5fe3' : '4px solid #af8cd7',
                                paddingLeft: 10,
                              }}
                            >
                              <ProFormItem name="id" label="ID" style={{ width: 60, opacity: existInMap ? 1 : 0.5 }}>
                                {currentRow.uid}
                              </ProFormItem>
                              <ProFormItem
                                name="id"
                                label="Image"
                                style={{ width: 100, opacity: existInMap ? 1 : 0.5 }}
                              >
                                {(currentRow.url ?? currentRow.thumbUrl) && (
                                  <Image src={`${currentRow.url ?? currentRow.thumbUrl}`} height={70} />
                                )}
                              </ProFormItem>
                              <ProFormSwitch
                                width="xs"
                                name={['pivot', 'disabled']}
                                label="Hide?"
                                tooltip="Hide on frontend?"
                                disabled={!existInMap}
                                formItemProps={{ style: { width: 60 } }}
                              />
                              <ProFormDigit
                                name={['pivot', 'position']}
                                label="Position"
                                tooltip="Image display position."
                                disabled={!existInMap}
                                formItemProps={{ style: { width: 70 } }}
                              />
                              <ProFormText
                                name={['pivot', 'label']}
                                width="md"
                                label="Alt Text"
                                tooltip="If blank, image file name will be used."
                                initialValue={currentRow.clean_file_name}
                                disabled={!existInMap}
                                formItemProps={{ style: { width: 350 } }}
                              />
                              <ProFormCheckbox.Group
                                name={['pivot', 'types']}
                                label="Types"
                                layout="vertical"
                                convertValue={(value) => {
                                  if (isArray(value)) return value;
                                  const newValue = value || '';
                                  return newValue.split(',');
                                }}
                                disabled={!existInMap}
                                options={PRODUCT_IMAGE_TYPES}
                              />
                              <ProFormItem name="action" label="Action">
                                <Space size={0}>
                                  <Tooltip title={existInMap ? 'Save' : 'Add parent image'}>
                                    <Button
                                      type="link"
                                      size="small"
                                      loading={loadings[`sysImageUpdate${currentRow.id}`]}
                                      onClick={() => handleImageUpdate(currentRow)}
                                      icon={existInMap ? <SaveFilled /> : <LinkOutlined />}
                                    />
                                  </Tooltip>
                                  <Popconfirm
                                    title="Are you sure you want to unlink？"
                                    okText="Yes"
                                    cancelText="No"
                                    disabled={!existInMap}
                                    onConfirm={async () => {
                                      if (!props.initialValues?.sku || !currentRow.id) return;
                                      setLoadings((prev) => ({
                                        ...prev,
                                        [`sysImageDelete${currentRow.id}`]: true,
                                      }));
                                      // const tmp = action.getCurrentRowData();
                                      const hide = message.loading(`Unlinking a file '${currentRow.file_name}'.`, 0);
                                      const res = await deleteEanFile({
                                        id: props.initialValues?.id,
                                        fileId: currentRow.uid,
                                      });
                                      hide();
                                      if (res.message) {
                                        message.success('Unlinked successfully!');
                                      } else {
                                        Util.error('Unlink failed, please try again!');
                                      }
                                      setLoadings((prev) => ({
                                        ...prev,
                                        [`sysImageDelete${currentRow.id}`]: false,
                                      }));
                                      fetchItemEanImages();
                                    }}
                                  >
                                    <Tooltip title="Unlink parent image">
                                      <Button
                                        type="text"
                                        danger
                                        size="small"
                                        disabled={!existInMap}
                                        loading={loadings[`sysImageDelete${currentRow.id}`]}
                                        icon={<StopFilled />}
                                      />
                                    </Tooltip>
                                  </Popconfirm>
                                </Space>
                              </ProFormItem>
                            </Space>
                          </>
                        );
                      }}
                    </ProFormList>
                  </Spin>
                </>
              );
            }}
          </ProFormDependency>
        )}
      </ProFormGroup>

      {sn(gdsnItem?.detail?.images?.length) > 0 && (
        <>
          <Divider />
          <ProFormGroup rowProps={{ gutter: 24 }} title="GDSN images">
            {gdsnItem?.detail?.images?.map((file) => (
              <div
                key={file}
                style={{
                  backgroundColor: 'white',
                  padding: '3px',
                  width: 88,
                  display: 'inline-block',
                  margin: '4px 4px',
                  overflow: 'hidden',
                }}
                className={`image-card`}
              >
                <Image
                  src={`${file}`}
                  preview={{
                    src: `${file}`,
                  }}
                  width={80}
                  height={80}
                />
                <div style={{ textAlign: 'center' }}>
                  <Popconfirm
                    title={<>Are you sure you want to download image?</>}
                    okText="Yes"
                    cancelText="No"
                    onConfirm={() => {
                      if (props.initialValues?.id && file) {
                        const hide = message.loading('Downloading file...', 0);
                        dsExternalProductImage(props.initialValues.id, file)
                          .then((res) => {
                            console.log(res);
                            fetchItemEanImages();
                          })
                          .catch(Util.error)
                          .finally(() => hide());
                      }
                    }}
                  >
                    <Button type="link" icon={<DownloadOutlined />} title="Download image..." className="text-xs">
                      Downlaod
                    </Button>
                  </Popconfirm>
                </div>
              </div>
            ))}
          </ProFormGroup>
        </>
      )}

      <Modal open={previewVisible} title={previewTitle} footer={null} onCancel={() => setPreviewVisible(false)}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </ProForm>
  );
};

export default UpdatePicturesFormEditor;
