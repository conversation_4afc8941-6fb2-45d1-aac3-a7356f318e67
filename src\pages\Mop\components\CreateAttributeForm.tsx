import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import React, { useRef } from 'react';
import { Button, message, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormRadio, ProFormSwitch } from '@ant-design/pro-form';
import { ProFormGroup, ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import _ from 'lodash';
import { useModel } from 'umi';
import { addMopProduct } from '@/services/foodstore-one/Mop/mop-product';
import { CustomerGroupOptions } from './UpdateAttributeForm';
import SProFormDigit from '@/components/SProFormDigit';
import { MopProductStatus, MopProductStatusOptions } from '@/constants';

const handleCreate = async (fields: FormValueType) => {
  const hide = message.loading('Creating...', 0);

  try {
    const res = await addMopProduct(fields);
    hide();
    message.success('Created successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.MopProduct>;

export type CreateAttributeFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.MopProduct) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  reloadList?: (updatedRow: Partial<API.MopProduct>) => Promise<boolean | void>;
};

const CreateAttributeForm: React.FC<CreateAttributeFormProps> = (props) => {
  const { modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);
  const [loadingLive, setLoadingLive] = useState(false);

  const { appSettings } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      formRef?.current?.resetFields();
    }
  }, [modalVisible]);

  return (
    <ModalForm<Partial<API.MopProduct>>
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Create a Stocklot Product</span>
        </div>
      }
      disabled={loading}
      width={930}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      grid
      formRef={formRef}
      onFinish={async (value) => {
        if (!formRef.current) return Promise.resolve(false);
        setLoading(true);
        if (formRef.current?.isFieldsTouched()) {
          const newData = { ...value };

          const success = await handleCreate(newData);
          if (success) {
            if ((value as any).closeModal) handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          setLoading(false);
          handleModalVisible(false);
        }
        setLoading(false);
        formRef.current.getFieldInstance('up-sync').value = '';
        return Promise.resolve(true);
      }}
      modalProps={{
        confirmLoading: loading,
        // className: initialValues?.is_single ? 'm-single' : 'm-multi',
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Create & Close
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText
          rules={[
            {
              required: true,
              message: 'SKU is required',
            },
          ]}
          width="md"
          name="sku"
          label="SKU"
          initialValue={'R_'}
          colProps={{ xl: 6 }}
        />

        <ProFormSelect
          name="customer_group"
          label="Customer Group"
          colProps={{ xl: 6 }}
          mode="single"
          initialValue={'All'}
          options={CustomerGroupOptions}
        />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText
          rules={[
            {
              required: true,
              message: 'Name is required',
            },
          ]}
          width="xl"
          name="name"
          label="Name"
          colProps={{ xl: 16 }}
        />
        <SProFormDigit
          name="price"
          label={'Price (€)'}
          placeholder="Price"
          width={105}
          fieldProps={{
            step: 0.1,
            precision: 7,
          }}
          colProps={{ xl: 8 }}
        />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }} title="Additional info">
        <ProFormSelect
          name="attribute_set_code"
          label="Attribute Set"
          colProps={{ xl: 6 }}
          required
          initialValue={4}
          options={appSettings.productAttributeSet?.map((x) => ({
            value: x.attribute_set_id,
            label: x.attribute_set_name,
          }))}
          rules={[
            {
              required: true,
              message: 'Attribute Set is required',
            },
          ]}
        />

        <ProFormSelect
          name="product_type"
          label="Product Type"
          colProps={{ xl: 6 }}
          required
          initialValue={'simple'}
          options={[
            {
              value: 'simple',
              label: 'Simple Product',
            },
          ]}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
          rules={[
            {
              required: true,
              message: 'Product Type is required',
            },
          ]}
        />

        <ProFormSelect
          name="visibility"
          label="Visibility"
          colProps={{ xl: 6 }}
          required
          initialValue={4}
          options={appSettings.productAttributes?.visibility?.options}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
          rules={[
            {
              required: true,
              message: 'Visibility is required',
            },
          ]}
        />
        <ProFormSelect
          name="product_websites"
          label="Product Websites"
          colProps={{ xl: 6 }}
          mode="multiple"
          initialValue={['2']}
          options={appSettings.storeWebsites
            ?.filter((x) => x.code != 'admin')
            ?.map((x) => ({
              value: `${x.id}`,
              label: x.name,
            }))}
        />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }} title="Product status">
        <ProFormRadio.Group
          name="status"
          label="Status"
          initialValue={MopProductStatus.DRAFT}
          options={MopProductStatusOptions}
        />
        {/* <ProFormSwitch
          name={'fs_export_kaufland'}
          label="Export to Kaufland?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_idealo'}
          label="Export to Idealo?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_billiger'}
          label="Export to Billiger?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <ProFormSwitch
          name={'fs_export_google'}
          label="Export to Google?"
          colProps={{ span: 'auto' }}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        /> */}
      </ProFormGroup>
    </ModalForm>
  );
};

export default CreateAttributeForm;
