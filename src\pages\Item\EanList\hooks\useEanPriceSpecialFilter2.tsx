import Util from '@/util';
import { FilterOutlined } from '@ant-design/icons';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import { ActionType } from '@ant-design/pro-table';
import { Space } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { useEffect, useMemo, useState } from 'react';

const useEanPriceSpecialFilter2 = (
  actionRef: React.MutableRefObject<ActionType | undefined>,
  searchRef: React.MutableRefObject<ProFormInstance | undefined>,
  parentLoading?: boolean,
  lsKey?: string,
) => {
  const [filterId, setFilterId] = useState<any>();

  const options: DefaultOptionType[] = [
    {
      value: '04',
      label: '04 - Active GFC, but no PriceStable',
    },
    {
      value: '05',
      label: '05 -  PriceStable Exists, but no Price GFC',
    },
    {
      value: '06',
      label: '06 -  GFC Price exist, but no Active GFC',
    },
    {
      value: '07',
      label: '07 -  GFC Price % < 125',
    },
    {
      value: '10',
      label: '10 -  Qty exists, but no active FS_ONE',
    },
  ];

  useEffect(() => {
    setFilterId(Util.getSfValues(`ean_special_filter2${lsKey}`)?.['filterId'] || '');
  }, [lsKey]);

  useEffect(() => {
    /* if (filterId == '01') {
      
    } else if (filterId == '02') {
      
    } else if (filterId == '03') {
      
    } else if (filterId == '04') {
      
    } else if (filterId == '50') {
      
    } else if (filterId == '51') {
      
    } */

    actionRef.current?.reload();
  }, [filterId]);

  const renderedEle = useMemo(() => {
    return (
      <Space style={{ marginLeft: 64 }}>
        <ProFormSelect
          name="filterId2"
          placeholder="00 No Filter"
          width={200}
          label={<FilterOutlined />}
          colon={false}
          allowClear
          showSearch
          options={options}
          formItemProps={{ style: { marginBottom: 0 } }}
          fieldProps={{
            dropdownMatchSelectWidth: false,
            value: filterId,
            onChange(value, option) {
              setFilterId(value);
              Util.setSfValues(`ean_special_filter2${lsKey}`, { filterId: value });
            },
          }}
        />
      </Space>
    );
  }, [filterId, lsKey, options, parentLoading]);

  return { renderedEle, filterId };
};

export default useEanPriceSpecialFilter2;
