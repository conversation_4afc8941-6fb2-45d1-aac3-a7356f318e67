import { request } from 'umi';
import { paramsSerializer } from '../api';
import type { DefaultOptionType } from 'antd/lib/select';
import Util from '@/util';

const urlPrefix = '/api/ibo/ibo-pre-management';

/** get GET /api/ibo/ibo-pre-management */
export async function getIboPreManagementList(params: API.PageParams, sort: any, filter: any) {
  return request<API.ResultList<API.IboPreManagement>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getIboPreManagement(
  id: string | number,
  params?: Record<string, any>,
): Promise<API.IboPreManagement> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params: {
      ...params,
      with: params?.with ? params.with : 'detail',
    },
    withToken: true,
  }).then((res) => res.message);
}

/** put PUT /api/ibo/ibo-pre-management */
export async function updateIboPreManagement(
  id: number,
  data: Partial<API.IboPreManagement>,
  options?: Record<string, any>,
): Promise<API.IboPreManagement> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/ibo/ibo-pre-management */
export async function createIboPreManagement(
  data: API.IboPreManagement,
  options?: Record<string, any>,
): Promise<API.IboPreManagement> {
  return request<API.BaseResult>(urlPrefix, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/ibo/ibo-pre-management */
export async function deleteIboPreManagement(id?: number | string, options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/ibo-pre-management/ac-list
 *
 * get the autocomplete lists.
 *
 */
export async function getIBOPreManagementACList(params: Record<string, string>, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      // sort_detail: JSON.stringify(!sort || !Object.keys(sort).length ? { order_no: 'descend' } : {}),
      sort: { id: 'descend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) =>
    res.message.map((x: API.IboPreManagement) => ({
      ...x,
      value: x.id,
      // label: `#${x.id || '-'} | ${x.supplier_name} - ${Util.dtToDMY(x.created_on) || 'N/A'}${x.note_supplier ? ' | ' + x.note_supplier : ''}${x.inbound_no ? ' | ' + x.inbound_no : ''}${x.note_customer ? ' | ' + x.note_customer : ''}${x.status == 'open' ? '' : ` (${x.status})`}`,
      label: `${x.inbound_no ? x.inbound_no : '-'} | ${x.note_supplier ?? ' '} - ${Util.dtToDMY(x.created_on)}${x.status == 'open' ? '' : ` (${x.status})`}`,
    })),
  );
}



/** delete DELETE /api/ibo-pre-management/delete-picture/{id} */
export async function deleteIboPreManagementPicture(id: number | string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/delete-picture/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}