import type { ProFormFieldProps } from '@ant-design/pro-form';

import styles from './index.less';
import { ArrowLeftOutlined, CloseOutlined, EllipsisOutlined } from '@ant-design/icons';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { But<PERSON>, Popover, Row, Space } from 'antd';
import { generateAlphanumericArray } from '@/util';
import ProCard from '@ant-design/pro-card';

/*
First choice / Click
D E F G H I X Y   [Other]

2nd choic / Click
0 A B C D E F G H I K [Other]

3rd Choice / Click
0 1 2 3 [Other]

4th Choice
L M N R  [Other]
*/

const ALPHABETIC_NUMERIC = generateAlphanumericArray();

export const WL_SELECTION_STEPS = [1, 2, 3, 4, 5];

export const KEYPAD_LETTERS: Record<number, string[]> = {
  1: ['D', 'E', 'F', 'G', 'H', 'I', 'X', 'Y', 'other1'],
  2: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'other2'],
  3: ['0', '1', '2', '3', 'other3'],
  4: ['L', 'M', 'N', 'R', 'other4'],
  5: ['other5'],
};

export const KEYPAD_LETTERS_OTHERS: Record<number, string[]> = WL_SELECTION_STEPS.reduce((prev: any, step: number) => {
  prev[step] = ALPHABETIC_NUMERIC.filter((x) => !KEYPAD_LETTERS[step].includes(x));
  return prev;
}, {});

export const INITIAL_OPEN_OTHERS = WL_SELECTION_STEPS.reduce((prev: Record<number, boolean>, x) => {
  prev[x] = false;
  return prev;
}, {});

type WarehouseLocationSelectorType = {
  defaultValue?: string;
  onChange: (value: string) => void;
  eleOptions?: ProFormFieldProps & { showDecimal?: boolean };
  showBodyScroll?: boolean;
  initialOpen?: boolean;
  isMobile?: boolean;
  disableAutoClose?: boolean; // Disable to close dialog in case 4 or 5 letters are selected.
  actionsRender?: (value: string, setOpen?: Dispatch<SetStateAction<boolean>>) => any;
  buttonRender?: (value: string) => any;
};

/**
 * Numpad
 */
const WarehouseLocationSelector: React.FC<WarehouseLocationSelectorType> = ({
  defaultValue,
  onChange,
  showBodyScroll,
  initialOpen,
  isMobile,
  disableAutoClose,
  actionsRender,
  buttonRender,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [openOthers, setOpenOthers] = useState<Record<number, boolean>>(INITIAL_OPEN_OTHERS);
  const [value, setValue] = useState<string>('');

  const gStep = value.length;

  const onClickHandler = (letterP: string | number, stepP?: number) => {
    if (stepP == gStep + 1) {
      setValue((prev) => `${prev}${letterP}`);
    } else if (stepP == gStep) {
      setValue((prev) => prev.slice(0, -1));
    }

    setOpenOthers(INITIAL_OPEN_OTHERS);
  };

  useEffect(() => {
    setValue(defaultValue || '');
  }, [defaultValue]);

  useEffect(() => {
    if (!disableAutoClose) {
      if (value.length >= 4) {
        setOpen(false);
        setOpenOthers(INITIAL_OPEN_OTHERS);
      }
    }
    onChange(value || '');
  }, [value, disableAutoClose]);

  useEffect(() => {
    setOpen(!!initialOpen);
  }, [initialOpen]);

  return (
    <Popover
      placement="bottom"
      open={open}
      onOpenChange={setOpen}
      trigger={['click']}
      overlayStyle={{ zIndex: 150 }}
      content={
        <ProCard
          className={styles.content}
          title={
            <Space>
              <div style={{ width: 230 }}>{value || 'Select'}</div>
              <div>
                <Button
                  icon={<ArrowLeftOutlined />}
                  size="large"
                  className="keypad-btn"
                  disabled={value.length == 0}
                  onClick={() => {
                    setValue((prev) => `${prev}`.slice(0, -1));
                    setOpenOthers(INITIAL_OPEN_OTHERS);
                  }}
                />
              </div>
            </Space>
          }
          headerBordered
          bodyStyle={{
            padding: 0,
            ...(showBodyScroll ? { maxHeight: 'calc(100vh - 250px)', overflowY: 'auto' } : {}),
          }}
          headStyle={{
            padding: 0,
          }}
          extra={
            <div>
              {actionsRender?.(value, setOpen)}
              <Button
                icon={<CloseOutlined />}
                size="large"
                className="keypad-btn"
                onClick={() => {
                  setOpenOthers(INITIAL_OPEN_OTHERS);
                  setOpen(false);
                }}
              />
            </div>
          }
        >
          {WL_SELECTION_STEPS.map((step) => (
            <Row className={`step step${step}`} key={step}>
              {KEYPAD_LETTERS[step].map((letter) =>
                letter.length == 1 ? (
                  <Button
                    key={`${step}_${letter}`}
                    size="large"
                    type="primary"
                    className={`keypad-btn${
                      (step <= gStep && letter != value.slice(step - 1, step)) || step > gStep + 1 ? ' disabled' : ''
                    }`}
                    disabled={(step <= gStep && letter != value.slice(step - 1, step)) || step > gStep + 1}
                    onClick={() => onClickHandler(letter, step)}
                  >
                    {letter}
                  </Button>
                ) : (
                  <Popover
                    key={`${step}_${letter}`}
                    placement="bottom"
                    trigger={[]}
                    open={openOthers[step]}
                    onOpenChange={(visible) => setOpenOthers((prev) => ({ ...prev, [step]: visible }))}
                    content={
                      <div className={styles.contentSmall}>
                        <Row className={`step step${step}`}>
                          {KEYPAD_LETTERS_OTHERS[step].map((letter) => (
                            <Button
                              key={`${step}_${letter}`}
                              size="large"
                              type="primary"
                              className={`keypad-btn keypad-btn-sm ${step <= gStep ? ' disabled' : ''}`}
                              disabled={step <= gStep}
                              onClick={() => onClickHandler(letter, step)}
                            >
                              {letter}
                            </Button>
                          ))}
                        </Row>
                      </div>
                    }
                  >
                    <Button
                      size="large"
                      icon={<EllipsisOutlined />}
                      className={`keypad-btn other  ${
                        (step <= gStep && !KEYPAD_LETTERS_OTHERS[step].includes(value.slice(step - 1, step))) ||
                        step > gStep + 1
                          ? ' disabled'
                          : ''
                      }`}
                      disabled={
                        (step <= gStep && !KEYPAD_LETTERS_OTHERS[step].includes(value.slice(step - 1, step))) ||
                        step > gStep + 1
                      }
                      onClick={(e) => {
                        console.log('Sub-modal opening...');
                        setOpenOthers((prev) => ({ ...prev, [step]: !prev[step] }));
                      }}
                    />
                  </Popover>
                ),
              )}
            </Row>
          ))}
        </ProCard>
      }
    >
      {buttonRender ? (
        buttonRender(value)
      ) : (
        <Button type="primary" size="large" onClick={() => setOpen(true)} style={{ width: isMobile ? 160 : 130 }}>
          {value ? `${value}` : 'Select Location'}
        </Button>
      )}
    </Popover>
  );
};

export default WarehouseLocationSelector;
