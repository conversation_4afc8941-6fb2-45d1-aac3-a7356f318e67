import HtmlEditor from '@/components/HtmlEditor';
import { CaseReason, DictCode, DictType } from '@/constants';
import CrmCaseListModal from '@/pages/Crm/CrmCase/CrmCaseListModal';
import { addCrmCase, getCrmCaseList, updateCrmCase } from '@/services/foodstore-one/Crm/crm-case';
import { dsPullEmail, getEmail, getNextEmail, updateEmail } from '@/services/foodstore-one/Email/email';
import { getAppSettings } from '@/services/foodstore-one/api';
import Util, { fullAddress, nf2, nl2br, skuToItemId, sn } from '@/util';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CloseOutlined,
  CodepenOutlined,
  EditOutlined,
  HighlightOutlined,
  LinkOutlined,
  LoadingOutlined,
  MailOutlined,
  PlusOutlined,
  SendOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import {
  Bad<PERSON>,
  But<PERSON>,
  Card,
  Col,
  Popconfirm,
  Popover,
  Radio,
  Row,
  Space,
  Spin,
  Tabs,
  Typography,
  message,
} from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useModel, useParams } from 'umi';
import ReplyForm from './components/ReplyForm';
import CaseReasonTextUpdateModalForm from './components/CaseReasonTextUpdateModalForm';
import RelatedEmailsList from './components/RelatedEmailsList';
import AttachmentIconIcon from './components/AttachmentIcon';
import OrderSelectModal from '@/pages/Crm/CrmCase/OrderSelectModal';
import ButtonGroup from 'antd/lib/button/button-group';
import { history } from 'umi';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormSwitch } from '@ant-design/pro-form';
import type { Editor as TinyMCEEditor } from 'tinymce';

const EmailDetail: React.FC = () => {
  const { setAppSettings, getDictByCode, getDictOptionsCV } = useModel('app-settings');
  const { initialState } = useModel('@@initialState');

  const [refreshTickMails, setRefreshTickMails] = useState<number>(0);

  useEffect(() => {
    if (initialState?.currentUser) {
      getAppSettings()
        .then((res) => setAppSettings(res))
        .catch(() => Util.error('Failed to fetch app settings. Please try to reload a page!'));
    }
  }, [initialState?.currentUser, setAppSettings]);

  const [loading, setLoading] = useState<boolean>(false);
  const [email, setEmail] = useState<API.Email>();
  // HTML editor ref object.
  const editorRef = useRef<TinyMCEEditor | null>(null);
  const [editorAcLoading, setEditorAcLoading] = useState<boolean>(false);

  // modals
  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false);
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [openCaseSelectModal, setOpenCaseSelectModal] = useState<boolean>(false);
  const [openCaseReasonTextModal, setOpenCaseReasonTextModal] = useState<boolean>(false);
  const [openOrderSelectModal, setOpenOrderSelectModal] = useState<boolean>(false);

  // hooks
  const { emailId } = useParams<any>();

  const loadEmail = useCallback(
    async (params?: any) => {
      if (!emailId) return;
      setLoading(true);
      return getEmail(emailId, { with: 'crmCase,orderDetail,parcel', includeHidden: 1, ...params })
        .then((res) => {
          setEmail(res);
          setRefreshTickMails((prev) => prev + 1);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    },
    [emailId],
  );

  const handleCreateAndLink = (e: any) => {
    if (!emailId) return;
    const hide = message.loading('Creating a new case...', 0);
    addCrmCase({ emailId: emailId })
      .then((res) => {
        message.success('Added successfully.');
        loadEmail();
      })
      .finally(() => hide());
  };

  const handlePullEmails = () => {
    const hide = message.loading('Downloading emails...', 0);
    dsPullEmail({})
      .then((res) => {
        loadEmail();
        message.success('Downloaded the latest emails successfully.');
      })
      .catch(Util.error)
      .finally(() => hide());
  };

  const handleEmailStatusUpdate = async (action: 'status', value?: string) => {
    const msgKv: any = {
      status: 'Email status',
    };
    const hide = message.loading(`Updating ${msgKv[action]}...`, 0);
    return updateEmail({ id: sn(email?.id), [action]: value })
      .then((res) => {
        setEmail((prev) => ({
          ...prev,
          [action]: res[action],
        }));
        message.success('Updated successfully.');
      })
      .catch(Util.error)
      .finally(() => hide());
  };

  const handleStatusReasonUpdate = async (
    action: 'reason' | 'status' | 'reason_text',
    value: Nullable<string | API.CaseReasonTextItemType[]>,
  ) => {
    const msgKv: any = {
      reason: 'Case reason',
      reason_text: 'Case reason notes',
      status: 'Case status',
    };
    const hide = message.loading(`Updating ${msgKv[action]}...`, 0);
    return updateCrmCase(sn(email?.crm_case_id), { [action]: value })
      .then((res) => {
        setEmail((prev) => ({
          ...prev,
          crm_case: { ...prev?.crm_case, [action]: res[action] },
        }));
        message.success('Updated successfully.');
      })
      .catch(Util.error)
      .finally(() => hide());
  };

  useEffect(() => {
    loadEmail();
  }, [loadEmail]);

  /**
   * ---------------------------------------------------------------------
   * Toolbar form
   * ---------------------------------------------------------------------
   */
  const toolbarFormRef = useRef<ProFormInstance>();
  useEffect(() => {
    toolbarFormRef.current?.setFieldValue('is_shown', email?.is_hidden != 1);
  }, [email?.is_hidden]);

  /**
   * Prev / Next navigation in Trademark filter
   * @param dir -1 or 1
   */
  const handleEmailNavigation = (dir: number) => {
    setLoading(true);
    const params = {
      boxes: toolbarFormRef.current?.getFieldValue('boxes'),
      visibility: toolbarFormRef.current?.getFieldValue('visibility'),
    };
    Util.setSfValues('sf_email_detail_toolbar', params);

    getNextEmail(dir, emailId, {
      ...params,
      boxes: typeof params.boxes === 'string' ? [params.boxes] : params.boxes,
      includeHidden: 1,
    })
      .then((res) => {
        if (res) {
          history.push(`/email/detail/${res}`);
        } else {
          message.info('No next or previous email exists!');
        }
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    toolbarFormRef.current?.setFieldsValue(Util.getSfValues('sf_email_detail_toolbar'));
  }, []);

  const order: API.Order | undefined = email?.crm_case?.order;

  // loading possible caseIDs list
  const [crmCases, setCrmCases] = useState<API.CrmCase[]>([]);

  useEffect(() => {
    if (email?.sender)
      if (email.crm_case_id) {
        setCrmCases([]);
      } else {
        getCrmCaseList(
          {
            sender: email?.sender,
          },
          { id: 'descend' },
        )
          .then((res) => {
            console.log(res);
            setCrmCases(res.data);
          })
          .finally(() => setLoading(false));
      }
  }, [email?.crm_case_id, email?.sender]);

  return (
    <PageContainer title={<>Email Detail</>} style={{ position: 'relative' }}>
      <div style={{ position: 'absolute', top: 8, left: 175 }}>
        <ProForm formRef={toolbarFormRef} layout="inline" submitter={false}>
          <Row gutter={8}>
            <Col>
              <ProFormSelect
                name="boxes"
                placeholder={'Box'}
                initialValue={'INBOX'}
                options={[
                  { value: 'INBOX', label: 'Inbox' },
                  { value: 'SENT', label: 'Sent' },
                ]}
              />
            </Col>
            <Col>
              <ProFormSelect
                name="visibility"
                placeholder={'Visibility'}
                initialValue={'shown'}
                options={[
                  { value: 'shown', label: 'Shown only' },
                  { value: 'hidden', label: 'Hidden only' },
                ]}
              />
            </Col>
            <Col flex="auto">
              <ButtonGroup style={{ marginLeft: 12, marginRight: 12 }}>
                <Button
                  onClick={() => handleEmailNavigation(-1)}
                  icon={<ArrowLeftOutlined />}
                  disabled={loading || !email?.id}
                />
                <Button
                  onClick={() => handleEmailNavigation(1)}
                  icon={<ArrowRightOutlined />}
                  disabled={loading || !email?.id}
                />
              </ButtonGroup>
            </Col>
            <Col>
              <ProFormSwitch
                name="is_shown"
                label="Visible?"
                tooltip="Update email visibility."
                disabled={!email?.id}
                fieldProps={{
                  checkedChildren: 'Visible',
                  unCheckedChildren: 'Hidden',
                  onChange(value) {
                    const hide = message.loading('Updating email visibility...', 0);
                    updateEmail({ id: emailId, is_hidden: value ? 0 : 1 })
                      .then((res) => {
                        setEmail((prev) => ({ ...prev, is_hidden: res.is_hidden }));
                      })
                      .catch(Util.error)
                      .finally(() => hide());
                  },
                }}
              />
            </Col>
          </Row>
        </ProForm>
      </div>
      <Spin spinning={loading}>
        <Row gutter={24}>
          <Col xxl={9}>
            <Row gutter={24} style={{ marginBottom: 4 }}>
              <Col flex="150px">
                <Button
                  type="primary"
                  className="btn-green"
                  size="small"
                  onClick={() => window.close()}
                  icon={<CloseOutlined />}
                >
                  Close
                </Button>
              </Col>
              <Col flex="250px">Email Date: {email?.date ? Util.dtToDMYHHMM(email.date) : null}</Col>
              <Col flex="auto">
                <Space size={24}>
                  <label>Case ID: {email?.crm_case_id} </label>
                </Space>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginBottom: 16 }} wrap={false}>
              <Col flex="150px">
                <Button
                  type="primary"
                  size="small"
                  icon={<SendOutlined />}
                  onClick={() => {
                    handleReplyModalVisible(true);
                  }}
                >
                  Reply
                </Button>
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    handleCreateModalVisible(true);
                  }}
                  style={{ marginLeft: 8 }}
                >
                  New
                </Button>
              </Col>
              <Col flex="250px">From: {email?.sender}</Col>
              <Col>
                <Space>
                  <Popconfirm
                    onConfirm={handleCreateAndLink}
                    title={'Are you sure you want to create a new Case for this email?'}
                  >
                    <Button type="primary" size="small" icon={<PlusOutlined />} title="Create a new case and link" />
                  </Popconfirm>
                  <Button
                    type="primary"
                    size="small"
                    className={crmCases.length > 0 ? 'btn-green' : ''}
                    onClick={() => {
                      setOpenCaseSelectModal(true);
                    }}
                    icon={<LinkOutlined />}
                    title="Choose a case"
                  />
                </Space>
              </Col>
              <Col style={{ paddingLeft: 0 }}>
                {crmCases.length > 0 &&
                  crmCases.map((x, ind) => (
                    <span
                      key={x.id}
                      className="cursor-pointer"
                      onClick={() => {
                        updateEmail({ id: emailId, crm_case_id: x.id })
                          .then((res) => {
                            message.success('Updated successfully.');
                            loadEmail();
                          })
                          .catch(Util.error);
                      }}
                    >
                      {ind > 0 ? ', ' : ''}
                      {x.id}
                    </span>
                  ))}
              </Col>
            </Row>
            <Row>
              <Col style={{ margin: '16px 0' }} flex={'1'}>
                <Card
                  size="small"
                  title={
                    <>
                      <MailOutlined />
                      &nbsp; {email?.subject ?? '[No subject]'}
                    </>
                  }
                  extra={
                    <div style={{ fontSize: 16 }}>
                      <AttachmentIconIcon attachments={email?.attachments} />
                    </div>
                  }
                  style={{ width: '100%' }}
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: email?.text_html ? email?.text_html : nl2br(email?.text_plain),
                    }}
                  />
                </Card>
              </Col>
            </Row>
          </Col>
          <Col xxl={7}>
            {/* <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                size="small"
                ghost
                onClick={handleNoActionNeeded}
                icon={<CloseOutlined />}
                title={`Set the default Status into this case and close`}
              >
                Close - No Action needed
              </Button>
            </div> */}
            <div style={{ marginBottom: 16 }}>
              <Row gutter={24} wrap={false}>
                <Col flex={'100px'}>
                  <label>Email Status: </label>
                </Col>
                <Col flex="auto">
                  {getDictOptionsCV(DictType.EmailStatus, 'sort').map((x) => (
                    <Radio
                      key={x.code}
                      value={x.code}
                      checked={x.code == email?.status}
                      onChange={(e) => {
                        handleEmailStatusUpdate('status', e.target.value);
                      }}
                    >
                      {x.label}
                    </Radio>
                  ))}
                </Col>
              </Row>
            </div>
            {email?.crm_case_id && (
              <div style={{ marginBottom: 16 }}>
                <Row gutter={24} wrap={false}>
                  <Col flex={'100px'}>
                    <label>Case Status: </label>
                  </Col>
                  <Col flex="auto">
                    {getDictOptionsCV(DictType.CRMCaseStatus, 'sort').map((x) => (
                      <Radio
                        key={x.code}
                        value={x.code}
                        checked={x.code == email?.crm_case?.status}
                        onChange={(e) => {
                          handleStatusReasonUpdate('status', e.target.value);
                        }}
                      >
                        {x.label}
                      </Radio>
                    ))}
                  </Col>
                </Row>
              </div>
            )}
            {email?.crm_case_id && (
              <div style={{ marginBottom: 16 }}>
                <Row gutter={24} wrap={false}>
                  <Col flex={'100px'}>
                    <label>Case reason:</label>
                  </Col>
                  <Col flex="auto">
                    {Object.values(CaseReason).map((x) => (
                      <Radio
                        key={x}
                        value={x}
                        checked={x == email?.crm_case?.reason}
                        onChange={(e) => {
                          handleStatusReasonUpdate('reason', e.target.value);
                        }}
                      >
                        {x}
                      </Radio>
                    ))}
                    {email?.crm_case?.reason == CaseReason.DamageItems && (
                      <Space size={4}>
                        <Popover
                          title="Damage reason"
                          content={email?.crm_case?.reason_text?.map((x) => (
                            <Row key={x.uid} gutter={12} style={{ width: 300, marginBottom: 6 }}>
                              <Col className="text-sm" span={6}>
                                <Typography.Text copyable>{x.sku}</Typography.Text>
                              </Col>
                              <Col
                                className="text-sm"
                                span={18}
                                dangerouslySetInnerHTML={{
                                  __html: nl2br(x.reason_text),
                                }}
                              />
                            </Row>
                          ))}
                        >
                          <Badge count={email?.crm_case?.reason_text?.length ?? 0} showZero color="#faad14" title="" />
                        </Popover>
                        <Button
                          type="link"
                          icon={<EditOutlined />}
                          title="Add or update damage reason."
                          onClick={() => {
                            setOpenCaseReasonTextModal(true);
                          }}
                        />
                      </Space>
                    )}
                  </Col>
                </Row>
              </div>
            )}
            <div>
              <Row style={{ alignItems: 'center' }}>
                <Col flex="auto">
                  <h3 style={{ margin: 0 }}>Internal Notes of CaseID #{email?.crm_case_id}</h3>
                </Col>
                <Col style={{ justifySelf: 'flex-end' }}>
                  {editorAcLoading && <LoadingOutlined />}
                  <Button
                    type="link"
                    icon={<HighlightOutlined />}
                    title="Append date and your initials."
                    onClick={() => {
                      if (editorRef.current) {
                        editorRef.current.setContent(
                          editorRef.current.getContent() +
                            ' ' +
                            Util.dtToDMY(new Date(), 'DD.MM.') +
                            ' ' +
                            initialState?.currentUser?.initials,
                          {},
                        );
                      }
                    }}
                  />
                </Col>
              </Row>

              <HtmlEditor
                extEditorRef={editorRef}
                initialValue={email?.crm_case?.notes ?? ''}
                hideMenuBar
                height={300}
                enableTextModule
                setLoading={setEditorAcLoading}
              />
              <Button
                type="primary"
                size="small"
                disabled={!email?.crm_case_id}
                onClick={() => {
                  if (email?.crm_case_id) {
                    const hide = message.loading('Updating notes...', 0);
                    updateCrmCase(sn(email?.crm_case_id), { notes: editorRef?.current?.getContent() })
                      .catch(Util.error)
                      .finally(() => hide());
                  }
                }}
                style={{ marginTop: 12, marginLeft: 'auto', display: 'block' }}
              >
                Save
              </Button>
            </div>

            <Row style={{ marginTop: 12 }}>
              <Col flex="50%">
                <label>
                  <Button
                    size="small"
                    type={'primary'}
                    ghost={!!order?.entity_id}
                    onClick={() => {
                      setOpenOrderSelectModal(true);
                    }}
                    title={
                      order?.entity_id ? 'Change an order...' : 'Connect to an order into Case #' + email?.crm_case_id
                    }
                    // style={{ marginRight: 16 }}
                    icon={<EditOutlined />}
                    disabled={!email?.crm_case_id}
                  />
                  &nbsp; Order ID:&nbsp;
                </label>
                {order ? (
                  <>
                    <a
                      href={`/orders/order-detail?entity_id=${order?.entity_id}`}
                      target="_blank"
                      rel="noreferrer"
                      title="Open Order detail page on new tab."
                    >
                      {order?.entity_id}
                    </a>{' '}
                    /{' '}
                    <a
                      href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${
                        order?.entity_id
                      }/`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      {order?.increment_id}
                    </a>
                  </>
                ) : null}
              </Col>
              <Col flex="50%">
                <label>Ordered Date: </label>
                {order ? Util.dtToDMY(order?.created_at) : null}
              </Col>
            </Row>
            <div style={{ clear: 'both' }}>
              <ProTable<API.OrderItem>
                cardProps={{ bodyStyle: { padding: 0 } }}
                bordered
                columns={[
                  {
                    title: 'SKU',
                    dataIndex: 'sku',
                    width: 80,
                    render: (dom, recordIn) => {
                      return (
                        <Typography.Link
                          href={`/item/ean-all-summary?sku=${skuToItemId(recordIn.sku)}_`}
                          target="_blank"
                          copyable
                        >
                          {recordIn.sku}
                        </Typography.Link>
                      );
                    },
                  },
                  { title: 'EAN', dataIndex: ['item_ean', 'ean'], width: 100 },
                  { title: 'Name', dataIndex: ['item_ean', 'ean_text_de', 'name'], width: 100 },
                  { title: 'Qty', dataIndex: 'qty_ordered', align: 'right', width: 50 },
                  {
                    title: 'Net Price',
                    dataIndex: ['price'],
                    align: 'right',
                    width: 80,
                    render: (dom, recordIn) => nf2(recordIn?.price),
                  },
                ]}
                style={{ width: '100%', marginTop: 16 }}
                rowKey="item_id"
                headerTitle={false}
                search={false}
                options={false}
                pagination={false}
                size="small"
                dataSource={email?.crm_case?.order?.mag_order_items ?? []}
                columnEmptyText={''}
              />
            </div>
            {order && email?.crm_case?.parcel?.parcel_no && (
              <div style={{ marginTop: 24 }}>
                <label>
                  <CodepenOutlined /> Tracking No:{' '}
                </label>
                <a
                  href={`${getDictByCode(DictCode.MAG_ADMIN_URL_TRACKING)}${email.crm_case.parcel.parcel_no}`}
                  target="_blank"
                  rel="noreferrer"
                >
                  <Typography.Text copyable={{ text: email.crm_case.parcel.parcel_no }}>
                    {email.crm_case.parcel.parcel_no}
                  </Typography.Text>
                </a>
              </div>
            )}
            {order && (
              <>
                <Tabs
                  defaultActiveKey="shipping"
                  items={[
                    {
                      key: 'invoice',
                      label: 'Invoice address',
                      children: (
                        <>
                          <div>{order.inv_fullname}</div>
                          <div>{fullAddress(order, 'invoice')}</div>
                        </>
                      ),
                    },
                    {
                      key: 'shipping',
                      label: 'Shipping address',
                      children: (
                        <>
                          <div>{order.sa_fullname}</div>
                          <div>{fullAddress(order, 'shipping')}</div>
                        </>
                      ),
                    },
                  ]}
                  tabBarExtraContent={
                    <>
                      {email?.crm_case?.order_id && (
                        <a
                          href={`${getDictByCode(DictCode.MAG_ADMIN_URL_INVOICE)}${email?.crm_case?.order_id}`}
                          target="_blank"
                          rel="noreferrer"
                        >
                          INV
                        </a>
                      )}
                    </>
                  }
                  style={{ marginTop: 24, marginBottom: 24 }}
                />
                {/* <Card size="small" title="Shipping address" style={{ marginTop: 24 }}>
                  <div>{order.sa_fullname}</div>
                  <div>{fullAddress(order)}</div>
                </Card> */}
              </>
            )}
          </Col>
          <Col xxl={8}>
            <RelatedEmailsList caseId={sn(email?.crm_case_id)} refreshTick={refreshTickMails} />
          </Col>
        </Row>
      </Spin>
      <ReplyForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={email}
        onSubmit={async (value) => {
          handlePullEmails();
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      <ReplyForm
        mode="create"
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        initialValues={email}
        onSubmit={async (value) => {
          handlePullEmails();
        }}
        onCancel={() => {
          handleCreateModalVisible(false);
        }}
      />

      <CrmCaseListModal
        values={{
          senderEmail: email?.sender,
          senderName: email?.sender_name,
          emailId: email?.id,

          orderId: email?.crm_case?.order_id,
          incrementId: email?.crm_case?.order?.increment_id,

          caseId: email?.crm_case_id,
        }}
        modalVisible={openCaseSelectModal}
        handleModalVisible={setOpenCaseSelectModal}
        cb={(action, data) => {
          //
          loadEmail();
        }}
      />

      <OrderSelectModal
        modalVisible={openOrderSelectModal}
        handleModalVisible={setOpenOrderSelectModal}
        values={{
          sender: email?.sender,
          order_id: email?.crm_case?.order_id,
          increment_id: order?.increment_id,

          emailId: email?.id,
          caseId: email?.crm_case_id,
        }}
        cb={(action, data) => {
          loadEmail();
        }}
        parentSearchFormRef={undefined}
      />

      <CaseReasonTextUpdateModalForm
        initialValues={email?.crm_case}
        modalVisible={openCaseReasonTextModal}
        handleModalVisible={setOpenCaseReasonTextModal}
        onSubmit={(crmCase) => {
          return handleStatusReasonUpdate('reason_text', crmCase?.reason_text ?? []);
        }}
      />
    </PageContainer>
  );
};

export default EmailDetail;
