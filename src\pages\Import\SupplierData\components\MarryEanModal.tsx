import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { useState } from 'react';
import { useMemo } from 'react';
import React, { useRef } from 'react';
import { ProFormText } from '@ant-design/pro-form';
import { getIboList } from '@/services/foodstore-one/IBO/ibo';
import { Modal, Tag } from 'antd';
import { Col, message, Row, Typography, Button } from 'antd';
import Util, { sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import SPrices from '@/components/SPrices';
import { CloseOutlined } from '@ant-design/icons';
import { getLastMarriedByEan, getMarriedList, updateMarryTwoEANs } from '@/services/foodstore-one/Import/import';
import { debounce } from 'lodash';
import type { DefaultOptionType } from 'antd/lib/select';
import MarriedEansListModal from './MarriedEansListModal';

type HeaderFiltersType = {
  item_name?: string;
  ean_name?: string;
};

type ExtraColumType = {
  headerFilters?: HeaderFiltersType;
  setHeaderFilters: Dispatch<SetStateAction<HeaderFiltersType>>;
};

export type ModalInitialValueType = {
  id: number; // import table ID
  xls_id: number; // ID in imported dynamic table
  imported_ean: string;

  // default filter in IBO table
  ibom_id: number;
  imported_table_name?: string;

  // For pre setting of live filters
  item_name?: string;
  ean_name?: string;
  ibom?: DefaultOptionType;

  // xls values
  i_case_qty?: number;
  i_qty?: number;
};

export type MarryEanModalProps = {
  initialValue: ModalInitialValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onMarriedCallback?: () => Promise<boolean | void>;
};

type ColumnDataType = API.Ibo & ExtraColumType;

const MarryEanModal: React.FC<MarryEanModalProps> = (props) => {
  const { initialValue, modalVisible, handleModalVisible, onMarriedCallback } = props;

  const [headerFilters, setHeaderFilters] = useState<HeaderFiltersType>({
    item_name: '',
    ean_name: '',
  });

  const actionRef = useRef<ActionType>();
  // View married EANs list on modal
  const [openMarriedEANsModal, setOpenMarriedEANsModal] = useState<boolean>(false);

  // last mapping for current imported EAN
  const [lastMarriedData, setLastMarriedData] = useState<API.ImportSupplierMarry>();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearch = useCallback(
    debounce(() => {
      actionRef.current?.reload();
    }, 330),
    [],
  );

  const handleUpdateMarryTwoEANs = useCallback(
    async (iboId: number, eanId: number, ean?: string) => {
      // fields: ModalInitialValueType & { eanSys: string; ibo_id: number },
      const hide = message.loading('Marring 2 EANS: ...', 0);
      updateMarryTwoEANs(initialValue.id, {
        ean: ean,
        ean_id: eanId,
        imported_ean: initialValue.imported_ean,
        xls_id: initialValue.xls_id,
        ibo_id: iboId,
      })
        .then(() => {
          message.success('Successfully linked!');
          actionRef.current?.reload();
          onMarriedCallback?.();
        })
        .catch(Util.error)
        .finally(() => hide());
    },
    [initialValue.id, initialValue.imported_ean, initialValue.xls_id, onMarriedCallback],
  );

  const columns: ProColumns<ColumnDataType>[] = useMemo(() => {
    const newColumns: ProColumns<ColumnDataType>[] = [
      {
        title: 'Item Name',
        dataIndex: ['item_ean', 'item', 'name'],
        children: [
          {
            title: (
              <>
                <ProFormText
                  fieldProps={{
                    // ref: itemNameRef,
                    value: headerFilters?.item_name ?? '',
                    onChange: (e) => {
                      const value = e.target.value;
                      setHeaderFilters((prev) => ({ ...prev, item_name: value }));
                      debouncedHandleSearch();
                    },
                  }}
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </>
            ),
            dataIndex: ['item_ean', 'item', 'name'],
            sorter: false,
            ellipsis: true,
            width: 300,
            render: (dom) => {
              return <>{dom}</>;
            },
          },
        ],
      },
      {
        title: 'EAN Name',
        dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
        width: 300,
        children: [
          {
            title: (
              <>
                <ProFormText
                  fieldProps={{
                    value: headerFilters?.ean_name ?? '',
                    onChange: (e) => {
                      const value = e.target.value;
                      setHeaderFilters((prev) => ({ ...prev, ean_name: value }));
                      debouncedHandleSearch();
                    },
                  }}
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </>
            ),
            dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
            width: 300,
            ellipsis: true,
            render: (dom, record) => {
              const eanName = record?.item_ean?.ean_texts?.[0]?.name;
              return (
                <Typography.Text type={eanName ? undefined : 'warning'}>
                  {eanName ?? record?.item_ean?.item?.name ?? <CloseOutlined style={{ color: '#cc2200' }} />}
                </Typography.Text>
              );
            },
          },
        ],
      },
      {
        title: 'EAN',
        dataIndex: ['item_ean', 'ean'],
        key: 'ean',
        copyable: true,
        width: 150,
      },
      {
        title: 'Single EAN',
        dataIndex: ['item_ean', 'parent', 'ean'],
        copyable: true,
        hideInSearch: true,
        width: 150,
      },
      {
        title: 'SKU',
        dataIndex: ['item_ean', 'sku'],
        ellipsis: true,
        copyable: true,
        width: 80,
      },
      {
        title: 'Price',
        dataIndex: 'price',
        valueType: 'digit',
        align: 'right',
        width: 100,
        render: (dom, record) => {
          const vat = record?.item_ean?.item?.vat?.value || 0;
          return (
            <>
              <Row
                gutter={4}
                /* title="View prices list..."
                className="cursor-pointer"
                onClick={() => {
                  setCurrentRow({ ...record });
                  setShowImportedPrices(true);
                }} */
                style={{ minHeight: 24 }}
              >
                <Col span={12}>
                  <SPrices price={record.price} vat={vat} hideGross />
                </Col>
                <Col span={12}>
                  <SPrices price={(record?.price ?? 0) * (record?.item_ean?.attr_case_qty ?? 0)} vat={vat} hideGross />
                </Col>
              </Row>
            </>
          );
        },
      },
      {
        title: 'Pkg. Qty',
        dataIndex: 'box_qty',
        valueType: 'digit',
        align: 'right',
        width: 80,
        render: (dom, record) => Util.numberFormat(record.box_qty),
      },
      {
        title: 'Pkg. Qty (XLS)',
        dataIndex: 'box_qty',
        valueType: 'digit',
        align: 'right',
        width: 80,
        className: 'c-grey',
        render: (dom, record) => Util.numberFormat(initialValue?.i_case_qty),
      },
      {
        title: 'Qty',
        dataIndex: 'qty',
        valueType: 'digit',
        showSorterTooltip: false,
        tooltip: 'Qty of Single EAN',
        align: 'right',
        width: 80,
        render: (dom, record) => Util.numberFormat(record.qty),
      },
      {
        title: 'Qty (XLS)',
        dataIndex: 'qty',
        valueType: 'digit',
        showSorterTooltip: false,
        tooltip: 'Qty of Single EAN in XLS',
        align: 'right',
        width: 80,
        className: 'c-grey',
        render: (dom, record) => Util.numberFormat(initialValue?.i_qty),
      },
      {
        title: '',
        dataIndex: 'option',
        valueType: 'option',
        width: 60,
        fixed: 'right',
        render: (dom, record) => (
          <Button
            size="small"
            type="primary"
            onClick={() => handleUpdateMarryTwoEANs(sn(record.id), sn(record.ean_id), record.item_ean?.ean)}
          >
            Select
          </Button>
        ),
      },
    ];
    return newColumns;
  }, [
    debouncedHandleSearch,
    handleUpdateMarryTwoEANs,
    headerFilters?.ean_name,
    headerFilters?.item_name,
    initialValue?.i_case_qty,
    initialValue?.i_qty,
  ]);

  useEffect(() => {
    if (modalVisible && initialValue.ibom_id) {
      actionRef.current?.reload();
    }
  }, [modalVisible, initialValue.ibom_id]);

  useEffect(() => {
    if (modalVisible && initialValue.imported_ean) {
      getLastMarriedByEan({ imported_ean: initialValue.imported_ean }).then((res) => setLastMarriedData(res));
    }
  }, [modalVisible, initialValue.imported_ean]);

  return (
    <>
      <Modal
        title={'Select an EAN in IBO ' + initialValue.ibom?.label ?? 'N/A'}
        width="1550px"
        open={modalVisible}
        onCancel={() => handleModalVisible(false)}
        bodyStyle={{ paddingTop: 0 }}
        footer={false}
      >
        <ProTable<ColumnDataType, API.PageParams>
          headerTitle={
            <>
              IBO List
              <Tag style={{ marginLeft: 32 }}>{initialValue.imported_ean}</Tag>
              <span className="text-sm">({initialValue.ean_name})</span>&nbsp;
              {lastMarriedData && (
                <>
                  has been matched to <Tag style={{ marginLeft: 4 }}>{lastMarriedData.ean}&nbsp;</Tag>&nbsp;
                  {lastMarriedData.item_ean?.ean_text_de?.name && (
                    <span className="text-sm">({lastMarriedData.item_ean?.ean_text_de?.name ?? '-'} )</span>
                  )}
                  <span className="text-sm">&nbsp;last time.</span>
                </>
              )}
            </>
          }
          actionRef={actionRef}
          rowKey="id"
          revalidateOnFocus={false}
          options={{ fullScreen: true }}
          sticky
          scroll={{ x: 800 }}
          size="small"
          bordered
          cardProps={{ bodyStyle: { padding: 0 } }}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: 20,
          }}
          search={false}
          request={async (params, sort, filter) => {
            const formValues = {
              ...params,
              ...headerFilters,
              ibom_id: initialValue.ibom_id,
              imported_table_name: initialValue.imported_table_name,
              imported_ean: initialValue.imported_ean,
            };

            if (formValues.ibom_id && formValues.imported_ean && formValues.imported_table_name) {
              return getIboList(formValues, sort, filter).finally(() => {});
            } else return Promise.resolve([]);
          }}
          onRequestError={Util.error}
          columns={columns}
          toolBarRender={() => [
            <Button key="default" type="primary" size="small" ghost onClick={() => setOpenMarriedEANsModal(true)}>
              Show all linked
            </Button>,
          ]}
        />
        <MarriedEansListModal
          modalVisible={openMarriedEANsModal}
          handleModalVisible={setOpenMarriedEANsModal}
          onUnlinkCallback={() => {
            getLastMarriedByEan({ imported_ean: initialValue.imported_ean }).then((res) => setLastMarriedData(res));
            actionRef.current?.reload();
          }}
          initialValue={{}}
        />
      </Modal>
    </>
  );
};

export default MarryEanModal;
