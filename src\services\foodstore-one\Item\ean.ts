/* eslint-disable */
import _ from 'lodash';
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/item/ean';

export const defaultWithGet =
  'item,latestIbo,trademarks,categories,vats,prices,texts,files,iboTotalQty,parentTexts,scrapPrices';

export const EAN_DEFAULT_SUMMARY_WITH =
  'item,categories,trademarks,vats,texts,parentTexts,prices,files,itemCategories,usHashSummary,magInventoryStocksQty,latestIbo,scrapPrices,stockStablesQty,importedCurrentBp,eanTasksCount,eanSuppliers,gdsnItem,magProduct,last_avg_gps';

export const EAN_DEFAULT_PIC_WITH = 'item,vats,texts,parentTexts,files,usHashSummary,eanTasksCount,magFiles,gdsnItem,scrapPrices';

/** get GET /api/item/ean */
export async function getEanList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.Ean> & { imports?: API.Import[]; }>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: _.isEmpty(sort)
        ? {
          'item.name': 'ascend',
          attr_case_qty: 'ascend',
        }
        : sort,
      filter_detail: filter,
      with:
        params?.with ??
        defaultWithGet +
        ',itemCategories,usHashSummary,magInventoryStocksQty,stockStablesQty,importedCurrentBp,eanTasksCount,eanSuppliers',
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    imports: res.message?.imports,
  }));
}

export type ItemInFileType = API.ZImportSupplierXlsEan & {
  item_ean?: API.Ean;
  ean_price_stable?: API.EanPriceStable;
}
/** 
 * Get all Items from supplier XLS files
 * 
 * GET /api/item/ean/getAllItemFilesList 
 * 
 * */
export async function getAllItemFilesList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<ItemInFileType> & { imports?: API.Import[]; }>>(`${urlPrefix}/getAllItemFilesList`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: _.isEmpty(sort)
        ? {
          'item.name': 'ascend',
          attr_case_qty: 'ascend',
        }
        : sort,
      filter_detail: filter,
      with:
        params?.with ??
        defaultWithGet +
        ',itemCategories,usHashSummary,magInventoryStocksQty,stockStablesQty,importedCurrentBp,eanTasksCount,eanSuppliers',
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    imports: res.message?.imports,
  }));
}

export type EanGPInfoType1 = {
  // AVG Gps
  gp_single_gp_avg_365?: number;
  gp_single_qty_ordered_sum_365?: number;
  gp_single_gp_avg_30?: number;
  gp_single_qty_ordered_sum_30?: number;
  gp_single_cturnover_30?: number;
  gp_single_cturnover_365?: number;
  gp_multi_cturnover_30?: number;

  gp_multi_gp_avg_365?: number;
  gp_multi_qty_ordered_sum_365?: number;
  gp_multi_gp_avg_30?: number;
  gp_multi_qty_ordered_sum_30?: number;
  gp_multi_cturnover_365?: number;

  // ggp related:
  gp_sum_30?: number;
  order_count_30?: number;
  ggp_avg_30?: number;
  gp_sum_365?: number;
  order_count_365?: number;
  ggp_avg_365?: number;

  gogp_sum_30?: number;
  gogp_avg_30?: number;
  gogp_sum_365?: number;
  gogp_avg_365?: number;
}

export type EanPriceRecordType = API.Ean & API.OrderItem & Record<string, any> & EanGPInfoType1;

/**
 * Get EANs list with prices columns.
 *
 * GET /api/item/ean/getAllEanPricesList
 *
 */
export async function getAllEanPricesList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<
    API.ResultObject<
      API.PaginatedResult<EanPriceRecordType> & {
        imports?: API.Import[];
        summary?: EanPriceRecordType;
        gfcPriceSettings?: Record<any, any>;
      }
    >
  >(`${urlPrefix}/getAllEanPricesList`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    imports: res.message.imports ?? [],
    gfcPriceSettings: res.message?.gfcPriceSettings ?? {},
    summary: res.message.summary ?? {},
  }));
}

/**
 * Get EANs list with prices columns.
 *
 * GET /api/item/ean/getAllEanPriceHistoryList
 *
 */
export async function getAllEanPriceHistoryList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<EanPriceRecordType>>>(`${urlPrefix}/getAllEanPriceHistoryList`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** get GET /api/item/ean */
export async function getEanDetailByListMode(params: API.PageParamsExt) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 1,
      with: params.with ?? defaultWithGet,
    },
    withToken: true,
  }).then((res) =>
    res.code === 200 && res?.message?.data && res?.message?.data.length > 0 ? res?.message?.data[0] : {},
  );
}

/** get GET /api/item/ean/getUvpPrice */
export async function getUvpPrice(params: API.PageParamsExt) {
  return request<API.ResultObject<API.Ean>>(`${urlPrefix}/getUvpPrice`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 1,
      with: params.with ?? defaultWithGet,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** get GET /api/item/ean/getLatestIbos */
export async function getLatestIbos(params: API.PageParamsExt) {
  return request<API.ResultObject<API.Ibo[]>>(`${urlPrefix}/getLatestIbos`, {
    method: 'GET',
    params: {
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

export async function exportEanList(params: API.PageParamsExt & { mode?: string }, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}/export-core`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(
        _.isEmpty(sort)
          ? {
            'item.name': 'ascend',
            attr_case_qty: 'ascend',
          }
          : sort,
      ),
      filter_detail: JSON.stringify(filter),
      with: defaultWithGet,
    },
    withToken: true,
  }).then((res) => ({
    data: res.message,
    success: res.status == 'success',
  }));
}

export async function exportEanListAlt(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}/export`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(
        _.isEmpty(sort)
          ? {
            'item.name': 'ascend',
            attr_case_qty: 'ascend',
          }
          : sort,
      ),
      filter_detail: JSON.stringify(filter),
      with: defaultWithGet,
    },
    withToken: true,
  }).then((res) => ({
    data: res.message,
    success: res.status == 'success',
  }));
}

export async function exportEanPriceList(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}/export-price`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(
        _.isEmpty(sort)
          ? {
            'item.name': 'ascend',
            attr_case_qty: 'ascend',
          }
          : sort,
      ),
      filter_detail: JSON.stringify(filter),
      with: 'trademarks,categories,vats,prices,texts,files',
    },
    withToken: true,
  }).then((res) => ({
    data: res.message,
    success: res.status == 'success',
  }));
}

export async function exportEanWithCodeList(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}/export-with-code`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(
        _.isEmpty(sort)
          ? {
            'item.name': 'ascend',
            attr_case_qty: 'ascend',
          }
          : sort,
      ),
      filter_detail: JSON.stringify(filter),
      with: 'trademarks,categories,vats,prices,texts,files',
    },
    withToken: true,
  }).then((res) => ({
    data: res.message,
    success: res.status == 'success',
  }));
}

/** put PUT /api/item/ean/{id} */
export async function updateEan(data: API.Ean, options?: Record<string, any>) {
  return request<API.Ean>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/item/ean */
export async function updateEanAll(data: { item_eans: API.Ean[] }, options?: Record<string, any>) {
  return request(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/item/ean/batch */
export async function updateEanBatch(
  data: { mode: string; ids: number[]; data: Array<Partial<API.Ean>> } & UpdateGdsnDataType & { stockStatus?: number },
  options?: Record<string, any>,
) {
  return request(`${urlPrefix}/batch`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/item/ean/category-batch */
export async function updateEanCategoriesBatch(
  data: { mode: string; ids: number[]; data: Array<Partial<API.Ean>> },
  options?: Record<string, any>,
) {
  return request(`${urlPrefix}/category-batch`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/item/ean/ean/{ean} */
export async function updateEanByEan(ean?: string, data?: API.Ean, options?: Record<string, any>) {
  return request<API.Ean>(`${urlPrefix}/ean/${ean}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/item/ean/{id}/attribute */
export async function updateEanAttribute(data: API.Ean, options?: Record<string, any>) {
  const url = `${urlPrefix}/` + (data instanceof FormData ? data.get('id') : data.id) + '/attribute';
  const config: any = {
    method: data instanceof FormData ? 'POST' : 'PUT',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config['body'] = data;
  } else {
    config['data'] = data;
  }

  return request<API.BaseResult>(url, config).then((res: any) => res.message);
}

export type UpdateGdsnDataType = {
  isPriceOverride?: boolean;
  supplierXlsFileId?: number;
  gdsnUpdateMode?: string | '1' | '2' | '3';
};
/** post POST /api/item/ean/{id}/attribute-partial */
export async function updateEanAttributePartial(
  data: API.Ean & { supplier_id?: number; product_no?: string } & UpdateGdsnDataType,
  options?: Record<string, any>,
) {
  const url = `${urlPrefix}/` + (data instanceof FormData ? data.get('id') : data.id) + '/attribute-partial';
  const config: any = {
    method: data instanceof FormData ? 'POST' : 'PUT',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config['body'] = data;
  } else {
    config['data'] = data;
  }

  return request<API.ResultObject<API.Ean>>(url, config).then((res: any) => res.message);
}

/** 
 * Create an EAN.
 * 
 * POST /api/item/ean */
export async function addEan(data: API.Ean, options?: Record<string, any>) {
  return request<API.ResultObject<API.Ean>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * Create an EAN in a simple way.
 * 
 * POST /api/item/ean/createSimple */
export async function addEanSimple(data: API.Ean, options?: Record<string, any>) {
  return request<API.ResultObject<API.Ean>>(`${urlPrefix}/createSimple`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** delete DELETE /api/item/ean */
export async function deleteEan(options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** delete DELETE /api/item/ean */
export async function deleteEanFile(options?: Record<string, any>) {
  return request<Record<string, any>>(
    `${urlPrefix}/${options ? options['id'] : ''}/${options ? options['fileId'] : ''}`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}

/** delete DELETE /api/item/ean */
export async function deleteAttributeFile(options?: Record<string, any>) {
  return request<Record<string, any>>(
    `${urlPrefix}/${options ? options['id'] : ''}/${options ? options['fileId'] : ''}`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}

/**
 * get GET /api/item/ean
 *
 * get the autocomplete lists.
 *
 */
export async function getEanACList(params: { [key: string]: string | number }, sort?: any): Promise<API.Ean[]> {
  return request<API.BaseResult>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      sort_detail: JSON.stringify(sort ?? { ean: 'ascend' }),
    },
    withToken: true,
  }).then((res) => res.message);
}

/**
 * get GET /api/item/ean/get *
 * get the EAN detail with item info.
 */
export async function getEanDetail(params: { [key: string]: any }) {
  return request<API.ResultObject<API.Ean>>(`${urlPrefix}/get`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/**
 * @deprecated Later we can remove
 * 
 * get GET /api/item/ean/getOneOrImportedEan *
 * get the EAN detail with item info or EAN from import_supplier_ean.
 */
export async function getEanDetailOrImportedEan(params: { [key: string]: string }) {
  return request<DefaultOptionType>(`${urlPrefix}/getOneOrImportedEan`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/**
 * get GET /api/item/ean/getOneOrGdsnEan
 *
 * get the EAN detail with item info or GDSN EAN from GDSN EAN service.
 */
export async function getEanDetailOrGdsnEan(params: { [key: string]: string }) {
  return request<DefaultOptionType>(`${urlPrefix}/getOneOrGdsnEan`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/**
 * get GET /api/item/ean/searchEans
 *
 * get the EAN detail with item info or GDSN EAN from GDSN EAN service.
 */
export async function searchEans(params: { [key: string]: string }) {
  return request<API.ResultObject<API.Ean[]>>(`${urlPrefix}/searchEans`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

export async function searchEansOptions(params: { [key: string]: string }) {
  return searchEans(params).then((res) => {
    return res.map((x) => ({
      ...x,
      value: x.id,
      lable: `${x.sku} | ${x.ean_text_de?.name ?? x.item?.name}`,
    }));
  });
}



export async function getEanWithAllImages(id: string, params?: { [key: string]: string }) {
  return request<DefaultOptionType>(`${urlPrefix}/${id}/images-all`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/**
 * get PUT /api/item/ean/{id}/images *
 * get the EAN detail with item info.
 */
export async function updateSysImage(id: number, data?: { [key: string]: string }) {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/images`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/**
 * get POST /api/item/ean/{id}/import-parent-images *
 * import parent EAN's images into selected EAN.
 */
export async function importParentImage(id: number, data?: { [key: string]: string }) {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/import-parent-images`, {
    method: 'POST',
    data: data,
  }).then((res) => res.message);
}

/** post POST /api/item/ean/{id}/us-image/{fileId} */
export async function usSetSmallImage(id: number, fileId: number, data?: Record<string, any>) {
  return request<API.Ean>(`${urlPrefix}/${id}/us-image/${fileId}`, {
    method: 'POST',
    data,
  });
}

/**
 * GET /api/item/ean/{sku}/ds/product *
 *
 */
export async function getShopProduct(sku: string, params?: { [key: string]: string }) {
  return request<API.AppApiResponse>(`${urlPrefix}/${sku}/ds/product`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/**
 * PUT /api/item/ean/{sku}/us/product-media/{id} *
 */
export async function updateShopProductMedia(sku: string, id: number, data?: { [key: string]: string }) {
  return request<API.AppApiResponse>(`${urlPrefix}/${sku}/us/product-media/${id}`, {
    method: 'PUT',
    data,
  }).then((res) => res.message);
}

/**
 * get PUT /api/item/ean/{sku}/us/product-media/{id} *
 * get the EAN detail with item info.
 */
export async function deleteShopProductMedia(sku: string, id: number) {
  return request<API.AppApiResponse>(`${urlPrefix}/${sku}/us/product-media/${id}`, {
    method: 'DELETE',
  }).then((res) => res.message);
}

/** post POST /api/item/ean/{id}/us-images */
export async function usAllImages(id: number, data?: Record<string, any>): Promise<Shop.Product> {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/us-images`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}

/** get POST /api/item/ean/{id}/ds/custom-attribute */
export async function dsGetCustomAttribute(id: number, data?: Record<string, any>): Promise<any> {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/ds/custom-attribute`, {
    method: 'GET',
    params: data,
    withToken: true,
  }).then((res) => res.message);
}

/** post POST /api/item/ean/{id}/us/product-full */
export async function usProductFull(id: number, data?: Record<string, any>): Promise<API.Ean> {
  /* return new Promise((resolve) => {
    setTimeout(() => resolve({}), 20000);
  }); */

  return request<API.AppApiResponse>(`${urlPrefix}/${id}/us/product-full`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}

/** post POST /api/item/ean/{id}/us/product-price */
export async function usProductPrice(id: number, data?: Record<string, any>): Promise<API.Ean> {
  /* return new Promise((resolve) => {
    setTimeout(() => resolve({}), 20000);
  }); */

  return request<API.AppApiResponse>(`${urlPrefix}/${id}/us/product-price`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}

/** post POST /api/item/ean/{sku}/us/product */
export async function usProduct(sku: string, data?: Record<string, any>): Promise<API.Ean> {
  return request<API.AppApiResponse>(`${urlPrefix}/${sku}/us/product`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}

/** post POST /api/item/ean/us/product-full-batch */
/* export async function usProductFullBatch(id: number, data?: Record<string, any>): Promise<API.Ean> {
  return request<API.AppApiResponse>(`${urlPrefix}/us/product-full-batch`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
} */

/**
 * download product images from Magento
 *
 * PUT /api/item/ean/ds/product-media
 */
export async function dsProductImages(data?: { ids?: number[]; skus?: string[] }) {
  return request<API.AppApiResponse>(`${urlPrefix}/ds/product-media`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/**
 * download product images from Magento
 *
 * PUT /api/item/ean/ds/product-media-info
 */
export async function dsProductImagesInfo(data?: { ids?: number[]; skus?: string[] }) {
  return request<API.AppApiResponse>(`${urlPrefix}/ds/product-media-info`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/**
 * down syncing product base prices from Magento
 *
 * PUT /api/item/ean/ds/product-media
 */
export async function dsProductBasePrices(data?: any) {
  return request<API.AppApiResponse>(`${urlPrefix}/ds/product-prices`, {
    method: 'PUT',
    data,
  }).then((res) => res.message);
}


/**
 * download product images from external URL.
 *
 * PUT /api/item/ean/ds/external-product-media
 */
export async function dsExternalProductImage(eanId: number, url: string) {
  return request<API.ResultObject<API.File>>(`${urlPrefix}/ds/external-product-media`, {
    method: 'PUT',
    data: { eanId, url },
  }).then((res) => res.message);
}

/** post POST /api/item/ean/upload-files */
export async function uploadFiles(data: any, options?: Record<string, any>) {
  const url = `${urlPrefix}/upload-files`;
  const config: any = {
    method: 'POST',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config['body'] = data;
  } else {
    config['data'] = data;
  }

  return request<API.BaseResult>(url, config).then((res: any) => res.message);
}

/**
 * PUT /api/item/ean/{id}/image/{fileId} *
 *
 */
export async function updateImageType(
  id?: number,
  fileId?: number,
  data?: {
    isRemove?: boolean;
    type: string;
  },
): Promise<API.File[]> {
  return request<API.ResultObject<API.File[]>>(`${urlPrefix}/${id}/image/${fileId}`, {
    method: 'PUT',
    data: data,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * PUT /api/item/ean/{id}/image-sort/{fileId} *
 *
 */
export async function updateImageSort(
  id?: number,
  fileId?: number,
  data?: {
    index?: number;
    orgIndex?: number;
  },
) {
  return request<API.AppApiResponse>(`${urlPrefix}/${id}/image-sort/${fileId}`, {
    method: 'PUT',
    data: data,
    paramsSerializer,
  }).then((res) => res.message);
}


/**
 * get  EANs info from EAN Service API
 *
 * GET /api/item/ean/getGdsnMessageItemList */
export async function getGdsnMessageItemList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultList<API.GdsnMessageItem>>(`${urlPrefix}/getGdsnMessageItemList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * get  ean details from GDSN
 * 
 * GET /api/item/ean/{ean}/getGdsnItemEan */
export async function getGdsnItemEan(ean: string, params?: Record<string, any>) {
  return request<API.ResultObject<API.ItemEanGdsn>>(`${urlPrefix}/${ean}/getGdsnItemEan`, {
    method: 'GET',
    params,
    withToken: true,
  }).then((res) => res.message);
}


/** get POST /api/item/ean/getGdsnMessageItemXml/{id}/xml */
export async function getGdsnMessageItemXml(msgId: number, params?: Record<string, any>) {
  return request<API.ResultObject<{ xml?: string }>>(`${urlPrefix}/getGdsnMessageItemXml/${msgId}/xml`, {
    method: 'GET',
    params,
    withToken: true,
  }).then((res) => res.message);
}


/**
 * get  messages (out) from EAN Service API
 *
 * GET /api/item/ean/getGdsnMessageList */
export async function getGdsnMessageProviderList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultList<API.GdsnMessageItem>>(`${urlPrefix}/getGdsnMessageList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/**
 * get  messages (out) from EAN Service API
 *
 * GET /api/item/ean/getGdsnSourceProviderList */
export async function getGdsnSourceProviderList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultList<API.GdsnMessageItem>>(`${urlPrefix}/getGdsnSourceProviderList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/**
 * Create  messages (out) from EAN Service API
 *
 * POST /api/item/ean/createGdsnSubscription */
export async function createGdsnSubscription(data: { provider_gln: string }) {
  return request<API.ResultObject<API.GdsnMessage>>(`${urlPrefix}/createGdsnSubscription`, {
    method: 'POST',
    data: data,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * GET /api/item/ean/nextEan
 *
 * Get next SKU No by current SKU and direction.
 *
 * param dir -1 or 1
 */
export async function getNextSku(dir: number, sku?: string, params?: any): Promise<string> {
  return request<API.ResultObject<string>>(`${urlPrefix}/nextEan`, {
    method: 'GET',
    params: {
      dir,
      sku: sku,
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}
