import React, { useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Popconfirm } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';

import Util from '@/util';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import CreateForm from './components/CreateForm';
import type { FormValueType } from './components/CreateForm';
import {
  getSysImportRwColMapList,
  createSysImportRwColMap,
  updateSysImportRwColMap,
  deleteSysImportRwColMap,
} from '@/services/foodstore-one/BasicData/sys-import-rw-col-map';

const SysImportRwColMapList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [editingRecord, setEditingRecord] = useState<API.SysImportRwColMap | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.SysImportRwColMap[]>([]);

  const handleCreate = async (fields: FormValueType) => {
    const hide = message.loading('Creating...');
    try {
      await createSysImportRwColMap(fields);
      hide();
      message.success('Created successfully');
      actionRef.current?.reload();
      return true;
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  const handleUpdate = async (fields: FormValueType) => {
    const hide = message.loading('Updating...');
    try {
      if (!editingRecord?.id) {
        throw new Error('No record ID found');
      }
      await updateSysImportRwColMap(editingRecord.id, fields);
      hide();
      message.success('Updated successfully');
      actionRef.current?.reload();
      return true;
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  const handleDelete = async (record: API.SysImportRwColMap) => {
    const hide = message.loading('Deleting...');
    try {
      if (!record.id) {
        throw new Error('No record ID found');
      }
      await deleteSysImportRwColMap(record.id);
      hide();
      message.success('Deleted successfully');
      actionRef.current?.reload();
    } catch (error) {
      hide();
      Util.error(error);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRows.length === 0) {
      message.warning('Please select records to delete');
      return;
    }

    const hide = message.loading('Deleting...');
    try {
      const ids = selectedRows.map((row) => row.id).filter((id) => id !== undefined);
      await Promise.all(ids.map((id) => deleteSysImportRwColMap(id!)));
      hide();
      message.success('Deleted successfully');
      setSelectedRows([]);
      actionRef.current?.reload();
    } catch (error) {
      hide();
      Util.error(error);
    }
  };

  const columns: ProColumns<API.SysImportRwColMap>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: 'Supplier Name (XLS)',
      dataIndex: 'supplier_name_xls',
      sorter: true,
      copyable: true,
      ellipsis: true,
    },
    {
      title: 'Supplier Name (System)',
      dataIndex: 'supplier_name',
      sorter: true,
      ellipsis: true,
    },
    {
      title: 'Supplier Add',
      dataIndex: 'supplier_add',
      copyable: true,
    },
    {
      title: 'Actions',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => {
            setEditingRecord(record);
            handleCreateModalVisible(true);
          }}
        />,
        <Popconfirm
          key="delete"
          title="Are you sure you want to delete this record?"
          onConfirm={() => handleDelete(record)}
          okText="Yes"
          cancelText="No"
        >
          <Button type="link" size="small" danger icon={<DeleteOutlined />} />
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.SysImportRwColMap, API.PageParams>
        headerTitle="Import RW Column Mapping"
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setEditingRecord(undefined);
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        tableAlertRender={false}
        request={getSysImportRwColMapList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      <CreateForm
        onSubmit={editingRecord ? handleUpdate : handleCreate}
        onCancel={() => {
          handleCreateModalVisible(false);
          setEditingRecord(undefined);
        }}
        createModalVisible={createModalVisible}
        values={editingRecord}
      />

      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRows.length}
              </a>{' '}
              map &nbsp;&nbsp;
            </div>
          }
        >
          <Popconfirm
            title={<>Are you sure you want to delete selected maps?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleBatchDelete();
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}
    </PageContainer>
  );
};

export default SysImportRwColMapList;
