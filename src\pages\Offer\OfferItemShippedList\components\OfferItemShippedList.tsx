import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { message, Space, Popconfirm, Button } from 'antd';
import React, { useRef, useEffect, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, sn } from '@/util';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import { DefaultOptionType } from 'antd/lib/select';
import { deleteOfferItemShipped, getOfferItemShippedList } from '@/services/foodstore-one/Offer/offer-item-shipped';
import UpdateForm from './UpdateFormMobile';

export type SearchFormValueType = Partial<API.OfferItemShipped> & {
  includeSubTable?: boolean;
  trademark?: DefaultOptionType;
};

export type OfferItemShippedListType = {
  item_id?: number;
  offer_item_id?: number;

  reloadTick?: number;
};

/**
 *
 * @param props
 * @returns
 */
const OfferItemShippedList: React.FC<OfferItemShippedListType> = ({ offer_item_id, item_id, reloadTick }) => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  // Update functionality
  const [currentRow, setCurrentRow] = useState<API.OfferItemShipped>();
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<API.OfferItemShipped>[] = [
    {
      title: 'Qty / Case',
      dataIndex: ['case_qty'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },

    {
      title: 'Qty',
      dataIndex: ['qty_pcs'],
      width: 60,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record.qty) * sn(record.case_qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 100,
      align: 'center',
      showSorterTooltip: false,
      sorter: true,
      render: (__, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'WA No',
      dataIndex: ['wa_no'],
      width: 65,
      className: 'bl2',
      showSorterTooltip: false,
      sorter: true,
    },
    {
      title: 'WA Date',
      dataIndex: 'wa_date',
      valueType: 'date',
      search: false,
      width: 100,
      align: 'center',
      showSorterTooltip: false,
      sorter: true,
      render: (dom, record) => Util.dtToDMY(record.wa_date),
    },
    {
      title: 'Comment',
      dataIndex: 'note',
      width: 200,
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 100,
      align: 'center',
      showSorterTooltip: false,
      defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      render: (_, record) => [
        <Button
          type="primary"
          key="edit"
          icon={<EditOutlined />}
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({ ...record });
          }}
        />,
        <Popconfirm
          key="delete"
          title="Are you sure you want to delete?"
          okButtonProps={{ size: 'large' }}
          cancelButtonProps={{ size: 'large' }}
          onConfirm={() => {
            const hide = message.loading('Deleting...', 0);
            deleteOfferItemShipped(sn(record.id))
              .then((res) => {
                message.success('Deleted successfully.');
                actionRef.current?.reload();
              })
              .catch(Util.error)
              .finally(hide);
          }}
        >
          <Button type="default" icon={<DeleteOutlined />} danger />
        </Popconfirm>,
      ],
    },
  ];

  useEffect(() => {
    if (reloadTick) {
      actionRef.current?.reload();
    }
  }, [reloadTick]);

  return (
    <>
      <ProTable<API.OfferItemShipped, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Offer Item's Shipped List</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: false, setting: false }}
        size="large"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        toolBarRender={() => []}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            offer_item_id,
            item_id,
            with: 'itemEan,itemEan.eanTextDe,offerItem,item',
          };

          return getOfferItemShippedList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </>
  );
};

export default OfferItemShippedList;
