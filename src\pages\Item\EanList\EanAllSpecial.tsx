import {
  CheckCircleOutlined,
  CheckSquareOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  EditTwoTone,
  ExpandOutlined,
  FileTextOutlined,
  LinkOutlined,
  LoadingOutlined,
  PictureOutlined,
  SaveOutlined,
  SnippetsOutlined,
  SyncOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Col, Drawer, Modal, Progress, Row } from 'antd';
import { Card } from 'antd';
import { Button, message, Dropdown, Space, Menu, Popconfirm, Typography } from 'antd';
import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ColumnsState } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateAttributeForm from './components/UpdateAttributeForm';

import Util, { ni, sn } from '@/util';
import CreateForm from './components/CreateForm';
import WebsiteIcons from './components/WebsiteIcons';
import {
  getEanList,
  deleteEan,
  exportEanList,
  exportEanListAlt,
  exportEanPriceList,
  dsGetCustomAttribute,
  usProductFull,
  EAN_DEFAULT_SUMMARY_WITH,
} from '@/services/foodstore-one/Item/ean';
import { ItemEANStatus, ScrapSystemIds } from '@/constants';
import { DEFAULT_PER_PAGE_PAGINATION, ItemEANStatusOptions } from '@/constants';
import UpdateCategoriesForm from './components/UpdateCategoriesForm';
import type { DataNode } from 'antd/lib/tree';
import { getCategoryList } from '@/services/foodstore-one/Item/category';
import UpdateTextsForm from './components/UpdateTextsForm';
import UpdatePicturesForm from './components/UpdatePicturesForm';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
import * as UpdateItemForm from '../ItemList/components/UpdateForm';
import { IRouteComponentProps, useLocation, useModel } from 'umi';
import SocialLinks from './components/SocialIcons';
import UpdateCategoriesFormBulk from './components/UpdateCategoriesFormBulk';
import UpdateAttributesFormBulk from './components/UpdateAttributesFormBulk';
import StockStableQtyModal from './components/StockStableQtyModal';
import { scrapWoSPrice } from '@/services/foodstore-one/Scrap/scrap-price';
import SPrices from '@/components/SPrices';
import ImportedPrices from './components/ImportedPrices';
import UpdatePriceAttributeForm from './components/UpdatePriceAttributeForm';
import UpdateVatFormBulk from './components/UpdateVatFormBulk';
import type { ResizeCallbackData } from 'react-resizable';
import { ResizableTitle } from './EanAllPic';
import EanTasksModals from './components/EanTasksModal';
import useModalNavigation from './hooks/useModalNavigation';
import usePageContainerTitle from './hooks/usePageContainerTitle';
import SProFormDigit from '@/components/SProFormDigit';
import ExpDate from '@/pages/Report/Order/components/ExpDate';
import UpdateLabelAndBadgeFormBulk from './components/UpdateLabelAndBadgeFormBulk';
import useTrademarkFormFilter, { TrademarkChangeCallbackHandlerTypeParamType } from './hooks/useTrademarkFormFilter';
import useIbomOptions from '@/hooks/BasicData/useIbomOptions';
import { updateStockStableStatusBatch } from '@/services/foodstore-one/Stock/stock-stable';
import EanFilesComp from '@/components/EanFilesComp';

/**
 *  Delete node
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.Ean[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEan({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!');
    return false;
  }
};

export const TaskIcon: React.FC<{ count?: number }> = ({ count }) => {
  return <CheckSquareOutlined style={{ color: count ? 'red' : 'lightgrey' }} />;
};

export type SearchFormValueType = Partial<API.Ean> & {
  special_filters?: {
    min_exp_date_base?: number;
    op1: 'and' | 'or';
    discounts: ('all' | 'set' | 'not_set')[];
    op2: 'and' | 'or';
    badges: ('all' | 'set' | 'not_set')[];
  };
};
/* 
const getColClassByPrices = (
  record: API.Ean,
  priceTypes?: API.PriceType[],
  expectedType?: string | number,
  okClassName?: string,
) => {
  const vat = sn(record.item?.vat?.value || 0);
  const priceLists: any = {};
  let minValue = Number.MAX_VALUE;
  let typeWithMinValue = null;
  ScrapSystemIds.forEach((scrapName) => {
    const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });
    if (scrapPriceObj) {
      const price = casePrice(scrapPriceObj?.price, record.attr_case_qty);
      if (price) {
        priceLists[scrapName] = price;
        if (minValue > price) {
          minValue = price;
          typeWithMinValue = scrapName;
        }
      }
    }
  });
  (priceTypes ?? [])
    .filter((x) => x.id == 1)
    .forEach((pt) => {
      let price = sn(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0));
      price = casePrice(price, record.attr_case_qty) * (1 + vat / 100);
      if (price) {
        priceLists[`${pt.id}`] = price;
        if (minValue > price) {
          minValue = price;
          typeWithMinValue = `${pt.id}`;
        }
      }
    });

  // console.log(priceLists, expectedType, typeWithMinValue);

  if (Object.keys(priceLists).length >= 2 && typeWithMinValue == expectedType) {
    // console.log(okClassName ?? 'bg-light-green');
    return okClassName ?? 'bg-green3';
  }
  return '';
}; */

const EanAllSpecial: React.FC<IRouteComponentProps> = (eanComponentProps) => {
  const eanTypeProp = 'default';
  const { appSettings } = useModel('app-settings');
  const { priceTypes, dict } = appSettings;

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);
  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  // batch
  const [visibleUpdateVatFormBulk, handleVisibleUpdateVatFormBulk] = useState<boolean>(false);
  const [batchUpSyncModalVisible, handleBatchUpSyncModalVisible] = useState<boolean>(false);
  const [batchUpSyncProgress, setBatchUpSyncProgress] = useState(0);
  const [batchModalData, setBatchModalData] = useState<{ title: string; desc?: string }>({
    title: 'Batch Up Syncing...',
    desc: 'Batch UpSync is in progress. Please wait...',
  });

  const [showDetail, setShowDetail] = useState<boolean>(false);

  // bulk updates
  const [visibleUpdateCategoriesFormBulk, handleVisibleUpdateCategoriesFormBulk] = useState<boolean>(false);
  const [visibleUpdateAttributesFormBulk, handleVisibleUpdateAttributesFormBulk] = useState<boolean>(false);
  const [visibleUpdateBadgesFormBulk, handleVisibleUpdateBadgesFormBulk] = useState<boolean>(false);

  // Ean Tasks model
  const [visibleEanTasksModal, setVisibleEanTasksModal] = useState<boolean>(false);

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});
  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const [selectedRows, setSelectedRows] = useState<API.Ean[]>([]);

  // datasource for inline editing
  const [datasource, setDatasource] = useState<API.Ean[]>([]);

  // column state management
  // table column states
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>(() => {
    return {
      item_vat: { show: false },
      ean: { show: false },
      parent_ean: { show: false },
    };
  });

  // hook for modal navigation
  // ------------------------------------------------------------- //
  const { handleNavigation } = useModalNavigation(datasource, {
    item: handleUpdateItemModalVisible,
    attribute: handleUpdateModalVisible,
    picture: handleUpdatePicturesModalVisible,
    price: handleUpdatePricesModalVisible,
    text: handleUpdateTextsModalVisible,
    setCurrentRow,
  });

  // ibom filter
  const { ibom, formElements: formElementsIbom } = useIbomOptions(undefined, searchFormRef);

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
    ibom_id: ibom?.id,
  });

  const [loadingExport, setLoadingExport] = useState(false);
  const location: any = useLocation();

  const handleTextsClick = (record: API.Ean) => {
    setCurrentRow({ ...record });
    handleUpdateTextsModalVisible(true);
  };

  const [columns, setColumns] = useState<ProColumns<API.Ean>[]>([]);
  const pricesColDefs = useMemo<ProColumns<API.Ean>[]>(
    () =>
      priceTypes.map(
        (pt): ProColumns<API.Ean> => ({
          title: pt.name,
          dataIndex: ['ean_prices', pt?.id ?? 0],
          valueType: 'digit',
          sorter: false,
          align: 'center',
          width: 100,
          hideInSearch: true,
          className: 'cursor-pointer',
          shouldCellUpdate(record, prevRecord) {
            return (
              !_.isEqual(record.ean_prices, prevRecord.ean_prices) ||
              !_.isEqual(record.parent?.ean_prices, prevRecord.parent?.ean_prices)
            );
          },
          render: (dom, record) => {
            const vat = record.item?.vat?.value || 0;
            /* const priceSingle = Util.safeNumber(
              _.get(
                _.find(record?.parent?.ean_prices, { price_type_id: pt.id }),
                'price',
                0,
              ).toFixed(2),
            ); */
            const priceSingle =
              Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0)) /
              (record?.attr_case_qty ? record?.attr_case_qty : 1);
            const price = Util.safeNumber(
              _.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0).toFixed(2),
            );
            return (
              <Row gutter={4}>
                <Col span={12}>
                  <SPrices price={priceSingle} vat={vat} />
                </Col>
                {!record.is_single && (
                  <Col span={12}>
                    <SPrices price={price /*  * (record?.attr_case_qty ?? 0) */} vat={vat} />
                  </Col>
                )}
              </Row>
            );
          },
          onCell(record, index) {
            return {
              onClick(e) {
                setCurrentRow(record);
                handleUpdatePricesModalVisible(true);
              },
            };
          },
        }),
      ),
    [priceTypes],
  );

  const orgColumns: ProColumns<API.Ean>[] = useMemo(
    () => [
      {
        title: 'Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        align: 'center',
        ellipsis: true,
        width: 50,
        showSorterTooltip: false,
        valueEnum: ItemEANStatusOptions as any,
        render: (__, record) => {
          let ele = null;
          if (record.status == ItemEANStatus.ACTIVE) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          } else {
            ele = <CloseOutlined style={{ color: 'gray' }} />;
          }
          return ele;
        },
      },
      {
        title: 'Shops',
        dataIndex: 'product_websites',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        width: 50,
        showSorterTooltip: false,
        render: (__, record) => (
          <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
        ),
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 80,
        render: (dom, record) => <EanFilesComp files={record.files} />,
      },
      {
        title: 'Item ID',
        dataIndex: 'item_id',
        fixed: 'left',
        sorter: true,
        ellipsis: true,
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: (
          <>
            <div>Name DE</div>
            <Button
              type="link"
              size="small"
              style={{ position: 'absolute', right: -4, top: -4, zIndex: 50 }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setColStates((prev) => ({
                  ...prev,
                  item_vat: { show: !prev.item_vat.show },
                  ean: { show: !prev.ean.show },
                  parent_ean: { show: !prev.parent_ean.show },
                }));
              }}
              title="Show / Hide EAN info"
            >
              <ExpandOutlined className="text-xs" />
            </Button>
          </>
        ),
        dataIndex: ['ean_texts', 0, 'name'],
        width: 180,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        fixed: 'left',
        tooltip: 'Orange color indicates the inherited value from its item.',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record, index) => {
          const defaultValue = record?.ean_texts?.[0]?.name ?? record?.item?.name;
          return (
            <a onClick={() => handleTextsClick(record)}>
              <Typography.Text type={record?.ean_texts?.[0]?.name ? undefined : 'warning'} title={defaultValue}>
                {defaultValue ?? <CloseOutlined style={{ color: '#cc2200' }} />}
              </Typography.Text>
            </a>
          );
        },
      },
      {
        title: 'VAT',
        dataIndex: ['item', 'vat', 'value'],
        key: 'item_vat',
        sorter: false,
        width: 50,
        ellipsis: true,
        align: 'right',
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.item?.vat, prevRecord.item?.vat),
        render: (dom, record) => (sn(record?.item?.vat?.value) >= 0 ? `${record?.item?.vat?.value}%` : ''),
      },

      {
        title: 'EAN',
        dataIndex: 'ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 150,
        render: (dom, record) => {
          return (
            <a
              onClick={async () => {
                let urlKey = record?.mag_url?.value;
                if (!urlKey)
                  urlKey = await dsGetCustomAttribute(record?.id || 0, {
                    force_update: 0,
                    attribute_code: 'url_key',
                  }).catch((e) => {
                    message.error('Not found SKU on the shop.');
                  });

                if (urlKey) {
                  window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                }
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Single EAN',
        dataIndex: ['parent', 'ean'],
        key: 'parent_ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 150,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.parent?.ean, prevRecord.parent?.ean),
      },
      {
        title: '-',
        dataIndex: ['mag_url', 'value'],
        valueType: 'text',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 50,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.mag_url, prevRecord.mag_url),
        render: (dom, record) => {
          return (
            <Space>
              <LinkOutlined
                // style={{ color: record?.mag_url?.value ? 'green' : 'c-lightgrey' }}
                title="Go to shop."
                className={record?.mag_url?.value ? 'green' : 'c-lightgrey'}
                onClick={async () => {
                  let urlKey = record?.mag_url?.value;
                  if (!urlKey)
                    urlKey = await dsGetCustomAttribute(record?.id || 0, {
                      force_update: 0,
                      attribute_code: 'url_key',
                    }).catch((e) => {
                      message.error('Not found SKU on the shop.');
                    });

                  if (urlKey) {
                    window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                  }
                }}
              />
              <Dropdown
                key="social-links-menu"
                overlay={
                  <Menu
                    items={[
                      {
                        key: 'all',
                        label: <SocialLinks ean={record.ean || ''} title={record?.ean_texts?.[0]?.name} />,
                      },
                    ]}
                  />
                }
              >
                <a onClick={(e) => e.preventDefault()}>
                  <DownOutlined />
                </a>
              </Dropdown>
            </Space>
          );
        },
      },
      {
        title: 'Qty/case',
        dataIndex: 'attr_case_qty',
        valueType: 'digit',
        sorter: true,
        align: 'right',
        hideInSearch: true,
        width: 60,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.attr_case_qty, prevRecord.attr_case_qty),
        render: (dom, record) => Util.numberFormat(record.attr_case_qty),
      },
      {
        title: 'SKU',
        dataIndex: 'sku',
        sorter: true,
        copyable: true,
        ellipsis: true,
        hideInSearch: true,
        width: 100,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.sku, prevRecord.sku),
        render: (dom, record) => {
          return (
            <a
              onClick={() => {
                setCurrentRow({
                  ...record,
                });
                setShowDetail(true);
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Exp (Qty)',
        dataIndex: ['ibo_earliest3'],
        width: 120,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        tooltip: (
          <>
            Qty in italic means `pcs`. <br />
            <br />
            Click to view stock detail...
          </>
        ),
        className: 'cursor-pointer',
        render: (dom, record) => {
          const list = (record.is_single ? record?.ibo_earliest3_item : record?.ibo_earliest3) ?? [];
          return list.map((x) => (
            <Row key={x.exp_date} gutter={8}>
              <Col>
                <ExpDate date={x.exp_date} />
              </Col>
              {x.mix_qty ? (
                <Col
                  className={record.is_single ? 'italic' : ''}
                  flex="auto"
                  style={{ textAlign: (x.statuses || '').includes('1') ? 'right' : 'left' }}
                >
                  ({ni(x.mix_qty)}){/* {record.is_single ? '' : ''}) */}
                </Col>
              ) : null}
              {/* <Col>{ni(x.ids)}</Col> */}
            </Row>
          ));
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Qty',
        dataIndex: ['stock_stables_sum_box_qty'],
        width: 60,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer',
        tooltip: 'Qty in Stock Warehouse. Please click to view stock details.',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record) => {
          return record.is_single
            ? Util.numberFormat(record?.parent_stock_stables_sum_total_piece_qty)
            : Util.numberFormat(record?.stock_stables_sum_box_qty);
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Discount',
        dataIndex: ['fs_special_discount'],
        width: 50,
        align: 'right',
        ellipsis: true,
        hideInSearch: true,
        tooltip: 'Edit...',
        className: 'cursor-pointer',
        onCell(record, index) {
          return {
            onClick(e) {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Badge',
        dataIndex: ['fs_special_badge'],
        width: 100,
        align: 'center',
        ellipsis: false,
        hideInSearch: true,
        tooltip: 'Edit...',
        render: (__, record: any) =>
          dict?.[`${record?.fs_special_badge}`]?.label ?? dict?.[`${record?.fs_special_badge}`]?.value ?? '',
        className: 'cursor-pointer',
        onCell(record, index) {
          return {
            onClick(e) {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Text Label/Badge',
        dataIndex: ['fs_special_badge2'],
        width: 100,
        align: 'left',
        ellipsis: false,
        hideInSearch: true,
        tooltip: 'Edit...',
        render: (__, record: any) =>
          dict?.[`${record?.fs_special_badge2}`]?.label ?? dict?.[`${record?.fs_special_badge2}`]?.value ?? '',
        className: 'cursor-pointer',
        onCell(record, index) {
          return {
            onClick(e) {
              setCurrentRow(record);
              handleUpdatePricesModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Task',
        dataIndex: ['ean_tasks_count'],
        width: 40,
        align: 'center',
        hideInSearch: true,
        className: 'cursor-pointer',
        render: (__, record) => <TaskIcon count={record.ean_tasks_count} />,
        onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentRow({ ...record });
              setVisibleEanTasksModal(true);
            },
          };
        },
      },
      {
        title: 'Latest BP',
        dataIndex: ['latest_ibo', 'price'],
        width: 100,
        align: 'center',
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.latest_ibo, prevRecord.latest_ibo),
        render: (dom, record) => {
          const vat = record.item?.vat?.value || 0;
          const isSingle = record.is_single;
          return (
            <>
              <Row
                gutter={4}
                title="View prices list..."
                className="cursor-pointer"
                onClick={() => {
                  setCurrentRow({ ...record });
                  setShowImportedPrices(true);
                }}
                style={{ minHeight: 24 }}
              >
                <Col span={12}>
                  <SPrices price={record?.latest_ibo?.price} vat={vat} />
                </Col>
                {!isSingle && (
                  <Col span={12}>
                    <SPrices price={(record?.latest_ibo?.price ?? 0) * (record?.attr_case_qty ?? 0)} vat={vat} />
                  </Col>
                )}
              </Row>
            </>
          );
        },
      },
      ...pricesColDefs,
      /* ...(ScrapSystemIds.map((scrapName, ind) => ({
        title: `${scrapName} price`,
        dataIndex: ['scrap_prices', ind, 'price'],
        width: 80,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer',
        shouldCellUpdate: (record: API.Ean, prevRecord: API.Ean) =>
          !_.isEqual(record.scrap_prices, prevRecord.scrap_prices),
        render: (dom, record: API.Ean) => {
          const vat = record.item?.vat?.value || 0;
          const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });

          return (
            <>
              <Row gutter={4} style={{ minHeight: 24 }}>
                <Col span={12} className="text-sm italic c-lightgrey" style={{ paddingTop: 3 }}>
                  {!record?.is_single && nf2(scrapPriceObj?.price)}
                </Col>
                <Col span={12}>
                  <SPrices
                    price={casePrice(scrapPriceObj?.price, record.attr_case_qty)}
                    vat={vat}
                    hideNet
                    isGross
                    direction="horizontal"
                  />
                </Col>
              </Row>
            </>
          );
        },
        onCell: (record: API.Ean) => {
          const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });
          return {
            onClick: (ev: any) => {
              if (scrapPriceObj?.link) {
                window.open(scrapPriceObj?.link, '_blank');
              }
            },
            className: 'cursor-pointer ' + getColClassByPrices(record, [{ id: 1 }], scrapName),
          };
        },
      })) as ProColumns<API.Ean>[]), */
      {
        title: 'Updated on',
        sorter: true,
        dataIndex: 'updated_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        className: 'text-xs c-grey',
        width: 80,
        renderFormItem: (item, { type, defaultRender }) => {
          return defaultRender(item);
        },
        render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 50,
        search: false,
        sorter: true,
        align: 'center',
        className: 'text-xs c-grey',
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 110,
        shouldCellUpdate(record, prevRecord) {
          return false;
        },
        render: (dom, record, index) => {
          const options = [
            <a
              key="update-item"
              title="Update item"
              onClick={() => {
                handleUpdateItemModalVisible(true);
                setCurrentRow({ ...record });
              }}
            >
              <SnippetsOutlined className="btn-gray" />
            </a>,
            <a
              key="texts"
              title="Update texts"
              onClick={() => {
                handleUpdateTextsModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <FileTextOutlined />
            </a>,
            <a
              key="config"
              title="Update attributes"
              onClick={() => {
                handleUpdateModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <EditTwoTone />
            </a>,
            <a
              key="files"
              title="Update pictures"
              onClick={() => {
                handleUpdatePicturesModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <PictureOutlined />
            </a>,
            <Popconfirm
              key="upsync"
              placement="topRight"
              title={
                <>
                  Are you sure you want to up sync the EAN？ <br />
                  <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
                </>
              }
              overlayStyle={{ width: 350 }}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                if (!record.id) return;
                const hide = message.loading(`Up syncing ...`, 0);
                usProductFull(record.id)
                  .then((res) => {
                    // console.log('---- Up sync result ---');
                    //console.log(res);
                    if (res.sku) {
                      message.success('Successfully up synced on shop!');
                    } else {
                      message.error(res.upSyncMessage || 'Failed to up sync EAN!');
                    }
                  })
                  .catch((e) => {
                    message.error(e.message ?? 'Failed to upsync!');
                  })
                  .finally(() => {
                    hide();
                  });
              }}
            >
              <CloudUploadOutlined className="btn-gray" />
            </Popconfirm>,
          ];
          return <Space>{options.map((option) => option)}</Space>;
        },
      },
    ],
    [dict, pricesColDefs],
  );

  useEffect(() => {
    setColumns(orgColumns);
  }, [orgColumns]);

  const handleResize =
    (index: number) =>
    (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const newColumns = [...columns];
      newColumns[index] = {
        ...newColumns[index],
        width: size.width,
      };
      setColumns(newColumns);
    };

  const mergeColumns: any /* ProColumns<API.Ean>[] */ = (columns ?? []).map((col, index) => ({
    ...col,
    onHeaderCell: (column: ProColumns<API.Ean>) => ({
      width: column.width,
      onResize: handleResize(index) as React.ReactEventHandler<any>,
    }),
  }));

  // vats
  const vats = appSettings.vats;

  // Category trees
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  const handleExportMenuClick: MenuProps['onClick'] = async (e) => {
    setLoadingExport(true);
    const hide = message.loading('Exporting...');
    const formValues = searchFormRef.current?.getFieldsValue();
    switch (e.key) {
      case 'export-core':
        exportEanList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-core-alt':
        exportEanListAlt(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-price':
        exportEanPriceList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'sync-sku':
        break;
    }
  };

  const handleBatchUpSync = async () => {
    setBatchUpSyncProgress(0);
    setBatchModalData({
      title: 'Batch Up Syncing...',
      desc: 'Batch UpSync is in progress. Please wait...',
    });
    handleBatchUpSyncModalVisible(true);

    const totalCount = selectedRows.length;
    const skip = 100 / totalCount;

    for (const x of selectedRows) {
      await usProductFull(Number(x.id))
        .then((res) => {
          setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
        })
        .catch((error) => {
          Util.error(error);
          handleBatchUpSyncModalVisible(false);
          return;
        });
    }

    setBatchUpSyncProgress(100);
    handleBatchUpSyncModalVisible(false);
  };

  const { pageTitle } = usePageContainerTitle(eanComponentProps.route);

  const lastFormValues = useMemo(() => {
    const values = Util.getSfValues('sf_ean_grid_all_special', {
      status: 1,
      sku: location.query?.sku || '',
      minimum_order_qty: 1,
    });

    if (values.special_filters === undefined) {
      values.special_filters = {
        min_exp_date_base: undefined,
        discounts: [],
        badges: [],
      };
    }
    if (!values.special_filters.op1) values.special_filters.op1 = 'or';
    if (!values.special_filters.op2) values.special_filters.op2 = 'or';
    if (values.special_filters.min_exp_date_base_from === undefined)
      values.special_filters.min_exp_date_base_from = -200;

    return values;
  }, [location.query?.sku]);

  return (
    <PageContainer className={styles.eanListContainer} title={pageTitle}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          // size="small"
          className="search-form"
          initialValues={lastFormValues}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Dropdown
                  key="export-menu"
                  disabled={loadingExport}
                  overlay={
                    <Menu
                      onClick={handleExportMenuClick}
                      items={[
                        {
                          label: 'Download EANs',
                          key: 'export-core',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          label: 'Download prices',
                          key: 'export-price',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          type: 'divider',
                        },
                        {
                          label: 'Download As Excel',
                          key: 'export-core-alt',
                          icon: <DownloadOutlined type="primary" />,
                        },
                      ]}
                    />
                  }
                >
                  <Button loading={loadingExport}>
                    <Space>
                      {!loadingExport && <DownloadOutlined type="primary" />}
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>,
              ];
            },
          }}
        >
          {eanTypeProp == 'default' && (
            <ProFormSelect
              name="ean_type_search"
              placeholder="Select type"
              label="Type"
              options={[
                { value: '', label: 'All' },
                { value: 'base', label: 'Single' },
                { value: 've', label: 'Multi' },
              ]}
              fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
            />
          )}
          {/* <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} /> */}
          {/* <ProFormText name={'ft_name1'} label="Name" width={180} placeholder={'Search by keywords'} /> */}
          <ProFormText name={'name2'} label="Name" width={180} placeholder={'Search by keywords'} />
          {/* <ProFormSelect
            name="producers[]"
            label="Producers"
            placeholder="Please select producers"
            mode="multiple"
            request={getProducerListSelectOptions}
            width={180}
          /> */}
          {/* <ProFormSelect
            name="trademarks[]"
            label="Trademarks"
            placeholder="Please select trademark"
            mode="single"
            request={getTrademarkListSelectOptions}
            width={180}
          /> */}
          {formElements}
          {/* <ProFormCheckbox name="noTrademark" label="No Trademark?" /> */}
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          {/* <ProFormSelect
            name="create_type"
            placeholder="All"
            label="Creation mode"
            options={[
              { value: '', label: 'All' },
              { value: '1', label: 'Manually' },
              { value: '2', label: 'Imported' },
            ]}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <SDatePicker name="created_on_start" label="Date of creation" />
          <SDatePicker name="created_on_end" addonBefore="~" /> */}
          {formElementsIbom}
          <ProFormDigit name={'minimum_order_qty'} label="Min. Qty" width={80} placeholder={'Min. Qty'} />
          <ProFormSelect
            name="product_websites"
            label="Websites"
            width={130}
            mode="multiple"
            placeholder={'Websites'}
            options={appSettings.storeWebsites
              ?.filter((x) => x.code != 'admin')
              ?.map((x) => ({
                value: `${x.id}`,
                label: x.name,
              }))}
          />
          <ProFormGroup spaceProps={{}} size={'small'}>
            <SProFormDigit
              name={['special_filters', 'min_exp_date_base_from']}
              label="EXP next range"
              labelCol={{ style: { padding: 0 } }}
              min={-999999}
              width={90}
            />
            <SProFormDigit
              name={['special_filters', 'min_exp_date_base']}
              addonBefore="~"
              labelCol={{ style: { padding: 0 } }}
              width={90}
              min={-999999}
            />
            {/* <ProFormRadio.Group
              name={['special_filters', 'op1']}
              radioType="button"
              fieldProps={{ style: { margin: 0, padding: 0 } }}
              formItemProps={{ style: { padding: 0 } }}
              options={[
                { value: 'and', label: 'AND' },
                { value: 'or', label: 'OR' },
              ]}
            /> */}
            <ProFormCheckbox.Group
              label="Discount?"
              name={['special_filters', 'discounts']}
              options={[
                { value: 'set', label: 'Set' },
                { value: 'not_set', label: 'Not Set' },
              ]}
            />
            {/* <ProFormRadio.Group
              name={['special_filters', 'op2']}
              radioType="button"
              labelCol={{ style: { padding: 0 } }}
              options={[
                { value: 'and', label: 'AND' },
                { value: 'or', label: 'OR' },
              ]}
            />
            <ProFormCheckbox.Group
              name={['special_filters', 'badges']}
              options={[
                { value: 'set', label: 'Set' },
                { value: 'not_set', label: 'Not Set' },
                // { value: 'all', label: 'All' },
              ]}
            /> */}
          </ProFormGroup>
          <ProFormGroup style={{ marginLeft: 80 }}>
            <ProFormCheckbox name={'blockedItemsOnly'} label="Blocked items?" width={'xs'} />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<API.Ean, API.PageParams>
        headerTitle={'EANs List - Better Report for Specials'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        onChange={handleTableChange}
        dataSource={datasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_grid_all_special_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_grid_all_special', searchFormValues);
          Util.setSfValues('sf_ean_grid_all_special_p', params);

          setLoading(true);
          return getEanList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              ean_type: eanTypeProp || 'base',
              trademarks: [searchFormValues.trademark?.value],
              with: EAN_DEFAULT_SUMMARY_WITH + ',iboEarliest3,gdsnItem',
              source: 'EanAllSpecial',
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);
              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Ean) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={mergeColumns}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            key="upsync"
            placement="topRight"
            title={
              <>
                Are you sure you want to activate stocks for sale?
                <br />
                <br />
                Stocks with Exp. Date &gt; 30 days will be activated for sale.
              </>
            }
            overlayStyle={{ width: 350 }}
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              const hide = message.loading(`Activating stocks with Exp. Date > 30...`, 0);
              updateStockStableStatusBatch({ eanIds: selectedRows.map((x) => sn(x.id)), status: 0 })
                .then((res) => {
                  message.success('Successfully updated!');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" className="btn-gree">
              Activate Stocks
            </Button>
          </Popconfirm>
          <Popconfirm
            key="upsync"
            placement="topRight"
            title={
              <>
                Are you sure you want to block stocks for sale?
                <br />
                <br />
                Stocks with Exp. Date &gt; 30 days will be blocked for sale.
              </>
            }
            overlayStyle={{ width: 350 }}
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              const hide = message.loading(`Blocking stocks with Exp. Date > 30...`, 0);
              updateStockStableStatusBatch({ eanIds: selectedRows.map((x) => sn(x.id)), status: 1 })
                .then((res) => {
                  message.success('Successfully updated!');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" ghost danger>
              Block Stocks
            </Button>
          </Popconfirm>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateBadgesFormBulk(true);
            }}
          >
            Badge & Label
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateAttributesFormBulk(true);
            }}
          >
            Status & Websites
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateCategoriesFormBulk(true);
            }}
          >
            Categories
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateVatFormBulk(true);
            }}
          >
            Vat
          </Button>

          <Popconfirm
            title={<>Are you sure you want to up sync selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              handleBatchUpSync();
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync
            </Button>
          </Popconfirm>

          {ScrapSystemIds.map((id) => (
            <Popconfirm
              key={id}
              title={<>Are you sure you want to scrap prices from WoS?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ maxWidth: 350 }}
              onConfirm={async () => {
                setBatchUpSyncProgress(0);
                setBatchModalData({
                  title: `Getting prices from ${id}...`,
                  desc: 'Batch action is in progress. Please wait...',
                });
                handleBatchUpSyncModalVisible(true);

                const totalCount = selectedRows.length;
                const skip = 100 / totalCount;

                for (const x of selectedRows) {
                  await scrapWoSPrice(x.ean || '', id)
                    .then((res) => {
                      setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
                    })
                    .catch((error) => {
                      Util.error(error);
                      setTimeout(() => {
                        handleBatchUpSyncModalVisible(false);
                      }, 1000);
                      return;
                    });
                  await Util.waitTime(400);
                }

                setBatchUpSyncProgress(100);
                actionRef.current?.reload();
                message.success('Successfully updated!');
                setTimeout(() => {
                  handleBatchUpSyncModalVisible(false);
                }, 1000);
              }}
            >
              <Button type="default" icon={<SyncOutlined />}>
                {id} prices
              </Button>
            </Popconfirm>
          ))}
          <Popconfirm
            title={<>Are you sure you want to delete selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleRemove(selectedRows);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <UpdateAttributesFormBulk
        modalVisible={visibleUpdateAttributesFormBulk}
        handleModalVisible={handleVisibleUpdateAttributesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateAttributesFormBulk(false);
        }}
      />

      <UpdateCategoriesFormBulk
        modalVisible={visibleUpdateCategoriesFormBulk}
        handleModalVisible={handleVisibleUpdateCategoriesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        treeData={treeData}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateCategoriesFormBulk(false);
        }}
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async () => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateAttributeForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
      />

      <UpdateCategoriesForm
        modalVisible={updateCategoriesModalVisible}
        handleModalVisible={handleUpdateCategoriesModalVisible}
        initialValues={currentRow || {}}
        treeData={treeData}
        onSubmit={async () => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateCategoriesModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        onSubmit={async (values) => {
          setCurrentRow((prev) => ({
            ...prev,
            ...values,
          }));

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
      />

      <UpdatePicturesForm
        modalVisible={updatePicturesModalVisible}
        handleModalVisible={handleUpdatePicturesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={{
          id: currentRow?.id,
          parent_id: currentRow?.parent_id,
          files: currentRow?.files || [],
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePicturesModalVisible(false);
        }}
      />

      <UpdateItemForm.default
        modalVisible={updateItemModalVisible}
        handleModalVisible={handleUpdateItemModalVisible}
        initialValues={{ ...currentRow?.item, eanId: currentRow?.id, ean: currentRow?.parent?.ean }}
        handleNavigation={handleNavigation}
        onSubmit={async (value) => {
          setCurrentRow((prev) => ({ ...prev, item: { ...prev?.item, ...value } }));
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateItemModalVisible(false);
        }}
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          parent_id: currentRow?.parent_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <EanTasksModals
        modalVisible={visibleEanTasksModal}
        handleModalVisible={setVisibleEanTasksModal}
        reloadList={() => actionRef.current?.reload()}
        itemEan={{
          id: currentRow?.id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
      />

      <UpdateVatFormBulk
        modalVisible={visibleUpdateVatFormBulk}
        handleModalVisible={handleVisibleUpdateVatFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        vats={vats || []}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateVatFormBulk(false);
        }}
      />

      <UpdateLabelAndBadgeFormBulk
        modalVisible={visibleUpdateBadgesFormBulk}
        handleModalVisible={handleVisibleUpdateBadgesFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        onSubmit={async (values) => {
          if (actionRef.current) {
            await handleBatchUpSync();
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateBadgesFormBulk(false);
        }}
      />

      <Modal title={batchModalData.title} centered open={batchUpSyncModalVisible} closable={false} footer={null}>
        <p>
          <LoadingOutlined /> {batchModalData.desc ?? 'Batch UpSync is in progress. Please wait...'}
        </p>
        <Progress percent={batchUpSyncProgress} style={{ width: '100%' }} status="active" />
      </Modal>

      <Drawer
        width={600}
        title={`Buying Price History - ${currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EanAllSpecial;
