-- Fixing missing Exp.Date after booking IBO.
UPDATE stock_movement a
SET exp_date = (SELECT exp_date FROM ibo WHERE id=a.`ref_id`)
WHERE ref_type = 'IBO' AND reason = 'In' AND ref_id IS NOT NULL;

ALTER TABLE `stock_movement` CHANGE `reason` `reason` VARCHAR(31) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'movement type: In,Out,MoveIn,MoveOut,Damage,Return,Correction';

TRUNCATE TABLE `stock_stable`;

INSERT INTO `stock_stable` (
    item_id,
    ean_id,
    parent_ean_id,
    wl_id,
    exp_date,
    piece_qty,
    box_qty,
    case_qty,
    total_piece_qty,
    `created_on`,
    updated_on
)
SELECT
    item_id,
    ean_id, parent_ean_id,
    new_wl_id AS wl_id,
    exp_date,
    SUM(piece_qty) AS piece_qty,
    SUM(box_qty) AS box_qty,
    case_qty,
    SUM(total_piece_qty) AS total_piece_qty,
    MIN(created_on) AS created_on,
    MAX(created_on) AS updated_on
FROM stock_movement
GROUP BY ean_id, new_wl_id, exp_date;