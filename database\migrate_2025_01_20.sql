CREATE TABLE `sys_oauth`
(
    `id`                       int(10) unsigned NOT NULL AUTO_INCREMENT,
    `type`                     varchar(31) DEFAULT NULL COMMENT 'e.g.M365',
    `token`                    text        DEFAULT NULL,
    `refresh_token`            text        DEFAULT NULL,
    `token_expired_at`         int(11)     DEFAULT NULL,
    `client_id_expired_at`     date        DEFAULT NULL,
    `client_secret_expired_at` date        DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4;

-- ==============================================================
-- NOTE: Send via Skype and remove below.
-- ==============================================================
INSERT INTO sys_oauth (`type`, `refresh_token`)
values ('M365',
        '');
-- ==============================================================

ALTER TABLE `email_server`
    ADD COLUMN `is_oauth` BOOLEAN DEFAULT 0 NULL AFTER `settings`;


INSERT INTO `email_server` (`domain`, `imap_host`, `imap_port`, `imap_port_ssl`, `imap_ssl`, `smtp_host`, `smtp_port`, `smtp_user`, `is_oauth`)
VALUES ('foodStore.one', 'outlook.office365.com', '993', '993', '1', 'smtp-mail.outlook.com', '587', '<EMAIL>', 1);



