import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import type { StockMovementRowType } from './StockCorrectionForm';
import type { StockMoveParamType } from '@/services/foodstore-one/Stock/stock';
import { stockMove } from '@/services/foodstore-one/Stock/stock';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import type { DefaultOptionType } from 'antd/lib/select';

export type FormValueType = Partial<StockMoveParamType>;

const handleMove = async (fields: FormValueType) => {
  const hide = message.loading('Moving stocks...');

  try {
    await stockMove(fields);
    hide();
    message.success('Moving is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

/**
 * @deprecated
 *
 * @param props
 * @returns
 */
export type StockMoveSettingFormProps = {
  ean?: Partial<API.Ean>;
  initialData?: Partial<StockMovementRowType>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: StockMovementRowType) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const StockMoveSettingForm: React.FC<StockMoveSettingFormProps> = (props) => {
  const qtyField = props.ean?.is_single ? 'piece_qty_edit' : 'box_qty_edit';
  const labelPrefix = props.ean?.is_single ? 'Pcs' : 'Box';
  const maxQty = sn(props.initialData?.[qtyField]);

  const formRef = useRef<ProFormInstance>();
  // Reference data
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);

  useEffect(() => {
    getWarehouseLocationACList({}).then((res) => {
      setWarehouseLocations(res);
    });
  }, []);

  useEffect(() => {
    if (props.modalVisible) {
      formRef.current?.setFieldsValue({
        [qtyField]: 1,
      });
    }
  }, [props.modalVisible, props.initialData, qtyField]);

  return (
    <ModalForm<FormValueType>
      title={'Stock Movement Setting'}
      width={500}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      className={props.ean?.is_single ? 'm-single' : 'm-multi'}
      onFinish={async (value) => {
        const success = await handleMove({
          ibo_id: props.initialData?.ibo_id,
          ...value,
          ean_id: props.ean?.id,
          old_wl_id: props.initialData?.new_wl_id,
          exp_date: props.initialData?.exp_date,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProForm.Item label="Old Location">{props.initialData?.new_wl_name}</ProForm.Item>
      <ProForm.Item label="Exp. Date">{props.initialData?.exp_date}</ProForm.Item>
      <ProForm.Item label={`Available ${labelPrefix} Qty`}>{maxQty}</ProForm.Item>
      <ProFormSelect
        name="new_wl_id"
        rules={[
          {
            required: true,
            message: 'New Warehouse Location is required',
          },
        ]}
        options={warehouseLocations.filter((x) => x.value != props.initialData?.new_wl_id)}
        width="md"
        label="New Location"
        placeholder="New Location"
        showSearch
      />
      <ProFormDigit
        width="sm"
        name={qtyField}
        label={`${labelPrefix} Qty`}
        initialValue={1}
        min={1}
        max={maxQty}
        fieldProps={{ precision: 0 }}
      />
    </ModalForm>
  );
};

export default StockMoveSettingForm;
