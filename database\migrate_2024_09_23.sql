CREATE TABLE `offer_import_matrix`
(
    `id`        int(11)             NOT NULL AUTO_INCREMENT,
    `offer_id`  bigint(20) unsigned NOT NULL,
    `import_id` bigint(20) unsigned NOT NULL,
    `value`     text DEFAULT NULL,
    PRIMARY KEY (`id`),
    <PERSON><PERSON><PERSON> `FK_offer_import_matrix_import_id` (`import_id`),
    <PERSON><PERSON>Y `UQ_offer_import_matrix` (`offer_id`, `import_id`),
    CONSTRAINT `FK_offer_import_matrix_import_id` FOREIGN KEY (`import_id`) REFERENCES `import` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_import_matrix_offer_id` FOREIGN KEY (`offer_id`) REFERENCES `offer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('LOTUS_PATH', 'sys config', 'notes://test123?xxx={orderNo}', 'LOTUS URL', '');
