import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Modal, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadDragger } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { LS_TOKEN_NAME } from '@/constants';
import { UploadChangeParam } from 'antd/lib/upload';
import { deleteFile } from '@/services/foodstore-one/File/file';

export type FormValueType = Partial<API.Trademark>;

export type UpdateLogoFileFormProps = {
  initialValues?: Partial<API.Trademark>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Trademark) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateLogoFileForm: React.FC<UpdateLogoFileFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      formRef.current.setFieldsValue({
        logo_files: props.initialValues?.logo_file ? [props.initialValues?.logo_file] : [],
      });
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Trademark Logo File'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      initialValues={props.initialValues || {}}
      formRef={formRef}
      size="small"
      className="form-list-sm"
      onFinish={async (value) => {
        props.handleModalVisible(false);
        if (props.onSubmit) props.onSubmit(value);
      }}
    >
      <ProFormUploadDragger
        name="logo_files"
        max={1}
        title="Logo"
        description="Please select files or drag & drop"
        accept="image/*"
        fieldProps={{
          multiple: false,
          listType: 'picture-card',
          name: 'file',
          action: `${API_URL}/api/basic-data/trademark/${props.initialValues?.id}/upload-file`,
          headers: {
            Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
          },
          // onPreview: handlePreview,
          onChange: (info: UploadChangeParam, updateState = true) => {
            if (info.file.status == 'done') {
              info.file.url = info.file.response.url;
              info.file.uid = info.file.response.uid;
              (info.file as any).id = info.file.response.uid;
              (info.file as any).file_name = info.file.response.file_name;
              (info.file as any).clean_file_name = info.file.response.clean_file_name;
              (info.file as any).path = info.file.response.path;
              (info.file as any).org_path = info.file.response.org_path;

              console.log(info.file);
              formRef.current?.setFieldsValue({ logo_files: [info.file] });
            }
          },
          style: { marginBottom: 24 },
          onRemove: async (file: API.File) => {
            const { confirm } = Modal;
            return new Promise((resolve, reject) => {
              confirm({
                title: 'Are you sure you want to delete?',
                onOk: async () => {
                  resolve(true);

                  const hide = message.loading('Deleting logo file...', 0);
                  try {
                    const result = await deleteFile(file.id, {
                      ref_type: 'trademark logo',
                      ref_id: props.initialValues?.id,
                    });
                    resolve(result);
                  } catch (err) {
                    Util.error(err);
                  } finally {
                    hide();
                  }
                },
                onCancel: () => {
                  reject(true);
                },
              });
            });
          },
        }}
      />
    </ModalForm>
  );
};

export default UpdateLogoFileForm;
