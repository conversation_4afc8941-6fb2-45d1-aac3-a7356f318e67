{"name": "foodstore-one", "version": "1.0.0", "private": true, "description": "WHC-Foodstore.one", "scripts": {"analyze": "cross-env NODE_OPTIONS=--openssl-legacy-provider ANALYZE=1 umi build", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=en-US --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "playwright": "playwright install && playwright test", "prepare": "husky install", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev umi dev", "start:dev": "cross-env REACT_APP_ENV=dev NODE_OPTIONS=--openssl-legacy-provider MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env NODE_OPTIONS=--openssl-legacy-provider MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env NODE_OPTIONS=--openssl-legacy-provider UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre NODE_OPTIONS=--openssl-legacy-provider UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test NODE_OPTIONS=--openssl-legacy-provider MOCK=none UMI_ENV=dev umi dev", "test": "umi test", "test:component": "umi test ./src/components", "test:e2e": "node ./tests/run-tests.js", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/compatible": "^1.1.0", "@ant-design/icons": "^4.7.0", "@ant-design/pro-card": "^1.19.0", "@ant-design/pro-descriptions": "^1.10.0", "@ant-design/pro-form": "^1.64.0", "@ant-design/pro-layout": "^6.35.0", "@ant-design/pro-list": "^2.6.2", "@ant-design/pro-table": "^2.71.0", "@babel/runtime": "^7.20.6", "@lexical/react": "^0.3.6", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@tinymce/tinymce-react": "^4.1.0", "@umijs/route-utils": "^2.0.0", "antd": "^4.19.0", "chonky": "^2.3.2", "chonky-icon-fontawesome": "^2.3.2", "classnames": "^2.3.0", "immutability-helper": "^3.1.1", "lodash": "^4.17.0", "moment": "^2.29.0", "omit.js": "^2.0.2", "pdfjs-dist": "3.4.120", "print-js": "^1.6.0", "rc-menu": "^9.1.0", "rc-util": "^5.16.0", "react": "^17.0.0", "react-dev-inspector": "~1.7.0", "react-device-detect": "^2.2.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^17.0.0", "react-helmet-async": "^1.2.0", "react-markdown": "^8.0.3", "react-resizable": "^3.0.4", "react-responsive": "^10.0.0", "react-xml-viewer": "^2.0.0", "umi": "^3.5.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@playwright/test": "^1.17.0", "@types/express": "^4.17.0", "@types/history": "^4.7.0", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@types/react-resizable": "^3.0.3", "@umijs/fabric": "^2.8.0", "@umijs/openapi": "^1.3.0", "@umijs/plugin-blocks": "^2.2.0", "@umijs/plugin-esbuild": "^1.4.0", "@umijs/plugin-openapi": "^1.3.0", "@umijs/preset-ant-design-pro": "^1.3.0", "@umijs/preset-dumi": "^1.1.0", "@umijs/preset-react": "^2.1.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.3.0", "detect-installer": "^1.0.0", "eslint": "^9.8.0", "gh-pages": "^3.2.0", "husky": "^7.0.4", "jsdom-global": "^3.0.0", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "prettier": "^2.5.0", "stylelint": "^16.8.1", "swagger-ui-react": "^3.52.0", "typescript": "^4.5.0", "umi-serve": "^1.9.10"}, "engines": {"node": ">=12.0.0"}}