declare namespace API {
  type GdsnMessageType =
    | 'catalogueItemNotification'
    | 'catalogueItemPublication'
    | 'catalogueItemSubscription'
    | 'catalogueItemRegistrationResponse'
    | 'registryCatalogueItem'
    | 'requestForCatalogueItemNotification'
    | 'catalogueItemConfirmation'
    | 'catalogueItemHierarchicalWithdrawal'
    | 'gS1Response:Exception'
    | 'gS1Response:Success';

  type GdsnMessage = {
    id?: number;
    msg_id?: string;
    kind?: GdsnMessageType;
    sender?: string;
    receiver?: string;
    direction?: string;
    remoteParty?: string;
    createdAt?: string;
    generatedAt?: string;
    submittedAt?: string;
    file_path?: string;
    file_size?: number;
  } & {
    gdsn_message_items?: GdsnMessageItem[];
    gdsn_message_items_count?: number;
    file_url?: string; // relative file url.
  };

  type GdsnMessageItem = {
    id?: number;

    gdsn_message_id?: number;
    transaction_id?: string;
    content_owner?: string;
    receiver?: string;
    source_data_pool?: string;
    gtin?: string;
    parent_gtin?: string;
    case_qty?: number;
    provider_gln?: string;
    provider_name?: string;
    gpc_category_code?: string;
    gpc_category_definition?: string;
    gpc_category_name?: string;
    ref_item_gtin?: string;
    ref_item_type_code?: string;
    item_state?: string;
    trademark_gln?: string;
    trademark_name?: string;
    maker_gln?: string;
    maker_name?: string;
    contact_code?: string;
    contact_address?: string;
    contact_name?: string;
    info_xml?: string;
    contact_xml?: string;
    contact_detail?: any;
    sync_last_change_dt?: string;
    sync_effective_dt?: string;

    name?: string;
    desc?: string;
    desc_short?: string;
    depth?: number;
    width?: number;
    height?: number;
    item_base?: number;
    item_base_unit?: string;
    net_weight?: number;
    gross_weight?: number;
    children_cnt?: number;
    children_total_qty?: number;

    detail?: any;
  } & {
    gdsn_message?: GdsnMessage;
  };

  type GdsnMessageSubscription = {
    id?: number;
    gdsn_message_id?: number;
    transaction_id?: string;
    content_owner?: string;
    data_source?: string;
    recipient_data_pool?: string;
    target_market_country_code?: string;
    detail?: string | Record<string, any>;
  };

  type GdsnMessageProvider = GdsnMessage &
    GdsnMessageSubscription & {
      provider_gln?: string;
      provider_name?: string;
      trademark_names?: string;
      gtin_count?: number;
      requested_count?: number;
      last_requested_date?: string;
    } & {
      gdsn_provider?: GdsnProvider;
    };
}
