import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText, ProFormTextArea, ModalForm } from '@ant-design/pro-form';
import { updateWarehouseLocation } from '@/services/foodstore-one/warehouse-location';
import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';

export const getWlPriority = (wlP: string) => {
  let wl = wlP.toUpperCase();
  let aa = '';
  const orgLength = wl.length;
  if (orgLength == 4) {
    wl = `0${wl}`;
    aa = '0';
  }

  if (wl.length == 5) {
    if (orgLength == 5) {
      aa = wl[0] >= 'A' && wl[0] <= 'Z' ? Util.numTo2Digits(wl.charCodeAt(0) - 64) : '99';
    }
    const bb = wl[1] >= 'A' && wl[1] <= 'Z' ? Util.numTo2Digits(wl.charCodeAt(1) - 64) : '99';
    const cc = wl[2] >= 'A' && wl[2] <= 'Z' ? Util.numTo2Digits(wl.charCodeAt(2) - 64) : '99';
    const ee = wl[3] >= '0' && wl[3] <= '9' ? `0${wl[3]}` : '99';

    let dd = '99';
    if ('BDFHKMOQSUVX'.includes(wl[1])) {
      switch (wl[4]) {
        case 'L':
          dd = '04';
          break;
        case 'M':
          dd = '03';
          break;
        case 'N':
          dd = '02';
          break;
        case 'R':
          dd = '01';
          break;
      }
    } else if ('ACEGILNPRTWY'.includes(wl[1])) {
      switch (wl[4]) {
        case 'L':
          dd = '01';
          break;
        case 'M':
          dd = '02';
          break;
        case 'N':
          dd = '03';
          break;
        case 'R':
          dd = '04';
          break;
      }
    }

    return Number(aa + bb + cc + dd + ee);
  }
  return 9_999_999_999;
};

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateWarehouseLocation(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.UserListItem>;

export type UpdateFormProps = {
  initialValues?: Partial<API.WarehouseLocation>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.WarehouseLocation) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  const handleNameChange = (firstStep?: boolean) => {
    const name = formRef.current?.getFieldValue('name');
    if (!name) return;
    const oldPriority = formRef.current?.getFieldValue('priority');

    if (firstStep) {
      if (oldPriority === undefined || oldPriority === null) {
        formRef.current?.setFieldValue('priority', getWlPriority(name));
      }
    } else {
      formRef.current?.setFieldValue('priority', getWlPriority(name));
    }
  };

  useEffect(() => {
    if (props.modalVisible) {
      handleNameChange(true);
    }
  }, [props.modalVisible, props.initialValues?.name]);

  return (
    <ModalForm
      title={'Update warehouse location'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
        fieldProps={{
          onChange(e) {
            handleNameChange();
          },
        }}
      />
      <SProFormDigit width="md" name="priority" label="Priority" />
      <ProFormTextArea width="md" name="address" label="Address" />
    </ModalForm>
  );
};

export default UpdateForm;
