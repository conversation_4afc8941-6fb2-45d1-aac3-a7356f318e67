import SProFormDigit from '@/components/SProFormDigit';
import { ShippingServiceNameType } from '@/constants';
import { createShippingLabel } from '@/services/foodstore-one/Magento/order';
import { getCountriesDEOptions } from '@/services/foodstore-one/countries';
import Util from '@/util';
import ProCard from '@ant-design/pro-card';
import ProForm, {
  ProFormCheckbox,
  ProFormDependency,
  ProFormGroup,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { Typography, message, notification } from 'antd';
import ButtonGroup from 'antd/lib/button/button-group';
import printJS from 'print-js';
import { useRef } from 'react';
import { useModel, useLocation } from 'umi';

type CreateShippingLabelValueType = API.OrderAddress & {
  shipping_provider: ShippingServiceNameType;
  returnLabel?: boolean;
  is_express: boolean;
  guaranteed24Service: boolean;
} & { service_name?: string };

const CreateShippingLabel: React.FC = (props) => {
  const location: any = useLocation();

  const formRef = useRef<ProFormInstance<CreateShippingLabelValueType>>();

  const { getDictByCode, storeWebsiteKv, getParcelUrl } = useModel('app-settings');

  return (
    <PageContainer>
      <ProCard>
        <ProForm<CreateShippingLabelValueType>
          layout="horizontal"
          labelAlign="left"
          size="small"
          initialValues={{ shipping_provider: 'GLS' }}
          formRef={formRef}
          onFinish={async (value) => {
            const postData: CreateShippingLabelValueType = { ...value };

            const hide = message.loading('Creating a shipping label');
            return createShippingLabel(postData)
              .then((res) => {
                if (res.files?.length) {
                  for (const file of res.files) {
                    window.open(`${API_URL}/api/${file.url}`, '_blank');
                  }
                  /* if (Util.isFirefox()) {
                    // We open files on new tabs
                    for (const file of res.files) {
                      window.open(`${API_URL}/api/${file.url}`, '_blank');
                    }
                  } else {
                    for (const file of res.files) {
                      printJS({ printable: file.b64, type: 'pdf', base64: true, showModal: true });
                      break;
                    }
                    if (res.files.length > 1) {
                      notification.info({
                        message: 'More labels exists. You can check them on "Print existing label" button.',
                        duration: 0,
                        placement: 'top',
                      });
                    }
                  } */
                }
              })
              .catch(Util.error)
              .finally(() => hide());
          }}
        >
          <ProFormGroup direction="horizontal" labelLayout="inline">
            <ProFormRadio.Group
              name={['shipping_provider']}
              label="Shipping Provider"
              radioType="button"
              fieldProps={{ buttonStyle: 'solid' }}
              options={['GLS', 'DHL', 'DPD']}
              required
              rules={[
                {
                  required: true,
                  message: 'Shipping option is required',
                },
              ]}
            />
            <ProFormCheckbox name={'returnLabel'} label="Return Label" />
            <ProFormDependency name={['shipping_provider']}>
              {(depValues) => {
                return depValues.shipping_provider == 'GLS' ? (
                  <ProFormCheckbox name={'is_express'} label="Express" />
                ) : null;
              }}
            </ProFormDependency>

            <ProFormDependency name={['shipping_provider']}>
              {(depValues) => {
                return depValues.shipping_provider == 'GLS' ? (
                  <ProFormCheckbox name={'guaranteed24Service'} label="Guaranteed 24h" />
                ) : null;
              }}
            </ProFormDependency>
          </ProFormGroup>

          <ProFormGroup direction="horizontal" labelLayout="inline">
            <ProFormDependency name={['shipping_provider']}>
              {(depValues) => {
                const fieldProps: any = {};
                const isDhl = depValues.shipping_provider == 'DHL';
                const isDPD = depValues.shipping_provider == 'DPD';
                const extraRules: any[] = [];
                let helpText = '';
                if (isDhl) {
                  fieldProps['minLength'] = 8;
                  fieldProps['maxLength'] = 35;

                  extraRules.push({
                    min: 8,
                    message: 'Fill at least 8 characters!',
                  });

                  extraRules.push({
                    max: 35,
                    message: 'Reference can be at most 35 characters!',
                  });
                  helpText = '8 ~ 35 characters only.';
                } else if (isDPD) {
                  fieldProps['maxLength'] = 35;
                  extraRules.push({
                    max: 35,
                    message: 'Reference can be at most 35 characters!',
                  });
                  helpText = '8 ~ 35 characters only.';
                }

                return (
                  <ProFormText
                    name="reference"
                    label="Reference"
                    required
                    rules={[
                      {
                        required: true,
                        message: 'Reference is required',
                      },
                      {
                        min: 8,
                      },
                      ...extraRules,
                    ]}
                    fieldProps={fieldProps}
                    help={helpText + ' e.g. "1234 / 232332"'}
                  />
                );
              }}
            </ProFormDependency>

            <SProFormDigit
              name="weight"
              label="Weight"
              fieldProps={{ precision: 3 }}
              required
              rules={[
                {
                  required: true,
                  message: 'Weight is required',
                },
              ]}
              addonAfter="kg"
            />
          </ProFormGroup>

          <ProFormGroup direction="vertical">
            <ProFormText
              width="md"
              name="company"
              label="Company"
              placeholder="Company"
              fieldProps={{ maxLength: 40 }}
              labelCol={{ style: { width: 120 } }}
            />
            <ProFormText
              width="sm"
              name="firstname"
              label="First Name"
              placeholder="First Name"
              labelCol={{ style: { width: 120 } }}
              required
              rules={[
                {
                  required: true,
                  message: 'First Name is required',
                },
              ]}
            />
            <ProFormText
              width="sm"
              name="lastname"
              label="Last Name"
              placeholder="Last Name"
              labelCol={{ style: { width: 120 } }}
              required
              rules={[
                {
                  required: true,
                  message: 'Last Name is required',
                },
              ]}
            />

            <ProFormText
              width="md"
              name={['street', 0]}
              label="Street 1"
              placeholder="Street 1"
              labelCol={{ style: { width: 120 } }}
              required
              rules={[
                {
                  required: true,
                  message: 'Street 1 is required',
                },
              ]}
            />
            <ProFormText
              width="md"
              name={['street', 1]}
              label="Street 2"
              placeholder="Street 2"
              labelCol={{ style: { width: 120 } }}
            />
            <ProFormText
              width="xs"
              name="postcode"
              label="Zip"
              placeholder="Zip"
              labelCol={{ style: { width: 120 } }}
              required
              rules={[
                {
                  required: true,
                  message: 'Zip is required',
                },
              ]}
            />
            <ProFormText
              width="sm"
              name="city"
              label="City"
              placeholder="City"
              labelCol={{ style: { width: 120 } }}
              required
              rules={[
                {
                  required: true,
                  message: 'City is required',
                },
              ]}
            />
            <ProFormSelect
              name={['country_id']}
              label="Country"
              showSearch
              width="sm"
              required
              rules={[
                {
                  required: true,
                  message: 'Country is required',
                },
              ]}
              options={getCountriesDEOptions()}
              initialValue={'DE'}
              labelCol={{ style: { width: 120 } }}
            />
            <ProFormText
              width="md"
              name="email"
              label="Email"
              placeholder="Email"
              labelCol={{ style: { width: 120 } }}
            />
            <ProFormText
              width="sm"
              name="telephone"
              label="Telephone"
              placeholder="Telephone"
              labelCol={{ style: { width: 120 } }}
            />
          </ProFormGroup>
        </ProForm>
      </ProCard>
    </PageContainer>
  );
};

export default CreateShippingLabel;
