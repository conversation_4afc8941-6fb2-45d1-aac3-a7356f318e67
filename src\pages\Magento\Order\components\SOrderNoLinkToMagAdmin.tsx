import { DictCode } from '@/constants';
import { Typography } from 'antd';
import { useModel } from 'umi';

type SOrderNoLinkToMagAdminProps = {
  entity_id?: number;
  copyable?: boolean;
};

const SOrderNoLinkToMagAdmin: React.FC<SOrderNoLinkToMagAdminProps> = ({ entity_id, copyable }) => {
  const { getDictByCode } = useModel('app-settings');

  return entity_id ? (
    <Typography.Link
      href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${entity_id}/`}
      target="_blank"
      rel="noreferrer"
      copyable={
        copyable
          ? {
              text: `${entity_id}`,
              tooltips: 'Copy Order No ' + entity_id,
            }
          : false
      }
    >
      {entity_id}
    </Typography.Link>
  ) : null;
};

export default SOrderNoLinkToMagAdmin;
