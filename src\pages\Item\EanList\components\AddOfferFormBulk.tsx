import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useRef } from 'react';
import { Button, Col, message, Row, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanBatch } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _ from 'lodash';
import useOfferOptions from '@/hooks/BasicData/useOfferOptions';

export type FormValueType = Partial<API.Ean>;

export type AddOfferFormBulkProps = {
  eanIds: number[];
  supplierXlsFileId?: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const AddOfferFormBulk: React.FC<AddOfferFormBulkProps> = (props) => {
  const [loading, setLoading] = useState(false);

  const formRef = useRef<ProFormInstance>();

  const { formElements } = useOfferOptions(undefined, formRef, { required: true });

  return (
    <ModalForm<Partial<API.Ean>>
      title={<>{`Add offers of ${props.eanIds.length} EANs`}</>}
      width={300}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      formRef={formRef}
      layout="inline"
      onFinish={async (value) => {
        if (!formRef.current) return;
        const offerId = formRef.current.getFieldValue('offer_id');
        if (!offerId) {
          message.error('Select an Offer!');
          return offerId;
        }

        try {
          setLoading(true);
          const postData = {
            mode: 'addOfferBatch',
            ids: props.eanIds,
            data: { ...value, supplierXlsFileId: props.supplierXlsFileId, percentage: 100 },
          };

          const hide = message.loading('Adding new offers...', 0);
          updateEanBatch(postData as any)
            .then((res) => {
              props.onSubmit?.(value as API.Ean);
              message.success('Added successfully.');

              props.handleModalVisible(false);
            })
            .catch(Util.error)
            .finally(() => {
              setLoading(false);
              hide();
            });
        } catch (err) {
          Util.error(err);
          setLoading(false);
        }
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
                disabled={loading}
              >
                Save
              </Button>
              <Button
                type="default"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
                disabled={loading}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <Row gutter={16}>
          <Col>{formElements}</Col>
          {/* <Col>
            <SProFormDigit
              colProps={{ span: 'auto' }}
              name="percentage"
              label="Percentage"
              width={120}
              addonAfter={'%'}
              min={-99999999}
              fieldProps={{
                precision: 4,
              }}
              initialValue={115}
              placeholder="Percentage"
              formItemProps={{
                tooltip: (
                  <div>
                    <div>Offer Price will be GFC Price * (1 + percentage)</div>
                  </div>
                ),
              }}
            />
          </Col> */}
        </Row>
      </ProFormGroup>
    </ModalForm>
  );
};

export default AddOfferFormBulk;
