import { request } from 'umi';
import { paramsSerializer } from '../api';
import { ItemType } from 'antd/lib/menu/hooks/useItems';

const urlPrefix = '/api/email';

/** 
 * Get all emails
 * 
 * GET /api/email */
export async function getEmailList(
  params: API.PageParams & Partial<API.Email>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Email>> {
  return request<API.Result<API.Email>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getEmail(id?: number, params?: Record<string, any>): Promise<API.Email> {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** put PUT /api/email */
export async function updateEmail(
  data: Partial<API.Email> & { action?: 'noActionNeeded' },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** post POST /api/email */
export async function addEmail(data: API.Email, options?: { [key: string]: any }) {
  return request<API.Email>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email/ds */
export async function dsPullEmail(data: API.Email, options?: { [key: string]: any }) {
  return request<API.Email>(`${urlPrefix}/ds`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email/send */
export async function sendEmail(data: API.Email | FormData, options?: { [key: string]: any }) {
  const config: any = {
    method: data instanceof FormData ? 'POST' : 'PUT',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config['body'] = data;
  } else {
    config['data'] = data;
  }

  return request<API.Email>(`${urlPrefix}/send`, config);
}

/** delete DELETE /api/email/{id} */
export async function deleteEmail(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * GET /api/email/nextEmail
 *
 * Get next email No by current emailId and direction.
 *
 * param dir -1 or 1
 */
export async function getNextEmail(dir: number, currentUid?: string, params?: any): Promise<number> {
  return request<API.ResultObject<number>>(`${urlPrefix}/nextEmail`, {
    method: 'GET',
    params: {
      dir,
      uid: currentUid,
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * Get all emails
 * 
 * GET /api/email/reasonableSenders */
export async function getReasonableSenders(
  params: API.PageParams & Partial<API.Email>,
  sort?: any,
  filter?: any,
) {
  return request<API.ResultObject<ItemType[]>>(`${urlPrefix}/reasonableSenders`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then(res => res.message);
}

