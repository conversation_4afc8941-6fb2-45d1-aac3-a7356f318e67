// https://umijs.org/config/
import { defineConfig } from 'umi';

export default defineConfig({
  plugins: ['react-dev-inspector/plugins/umi/react-inspector'],
  inspectorConfig: {
    exclude: [],
    babelPlugins: [],
    babelOptions: {},
  },
  define: {
    API_URL: 'http://localhost/whc-foodstore-backend/public',
    API_URL_EAN: 'http://localhost/whc-gdsn-backend/public',
    GFC_CUST_PIM_URL: 'http://localhost:8001',
  },
});
