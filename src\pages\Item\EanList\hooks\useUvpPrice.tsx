import { getUvpPrice } from '@/services/foodstore-one/Item/ean';
import { useCallback, useEffect, useState } from 'react';

export default (ean?: string, load?: boolean) => {
  // UVP data
  const [loading, setLoading] = useState<boolean>(false);
  const [uvpData, setUvpData] = useState<API.zImportSupplierDataTpl & { byEan?: API.zImportSupplierDataTpl }>();

  const fetchUvpData = useCallback(() => {
    if (load && ean) {
      setLoading(true);
      setUvpData(undefined);
      getUvpPrice({ ean })
        .then((res) => {
          setUvpData({ ...res.latest_ibo_xls_data, byEan: res.latest_ibo_xls_data_ean });
        })
        .catch((err) => {
          setUvpData(undefined);
        })
        .finally(() => setLoading(false));
    } else {
      setUvpData(undefined);
    }
  }, [ean, load]);

  useEffect(() => {
    fetchUvpData();
  }, [fetchUvpData]);

  return { fetchUvpData, uvpData, loading };
};
