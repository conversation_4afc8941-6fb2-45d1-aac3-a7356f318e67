import { RequestConfig, request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/ibo/ibo-pre';

/** get GET /api/ibo/ibo-pre */
export async function getIboPreList(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.IboPre>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getIboPre(id: string | number, params?: Record<string, any>): Promise<API.IboPre> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params: {
      ...params,
      with: 'detail',
    },
    withToken: true,
  }).then((res) => res.message);
}

/** post POST /api/ibo/ibo-pre */
export async function addIboPre(
  data: Partial<API.IboPre>,
  options?: Record<string, any>,
): Promise<API.IboPre> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** put PUT /api/ibo/ibo-pre/{id} */
export async function updateIboPre(
  id: number,
  data: Partial<API.IboPre>,
  options?: Record<string, any>,
): Promise<API.IboPre> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/**
 * bulk update
 *
 * PUT /api/ibo/ibo-pre */
export async function updateIboPreBulk(
  ids: number[],
  data: Partial<API.IboPre> & { mode?: 'moveAll', prev_ibo_pre_management_id?: number; },
  params?: Record<string, any>,
): Promise<API.ResultObject<boolean>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'PUT',
    data: {
      ids,
      data,
      params,
    }
  }).then((res) => res.message);
}

/** 
 * Create a new IBO Pre entry
 * 
 * POST /api/ibo/ibo-pre 
 * */
export async function createIboPre(data: API.IboPre, options?: Record<string, any>): Promise<API.IboPre> {
  return request<API.BaseResult>(urlPrefix, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** 
 * Create IBO Pres
 * 
 *  POST /api/ibo/ibo-pre/bulk */
export async function createIboPreBulk(data: Partial<API.IboPre> & { rows: API.IboPre[], }, options?: Record<string, any>) {
  return request<API.ResultList<API.IboPre>>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}



/** delete DELETE /api/ibo/ibo-pre */
export async function deleteIboPre(id?: number | string, options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** export as PDF GET /api/ibo-pre/export-xls */
export async function exportIboPreListInXls(params?: any): Promise<{ url?: string }> {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-xls`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/** export as PDF GET /api/ibo-pre/export-pdf */
export async function exportIboPreListInPdf(params?: any): Promise<{ url?: string }> {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-pdf`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/** export as PDF GET /api/ibo-pre/export-xls-with-ibo */
export async function exportIboPreListWithIboInXls(params?: any): Promise<{ url?: string }> {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-xls-with-ibo`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/**  
 * Get master price info.
 * 
 * GET /api/ibo/ibo-pre/openGrouped */
export async function getIboPreOpenGrouped(ean_id?: number, options?: Record<string, any>) {
  return request<API.ResultObject<API.IboPre[]>>(`${urlPrefix}/openGrouped`, {
    method: 'GET',
    params: {
      ean_id,
      ...options,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * Upload supplier's XLS file and import IboPres
 * 
 * POST /api/ibo/ibo-pre/import-xls */
export async function importXls(data?: Record<string, any>) {
  const url = `${urlPrefix}/import-xls`;
  const config: RequestConfig = {
    method: 'POST',
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.BaseResult>(url, config).then((res) => res.message);
}
