import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormSwitch } from '@ant-design/pro-form';
import { Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import Util from '@/util';
import { exportOfferItemList } from '@/services/foodstore-one/Offer/offer-item';

export type ExportPdfFormValueType = {
  percentage?: number;
  inc_price?: boolean;
  inc_trademark_logo?: boolean;
  inc_footer_address?: boolean;
  inc_footer_logo?: boolean;
  inc_footer_page_no?: boolean;
  page_break_per_trademark?: boolean;
};

export type ExportPdfSettingFormModalProps = {
  offer?: API.Offer;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: ExportPdfFormValueType) => Promise<boolean | void>;
  // parent search form
  getParentParams: () => any;
};

const ExportPdfSettingFormModal: React.FC<ExportPdfSettingFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<ExportPdfFormValueType>>();

  const { offer, modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (modalVisible && offer) {
      //formRef.current?.setFieldValue('wish_note', offerProp?.wish_note);
    }
  }, [offer, modalVisible]);

  return (
    <ModalForm<ExportPdfFormValueType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>PDF Export Settings</span>
        </Space>
      }
      width={400}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      size="small"
      labelCol={{ span: 12 }}
      labelAlign="left"
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      colon={false}
      onFinish={async (values) => {
        setLoading(true);

        const hide = message.loading('Exporting PDF...', 0);
        setLoading(true);
        exportOfferItemList({
          ...props.getParentParams(),
          ...values,
          format: 'pdf',
        })
          .then((res) => {
            window.open(`${API_URL}/api/${res.url}`, '_blank');
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Continue to Export PDF' },
        onReset(value) {
          props.handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading, loading: loading },
        submitButtonProps: { disabled: loading, loading: loading },
      }}
      modalProps={{
        confirmLoading: loading,
      }}
    >
      <SProFormDigit
        colProps={{ span: 'auto' }}
        name="percentage"
        label="Percentage"
        width={120}
        addonAfter={'%'}
        min={-99999999}
        fieldProps={{
          precision: 4,
        }}
        initialValue={90}
        placeholder="Percentage"
        formItemProps={{
          tooltip: (
            <div>
              <div>GFC Offer Price will be Price * (1 + percentage)</div>
            </div>
          ),
        }}
      />
      <ProFormSwitch name="only_gfc_active" label="Only GFC Active?" initialValue={true} />
      <ProFormSwitch name="inc_price" label="Include Price?" initialValue={true} />
      <ProFormSwitch name="inc_trademark_logo" label="Include Trademark Logo?" initialValue={true} />
      <ProFormSwitch name="inc_footer_address" label="Include Footer Address?" initialValue={true} />
      <ProFormSwitch name="inc_footer_logo" label="Include Footer Logo?" initialValue={true} />
      <ProFormSwitch name="inc_footer_truck" label="Include Footer Truck?" initialValue={true} />
      <ProFormSwitch name="inc_footer_page_no" label="Include Footer Page No?" initialValue={true} />
      <ProFormSwitch name="page_break_per_trademark" label="Page Break per Trademark?" initialValue={true} />

      <ProFormSelect
        name="version"
        label="Version"
        initialValue={'v3'}
        width={'sm'}
        options={[
          { value: 'v3', label: 'V3' },
          { value: 'v4', label: 'V4' },
        ]}
      />
    </ModalForm>
  );
};

export default ExportPdfSettingFormModal;
