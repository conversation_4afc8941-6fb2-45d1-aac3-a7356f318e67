/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/email-account';

/** rule GET /api/email-account */
export async function getEmailAccountList(
  params: API.PageParams &
    Partial<API.EmailAccount> & {
      customer_id?: number;
      supplier_id?: number;
      order_no?: number;
    },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.EmailAccount>> {
  return request<API.Result<API.EmailAccount>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/email-account */
export async function updateEmailAccount(data: Partial<API.EmailAccount>, options?: { [key: string]: any }) {
  return request<API.EmailAccount>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/email-account/{order_no} */
export async function updateOrderEmailAccount(
  order_no?: number,
  data?: Partial<API.EmailAccount> & { by_email_id?: number; type?: string },
  options?: { [key: string]: any },
) {
  return request<API.EmailAccount>(`${urlPrefix}/${order_no}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email-account */
export async function addEmailAccount(data: API.EmailAccount, options?: { [key: string]: any }) {
  return request<API.EmailAccount>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/email-account/{id} */
export async function deleteEmailAccount(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
