ALTER TABLE `ean_task`
    ADD COLUMN `status` INT NULL COMMENT '0: open, 1: in-progress 2: done' AFTER `task`
    , ADD COLUMN `user_id` BIGINT UNSIGNED NULL COMMENT 'assigned user' AFTER `status`
    , ADD CONSTRAINT `FK_ean_task_user_id` FOREIGN KEY (`user_id`) REFERENCES `user`(`user_id`) ON UPDATE SET NULL ON DELETE SET NULL;

ALTER TABLE `ean_file`
    ADD COLUMN `mag_id` INT NULL COMMENT 'Magento file ID' AFTER `label`;

CREATE TABLE `xmag_product_file`
(
    `sku`        VARCHAR(50) NOT NULL,
    `id`         INT         NOT NULL,
    `media_type` VARCHAR(255),
    `label`      VARCHAR(255),
    `position`   INT,
    `disabled`   BOOLEAN,
    `types`      VARCHAR(255),
    `file`       VARCHAR(255),
    `created_on` DATETIME,
    `updated_on` DATETIME,
    <PERSON>IMAR<PERSON> KEY (`sku`, `id`)
);

