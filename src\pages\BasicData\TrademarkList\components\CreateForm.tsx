import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addTrademark } from '@/services/foodstore-one/BasicData/trademark';
import { message } from 'antd';
import Util from '@/util';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import SProFormDigit from '@/components/SProFormDigit';
import useTrademarkGroupOptions from '@/hooks/BasicData/useTrademarkGroupOptions';

const handleAdd = async (fields: API.Trademark) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addTrademark(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Trademark) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  // Trademark group
  const { formElements } = useTrademarkGroupOptions(formRef);

  return (
    <ModalForm
      title={'New trademark'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({ ...value, group_id: value.group_id ?? null } as API.Trademark);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      {formElements}
      <ProFormSelect
        name="producers"
        label="Producers"
        placeholder="Please select producers"
        mode="multiple"
        request={getProducerListSelectOptions}
      />

      <ProFormText width="md" name="name_pdf" label="Name in PDF" tooltip="Name in catalogue PDF" />
      <SProFormDigit width="xs" name="sort_pdf" label="sort in PDF" tooltip="Sort in catalogue PDF" />
    </ModalForm>
  );
};

export default CreateForm;
