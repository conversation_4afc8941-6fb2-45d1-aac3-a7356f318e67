CREATE TABLE if not exists `tmp_import`
(
    `ean`         varchar(50)  default NULL,
    `title`       varchar(500) default NULL,
    `ingredients` longtext     default NULL,
    `item_base`   double       DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- ---------------------------------------------------------
-- Update Title by EAN
-- ---------------------------------------------------------
update ean_text
    inner join item_ean on item_ean.id = ean_text.ean_id
    inner join tmp_import on tmp_import.ean = item_ean.ean
set `name` = tmp_import.title
where ean_text.lang='DE' and tmp_import.title is not null;

-- ---------------------------------------------------------
-- Update Ingredients by EAN
-- ---------------------------------------------------------
update ean_text
    inner join item_ean on item_ean.id = ean_text.ean_id
    inner join tmp_import on tmp_import.ean = item_ean.ean
set `official_ingredients` = tmp_import.ingredients
where ean_text.lang='DE' and tmp_import.ingredients is not null;

-- ---------------------------------------------------------
-- Update item base by EAN
-- ---------------------------------------------------------
update item_ean
    inner join tmp_import on tmp_import.ean = item_ean.ean
set item_ean.`item_base` = tmp_import.item_base
where tmp_import.item_base is not null;


