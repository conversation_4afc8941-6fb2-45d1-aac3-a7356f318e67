import { Typo<PERSON>, Row, Col } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';
import { getIboList } from '@/services/foodstore-one/IBO/ibo';

import SPrices from '@/components/SPrices';

export type LatestIboListSummaryProps = {
  eanId?: number;
  itemId?: number;
  refreshTick?: number;
  filterType?: 'light' | 'query';
};

export type SearchFormValueType = Partial<API.Ibo>;

const LatestIboListSummary: React.FC<LatestIboListSummaryProps> = ({ eanId, itemId, refreshTick, filterType }) => {
  // const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.Ibo>[] = [
    {
      title: 'IBOM',
      dataIndex: ['ibom', 'supplier', 'name'],
      width: 130,
      showSorterTooltip: false,
      tooltip: '#{IBOM Order No} | {Sup. Name} ({Sup. No})',
      render: (dom, record) => (
        <Typography.Text>
          #{record?.ibom?.order_no || '-'} | {record?.ibom?.supplier?.name}{' '}
          {record?.ibom?.supplier?.supplier_no ? `(${record?.ibom?.supplier?.supplier_no})` : ''}
        </Typography.Text>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'price',
      valueType: 'digit',
      align: 'right',
      width: 90,
      render: (dom, record) => {
        const vat = record?.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                // setCurrentRow({ ...record });
                // setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12}>
                <SPrices price={record.price} vat={vat} hideGross />
              </Col>
              <Col span={12}>
                <SPrices price={(record?.price ?? 0) * (record?.item_ean?.attr_case_qty ?? 0)} vat={vat} hideGross />
              </Col>
            </Row>
          </>
        );
      },
    },
    {
      title: 'Order Date',
      dataIndex: ['ibom', 'order_date'],
      valueType: 'dateRange',
      align: 'center',
      ellipsis: true,
      render: (dom, record) => Util.dtToDMY(record.ibom?.order_date),
    },
  ];

  useEffect(() => {
    if (eanId) {
      actionRef.current?.reload();
    }
  }, [eanId]);

  useEffect(() => {
    if (itemId) {
      actionRef.current?.reload();
    }
  }, [itemId]);

  return (
    <ProTable<API.Ibo, API.PageParams>
      cardProps={{ bodyStyle: { padding: 0 } }}
      headerTitle="Latest Item Buying Orders"
      actionRef={actionRef}
      rowKey="id"
      bordered
      revalidateOnFocus={false}
      sticky
      scroll={{ x: '100%' }}
      size="small"
      loading={loading}
      onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
      pagination={{
        hideOnSinglePage: true,
        showSizeChanger: false,
      }}
      rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
      options={false}
      search={false}
      request={(params, sort, filter) => {
        let sortStr = JSON.stringify(sort);
        sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
        let newSort = Util.safeJsonParse(sortStr);
        if (Object.keys(newSort).length < 1) {
          newSort = { id: 'descend' };
        }
        // const searchValues = searchFormRef.current?.getFieldsValue();

        return getIboList(
          {
            ...params,
            with: 'lowestBuying',
            ean_id: eanId,
            item_id: itemId,
            filterMode: 'latest',
            pageSize: 8,
          },
          { ...newSort },
          { ...filter },
        );
      }}
      columns={columns}
      tableAlertRender={false}
    />
  );
};

export default LatestIboListSummary;
