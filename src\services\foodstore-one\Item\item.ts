/* eslint-disable */
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';

const urlPrefix = '/api/item/list';

/** get GET /api/item/list */
export async function getItemList(params: API.ItemSupplierPageParams, sort: any, filter: any) {
  const newSorter: any = {};
  if (sort) {
    Object.keys(sort).forEach((k) => {
      switch (k) {
        case 'supplier_name':
          newSorter['supplier.name'] = sort[k];
          break;
        default:
          newSorter[`item.${k}`] = sort[k];
          break;
      }
    });
  }
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(newSorter),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** get GET /api/item/get/{itemId} */
export async function getItem(itemId: number | undefined, params?: Record<string, any>): Promise<API.Item> {
  return request<API.BaseResult>(`${urlPrefix}/get/${itemId}`, {
    method: 'GET',
    params,
    withToken: true,
  }).then((res) => res.message);
}

/** put PUT /api/item/list/{id} */
export async function updateItem(data: API.Item, options?: { [key: string]: any }) {
  return request<API.Item>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/item/list */
export async function updateItemAll(
  data: {
    items: Array<API.Item & { categories?: API.Category[] | number[] | string[] }>;
  },
  options?: { [key: string]: any },
) {
  return request(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/item/list */
export async function addItem(data: API.Item, options?: { [key: string]: any }) {
  return request<API.Item>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/item/list */
export async function deleteItem(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/item/ac-list
 *
 * get the autocomplet lists.
 *
 */
export async function getItemACList(params: { [key: string]: string }, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      sort_detail: JSON.stringify(sort ?? { name: 'ascend' }),
    },
    withToken: true,
  }).then((res) => res.message.map((x: API.Item) => ({ ...x, value: x.name, label: `${x.name}` })));
}

/** post POST /api/item/create-with-ean */
export async function addItemWithEan(data: API.Item, options?: { [key: string]: any }) {
  return request<API.Item>(`/api/item/create-with-ean`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}
