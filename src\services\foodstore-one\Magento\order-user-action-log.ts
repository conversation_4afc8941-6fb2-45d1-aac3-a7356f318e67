
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/magento-data/order-user-action-log';

/** 
 * Get Order's User action logs
 * 
 * GET /api/magento-data/order-user-action-log */
export async function getMagOrderUserActionLogList(params: API.PageParams, sort?: any, filter?: any) {
    return request<API.ResultList<API.MagOrderUserActionLog>>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
        pagination: res.message.pagination, // For total row pagination hack.
    }));
}

/**
 * Create log
 *
 * POST /api/magento-data/order-user-action-log
 */
export async function createOrderUserActionLog(data: Partial<API.MagOrderUserActionLog>, options?: { [key: string]: any },
) {
    return request<API.BaseResult>(urlPrefix, {
        method: 'POST',
        data: data,
        ...(options || {}),
    });
}
