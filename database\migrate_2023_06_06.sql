ALTER TABLE `supplier`
    ADD COLUMN import_setting longtext NULL COMMENT 'Xls column to DB column mapping to import XLS file' AFTER `supplier_no`;

ALTER TABLE `ean_supplier`
    ADD UNIQUE INDEX `UQ_ean_supplier_full` (`ean_id`, `supplier_id`, `product_no`);


ALTER TABLE `ean_supplier`
    CHANGE `product_no` `product_no` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    DROP INDEX `UQ_ean_supplier_full`,
    DROP PRIMARY KEY,
    ADD PRIMARY KEY (`ean_id`, `supplier_id`, `product_no`);