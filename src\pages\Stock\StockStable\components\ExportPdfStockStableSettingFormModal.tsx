import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormRadio, ProFormTextArea } from '@ant-design/pro-form';
import { Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import Util from '@/util';
import { exportStockStableList } from '@/services/foodstore-one/Stock/stock-stable';

export type ExportPdfStockStableSettingFormValueType = {
  percentage?: number;
  inc_price?: boolean;
  inc_trademark_logo?: boolean;
  inc_footer_address?: boolean;
  inc_footer_logo?: boolean;
  inc_footer_page_no?: boolean;
  page_break_per_trademark?: boolean;
};

export type ExportPdfStockStableSettingFormModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: ExportPdfStockStableSettingFormValueType) => Promise<boolean | void>;
  // parent search form
  getParentParams: () => any;
};

const ExportPdfStockStableSettingFormModal: React.FC<ExportPdfStockStableSettingFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<ExportPdfStockStableSettingFormValueType>>();

  const { modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldValue('version', 'v2');
    }
  }, [modalVisible]);

  return (
    <ModalForm<ExportPdfStockStableSettingFormValueType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>PDF Export Settings - Stock Warehouses</span>
        </Space>
      }
      width={400}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      size="small"
      labelAlign="left"
      formRef={formRef}
      colon={false}
      onFinish={async (values) => {
        setLoading(true);

        const hide = message.loading('Exporting PDF...', 0);
        setLoading(true);
        exportStockStableList(
          {
            ...props.getParentParams(),
            ...values,
            format: 'pdf',
          },
          {},
          {},
        )
          .then((res) => {
            props.handleModalVisible(false);
            window.open(`${API_URL}/${res.url}`, '_blank');
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Continue to Export PDF' },
        onReset(value) {
          props.handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading, loading: loading },
        submitButtonProps: { disabled: loading, loading: loading },
      }}
      modalProps={{
        confirmLoading: loading,
      }}
    >
      <ProFormTextArea
        name="notes"
        label="Do you want to add a note to the report?"
        placeholder="Please fill your notes."
        fieldProps={{ rows: 7 }}
        width="lg"
      />
      <ProFormRadio.Group
        name="version"
        label="Version"
        options={[
          { value: 'v1', label: 'V1' },
          { value: 'v2', label: 'V2' },
        ]}
      />
    </ModalForm>
  );
};

export default ExportPdfStockStableSettingFormModal;
