import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Drawer, Card } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getTrademarkGroupList, deleteTrademarkGroup } from '@/services/foodstore-one/BasicData/trademark-group';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { ProForm, ProFormText, type ProFormInstance } from '@ant-design/pro-form';
import { history } from 'umi';

export type SearchFormValueType = Partial<API.TrademarkGroup>;

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.TrademarkGroup[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteTrademarkGroup(selectedRows.map((row) => row.id).join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const TrademarkGroupList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.TrademarkGroup>();
  const [selectedRowsState, setSelectedRows] = useState<API.TrademarkGroup[]>([]);

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();

  const columns: ProColumns<API.TrademarkGroup>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 50,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 400,
      sorter: true,
    },
    {
      title: 'Option',
      valueType: 'option',
      width: 50,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer
      extra={
        <>
          <Button
            type="link"
            key="Trademarks"
            title="Go to Trademarks"
            onClick={() => {
              history.push('/basic-data/trademark');
            }}
          >
            Trademarks
          </Button>
        </>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_trademark_groups', {
            name: '',
          })}
          submitter={{
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormText name={'name'} label="Name" width={200} placeholder={'Name'} />
        </ProForm>
      </Card>

      <ProTable<API.TrademarkGroup, API.PageParams>
        headerTitle={'Trademark Group list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_trademark_groups_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        params={{ with: '' }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_trademark_groups', searchFormValues);
          Util.setSfValues('sf_trademark_groups_p', params);

          return getTrademarkGroupList({ ...params, ...searchFormValues }, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              TrademarkGroup &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.TrademarkGroup>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<API.TrademarkGroup>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TrademarkGroupList;
