import { <PERSON><PERSON>, <PERSON>, Col, message, Row, Space, Spin, Typography } from 'antd';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import type { ActionType } from '@ant-design/pro-table';

import Util, { sEllipsed, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormSwitch } from '@ant-design/pro-form';
import { IRouteComponentProps } from 'umi';
import { DefaultOptionType } from 'antd/lib/select';
import styles from './index.less';
import SkuComp from '@/components/SkuComp';
import EanFilesComp from '@/components/EanFilesComp';
import ProList from '@ant-design/pro-list';
import useSearchEanByScan from '@/hooks/BasicData/useSearchEanByScan';
// import CreateOrUpdatePackedReadyQty from './components/CreateOrUpdatePackedReadyQty';
import QtyComp from '@/components/QtyComp';
import { PageSizeOptionsOnCard3, SysLogCategory } from '@/constants';
import { ArrowLeftOutlined, EditOutlined, ReloadOutlined } from '@ant-design/icons';
import useIboPreManagementOptions from '@/hooks/BasicData/useIboPreManagementOptions';
import { exportIboPreListInPdf, getIboPreList } from '@/services/foodstore-one/IBO/ibo-pre';
import CreateOrUpdatePackedReadyQty from './components/CreateOrUpdatePackedReadyQty';
import useOfferOptions from '@/hooks/BasicData/useOfferOptions';
import { PageContainer } from '@ant-design/pro-layout';
import CreateItemEanFormModal from './components/CreateItemEanFormModal';
import SysLogModal from '@/pages/Audit/SysLog/SysLogModal';
import UpdateNoteAndPictureForm from '../IboPreManagementList/components/UpdateNoteAndPictureForm';

export type SearchFormValueType = Partial<API.OfferItem> & { diff_qty_only?: boolean; trademark?: DefaultOptionType };

const DEFAULT_WITH =
  'iboPreManagement,incOfferItems,itemEan,itemEan.parent,itemEan.files,itemEan.eanTextDe,itemEan.magInventoryStocksQty,incOfferItemsDetail,offerItemPackedReadyListSum';

/**
 *
 * @param props
 * @returns
 */
const IboPreRegister: React.FC<IRouteComponentProps> = (props) => {
  const iboPreManagementIdInUrl = props.location.query?.ibo_pre_management_id;

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  // local states
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingIboPre, setLoadingIboPre] = useState<boolean>(false);
  const [currentIboPreManagement, setCurrentIboPreManagement] = useState<API.IboPreManagement>();

  const [matchedIboPre, setMatchedIboPre] = useState<API.IboPre | null>(); // After scanning EAN, we find the exact offer item

  const [openCreateOrUpdatePackedReadyQtyForm, setOpenCreateOrUpdatePackedReadyQtyForm] = useState<boolean>(false);
  // last activity modal
  const [openSysLogModal, setOpenSysLogModal] = useState<boolean>(false);

  // EAN creation related
  const [openEanCreateForm, setOpenEanCreateForm] = useState<boolean>(false);
  const [eanScannedStr, setEanScannedStr] = useState<string>('');

  // Update IBOPreMgmt modal
  const [openUpdateNoteAndPictureForm, setOpenUpdateNoteAndPictureForm] = useState<boolean>(false);

  const {
    iboPreManagementOptions,
    searchIboPreManagementOptions,
    loading: loadingIboPreManagement,
  } = useIboPreManagementOptions();

  // Offer search params
  const offerSearchParams = useMemo(
    () => ({ linked_ibo_pre_management_id: currentIboPreManagement?.id }),
    [currentIboPreManagement?.id],
  );

  // Offer search hooks
  const offerHookEleOptions = useMemo(() => {
    return {
      onChange: (value: any, option: any) => actionRef.current?.reload(),
      labelCol: { span: 8 },
    };
  }, []);
  const { formElements, offer } = useOfferOptions(offerSearchParams, searchFormRef, offerHookEleOptions);

  useEffect(() => {
    searchIboPreManagementOptions();
  }, [searchIboPreManagementOptions]);

  useEffect(() => {
    const selectedId = searchFormRef.current?.getFieldValue('ibo_pre_management_id');
    const tmp = iboPreManagementOptions?.find((x: any) => x.id == selectedId);
    if (tmp) {
      setCurrentIboPreManagement({ ...tmp });
    }
  }, [iboPreManagementOptions]);

  useEffect(() => {
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_ibo_pre_register', { diff_qty_only: true }));
  }, []);

  useEffect(() => {
    if (iboPreManagementIdInUrl) {
      searchFormRef.current?.setFieldValue('ibo_pre_management_id', sn(iboPreManagementIdInUrl));
      actionRef.current?.reload();
    }
  }, [iboPreManagementIdInUrl]);

  const {
    itemEan: itemEanScanned,
    setItemEan: setItemEanScanned,
    formElements: formElementsScanned,
  } = useSearchEanByScan({
    name: 'ean_scanned',
    style: { marginTop: 0, width: 150 },
    placeholder: 'Scan EAN',
    with: 'siblings,files',
    skipEanInfo: true,
    cbSearched: (ean: API.Ean) => {
      setOpenCreateOrUpdatePackedReadyQtyForm(true);
      setEanScannedStr('');
    },
    cbNotFound: (eanScanned) => {
      message.error(<>Not found EAN: {eanScanned}!</>, 5);
      setEanScannedStr(eanScanned || '');
    },
  });

  /**
   * Load Ibo Pre Detail with linked offer info
   */
  const loadIboPreDetail = useCallback(
    async (ibo_pre_id?: number) => {
      if (ibo_pre_id) {
        setLoading(true);
        return getIboPreList({
          id: ibo_pre_id,
          offer_id: offer?.id,
          with: DEFAULT_WITH,
        })
          .then((res) => {
            if (res) {
              setMatchedIboPre(res?.data?.[0]);
              return res?.data?.[0];
            } else {
              message.error('EAN does not exist in this offer! Please scan another one.');
              return Promise.resolve(null);
            }
          })
          .catch((err) => {
            Util.error(err);
            setMatchedIboPre(undefined);
            return Promise.resolve(null);
          })
          .finally(() => {
            setLoading(false);
          });
      }
      return Promise.resolve(null);
    },
    [offer?.id],
  );

  const handleClickOnTile = (entity: API.IboPre) => {
    setMatchedIboPre(entity);
    setItemEanScanned(entity.item_ean ?? null);
    setOpenCreateOrUpdatePackedReadyQtyForm(true);
  };

  useEffect(() => {
    if (itemEanScanned?.item_id && currentIboPreManagement?.id) {
      const hide = message.loading('Loading IBO Pre detail...', 0);
      setLoadingIboPre(true);
      const searchValues = searchFormRef.current?.getFieldsValue() || {};
      getIboPreList({
        ...searchValues,
        ibo_pre_management_id: currentIboPreManagement?.id,
        item_id: itemEanScanned?.item_id,
        pageSize: 1,
        with: DEFAULT_WITH,
      })
        .then((res) => {
          const firstRow = res.data?.[0];
          setMatchedIboPre(firstRow);
        })
        .catch(Util.error)
        .finally(() => {
          hide();
          setLoadingIboPre(false);
        });
    }
  }, [itemEanScanned?.item_id, currentIboPreManagement?.id]);

  useEffect(() => {
    if (matchedIboPre?.id && itemEanScanned?.id) {
      setOpenCreateOrUpdatePackedReadyQtyForm(true);
    }
  }, [matchedIboPre?.id, itemEanScanned?.id]);

  const handlePrintPdf = (is_last_activity?: '1') => {
    const hide = message.loading('Downloading list in PDF...', 0);

    const searchValues = searchFormRef.current?.getFieldsValue();
    Util.setSfValues('sf_ibo_pre', searchValues);

    const iboParam = {
      ...searchValues,
      nStatuses: ['canceled'],
      is_last_activity,
    };
    exportIboPreListInPdf(iboParam)
      .then((res) => {
        hide();
        if (res.url) {
          window.open(`${API_URL}/api/${res.url}`, '_blank');
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
      });
  };

  return (
    <PageContainer className={styles.preIboRegister}>
      <Spin spinning={loading || loadingIboPre}>
        <Card title={false} size="small" bordered={false}>
          <Row gutter={36} align="middle" wrap={false}>
            <Col>
              <Space direction="vertical" size={16}>
                <Space size={12}>
                  <Button
                    type="primary"
                    ghost
                    danger
                    size="large"
                    onClick={() => {
                      if (currentIboPreManagement) {
                        setOpenSysLogModal(true);
                      }
                    }}
                    disabled={!currentIboPreManagement}
                  >
                    Last Activity
                  </Button>
                  <Button
                    type={'primary'}
                    size="large"
                    ghost={!currentIboPreManagement?.note2}
                    onClick={() => {
                      if (currentIboPreManagement) {
                        setOpenUpdateNoteAndPictureForm(true);
                      }
                    }}
                    disabled={!currentIboPreManagement}
                    title="Update Note & Pictures"
                    icon={<EditOutlined />}
                  />
                </Space>
                <div className="ant-page-header-heading-title">{props.route.name}</div>
              </Space>
            </Col>
            <Col>
              <Space direction="vertical" size={16}>
                <Button
                  type="primary"
                  ghost
                  size="large"
                  onClick={() => {
                    actionRef.current?.reload();
                  }}
                  icon={<ReloadOutlined />}
                />
                <Button
                  type="primary"
                  ghost
                  size="large"
                  onClick={() => {
                    location.href = '/ibo/ibo-register-mobile';
                  }}
                  icon={<ArrowLeftOutlined />}
                />
              </Space>
            </Col>

            <Col flex="auto">
              <ProForm<SearchFormValueType>
                layout="horizontal"
                size="large"
                grid
                formRef={searchFormRef}
                isKeyPressSubmit
                className="search-form"
                submitter={false}
                labelCol={{ span: 6 }}
                labelAlign="left"
              >
                <Col span={13}>
                  <ProFormSwitch
                    label="Open only?"
                    name="diff_qty_only"
                    formItemProps={{ style: { marginBottom: 8 } }}
                    fieldProps={{
                      onChange(checked) {
                        actionRef.current?.reload();
                      },
                    }}
                  />
                  <ProFormSelect
                    name={'ibo_pre_management_id'}
                    showSearch
                    label="Pre Order"
                    options={iboPreManagementOptions}
                    request={searchIboPreManagementOptions}
                    fieldProps={{
                      loading: loadingIboPreManagement,
                      dropdownMatchSelectWidth: false,
                      onChange(value, option) {
                        setCurrentIboPreManagement(option as API.IboPreManagement);
                        actionRef.current?.reload();
                      },
                    }}
                    formItemProps={{ style: { marginBottom: 8 } }}
                  />

                  <ProForm.Item
                    name="eanScanned"
                    label="EAN"
                    style={{ marginBottom: 0 }}
                    addonAfter={
                      eanScannedStr ? (
                        <>
                          <Typography.Link
                            onClick={() => {
                              setOpenEanCreateForm(true);
                            }}
                          >
                            New?
                          </Typography.Link>
                        </>
                      ) : null
                    }
                  >
                    {formElementsScanned}
                  </ProForm.Item>
                </Col>
                <Col span={11}>
                  <Col span={24} style={{ textAlign: 'right' }}>
                    <Button type="primary" ghost onClick={(e) => handlePrintPdf()} style={{ marginBottom: 4 }}>
                      Print PDF
                    </Button>
                    <Button
                      type="primary"
                      ghost
                      onClick={(e) => handlePrintPdf('1')}
                      style={{ marginBottom: 4, marginLeft: 8 }}
                      title="Export PDF with Last Activities"
                    >
                      Print PDF 2
                    </Button>
                  </Col>
                  <ProFormSelect
                    name={'offer_exists'}
                    showSearch
                    label="Offer exists"
                    width={100}
                    options={[
                      { value: '', label: 'All' },
                      { value: '1', label: 'Exists Only' },
                      { value: '2', label: 'Not Exists Only' },
                    ]}
                    fieldProps={{
                      onChange(value, option) {
                        actionRef.current?.reload();
                      },
                    }}
                    labelCol={{ span: 8 }}
                    formItemProps={{ style: { marginBottom: 8 } }}
                  />
                  {formElements}
                </Col>
              </ProForm>
            </Col>
          </Row>
        </Card>

        <ProList<API.IboPre, API.PageParams>
          toolBarRender={false}
          actionRef={actionRef as any}
          rowKey="id"
          request={(params, sort, filter) => {
            let sortStr = JSON.stringify(sort || {});
            sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
            const newSort = Util.safeJsonParse(sortStr);

            const searchValues = searchFormRef.current?.getFieldsValue() || {};
            Util.setSfValues('sf_ibo_pre_register', searchValues);
            Util.setSfValues('sf_ibo_pre_register_p', params);

            const iboParam = {
              ...params,
              ...searchValues,
              trademarks: searchValues.trademark ? [searchValues.trademark?.value] : undefined,
              with: DEFAULT_WITH,
              orderByMode: 'xls',
            };

            return getIboPreList(iboParam, { ...newSort }, filter);
          }}
          onRequestError={Util.error}
          grid={{ gutter: 16, column: 3 }}
          tableAlertRender={false}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: sn(Util.getSfValues('sf_offer_item_mobile_p')?.pageSize ?? 60),
            pageSizeOptions: PageSizeOptionsOnCard3,
          }}
          itemCardProps={{ bodyStyle: { padding: 0 }, cover: true, size: 'small' }}
          metas={{
            type: {},
            title: {
              fieldProps: { style: { padding: 0 } },
              render(dom, entity) {
                return null;
              },
            },

            content: {
              fieldProps: { style: { padding: 0 } },
              render: (dom, entity, index) => {
                let bgCls = '';
                const packedQty = sn(entity.offer_item_packed_ready_list_sum_qty);
                const iboPreQty = sn(entity.case_qty) * sn(entity.qty);
                if (packedQty == iboPreQty) {
                  bgCls = 'bg-green1';
                } else if (packedQty > iboPreQty) {
                  bgCls = 'bg-orange1';
                } else if (packedQty > 0 && packedQty < iboPreQty) {
                  bgCls = 'bg-yellow1';
                }

                if (packedQty == 0 && entity.note_delivered) {
                  bgCls = 'bg-orange2';
                }

                return (
                  <div style={{ padding: '8px 8px', width: '100%', marginTop: -8 }} className={bgCls}>
                    <Row wrap={false} gutter={12}>
                      <Col flex="90px">
                        <div style={{ width: 80, height: 80 }}>
                          <EanFilesComp files={entity.item_ean?.files} width={80} outlineBorder={false} />
                        </div>
                        <div style={{ marginTop: 8 }}>
                          <SkuComp sku={entity.item_ean?.sku} noCopyable={true} />
                        </div>
                      </Col>
                      <Col
                        flex="auto"
                        onClick={(e) => {
                          handleClickOnTile(entity);
                        }}
                      >
                        <Row style={{ height: 70 }}>
                          <Col span={24}>
                            {sEllipsed(entity.item_ean?.ean_text_de?.name ?? entity.item_ean?.item?.name ?? '', 75)}
                          </Col>
                        </Row>
                        <Row wrap={false} gutter={8} style={{ paddingTop: 12 }}>
                          <Col flex="0 0 75px" style={{ fontSize: 12 }}>
                            <Space direction="vertical" size={0} style={{ textAlign: 'left', width: '100%' }}>
                              <Typography.Text style={{ color: entity.item_ean?.is_single ? 'blue' : '#000' }}>
                                {entity.item_ean?.parent?.ean}
                              </Typography.Text>

                              {/* {!entity.item_ean?.is_single ? (
                              <Typography.Text style={{ color: 'blue' }}>{entity.item_ean?.ean}</Typography.Text>
                            ) : (
                              <div>&nbsp;</div>
                            )} */}
                            </Space>
                          </Col>
                          <Col flex="auto">
                            {!!entity.qty && (
                              <Row style={{ textAlign: 'right' }}>
                                <Col span={12}>
                                  <div className="text-md bold cursor-pointer" style={{ fontSize: 16 }}>
                                    <QtyComp
                                      case_qty={1}
                                      qty={entity.offer_item_packed_ready_list_sum_qty}
                                      show_zero
                                      style={{ color: 'gray' }}
                                    />
                                  </div>
                                </Col>
                                <Col span={12}>
                                  <div className="text-md bold cursor-pointer" style={{ fontSize: 16 }}>
                                    <QtyComp case_qty={entity.case_qty} qty={entity.qty} show_zero />
                                  </div>
                                </Col>
                              </Row>
                            )}
                          </Col>
                        </Row>
                      </Col>
                    </Row>
                  </div>
                );
              },
            },
          }}
        />

        {itemEanScanned && matchedIboPre ? (
          <CreateOrUpdatePackedReadyQty
            itemEan={itemEanScanned}
            iboPre={matchedIboPre}
            offer_id={offer?.id}
            modalVisible={openCreateOrUpdatePackedReadyQtyForm}
            handleModalVisible={setOpenCreateOrUpdatePackedReadyQtyForm}
            onSubmit={async (deliveredRow) => {
              setItemEanScanned(null);
              setMatchedIboPre(null);
              actionRef.current?.reload();
            }}
            loadIboPreDetail={loadIboPreDetail}
            setIboPre={setMatchedIboPre}
          />
        ) : null}
      </Spin>

      {eanScannedStr && (
        <CreateItemEanFormModal
          modalVisible={openEanCreateForm}
          handleModalVisible={setOpenEanCreateForm}
          values={{ ean: eanScannedStr }}
          onSubmit={async (value) => {
            setEanScannedStr('');
          }}
        />
      )}

      {!!currentIboPreManagement?.id && (
        <SysLogModal
          modalVisible={openSysLogModal}
          handleModalVisible={setOpenSysLogModal}
          searchParams={{
            ibo_pre_management_id: currentIboPreManagement?.id,
            categories: [SysLogCategory.CATEGORY_IBO_PRE_REGISTER_PACKED_QTY_MODAL, SysLogCategory.CATEGORY_OFFER_ITEM],
          }}
        />
      )}

      <UpdateNoteAndPictureForm
        modalVisible={openUpdateNoteAndPictureForm}
        handleModalVisible={setOpenUpdateNoteAndPictureForm}
        initialValues={currentIboPreManagement}
        onSubmit={async (value) => {
          setCurrentIboPreManagement((prev) => ({ ...prev, ...value }));
        }}
      />
    </PageContainer>
  );
};

export default IboPreRegister;
