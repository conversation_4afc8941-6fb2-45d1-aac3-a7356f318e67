INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('PARCEL_TRACKING_GLS', 'Shipping Config', 'https://www.gls-pakete.de/sendungsverfolgung?trackingNumber=', 'GLS Tracking URL', ''),
       ('PARCEL_TRACKING_DPD', 'Shipping Config', 'https://business.dpd.de/meinepakete/tracking-tabelle.aspx?parcelno=', 'DPD Tracking URL', ''),
       ('PARCEL_TRACKING_DHL', 'Shipping Config', 'https://www.dhl.de/de/privatkunden/dhl-sendungsverfolgung.html?piececode=', 'DHL Tracking URL', '')
;
ALTER TABLE `xmag_order_parcel`
    ADD INDEX `IDX_xmag_order_parcel_ref_no` (`ref_no`);

ALTER TABLE `xmag_order_label`
    ADD INDEX `IDX_xmag_order_label_ref_no` (`ref_no`);


drop table if exists xmag_order_parcel_log;

CREATE TABLE `xmag_order_parcel_log`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT,
    `parcel_id`           int(11) NOT NULL COMMENT 'FK in xmag_order_parcel',
    `status`              varchar(255) DEFAULT NULL COMMENT 'Status',
    `note`                text         DEFAULT NULL COMMENT 'note from shipping provider',
    `detail`              text         DEFAULT NULL COMMENT 'detail info in JSON',
    `shipping_updated_on` datetime     DEFAULT NULL COMMENT 'Updated on shipping service',
    `updated_on`          datetime     DEFAULT NULL,
    `created_on`          datetime     DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_xmag_order_parcel_log_status` (`status`),
    KEY `FK_xmag_order_parcel_log_parcel_id` (`parcel_id`),
    CONSTRAINT `FK_xmag_order_parcel_log_parcel_id` FOREIGN KEY (`parcel_id`) REFERENCES `xmag_order_parcel` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


ALTER TABLE `xmag_order_parcel_log`
    ADD COLUMN `push_id` VARCHAR(255) NULL COMMENT 'Push ID' AFTER `detail`;


-- Data migration --
insert into xmag_order_parcel(service_name, parcel_no, ref_no, track_id, order_id)
select distinct xmag_order_label.service_name,
       xmag_order_label.parcel_no,
       xmag_order_label.ref_no,
       xmag_order_label.track_id,
       xmag_order_label.entity_id
from xmag_order_label
         left join xmag_order_parcel p on xmag_order_label.service_name = p.service_name and xmag_order_label.parcel_no = p.parcel_no and
                                          xmag_order_label.entity_id = p.order_id
where p.id is null
;



