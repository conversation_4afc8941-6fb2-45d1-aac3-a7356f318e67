import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import Util from '@/util';
import {
  stockStableBulkMove2TargetByName,
  StockStableCorrectionParamType,
} from '@/services/foodstore-one/Stock/stock-stable';
import WarehouseLocationSelector from '@/components/WarehouseLocationSelector';

type BulkStockMoveFormValueType = {
  new_wl_id?: number;
  new_wl_name?: string;
};

export type StockStableBulkMoveFormModalProps = {
  stockStableList: API.StockStable[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: StockStableCorrectionParamType) => void;
  // extra props
  orgStockStable?: API.StockStable;
};

const StockStableBulkMoveFormModal: React.FC<StockStableBulkMoveFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<BulkStockMoveFormValueType>>();

  const { stockStableList, modalVisible, handleModalVisible, onSubmit } = props;

  const [loading, setLoading] = useState(false);

  // Used to open Selection Modal without clicking "Select Location" button
  /* const [openLocationSelector, setOpenLocationSelector] = useState<boolean>(false);

  useEffect(() => {
    setOpenLocationSelector(modalVisible);
  }, [modalVisible]); */

  return (
    <ModalForm<BulkStockMoveFormValueType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>Move Stocks</span>
        </Space>
      }
      width={375}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      size={'large'}
      labelAlign="left"
      formRef={formRef}
      colon={false}
      onFinish={async (values) => {
        if (!values.new_wl_name) {
          message.error('Please select Warehouse Location!');
          return;
        }
        const hide = message.loading('Moving stocks...', 0);
        stockStableBulkMove2TargetByName(
          stockStableList.map((x) => ({
            id: x.id,
            ean_id: x.ean_id,
            wl_id: x.wl_id,
            exp_date: x.exp_date,
            ibo_id: x.ibo_id,
            piece_qty: x.piece_qty,
            box_qty: x.box_qty,
          })),
          values.new_wl_name,
        )
          .then((res) => {
            message.success('Moved successfully.');
            onSubmit?.(res);
            handleModalVisible(false);
          })
          .catch(Util.error)
          .finally(hide);
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Move' },
        onReset(value) {
          handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading },
        submitButtonProps: { disabled: loading },
      }}
      modalProps={{
        confirmLoading: loading,
        maskClosable: false,
        destroyOnClose: true,
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name={'new_wl_name'} label="Location" width={'sm'} placeholder={'Location. e.g. B0001'} />
      </div>

      <WarehouseLocationSelector
        defaultValue={Util.getSfValues('sf_stock_stable_mobile_update')?.wl_name || ''}
        onChange={(value) => {
          if (value.length >= 4) {
            formRef.current?.setFieldValue('new_wl_name', value);
          }
        }}
        showBodyScroll
        initialOpen={modalVisible}
      />
    </ModalForm>
  );
};

export default StockStableBulkMoveFormModal;
