import { DEFAULT_PER_PAGE_PAGINATION, DictCode, EURO } from '@/constants';
import {
  deleteIboPre,
  exportIboPreListInXls,
  exportIboPreListWithIboInXls,
  getIboPreList,
  updateIboPreBulk,
} from '@/services/foodstore-one/IBO/ibo-pre';
import Util, { nf2, nf3, ni, sUrlByTpl, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { FooterToolbar, PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Card, Col, Popover, Row, Space, Tag, Typography, message } from 'antd';
import { CommentOutlined, EyeOutlined, FileExcelOutlined, LinkOutlined } from '@ant-design/icons';
import { useEffect, useRef, useState } from 'react';
import { dsGetCustomAttribute } from '@/services/foodstore-one/Item/ean';
import { IRouteComponentProps, useModel } from 'umi';
import UpdateForm from './components/UpdateForm';
import CreateFormManagement from '../IboPreManagementList/components/CreateForm';
import UpdateFormManagement from '../IboPreManagementList/components/UpdateForm';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import SPrices from '@/components/SPrices';
import ImportXlsModalForm from './components/ImportXlsModalForm';
import useIboPreManagementOptions from '@/hooks/BasicData/useIboPreManagementOptions';
import IboPrePackedReadyListModal from './components/IboPrePackedReadyListModal';
import UpdateNotesDeliveredForm from './components/UpdateNotesDeliveredForm';

type BulkStatusForm = {
  status?: string;
};

export const IboPreStatusOptions = [
  { value: 'open', label: 'Open' },
  { value: 'sent', label: 'Sent' },
  { value: 'invoiced', label: 'Invoiced' },
  { value: 'done', label: 'Done' },
];

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.IboPre[]) => {
  const hide = message.loading('Deleting...');
  if (!selectedRows) return true;

  try {
    await deleteIboPre(selectedRows.map((row) => row.id).join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type RecordType = Partial<API.IboPre>;
export type SearchFormValueType = Partial<API.IboPre>;

export type IboPreListProps = {
  refreshTick?: number;
  filterType?: 'light' | 'query';
} & IRouteComponentProps;

const IboPreList: React.FC<IboPreListProps> = (props) => {
  const iboPreManagementIdInUrl = props.location.query?.ibo_pre_management_id;

  const { getDictByCode } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);

  // const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [openPreIBOMCreateModal, setOpenPreIBOMCreateModal] = useState<boolean>(false);

  const [currentIboPreManagement, setCurrentIboPreManagement] = useState<API.IboPreManagement>();
  const [openUpdateIboPreManagementModal, setOpenUpdateIboPreManagementModal] = useState<boolean>(false);
  // import modal visibility
  const [openImportModal, setOpenImportModal] = useState<boolean>(false);
  // IBO Pre packed ready qty modal
  const [openIboPrePackedQtyModal, setOpenIboPrePackedQtyModal] = useState<boolean>(false);
  // Delivery notes of current IBO Pre
  const [openIboPreDeliveryNotesUpdateForm, setOpenIboPreDeliveryNotesUpdateForm] = useState(false);

  // IBO Pre selection modal
  const [openIboPreSelectionForm, setOpenIboPreSelectionForm] = useState<boolean>(false);

  const {
    iboPreManagementOptions,
    searchIboPreManagementOptions,
    loading: loadingIboPreManagement,
  } = useIboPreManagementOptions();

  // bulk status update
  const bulkStatusFormRef = useRef<ProFormInstance<BulkStatusForm>>();
  const [openBulkStatusForm, setOpenBulkStatusForm] = useState<boolean>(false);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'PreOrder No',
      dataIndex: ['order_no'],
      width: 45,
      align: 'center',
      render(dom, record) {
        // return `#${record.ibo_pre_management?.order_no} | ${record.supplier?.name}`;
        // return `${record.ibo_pre_management?.order_no}`;
        return `${record.ibo_pre_management?.id}`;
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['supplier', 'name'],
      width: 60,
      hideInSearch: true,
      align: 'center',
      render(dom, entity, index, action, schema) {
        return (
          <Space direction="vertical" style={{ lineHeight: 1 }}>
            <span>{dom}</span>
            <span className="text-xs c-grey">{entity?.import?.supplier_add}</span>
          </Space>
        );
      },
    },
    {
      title: 'Item',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      align: 'left',
      width: 300,
      render: (dom, recordWrap) => {
        const record = recordWrap.item_ean;
        if (!record) return null;
        return (
          <Row gutter={8} wrap={false}>
            <Col flex="auto">
              <Typography.Text ellipsis title={`${record?.ean_text_de?.name || ''}`}>
                <a
                  href={`/item/ean-all-summary?sku=${record.item_id}_`}
                  target="_blank"
                  rel="noreferrer"
                  title="Open EAN All page in the new tab"
                >
                  {`${record.item_id || ''}_`}
                </a>
                {`--- ${record?.ean_text_de?.name || ''}`}
              </Typography.Text>
            </Col>
            <Col flex="0 0 20px">
              <Typography.Text
                title={`${record?.ean_text_de?.name || ''}`}
                copyable={{ text: `${record?.ean_text_de?.name || ''}` }}
              >
                {''}
              </Typography.Text>
            </Col>
            <Col flex="0 0 20px">
              <Typography.Link
                href={`/item/ean-all-summary?sku=${record.item_id}_`}
                title="Search EANs on new tab."
                target="_blank"
              >
                <LinkOutlined />
              </Typography.Link>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'VAT',
      dataIndex: ['item_ean', 'item', 'vat', 'value'],
      sorter: false,
      width: 50,
      ellipsis: true,
      align: 'right',
      hideInSearch: true,
      render: (dom, record) => (sn(record?.item_ean?.item?.vat?.value) ? `${record?.item_ean?.item?.vat?.value}%` : ''),
    },

    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 120,
      render: (dom, record) => {
        return (
          <a
            onClick={async () => {
              let urlKey = record?.item_ean?.mag_url?.value;
              if (!urlKey)
                urlKey = await dsGetCustomAttribute(record?.item_ean?.id || 0, {
                  force_update: 0,
                  attribute_code: 'url_key',
                }).catch(() => {
                  message.error('Not found SKU on the shop.');
                });

              if (urlKey) {
                window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
              }
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Package EAN (XLS)',
      dataIndex: ['xls_info', 'multi_ean'],
      copyable: true,
      tooltip: 'Package EAN in XLS data.',
      showSorterTooltip: false,
      width: 120,
    },
    {
      title: 'Last Sale (pcs)',
      dataIndex: ['last30_sales_qty'],
      width: 140,
      align: 'center',
      hideInSearch: true,
      tooltip: (
        <>
          <table style={{ textAlign: 'center' }} className="text-sm">
            <tr>
              <td>Sold pcs last 30 /365 days</td>
              <td>AVG Net Price Last 30 days</td>
            </tr>
            <tr>
              <td>
                <b>Stock Available Qty + Stock blocked Qty - processing orders Qty</b>
              </td>
              <td>Discount %</td>
            </tr>
          </table>
        </>
      ),
      render(dom, recordWrap) {
        const record: API.Ean & Record<string, any> = recordWrap.item_ean ?? {};
        const newStockQty = sn(record?.stock_mix_qty) - sn(record.processing_qty);
        const last30_sales_qty = sn(record.last30_sales_qty);
        const last30AvgPrice = last30_sales_qty ? sn(record.last30_cturover) / last30_sales_qty : 0;

        const last365_sales_qty = sn(record.last365_sales_qty);
        const last365AvgPrice = last365_sales_qty ? sn(record.last365_cturover) / last365_sales_qty : 0;

        return (
          <div style={{ textAlign: 'right' }}>
            <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
              <Col span={14} className="text-right">
                {!!last365_sales_qty || !!last30_sales_qty
                  ? `${ni(record.last30_sales_qty, !!last365_sales_qty)} / ${ni(record.last365_sales_qty)}`
                  : null}
              </Col>
              {last30AvgPrice || last365AvgPrice ? (
                <Col span={10} title={`Net Turnover last 30 days: ${nf2(record.last30_cturover)}${EURO}`}>
                  <Popover
                    content={
                      <table style={{ textAlign: 'left' }} className="text-sm">
                        <tr>
                          <td style={{ width: 100 }}>AVG (30)</td>
                          <td>
                            {last30AvgPrice ? (
                              <SPrices
                                price={last30AvgPrice}
                                vat={recordWrap.item_ean?.item?.vat?.value}
                                direction="horizontal"
                                showZero
                                showCurrency
                                noTextSmallCls
                              />
                            ) : null}
                          </td>
                        </tr>
                        <tr>
                          <td>AVG (365)</td>
                          <td className="text-right">
                            {last365AvgPrice ? (
                              <SPrices
                                price={last365AvgPrice}
                                vat={recordWrap.item_ean?.item?.vat?.value}
                                direction="horizontal"
                                showZero
                                showCurrency
                                noTextSmallCls
                              />
                            ) : null}
                          </td>
                        </tr>
                      </table>
                    }
                    trigger="hover"
                  >
                    &nbsp;
                    <SPrices
                      price={last30AvgPrice != 0 ? last30AvgPrice : last365AvgPrice}
                      vat={recordWrap.item_ean?.item?.vat?.value}
                      direction="horizontal"
                      showZero
                      showCurrency
                      noTextSmallCls
                      hideGross
                    />
                  </Popover>
                </Col>
              ) : null}
            </Row>
            <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
              <Col
                span={14}
                title={`${ni(record?.stock_mix_qty, true)} - ${ni(record.processing_qty, true)} = ${ni(
                  newStockQty,
                  true,
                )}`}
                style={{ fontWeight: 'bold', textAlign: 'left' }}
              >
                {ni(newStockQty)}
              </Col>
              <Col span={10} className={record.fs_special_discount ? 'c-red' : ''}>
                {record.fs_special_discount}
              </Col>
            </Row>
          </div>
        );
      },
    },
    {
      title: 'Qty/Pkg (XLS)',
      dataIndex: ['xls_info', 'case_qty'],
      width: 60,
      hideInSearch: true,
      align: 'right',
      className: 'bl2',
    },
    {
      title: 'Qty (pcs)',
      dataIndex: ['qty'],
      width: 70,
      align: 'right',
      hideInSearch: true,
      tooltip: 'Red Color --> Order Qty is not matched with Qty / Pkg',
      render(dom, record) {
        return ni(record.qty);
      },
      onCell: (record) => {
        let cls = '';
        const caseQtyXls = sn(record.xls_info?.case_qty);
        if (record.qty && caseQtyXls) {
          if (record.qty % caseQtyXls != 0) {
            cls += ' bg-light-red2';
          }
        }
        return {
          className: cls,
        };
      },
    },
    {
      title: 'Qty Packed (pcs)',
      dataIndex: ['offer_item_packed_ready_list_sum_qty'],
      width: 70,
      align: 'right',
      hideInSearch: true,
      className: 'cursor-pointer',
      tooltip: 'Click to view details...',
      render(dom, record) {
        return (
          <>
            <CommentOutlined
              className="cursor-pointer"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setCurrentRow(record);
                setOpenIboPreDeliveryNotesUpdateForm(true);
              }}
              style={{ color: record.note_delivered ? 'red' : 'gray', position: 'absolute', left: 2, top: 2 }}
            />
            {ni(record.offer_item_packed_ready_list_sum_qty)}
          </>
        );
      },
      onCell: (record) => {
        let cls = 'relative';
        return {
          className: cls,
          onClick: () => {
            if (record.offer_item_packed_ready_list_sum_qty) {
              setCurrentRow(record);
              setOpenIboPrePackedQtyModal(true);
            }
          },
        };
      },
    },

    {
      title: 'Qty (VE)',
      dataIndex: ['qty_ve_calc'],
      width: 60,
      hideInSearch: true,
      align: 'right',
      className: 'bl2',
      render(__, record) {
        const caseQtyXls = sn(record.xls_info?.case_qty);
        if (caseQtyXls) {
          return <span title={`${sn(record.qty) / caseQtyXls}`}>{ni(sn(record.qty) / caseQtyXls, false)}</span>;
        }
        return null;
      },
    },

    {
      title: 'Price',
      dataIndex: ['price_xls'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render(__, record) {
        return sn(record.price_xls) > 0 ? nf3(record.price_xls) + EURO : null;
      },
    },
    {
      title: 'Price Total',
      dataIndex: ['price_t'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render(__, record) {
        return sn(record.price_xls) > 0 ? nf2(sn(record.price_xls) * sn(record.qty)) + EURO : null;
      },
    },
    {
      title: 'Item No',
      dataIndex: ['product_no'],
      width: 90,
      hideInSearch: true,
      align: 'center',
      render(dom, record) {
        return record.product_no;
      },
    },
    /* {
      title: 'NAN',
      dataIndex: ['nan'],
      width: 90,
      hideInSearch: true,
      align: 'center',
    }, */
    {
      title: 'Offers',
      dataIndex: ['offer_ids'],
      className: 'text-xs',
      width: 100,
      render(dom, entity, index, action, schema) {
        const offer_ids = entity.offer_ids ? entity.offer_ids.split(',') : [];
        if (offer_ids.length) {
          return offer_ids.map((x) => {
            const arr = x.split(':');
            return (
              <a href={`/quotes/offer-item?offer_id=${arr[0]}&offer_no=${arr[1]}`} target="_blank">
                {arr[1]}
              </a>
            );
          });
        }
        return null;
      },
    },
    {
      title: 'Pallet (pcs)',
      dataIndex: ['qty_pallet'],
      width: 90,
      hideInSearch: true,
      align: 'right',
      onCell: (record) => {
        return {
          style: { opacity: 0.3 },
        };
      },
    },
    {
      title: 'Pallet Price',
      dataIndex: ['price_pallet'],
      width: 90,
      hideInSearch: true,
      align: 'right',
    },
    {
      title: 'Pallet Price Total',
      dataIndex: ['total'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render(dom, record) {
        if (record.qty && record.qty_pkg && record.qty % record.qty_pkg == 0 && record.price_pallet) {
          return nf2((sn(record.price_pallet) * record.qty) / sn(record.qty_pkg)) + EURO;
        }

        return null;
      },
    },

    {
      title: 'Status',
      dataIndex: ['status'],
      className: 'text-sm',
      width: 60,
      fixed: 'right',
      render(dom, record) {
        let color = '';
        if (record.status == 'done') {
          color = 'green';
        }
        return <Tag color={color}>{record.status}</Tag>;
      },
    },

    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 60,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({
              ...record,
            });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  useEffect(() => {
    searchIboPreManagementOptions();
  }, [searchIboPreManagementOptions]);

  useEffect(() => {
    const selectedId = searchFormRef.current?.getFieldValue('ibo_pre_management_id');
    const tmp = iboPreManagementOptions?.find((x: any) => x.id == selectedId);
    if (tmp) {
      setCurrentIboPreManagement({ ...tmp });
    }
  }, [iboPreManagementOptions]);

  useEffect(() => {
    if (iboPreManagementIdInUrl) {
      searchFormRef.current?.setFieldValue('ibo_pre_management_id', sn(iboPreManagementIdInUrl));
      actionRef.current?.reload();
    }
  }, [iboPreManagementIdInUrl]);

  return (
    <PageContainer
      title={
        <Space size={24}>
          <div>{props.route.name}</div>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setOpenPreIBOMCreateModal(true);
            }}
          >
            New
          </Button>
        </Space>
      }
    >
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ibo_pre', {
            ean_search_mode: 'contain_siblings',
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            name={'ibo_pre_management_id'}
            showSearch
            label="Pre Order"
            width={400}
            options={iboPreManagementOptions}
            request={searchIboPreManagementOptions}
            fieldProps={{
              filterOption: (inputValue: string, option?: any) => true,
              loading: loadingIboPreManagement,
              dropdownMatchSelectWidth: false,
              onChange(value, option) {
                setCurrentIboPreManagement(option as API.IboPreManagement);
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          <ProFormText name={'sku'} label="SKU" width={150} placeholder={'SKU'} />
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            request={async (params) => {
              const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
              if (res && res.data) {
                const tmp = res.data.map((x: API.Supplier) => ({
                  label: `${x.supplier_no} - ${x.name}`,
                  value: x.id,
                }));
                return tmp;
              }
              return [];
            }}
            width="sm"
            name="supplier_id"
            label="Supplier"
          />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        style={{ marginTop: 20 }}
        headerTitle="Pre Orders List"
        actionRef={actionRef}
        rowKey="id"
        bordered
        revalidateOnFocus={false}
        sticky
        scroll={{ x: '100%' }}
        size="small"
        onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ibo_pre_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
        options={{ fullScreen: true }}
        search={false}
        request={(params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ibo_pre', searchValues);
          Util.setSfValues('sf_ibo_pre_p', params);

          const iboParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            trademarks: [searchValues.trademark?.value],
            with: 'mode1,item_no,supplier,iboPreManagement,incOfferItems,offerItemPackedReadyListSum,import',
          };

          return getIboPreList(iboParam, { ...newSort }, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedRowsState.map((x) => sn(x.id)),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
        tableAlertRender={false}
        toolBarRender={() => [
          <div key="status">
            {currentIboPreManagement?.id ? (
              <>
                Status: <Tag>{currentIboPreManagement?.status}</Tag>
              </>
            ) : null}
          </div>,
          <Button
            key="openUpdateIboPreManagement"
            type="primary"
            size="small"
            ghost
            disabled={!currentIboPreManagement?.id}
            onClick={() => {
              setOpenUpdateIboPreManagementModal(true);
            }}
          >
            Update Selected Pre IBOM
          </Button>,
          <Button
            key="importXls"
            type="primary"
            size="small"
            className="btn-green"
            disabled={!currentIboPreManagement?.id}
            onClick={() => {
              setOpenImportModal(true);
            }}
          >
            Import
          </Button>,
          <Button
            key="export-xls"
            type="primary"
            size="small"
            icon={<FileExcelOutlined />}
            onClick={() => {
              const hide = message.loading('Downloading list in Xls...', 0);

              const searchValues = searchFormRef.current?.getFieldsValue();
              Util.setSfValues('sf_ibo_pre', searchValues);

              const iboParam = {
                ...Util.mergeGSearch(searchValues),
                trademarks: [searchValues.trademark?.value],
                nStatuses: ['canceled'],
                with: 'mode1,item_no,supplier,incOfferItems',
              };
              exportIboPreListInXls(iboParam)
                .then((res) => {
                  hide();
                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            Export XLS
          </Button>,
          <Button
            key="export-xls-summarized"
            type="primary"
            size="small"
            icon={<FileExcelOutlined />}
            onClick={() => {
              const hide = message.loading('Downloading list in Xls...', 0);

              const searchValues = searchFormRef.current?.getFieldsValue();
              Util.setSfValues('sf_ibo_pre', searchValues);

              const iboParam = {
                ...Util.mergeGSearch(searchValues),
                trademarks: [searchValues.trademark?.value],
                nStatuses: ['canceled'],
                summarized: 1,
                with: 'mode1,item_no,supplier,incOfferItems,offerItemPackedReadyListSum',
              };
              exportIboPreListInXls(iboParam)
                .then((res) => {
                  hide();
                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            Export XLS(Summarized)
          </Button>,
          <Button
            key="export-xls-with-ibo"
            type="primary"
            size="small"
            icon={<FileExcelOutlined />}
            onClick={() => {
              const hide = message.loading('Downloading list in Xls...', 0);

              const searchValues = searchFormRef.current?.getFieldsValue();
              Util.setSfValues('sf_ibo_pre', searchValues);

              const iboParam = {
                ...Util.mergeGSearch(searchValues),
                trademarks: [searchValues.trademark?.value],
                nStatuses: ['canceled'],
                summarized: 1,
                with: 'item_no,supplier,offerItemPackedReadyListSum',
              };
              exportIboPreListWithIboInXls(iboParam)
                .then((res) => {
                  hide();
                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            XLS (with IBO)
          </Button>,
          <Button
            key="openIboPre"
            type="primary"
            // type="link"
            size="small"
            ghost
            title="Open WholeSale (IBO Pre) Register in new tab"
            href={`/ibo/pre-ibo-register?ibo_pre_management_id=${currentIboPreManagement?.id}`}
            target="_blank"
            style={{ marginRight: 8, display: 'block' }}
            icon={<EyeOutlined />}
          />,
        ]}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                &nbsp;Pre IBOs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popover
            title="Move..."
            trigger="click"
            open={openIboPreSelectionForm}
            onOpenChange={(visible) => {
              setOpenIboPreSelectionForm(visible);
            }}
            content={
              <ProForm<{ ibo_pre_management_id?: number }>
                size="small"
                onFinish={async (values) => {
                  if (!values.ibo_pre_management_id) {
                    message.error('Please select IBO Pre!');
                    return;
                  }

                  const hide = message.loading('Moving selected SKUs to the selected IBO Pre...', 0);
                  updateIboPreBulk(
                    selectedRowsState.map((x) => sn(x.id)),
                    { ibo_pre_management_id: sn(values.ibo_pre_management_id) },
                  )
                    .then((offer) => {
                      message.success(`Moved to IBO Pre #${values.ibo_pre_management_id} successfully.`);
                      actionRef.current?.reload();
                      setOpenIboPreSelectionForm(false);
                      setSelectedRows([]);
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Move' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name={'ibo_pre_management_id'}
                  showSearch
                  label="Pre Order"
                  width={200}
                  options={iboPreManagementOptions}
                  request={(params) => {
                    return searchIboPreManagementOptions({
                      ...params,
                      nin_status: ['done', 'sent', 'invoiced'],
                      nin_id: [currentIboPreManagement?.id],
                    });
                  }}
                  fieldProps={{
                    dropdownMatchSelectWidth: false,
                  }}
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              title="Move to other IBO Pre..."
              onClick={() => setOpenIboPreSelectionForm(true)}
            >
              Move...
            </Button>
          </Popover>

          <Popover
            placement="top"
            title="Selection status option"
            trigger="click"
            open={openBulkStatusForm}
            onOpenChange={(visible) => {
              setOpenBulkStatusForm(visible);
            }}
            content={
              <ProForm<BulkStatusForm>
                formRef={bulkStatusFormRef}
                size="small"
                onFinish={async (values) => {
                  const hide = message.loading('Updating...', 0);
                  updateIboPreBulk(
                    selectedRowsState.map((x) => sn(x.id)),
                    values,
                    searchFormRef.current?.getFieldsValue(),
                  )
                    .then((res) => {
                      setOpenBulkStatusForm(false);
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .finally(() => {
                      hide();
                    });
                }}
                submitter={{
                  searchConfig: { submitText: 'Update' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect name="status" label="Status" options={IboPreStatusOptions} />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenBulkStatusForm(true)}
            >
              Update Status
            </Button>
          </Popover>
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      {/* <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      /> */}

      <ImportXlsModalForm
        modalVisible={openImportModal}
        handleModalVisible={setOpenImportModal}
        initialValues={currentIboPreManagement ?? {}}
        onSubmit={async (value) => {
          actionRef.current?.reload();
        }}
      />

      <UpdateFormManagement
        modalVisible={openUpdateIboPreManagementModal}
        handleModalVisible={setOpenUpdateIboPreManagementModal}
        initialValues={currentIboPreManagement ?? {}}
        onSubmit={async (value) => {
          setCurrentIboPreManagement(value);
          searchIboPreManagementOptions();
          actionRef.current?.reload();
        }}
        onCancel={() => {
          setOpenUpdateIboPreManagementModal(false);
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        // buyingHistoryComp={buyingHistoryComp}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />

      <CreateFormManagement
        modalVisible={openPreIBOMCreateModal}
        handleModalVisible={setOpenPreIBOMCreateModal}
        onSubmit={async (value) => {
          if (actionRef.current) {
            searchFormRef.current?.setFieldValue('ibo_pre_management_id', value.id);
            searchIboPreManagementOptions();
            actionRef.current.reload();
          }
        }}
      />

      <IboPrePackedReadyListModal
        iboPre={currentRow}
        modalVisible={openIboPrePackedQtyModal}
        handleModalVisible={setOpenIboPrePackedQtyModal}
        cbDelete={(data) => {
          actionRef.current?.reload();
        }}
      />

      <UpdateNotesDeliveredForm
        modalVisible={openIboPreDeliveryNotesUpdateForm}
        handleModalVisible={setOpenIboPreDeliveryNotesUpdateForm}
        initialValues={currentRow}
        supplierAdd={currentRow?.import?.supplier_add ?? currentIboPreManagement?.note_supplier}
        showDisabledEanList
        onSubmit={async (values) => {
          actionRef.current?.reload();
          return Promise.resolve(true);
        }}
      />
    </PageContainer>
  );
};

export default IboPreList;
