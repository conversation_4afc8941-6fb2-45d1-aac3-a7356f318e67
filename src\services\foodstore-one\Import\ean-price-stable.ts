import _ from 'lodash';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/import/ean-price-stable';


export async function getEanPriceStableList(params: API.PageParamsExt, sort: any, filter: any) {
  console.log(params);
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...filter,
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * POST  
 * POST /api/import/ean-price-stable */
export async function addEanPriceStable(
  data: Partial<API.EanPriceStable>,
  options?: Record<string, any>,
): Promise<API.EanPriceStable> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}


/**
 * Update all search filters of import
 *
 * PUT /api/import/ean-price-stable/{id} */
export async function updateEanPriceStable(id?: number, params?: Partial<API.EanPriceStable>) {
  return request<API.ResultObject<API.EanPriceStable>>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/import/ean-price-stable */
export async function deleteEanPriceStable(id?: number | string, options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}