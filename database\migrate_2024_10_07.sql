ALTER TABLE `offer`
    CHANGE `ibo_status` `ibo_status` ENUM (
        'open',
        'expecting items',
        'closed',
        'in discussion',
        'in preparation',
        'paid'
        ) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'open' NULL COMMENT 'Status for IBO. open,expecting items,closed';

ALTER TABLE `item_ean`
    ADD COLUMN `not_deliverable` BOOLEAN DEFAULT 0 NULL COMMENT 'Not deliverable' AFTER `status`;


ALTER TABLE `xmag_product`
    ADD COLUMN `not_deliverable` BOOLEAN DEFAULT 0 NULL COMMENT 'Not deliverable' AFTER `status`;

