import { <PERSON><PERSON>, <PERSON>, Col, Drawer, message, Row, Space, Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, skuToItemId, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import SDatePicker from '@/components/SDatePicker';
import _ from 'lodash';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import {
  getStockStableProblemList,
  updateStockStableProblem,
} from '@/services/foodstore-one/Stock/stock-stable-problem';
import EditableCell from '@/components/EditableCell';
import { FilePdfOutlined, FilterOutlined } from '@ant-design/icons';
import ExportPdfStockStableProblemSettingFormModal from './components/ExportPdfStockStableProblemSettingFormModal';

export enum StockStableProblemStatus {
  New = 'New',
  Check = 'Check',
  Closed = 'Closed',
}

export const StockStableProblemStatusOptions = [
  { value: StockStableProblemStatus.New, label: StockStableProblemStatus.New },
  { value: StockStableProblemStatus.Check, label: StockStableProblemStatus.Check },
  { value: StockStableProblemStatus.Closed, label: StockStableProblemStatus.Closed },
];

export type SearchFormValueType = Partial<API.StockStableProblem>;

const StockStableProblemList: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);

  const [currentRow, setCurrentRow] = useState<SearchFormValueType & { openParent?: boolean }>();
  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<API.StockStableProblem[]>([]);

  // Export PDF Option Form
  const [openExportPdfForm, setOpenExportPdfForm] = useState<boolean>(false);

  useEffect(() => {
    getWarehouseLocationACList({})
      .then((res) => {
        setWarehouseLocations(res);
      })
      .catch(Util.error);
  }, []);

  const columns: ProColumns<API.StockStableProblem>[] = [
    {
      title: '',
      dataIndex: ['apply_sku_filter'],
      hideInSearch: true,
      width: 20,
      render(__, entity) {
        return (
          <FilterOutlined
            className="cursor-pointer c-blue"
            title="Apply SKU filter here"
            onClick={() => {
              searchFormRef.current?.setFieldValue('sku', `${entity.item_id}_`);
              actionRef.current?.reload();
            }}
          />
        );
      },
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      width: 130,
      align: 'center',
      render(__, record) {
        return (
          <EditableCell
            dataType="select"
            defaultValue={record.status || ''}
            style={{ marginRight: 0 }}
            fieldProps={{ style: { lineHeight: 1 }, allowClear: true }}
            showSearch
            options={StockStableProblemStatusOptions as any}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateStockStableProblem(sn(record.id), {
                status: newValue ?? null,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {record.status}
          </EditableCell>
        );
      },
    },
    {
      title: 'SKU',
      dataIndex: ['sku'],
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      width: 100,
      render(__, entity) {
        return entity.sku ? (
          <Typography.Link href={`/item/ean-all-summary?sku=${skuToItemId(entity.sku)}_`} target="_blank" copyable>
            {entity.sku}
          </Typography.Link>
        ) : null;
      },
    },
    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      copyable: true,
      hideInSearch: true,
      width: 130,
    },

    {
      title: 'Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      ellipsis: true,
      hideInSearch: false,
      width: 180,
    },

    {
      title: 'Warehouse',
      dataIndex: ['warehouse_location', 'name'],
      width: 80,
      sorter: true,
      copyable: true,
      render(dom, entity) {
        return (
          <Typography.Link
            href={`/stock/stock-warehouse?sku=${skuToItemId(entity.sku)}_&wl_id=${entity.wl_id}`}
            target="_blank"
            copyable
          >
            {entity.warehouse_location?.name}
          </Typography.Link>
        );
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      width: 100,
      align: 'center',
      sorter: true,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Order ID',
      dataIndex: ['order_id'],
      width: 80,
      sorter: true,
      copyable: true,
      align: 'center',
      render(__, entity) {
        return (
          <a
            href={`/orders/order-detail?entity_id=${entity?.order_id}`}
            target="_blank"
            rel="noreferrer"
            title="Open Order detail page on new tab."
          >
            {entity?.order_id}
          </a>
        );
      },
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 70,
      align: 'right',
      className: 'c-grey',
      editable: false,
    },
    {
      title: 'Ordered Qty',
      dataIndex: ['qty_ordered'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (__, record) => ni(record.qty_ordered),
    },
    {
      title: 'Picked Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (__, record) => {
        const qty_picked = sn(sn(record.case_qty) > 1 ? record.box_qty : record.piece_qty);
        return ni(qty_picked);
      },
    },
    {
      title: 'Missing Qty',
      dataIndex: ['qty_missing'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (__, record) => {
        // We use qty_missing
        // const qty_ordered = sn(record.qty_ordered);
        // const qty_picked = sn(sn(record.case_qty) > 1 ? record.box_qty : record.piece_qty);
        return ni(record.qty_missing);
      },
      onCell: (entity) => ({ className: 'c-red bold' }),
    },
    {
      title: 'Stock Qty',
      dataIndex: ['qty_stock'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (__, record) => {
        const qty_stock = sn(sn(record.case_qty) > 1 ? record.stock_stable?.box_qty : record.stock_stable?.piece_qty);
        return ni(qty_stock);
      },
    },

    /* {
      title: 'BP / IBOM',
      dataIndex: ['ibo_id'],
      width: 120,
      align: 'left',
      editable: false,
      render: (dom, record: API.StockStableProblem) => {
        const ibo = record.stock_stable?.ibo;

        return ibo ? (
          <Row gutter={4}>
            <Col span={10}>{ibo.price ? `€${nf2(ibo.price)}` : ''}</Col>
            <Col
              span={14}
              className=" ant-table-cell-ellipsis"
              title={'IBO ID: #' + ibo.id}
            >{`#${ibo.ibom?.order_no} | ${ibo.ibom?.supplier?.name}`}</Col>
          </Row>
        ) : null;
      },
    }, */

    /* {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      tooltip: 'Click to view stock detail.',
      render: (__, record) => ni(record.box_qty),
      onCell: (record) => {
        if (record.id) {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleQtyModalVisible(true);
            },
          };
        }
        return {};
      },
    },
    {
      title: 'Total Piece Qty',
      dataIndex: ['total_piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      tooltip: 'Click to view stock detail.',
      render: (dom, record) => ni(record.total_piece_qty),
      onCell: (record) => {
        if (record.id) {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleQtyModalVisible(true);
            },
          };
        }
        return {};
      },
    }, */

    /* {
      title: 'BP',
      dataIndex: ['ibo', 'price'],
      width: 100,
      align: 'right',
      hideInSearch: true,
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        const isSingle = record.item_ean?.is_single;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12}>
                <SPrices price={record?.ibo?.price} vat={vat} />
              </Col>
              {!isSingle && (
                <Col span={12}>
                  <SPrices price={(record?.ibo?.price ?? 0) * (record?.case_qty ?? 0)} vat={vat} />
                </Col>
              )}
            </Row>
          </>
        );
      },
    },
    {
      title: 'BP * Qty',
      dataIndex: ['ibo', 'priceTotal'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row gutter={4} style={{ minHeight: 24 }}>
              <Col span={24}>
                <SPrices price={sn(record?.ibo?.price) * sn(record.total_piece_qty)} vat={vat} />
              </Col>
            </Row>
          </>
        );
      },
    }, */

    /* {
      title: 'Latest BP',
      dataIndex: ['item_ean', 'latest_ibo', 'price'],
      width: 100,
      align: 'right',
      hideInSearch: true,
      shouldCellUpdate: (record, prevRecord) =>
        !_.isEqual(record.item_ean?.latest_ibo, prevRecord.item_ean?.latest_ibo),
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        const isSingle = record.item_ean?.is_single;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12}>
                <SPrices price={record?.item_ean?.latest_ibo?.price} vat={vat} />
              </Col>
              {!isSingle && (
                <Col span={12}>
                  <SPrices
                    price={
                      (record?.item_ean?.latest_ibo?.price ?? 0) *
                      (record?.item_ean?.attr_case_qty ?? 0)
                    }
                    vat={vat}
                  />
                </Col>
              )}
            </Row>
          </>
        );
      },
    },
    {
      title: 'Latest BP * Qty',
      dataIndex: ['item_ean', 'latest_ibo', 'price'],
      width: 100,
      align: 'right',
      hideInSearch: true,
      shouldCellUpdate: (record, prevRecord) =>
        !_.isEqual(record.item_ean?.latest_ibo, prevRecord.item_ean?.latest_ibo),
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row gutter={4} style={{ minHeight: 24 }}>
              <Col span={24}>
                <SPrices
                  price={sn(record?.item_ean?.latest_ibo?.price) * sn(record.total_piece_qty)}
                  vat={vat}
                />
              </Col>
            </Row>
          </>
        );
      },
    }, */
    {
      title: 'Updated on',
      dataIndex: ['updated_on'],
      sorter: true,
      width: 120,
      defaultSortOrder: 'descend',
      className: 'c-grey text-sm',
      render: (__, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'IBO',
      dataIndex: ['ibo_id'],
      sorter: true,
      width: 60,
      render: (dom, record) => (
        <Row wrap={false}>
          <Col flex={'50px'}>{record.stock_stable?.ibo_id}</Col>
          {/* <Col flex={'30px'}>
            <a
              key="select-ibo"
              title="Select an IBO..."
              onClick={() => {
                setCurrentRow(record);
                setOpenSelectIboModalVisible(true);
              }}
            >
              <EditOutlined />
            </a>
          </Col> */}
        </Row>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_stock_stable_problem')}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Button
                  key="export-pdf"
                  type="primary"
                  htmlType="button"
                  loading={loading}
                  disabled={loading}
                  onClick={() => setOpenExportPdfForm(true)}
                  icon={<FilePdfOutlined />}
                >
                  Export PDF
                </Button>,
              ];
            },
          }}
        >
          <ProFormSelect
            name="status"
            options={StockStableProblemStatusOptions}
            label="Status"
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
          />
          <ProFormSelect
            name="wl_id"
            options={warehouseLocations}
            width="xs"
            label="Location"
            placeholder="Location"
            showSearch
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" placeholder={'EAN'} width={180} />
          <ProFormText name={'name'} label="Name" width={'sm'} placeholder={'Item Name'} />
          <SDatePicker name="created_on_start" label="Creation Date" width={120} />
          <SDatePicker name="created_on_end" addonBefore="~" width={120} />
        </ProForm>
      </Card>

      <ProTable<API.StockStableProblem, API.PageParams>
        headerTitle={
          <Space size={24}>
            <span>Stock Problems List</span>
            <FilterOutlined
              className="cursor-pointer c-red"
              title="Remove SKU filter"
              style={{ fontWeight: 'normal', fontSize: 12 }}
              onClick={() => {
                searchFormRef.current?.setFieldValue('sku', '');
                actionRef.current?.reload();
              }}
            />
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        size="small"
        scroll={{ x: 800 }}
        rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_stock_stable_problem', searchFormValues);

          if (searchFormValues.exp_date_start) {
            searchFormValues.exp_date_start = Util.dtToYMD(searchFormValues.exp_date_start);
          } else {
            searchFormValues.exp_date_start = undefined;
          }
          if (searchFormValues.exp_date_end) {
            searchFormValues.exp_date_end = Util.dtToYMD(searchFormValues.exp_date_end);
          } else {
            searchFormValues.exp_date_end = undefined;
          }
          setLoading(true);
          return getStockStableProblemList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              with: 'item,itemEan,stockStable,stockStable.ibo,warehouseLocation',
            },
            sort,
            filter,
          )
            .then((res) => {
              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.StockStableProblem) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
        columnEmptyText=""
      />
      {/* {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;Stocks.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popover
            title="Move Stocks"
            trigger="click"
            open={openBulkStockMoveForm}
            onOpenChange={(visible) => {
              setOpenBulkStockMoveForm(visible);
            }}
            content={
              <ProForm<BulkStockMoveFormValueType>
                size="small"
                onFinish={async (values) => {
                  const hide = message.loading('Moving stocks...', 0);
                  stockStableBulkMove2Target(
                    selectedRows.map((x) => ({
                      id: x.id,
                      ean_id: x.ean_id,
                      wl_id: x.wl_id,
                      exp_date: x.exp_date,
                      ibo_id: x.ibo_id,
                      piece_qty: x.piece_qty,
                      box_qty: x.box_qty,
                    })),
                    sn(values.new_wl_id),
                  )
                    .then((res) => {
                      message.success('Moved successfully.');
                      setOpenBulkStockMoveForm(false);
                      setSelectedRows([]);
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Move' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name="new_wl_id"
                  rules={[
                    {
                      required: true,
                      message: 'New Warehouse Location is required',
                    },
                  ]}
                  request={(params) => {
                    return getWarehouseLocationACList(params).catch(Util.error);
                  }}
                  width="md"
                  label="New Location"
                  placeholder="New Location"
                  showSearch
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenBulkStockMoveForm(true)}
            >
              Move Stocks
            </Button>
          </Popover>
        </FooterToolbar>
      )} */}
      {/* <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      /> */}

      <ExportPdfStockStableProblemSettingFormModal
        modalVisible={openExportPdfForm}
        handleModalVisible={setOpenExportPdfForm}
        getParentParams={() => {
          return {
            ssProblemIds: selectedRows?.length ? selectedRows.map((x) => x.id) : null,
            ...searchFormRef.current?.getFieldsValue(),
          };
        }}
      />

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.item_ean?.id && <ImportedPrices itemEan={currentRow?.item_ean} />}
      </Drawer>
    </PageContainer>
  );
};

export default StockStableProblemList;
