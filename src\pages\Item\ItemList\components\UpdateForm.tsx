import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateItem } from '@/services/foodstore-one/Item/item';
import Util, { sn } from '@/util';
import { getTrademarkList } from '@/services/foodstore-one/BasicData/trademark';
import { useModel } from 'umi';
import type { HandleNavFuncType } from '../../EanList/hooks/useModalNavigation';
import ModalNavigation from '../../EanList/components/ModalNavigation';
import useGdsnItem from '../../EanList/hooks/useGdsnItem';
import type { DefaultOptionType } from 'antd/lib/select';
import GdsnItemButton from '../../EanList/components/GdsnItemButton';
import useUpdateModalActions from '../../EanList/hooks/useUpdateModalActions';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateItem(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!');
    return false;
  }
};

export type FormValueType = {
  eanId?: number;
  ean?: string;
  sku?: string;
} & Partial<API.Item> & { closeModal?: boolean; trademark_id?: number; vat_id?: number };

export type UpdateFormProps = {
  initialValues?: FormValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Item) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  handleNavigation?: HandleNavFuncType;
  gdsn?: boolean;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { appSettings } = useModel('app-settings');
  const formRef = useRef<ProFormInstance<FormValueType>>();
  const [trademarks, setTrademarks] = useState<DefaultOptionType[]>([]);

  useEffect(() => {
    getTrademarkList({ pageSize: 500 }, { name: 'ascend' }, {})
      .then((res) => {
        if (res && res.data) {
          setTrademarks(
            res.data.map((x: API.Trademark) => ({
              label: `${x.name}`,
              value: x.id,
            })),
          );
        }
      })
      .catch(Util.error);
  }, []);

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  // GDSN data
  const {
    gdsnItem,
    fetchGdsnItem,
    renderTakeButton,
    loading: loadingGdsn,
  } = useGdsnItem(props.initialValues?.ean, props.modalVisible);

  const [gdsnItemsDiff, setGdsnItemsDiff] = useState<any>({});

  const dietsCodeList = useMemo<string[]>(
    () => gdsnItem?.detail?.diets?.map((x: any) => x.code) ?? [],
    [gdsnItem?.detail?.diets],
  );

  const validateGdsnItemsDiff = useCallback(() => {
    const values = formRef.current?.getFieldsValue() ?? {};

    setGdsnItemsDiff({
      name: values.name == gdsnItem?.name,
      hs_code: values.hs_code == gdsnItem?.detail?.hs_code,
      vat_id: values.vat_id == gdsnItem?.detail?.vat?.id,
      fs_bio_certificate: values.fs_bio_certificate == gdsnItem?.detail3?.bio_certificate,
      fs_bio_origin: values.fs_bio_origin == gdsnItem?.detail3?.bio_origin,
      trademark_id:
        (!values.trademark_id && !gdsnItem?.detail?.trademark) ||
        (values.trademark_id &&
          values.trademark_id == trademarks.find((x) => x.label == gdsnItem?.detail?.trademark)?.value),
      special_filter: JSON.stringify(values.special_filter) == JSON.stringify(dietsCodeList),
    });
  }, [
    dietsCodeList,
    gdsnItem?.detail?.hs_code,
    gdsnItem?.detail?.trademark,
    gdsnItem?.detail?.vat?.id,
    gdsnItem?.name,
    trademarks,
  ]);

  useEffect(() => {
    if (props.modalVisible && props.initialValues?.ean && props.initialValues?.id) {
      validateGdsnItemsDiff();
    }
  }, [props.modalVisible, props.initialValues?.ean, validateGdsnItemsDiff, props.initialValues?.id]);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useUpdateModalActions(
    props.initialValues?.eanId ?? 0,
    props.initialValues?.sku ?? '',
    formRef.current,
  );

  return (
    <ModalForm
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update Item</span>
          <ModalNavigation
            modalName="item"
            itemId={props.initialValues?.id}
            eanId={props.initialValues?.eanId}
            handleNavigation={props.handleNavigation}
            style={{ marginLeft: 50 }}
          />
          {props.gdsn && (
            <GdsnItemButton
              ean={props.initialValues?.ean}
              eanId={props.initialValues?.eanId}
              itemId={props.initialValues?.id}
              style={{ marginLeft: 30 }}
              fetchGdsnItem={fetchGdsnItem}
            />
          )}
          {actionButtons}
        </div>
      }
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      labelAlign="left"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({
          ...value,
          id: props.initialValues?.id,
        });

        if (success) {
          await runActionsCallback();

          if (value.closeModal) props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {actionButtons}
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Save & Close
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
      disabled={loadingGdsn}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
        {hiddenFormElements}
      </div>
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        name="name"
        label="Name"
        fieldProps={{
          onChange(value) {
            validateGdsnItemsDiff();
          },
        }}
        help={
          gdsnItem?.detail &&
          renderTakeButton(
            gdsnItem.name,
            () => {
              formRef.current?.setFieldValue(['name'], gdsnItem.name);
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.name,
          )
        }
      />
      <ProFormSelect
        showSearch
        placeholder="Select filters"
        mode="multiple"
        options={appSettings.itemSpecialFilters ?? []}
        width="md"
        name="special_filter"
        label="Special Filters"
        help={
          gdsnItem?.detail &&
          renderTakeButton(
            dietsCodeList.join(', '),
            () => {
              formRef.current?.setFieldValue(['special_filter'], dietsCodeList);
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.name,
          )
        }
      />
      {/* <ProFormSelect
        showSearch
        placeholder="Select a supplier"
        request={async (params) => {
          const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Item) => ({
              label: `${x.id} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        width="md"
        name="supplier_id"
        label="Supplier"
        convertValue={(value) => (value ? sn(value) : value)}
      /> */}
      <ProFormSelect
        showSearch
        placeholder="Select a trademark"
        options={trademarks}
        width="md"
        name="trademark_id"
        label="Trademark"
        convertValue={(value) => (value ? sn(value) : value ?? null)}
        colProps={{ xl: 6 }}
        fieldProps={{
          onChange(value) {
            validateGdsnItemsDiff();
          },
        }}
        help={
          gdsnItem?.detail &&
          renderTakeButton(
            gdsnItem.detail?.trademark,
            () => {
              formRef.current?.setFieldValue(
                ['trademark_id'],
                trademarks.find((x) => x.label == gdsnItem.detail?.trademark)?.value,
              );
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.trademark_id,
          )
        }
      />
      <ProFormSelect
        showSearch
        placeholder="Select a VAT"
        request={async (params) =>
          appSettings.vats.map((x) => ({
            label: `${x.value}%`,
            value: x.id,
          }))
        }
        width="md"
        name="vat_id"
        label="VAT"
        convertValue={(value) => (value ? sn(value) : value)}
        fieldProps={{
          onChange(value) {
            validateGdsnItemsDiff();
          },
        }}
        help={
          gdsnItem?.detail &&
          renderTakeButton(
            gdsnItem.detail?.vat ? `${gdsnItem.detail?.vat?.value} | ${gdsnItem.detail?.vat?.code}` : null,
            () => {
              formRef.current?.setFieldValue(['vat_id'], gdsnItem.detail?.vat?.id);
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.vat_id,
          )
        }
      />
      <ProFormText
        name="hs_code"
        label="HS Code"
        fieldProps={{
          onChange(value) {
            validateGdsnItemsDiff();
          },
        }}
        help={
          gdsnItem?.detail &&
          renderTakeButton(
            gdsnItem.detail?.hs_code,
            () => {
              formRef.current?.setFieldValue(['hs_code'], gdsnItem.detail?.hs_code);
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.hs_code,
          )
        }
      />

      <ProFormText
        name="fs_bio_certificate"
        label="Bio Certificate"
        fieldProps={{
          onChange(value) {
            validateGdsnItemsDiff();
          },
        }}
        help={
          gdsnItem?.detail3 &&
          renderTakeButton(
            gdsnItem.detail3?.bio_certificate,
            () => {
              formRef.current?.setFieldValue(['fs_bio_certificate'], gdsnItem.detail3?.bio_certificate);
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.fs_bio_certificate,
          )
        }
      />

      <ProFormSelect
        showSearch
        placeholder="Bio PlaceOfFarming"
        options={['NON_EU_AGRICULTURE', 'EU_AGRICULTURE', 'EU_OR_NON_EU_AGRICULTURE']}
        width="md"
        name="fs_bio_origin"
        label="Bio PlaceOfFarming"
        fieldProps={{
          onChange(value) {
            validateGdsnItemsDiff();
          },
        }}
        help={
          gdsnItem?.detail3 &&
          renderTakeButton(
            `${gdsnItem.detail3?.bio_origin}`,
            () => {
              formRef.current?.setFieldValue(['fs_bio_origin'], gdsnItem.detail3?.bio_origin);
              validateGdsnItemsDiff();
            },
            gdsnItemsDiff.fs_bio_origin,
          )
        }
      />

      <ProFormTextArea width="lg" name="description" label="Description" />
    </ModalForm>
  );
};

export default UpdateForm;
