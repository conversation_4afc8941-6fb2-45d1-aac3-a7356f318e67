import _ from 'lodash';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/import/import-ean-disabled';


export async function getImportEanDisabledList(params: API.PageParamsExt, sort: any, filter: any) {
  console.log(params);
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...filter,
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * POST  
 * POST /api/import/import-ean-disabled */
export async function addImportEanDisabled(
  data: Partial<API.ImportEanDisabled>,
  options?: Record<string, any>,
): Promise<API.ImportEanDisabled> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}


/**
 * Update all search filters of import
 *
 * PUT /api/import/import-ean-disabled/{id} */
export async function updateImportEanDisabled(id?: number, params?: Partial<API.ImportEanDisabled>) {
  return request<API.ResultObject<API.ImportEanDisabled>>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/import/import-ean-disabled */
export async function deleteImportEanDisabled(id?: number | string, options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}