truncate table ean_price_stable;

-- According to design spec, we'd better add important info in stable price table.
ALTER TABLE `ean_price_stable`
    ADD COLUMN `case_qty` DOUBLE NULL AFTER `deleted_on`,
    ADD COLUMN `ve_pallet` DOUBLE NULL AFTER `case_qty`,
    ADD COLUMN `exp_date` VARCHAR (30) NULL AFTER `ve_pallet`,
    ADD COLUMN `shelf_life` INT NULL AFTER `exp_date`,
    ADD COLUMN `hs_code` INT NULL AFTER `shelf_life`,
    ADD COLUMN `detail` TEXT NULL AFTER `hs_code`;

-- Indexes to improve search.
ALTER TABLE `import_ean_disabled`
    ADD INDEX `IDX_import_ean_disabled_start_date` (`start_date`),
    ADD INDEX `IDX_import_ean_disabled_end_date` (`end_date`);

ALTER TABLE `import_ean_disabled`
    ADD INDEX `IDX_import_ean_disabled_supplier_add` (`supplier_add`);
