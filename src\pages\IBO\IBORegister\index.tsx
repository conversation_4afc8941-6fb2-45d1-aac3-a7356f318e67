import { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormCheckbox, ProFormItem, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Button, Card, message, Popover, Modal } from 'antd';
import { getEanDetailOrGdsnEan } from '@/services/foodstore-one/Item/ean';
import { debounce } from 'lodash';
import SProFormDigit from '@/components/SProFormDigit';
import type { Rule } from 'antd/lib/form';
import SDatePicker from '@/components/SDatePicker';
import Util, { sn, sParseEan } from '@/util';
import { DeleteFilled, InfoCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { createIboDraftDetail } from '@/services/foodstore-one/IBO/ibo-draft-detail';
import IBODraftDetailList from './IBODraftDetailList';
import moment from 'moment';
import styles from './style.less';
import { useLocation } from 'umi';

const defaultItemEan: API.Ean = {
  id: undefined,
  attr_case_qty: undefined,
  parent: undefined,
  ean: undefined,
  ean_texts: [],
};

type IboDraftDetailFormType = API.IboDraftDetail & {
  uid: React.Key;
  box_qty_all?: number;
  qty_all?: number;
  children?: IboDraftDetailFormType[];
};

const defaultListData: IboDraftDetailFormType[] = new Array(1).fill(1).map((_, index) => {
  return {
    uid: Date.now().toString() + '-' + index,
    item_ean: defaultItemEan,
    exp_date: undefined,
    warehouse_location: undefined,
    box_qty: undefined,
    qty_all: undefined,
    box_qty_all: undefined,
    qty: undefined,
  };
});

const IBORegister: React.FC<any> = (props) => {
  const location: any = useLocation();

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<API.IboDraftDetail>>({});
  const [refreshTick, setRefreshTick] = useState<number>(0);

  const formRef = useRef<ProFormInstance>();

  // Editable table form
  const editableFormRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => defaultListData.map((item) => item.uid));

  const [dataSource, setDataSource] = useState<IboDraftDetailFormType[]>(() => defaultListData);

  // Reference data
  const [warehouseLocations, setWarehouseLocations] = useState<any>(undefined);

  useEffect(() => {
    getWarehouseLocationACList({}).then((res) => {
      setWarehouseLocations(res);
    });
  }, []);

  useEffect(() => {
    if (location.query.ibomId) {
      formRef.current?.setFieldValue(['ibom', 'id'], {
        value: sn(location.query.ibomId),
        // label: location.query.ibomId,
      });
      actionRef.current?.reload();
      setFormData((prev) => ({ ...prev, ibom: { id: sn(location.query.ibomId) } }));
    }
  }, [location.query.ibomId]);

  // ---------------------------------------------------------------------------------
  // Search EAN
  // ---------------------------------------------------------------------------------
  const handleSearchEan = async (v: string, cb: any) => {
    if (!v) {
      cb(null);
      return;
    }
    message.destroy();
    const hide = message.loading('Searching EAN...', 0);
    // await getEanDetailOrImportedEan({ eanExact: v, with: 'suppliers' })
    await getEanDetailOrGdsnEan({ eanExact: v, with: 'suppliers' })
      .then((res) => {
        cb(res);
      })
      .catch(() => {
        cb(null);
      })
      .finally(() => hide());
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearchEan = useCallback(
    debounce((newValue, cb) => handleSearchEan(newValue, cb), 330),
    [],
  );

  const hiddenColumns: ProColumns<IboDraftDetailFormType>[] = useMemo(
    () => [
      {
        dataIndex: ['item_ean'],
        hideInTable: false,
        hideInForm: false,
        width: 1,
        fieldProps: { hidden: true },
      },
      {
        dataIndex: ['uid'],
        hideInTable: false,
        hideInForm: false,
        width: 1,
        fieldProps: { hidden: true },
      },
    ],
    [],
  );

  const columns: ProColumns<IboDraftDetailFormType>[] = useMemo(
    () => [
      ...hiddenColumns,
      {
        title: (
          <>
            EAN&nbsp;
            <Popover
              title="Parsable EAN examples in barcode"
              content={
                <div style={{ width: 550 }}>
                  <div>
                    <span>04018077004384</span>: <span className="c-green-dark">4018077004384</span>
                  </div>
                  <div>
                    <span>
                      (01)<b>04018077004384</b>(10)001004
                    </span>{' '}
                    : <span className="c-green-dark">4018077004384</span>
                  </div>
                  <div>
                    <span>
                      (01)<b>04018077004384</b>(10)00100420(15)<b>230713</b>
                    </span>{' '}
                    : <span className="c-green-dark">4018077004384, Exp. Date: 230713</span>
                  </div>
                  <div>
                    <span>
                      (01)<b>04018077004384</b>(10)<b>230713</b>(15)111111
                    </span>{' '}
                    : <span className="c-green-dark">4018077004384, Exp. Date: 230713</span>
                  </div>
                  <div>
                    <span>01087234007957971525073110E7364451 (starts with "010")</span> :{' '}
                    <span className="c-green-dark">8723400795797</span>
                  </div>
                </div>
              }
            >
              <InfoCircleOutlined />
            </Popover>
          </>
        ),
        dataIndex: ['item_ean', 'ean'],
        listKey: 'ean',
        fieldProps: {
          placeholder: 'EAN',
        },
        formItemProps: (form) => {
          const rules = [{ required: true, message: 'EAN is required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        width: 180,
        renderFormItem: (item, { record }) => {
          return (
            <ProFormText
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                placeholder: 'EAN',
                id: `ean${item.index}`,
                onChange: (e: any) => {
                  const only_right_13 = formRef.current?.getFieldValue('only_right_13');
                  console.log('mode', only_right_13);
                  let curEan: string = '';
                  let tmp: string[] = [];
                  if (only_right_13) {
                    curEan = e.target.value.slice(-13);
                    tmp = [curEan, ''];
                  } else {
                    tmp = sParseEan(e.target.value ?? '');
                    curEan = tmp[0];
                  }
                  // console.log(curEan, record?.item_ean?.ean ?? null);
                  debouncedHandleSearchEan(curEan, (eanData: API.Ean) => {
                    if (eanData) {
                      console.log('[ibo][search][eanData]', eanData);
                      setDataSource((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.uid == record?.uid);
                        if (current) {
                          current.item_ean = {
                            id: eanData.id,
                            ean: eanData.ean,
                            attr_case_qty: eanData.attr_case_qty,
                            parent: eanData.parent,
                            ean_texts: eanData.ean_texts,
                            gdsn_item: eanData.gdsn_item,
                            expecting_offer_items: eanData.expecting_offer_items,
                          };
                          /* setColStates((p) => ({
                            ...p,
                            parent_ean: { show: sn(current?.item_ean?.attr_case_qty) > 1 },
                          })); */

                          if (tmp[1]) {
                            current.exp_date = tmp[1];
                          }
                          console.log('[ibo][search][updatedResult]', current);
                        } else {
                          console.log('** Not found: ', newList, record?.uid);
                        }
                        return newList;
                      });
                    } else {
                      setDataSource((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.uid == record?.uid);
                        if (current) {
                          current.item_ean = {
                            ...defaultItemEan,
                            ean: curEan,
                          };
                          if (tmp[1]) {
                            current.exp_date = tmp[1];
                          }
                          current.item_ean.ean_texts = [{ name: '' }];
                        }
                        return newList;
                      });
                    }
                  });
                },
                onBlur: () => {},
              }}
            />
          );
        },
      },
      {
        title: 'Text of EAN',
        dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
        listKey: 'ean_name',
        fieldProps: {
          placeholder: 'Text of EAN',
        },
        width: 350,
        formItemProps: (form) => {
          const rules = [{ required: true, message: 'Text of EAN is required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender, record }) => {
          return record?.item_ean?.id ? record.item_ean?.ean_texts?.[0]?.name : defaultRender(item);
          // <ProFormText placeholder="Text of EAN" formItemProps={{ style: { marginBottom: 0 } }} />
        },
      },
      {
        title: 'Qty of Boxes',
        dataIndex: ['box_qty'],
        listKey: 'box_qty',
        valueType: 'digit',
        sorter: false,
        align: 'center',
        width: 100,
        className: 'p-0',
        formItemProps: (form) => {
          const rules: Rule[] = [{ required: true, message: 'Qty of Boxes Required!' }];
          return {
            rules,
            hasFeedback: true,
          };
        },
        renderFormItem: (item, { defaultRender }) => {
          return <SProFormDigit placeholder="Qty of Boxes" min={1} formItemProps={{ style: { marginBottom: 0 } }} />;
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        listKey: 'exp_date',
        valueType: 'date',
        sorter: false,
        align: 'right',
        width: 150,
        formItemProps: (form) => {
          const rules: Rule[] = [];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender }) => {
          return <SDatePicker placeholder="EXP. Date" formItemProps={{ style: { marginBottom: 0 } }} />;
        },
      },
      {
        title: 'Days',
        dataIndex: ['exp_date_diff'],
        listKey: 'exp_date_diff',
        sorter: false,
        width: 70,
        editable: false,
        render(__, record) {
          if (record.exp_date) {
            const mDate = record.exp_date ? moment(record.exp_date) : null;
            const days = mDate && mDate.isValid() ? mDate?.diff(moment(), 'days') : 0;
            let cls = '';
            if (days <= 60) {
              cls = 'c-red';
            }
            return (
              <span className={cls} title={`Expired after ${days} days.`}>
                {days < 14 ? <WarningOutlined className="c-red" /> : null} {days}
              </span>
            );
          }
          return null;
        },
      },
      {
        title: 'Lifespan & Temp',
        dataIndex: ['item_ean', 'parent', 'gdsn_item', 'detail', 'life_span_arrival'],
        listKey: 'item_ean_parent_lifespan',
        width: 100,
        renderFormItem: (item, { record, defaultRender }) => {
          const gdsnItem = record?.item_ean?.parent?.gdsn_item;
          const gdsnTemperature = gdsnItem?.detail?.temp?.length
            ? gdsnItem?.detail?.temp.find((x) => x.code == 'STORAGE_HANDLING')
            : null;

          return gdsnItem && gdsnItem.detail ? (
            <div className="text-xs">
              <div>
                {gdsnItem.detail.life_span_arrival ?? '-'} / {gdsnItem.detail.life_span_production ?? '-'}
              </div>
              {gdsnTemperature && (
                <div className={gdsnTemperature.min && +gdsnTemperature.min <= 10 ? 'c-red' : ''}>
                  {gdsnTemperature.min ?? '-'} {gdsnTemperature?.min_unit == 'CEL' ? '°' : gdsnTemperature?.min_unit} ~{' '}
                  {gdsnTemperature.max ?? '-'}
                  {gdsnTemperature?.max_unit == 'CEL' ? '°' : gdsnTemperature?.max_unit}
                </div>
              )}
            </div>
          ) : null;
        },
      },
      {
        title: 'Pcs / Package',
        dataIndex: ['item_ean', 'attr_case_qty'],
        listKey: 'case_qty',
        valueType: 'digit',
        sorter: false,
        align: 'center',
        width: 100,
        className: 'p-0',
        formItemProps: (form) => {
          const rules = [{ required: true, message: 'Qty Required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, config) => {
          const record = config.record;
          if (record?.item_ean?.id && record?.item_ean?.parent?.id) {
            return <ProFormItem ignoreFormItem>{Util.numberFormat(record.item_ean?.attr_case_qty)}</ProFormItem>;
          }
          return <SProFormDigit placeholder="Pcs / Package" formItemProps={{ style: { marginBottom: 0 } }} />;
        },
        render: (dom, record) => Util.numberFormat(record.item_ean?.attr_case_qty),
      },
      {
        title: 'Single EAN',
        dataIndex: ['item_ean', 'parent', 'ean'],
        listKey: 'parent_ean',
        valueType: 'text',
        sorter: false,
        width: 130,
        formItemProps: (form) => {
          const rules = [{ required: true, message: 'Single EAN Required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { record }) => {
          if (record?.item_ean?.parent?.id) {
            if (record?.item_ean?.id && sn(record.item_ean?.attr_case_qty) > 1)
              return <ProFormItem ignoreFormItem>{record.item_ean?.parent?.ean}</ProFormItem>;
          }

          if (sn(record?.item_ean?.attr_case_qty) == 1) {
            return undefined;
          }

          return <ProFormText placeholder="Single EAN" formItemProps={{ style: { marginBottom: 0 } }} />;
        },
      },
      {
        title: 'Location',
        dataIndex: ['warehouse_location', 'id'],
        listKey: 'warehouse_location_id',
        valueType: 'select',
        width: 150,
        sorter: false,
        formItemProps: (form) => {
          const rules = [{ required: true, message: 'Location Required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem(item, { record }) {
          return (
            <ProFormSelect
              options={warehouseLocations || []}
              style={{ marginBottom: 0 }}
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                showSearch: true,
                onInputKeyDown: (e: any) => {
                  if (Util.isTabPressed(e)) {
                    const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
                    if (dropdownWrapper?.children.length == 1) {
                      const id = e.target.getAttribute('aria-activedescendant');
                      const value = sn(document.getElementById(id)?.innerText);
                      // Note: not working! later will check.
                      // form.setFieldValue(['warehouse_location', 'id'], value);
                      // editableFormRef.current?.setRowData()

                      console.log(item, record, editableFormRef.current?.getRowData?.(sn(item.index)));

                      editableFormRef.current?.setRowData?.(sn(item.index), { warehouse_location: { id: value } });
                      console.log('Changed Row', editableFormRef.current?.getRowData?.(sn(item.index)));

                      setDataSource((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.uid == record?.uid);
                        if (current) {
                          (current as any).warehouse_location = { id: value };
                        }
                        console.log('Location change detected', value, newList);
                        return newList;
                      });
                    }
                  }
                },
              }}
            />
          );
        },
      },
      {
        title: 'Total Qty',
        dataIndex: ['qty'],
        valueType: 'digit',
        sorter: false,
        align: 'right',
        editable: false,
        width: 100,
        render: (dom, record) => {
          const qty = Util.safeInt(record.item_ean?.attr_case_qty) * Util.safeInt(record.box_qty);
          return Util.numberFormat(qty);
        },
      },
      {
        title: 'Option',
        valueType: 'option',
        width: 80,
        align: 'center',
        render: (_) => {
          return null;
        },
      },
    ],
    [hiddenColumns, debouncedHandleSearchEan, warehouseLocations],
  );

  const handleSaveRow = (rowParam: IboDraftDetailFormType) => {
    const row: IboDraftDetailFormType = { ...rowParam };
    const rowData: IboDraftDetailFormType = editableFormRef.current?.getRowData?.(0) || {};

    if (rowData?.warehouse_location) {
      row.warehouse_location = rowData?.warehouse_location;
    }
    if (sn(row.box_qty) <= 0) {
      message.error('Please fill Qty of box!');
      return;
    }
    if (!row.exp_date) {
      message.error('Please fill Exp. date!');
      return;
    }
    if (!row.warehouse_location?.id) {
      message.error('Please select warehouse location!');
      return;
    }

    formRef.current
      ?.validateFields()
      .then(async (values) => {
        const data = { ...row, ...values };

        // Check if expiration date is within 14 days
        const currentDate = moment();
        const expDate = moment(row.exp_date);
        const daysDiff = expDate.diff(currentDate, 'days');

        const proceedWithCreation = () => {
          setLoading(true);
          return createIboDraftDetail(data)
            .then(() => {
              setDataSource(defaultListData);
              setRefreshTick((prev) => prev + 1);
              document.getElementById('ean0')?.focus();
            })
            .catch((error) => {
              Util.error(error);
            })
            .finally(() => {
              setLoading(false);
            });
        };

        if (daysDiff < 14) {
          Modal.confirm({
            title: 'Expiration Date Warning',
            content: `Only ${daysDiff} days are left until the expiration date, continue?`,
            okText: 'Yes, Continue',
            cancelText: 'Cancel',
            onOk: proceedWithCreation,
            onCancel: () => {
              // Do nothing, just close the modal
            },
          });
        } else {
          proceedWithCreation();
        }

        return Promise.resolve();
      })
      .catch((err) => {
        console.log(err);
      });
  };

  return (
    <PageContainer className={styles.iboRegisterListPage}>
      <Card>
        <ProForm
          key="toolbar-form"
          formRef={formRef}
          layout="inline"
          submitter={false}
          onValuesChange={(values) => {
            setFormData(values);
          }}
        >
          {/* <ProFormSelect
            name={['ibom']}
            showSearch
            label="TEST List"
            required
            width={'sm'}
            options={warehouseLocations}
            onMetaChange={(meta) => {
              console.log('meta change: ', meta);
            }}
            fieldProps={{
              onInputKeyDown: (e: any) => {
                if (Util.isTabPressed(e)) {
                  const dropdownWrapper = document.getElementById(
                    e.target.getAttribute('aria-controls'),
                  );
                  if (dropdownWrapper?.children.length == 1) {
                    const id = e.target.getAttribute('aria-activedescendant');
                    formRef.current?.setFieldValue(
                      ['ibom'],
                      sn(document.getElementById(id)?.innerText),
                    );
                  }
                }
              },
            }}
            rules={[
              {
                required: true,
                message: 'List is required',
              },
            ]}
          /> */}
          <ProFormSelect
            name={['ibom', 'id']}
            showSearch
            label="IBOM"
            required
            width={'sm'}
            request={async (params) => {
              const res = await getIBOManagementACList(params);
              return res;
            }}
            rules={[
              {
                required: true,
                message: 'IBOM is required',
              },
            ]}
            fieldProps={{ dropdownMatchSelectWidth: false }}
          />
          <ProFormCheckbox
            name="only_right_13"
            label="Last 13 digits?"
            tooltip="Take only last 13 digits"
            initialValue={true}
          />
        </ProForm>

        <EditableProTable<IboDraftDetailFormType>
          editableFormRef={editableFormRef}
          actionRef={actionRef}
          rowKey="uid"
          sticky
          controlled
          debounceTime={200}
          cardProps={{
            style: { marginBottom: '2rem', marginTop: 16 },
            bodyStyle: { padding: 0 },
          }}
          scroll={{
            x: 600,
          }}
          style={{
            padding: 0,
          }}
          recordCreatorProps={false}
          loading={loading}
          columns={columns}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config) => {
              return [
                <Button
                  key="save"
                  type="primary"
                  size="small"
                  onClick={() => {
                    handleSaveRow(row);
                  }}
                >
                  Create
                </Button>,
              ];
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
            deleteText: <DeleteFilled />,
          }}
          rowClassName={(record) => {
            let cls = '';
            if (record.item_ean?.expecting_offer_items?.length) {
              cls += ' expecting-row';
            }

            return cls;
          }}
        />
      </Card>

      {formData.ibom?.id && <IBODraftDetailList ibomId={formData.ibom?.id} refreshTick={refreshTick} />}
    </PageContainer>
  );
};
export default IBORegister;
