import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, message, Row, Typography } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormList, ProFormTextArea } from '@ant-design/pro-form';
import Util, { ni } from '@/util';
import OfferItemShippedList from '../../OfferItemShippedList/components/OfferItemShippedList';
import { addOfferItemShippedBulk } from '@/services/foodstore-one/Offer/offer-item-shipped';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import DateSelector from '@/components/DateSelector';
import EanFilesComp from '@/components/EanFilesComp';
import { getEanDetail } from '@/services/foodstore-one/Item/ean';
import SkuComp from '@/components/SkuComp';

import styles from './CreateOrUpdateQtyShippedModalForm.less';

export type FormValueType = Partial<API.OfferItemShipped>;

export type CreateOrUpdateQtyShippedModalFormProps = {
  itemEan: Partial<API.Ean>;
  offerItem: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  loadOfferItem?: () => Promise<API.OfferItem | null>;
  onSubmit?: (formData: API.OfferItemShipped) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const CreateOrUpdateQtyShippedModalForm: React.FC<CreateOrUpdateQtyShippedModalFormProps> = ({
  itemEan,
  offerItem,
  modalVisible,
  handleModalVisible,
  loadOfferItem,
}) => {
  const formRef = useRef<ProFormInstance>();
  const [loadingEan, setLoadingEan] = useState<boolean>(false);

  const [listReloadTick, setListReloadTick] = useState<number>(0);
  // Editable list
  const listActionRef = useRef<FormListActionType>();

  useEffect(() => {
    setListReloadTick((prev) => prev + 1);
  }, [modalVisible]);

  const resetFormFields = () => {
    const listByRef = listActionRef.current?.getList();
    const newList = listByRef?.map((x) => ({ ...x, qty: null, exp_date: null, wa_date: null, wa_no: null }));
    formRef.current?.setFieldValue('qty_shipped_list', newList);
  };

  useEffect(() => {
    if (modalVisible && itemEan.id && offerItem.id) {
      setLoadingEan(true);
      getEanDetail({ id: itemEan.id, with: 'siblings' })
        .then((res) => {
          const qtyShippedList: any[] = [];
          if (res.siblings) {
            res.siblings.forEach((x) => {
              const rowObj = {
                ean_id: x.id,
                item_id: x.item_id,
                ean: x.ean,
                sku: x.sku,
                case_qty: x.attr_case_qty,
                offer_item_id: offerItem.id,
              };
              qtyShippedList.push(rowObj);
            });
          }

          formRef.current?.setFieldValue('qty_shipped_list', qtyShippedList);
        })
        .catch((err) => {
          Util.error(err);
          formRef.current?.setFieldValue('qty_shipped_list', []);
        })
        .finally(() => {
          setLoadingEan(false);
        });
    }
  }, [modalVisible, itemEan, offerItem.id]);

  return (
    <ModalForm<FormValueType>
      title={
        <Row gutter={16}>
          <Col>Add Shipped Qty -&nbsp;</Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.ean || '',
                tooltips: 'Copy EAN ' + (itemEan?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.ean || ''}
            </Typography.Paragraph>
          </Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.sku || '',
                tooltips: 'Copy SKU ' + (itemEan?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.sku || ''}
            </Typography.Paragraph>
          </Col>
          <Col>{`Qty / Case: ${itemEan.attr_case_qty}`}</Col>
        </Row>
      }
      width="1200px"
      size="large"
      visible={modalVisible}
      onVisibleChange={(visible) => {
        handleModalVisible(visible);
      }}
      grid
      formRef={formRef}
      className={styles.createOrUpdateQtyShippedModalForm}
      onFinish={async (value) => {
        return Promise.resolve(true);
      }}
      modalProps={{
        maskClosable: false,
        className: itemEan?.is_single ? 'm-single' : 'm-multi',
        confirmLoading: loadingEan,
      }}
      submitter={{ submitButtonProps: { style: { display: 'none' } } }}
    >
      <Col flex="180px">
        <EanFilesComp files={itemEan.files} width={160} outlineBorder />
        <div className="text-center" style={{ marginTop: 12 }}>
          <SkuComp sku={itemEan.sku} />
        </div>
      </Col>
      <Col flex="auto" style={{ paddingLeft: 24 }}>
        <ProFormList
          actionRef={listActionRef}
          key={'uid'}
          name="qty_shipped_list"
          creatorButtonProps={false}
          creatorRecord={{}}
          deleteIconProps={{ tooltipText: 'Remove' }}
          copyIconProps={false}
          alwaysShowItemLabel={false}
          actionRender={(field, action, doms) => []}
        >
          {(meta, index, action, count) => {
            // console.log('list render', meta, index, action, count);
            const rowData = action.getCurrentRowData();

            return (
              <Row key={meta.key} wrap={false}>
                <Col span={5}>
                  <ProForm.Item
                    name="ean"
                    label={
                      <Typography.Text ellipsis copyable>
                        {itemEan.ean_text_de?.name}
                      </Typography.Text>
                    }
                  >
                    {rowData.ean}
                  </ProForm.Item>
                </Col>
                <Col span={1}>
                  <ProForm.Item name="case_qty" label=" ">
                    {rowData.case_qty}
                  </ProForm.Item>
                </Col>

                <Col flex="100px">
                  <ProForm.Item name="qty" label="Shipped Qty">
                    <NumpadExtSelector inputProps={{ style: { width: 90 }, inputMode: 'none' }} isMobile />
                  </ProForm.Item>
                </Col>
                <Col flex="140px">
                  <ProForm.Item name="exp_date" label="Exp. Date">
                    <DateSelector
                      showBodyScroll
                      onChange={function (value: string): void {
                        //
                      }}
                      isMobile
                      eleOptions={{ width: 120 }}
                    />
                  </ProForm.Item>
                </Col>
                <Col flex="100px">
                  <ProForm.Item name="wa_no" label="WA No">
                    <NumpadExtSelector inputProps={{ style: { width: 90 }, inputMode: 'none' }} isMobile />
                  </ProForm.Item>
                </Col>
                <Col flex="140px">
                  <ProForm.Item name="wa_date" label="WA Date">
                    <DateSelector
                      showBodyScroll
                      onChange={function (value: string): void {
                        //
                      }}
                      isMobile
                      eleOptions={{ width: 120 }}
                    />
                  </ProForm.Item>
                </Col>
                <Col flex="auto">
                  <ProFormTextArea name="note" label="Comment" fieldProps={{ rows: 1 }} />
                </Col>
              </Row>
            );
          }}
        </ProFormList>
        <Row>
          <Col span={8} className="text-right" style={{ fontSize: 16 }}>
            <span>Offer Qty: </span>
            <span style={{ fontWeight: 'bold' }}>
              {offerItem.case_qty == 1 ? ni(offerItem.qty) : `${ni(offerItem.qty)} x ${ni(offerItem.case_qty)}`}
            </span>
          </Col>
          <Col span={7} offset={2} style={{ fontSize: 16 }} className="c-orange">
            <span>Shipped: </span>
            <span style={{ fontWeight: 'bold' }}>{ni(offerItem.shipped_qty_pcs, true)} pcs</span>
          </Col>
          <Col span={7}>
            <Button
              type="primary"
              size="large"
              className="btn-green"
              onClick={async () => {
                const listByRef = listActionRef.current?.getList();

                const rows: any = [];
                listByRef?.forEach((row) => {
                  if (row.qty && row.exp_date && row.wa_no && row.wa_date) {
                    rows.push(row);
                  }
                });

                if (rows.length < 1) {
                  message.info('Nothing to save! Please fill data correctly.');
                  return;
                }

                const hide = message.loading('Adding a Shipment...', 0);

                const success = await addOfferItemShippedBulk(rows).catch(Util.error).finally(hide);

                if (success) {
                  message.success('Shipped Qty added successfully.');
                  loadOfferItem?.();
                  setListReloadTick((prev) => prev + 1);

                  resetFormFields();
                }
              }}
            >
              Save
            </Button>
          </Col>
        </Row>
      </Col>
      <OfferItemShippedList offer_item_id={offerItem.id} item_id={offerItem.item_id} reloadTick={listReloadTick} />
    </ModalForm>
  );
};

export default CreateOrUpdateQtyShippedModalForm;
