import SPrices from '@/components/SPrices';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { getImportedPricesList } from '@/services/foodstore-one/Import/import';
import Util, { nf2, sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Col, Divider, Row, Spin } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef } from 'react';
import { useMemo } from 'react';
import _ from 'lodash';
import LatestIboList from './LatestIboList';

export type ImportedPrice = {
  uid?: string;
  sid?: number;
  import_id?: number;
  price?: number;
  ean?: string;
} & API.Import;

export type ImportedPricesProps = {
  itemEan?: Partial<API.Ean>;
  modalVisible?: boolean;
  handleModalVisible?: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  loading?: boolean;
  iboListProps?: {
    filters?: any;
  };
};

const ImportedPrices: React.FC<ImportedPricesProps> = (props) => {
  const { itemEan, loading } = props;
  const vat = itemEan?.item?.vat?.value;

  const actionRefM = useRef<ActionType>();
  const actionRefS = useRef<ActionType>();

  const columns: ProColumns<ImportedPrice>[] = useMemo(() => {
    return [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 30,
        align: 'center',
        // fixed: 'left',
        render: (item, record, index, action) => {
          return (
            ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
            index +
            1
          );
        },
      },
      {
        title: 'Import date',
        sorter: true,
        dataIndex: 'updated_on',
        valueType: 'dateTime',
        search: false,
        align: 'center',
        width: 80,
        render: (dom, record) => Util.dtToDMY(record.updated_on),
      },
      {
        title: 'Supplier',
        sorter: true,
        dataIndex: ['supplier', 'name'],
        valueType: 'text',
        search: false,
        ellipsis: true,
        width: 100,
      },
      {
        title: 'File',
        sorter: true,
        dataIndex: ['files', 0, 'clean_file_name'],
        valueType: 'text',
        search: false,
        ellipsis: true,
        width: 130,
        render: (dom, record) => {
          return (
            <a href={record.files?.[0]?.url || '#'} target="_blank" rel="noreferrer" className="c-blue">
              {record.files?.[0]?.clean_file_name}
            </a>
          );
        },
      },
      {
        title: 'Price',
        sorter: true,
        dataIndex: 'price',
        valueType: 'digit',
        search: false,
        ellipsis: true,
        align: 'right',
        fixed: 'right',
        width: 90,
        render: (dom, record) => nf2(record.price),
      },
      {
        title: 'Gross Price',
        sorter: true,
        dataIndex: 'price2',
        valueType: 'digit',
        search: false,
        ellipsis: true,
        align: 'right',
        fixed: 'right',
        width: 90,
        render: (dom, record) => (vat !== null ? nf2(sn(record.price) * (1 + sn(vat) / 100)) : null),
      },
    ];
  }, [vat]);

  const price = Util.safeNumber(_.get(_.find(itemEan?.ean_prices, { price_type_id: 1 }), 'price', 0).toFixed(2));

  const priceSingle = Util.safeNumber(
    _.get(_.find(itemEan?.parent?.ean_prices, { price_type_id: 1 }), 'price', 0).toFixed(2),
  );

  useEffect(() => {
    if (itemEan?.ean) {
      actionRefM.current?.reload();
      actionRefS.current?.reload();
    }
  }, [itemEan?.ean]);

  return (
    <>
      <Spin spinning={loading !== undefined ? loading : false}>
        <Row gutter={16}>
          <Col>
            <b>Standardpreis (FS_ONE)</b>
          </Col>
          <Col>
            <SPrices price={priceSingle} vat={vat} />
          </Col>
          {!itemEan?.is_single && (
            <>
              <Divider type="vertical" style={{ minHeight: 40 }} />
              <Col>
                <SPrices price={price} vat={vat} showZero />
              </Col>
            </>
          )}
          <Col offset={3}>
            <b>Latest BP</b>
          </Col>
          <Col>
            <SPrices price={itemEan?.latest_ibo?.price} vat={vat} />
          </Col>
          {!itemEan?.is_single && (
            <>
              <Divider type="vertical" style={{ minHeight: 40 }} />
              <Col>
                <SPrices price={sn(itemEan?.latest_ibo?.price) * sn(itemEan?.attr_case_qty)} vat={vat} />
              </Col>
            </>
          )}
        </Row>
      </Spin>
      <ProTable<ImportedPrice, API.PageParams>
        rowKey="uid"
        headerTitle={itemEan?.is_single ? 'Single EAN' : 'Multi EAN'}
        options={{ fullScreen: true }}
        size="small"
        bordered
        actionRef={actionRefM}
        pagination={{
          showSizeChanger: false,
          pageSize: 10,
        }}
        search={false}
        cardProps={{
          bodyStyle: { padding: 0 },
        }}
        request={(params, sort, filter) => {
          return getImportedPricesList({ ...params, ean: itemEan?.ean }, sort, filter) as any;
        }}
        columns={columns}
      />
      {!itemEan?.is_single && (
        <ProTable<ImportedPrice, API.PageParams>
          rowKey="uid"
          headerTitle="Single EAN"
          options={{ fullScreen: true }}
          size="small"
          bordered
          actionRef={actionRefS}
          pagination={{
            showSizeChanger: false,
            pageSize: 10,
          }}
          search={false}
          cardProps={{
            bodyStyle: { padding: 0 },
          }}
          request={(params, sort, filter) => {
            return getImportedPricesList({ ...params, ean: itemEan?.parent?.ean }, sort, filter) as any;
          }}
          columns={columns}
        />
      )}
      {itemEan?.item_id && <LatestIboList {...props.iboListProps} itemId={itemEan?.item_id} />}
    </>
  );
};

export default ImportedPrices;
