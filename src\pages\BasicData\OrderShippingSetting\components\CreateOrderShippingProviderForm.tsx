import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { message } from 'antd';
import Util from '@/util';
import { addOrderShippingProvider } from '@/services/foodstore-one/BasicData/order-shipping-provider';

const handleAdd = async (fields: API.ShippingProvider) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addOrderShippingProvider(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.ShippingProvider>;

export type CreateOrderShippingProviderFormProps = {
  values?: Partial<API.ShippingProvider>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.ShippingProvider) => Promise<boolean | void>;
};

const CreateOrderShippingProviderForm: React.FC<CreateOrderShippingProviderFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  return (
    <ModalForm
      title={'New shipping provider'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.ShippingProvider);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          handleModalVisible(false);
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
    </ModalForm>
  );
};

export default CreateOrderShippingProviderForm;
