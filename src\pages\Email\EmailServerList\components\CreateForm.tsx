import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormDigit, ProFormSwitch } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEmailServer } from '@/services/foodstore-one/Email/email-server';
import { Divider, message } from 'antd';
import Util from '@/util';

const handleAdd = async (fields: API.EmailServer) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEmailServer(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.EmailServer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailServer) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Email Server'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.EmailServer);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormSwitch width="md" name="is_oauth" label="Is OAuth?" />
      <Divider />
      <ProFormText width="md" name="domain" label="Domain" />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'IMAP Host is required',
          },
        ]}
        width="md"
        name="imap_host"
        label="IMAP Host"
      />
      <ProFormDigit width="md" name="imap_port" label="IMAP Port" />
      <ProFormDigit width="md" name="imap_port_ssl" label="IMAP Port (SSL)" />

      <Divider />
      <ProFormText width="md" name="smtp_host" label="SMTP Host" />
      <ProFormDigit width="xs" name="smtp_port" label="SMTP Port" />
      <ProFormText width="md" name="smtp_user" label="SMTP User" />
      <ProFormDependency name={['is_oauth']}>
        {(deps) => {
          if (deps.is_oauth) return null;
          else return <ProFormText.Password width="md" name="smtp_password" label="SMTP User Password" />;
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default CreateForm;
