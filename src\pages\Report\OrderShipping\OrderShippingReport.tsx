import { PageContainer } from '@ant-design/pro-layout';
import { Col, Row } from 'antd';
import OrderShippingReportByProvider from './components/OrderShippingReportByProvider';
import styles from './style.less';
import usePicklistAlert from '@/pages/Warehouse/PicklistDetail/hooks/usePicklistAlert';

const OrderShippingReport: React.FC = (props) => {
  const { alertsElements } = usePicklistAlert();

  return (
    <PageContainer
      className={styles.orderShippingReport}
      extra={
        alertsElements ? (
          <div style={{ position: 'absolute', top: 10, left: '50%', marginLeft: -150, zIndex: 10 }}>
            {alertsElements}
          </div>
        ) : undefined
      }
    >
      <Row>
        <Col span={24}>
          <OrderShippingReportByProvider />
        </Col>
      </Row>
    </PageContainer>
  );
};

export default OrderShippingReport;
