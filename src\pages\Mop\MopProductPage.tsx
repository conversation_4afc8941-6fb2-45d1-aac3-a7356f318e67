import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useCallback, useEffect, useRef, useState } from 'react';

import styles from './MopProductPage.less';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormRadio, ProFormSelect, ProFormSwitch, ProFormText } from '@ant-design/pro-form';
import Util, { nf2, sn } from '@/util';
import { DEFAULT_PER_PAGE_PAGINATION, MopProductStatus, MopProductStatusOptions } from '@/constants';
import {
  CheckCircleOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  EditTwoTone,
  FileTextOutlined,
  PictureOutlined,
  PlusOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Popconfirm, Space, message, Image, Row, Col } from 'antd';
import {
  deleteMopProduct,
  dsMopProductList,
  getMopProductList,
  usMopProductFull,
} from '@/services/foodstore-one/Mop/mop-product';
import { useLocation } from 'umi';
import WebsiteIcons from '../Item/EanList/components/WebsiteIcons';
import UpdateTextsForm from './components/UpdateTextsForm';
import useMopModalNavigation from './hooks/useMopModalNavigation';
import UpdateAttributeForm from './components/UpdateAttributeForm';
import CreateAttributeForm from './components/CreateAttributeForm';
import UpdatePicturesForm from './components/UpdatePicturesForm';
import useBatchProcess from '../Item/EanList/hooks/useBatchProcess';

export type SearchFormValueType = Partial<API.MopProduct>;

type MopProductPageProps = {
  route?: any;
};

const MopProductPage: React.FC<MopProductPageProps> = (compProps) => {
  const location: any = useLocation();

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);

  // datasource for inline editing
  const [datasource, setDatasource] = useState<API.MopProduct[]>([]);
  const [currentRow, setCurrentRow] = useState<API.MopProduct>();
  const [selectedRows, setSelectedRows] = useState<API.MopProduct[]>([]);

  // states for modal visible control
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  // const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);

  const [onlyWebsiteAndStatus, setOnlyWebsiteAndStatus] = useState(false);

  useEffect(() => {
    setOnlyWebsiteAndStatus(searchFormRef.current?.getFieldValue('onlyWebsiteAndStatus') ?? false);
  }, []);

  useEffect(() => {
    actionRef.current?.reload();
  }, [onlyWebsiteAndStatus]);

  // hook for modal navigation
  // ------------------------------------------------------------- //
  const { handleNavigation } = useMopModalNavigation(datasource, {
    attribute: handleUpdateModalVisible,
    picture: handleUpdatePicturesModalVisible,
    text: handleUpdateTextsModalVisible,
    modals: ['attribute', 'text', 'picture'],
    setCurrentRow,
  });

  // Batch processing UI hook.
  const { modalElement, run } = useBatchProcess();

  /**
   * UpSync this EAN fully.
   */
  const handleUpSync = useCallback(async (id: number) => {
    const hide = message.loading(`Up syncing ...`, 0);
    return usMopProductFull(id)
      .then((res) => {
        if (res.sku) {
          message.success('Successfully up synced on shop!');
        } else {
          message.error(res.upSyncMessage || 'Failed to up sync EAN!');
        }
      })
      .catch((e) => {
        message.error(e.message ?? 'Failed to upsync!');
      })
      .finally(() => {
        hide();
      });
  }, []);

  const columns: ProColumns<API.MopProduct>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      fixed: 'left',
      ellipsis: true,
      width: 100,
      sorter: true,
      showSorterTooltip: false,
      copyable: true,
    },
    {
      title: 'Sys / Mag. Status',
      dataIndex: 'status',
      hideInForm: false,
      filters: false,
      fixed: 'left',
      align: 'center',
      width: 50,
      showSorterTooltip: false,
      valueEnum: MopProductStatusOptions as any,
      render: (__, record) => {
        let ele = null;
        if (record.status == MopProductStatus.DRAFT) {
          ele = <EditOutlined style={{ color: 'gray' }} />;
        } else if (record.status == MopProductStatus.ACTIVE) {
          ele = <CheckCircleOutlined style={{ color: 'green' }} />;
        } else {
          ele = <CloseOutlined style={{ color: 'gray' }} />;
        }

        return (
          <Row style={{ textAlign: 'center' }}>
            <Col span={12}>{ele}</Col>
            <Col span={12}>
              {record.m_status == 1 ? (
                <CheckCircleOutlined style={{ color: 'green' }} />
              ) : (
                <CloseOutlined style={{ color: 'gray' }} />
              )}
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Sys Shops',
      dataIndex: 'website_ids',
      hideInForm: false,
      sorter: false,
      filters: false,
      width: 50,
      showSorterTooltip: false,
      className: 'bl2 b-gray align-top',
      fixed: 'left',
      render: (__, record) => (
        <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.m_website_ids} />
      ),
    },
    {
      title: 'Mag. Shops',
      dataIndex: 'm_website_ids',
      hideInForm: false,
      sorter: false,
      filters: false,
      width: 50,
      showSorterTooltip: false,
      className: 'align-top',
      fixed: 'left',
      render: (__, record) => (
        <WebsiteIcons product_websites={record.m_website_ids} website_ids={record.m_website_ids} />
      ),
    },
    {
      title: 'Image',
      dataIndex: ['files', 0, 'url'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      width: 80,
      render: (dom, record) => {
        // return dom;
        // return record.files ? <img src={record.files?.[0]?.url} /> : <></>;
        return record.files ? (
          <Image.PreviewGroup>
            {record.files &&
              record.files.map((file, ind) => (
                <Image
                  key={file.id}
                  src={file.thumb_url}
                  preview={{
                    src: file.url,
                  }}
                  wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                  width={40}
                />
              ))}
          </Image.PreviewGroup>
        ) : (
          <></>
        );
      },
    },
    {
      title: 'Name DE',
      dataIndex: ['product_text_de', 'name'],
      ellipsis: true,
      width: 300,
      sorter: true,
      copyable: true,
    },
    {
      title: 'Cust. Group',
      dataIndex: 'customer_group',
      width: 70,
      align: 'center',
    },
    {
      title: 'Price',
      dataIndex: ['product_price', 'price'],
      width: 50,
      align: 'right',
      render(__, entity) {
        return nf2(entity.product_price?.price);
      },
    },

    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 110,
      shouldCellUpdate(record, prevRecord) {
        return false;
      },
      render: (__, record, index) => {
        const options = [
          <a
            key="config"
            title="Update attributes"
            onClick={() => {
              handleUpdateModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <EditTwoTone />
          </a>,
          <a
            key="texts"
            title="Update texts"
            onClick={() => {
              handleUpdateTextsModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <FileTextOutlined />
          </a>,
          <a
            key="files"
            title="Update pictures"
            onClick={() => {
              handleUpdatePicturesModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <PictureOutlined />
          </a>,
          <Popconfirm
            key="upsync"
            placement="topRight"
            title={
              <>
                Are you sure you want to up sync the product <br />
                <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
              </>
            }
            overlayStyle={{ width: 350 }}
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              if (!record.id) return;
              handleUpSync(record.id);
            }}
          >
            <CloudUploadOutlined className="btn-gray" />
          </Popconfirm>,
        ];
        return <Space>{options.map((option) => option)}</Space>;
      },
    },
    {
      title: '',
      dataIndex: 'tmp_xxx',
      render: (__, record) => null,
    },
  ];

  return (
    <PageContainer className={styles.eanListContainer}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues(
            'sf_mop_product_all',
            {
              status: '',
            },
            { sku: location.query?.sku ?? undefined },
          )}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />

          <ProFormSelect
            name="status"
            label="Status"
            initialValue={MopProductStatus.DRAFT}
            options={[{ value: '', label: 'All' }, ...MopProductStatusOptions]}
            colProps={{ span: 'auto' }}
            width="xs"
          />

          <ProFormText name={'name'} label="Name" width={'sm'} placeholder={'Name'} />

          <ProFormSwitch
            name={'onlyWebsiteAndStatus'}
            label="Status & Website Diff Only?"
            initialValue={false}
            fieldProps={{
              onChange: (checked) => {
                setOnlyWebsiteAndStatus(checked);
              },
            }}
          />
        </ProForm>
      </Card>

      <ProTable<API.MopProduct, API.PageParams>
        headerTitle={'Stocklot Product List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        columnEmptyText=""
        dataSource={datasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_mop_product_all_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        // rowClassName={(record) => ('')}
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_mop_product_all', searchFormValues);
          Util.setSfValues('sf_mop_product_all_p', params);

          setLoading(true);
          return getMopProductList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
              with: 'productTextDe,productTexts,productPrice,files',
            },
            Object.keys(sort ?? {}).length < 1 ? { sku_idx: 'descend' } : sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);
              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.MopProduct) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.MopProduct) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        toolBarRender={(action) => [
          <Button
            key="new-mop"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            New
          </Button>,
          <Popconfirm
            key="down-sync-mop"
            icon={false}
            title={'Down sync'}
            okText="Down Sync"
            cancelText="Cancel"
            placement="left"
            onConfirm={() => {
              const hide = message.loading(`Down syncing offer products...`, 0);
              const formValues = searchFormRef.current?.getFieldsValue();
              dsMopProductList({ ...formValues })
                .then((res) => {
                  message.success('Successfully down synced! Reload a page.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" icon={<DownloadOutlined />}>
              Down Sync
            </Button>
          </Popconfirm>,
        ]}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
      />

      <CreateAttributeForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        reloadList={async (updatedRow: Partial<API.MopProduct>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleCreateModalVisible(false);
        }}
      />

      <UpdateAttributeForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.MopProduct>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        handleNavigation={handleNavigation}
        initialValues={currentRow || {}}
        onSubmit={async (values) => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
      />

      <UpdatePicturesForm
        modalVisible={updatePicturesModalVisible}
        handleModalVisible={handleUpdatePicturesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={{
          id: currentRow?.id,
          files: currentRow?.files || [],
          sku: currentRow?.sku,
          product_text_de: currentRow?.product_text_de,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePicturesModalVisible(false);
        }}
      />

      {modalElement}

      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;products.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            title={<>Are you sure you want to up sync selected products?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const fnLists = [];
              for (const x of selectedRows) {
                fnLists.push(async () => {
                  return usMopProductFull(Number(x.id));
                });
              }
              await run(fnLists);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync
            </Button>
          </Popconfirm>

          <Popconfirm
            title={<>Are you sure you want to delete selected products?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const hide = message.loading('Deleting...', 0);

              await deleteMopProduct({ id: selectedRows.map((x) => x.id).join(',') })
                .then((res) => {
                  message.success('Deleted successfully.');
                })
                .catch(Util.error)
                .finally(hide);

              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}
    </PageContainer>
  );
};

export default MopProductPage;
