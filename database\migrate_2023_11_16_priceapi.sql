CREATE TABLE priceapi_job
(
    job_id        VARCHAR(31) PRIMARY KEY,
    version       VARCHAR(10),
    country       VARCHAR(2),
    source        VARCHAR(255),
    topic         VARCHAR(255),
    key_name      VARCHAR(255),
    status        VARCHAR(31),
    requested     INT,
    open_values   INT,
    free_credits  INT,
    paid_credits  INT,
    refusals      INT,
    timeouts      INT,
    errors        INT,
    created_at    VARCHAR(31),
    terminated_at VARCHAR(31),
    downloaded_at VARCHAR(31),
    expires_at    VARCHAR(31)
);