ALTER TABLE `ean_file`
    ADD COLUMN `us_modes` VARCHAR(5) NULL COMMENT 'Up Sync mode in CSV. 1,2 available. 1: Own Shop, 2: ebay' AFTER `types`;


ALTER TABLE `ean_file`
    CHANGE `types` `types` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'csv format';

update ean_file
set ean_file.us_modes = '1,2'
where ean_file.us_modes is NULL;

-- FK constraints fix for update ean PK.
ALTER TABLE `ean_category`
    DROP FOREIGN KEY `FK_ean_category_ean_id`;
ALTER TABLE `ean_category`
    ADD CONSTRAINT `FK_ean_category_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `ean_file`
    DROP FOREIGN KEY `FK_ean_file_ean_id`;
ALTER TABLE `ean_file`
    ADD CONSTRAINT `FK_ean_file_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE `ean_file`
    DROP FOREIGN KEY `FK_ean_file_file_id`;
ALTER TABLE `ean_file`
    ADD CONSTRAINT `FK_ean_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `xmag_custom_attribute` DROP FOREIGN KEY `FK_mag_ca_ean_id`;
ALTER TABLE `xmag_custom_attribute` ADD CONSTRAINT `FK_mag_ca_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean`(`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `ibo_draft_detail` DROP FOREIGN KEY `fk_ibo_draft_detail_ean_id`;
ALTER TABLE `ibo_draft_detail` ADD CONSTRAINT `fk_ibo_draft_detail_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean`(`id`) ON UPDATE CASCADE;


ALTER TABLE `ibo` DROP FOREIGN KEY `fk_ibo_ean_id`;
ALTER TABLE `ibo` ADD CONSTRAINT `fk_ibo_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean`(`id`) ON UPDATE CASCADE;

