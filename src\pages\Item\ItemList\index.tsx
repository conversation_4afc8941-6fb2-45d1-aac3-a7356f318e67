import { DeleteOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Card, Popconfirm, List } from 'antd';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getItemList, deleteItem } from '@/services/foodstore-one/Item/item';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { getVatList } from '@/services/foodstore-one/BasicData/vat';
import UpdateCategoriesForm from './components/UpdateCategoriesForm';
import type { DataNode } from 'antd/lib/tree';
import { getCategoryList } from '@/services/foodstore-one/Item/category';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import type { DefaultOptionType } from 'antd/lib/select';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import UpdateCategoriesBulkForm from './components/UpdateCategoriesBulkForm';
import UpdateBulkForm from './components/UpdateBulkForm';

export type SearchFormValueType = Partial<API.Item>;

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Item[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteItem({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const ItemList: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const searchFormRef = useRef<ProFormInstance>();
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateBulkCategoriesModalVisible, handleUpdateBulkCategoriesModalVisible] = useState<boolean>(false);
  const [updateBulkTrademarksModalVisible, handleUpdateBulkTrademarksModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Item>();
  const [selectedRowsState, setSelectedRows] = useState<API.Item[]>([]);

  const [ibomList, setIbomList] = useState<DefaultOptionType[]>([]);

  // vats
  const [vats, setVat] = useState<API.Vat[]>([]);
  useEffect(() => {
    getVatList({}, {}, { value: '' }).then((res) => setVat(res.data));
    getIBOManagementACList({}, {}).then((res) => {
      setIbomList(res);
    });
  }, []);

  // Category trees
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  const columns: ProColumns<API.Item>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      fixed: 'left',
      width: 180,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'HS Code',
      dataIndex: 'hs_code',
      ellipsis: true,
      hideInSearch: true,
      width: 120,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      ellipsis: true,
      hideInSearch: true,
      width: 120,
    },
    {
      title: 'Total EANs',
      dataIndex: 'item_eans_count',
      tooltip: 'Number of EANs in an item.',
      ellipsis: true,
      hideInSearch: true,
      align: 'right',
      width: 100,
    },
    {
      title: 'Categories',
      dataIndex: ['categories'],
      tooltip: 'Please click to update categories.',
      width: 120,
      align: 'left',
      ellipsis: true,
      hideInSearch: true,
      render: (dom, record) => {
        // return record?.categories?.map((c) => <span key={c.id}>{c.name}, </span>);
        return (
          <a
            onClick={() => {
              setCurrentRow({ ...record });
              handleUpdateCategoriesModalVisible(true);
            }}
          >
            {record?.categories?.map((x) => x.name).join(', ') || <div>&nbsp;</div>}
          </a>
        );
      },
    },
    /* {
      title: 'Supplier',
      dataIndex: 'supplier_name',
      sorter: false,
      width: 120,
      render: (dom, entity) => {
        return (
          entity.suppliers &&
          entity.suppliers.map((supplier) => <div key={supplier.id}>{supplier.name}</div>)
        );
      },
    }, */
    {
      title: 'VAT',
      dataIndex: ['vat', 'value'],
      sorter: false,
      width: 60,
      ellipsis: true,
      align: 'right',
      hideInSearch: true,
      render: (dom, record) => (sn(record.vat?.value) >= 0 ? `${record.vat?.value}%` : ''),
    },
    {
      title: 'Trademark',
      dataIndex: ['trademark', 'name'],
      sorter: false,
      width: 130,
      ellipsis: true,
      hideInSearch: true,
      render: (dom, record) => (record.trademark?.name ? `${record.trademark?.name}` : ''),
    },
    {
      title: 'Tmp Trademark',
      dataIndex: ['tmp_trademark'],
      sorter: false,
      width: 130,
      ellipsis: true,
      hideInSearch: true,
      render: (dom, record) => (
        <div className="text-small c-gray">{record.tmp_trademark ? `${record.tmp_trademark}` : ''}</div>
      ),
    },
    {
      title: 'Producers',
      dataIndex: ['trademark', 'producers'],
      sorter: false,
      width: 260,
      ellipsis: false,
      hideInSearch: true,
      render: (dom, record) => (
        <List
          size="small"
          rowKey="id"
          className="padding-0"
          locale={{ emptyText: <></> }}
          dataSource={record.trademark?.producers}
          renderItem={(item) => (
            <List.Item className="text-small c-gray" style={{ padding: '2px 0' }}>
              {item.full_name}
            </List.Item>
          )}
        />
      ),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
      colSize: 1,
      align: 'center',
      search: false,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            if (record.suppliers && record.suppliers.length > 0) record.supplier_id = `${record.suppliers[0].id}`;
            else record.supplier_id = undefined;
            // record.supplier_id = { value: record.suppliers[0].id, label: record.suppliers[0].name };
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
        <a
          key="categories"
          onClick={() => {
            handleUpdateCategoriesModalVisible(true);
            setCurrentRow({
              ...record,
            });
          }}
        >
          Cat.
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormText
            name={'name'}
            label="Name"
            width={180}
            placeholder={'Item Name'}
            /* style={{ marginBottom: 12 }} */
          />
          <ProFormSelect
            name="producers[]"
            label="Producers"
            placeholder="Please select producers"
            mode="multiple"
            request={getProducerListSelectOptions}
            width={180}
          />
          <ProFormText name={'trademark'} label="Trademark" width={180} placeholder={'Trademark'} />
          <ProFormText
            name={'ean'}
            label="EAN"
            width={160}
            placeholder={'EAN'}
            /* style={{ marginBottom: 12 }} */
          />
          <ProFormText
            name={'sku'}
            label="SKU"
            width={'xs'}
            placeholder={'SKU'}
            /* style={{ marginBottom: 12 }} */
          />
          <ProFormSelect name={'ibom_id'} showSearch label="IBOM" options={ibomList} />
        </ProForm>
      </Card>
      <ProTable<API.Item, API.PageParams>
        headerTitle={'Items List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_item_grid_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          const sortStr = JSON.stringify(sort || {});
          const newSort = Util.safeJsonParse(sortStr);

          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_item_grid', searchFormValues);
          Util.setSfValues('sf_item_grid_p', params);

          setLoading(true);
          return getItemList(
            {
              ...params,
              ...searchFormValues,
              with: 'suppliers,vat,trademark,categories,producers',
            },
            { ...newSort /* , ['wl.name']: 'ascend', ['item_name']: 'ascend' */ },
            filter,
          ).finally(() => setLoading(false));
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              items &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleUpdateBulkTrademarksModalVisible(true);
            }}
          >
            Update Trademarks
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleUpdateBulkCategoriesModalVisible(true);
            }}
          >
            Update Categories
          </Button>
          <Popconfirm
            title={<>Are you sure you want to delete selected items?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <CreateForm
        vats={vats}
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateCategoriesForm
        modalVisible={updateCategoriesModalVisible}
        handleModalVisible={handleUpdateCategoriesModalVisible}
        initialValues={currentRow || {}}
        treeData={treeData}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateCategoriesModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateCategoriesBulkForm
        modalVisible={updateBulkCategoriesModalVisible}
        handleModalVisible={handleUpdateBulkCategoriesModalVisible}
        initialValues={{ ids: selectedRowsState.map((x) => Number(x.id)) }}
        treeData={treeData}
        onSubmit={async (value) => {
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleUpdateBulkCategoriesModalVisible(false);
        }}
      />

      <UpdateBulkForm
        modalVisible={updateBulkTrademarksModalVisible}
        handleModalVisible={handleUpdateBulkTrademarksModalVisible}
        initialValues={{ selectedRows: selectedRowsState }}
        onSubmit={async (value) => {
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleUpdateBulkTrademarksModalVisible(false);
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.Item>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.Item>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default ItemList;
