import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { createOrUpdateGdsnProvider } from '@/services/foodstore-one/Gdsn/gdsn';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await createOrUpdateGdsnProvider(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.GdsnProvider>;

export type UpdateGdsnProviderFormModalProps = {
  initialValues?: Partial<API.GdsnProvider>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.GdsnProvider) => Promise<boolean | void>;
};

const UpdateGdsnProviderFormModal: React.FC<UpdateGdsnProviderFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { trademarks } = useTrademarkFormFilter();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update GDSN Provider'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({
          ...value,
          name: value.name || '',
          provider_gln: props.initialValues?.provider_gln,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText width="lg" name="name" label="Name" />
      <ProFormSelect name={'trademark_ids'} label="Trademarks" options={trademarks} mode="multiple" />
      <ProFormTextArea width="lg" name="notes" label="Notes" />
    </ModalForm>
  );
};

export default UpdateGdsnProviderFormModal;
