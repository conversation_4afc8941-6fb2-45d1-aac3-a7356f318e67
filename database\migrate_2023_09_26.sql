UPDATE item
SET `special_filter` = REPLACE(`special_filter`, '"1"', '"Zuckerfrei"')
WHERE special_filter IS NOT NULL
  AND JSON_VALID(special_filter);

UPDATE item
SET `special_filter` = REPLACE(`special_filter`, '"2"', '"Vegan"')
WHERE special_filter IS NOT NULL
  AND JSON_VALID(special_filter);


UPDATE item
SET `special_filter` = REPLACE(`special_filter`, '"3"', '"Laktosefrei"')
WHERE special_filter IS NOT NULL
  AND JSON_VALID(special_filter);


ALTER TABLE `ean_text`
    ADD COLUMN `short_description` TEXT NULL AFTER `name_cat`;


ALTER TABLE `ean_text`
    ADD COLUMN `meta_title`       VARCHAR(255) NULL AFTER `description1`,
    ADD COLUMN `meta_keywords`    VARCHAR(255) NULL AFTER `meta_title`,
    ADD COLUMN `meta_description` VARCHAR(255) NULL AFTER `meta_keywords`;


UPDATE item
SET `special_filter` = '["Zuckerfrei"]'
WHERE special_filter IS NOT NULL and special_filter = '1';

UPDATE item
SET `special_filter` = '["Vegan"]'
WHERE special_filter IS NOT NULL and special_filter = '2';

UPDATE item
SET `special_filter` = '["Laktosefrei"]'
WHERE special_filter IS NOT NULL and special_filter = '3';