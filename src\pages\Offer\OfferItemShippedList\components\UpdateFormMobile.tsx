import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateOfferItemShipped } from '@/services/foodstore-one/Offer/offer-item-shipped';
import Util, { sn } from '@/util';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import DateSelector from '@/components/DateSelector';
import SkuComp from '@/components/SkuComp';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOfferItemShipped(sn(fields.id), fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.OfferItemShipped>;

export type UpdateFormMobileProps = {
  initialValues?: Partial<API.OfferItemShipped>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItemShipped) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateFormMobile: React.FC<UpdateFormMobileProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [initialValues]);

  return (
    <ModalForm
      title={
        <Space size={24}>
          <div>Update Shipment Detail</div>
          <div>
            <SkuComp sku={initialValues?.item_ean?.sku} />
          </div>
          <Typography.Paragraph
            copyable={{
              text: initialValues?.item_ean?.ean || '',
              tooltips: 'Copy EAN ' + (initialValues?.item_ean?.ean || ''),
            }}
          >
            {initialValues?.item_ean?.ean || ''}
          </Typography.Paragraph>
        </Space>
      }
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: initialValues?.id,
        };
        const success = await handleUpdate(data);

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProForm.Item name="case_qty" label="Case Qty">
        {initialValues?.case_qty}
      </ProForm.Item>

      <ProForm.Item name="qty" label="Shipped Qty">
        <NumpadExtSelector inputProps={{ style: { width: 90 }, inputMode: 'none' }} isMobile />
      </ProForm.Item>

      <ProForm.Item name="exp_date" label="Exp. Date">
        <DateSelector
          showBodyScroll
          onChange={function (value: string): void {
            //
          }}
          isMobile
          eleOptions={{ width: 120 }}
        />
      </ProForm.Item>

      <ProForm.Item name="wa_no" label="WA No">
        <NumpadExtSelector inputProps={{ style: { width: 90 }, inputMode: 'none' }} isMobile />
      </ProForm.Item>
      <ProForm.Item name="wa_date" label="WA Date">
        <DateSelector
          showBodyScroll
          onChange={function (value: string): void {
            //
          }}
          isMobile
          eleOptions={{ width: 120 }}
        />
      </ProForm.Item>

      <ProFormTextArea name="note" label="Comment" fieldProps={{ rows: 5 }} />
    </ModalForm>
  );
};

export default UpdateFormMobile;
