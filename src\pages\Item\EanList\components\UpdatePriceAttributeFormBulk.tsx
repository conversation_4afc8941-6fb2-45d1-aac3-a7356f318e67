import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useRef } from 'react';
import { Button, Col, message, Row, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormRadio, ProFormText } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanBatch } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _ from 'lodash';
import SProFormDigit from '@/components/SProFormDigit';

export type FormValueType = Partial<API.Ean>;

export type UpdatePriceAttributeFormBulkProps = {
  eanIds: number[];
  supplierXlsFileId?: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdatePriceAttributeFormBulk: React.FC<UpdatePriceAttributeFormBulkProps> = (props) => {
  const [loading, setLoading] = useState(false);

  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm<Partial<API.Ean>>
      title={<>{`Update ${props.eanIds.length} EANs' Price`}</>}
      width={600}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      formRef={formRef}
      onFinish={async (value) => {
        if (!formRef.current) return;
        try {
          setLoading(true);
          const postData = {
            mode: 'priceBatch',
            ids: props.eanIds,
            data: { ...value, supplierXlsFileId: props.supplierXlsFileId },
          };

          const hide = message.loading('Batch price updating...', 0);
          updateEanBatch(postData as any)
            .then((res) => {
              if (props.onSubmit) {
                props.onSubmit?.(value);
                if ((value as any).closeModal) props.handleModalVisible(false);
              }
            })
            .catch(Util.error)
            .finally(() => {
              setLoading(false);
              hide();
            });
        } catch (err) {
          Util.error(err);
          setLoading(false);
        }
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {/* <Alert
                type="warning"
                message="Successful save will be followed by up sync."
                style={{ marginRight: 30 }}
              /> */}
              <Button
                type="primary"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
                disabled={loading}
              >
                Save
              </Button>
              <Button
                type="default"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
                disabled={loading}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <Row gutter={16}>
          <Col>
            <ProFormRadio.Group
              name={['website_price_mode']}
              label="Website Price Mode"
              radioType="button"
              initialValue={'base_gfc'}
              fieldProps={{ buttonStyle: 'solid' }}
              options={[
                { value: 'base', label: 'FS_ONE(BP)' },
                { value: 'xls_price', label: 'FS_ONE(XLS)' },
                { value: 'xls_uvp', label: 'FS_ONE(UVP)' },
                { value: 'base_gfc', label: 'GFC' },
                { value: 'base_ex_rs', label: 'EX_RS' },
              ]}
            />
          </Col>
          <Col>
            <SProFormDigit
              colProps={{ span: 'auto' }}
              name="percentage"
              label="Percentage"
              width={120}
              addonAfter={'%'}
              min={-99999999}
              fieldProps={{
                precision: 4,
              }}
              initialValue={100}
              placeholder="Percentage"
              formItemProps={{
                tooltip: (
                  <div>
                    <div>FS_ONE {'=>'} Last BP from lates IBO * (1 + percentage)</div>
                    <div>GFC {'=>'} BP from selected XLS * (1 + percentage)</div>
                  </div>
                ),
              }}
            />
          </Col>
        </Row>
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdatePriceAttributeFormBulk;
