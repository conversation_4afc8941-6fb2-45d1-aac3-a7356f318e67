drop table if exists `stock_stable_booked`;

CREATE TABLE `stock_stable_booked`
(
    `id`              BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `item_id`         BIGINT(20) UNSIGNED DEFAULT NULL,
    `ean_id`          BIGINT(20) UNSIGNED DEFAULT NULL,
    `parent_ean_id`   BIGINT(20) UNSIGNED DEFAULT NULL,
    `wl_id`           BIGINT(20) UNSIGNED DEFAULT NULL,
    `piece_qty`       INT(11)             DEFAULT 0,
    `box_qty`         INT(11)             DEFAULT 0,
    `case_qty`        INT(11)             DEFAULT 0,
    `total_piece_qty` INT(11)             DEFAULT 0 COMMENT 'piece_qty + box_qty * case_qty',
    `exp_date`        DATE                DEFAULT NULL,
    `ibo_id`          BIGINT(20) UNSIGNED DEFAULT NULL COMMENT 'FK in Ibo table',
    `status`          INT(11)             DEFAULT 0 comment 'Status in Stock Stable table.',
    `order_id`        INT(11)             DEFAULT NULL comment 'Order ID',
    `order_item_id`   INT(11)             DEFAULT NULL comment 'Order Item ID',
    `sku`             varchar(50)         DEFAULT NULL comment 'SKU',
    `qty_ordered`     int(11)             DEFAULT NULL comment 'Ordered quantity',
    `stock_stable_id` BIGINT(20) UNSIGNED DEFAULT NULL comment 'Parent ID: PK in stock_stable table. Referenced ID',
    `picklist_id`     INT(11)             DEFAULT NULL comment 'Picklist ID',
    `created_on`      DATETIME            DEFAULT CURRENT_TIMESTAMP(),
    `created_by`      BIGINT(20) UNSIGNED DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_stock_stable_booked_mix` (`ean_id`, `wl_id`, `exp_date`, `ibo_id`),
    KEY `FK_stock_stable_booked_item_id` (`item_id`),
    KEY `FK_stock_stable_booked_ean_id` (`ean_id`),
    KEY `FK_stock_stable_booked_parent_ean_id` (`parent_ean_id`),
    KEY `FK_stock_stable_booked_wl_id` (`wl_id`),
    KEY `IDX_stock_stable_booked_exp_date` (`exp_date`),
    KEY `IDX_stock_stable_booked_order_item_id` (`order_item_id`),
    KEY `IDX_stock_stable_booked_sku` (`sku`),
    KEY `IDX_stock_stable_booked_order_id` (`order_id`),
    KEY `IDX_stock_stable_booked_stock_stable_id` (`stock_stable_id`),
    KEY `IDX_stock_stable_booked_picklist_id` (`picklist_id`),
    CONSTRAINT `FK_stock_stable_booked_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_booked_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_booked_parent_ean_id` FOREIGN KEY (`parent_ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_booked_wl_id` FOREIGN KEY (`wl_id`) REFERENCES `warehouse_location` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = INNODB
  DEFAULT CHARSET = utf8mb4;