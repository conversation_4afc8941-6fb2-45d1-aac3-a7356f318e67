<?php

declare(strict_types=1);

namespace App\Controller\Import\Import;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\ItemEan;
use App\Models\ItemEanGdsn;
use Illuminate\Database\Query\Builder;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetOne extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('ID required');
        }
        $importObj = $this->importService->getOne((int)$id);
        if (!$importObj) {
            \App\Exception\Base::raiseNotFound('Not found uploaded data.');
        }

        $params = $request->getParams();
        $filterModes = $params['filter_modes'] ?? [];
        $name_search = $params['name_search'] ?? '';

        if ($params['imported'] ?? null) {
            if (!($importObj->settings['input']['col_ean'] ?? null)) {
                \App\Exception\Base::raiseNotFound('EAN cole does not exist! Please re-import data correctly.');
            }

            $colDefines = $importObj->settings['input'] ?? [];

            $isEanColDefined = $colDefines['col_ean'] ?? false;
            $isTrademarkColDefined = $colDefines['col_trademark'] ?? false;
            $isHsCodeColDefined = $colDefines['col_hs_code'] ?? false;
            $isShelfLifeColDefined = $colDefines['col_shelf_life'] ?? false;
            $isNameColDefined = $colDefines['col_name'] ?? false;

            $isMakerColDefined = $colDefines['col_maker'] ?? false;
            $isCategoryColDefined = $colDefines['col_category'] ?? false;
            $isGlnColDefined = $colDefines['col_gln'] ?? false;

            $page = (int)$request->getQueryParam('page', 1);
            $perPage = (int)$request->getQueryParam('perPage', $this->importService::DEFAULT_PER_PAGE_PAGINATION);

            $db = $this->importService->getImportRepository()->getDb();
            $query = $db->table($importObj->table_name);
            $query->select("{$importObj->table_name}.*");
            //$query->leftJoin('item_ean as ie_own', 'ie_own.ean', '=', "{$importObj->table_name}.ean");
            $orRawWheres = [];
            if ($isEanColDefined) {
                $query->addSelect($db->raw("(SELECT id FROM item_ean WHERE ean = {$importObj->table_name}.ean LIMIT 1) AS ean_id"));
                $query->addSelect($db->raw("(SELECT `name` FROM item_ean, ean_text WHERE item_ean.id=ean_text.ean_id AND ean = {$importObj->table_name}.ean LIMIT 1) AS name_sys"));
                $query->addSelect($db->raw("(SELECT `name` FROM item_ean, item WHERE item_ean.item_id=item.id AND ean = {$importObj->table_name}.ean LIMIT 1) AS item_name_sys"));
                $query->addSelect($db->raw("(SELECT 1 FROM item_ean WHERE ean = {$importObj->table_name}.ean LIMIT 1) AS ean_exist"));
                if (($params['only_existing_ean'] ?? false) == 1) {
                    $query->whereRaw("EXISTS (SELECT 1 FROM item_ean WHERE ean = {$importObj->table_name}.ean LIMIT 1)");
                }

                if (($params['only_not_existing_ean'] ?? false) == 1) {
                    $orRawWheres[] = "NOT EXISTS (SELECT 1 FROM item_ean WHERE ean = {$importObj->table_name}.ean LIMIT 1)";
                }
            }
            if ($importObj->settings['input']['col_multi_ean'] ?? null) {
                $query->addSelect($db->raw("(SELECT 1 FROM item_ean WHERE ean = {$importObj->table_name}.multi_ean LIMIT 1) AS multi_ean_exist"));
                /*if (($params['only_existing_ean'] ?? false) === 'true') {
                    $query->whereRaw("(SELECT 1 FROM item_ean WHERE ean = {$importObj->table_name}.multi_ean)");
                }
                if (($params['only_not_existing_ean'] ?? false) === 'true') {
                    $rawWheres[] = "NOT EXISTS (SELECT 1 FROM item_ean WHERE ean = {$importObj->table_name}.multi_ean)";
                }*/
            }
            if ($orRawWheres) {
                $query->whereRaw("(" . implode(' OR ', $orRawWheres) . ")");
            }

            if ($name_search) {
                $query->where(function (Builder $builder) use ($params, $name_search, $importObj, $isNameColDefined, $db) {
                    $builder->whereRaw("(SELECT `name` FROM item_ean, ean_text WHERE item_ean.id=ean_text.ean_id AND ean = {$importObj->table_name}.ean LIMIT 1) LIKE ?", ["%$name_search%"])
                        ->orWhereRaw("(SELECT `name` FROM item_ean, item WHERE item_ean.item_id=item.id AND ean = {$importObj->table_name}.ean LIMIT 1) LIKE ?", ["%$name_search%"]);
                    if ($isNameColDefined)
                        $builder->orwhere("{$importObj->table_name}.name", 'LIKE', "%$name_search%");
                });
            }

            // applying filters
            if ($dbCols = $importObj->settings['dbCols'] ?? [] && $params) {
                foreach ($dbCols as $dbCol) {
                    if ($params[$dbCol] ?? null) {
                        if ($dbCol == 'maker') {
                            $query->where('maker', $params['maker']);
                        } else if ($dbCol == 'category') {
                            $query->where('category', $params['category']);
                        } else {
                            $query->where($dbCol, 'LIKE', "%{$params[$dbCol]}%");
                        }
                    }
                }
            }

            // applying filter modes
            if ($filterModes) {
                foreach ($filterModes as $filterMode) {
                    if ($filterMode == 'no_shelf_life') {
                        if ($isShelfLifeColDefined)
                            $query->whereNull($db->raw("(SELECT `shelf_life` FROM item_ean, item WHERE item_ean.item_id=item.id AND ean = {$importObj->table_name}.ean)"));
                    } else if ($filterMode == 'no_hs_code') {
                        if ($isHsCodeColDefined)
                            $query->whereNull($db->raw("(SELECT `hs_code` FROM item_ean, item WHERE item_ean.item_id=item.id AND ean = {$importObj->table_name}.ean LIMIT 1)"));
                    } else if ($filterMode == 'no_trademark') {
                        if ($isHsCodeColDefined)
                            $query->whereNull($db->raw("(SELECT `trademark_id` FROM item_ean, item WHERE item_ean.item_id=item.id AND ean = {$importObj->table_name}.ean)"));
                    } else if ($filterMode == 'diff_shelf_life') {
                        if ($isShelfLifeColDefined)
                            $query->whereRaw("(SELECT `shelf_life` FROM item_ean, item WHERE item_ean.item_id=item.id AND ean = {$importObj->table_name}.ean) != {$importObj->table_name}.shelf_life");
                    }
                }
            }

            if (Func::isDev())
                FuncModel::setGroupConcatLimit();

            // oi_piece_qty365,stock_stable_qty
            if (Func::keyExistsInWithParam($params, 'oi_piece_qty365')) {
                $query->selectSub(function (Builder $builder) use (&$importObj) {
                    $builder->selectRaw("SUM(oi.qty_ordered * e.attr_case_qty)")
                        ->from('item_ean', 'e')
                        ->join('xmag_order_item AS oi', 'oi.sku', '=', 'e.sku')
                        ->whereColumn('e.ean', "{$importObj->table_name}.ean")
                        ->where('oi.created_at', '>=', substr(Func::dtDateByOffset(time(), -365), 0, 10));
                }, 'oi_piece_qty365');
            }
            if (Func::keyExistsInWithParam($params, 'stock_stable_qty')) {
                $query->selectSub(function (Builder $builder) use (&$importObj) {
                    $builder->selectRaw("ss.mix_total_piece_qty")
                        ->from('item_ean', 'e')
                        ->join('v_stock_stable_aggregated AS ss', 'ss.ean_id', '=', 'e.id')
                        ->whereColumn('e.ean', "{$importObj->table_name}.ean");
                }, 'stock_stable_mix_total_piece_qty');

                $query->selectSub(function (Builder $builder) use (&$importObj) {
                    $builder->selectRaw("ss.box_qty")
                        ->from('item_ean', 'e')
                        ->join('v_stock_stable_aggregated AS ss', 'ss.ean_id', '=', 'e.id')
                        ->whereColumn('e.ean', "{$importObj->table_name}.ean");
                }, 'stock_stable_box_qty');
            }

            if (key_exists('gdsn_exists', $params)) {
                $gdsn_exists = intval($params['gdsn_exists']);
                if ($gdsn_exists == 1) {
                    $query->whereExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean_gdsn', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                    });
                } else if ($gdsn_exists == 2) {
                    $query->whereExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean_gdsn', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean')
                            ->whereRaw("JSON_LENGTH( JSON_EXTRACT(t.detail, '$.images') ) > 0 ")
                        ;
                    });
                } else {
                    $query->whereNotExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean_gdsn', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                    });
                }
            }
            $query->selectSub(function (Builder $builder) use (&$importObj) {
                $builder->from('item_ean_gdsn', 't')
                    ->selectRaw('1')
                    ->whereColumn("{$importObj->table_name}.ean", 't.ean')
                    ->limit(1)
                ;
            }, 'gdsn');

            $total = $query->count();
            $result = $this->importService->getImportRepository()->getResultsWithPagination(
                $query,
                $page,
                $perPage,
                $total
            );
            if ($result['data'] ?? []) {
                $eanIds = [];
                $eans = [];
                foreach ($result['data'] as $r) {
                    if ($r->ean_id ?? null) {
                        $eanIds[] = $r->ean_id;
                    }
                    if ($r->ean ?? null) {
                        $eans[] = $r->ean;
                    }
                }
                if ($eanIds) {
                    /** @var \Illuminate\Database\Eloquent\Builder $eBuilder */
                    $eBuilder = ItemEan::query();
                    $eBuilder->with('latestOwnIbo');
                    $eanList = $eBuilder->whereIn('id', $eanIds)
                        ->with(['Item' => function ($query) use ($params) {
                            $query->addSelect('item.*');
                            $query->with(['Vat' => function ($query) use ($params) {
                                $query->select('id', 'value');
                            }])->with('trademark:id,name');
                        }])->get()->keyBy('id');
                    foreach ($result['data'] as &$r) {
                        if ($r->ean_id ?? null)
                            $r->item_ean = $eanList[$r->ean_id] ?? null;
                    }
                }

                // GDSN data detail
                if ($eans) {
                    $gdsnItems = ItemEanGdsn::query()->whereIn('ean', $eans)
                        ->select(['id', 'ean', 'name', 'detail'])
                        ->get()->keyBy('ean')
                        ->toArray();
                    foreach ($result['data'] as &$r) {
                        $r->gdsn_item = $gdsnItems[$r->ean] ?? null;
                    }
                }
            }

            // Getting makers
            if ($isMakerColDefined) {
                $query = $db->table($importObj->table_name);
                $query
                    ->selectRaw('maker AS value')
                    ->selectRaw("COUNT(*) AS cnt")
                    ->selectRaw("CONCAT(maker, ' (', COUNT(*), ')') AS label")
                    ->groupBy("value");

                if (key_exists('gdsn_exists', $params)) {
                    $gdsn_exists = intval($params['gdsn_exists']);
                    if ($gdsn_exists == 1) {
                        $query->whereExists(function (Builder $builder) use (&$importObj) {
                            $builder->from('item_ean_gdsn', 't')
                                ->selectRaw('1')
                                ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                        });
                    } else if ($gdsn_exists == 2) {
                        $query->whereExists(function (Builder $builder) use (&$importObj) {
                            $builder->from('item_ean_gdsn', 't')
                                ->selectRaw('1')
                                ->whereColumn("{$importObj->table_name}.ean", 't.ean')
                                ->whereRaw("JSON_LENGTH( JSON_EXTRACT(t.detail, '$.images') ) > 0 ")
                            ;
                        });
                    } else {
                        $query->whereNotExists(function (Builder $builder) use (&$importObj) {
                            $builder->from('item_ean_gdsn', 't')
                                ->selectRaw('1')
                                ->whereColumn("{$importObj->table_name}.ean", 't.ean')
                            ;
                        });
                    }
                }

                if ($isCategoryColDefined && ($params['category'] ?? null)) {
                    $query->where('category', $params['category']);
                }

                if (($params['only_existing_ean'] ?? false) == 1) {
                    $query->whereExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                    });
                }

                if (($params['only_not_existing_ean'] ?? false) == 1) {
                    $query->whereNotExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                    });
                }

                $result['makers'] = $query->get()->toArray();
            } else {
                $result['makers'] = [];
            }

            // Getting categories
            if ($isCategoryColDefined) {
                $query = $db->table($importObj->table_name);
                $query
                    ->selectRaw('category AS value')
                    ->selectRaw("COUNT(*) AS cnt")
                    ->selectRaw("CONCAT(category, ' (', COUNT(*), ')') AS label")
                    ->groupBy("value");

                if (key_exists('gdsn_exists', $params)) {
                    $gdsn_exists = intval($params['gdsn_exists']);
                    if ($gdsn_exists == 1) {
                        $query->whereExists(function (Builder $builder) use (&$importObj) {
                            $builder->from('item_ean_gdsn', 't')
                                ->selectRaw('1')
                                ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                        });
                    } else if ($gdsn_exists == 2) {
                        $query->whereExists(function (Builder $builder) use (&$importObj) {
                            $builder->from('item_ean_gdsn', 't')
                                ->selectRaw('1')
                                ->whereColumn("{$importObj->table_name}.ean", 't.ean')
                                ->whereRaw("JSON_LENGTH( JSON_EXTRACT(t.detail, '$.images') ) > 0 ")
                            ;
                        });
                    } else {
                        $query->whereNotExists(function (Builder $builder) use (&$importObj) {
                            $builder->from('item_ean_gdsn', 't')
                                ->selectRaw('1')
                                ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                        });
                    }
                }

                if ($isMakerColDefined && ($params['maker'] ?? null)) {
                    $query->where('maker', $params['maker']);
                }

                if (($params['only_existing_ean'] ?? false) == 1) {
                    $query->whereExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                    });
                }

                if (($params['only_not_existing_ean'] ?? false) == 1) {
                    $query->whereNotExists(function (Builder $builder) use (&$importObj) {
                        $builder->from('item_ean', 't')
                            ->selectRaw('1')
                            ->whereColumn("{$importObj->table_name}.ean", 't.ean');
                    });
                }

                $result['categories'] = $query
                    ->get()
                    ->toArray();
            } else {
                $result['categories'] = [];
            }

            return $this->jsonResponse($response, 'success', $result, 200);
        } else {
            $importObj->excelData = $this->importService->readExcelFile($importObj, $params);
        }

        return $this->jsonResponse($response, 'success', $importObj, 200);
    }
}
