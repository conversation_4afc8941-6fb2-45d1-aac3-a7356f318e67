import { <PERSON><PERSON>ontainer } from '@ant-design/pro-layout';
import { IRouteComponentProps } from 'umi';

import { Button, Col, message, Row, Space, Typography } from 'antd';
import ProForm, { ProFormCheckbox, ProFormGroup, ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import { useRef, useState } from 'react';
import useSearchEanOrGdsn from '@/hooks/BasicData/useSearchEanOrGdsn';
import SNumpadExt from '@/components/NumpadExt';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import EanFilesComp from '@/components/EanFilesComp';
import SkuComp from '@/components/SkuComp';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import WarehouseLocationSelector from '@/components/WarehouseLocationSelector';
import DateSelector from '@/components/DateSelector';
import Util, { sn } from '@/util';
import { createIboDraftDetail } from '@/services/foodstore-one/IBO/ibo-draft-detail';
import IBODraftDetailListMobile from './IBODraftDetailListMobile';
import styles from './IBORegisterMobile.less';

type IboDraftDetailFormType = API.IboDraftDetail & {
  warehouse_location_name?: string;
};

const IBORegisterMobile: React.FC<IRouteComponentProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<API.IboDraftDetail>>({});
  const [refreshTick, setRefreshTick] = useState<number>(0);

  // Numpad for EAN field
  const numpadFieldRef = useRef<HTMLInputElement>();
  const {
    itemEan,
    setItemEan,
    // formElements: formElementsScanned,
    debouncedHandleSearchEan,
  } = useSearchEanOrGdsn({
    name: 'ean_scanned',
    style: { marginTop: 8 },
    placeholder: 'Scan EAN',
    with: 'siblingsMulti,files',
  });

  const clearForm = () => {
    const ele = (numpadFieldRef.current as any)?.input ?? numpadFieldRef.current;
    ele.value = '';
    setItemEan(null);
    formRef.current?.setFieldsValue({ box_qty: null, exp_date: null });
  };

  const handleSaveRow = () => {
    if (!itemEan) {
      message.error('Please search EAN first!');
      return;
    }

    const row: IboDraftDetailFormType = formRef.current?.getFieldsValue();

    console.log('Saving: ', row);
    // console.log('Saving: ', editableFormRef.current?.getRowsData?.());
    if (sn(row.box_qty) <= 0) {
      message.error('Please fill Qty of box!');
      return;
    }
    if (!row.exp_date) {
      message.error('Please fill Exp. date!');
      return;
    }
    if (!row.warehouse_location_name) {
      message.error('Please select warehouse location!');
      return;
    }

    formRef.current
      ?.validateFields()
      .then(async (values) => {
        const data = {
          ...row,
          ...values,
          item_ean: itemEan,
        };

        setLoading(true);
        return createIboDraftDetail(data)
          .then(() => {
            clearForm();
            setRefreshTick((prev) => prev + 1);
          })
          .catch(Util.error)
          .finally(() => {
            setLoading(false);
          });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  return (
    <PageContainer
      className={`mobile ${styles.iboRegisterMobile}`}
      title={
        <Space size={36}>
          <div>{props.route?.name}</div>
          <Typography.Link href={`/ibo/pre-ibo-register`} style={{ fontSize: 14 }}>
            Wholesale (Pre IBO)
          </Typography.Link>
        </Space>
      }
    >
      <ProForm<API.IboDraftDetail>
        key="toolbar-form"
        formRef={formRef}
        layout="horizontal"
        labelCol={{ flex: '80px' }}
        labelAlign="left"
        submitter={false}
        onValuesChange={(changedValues, values) => {
          setFormData(values);
        }}
      >
        <ProFormGroup style={{ width: '100%' }}>
          <ProFormSelect
            name={['ibom', 'id']}
            showSearch
            label="IBOM"
            required
            width={'lg'}
            request={async (params) => {
              const res = await getIBOManagementACList(params);
              return res;
            }}
            rules={[
              {
                required: true,
                message: 'IBOM is required',
              },
            ]}
            fieldProps={{ dropdownMatchSelectWidth: false, size: 'large' }}
          />
          <ProFormCheckbox
            name="only_right_13"
            label="Last 13 digits?"
            tooltip="Take only last 13 digits"
            initialValue={true}
            labelCol={{ flex: '160px' }}
          />
        </ProFormGroup>

        <Row gutter={48} wrap={false}>
          <Col flex="420px">
            <ProForm.Item name="eanFilled" label="EAN">
              <div>
                <input
                  ref={numpadFieldRef as any}
                  className="ant-input ant-input-lg"
                  disabled={false}
                  placeholder="EAN"
                  inputMode="none"
                />
                <div style={{ marginTop: 8 }}>
                  <SNumpadExt
                    fieldRef={numpadFieldRef}
                    onOk={(value) => {
                      // if (value?.length > )
                      if (value?.length > 1) {
                        debouncedHandleSearchEan(value, (t: any) => console.log('cb', t));
                      }
                    }}
                    isMobile
                  />
                </div>
              </div>
            </ProForm.Item>
          </Col>
          <Col flex="auto">
            <Row gutter={24}>
              <Col span={24}>
                <h1>{itemEan?.ean_texts?.find((x) => x.lang == 'DE')?.name ?? itemEan?.item?.name ?? '-'}</h1>
              </Col>
            </Row>
            <Row gutter={24} wrap={false}>
              <Col flex="180px">
                <div style={{ minHeight: 160, background: '#fff' }}>
                  <EanFilesComp files={itemEan?.files || []} width={160} />
                </div>
              </Col>
              <Col flex="0 1 auto" style={{ minWidth: 320 }}>
                <h2>{`Pcs / Pkg: ${itemEan?.attr_case_qty || ''}`}</h2>
                <h2>
                  {`SKU: `} <SkuComp sku={itemEan?.sku} />
                </h2>
              </Col>
            </Row>

            <div style={{ marginTop: 16 }}>
              <ProForm.Item name="box_qty" label="Box/Pcs Qty" labelCol={{ flex: '120px' }} style={{ marginTop: 16 }}>
                <NumpadExtSelector inputProps={{ style: { width: 140 }, inputMode: 'none' }} isMobile />
              </ProForm.Item>
            </div>

            <div style={{ marginTop: 16 }}>
              <ProForm.Item
                name="warehouse_location_name"
                label="Location"
                labelCol={{ flex: '120px' }}
                style={{ marginTop: 16 }}
              >
                <WarehouseLocationSelector
                  onChange={(value) => {
                    // console
                  }}
                  isMobile
                />
              </ProForm.Item>
            </div>

            <div style={{ marginTop: 16 }}>
              <ProForm.Item name="exp_date" label="Exp. Date" labelCol={{ flex: '120px' }}>
                <DateSelector
                  showBodyScroll
                  onChange={function (value: string): void {
                    //
                  }}
                  isMobile
                />
              </ProForm.Item>
            </div>

            <Button
              type="primary"
              size="large"
              onClick={() => handleSaveRow()}
              disabled={!itemEan || !formData.ibom?.id}
            >
              Create
            </Button>
          </Col>
        </Row>
      </ProForm>
      {formData.ibom?.id && <IBODraftDetailListMobile ibomId={formData.ibom?.id} refreshTick={refreshTick} />}
    </PageContainer>
  );
};

export default IBORegisterMobile;
