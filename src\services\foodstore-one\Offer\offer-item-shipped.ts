/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/lib/select';

const urlPrefix = '/api/offer-item-shipped';

/** get GET /api/offer-item-shipped */
export async function getOfferItemShippedList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * 
 * POST /api/offer-item-shipped 
 * */
export async function addOfferItemShipped(data: API.OfferItemShipped, options?: { [key: string]: any }) {
  return request<API.OfferItemShipped>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/offer-item-shipped/bulk */
export async function addOfferItemShippedBulk(rows: API.OfferItemShipped[], options?: { [key: string]: any }) {
  return request<API.OfferItemShipped>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: {
      rows,
    },
    ...(options || {}),
  });
}


/** put PUT /api/offer-item-shipped/{id} */
export async function updateOfferItemShipped(id: number, data: API.OfferItemShipped, options?: { [key: string]: any }) {
  return request<API.OfferItemShipped>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}



/** delete DELETE /api/offer-item-shipped */
export async function deleteOfferItemShipped(id: number | string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}


/**
 * get GET /api/offer-item-shipped
 *
 * get the autocomplete lists.
 *
 */
export async function getWaNoACList(params: { [key: string]: string }, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}/wa-no-ac-list`, {
    method: 'GET',
    params: {
      ...params,
      with: (params?.with ? `${params?.with},` : ''),
      perPage: params.pageSize || 1000,
      sort: !Object.keys(sort ?? {}).length ? { wa_no: 'descend' } : {},
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: { wa_no?: number }) => ({
      ...x,
      value: x.wa_no,
      label: x.wa_no,
    })),
  );
}


