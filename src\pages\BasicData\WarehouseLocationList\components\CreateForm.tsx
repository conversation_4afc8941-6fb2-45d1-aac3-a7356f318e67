import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { addWarehouseLocation } from '@/services/foodstore-one/warehouse-location';
import { message } from 'antd';
import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';
import { getWlPriority } from './UpdateForm';

const handleAdd = async (fields: API.WarehouseLocation) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addWarehouseLocation(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.WarehouseLocation) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  const handleNameChange = (firstStep?: boolean) => {
    const name = formRef.current?.getFieldValue('name');
    if (!name) return;
    formRef.current?.setFieldValue('priority', getWlPriority(name));
  };

  return (
    <ModalForm
      title={'New warehouse location'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.WarehouseLocation);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
        fieldProps={{
          onChange(e) {
            handleNameChange();
          },
        }}
      />
      <SProFormDigit width="md" name="priority" label="Priority" />
      {/* <ProFormText width="md" name="code" label="Code" /> */}
      <ProFormTextArea width="md" name="address" label="Address" />
    </ModalForm>
  );
};

export default CreateForm;
