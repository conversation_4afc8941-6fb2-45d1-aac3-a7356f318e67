import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormDigit } from '@ant-design/pro-form';
import { Button, Popconfirm, Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import Util, { ni, sn } from '@/util';
import { stockStableCorrection, StockStableCorrectionParamType } from '@/services/foodstore-one/Stock/stock-stable';
import { StockMovementReason } from '@/constants';
import { SizeType } from 'antd/lib/config-provider/SizeContext';
import { WarningFilled } from '@ant-design/icons';
import SNumpad from '@/components/Numpad';
import styles from './StockStableQtyUpdateFormModal.less';

export type StockStableQtyUpdateFormValueType = {
  new_qty?: number;
};

export type StockStableQtyUpdateFormModalProps = {
  initialValues?: API.StockStable;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  formSize?: SizeType;
  showNumpad?: boolean;
  isMobile?: boolean;
  onSubmit?: (formData: StockStableCorrectionParamType) => void;
};

const StockStableQtyUpdateFormModal: React.FC<StockStableQtyUpdateFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<StockStableQtyUpdateFormValueType>>();

  const { initialValues, modalVisible, handleModalVisible, formSize, onSubmit } = props;
  const isSingle = initialValues?.case_qty == 1;

  const [loading, setLoading] = useState(false);

  // Numpad
  const numpadFieldRef = useRef<HTMLInputElement>();

  useEffect(() => {
    if (modalVisible && initialValues) {
      // formRef.current?.setFieldValue('new_qty', isSingle ? initialValues.piece_qty : initialValues.box_qty);
      formRef.current?.setFieldValue('new_qty', null);
    }
  }, [initialValues, modalVisible]);

  const handleCorrection = (row: StockStableCorrectionParamType) => {
    const hide = message.loading('Updating...', 0);
    setLoading(true);
    stockStableCorrection({
      ...row,
      id: initialValues?.id,
      ibo_id: initialValues?.ibo_id,
      ean_id: initialValues?.ean_id,
      wl_id: initialValues?.wl_id,
      exp_date: initialValues?.exp_date,
      warehouse_location: initialValues?.warehouse_location,
    })
      .then((res) => {
        hide();
        if (res.message === false) {
          message.info('Nothing to update! Please adjust new Qty.');
        } else {
          message.success('Successfully updated');
          onSubmit?.(row);
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  return (
    <ModalForm<StockStableQtyUpdateFormValueType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>Update Stock Qty</span>
        </Space>
      }
      width={props.isMobile ? 500 : 375}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      size={formSize ?? 'middle'}
      labelAlign="left"
      formRef={formRef}
      colon={false}
      onFinish={async (values) => {
        if (!values.new_qty && values.new_qty != 0) {
          message.error('Please fill qty to be corrected!');
          return;
        }
        handleCorrection({
          piece_qty_edit: isSingle ? sn(values.new_qty) : undefined,
          box_qty_edit: isSingle ? undefined : sn(values.new_qty),
          reason: StockMovementReason.Correction,
        });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Save' },
        onReset(value) {
          handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading },
        submitButtonProps: { disabled: loading },

        render(props, dom) {
          return (
            <div style={{ display: 'flex', width: '100%' }}>
              <Popconfirm
                title={<div style={{ fontSize: 20 }}>Are you sure you want to remove stock?</div>}
                okText="Yes"
                cancelText="No"
                overlayStyle={{ maxWidth: 360 }}
                okButtonProps={{ size: 'large' }}
                cancelButtonProps={{ size: 'large' }}
                icon={<WarningFilled style={{ fontSize: 30 }} />}
                onConfirm={() => {
                  formRef.current?.setFieldValue('new_qty', 0);
                  formRef.current?.submit();
                }}
              >
                <Button type="primary" danger size="large" style={{ marginRight: 'auto' }} disabled={loading}>
                  Remove Qty
                </Button>
              </Popconfirm>

              {dom}
            </div>
          );
        },
      }}
      modalProps={{
        confirmLoading: loading,
        className: `${styles.modalWrap} ${props.isMobile ? styles.modalWrapMobile : ''}`,
      }}
    >
      <ProFormDigit
        name="new_qty"
        label="New Qty"
        placeholder="Qty"
        width="sm"
        fieldProps={{ autoFocus: true, ref: numpadFieldRef }}
        addonAfter={isSingle ? 'pcs' : 'boxes'}
        help={`Old qty: ${isSingle ? ni(initialValues.piece_qty) : ni(initialValues?.box_qty)}`}
      />
      {props.showNumpad ? (
        <SNumpad
          fieldRef={numpadFieldRef}
          onChange={(value) => {
            formRef.current?.setFieldValue('new_qty', value || '');
          }}
          isMobile={props.isMobile}
        />
      ) : null}
    </ModalForm>
  );
};

export default StockStableQtyUpdateFormModal;
