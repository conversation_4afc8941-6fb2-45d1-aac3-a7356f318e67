import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, Row, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import SProFormDigit from '@/components/SProFormDigit';
import { updateIboPre } from '@/services/foodstore-one/IBO/ibo-pre';
import { IboPreStatusOptions } from '..';

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateIboPre(id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.IboPre>;

export type UpdateFormProps = {
  initialValues?: Partial<API.IboPre>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPre) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;

  buyingHistoryComp?: JSX.Element;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm<FormValueType>
      title={'Update Pre IBO'}
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      modalProps={{ okText: 'Update' }}
      onFinish={async (value) => {
        const success = await handleUpdate(sn(props.initialValues?.id), { ...value });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <Row gutter={32}>
        <Col>
          <ProFormSelect name="status" label="Status" options={IboPreStatusOptions} />
        </Col>
        <Col>
          <SProFormDigit
            width={150}
            name="qty"
            label="Qty"
            required
            rules={[
              {
                required: true,
                message: 'Qty is required',
              },
            ]}
            fieldProps={{ precision: 0 }}
          />
        </Col>
        <Col>
          <SProFormDigit
            width={150}
            name="price_xls"
            label="Order price"
            addonAfter="€"
            required
            rules={[
              {
                required: true,
                message: 'Order price is required',
              },
            ]}
            fieldProps={{ precision: 3 }}
          />
        </Col>
      </Row>
      <Row gutter={32}>
        {/* <Col>
          <SProFormDigit width={150} name="qty_pkg" label="Qty / Pkg" />
        </Col> */}
        <Col>
          <SProFormDigit
            width={150}
            name="price_pallet"
            label="Pallet Price"
            fieldProps={{ precision: 3 }}
            addonAfter="€"
          />
        </Col>

        <Col style={{ opacity: 0.3 }}>
          <SProFormDigit width={150} name="qty_pallet" label="Pallet (pcs)" />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default UpdateForm;
