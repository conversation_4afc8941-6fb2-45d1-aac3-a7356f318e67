import Util, { ni, sn } from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Col, message, Modal, Popconfirm, Row } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import {
  addOfferItemIboPrePackReadyMapBulk,
  deleteOfferItemIboPrePackReadyMap,
  getOfferItemIboPrePackReadyMapList,
} from '@/services/foodstore-one/Offer/offer-item-packed-ready';
import { getIboPreList } from '@/services/foodstore-one/IBO/ibo-pre';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import SDatePicker from '@/components/SDatePicker';
import { getEanDetail } from '@/services/foodstore-one/Item/ean';

type RecordType = API.OfferItemIboPrePackReadyMap & { uid: React.Key };
export type SearchFormValueType = Partial<API.OfferItemIboPrePackReadyMap>;

type OfferItemPackedReadyListEditableModalProps = {
  offerItem?: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  parentReload?: () => void;
};

const OfferItemPackedReadyListEditableModal: React.FC<OfferItemPackedReadyListEditableModalProps> = (props) => {
  const { offerItem, modalVisible, handleModalVisible, parentReload } = props;

  const [loading, setLoading] = useState(false);
  const [loadingIboPreList, setLoadingIboPreList] = useState(false);
  const [loadingEan, setLoadingEan] = useState<boolean>(false);

  const allLoading = loading || loadingIboPreList || loadingEan;

  const editableFormRef = useRef<ProFormInstance<RecordType>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<RecordType[]>([]);
  const [itemEan, setItemEan] = useState<API.Ean>(); // OfferItemEan

  const [iboPreList, setIboPreList] = useState<API.IboPre[]>([]);

  const loadList = useCallback(() => {
    if (offerItem?.id) {
      const newParam = {
        offer_item_id: offerItem?.id,
        with: 'iboPre,itemEan',
        pageSize: 500,
      };

      setLoading(true);
      getOfferItemIboPrePackReadyMapList(newParam, {}, {})
        .then((res) => {
          setDataSource(res.data);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    } else {
      setDataSource([]);
    }
  }, [offerItem?.id]);

  // Get IBO PreList connected to this OfferItem.
  const loadIboPreList = useCallback(() => {
    if (offerItem?.id) {
      setLoadingIboPreList(true);
      getIboPreList({ pageSize: 100, with: 'iboPreManagement', offer_item_id: offerItem?.id })
        .then((res) => {
          setIboPreList(res.data);
        })
        .catch(Util.error)
        .finally(() => setLoadingIboPreList(false));
    } else {
      setIboPreList([]);
    }
  }, [offerItem?.id]);

  useEffect(() => {
    if (modalVisible) {
      loadIboPreList();
    }
  }, [modalVisible, loadIboPreList]);

  useEffect(() => {
    if (modalVisible) {
      loadList();
    }
  }, [modalVisible, loadList]);

  useEffect(() => {
    if (modalVisible && offerItem?.ean_id && offerItem?.id) {
      setLoadingEan(true);
      getEanDetail({ id: offerItem?.ean_id, with: 'siblings' })
        .then((res) => {
          setItemEan(res);
        })
        .catch((err) => {
          Util.error(err);
          setItemEan(undefined);
        })
        .finally(() => {
          setLoadingEan(false);
        });
    }
  }, [modalVisible, offerItem?.id, offerItem?.ean_id]);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'Offer No - Pre IBO',
      dataIndex: ['ibo_pre_id'],
      width: 180,
      valueType: 'select',
      // valueEnum: IboPreListOptionKv,
      ellipsis: true,
      fieldProps(form, config) {
        return {
          dropdownMatchSelectWidth: false,
          required: true,
        };
      },
      proFieldProps: {
        rules: [{ required: true, message: 'required' }],
      },
      render(dom, entity) {
        return (
          <span>
            {entity.offer_item?.offer?.offer_no}
            {entity.ibo_pre_id ? ` - Pre IBO #${entity.ibo_pre_id}` : ''}
          </span>
        );
      },
      renderFormItem(schema, config, form, action) {
        return (
          <ProFormSelect
            options={iboPreList.map((x2) => {
              const x = x2.ibo_pre_management ?? {};
              return {
                ...x2,
                value: x2.id,
                label: `${x.inbound_no ? x.inbound_no : '-'} | ${x.note_supplier ?? ' '} - ${Util.dtToDMY(
                  x.created_on,
                )}${x.status == 'open' ? '' : ` (${x.status})`}`,
              };
            })}
            formItemProps={{ style: { marginBottom: 0 } }}
            rules={[{ required: true, message: 'required' }]}
          />
        );
      },
    },
    {
      title: 'Case Qty',
      dataIndex: ['ean_id'],
      width: 90,
      valueType: 'select',
      ellipsis: true,
      render(dom, entity) {
        return <span>{entity.case_qty}</span>;
      },
      fieldProps(form, config) {
        return {
          dropdownMatchSelectWidth: false,
        };
      },
      renderFormItem(schema, config, form, action) {
        return (
          <ProFormSelect
            options={itemEan?.siblings?.map((x) => {
              return {
                ...x,
                value: x.id,
                label: x.attr_case_qty,
              };
            })}
            formItemProps={{ style: { marginBottom: 0 } }}
            rules={[{ required: true }]}
          />
        );
      },
    },
    {
      title: '',
      dataIndex: ['case_qty'],
      width: 65,
      align: 'right',
      className: 'bl2',
      hideInTable: true,
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 100,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },
    {
      title: 'Qty (pcs)',
      dataIndex: ['qty_pcs'],
      width: 65,
      align: 'right',
      editable: false,
      render: (__, record) => {
        return ni(sn(record?.case_qty) * sn(record?.qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
      renderFormItem(schema, config, form, action) {
        return <SDatePicker placeholder="EXP. Date" formItemProps={{ style: { marginBottom: 0 } }} />;
      },
    },
    {
      title: 'Created on',
      dataIndex: 'updated_on',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      editable: false,
      fieldProps(form, config) {
        return {
          format: 'DD.MM.YYYY',
        };
      },
      render: (__, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 140,
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.uid);
          }}
        >
          <EditOutlined />
        </a>,
        <Popconfirm
          key="delete"
          title="Are you sure you want to delete?"
          onConfirm={() => {
            if (`${record.uid}`.startsWith('uid_')) {
              setDataSource((prev) => prev.filter((x) => x.uid != record.uid));
            } else {
              deleteOfferItemIboPrePackReadyMap({
                offer_item_id: record.offer_item_id,
                ibo_pre_id: record.ibo_pre_id,
                ean_id: record.ean_id,
                exp_date: record.exp_date,
              })
                .then((res) => {
                  message.success('Deleted successfully.');
                  setDataSource((prev) => prev.filter((x) => x.uid != record.uid));
                  parentReload?.();
                })
                .catch(Util.error);
            }
          }}
        >
          <a key="delete">
            <DeleteOutlined color="danger" />
          </a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={1000}
      maskClosable={false}
      footer={false}
      /* footer={
        <>
          <Button
            type="primary"
            onClick={async () => {
              const rows: RecordType[] = dataSource?.map((x) => ({ ...x, offer_item_id: offerItem?.id }));
              for (const x of rows) {
                if (!x.case_qty || !x.offer_item_id || !x.ibo_pre_id || !x.ean_id || !x.qty) {
                  message.error('Please fill data correctly.');
                  return Promise.reject(false);
                }
              }

              const hide = message.loading('Saving Packed Qtys...', 0);

              const success = await addOfferItemIboPrePackReadyMapBulk(rows).catch(Util.error).finally(hide);

              if (success) {
                message.success('Packed Qty saved successfully.');
                setEditableRowKeys([]);
                parentReload?.();
              }
            }}
          >
            Save All
          </Button>
        </>
      } */
      title={
        <Row gutter={32}>
          <Col>Qty Packed Ready List for #{offerItem?.offer?.offer_no}</Col>
          <Col>Offer Qty: {ni(sn(offerItem?.case_qty) * sn(offerItem?.qty), true)} pcs</Col>
        </Row>
      }
    >
      <EditableProTable<RecordType, API.PageParams>
        loading={allLoading}
        headerTitle={null}
        editableFormRef={editableFormRef}
        columns={columns}
        dataSource={dataSource}
        value={dataSource}
        // rowKey={(entity) => `${entity.offer_item}_${entity.ibo_pre_id}_${entity.ean_id}_${entity.exp_date}`}
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: false, setting: false }}
        size="large"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          hideOnSinglePage: true,
          defaultPageSize: 400,
        }}
        toolBarRender={() => []}
        rowClassName={(record) => {
          let cls = record?.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
        recordCreatorProps={{
          newRecordType: 'dataSource',
          position: 'bottom',
          creatorButtonText: 'Add new row',
          record: (index: number, dataSource2: RecordType[]) => {
            return { uid: 'uid_' + Date.now().toString() + '-' + index, ean_id: itemEan?.siblings?.[0].id };
          },
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          onChange: setEditableRowKeys,
          deletePopconfirmMessage: 'Are you sure you want to delete?',
          onlyAddOneLineAlertMessage: 'You can only add one.',
          onValuesChange(record, recordList) {
            const newRecordList = [...recordList];
            if (record) {
              let newRecord: any = newRecordList.find((x) => x.uid == record.uid);
              if (record.ean_id) {
                const ean = itemEan?.siblings?.find((x) => x.id == record.ean_id);
                if (newRecord) {
                  newRecord.case_qty = ean?.attr_case_qty;
                }
              } else {
                newRecord.case_qty = 0;
              }
            }
            setDataSource(newRecordList);
          },
          onSave(key, record, originRow, newLineConfig) {
            const rows: RecordType[] = [
              {
                offer_item_id: offerItem?.id,
                ibo_pre_id: record.ibo_pre_id,
                case_qty: record.case_qty,
                qty: record.qty,
                ean_id: record.ean_id,
                exp_date: record.exp_date,
              } as RecordType,
            ];

            for (const x of rows) {
              console.log(x);
              if (!x.case_qty || !x.offer_item_id || !x.ibo_pre_id || !x.ean_id || !x.qty) {
                message.error('Please fill data correctly.');
                return Promise.reject(false);
              }
            }

            const hide = message.loading('Saving Packed Qtys...', 0);

            return addOfferItemIboPrePackReadyMapBulk(rows, { reset: true })
              .then((res) => {
                message.success('Saved successfully.');
                loadList();
                parentReload?.();
                return res;
              })
              .catch(Util.error)
              .finally(() => {
                hide();
              });
          },
          onDelete(key, record) {
            if (`${key}`.startsWith('uid_')) {
              return Promise.resolve(true);
            } else {
              return deleteOfferItemIboPrePackReadyMap({
                offer_item_id: record.offer_item_id,
                ibo_pre_id: record.ibo_pre_id,
                ean_id: record.ean_id,
                exp_date: record.exp_date,
              })
                .then((res) => {
                  message.success('Deleted successfully.');
                  setDataSource((prev) => prev.filter((x) => x.uid != key));
                  parentReload?.();
                })
                .catch(Util.error);
            }
          },
        }}
      />
    </Modal>
  );
};

export default OfferItemPackedReadyListEditableModal;
