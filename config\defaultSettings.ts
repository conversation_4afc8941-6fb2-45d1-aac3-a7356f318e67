import { Settings as LayoutSettings } from '@ant-design/pro-layout';

const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
} = {
  // navTheme: process.env.NODE_ENV === 'development' ? 'light' : 'dark',
  // navTheme: 'dark',
  primaryColor: '#1890ff',
  layout: 'top',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: 'FoodStore.one',
  pwa: false,
  // headerHeight: 48,  // default height!
  splitMenus: false,
  logo: '/images/favicon.png',
  iconfontUrl: '',
};

export default Settings;
