import type { ProFormFieldProps } from '@ant-design/pro-form';

import styles from './index.less';
import { ArrowLeftOutlined } from '@ant-design/icons';

export type SNumpadType = {
  fieldRef: React.MutableRefObject<HTMLInputElement | undefined>;
  eleOptions?: ProFormFieldProps & { showDecimal?: boolean };
  isMobile?: boolean;
  onChange?: (value: string) => void;
};

/**
 * Numpad
 */
const SNumpad: React.FC<SNumpadType> = ({ fieldRef, onChange, children, eleOptions, isMobile }) => {
  const onClickHandler = (num: number | string) => {
    const ele = (fieldRef.current as any)?.input ?? fieldRef.current;
    if (!ele) return;

    const index = ele?.selectionStart || 0;
    const originalString = ele.value || '';

    const part1 = originalString.slice(0, index);
    const part2 = originalString.slice(index);

    ele.value = `${part1}${num}${part2}`;
    ele.setSelectionRange(index + 1, index + 1);

    onChange?.(ele.value);
    ele.focus();
  };

  const onBackspaceHandler = () => {
    const ele = (fieldRef.current as any)?.input ?? fieldRef.current;
    if (!ele) return;

    const index = ele?.selectionStart || 0;
    const originalString = ele.value || '';

    const part1 = originalString.slice(0, index - 1);
    const part2 = originalString.slice(index);

    ele.value = `${part1}${part2}`;
    onChange?.(ele.value);

    ele.setSelectionRange(index - 1, index - 1);
    ele.focus();
  };

  return (
    <div className={`${styles.numpadWrap} ${isMobile ? styles.numpadWrapTablet : ''}`}>
      <table cellPadding={0} cellSpacing={0} style={{ border: '1px solid #222' }}>
        <tbody>
          <tr>
            <td className="num-key" onClick={() => onClickHandler(1)}>
              1
            </td>
            <td className="num-key" onClick={() => onClickHandler(2)}>
              2
            </td>
            <td className="num-key" onClick={() => onClickHandler(3)}>
              3
            </td>
          </tr>
          <tr>
            <td className="num-key" onClick={() => onClickHandler(4)}>
              4
            </td>
            <td className="num-key" onClick={() => onClickHandler(5)}>
              5
            </td>
            <td className="num-key" onClick={() => onClickHandler(6)}>
              6
            </td>
          </tr>
          <tr>
            <td className="num-key" onClick={() => onClickHandler(7)}>
              7
            </td>
            <td className="num-key" onClick={() => onClickHandler(8)}>
              8
            </td>
            <td className="num-key" onClick={() => onClickHandler(9)}>
              9
            </td>
          </tr>
          <tr>
            <td className="backspace" onClick={() => onBackspaceHandler()}>
              <ArrowLeftOutlined />
            </td>
            <td className="num-key" onClick={() => onClickHandler(0)}>
              0
            </td>
            <td onClick={() => (eleOptions?.showDecimal ? onClickHandler('.') : void 0)}>
              {eleOptions?.showDecimal && '.'}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default SNumpad;
