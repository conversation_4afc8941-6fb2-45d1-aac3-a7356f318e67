-- drop table if exists `xmag_order_ext`;
CREATE TABLE `xmag_order_ext`
(
    `entity_id`    int(11) NOT NULL COMMENT 'PK: Order ID',
    `ff`           numeric          DEFAULT NULL COMMENT 'ff param',
    `fp`           numeric          DEFAULT NULL COMMENT 'fp param',
    `utm_source`   varchar(255) DEFAULT NULL COMMENT 'utm_source param',
    `utm_medium`   varchar(255) DEFAULT NULL COMMENT 'utm_medium param',
    `utm_campaign` varchar(255) DEFAULT NULL COMMENT 'utm_campaign param',
    `utm_term`     varchar(255) DEFAULT NULL COMMENT 'utm_term param',
    `utm_content`  varchar(255) DEFAULT NULL COMMENT 'utm_content param',
    PRIMARY KEY (`entity_id`),
    CONSTRAINT `FK_xmag_order_ext_entity_id` FOREIGN KEY (`entity_id`) REFERENCES `xmag_order` (`entity_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


ALTER TABLE `warehouse_picklist_shipping_imported`
    CHANGE `picklist_id` `picklist_id` INT(11) NULL COMMENT 'Picklist ID';

