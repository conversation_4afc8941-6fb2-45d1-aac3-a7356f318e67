/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/dict';

/** rule GET /api/sys/dict */
export async function getDictList(
  params: API.PageParams & Partial<API.Dict>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Dict>> {
  return request<API.Result<API.Dict>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
/** post POST /api/sys/dict */
export async function addDict(data: API.Dict | FormData, options?: { [key: string]: any }): Promise<API.Dict> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.Dict>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}

/** post PUT /api/sys/dict */
export async function updateDict(data?: API.Dict | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Dict>>(`${urlPrefix}`, config).then((res) => res.message);
}

/** post PUT /api/sys/dict/update-sort */
export async function updateDictsSort(
  data: { sortInfo: any[]; workflow_id?: number },
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.BaseResult>(`${urlPrefix}/update-sort`, config);
}

/** delete DELETE /api/sys/dict/{id} */
export async function deleteDict(codes?: string[], options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}`, {
    method: 'DELETE',
    data: {
      codes,
    },
    ...(options || {}),
  });
}
