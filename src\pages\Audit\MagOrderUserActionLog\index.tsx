import React, { useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, Typography } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import Util from '@/util';
import moment from 'moment';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProForm } from '@ant-design/pro-form';
import { ReloadOutlined } from '@ant-design/icons';
import { getMagOrderUserActionLogList } from '@/services/foodstore-one/Magento/order-user-action-log';
import { OrderUserActionLogType } from '@/constants';
import { IRouteComponentProps } from 'umi';

const MagOrderUserActionLogList: React.FC<IRouteComponentProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.MagOrderUserActionLog>[] = [
    {
      dataIndex: 'type',
      width: 150,
      render: (dom: any, record: any) => dom,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      width: 200,
      ellipsis: true,
    },
    {
      title: 'Order ID',
      dataIndex: 'order_id',
      sorter: true,
      width: 100,
    },
    {
      title: 'Date',
      dataIndex: 'created_on',
      width: 110,
      sorter: true,
      defaultSortOrder: 'descend',
      render: (dom, record) =>
        record.created_on ? (
          <div title={moment(record.created_on).fromNow()}>{Util.dtToDMYHHMM(record.created_on)}</div>
        ) : undefined,
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
      width: 120,
    },
    {
      title: 'Detail',
      dataIndex: 'detail',
      ellipsis: true,
      copyable: true,
      render: (dom, record) =>
        record.detail ? (
          <Typography.Text copyable ellipsis>
            {JSON.stringify(record.detail)}
          </Typography.Text>
        ) : null,
    },
  ];

  const loadData = () => {
    actionRef.current?.reload();
  };

  const orderIdInParam = props.location.query.order_id;
  useEffect(() => {
    if (orderIdInParam) {
      formRef.current?.setFieldValue('order_id', orderIdInParam);
      loadData();
    }
  }, [orderIdInParam]);

  return (
    <PageContainer>
      <Card>
        <ProForm
          layout="inline"
          formRef={formRef}
          submitter={false}
          initialValues={Util.getSfValues('sf_order_user_action_log', {}, {})}
        >
          <ProFormSelect
            name="type"
            placeholder="Select type"
            label="Type"
            options={[
              { value: '', label: 'All' },
              { value: OrderUserActionLogType.OrderDetail, label: OrderUserActionLogType.OrderDetail },
              { value: OrderUserActionLogType.OrderDetailWH, label: OrderUserActionLogType.OrderDetailWH },
            ]}
            width="sm"
            fieldProps={{ onChange: (e) => formRef.current?.submit() }}
          />
          <ProFormText name="order_id" width={120} label="Order ID" />
          <ProFormText name="note" width={150} label="Note" />

          <Button htmlType="submit" type="primary" icon={<ReloadOutlined />} onClick={() => loadData()}>
            Search
          </Button>
        </ProForm>
      </Card>

      <ProTable<API.MagOrderUserActionLog, API.PageParams>
        rowKey="id"
        size="small"
        headerTitle="User Action Logs in Order Detail"
        revalidateOnFocus={false}
        search={false}
        params={{ with: 'user' }}
        actionRef={actionRef}
        request={async (params, sort, filter) => {
          const sfValues = formRef.current?.getFieldsValue();
          Util.setSfValues('sf_order_user_action_log', sfValues);
          return getMagOrderUserActionLogList({ ...params, ...sfValues }, sort, filter);
        }}
        columns={columns}
        pagination={{ showSizeChanger: true, defaultPageSize: 20 }}
        columnEmptyText=""
      />
    </PageContainer>
  );
};

export default MagOrderUserActionLogList;
