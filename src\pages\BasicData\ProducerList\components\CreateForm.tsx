import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addProducer } from '@/services/foodstore-one/BasicData/producer';
import { message } from 'antd';
import Util from '@/util';
import { getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';

const handleAdd = async (fields: API.Producer) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addProducer(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Producer) => Promise<boolean | void>;
  countries: any[];
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New producer'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Producer);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormText name="street" label="Street" width="md" />
      <ProFormText name="zip" label="Zip" width="md" />
      <ProFormText name="city" label="City" width="md" />
      <ProFormSelect
        name="country"
        label="Country"
        showSearch
        options={props.countries}
        formItemProps={{ initialValue: 'DE' }}
        width="md"
      />
      <ProFormSelect
        name="trademarks"
        label="Trademarks"
        placeholder="Please select trademarks"
        mode="multiple"
        request={getTrademarkListSelectOptions}
      />
    </ModalForm>
  );
};

export default CreateForm;
