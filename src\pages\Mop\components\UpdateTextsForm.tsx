import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, message, Modal, Row, Typography, Button, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormGroup, ProFormList, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import _ from 'lodash';
import type { UploadFile } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/lib/upload';
import ModalNavigation from './MopModalNavigation';
import type { HandleNavFuncType } from '../hooks/useMopModalNavigation';
import type { ValidateStatus } from 'antd/es/form/FormItem';
import useMopUpdateModalActions from '../hooks/useMopUpdateModalActions';
import { getMopProductList, updateMopProduct } from '@/services/foodstore-one/Mop/mop-product';

export type FormValueType = {
  upSync?: string;
  product_texts?: API.MopProductText[];
} & Partial<API.MopProduct> & { closeModal?: boolean };

const handleUpdate = async (fields: FormValueType) => {
  const data = new FormData();
  const hide = message.loading('Updating...', 0);

  data.append('id', `${fields.id}`);
  data.append('mode', 'product_texts');
  data.append('product_texts', JSON.stringify(fields.product_texts ?? []));

  try {
    const res = await updateMopProduct(sn(fields.id), data as API.MopProduct);
    hide();
    message.success('Updated successfully.');
    if (res.upSyncMessage) {
      message.error('Up sync error: ' + res.upSyncMessage);
    }
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!');
    return false;
  }
};

const defaultInitialValues: API.MopProduct = {
  product_texts: [
    {
      lang: 'DE',
    },
  ],
};

export type UpdateTextsFormProps = {
  initialValues?: Partial<API.MopProduct> & { openParent?: boolean };
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.MopProduct, isParent?: boolean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  handleNavigation?: HandleNavFuncType;
  isRefetchInitialValues?: boolean; // if true, we should reload initialValues from backend.
  gdsn?: boolean;
};

const UpdateTextsForm: React.FC<UpdateTextsFormProps> = (props) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);

  // "Internal Short Name": Count Letters as usual. Mark yellow Len31..40 and Red >40.
  const [shortNameStatus, setShortNameStatus] = useState<ValidateStatus>('');

  // refetch initial values
  const [refetchedInitialValues, setRefetchedInitialValues] = useState<API.MopProduct | undefined>(props.initialValues);
  const [loadingRefetch, setLoadingRefetch] = useState<boolean>(false);

  useEffect(() => {
    if (props.modalVisible && props.initialValues?.id) {
      setRefetchedInitialValues(props.initialValues);

      if (props.isRefetchInitialValues) {
        setLoadingRefetch(true);
        getMopProductList({ id: props.initialValues?.id })
          .then((res) => {
            setRefetchedInitialValues(res.data[0]);
          })
          .catch(Util.error)
          .finally(() => setLoadingRefetch(false));
      }
    }
  }, [props.isRefetchInitialValues, props.modalVisible, props.initialValues]);

  useEffect(() => {
    if (!props.modalVisible) return;
    if (formRef.current) {
      const newValues: API.MopProduct = { ...(refetchedInitialValues || defaultInitialValues) };
      if (!newValues.product_texts || newValues.product_texts.length < 1) {
        newValues.product_texts = defaultInitialValues.product_texts;
      }

      formRef.current.setFieldsValue(newValues);
    }
  }, [refetchedInitialValues, props.modalVisible]);

  // Image preview
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await Util.getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useMopUpdateModalActions(
    props.initialValues?.id ?? 0,
    props.initialValues?.sku ?? '',
    formRef.current,
  );

  //const isTextEditable = !!props.initialValues?.pro_texts?.[0]?.settings?.useOwnText;
  const isTextEditable = true;

  return (
    <>
      <ModalForm
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>Update Stocklot Product Texts -&nbsp;</span>
            <Typography.Paragraph
              copyable={{
                text: refetchedInitialValues?.sku || '',
                tooltips: 'Copy SKU ' + (refetchedInitialValues?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {refetchedInitialValues?.sku || ''}
            </Typography.Paragraph>
            {props.handleNavigation ? (
              <ModalNavigation
                modalName="text"
                eanId={refetchedInitialValues?.id}
                handleNavigation={props.handleNavigation}
                style={{ marginLeft: 50 }}
              />
            ) : null}
            {actionButtons}
          </div>
        }
        width={900}
        visible={props.modalVisible}
        onVisibleChange={props.handleModalVisible}
        grid
        readonly={props.isRefetchInitialValues && loadingRefetch}
        modalProps={{
          maskClosable: true,
          className: props.isRefetchInitialValues && loadingRefetch ? ' m-masked' : '',
        }}
        formRef={formRef}
        onFinish={async (values) => {
          if (formRef.current?.isFieldsTouched()) {
            const success = await handleUpdate({ ...values, id: props.initialValues?.id });
            if (success) {
              await runActionsCallback();

              if (values.closeModal) props.handleModalVisible(false);
              if (props.onSubmit) props.onSubmit(values);
            }
          } else {
            props.handleModalVisible(false);
          }
        }}
        submitter={{
          render: (p, dom) => {
            return (
              <Space>
                {actionButtons}
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    formRef.current?.setFieldValue('closeModal', 1);
                    p.submit();
                  }}
                >
                  Save & Close
                </Button>
                <Button
                  type="default"
                  size="small"
                  onClick={() => {
                    props.handleModalVisible(false);
                  }}
                >
                  Cancel
                </Button>
              </Space>
            );
          },
        }}
        disabled={loading}
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="closeModal" />
          {hiddenFormElements}
        </div>
        <ProFormGroup rowProps={{ gutter: 24 }}>
          <ProFormList
            key={'product_texts'}
            name="product_texts"
            creatorButtonProps={{
              position: 'bottom',
              creatorButtonText: 'Add a language',
            }}
            max={1}
            deleteIconProps={{ tooltipText: 'Remove' }}
            copyIconProps={{ tooltipText: 'Copy row' }}
          >
            <ProFormSelect
              name={'lang'}
              label="Language"
              rules={[
                {
                  required: true,
                  message: 'Language is required',
                },
              ]}
              initialValue={'DE'}
              options={[{ value: 'DE', label: 'German' } /* , { value: 'EN', label: 'English' } */]}
            />

            <Row gutter={8}>
              <Col span={14}>
                <ProFormText
                  name={'name'}
                  label={
                    <>
                      <span>Product Name &nbsp;</span>
                      <ProFormDependency key={'name_length'} name={['name']}>
                        {(depValues) => {
                          return (
                            <span className={depValues.name?.length > 65 ? 'red' : ''}>
                              {depValues.name ? '(' + depValues.name.length + ')' : ''}
                            </span>
                          );
                        }}
                      </ProFormDependency>
                    </>
                  }
                />
              </Col>
            </Row>

            <ProFormTextArea
              name={'short_description'}
              label="Short Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
              }}
              colProps={{ span: 24 }}
            />
            <ProFormTextArea
              name={'description'}
              label="Description"
              tooltip="Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                rows: 6,
              }}
              colProps={{ span: 24 }}
            />

            <ProFormText
              name={'meta_title'}
              label="Meta Title"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
              }}
              colProps={{ span: 24 }}
            />
            <ProFormTextArea
              name={'meta_keywords'}
              label="Meta Keywords"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
                maxLength: 255,
              }}
              colProps={{ span: 24 }}
            />
            <ProFormTextArea
              name={'meta_description'}
              label="Meta Description"
              disabled={!isTextEditable}
              fieldProps={{
                showCount: true,
              }}
              colProps={{ span: 24 }}
            />
          </ProFormList>
        </ProFormGroup>
        <Modal open={previewVisible} title={previewTitle} footer={null} onCancel={() => setPreviewVisible(false)}>
          <img alt="example" style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </ModalForm>
    </>
  );
};

export default UpdateTextsForm;
