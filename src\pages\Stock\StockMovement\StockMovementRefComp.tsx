import { Col, Row, Typography } from 'antd';

type StockMovementRefCompProps = {
  record?: Partial<API.StockMovement>;
};

const StockMovementRefComp: React.FC<StockMovementRefCompProps> = ({ record }) => {
  const type = record?.ref_type;
  const refId = record?.ref_id;

  if (!record || !type || !refId) return null;

  let orderId;
  if (type == 'OrderItem') {
    orderId = record.ref_order_id || '';
    return orderId ? (
      <Row gutter={4}>
        <Col>Order</Col>
        <Col>
          <Typography.Link
            href={`/orders/order-detail?entity_id=${orderId}`}
            target="_blank"
            rel="noreferrer"
            copyable={{ text: `${orderId}` }}
            title="Open Order Detail in new tab"
          >
            {orderId}
          </Typography.Link>
        </Col>
        <Col>
          <Typography.Text title="Order Item ID" style={{ paddingLeft: 6, color: 'grey' }} className="text-sm">
            {record.ref_id}
          </Typography.Text>
        </Col>
      </Row>
    ) : null;
  } else if (type == 'IBO') {
    return (
      <>
        <Typography.Link
          href={`/ibo/item-buying-order?ibom_id=${record.ref_ibom_id}`}
          target="_blank"
          rel="noreferrer"
          // copyable={{ text: `${record.ref_ibom_order_no}` }}
          title={`IBO ${record.ref_id}. Open IBO in new tab`}
        >
          #{record.ref_ibom_order_no}
        </Typography.Link>
      </>
    );
  } else if (type == 'StockStb') {
    return <span className="text-sm c-grey">{`Stock #${refId}`}</span>;
  }
  return null;
};

export default StockMovementRefComp;
