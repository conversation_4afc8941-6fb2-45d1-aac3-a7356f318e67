import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addDict } from '@/services/foodstore-one/Sys/sys-dict';
import { message } from 'antd';
import Util from '@/util';
import { DictType, DictTypeCreatableKv, DictTypeKv } from '@/constants';

const handleAdd = async (fields: API.Dict) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addDict(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.Dict>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Dict) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  return (
    <ModalForm
      title={'New dictionary'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Dict);
        if (success) {
          if (formRef.current) {
            formRef.current.setFieldsValue({
              code: formRef.current.getFieldValue('code'),
              value: null,
              label: null,
            });
          }
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'Type is required',
          },
        ]}
        showSearch
        width="md"
        name="type"
        label="Type"
        valueEnum={DictTypeCreatableKv}
      />
      <ProFormText
        width="xl"
        name="value"
        label="Value"
        required
        rules={[
          {
            required: true,
            message: 'Value is required',
          },
        ]}
      />
    </ModalForm>
  );
};

export default CreateForm;
