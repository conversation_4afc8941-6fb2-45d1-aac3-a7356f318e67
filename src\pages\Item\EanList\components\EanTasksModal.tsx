import { Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import _ from 'lodash';
import EanTaskList from '../../EanTaskList';

export type EanTasksModalsProps = {
  itemEan?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  reloadList?: () => void;
};

const EanTasksModals: React.FC<EanTasksModalsProps> = (props) => {
  const { itemEan, modalVisible, handleModalVisible, reloadList } = props;
  const [refreshTick, setRefreshTick] = useState<number>(0);

  useEffect(() => {
    if (modalVisible) {
      setRefreshTick((prev) => prev + 1);
    }
  }, [modalVisible, itemEan?.id]);

  return (
    <>
      <Modal
        open={modalVisible}
        width={800}
        onCancel={() => {
          handleModalVisible(false);
        }}
        footer={null}
        closable={false}
      >
        <EanTaskList ean={itemEan?.ean} reloadList={reloadList} refreshTick={refreshTick} />
      </Modal>
    </>
  );
};

export default EanTasksModals;
