import { getTrademarkGroupListSelectOptions } from '@/services/foodstore-one/BasicData/trademark-group';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion list of Trademark Group
 */
export default (formRef?: React.MutableRefObject<ProFormInstance | undefined>, eleOptions?: any) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [trademarkGroupOptions, setTrademarkGroupOptions] = useState<DefaultOptionType[]>([]);
  // selected trademark group
  const [trademarkGroup, setTrademarkGroup] = useState<DefaultOptionType>();

  const searchTrademarkGroupOptions = useCallback(async (params?: Record<string, any>, sort?: any) => {
    setLoading(true);
    return getTrademarkGroupListSelectOptions({ ...params }, sort)
      .then((res) => {
        setTrademarkGroupOptions(res);
        return res;
      })
      .catch((err) => {
        Util.error(err);
        return [];
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    searchTrademarkGroupOptions().then((res) => {
      const group_id = formRef?.current?.getFieldValue('group_id');
      if (group_id) {
        const found = res.find((x: any) => x.id == group_id);
        setTrademarkGroup(found);
      }
    });
  }, [formRef, searchTrademarkGroupOptions]);

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        name={eleOptions?.name ?? 'group_id'}
        label={'Group'}
        placeholder="Please select Trademark Group"
        mode="single"
        showSearch
        options={trademarkGroupOptions}
        required={eleOptions?.required}
        request={(params) => {
          return searchTrademarkGroupOptions(params);
        }}
        rules={
          eleOptions?.required
            ? [
                {
                  required: true,
                  message: 'Group is required',
                },
              ]
            : []
        }
        fieldProps={{
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          onChange: (value, option) => {
            setTrademarkGroup(option as any);
          },
        }}
        width={200}
        disabled={loading}
      />
    );
  }, [trademarkGroupOptions, loading, eleOptions?.required, searchTrademarkGroupOptions, eleOptions?.name]);

  return {
    trademarkGroupOptions,
    searchTrademarkGroupOptions,
    loading,
    trademarkGroup,
    setTrademarkGroup,
    formElements,
  };
};
