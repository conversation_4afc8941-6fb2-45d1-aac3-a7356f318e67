CREATE TABLE `sys_log`
(
    `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `category`       varchar(255)        DEFAULT NULL COMMENT 'Log category',
    `name`           varchar(255)        DEFAULT NULL COMMENT 'Log name',
    `note`           text                DEFAULT NULL COMMENT 'Log description',
    `status`         varchar(10)         DEFAULT NULL COMMENT 'Log status: started,processing,success, error',
    `request_uri`    varchar(255)        DEFAULT NULL COMMENT 'URI',
    `request_method` varchar(31)         DEFAULT NULL COMMENT 'request method',
    `created_by`     bigint(20) unsigned DEFAULT NULL COMMENT 'User ID',
    `created_on`     datetime            DEFAULT NULL COMMENT 'Created time',
    PRIMARY KEY (`id`),
    KEY `sys_log_name` (`name`),
    KEY `sys_log_status` (`status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;