import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/basic-data/trademark-group';

/** 
 * Get all trademark groups 
 * 
 * GET /api/basic-data/trademark-group */
export async function getTrademarkGroupList(params: API.PageParams, sort: any, filter: any) {
    return request<API.ResultList<API.TrademarkGroup>>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
    }));
}

/**
 * Get trademark group list for dropdown selecion.
 * 
 * @param params 
 * @param appendMode 
 * @returns 
 */
export async function getTrademarkGroupListSelectOptions(params: API.PageParams, appendMode?: number) {
    const res = await getTrademarkGroupList({ ...params, pageSize: 2000 }, { name: 'ascend' }, {});
    const defaultList: any[] = [];
    // default options
    /* if (appendMode == 1) {
      TRADEMARK_SPECIAL_FILTERS.forEach((element) => {
        defaultList.push(element);
      });
    } */

    // actual options
    if (res && res.data) {
        res.data.forEach((x: API.TrademarkGroup) => {
            defaultList.push({
                label: `${x.name}`,
                value: x.id,
            });
        });
    }
    return defaultList;
}

/** put PUT /api/basic-data/trademark-group */
export async function updateTrademarkGroup(id: number, data: API.TrademarkGroup, options?: { [key: string]: any }) {
    return request<API.TrademarkGroup>(`${urlPrefix}/${id}`, {
        method: 'PUT',
        data: data,
        ...(options || {}),
    });
}

/** post POST /api/basic-data/trademark-group */
export async function addTrademarkGroup(data: API.TrademarkGroup, options?: { [key: string]: any }) {
    return request<API.TrademarkGroup>(`${urlPrefix}`, {
        method: 'POST',
        data: data,
        ...(options || {}),
    });
}

/** delete DELETE /api/basic-data/trademark-group/{id} */
export async function deleteTrademarkGroup(id: number | string, options?: { [key: string]: any }) {
    return request<Record<string, any>>(`${urlPrefix}/${id}`, {
        method: 'DELETE',
        ...(options || {}),
    });
}

