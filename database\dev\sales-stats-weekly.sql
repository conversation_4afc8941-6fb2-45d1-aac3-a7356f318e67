SELECT LEFT(oi.created_at, 10)                                                                                                        AS d,
       YEARWEEK(xmag_order.created_at, 5)                                                                                             AS yw,
       WEEKDAY(xmag_order.created_at)                                                                                                 AS wd,
       CONCAT(YEARWEEK(oi.created_at, 5), '|', STR_TO_DATE(CONCAT(YEARWEEK(oi.created_at, 5), ' Monday'), '%x%v %W'), '|',
              STR_TO_DATE(CONCAT(YEARWEEK(oi.created_at, 5), ' Sunday'), '%x%v %W'))                                                  AS yw_detail
        ,
       COUNT(DISTINCT xmag_order.entity_id)                                                                                           AS qty,
       SUM(oi.qty_ordered)                                                                                                            AS qty_ordered,
       SUM(price * oi.qty_ordered)                                                                                                    AS turnover,
       SUM(price_incl_tax * oi.qty_ordered)                                                                                           AS g_turnover,
       SUM(IF(xmag_order.increment_id LIKE 'EB%',
              IF(xmag_order.increment_id LIKE 'EB%', (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) * 0.11, 0),
              0))                                                                                                                     AS ebay_fee_1,
       SUM(IF(xmag_order.increment_id LIKE 'EB%', IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0), 0))  AS ebay_fee_2,
       SUM(IF(xmag_order.increment_id LIKE 'EB%', IF(xmag_order.increment_id LIKE 'EB%', (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                             (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                             0.11, 0) +
                                                                                          IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                                     0) * oi.qty_ordered, 0))                                                         AS ebay_fee,
       SUM((price - IF(xmag_order.increment_id LIKE 'EB%',
                       (IF(xmag_order.increment_id LIKE 'EB%', (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) * 0.11,
                           0) + IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)), 0)) * oi.qty_ordered) AS cturnover,
       SUM(oi.qty_ordered * (price - IF(xmag_order.increment_id LIKE 'EB%', (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                0.11, 0) +
                                                                             IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                        0) - COALESCE(oi_idx.bp, ibo.price, (price - IF(xmag_order.increment_id LIKE 'EB%',
                                                                                        (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                            (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                            0.11, 0) +
                                                                                         IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                                                                        0)), 0) * IFNULL(e.attr_case_qty, 0)))        AS gp,
       SUM(COALESCE(oi_idx.bp, ibo.price, (price - IF(xmag_order.increment_id LIKE 'EB%', (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                              (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                              0.11, 0) +
                                                                                           IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                                      0)), 0) * oi.qty_ordered * e.attr_case_qty)                                     AS landed,
       SUM(COALESCE(oi_idx.bp, ibo.price, (price - IF(xmag_order.increment_id LIKE 'EB%', (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                              (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                              0.11, 0) +
                                                                                           IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                                      0)), 0) * oi.qty_ordered * e.attr_case_qty) / SUM(oi.qty_ordered)               AS landed_pcs,
       IFNULL(SUM(COALESCE(oi_idx.bp, ibo.price, (price - IF(xmag_order.increment_id LIKE 'EB%', (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                                     (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                                     0.11, 0) +
                                                                                                  IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                                             0)), 0) * oi.qty_ordered * e.attr_case_qty), 0) + IFNULL(
               SUM(IF(xmag_order.increment_id LIKE 'EB%', IF(xmag_order.increment_id LIKE 'EB%', (IF(xmag_order.increment_id LIKE 'EB%',
                                                                                                     (price_incl_tax + (price / xmag_order.subtotal) * xmag_order.shipping_incl_tax) *
                                                                                                     0.11, 0) +
                                                                                                  IF(xmag_order.increment_id LIKE 'EB%', price * 0.35 / xmag_order.subtotal, 0)),
                                                             0) * oi.qty_ordered, 0)), 0)                                             AS bp
FROM `xmag_order`
         INNER JOIN `xmag_order_item` AS `oi`
                    ON `oi`.`order_id` = `xmag_order`.`entity_id`
         LEFT JOIN `xmag_order_item_idx` AS `oi_idx` ON `oi_idx`.`order_item_id` = `oi`.`item_id`
         LEFT JOIN `item_ean` AS `e` ON `e`.`sku` = `oi`.`sku`
         LEFT JOIN `item` AS `i` ON `i`.`id` = `e`.`item_id`
         LEFT JOIN `trademark` AS `t` ON `t`.`id` = `i`.`trademark_id`
         LEFT JOIN (

            WITH ibo AS (SELECT ibo.*, ROW_NUMBER() OVER (PARTITION BY ean_id ORDER BY id DESC) AS rn
                          FROM ibo)
             SELECT ibo.*
             FROM ibo
             WHERE rn = 1)
                  AS `ibo` ON `ibo`.`ean_id` = `e`.`id`
WHERE `xmag_order`.`status` IN ('complete', 'processing', 'closed')
  AND `xmag_order`.`created_at` >= '2023-01-23'
  AND `xmag_order`.`created_at` <= '2023-01-30 23:59:59'
GROUP BY `d`
ORDER BY `yw` DESC