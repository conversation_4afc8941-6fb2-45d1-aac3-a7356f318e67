import {
  CheckCircleOutlined,
  CloseOutlined,
  DownloadOutlined,
  UploadOutlined,
  WarningOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { But<PERSON>, Card, Col, message, Popconfirm, Row, Space, Typography } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FooterToolbar, PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { dsMagentoInventoryStock, getStockCompareList } from '@/services/foodstore-one/Stock/stock-compare';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import Util, { nf2, ni, skuToItemId, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { usUpdateStockQty } from '@/services/foodstore-one/api';
import { useModel } from 'umi';
// import IboDetailModal from '@/pages/Report/Order/components/IboDetailModal';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import { dsProductBasePrices, updateEanAttributePartial } from '@/services/foodstore-one/Item/ean';
import SProFormDigit from '@/components/SProFormDigit';
import useBatchProcess from '@/pages/Item/EanList/hooks/useBatchProcess';
import WebsiteIcons from '@/pages/Item/EanList/components/WebsiteIcons';
import type { TrademarkChangeCallbackHandlerTypeParamType } from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import { usProductPrice } from '@/services/foodstore-one/Item/ean';

export type SearchFormValueType = Partial<API.StockCompare> & {
  onlyWebsiteAndStatus?: boolean;
  onlyActiveShops?: boolean;
  onlyDiffPrice1?: boolean;
  onlyDiffPrice2?: boolean;
};

const StockCompareList: React.FC = () => {
  const { appSettings, setAppSettings } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.StockCompare>();
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  // const [openIBOModal, setOpenIBOModal] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<API.StockCompare[]>([]);

  const [onlyWebsiteAndStatus, setOnlyWebsiteAndStatus] = useState(false);
  const [onlyActiveShops, setOnlyActiveShops] = useState(false);
  const [onlyDiffPrice1, setOnlyDiffPrice1] = useState(false);
  const [onlyDiffPrice2, setOnlyDiffPrice2] = useState(false);

  // Batch processing UI hook.
  const { modalElement, run } = useBatchProcess();

  const vstockRef = useRef<number>(0);

  const columns: ProColumns<API.StockCompare>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Item Name',
      dataIndex: ['item_name'],
      sorter: true,
      ellipsis: true,
      hideInSearch: false,
      width: 180,
    },
    {
      title: 'Name',
      dataIndex: ['name'],
      sorter: true,
      ellipsis: true,
      hideInSearch: false,
      width: 180,
    },
    {
      title: 'EAN',
      dataIndex: 'ean',
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
    },
    {
      title: 'SKU',
      dataIndex: ['sku'],
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      width: 100,
      render(__, entity) {
        return (
          <Typography.Link
            href={`/item/ean-all-summary?sku=${skuToItemId(entity.sku)}_`}
            title="Open EAN page in new tab"
            target="_blank"
            copyable
          >
            {entity.sku}
          </Typography.Link>
        );
      },
    },
    {
      title: 'FsOne Price',
      dataIndex: ['prices'],
      sorter: false,
      width: 120,
      tooltip: 'Gross Price',
      align: 'center',
      children: [
        {
          title: 'Sys',
          dataIndex: ['sys_price1'],
          sorter: false,
          width: 60,
          align: 'right',
          render: (__, record) => nf2(record.sys_price1),
        },
        {
          title: 'Mag.',
          dataIndex: ['m_price1'],
          sorter: false,
          width: 60,
          align: 'right',
          render: (__, record) => nf2(record.m_price1),
        },
      ],
    },
    {
      title: 'GFC Price',
      dataIndex: ['prices2'],
      sorter: false,
      width: 120,
      tooltip: searchFormRef.current?.getFieldValue('diff') == 'price_diff_gfc' ? 'Net Price' : 'Gross Price',
      align: 'center',
      children: [
        {
          title: 'Sys',
          dataIndex: ['sys_price2'],
          sorter: false,
          width: 60,
          align: 'right',
          render: (__, record) =>
            searchFormRef.current?.getFieldValue('diff') !== 'price_diff_gfc'
              ? nf2(record.sys_price2)
              : nf2(sn(record.sys_price2) / (1 + sn(record.vat_value) / 100)),
        },
        {
          title: 'Mag.',
          dataIndex: ['m_price2'],
          sorter: false,
          width: 60,
          align: 'right',
          render: (__, record) =>
            searchFormRef.current?.getFieldValue('diff') !== 'price_diff_gfc'
              ? nf2(record.m_price2)
              : nf2(sn(record.m_price2) / (1 + sn(record.vat_value) / 100)),
        },
      ],
    },
    {
      title: 'RS Price',
      dataIndex: ['prices3'],
      sorter: false,
      width: 120,
      tooltip: 'Gross Price',
      align: 'center',
      children: [
        {
          title: 'Sys',
          dataIndex: ['sys_price3'],
          sorter: false,
          width: 60,
          align: 'right',
          render: (__, record) => nf2(record.sys_price3),
        },
        {
          title: 'Mag.',
          dataIndex: ['m_price3'],
          sorter: false,
          width: 60,
          align: 'right',
          render: (__, record) => nf2(record.m_price3),
        },
      ],
    },

    {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      render: (dom, record) => ni(record.box_qty),
    },
    {
      title: 'Total Pcs Qty',
      dataIndex: ['total_piece_qty_per_item'],
      sorter: false,
      width: 100,
      align: 'right',
      render: (dom, record) => (
        <>
          {record?.is_single ? (
            ni(record.total_piece_qty_per_item)
          ) : (
            <span className="text-sm c-grey" style={{ opacity: 0.3 }}>
              {ni(record.total_piece_qty)}
            </span>
          )}
        </>
      ),
    },
    {
      title: 'Sys Qty',
      dataIndex: ['box_qty_dummy'],
      sorter: false,
      width: 120,
      align: 'right',
      className: 'bl2 b-gray',
      tooltip: 'Click to manage system stock. Grey color indicates a figure to be up synced into Magento shop.',
      render: (dom, record) => (
        <Row gutter={8}>
          <Col span={12}>{ni(record.box_qty)}</Col>
          <Col span={12} className="italic c-grey">
            {ni(
              (record.is_single ? sn(record.total_piece_qty_per_item) : sn(record.box_qty)) -
                (record.is_single ? 0 : sn(record.res_cal)),
            )}
          </Col>
        </Row>
      ), // It has different meaning: If single -> piece qty, if box -> box qty
      onCell: (record) => {
        if (record.id) {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleQtyModalVisible(true);
            },
          };
        }
        return {};
      },
    },
    {
      title: 'Magento Qty',
      dataIndex: ['quantity'],
      sorter: false,
      width: 130,
      align: 'right',
      className: 'bl2 b-gray',
      tooltip: 'Stock Qty = Salable Qty + Reserved Qty',
      render: (dom, record) => (
        <>
          <Row gutter={8}>
            <Col flex={'60px'}>{ni(record.quantity)}</Col>
            <Col flex="auto" className="italic c-grey">
              {ni(sn(record.quantity) - sn(record.res_quantity))}{' '}
              {sn(record.res_quantity) ? `+ ${ni(record.res_quantity)}` : ''}
            </Col>
          </Row>
        </>
      ),
    },
    {
      title: 'Status Sys/Mag. Stock',
      dataIndex: ['status'],
      sorter: false,
      width: 90,
      align: 'center',
      className: 'bl2 b-gray p-0',
      render: (__, record) => {
        let eleSys = <WarningOutlined style={{ opacity: 0.07 }} title="No sys EAN exists!" />;
        if (record.sys_status == 1) {
          eleSys = <CheckCircleOutlined style={{ color: 'green' }} />;
        } else if (record.sys_status == 0) {
          eleSys = <CloseOutlined style={{ color: 'gray' }} />;
        }

        let ele = <WarningOutlined style={{ opacity: 0.07 }} title="No Magento stock exists!" />;
        if (record.status == 1) {
          ele = <CheckCircleOutlined style={{ color: 'green' }} />;
        } else if (record.status == 0) {
          ele = <CloseOutlined style={{ color: 'gray' }} />;
        }
        return (
          <Row>
            <Col span={12}>{eleSys}</Col>
            <Col span={12}>{ele}</Col>
          </Row>
        );
      },
    },
    {
      title: 'Sys GFC Qty',
      dataIndex: ['virtual_stock_qty_gfc'],
      sorter: false,
      width: 70,
      align: 'right',
      tooltip: 'empty: upsync 10.000, 0: upsync 0',
      className: 'bl2 b-gray',
      render: (dom, record) => {
        return record.id ? (
          <Row gutter={6}>
            <Col flex="auto">{record.virtual_stock_qty_gfc == null ? '' : ni(record.virtual_stock_qty_gfc, true)}</Col>
            <Col flex="20px">
              <Popconfirm
                key="edit"
                icon={false}
                title={
                  <SProFormDigit
                    placeholder="Qty"
                    fieldProps={{
                      defaultValue: record.virtual_stock_qty_gfc == null ? 10000 : record.virtual_stock_qty_gfc,
                      onChange(value) {
                        vstockRef.current = value as number;
                      },
                    }}
                    help={
                      <>
                        Leave empty to up sync 10.000.
                        <br /> Set 0 to up sync 0.
                      </>
                    }
                  />
                }
                okText="Ok"
                cancelText="Cancel"
                placement="left"
                onOpenChange={(visible) => {
                  if (visible) {
                    vstockRef.current = record.virtual_stock_qty_gfc == null ? 10000 : record.virtual_stock_qty_gfc;
                  }
                }}
                onConfirm={() => {
                  const hide = message.loading(`Updating...`, 0);
                  updateEanAttributePartial({
                    mode: 'virtualQtyForGFC',
                    id: record.id,
                    virtual_stock_qty_gfc: vstockRef.current,
                  })
                    .then((res) => {
                      message.destroy();
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                <EditOutlined />
              </Popconfirm>
            </Col>
          </Row>
        ) : null;
      },
    },
    {
      title: 'Mag. GFC Qty',
      dataIndex: ['mag_inventory_stocks_quantity_gfc'],
      sorter: false,
      width: 70,
      align: 'right',
      className: 'bl2 b-gray',
      render: (dom, record) => ni(record.mag_inventory_stocks_quantity_gfc),
    },
    {
      title: 'Sys / Mag. Status',
      dataIndex: ['m_status'],
      sorter: false,
      width: 70,
      className: 'bl2 b-gray ',
      render: (dom, record) => (
        <Row style={{ textAlign: 'center' }}>
          <Col span={12}>
            {record.sys_status == 1 ? (
              <CheckCircleOutlined style={{ color: 'green' }} />
            ) : (
              <CloseOutlined style={{ color: 'gray' }} />
            )}
          </Col>
          <Col span={12}>
            {record.m_status == 1 ? (
              <CheckCircleOutlined style={{ color: 'green' }} />
            ) : (
              <CloseOutlined style={{ color: 'gray' }} />
            )}
          </Col>
        </Row>
      ),
    },
    {
      title: 'Sys Shops',
      dataIndex: 'website_ids',
      hideInForm: false,
      sorter: false,
      filters: false,
      width: 50,
      showSorterTooltip: false,
      className: 'bl2 b-gray align-top',
      render: (__, record) => <WebsiteIcons product_websites={record.website_ids} website_ids={record.m_website_ids} />,
    },
    {
      title: 'Mag. Shops',
      dataIndex: 'm_website_ids',
      hideInForm: false,
      sorter: false,
      filters: false,
      width: 50,
      showSorterTooltip: false,
      className: 'align-top',
      render: (__, record) => (
        <WebsiteIcons product_websites={record.m_website_ids} website_ids={record.m_website_ids} />
      ),
    },

    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      sorter: false,
      className: 'bl2 b-gray',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <a
            title="Update Magento Qty"
            key="us-qty"
            onClick={() => {
              const hide = message.loading('Updating stock Qty in shop', 0);
              usUpdateStockQty(record.sku || '', {
                qty: record?.is_single ? record.total_piece_qty_per_item : record.box_qty,
                includeStatusUpSync: true,
              })
                .then(() => {
                  hide();
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          >
            <UploadOutlined /> Update
          </a>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    setOnlyWebsiteAndStatus(searchFormRef.current?.getFieldValue('onlyWebsiteAndStatus') ?? false);
    setOnlyActiveShops(searchFormRef.current?.getFieldValue('onlyActiveShops') ?? false);
    setOnlyDiffPrice1(searchFormRef.current?.getFieldValue('onlyDiffPrice1') ?? false);
    setOnlyDiffPrice2(searchFormRef.current?.getFieldValue('onlyDiffPrice2') ?? false);
  }, []);

  useEffect(() => {
    actionRef.current?.reload();
  }, [onlyWebsiteAndStatus, onlyActiveShops, onlyDiffPrice1, onlyDiffPrice2]);

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);

  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
  });

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_stock_compare', { status: 'all2' })}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading: false, htmlType: 'submit', disabled: loading },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
          }}
        >
          <ProFormGroup size="small">
            <ProFormSelect
              name="diff"
              placeholder="Diff"
              label="Diff"
              options={[
                { value: '', label: 'All' },
                { value: 'status_diff', label: 'Status  <>  Status [include "Not Deliverable" difference]' },
                { value: 'website_diff', label: 'WebShop <> WebShop' },
                { value: 'qty_diff_fs1', label: 'Qty.Stock.FS1 <> Qty.Stock.FS1  [only If WebShop FS1 is set]' },
                { value: 'qty_diff_gfc', label: 'Qty.Stock.GFC <> Qty.Stock.GFC  [only If WebShop GFC is set]' },
                { value: 'qty_diff_rs', label: 'Qty.Stock.RS <> Qty.Stock.RS  [only If WebShop RS is set]' },
                { value: 'price_diff_fs1', label: 'Price.FS1 <> Price.FS1 [only If WebShop FS1 is set]' },
                { value: 'price_diff_gfc', label: 'Price.GFC <> Price.GFC [only If WebShop GFC is set]' },
                { value: 'price_diff_rs', label: 'Price.RS <> Price.RS [only If WebShop RS is set]' },
              ]}
              width={'sm'}
              fieldProps={{ onChange: () => actionRef.current?.reload(), dropdownMatchSelectWidth: false }}
              disabled={onlyWebsiteAndStatus}
            />
            <ProFormSelect
              name="diff2"
              placeholder="Diff 2"
              label="Diff 2"
              options={[
                { value: '', label: 'All' },
                { value: 'diff2_1', label: 'GFC Sys(No) <> Mag (Yes)' },
                { value: 'diff2_2', label: 'GFC Sys(Yes) <> Mag (no)' },
              ]}
              width={'sm'}
              fieldProps={{ onChange: () => actionRef.current?.reload() }}
              disabled={onlyWebsiteAndStatus}
            />

            <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} disabled={onlyWebsiteAndStatus} />
            {/* <ProFormSwitch
              name={'only_matched'}
              label="Matched?"
              initialValue={false}
              fieldProps={{
                onChange: () => {
                  actionRef.current?.reload();
                },
              }}
              disabled={onlyWebsiteAndStatus}
            /> */}
            <ProFormText
              name={'name'}
              label="Name"
              width={180}
              placeholder={'Item Name'}
              disabled={onlyWebsiteAndStatus}
            />
            {formElements}
            {/* <ProFormSelect
              name="trademarks[]"
              label="Trademarks"
              placeholder="Please select trademarks"
              mode="multiple"
              request={getTrademarkListSelectOptions}
              formItemProps={{ style: { width: 300 } }}
              disabled={onlyWebsiteAndStatus}
            /> */}
            {/* <ProFormCheckbox name={'open_orders'} label="Only open orders?" disabled={onlyWebsiteAndStatus} /> */}
          </ProFormGroup>
          <ProFormGroup size="small">
            <ProFormSelect
              name="product_websites"
              label="Websites"
              width={130}
              mode="multiple"
              placeholder={'Websites'}
              options={appSettings.storeWebsites
                ?.filter((x) => x.code != 'admin')
                ?.map((x) => ({
                  value: `${x.id}`,
                  label: x.name,
                }))}
            />
            {/* <ProFormSelect
              name="diff_old"
              placeholder="Qty Diff"
              label="Qty Diff"
              options={[
                { value: '', label: 'All' },
                { value: 'not_eq', label: 'Differences' },
                { value: 'gt', label: 'Qty. Sys < Qty. Magento' },
                { value: 'lt', label: 'Qty. Sys > Qty. Magento' },
                { value: 'eq', label: 'Qty. Sys = Qty. Magento' },
                // { value: 'status_not_eq', label: 'Status <> Sys/Magento' },
                // { value: 'status_eq', label: 'Status = Sys/Magento' },
              ]}
              formItemProps={{ style: { width: 240 } }}
              fieldProps={{ onChange: () => actionRef.current?.reload() }}
              disabled={onlyWebsiteAndStatus}
            /> */}
            {/* <ProFormSelect
              name="ean_status"
              placeholder="Status Sys"
              label="Status Sys"
              tooltip="Filter by EAN status in System"
              options={[
                { value: 1, label: 'Active' },
                { value: 0, label: 'Passive' },
              ]}
              width={100}
              fieldProps={{
                // onChange: (e) => actionRef.current?.reload(),
                dropdownMatchSelectWidth: false,
              }}
              disabled={onlyWebsiteAndStatus}
            /> */}
            {/* <ProFormSelect
              name="mag_status"
              placeholder="Status Magento"
              label="Status Magento"
              tooltip="Filter by EAN status in Magento"
              options={[
                { value: 1, label: 'Active' },
                { value: 0, label: 'Passive' },
              ]}
              width={100}
              fieldProps={{
                // onChange: (e) => actionRef.current?.reload(),
                dropdownMatchSelectWidth: false,
              }}
              disabled={onlyWebsiteAndStatus}
            /> */}
            {/* <ProFormSelect
              name="status"
              placeholder="Select status"
              label="Status All"
              tooltip="Filter by status in Sys & Magento Stock"
              options={[
                { value: '', label: 'All' },
                { value: 'all2', label: 'All except no Magento stock' },
                { value: 1, label: 'Active' },
                { value: 0, label: 'Passive' },
              ]}
              width={150}
              fieldProps={{
                onChange: (e) => actionRef.current?.reload(),
                dropdownMatchSelectWidth: false,
              }}
              disabled={onlyWebsiteAndStatus}
            /> */}
            {/* <ProFormSwitch
              name={'onlyWebsiteAndStatus'}
              label="Status & Website Diff Only?"
              initialValue={false}
              fieldProps={{
                onChange: (checked) => {
                  setOnlyWebsiteAndStatus(checked);
                },
              }}
            />
            <ProFormSwitch
              name={'onlyActiveShops'}
              label="Active Shop?"
              initialValue={false}
              tooltip="Only items which are in our System or in Magento active in any Shop."
              fieldProps={{
                onChange: (checked) => {
                  setOnlyActiveShops(checked);
                },
              }}
            /> */}
            {/* <ProFormSwitch
              name={'onlyDiffPrice1'}
              label="FsOne Price Diff Only?"
              initialValue={false}
              fieldProps={{
                onChange: (checked) => {
                  setOnlyDiffPrice1(checked);
                },
              }}
            />
            <ProFormSwitch
              name={'onlyDiffPrice2'}
              label="GFC Price Diff Only?"
              initialValue={false}
              fieldProps={{
                onChange: (checked) => {
                  setOnlyDiffPrice2(checked);
                },
              }}
            /> */}
          </ProFormGroup>
        </ProForm>
      </Card>

      <ProTable<API.StockCompare, API.PageParams>
        headerTitle={'Live Stock Compare List'}
        actionRef={actionRef}
        rowKey="sku"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        size="small"
        bordered
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_stock_compare', searchFormValues);
          Util.setSfValues('sf_stock_compare_p', params);
          setLoading(true);
          return getStockCompareList(
            {
              ...params,
              ...(!searchFormValues.onlyWebsiteAndStatus
                ? Util.mergeGSearch(searchFormValues)
                : {
                    onlyWebsiteAndStatus: searchFormValues.onlyWebsiteAndStatus,
                    trademarks: [searchFormValues.trademark?.value],
                  }),
            },
            sort,
            filter,
          )
            .then((res) => {
              // validate selected rows
              if (selectedRows?.length) {
                const skus = res.data.map((x: API.StockCompare) => x.sku);
                setSelectedRows((prev) => prev.filter((x) => skus.findIndex((sku: string) => sku == x.sku) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_stock_compare_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record?.is_single ? 'row-single' : 'row-multi')}
        toolBarRender={() => [
          <Popconfirm
            key="primary"
            title={<>Are you sure you want to down sync all prices?</>}
            overlayStyle={{ width: 350 }}
            style={{}}
            onConfirm={() => {
              const hide = message.loading('Down Syncing prices... It would take several minutes.', 0);
              dsProductBasePrices()
                .then((res) => {
                  message.success('Prices down synced successfully.');
                  // setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          >
            <Button type="primary" icon={<DownloadOutlined />}>
              Sync Magento Prices
            </Button>
          </Popconfirm>,
          <Popconfirm
            key="primary"
            title={
              <>
                Are you sure you want to sync stocks for cached SKUs with the following steps? <br />
                <br />
                1. Sync the latest orders. <br />
                2. Sync stocks for cached SKUs.
                <br />
                <br />
                If you want to fully sync stocks, please run deep sync at the top menu.
              </>
            }
            overlayStyle={{ width: 350 }}
            style={{}}
            onConfirm={() => {
              const hide = message.loading('Down Syncing stock items... It would take several minutes.', 0);
              dsMagentoInventoryStock({ isCache: true })
                .then((res) => {
                  setAppSettings((prev) => ({ ...prev, magDsStat: res.magDsStat }));
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          >
            <Button type="primary" icon={<DownloadOutlined />}>
              Sync Magento Stocks (Cache)
            </Button>
          </Popconfirm>,
        ]}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.sku as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
        columnEmptyText=""
      />

      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            key="primary"
            title={
              <>
                Are you sure you want to up sync statuses for selected? <br />
              </>
            }
            overlayStyle={{ width: 350 }}
            style={{}}
            icon={<UploadOutlined />}
            onConfirm={async () => {
              const fnLists = [];
              for (const record of selectedRows) {
                fnLists.push(async () => {
                  return usUpdateStockQty(record.sku || '', {
                    qty: record?.is_single ? record.total_piece_qty_per_item : record.box_qty,
                    includeStatusUpSync: true,
                  });
                });
              }
              await run(fnLists);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              UpSync Status & Stock
            </Button>
          </Popconfirm>
          <Popconfirm
            title={<>Are you sure you want to up sync stocks of selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const fnLists = [];
              for (const record of selectedRows) {
                fnLists.push(async () => {
                  return usUpdateStockQty(record.sku || '', {
                    qty: record?.is_single ? record.total_piece_qty_per_item : record.box_qty,
                    includeStatusUpSync: true,
                  });
                });
              }
              await run(fnLists);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync Stock
            </Button>
          </Popconfirm>
          <Popconfirm
            title={<>Are you sure you want to up sync prices of selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const fnLists = [];
              for (const x of selectedRows) {
                fnLists.push(async () => {
                  return usProductPrice(Number(x.id));
                });
              }
              await run(fnLists);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync (Price)
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      {/* {currentRow?.id && (
        <IboDetailModal
          eanId={currentRow?.id}
          modalVisible={openIBOModal}
          handleModalVisible={setOpenIBOModal}
        />
      )} */}
      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      {modalElement}
    </PageContainer>
  );
};

export default StockCompareList;
