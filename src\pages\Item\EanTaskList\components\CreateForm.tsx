import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
import { addEanTask } from '@/services/foodstore-one/Item/ean-task';
import { message } from 'antd';
import Util from '@/util';
import { useModel } from 'umi';
import { DictType } from '@/constants';

const handleAdd = async (fields: API.EanTask) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEanTask(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.EanTask>;

export type CreateFormProps = {
  values?: Partial<API.EanTask>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EanTask) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { getDictOptionsCV } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Ean Task'}
      width="600px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 5 }}
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const newData = { ...value, ean: props.values?.ean };
        const success = await handleAdd(newData);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(newData);
        }
      }}
    >
      <ProFormSelect
        name="category_code"
        label="Category"
        width="sm"
        options={getDictOptionsCV(DictType.EanTaskCategory)}
      />
      <ProFormTextArea
        rules={[
          {
            required: true,
            message: 'Task is required',
          },
        ]}
        name="task"
        label="Task"
      />
    </ModalForm>
  );
};

export default CreateForm;
