import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup, ProFormRadio } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProForm } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import type { ButtonProps } from 'antd';
import { Col, Row } from 'antd';
import { Button } from 'antd';
import { Card } from 'antd';
import { TRADEMARK_FILTER_DEFAULT, getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
import type { DateRangeType } from '@/util';
import Util, { dtYw, dtYwNo, nf2, ni, sn } from '@/util';
import type { ActionType, ColumnsState, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import ButtonGroup from 'antd/lib/button/button-group';
import SProFormDateRange, { DRSelection } from '@/components/SProFormDateRange';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { getSalesStatsWeeklyList } from '@/services/foodstore-one/Report/sales-stat';
import { DAYS, DT_FORMAT_YMD } from '@/constants';
import { Column, Line } from '@ant-design/plots';
import type { ColumnConfig, LineConfig } from '@ant-design/charts';
import OrderListModal from '@/pages/Magento/Order/components/OrderListModal';
import moment from 'moment';

// value columns
const VCOLS = ['qty', 'turnover', 'cturnover', 'ebay_fee', 'bp', 'gp', 'gp_no_discounted', 'gp_discounted', 'landed'];

const VCOL_LABELS: Record<string, string> = {
  qty: 'Quantity',
  cturnover: 'Turnover / Prov.',
  turnover: 'Order Turnover',
  ebay_fee: 'Ebay Fee',
  bp: 'BP',
  gp: 'GP',
  gp_no_discounted: 'GP w/o %',
  gp_discounted: 'GP %',
  landed: 'Landed',
};

const VCOL_OPTIONS = VCOLS.map((v) => ({ value: v, label: VCOL_LABELS[v] }));

type OrderModalSearchParamsType = SearchFormValueType & {
  dateRange?: DateRangeType;
  source?: string;
  columnSection?: string;
} & { filtered_only?: boolean } & { trademark_name?: string };

const TrademarkNavButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  style: { width: 24, height: 24, fontSize: 14 },
};

type RecordType = API.OrderItem & Record<string, any>;

export type SearchFormValueType = {
  trademark?: DefaultOptionType;
  ean?: string;
  sku?: string;
  ean_type_search?: string;
  order_id?: number;
  //   sale_types?: ColumnSection[];

  // Date range selector
  start_date?: string;
  end_date?: string;
  dr_selection?: string;
};

const isTotalRow = (uid: string) => uid == 'total';
const isAvgRow = (uid: string) => uid == 'avg';
const isDataRow = (uid: string) => !isTotalRow(uid) && !isAvgRow(uid);

const defaultSearchFormValues: SearchFormValueType = {
  sku: '',
  ean: '',
  trademark: undefined, // All items as a default.
  // sale_types: [ColumnSection.all],
};

const SalesStatWeeklyList: React.FC<any> = (props) => {
  const inModal = props.inModal;

  // Table related
  const actionRef = useRef<ActionType>();
  const [ds, setDs] = useState<RecordType[]>([]);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);
  // table column states
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>(() => ({}));

  // Search forms
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const [loading, setLoading] = useState<boolean>(false);
  const [trademarks, setTrademarks] = useState<DefaultOptionType[]>([]); // trademarks dropdown options

  // view mode
  const [colMode, setColMode] = useState<string>(
    () => Util.getSfValues('sf_order_sales_stat_weekly' + (inModal ? '_m' : ''), {}).colMode ?? 'qty',
  );

  // AVG quantity chart
  const [avgChartConfig, setAvgChartConfig] = useState<ColumnConfig>();
  const [lineChartConfig, setLineChartConfig] = useState<LineConfig>();
  const [wholeLineChartConfig, setWholeLineChartConfig] = useState<LineConfig>();

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});

  const avgRow = useMemo(() => {
    return ds[0];
  }, [ds]);

  /**
   * Handler to open the orders list modal.
   *
   * @param columnField
   * @param intervalType
   * @param dateRange
   * @param options
   */
  const handleQuantityOnClick = useCallback((columnField: string, extraFilters?: Record<string, any>) => {
    const sfValues = searchFormRef.current?.getFieldsValue() || {};
    const params: OrderModalSearchParamsType = {
      source: 'salesReport',
      filtered_only: true,
      ...sfValues,
      // ean_type_search: sfValues.ean_type_search,
      // order_id: sfValues.order_id,
      trademark: sfValues.trademark,
      /* trademark_name: sfValues.trademark
          ? (trademarks?.find((x) => x.value == sfValues.trademark)?.label as string)
          : undefined, */
      sku: sfValues.sku,
      ...extraFilters,
    };

    setModalSearchParams(params);
    setOpenOrderListModal(true);
  }, []);

  useEffect(() => {
    if (props.sku && inModal) {
      searchFormRef.current?.resetFields();
      searchFormRef.current?.setFieldValue('sku', props.sku || '');

      searchFormRef.current?.setFieldValue('dr_selection', DRSelection.DR_CUSTOM);
      searchFormRef?.current?.setFieldValue('start_date', moment().subtract(365, 'days').format(DT_FORMAT_YMD));

      actionRef.current?.reload();
    }
  }, [inModal, props.sku]);

  const buildColumns = useCallback(() => {
    const cols: ProColumns[] = [
      {
        title: 'Range',
        dataIndex: 'yw',
        width: 100,
        render(__, record) {
          return isDataRow(record.uid) ? dtYw(record.yw_detail) : record.yw;
        },
      },
      {
        title: 'Week No',
        dataIndex: 'yw',
        width: 70,
        align: 'center',
        className: 'br2 b-black',
        render(dom, record) {
          return dtYwNo(record.yw_detail);
        },
      },
    ];
    DAYS.forEach((day, wd) => {
      const suffix = '_' + wd;
      VCOLS.forEach((x) => {
        cols.push({
          title: day,
          dataIndex: `${x}${suffix}`,
          hideInTable: x != colMode,
          align: 'right',
          render: (__, record) => {
            const val = sn(record[`${x}${suffix}`]);
            const valStr = Util.numberFormat(val, false, x == 'qty' ? 0 : 2);
            const avgValue = sn(avgRow?.[`${x}${suffix}`]);
            let cls = '';
            if (isDataRow(record.uid) && avgValue && val) {
              if (avgValue < val) cls = 'c-green';
              else if (avgValue > val) cls = 'c-red';
            }

            return (
              <>
                <span className={cls} title={x == 'qty' ? '' : `Quantity: ${ni(record[`qty${suffix}`])}`}>
                  {valStr}
                </span>
                {/* <Row wrap={false}><Col flex="20px" style={{ textAlign: 'left' }}>T</Col> <Col flex="auto">{Util.numberFormat(sn(record[`turnover${suffix}`]), false, x == 'turnover' ? 0 : 2)}</Col></Row>
                <Row wrap={false}><Col flex="20px" style={{ textAlign: 'left' }}>BP</Col><Col flex="auto">{Util.numberFormat(sn(record[`bp${suffix}`]), false, x == 'bp' ? 0 : 2)}</Col></Row>
                <Row wrap={false}><Col flex="20px" style={{ textAlign: 'left' }}>CT</Col> <Col flex="auto">{Util.numberFormat(sn(record[`cturnover${suffix}`]), false, x == 'cturnover' ? 0 : 2)}</Col></Row>
                <Row wrap={false}><Col flex="20px" style={{ textAlign: 'left' }}>L</Col><Col flex="auto">{Util.numberFormat(sn(record[`landed${suffix}`]), false, x == 'landed' ? 0 : 2)}</Col></Row>
                <Row wrap={false}><Col flex="20px" style={{ textAlign: 'left' }}>GP</Col><Col flex="auto">{Util.numberFormat(sn(record[`gp${suffix}`]), false, x == 'gp' ? 0 : 2)}</Col></Row>
                <Row wrap={false}><Col flex="20px" style={{ textAlign: 'left' }}>Q</Col><Col flex="auto">{Util.numberFormat(sn(record[`qty${suffix}`]), false, x == 'gp' ? 0 : 0)}</Col></Row> */}
              </>
            );
          },
          onCell: (record) => {
            if (record[`qty${suffix}`]) {
              const filterOptions: any = {};
              // week of day
              filterOptions.wd = wd;
              // range date
              const arr = `${record.yw_detail}`.split('|');
              if (isDataRow(record.uid) && arr.length > 2) {
                filterOptions.dateRange = {
                  from: arr[1],
                  to: arr[2],
                };
              } else if (!isDataRow(record.uid)) {
                filterOptions.dateRange = {
                  from: searchFormRef.current?.getFieldValue('start_date'),
                  to: searchFormRef.current?.getFieldValue('end_date'),
                };
              }

              return {
                className: 'cursor-pointer',
                onClick: () => {
                  handleQuantityOnClick(colMode, filterOptions);
                },
              };
            } else return {};
          },
        });
      });
    });

    VCOLS.forEach((x) => {
      const suffix = '_s';
      cols.push({
        title: 'Total',
        dataIndex: `${x}${suffix}`,
        align: 'right',
        className: 'bl2 b-black',
        hideInTable: x != colMode,
        render: (__, record) => Util.numberFormat(record[`${x}${suffix}`], false, x == 'qty' ? 0 : 2),
        onCell: (record) => {
          if (record[`qty${suffix}`]) {
            const filterOptions: any = {};
            // range date
            const arr = `${record.yw_detail}`.split('|');
            if (isDataRow(record.uid) && arr.length > 2) {
              filterOptions.dateRange = {
                from: arr[1],
                to: arr[2],
              };
            } else if (!isDataRow(record.uid)) {
              filterOptions.dateRange = {
                from: searchFormRef.current?.getFieldValue('start_date'),
                to: searchFormRef.current?.getFieldValue('end_date'),
              };
            }

            return {
              className: 'cursor-pointer',
              onClick: () => {
                handleQuantityOnClick(colMode, filterOptions);
              },
            };
          } else return {};
        },
      });
    });
    setColumns(cols);

    // AVG charts section
    if (avgRow) {
      const avgConfig: ColumnConfig = {
        data: DAYS.map((day, wd) => ({
          key: day,
          value: sn(avgRow[`${colMode}_${wd}`] ?? 0, colMode == 'qty' ? 0 : 2),
        })),
        xField: 'key',
        yField: 'value',
        label: {
          position: 'top',
          style: {
            opacity: 1,
          },
          offsetY: 6,
        },
        xAxis: {
          label: {
            autoHide: true,
            autoRotate: false,
          },
        },
        meta: {
          key: {
            alias: 'Day',
          },
          value: {
            alias: VCOL_LABELS[colMode],
            formatter(value, index) {
              return '' + (colMode == 'qty' ? ni(value) : nf2(value));
            },
          },
        },
      };
      setAvgChartConfig(avgConfig);
    }
  }, [avgRow, colMode, handleQuantityOnClick]);

  useEffect(() => {
    const chartData: any[] = [];
    const wholeChartData: any[] = [];
    if (ds.length > 2) {
      for (let i = ds.length - 1; i > 1; i--) {
        const x = ds[i];
        chartData.push({
          week: dtYw(x.yw_detail),
          value: sn(x[`${colMode}_s`] ?? 0, colMode == 'qty' ? 0 : 2),
        });

        DAYS.forEach((day, wd) => {
          wholeChartData.push({
            week: dtYw(x.yw_detail),
            value: sn(x[`${colMode}_${wd}`] ?? 0, colMode == 'qty' ? 0 : 2),
            wd: day,
          });
        });
      }
    }
    const lineConfig: LineConfig = {
      data: chartData,
      xField: 'week',
      yField: 'value',
      label: {},
      point: {
        size: 4,
        shape: 'circle',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      xAxis: {
        /* grid: {
          line: {
            style: {
              stroke: 'black',
              lineWidth: 2,
              lineDash: [4, 5],
              strokeOpacity: 0.7,
              shadowColor: 'black',
              shadowBlur: 10,
              shadowOffsetX: 5,
              shadowOffsetY: 5,
              cursor: 'pointer',
            },
          },
        }, */
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      meta: {
        week: {
          alias: 'Week',
        },
        value: {
          alias: VCOL_LABELS[colMode],
          formatter(value, index) {
            return '' + (colMode == 'qty' ? ni(value) : nf2(value));
          },
        },
      },
      slider:
        ds.length > 20
          ? {
              start: (ds.length - 2 - 10) / (ds.length - 2),
              end: 1,
            }
          : undefined,
    };
    setLineChartConfig(lineConfig);

    const wholeConfig: LineConfig = {
      data: wholeChartData,
      xField: 'week',
      yField: 'value',
      seriesField: 'wd',
      meta: {
        week: {
          alias: 'Week',
        },
        value: {
          formatter(value, index) {
            return '' + (colMode == 'qty' ? ni(value) : nf2(value));
          },
        },
      },
      slider:
        ds.length > 20
          ? {
              start: (ds.length - 2 - 10) / (ds.length - 2),
              end: 1,
            }
          : undefined,
    };
    setWholeLineChartConfig(wholeConfig);
  }, [ds, colMode]);

  useEffect(() => {
    getTrademarkListSelectOptions({})
      .then((res) => setTrademarks(res))
      .catch(Util.error);
  }, []);

  useEffect(() => {
    buildColumns();
  }, [buildColumns]);

  /**
   * Prev / Next navigation in Trademark filter
   * @param mode
   */
  const handleTrademarkNavigation = (mode: 'prev' | 'next') => {
    setLoading(true);
    const curTrademarkObj = searchFormRef.current?.getFieldValue('trademark');
    const curTrademarkId = curTrademarkObj.value ?? TRADEMARK_FILTER_DEFAULT;
    const index = trademarks.findIndex((x) => x.value === curTrademarkId) ?? 0;
    const dir: number = mode == 'prev' ? -1 : 1;
    let nextIndex = (index + dir + trademarks.length) % trademarks.length;
    if (trademarks[nextIndex]?.value == 'sep') {
      nextIndex = (nextIndex + dir + trademarks.length) % trademarks.length;
    }
    searchFormRef.current?.setFieldValue('trademark', trademarks[nextIndex]);
    actionRef.current?.reload();
  };

  return (
    <>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues(
            'sf_order_sales_stat_weekly' + (inModal ? '_m' : ''),
            defaultSearchFormValues,
          )}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue(defaultSearchFormValues as any);
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name="trademark"
            label="Trademark"
            placeholder="Please select trademark"
            mode="single"
            showSearch
            options={trademarks}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              maxTagCount: 1,
              labelInValue: true,
              onChange: () => {
                actionRef.current?.reload();
              },
            }}
            width={180}
            disabled={loading}
          />
          <ButtonGroup>
            <Button
              {...TrademarkNavButtonProps}
              onClick={() => handleTrademarkNavigation('prev')}
              icon={<ArrowLeftOutlined />}
              disabled={loading}
            />
            <Button
              {...TrademarkNavButtonProps}
              onClick={() => handleTrademarkNavigation('next')}
              icon={<ArrowRightOutlined />}
              disabled={loading}
            />
          </ButtonGroup>

          <SProFormDateRange label="Date" formRef={searchFormRef} style={{ marginLeft: 16 }} disabled={loading} />
          <ProFormText label="SKU" name="sku" width="xs" placeholder="SKU" />
          <ProFormGroup>
            <ProFormRadio.Group
              name="colMode"
              radioType="button"
              options={VCOL_OPTIONS}
              fieldProps={{
                onChange(e) {
                  setColMode(e.target.value);
                  const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
                  Util.setSfValues('sf_order_sales_stat_weekly' + (inModal ? '_m' : ''), searchFormValues);
                },
              }}
            />
          </ProFormGroup>
        </ProForm>
      </Card>

      <Row gutter={24}>
        <Col flex="950px" style={{ paddingBottom: 24 }}>
          <ProTable<RecordType, API.PageParams>
            headerTitle={`Weekly Sales Statistics - ${VCOL_LABELS[`${colMode}`]}`}
            actionRef={actionRef}
            size="small"
            rowKey="uid"
            revalidateOnFocus={false}
            options={{ fullScreen: true }}
            search={false}
            sticky
            bordered
            // scroll={{ x: 1200 }}
            style={{ maxWidth: 1000 }}
            // scroll={{ x: 'max-content' }}
            dataSource={ds}
            request={async (params, sort, filter) => {
              const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
              Util.setSfValues('sf_order_sales_stat_weekly' + (inModal ? '_m' : ''), searchFormValues);
              setLoading(true);
              return getSalesStatsWeeklyList(
                {
                  ...params,
                  ...Util.mergeGSearch(searchFormValues),
                  trademark: searchFormValues.trademark?.value,
                  // columnSections,
                },
                sort,
                filter,
              )
                .then((res) => {
                  setDs(res.data);
                  return [];
                })
                .finally(() => setLoading(false));
            }}
            onRequestError={Util.error}
            pagination={{
              showSizeChanger: true,
              hideOnSinglePage: true,
              defaultPageSize: 10000,
            }}
            columns={columns}
            columnsState={{
              value: colStates,
              onChange(map) {
                setColStates(map);
              },
            }}
            columnEmptyText=""
            rowSelection={false}
            rowClassName={(record) => {
              if (record.uid == 'total') {
                return 'total-row';
              } else return record.ean_id ? (record?.is_single ? 'row-single' : 'row-multi') : '';
            }}
          />
        </Col>
        <Col flex="0 0 500px">
          <Card title={`Avg - ${VCOL_LABELS[`${colMode}`]}`} size="small" bodyStyle={{ minHeight: 200, maxWidth: 700 }}>
            {avgChartConfig && <Column height={200} {...avgChartConfig} />}
          </Card>

          <Card
            title={`Stat Weekly Total - ${VCOL_LABELS[`${colMode}`]}`}
            size="small"
            style={{ marginTop: 24 }}
            bodyStyle={{ minHeight: 200, maxWidth: 700 }}
          >
            {lineChartConfig && <Line height={200} {...lineChartConfig} />}
          </Card>

          <Card
            title={`Stat Week Daily - ${VCOL_LABELS[`${colMode}`]}`}
            size="small"
            style={{ marginTop: 24 }}
            bodyStyle={{ minHeight: 200, maxWidth: 700 }}
          >
            {wholeLineChartConfig && <Line height={200} {...wholeLineChartConfig} />}
          </Card>
        </Col>
      </Row>
      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
      />
    </>
  );
};

export default SalesStatWeeklyList;
