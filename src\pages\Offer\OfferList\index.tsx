import { DeleteOutlined, EditOutlined, LinkOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Card, Space, Popconfirm, Tag, Typography, Row, Col } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>Container, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { nf2, ni, sn, sUrlByTpl } from '@/util';
import CreateForm from './components/CreateForm';
import { getOfferList, deleteOffer, updateOffer } from '@/services/foodstore-one/Offer/offer';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode, OfferIboStatus, OfferIboStatusKv } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProForm<PERSON>heckbox, ProFormDigit, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import EditableCell from '@/components/EditableCell';
import { useLocation, useModel } from 'umi';
import UpdateGfcNoteFormModal from './components/UpdateGfcNoteFormModal';
import QuoteSelectionModal from './components/QuoteSelectionModal';
import CustomerSelectionModal from './components/CustomerSelectionModal';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Offer[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteOffer(selectedRows.map((row) => row.id).join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export const OfferIboStatusComp: React.FC<{ status?: OfferIboStatus | string }> = ({ status }) => {
  let color = 'default';
  switch (`${status}`) {
    case OfferIboStatus.Closed:
      color = 'success';
      break;
    case OfferIboStatus.Open:
      color = 'blue';
      break;

    case OfferIboStatus.ExpectingItems:
      color = 'orange';
      break;
  }

  return <Tag color={color as any}>{OfferIboStatusKv[`${status}`]}</Tag>;
};

export const IboPreTitle: React.FC<{ iboPreManagement: API.IboPreManagement }> = ({ iboPreManagement }) => {
  let cls = '';
  if (iboPreManagement.status == 'sent') {
    cls += ' c-blue';
  } else if (iboPreManagement.status == 'open') {
    cls += ' c-red';
  } else if (iboPreManagement.status == 'invoiced') {
    cls += ' c-green';
  } else if (iboPreManagement.status == 'done') {
    cls += ' c-green-dark';
  }
  return (
    <Typography.Link
      href={`/ibo/ibo-pre?ibo_pre_management_id=${iboPreManagement.id}`}
      target="_blank"
      style={{ display: 'block' }}
      key={iboPreManagement.id}
    >
      <span className={cls}>
        {`${iboPreManagement.inbound_no || '-'} | ${iboPreManagement.supplier?.name || '-'} | ${
          iboPreManagement.note_supplier || '-'
        } `}
        <br />
        {`(${iboPreManagement.status})`}
      </span>
    </Typography.Link>
  );
};

export const IboPreManagementListInCell: React.FC<{
  ibo_pre_managements: API.IboPreManagement[];
  offer_id?: number;
  mapped_detail?: any;
}> = ({ ibo_pre_managements, offer_id, mapped_detail }) => {
  return (
    <>
      {ibo_pre_managements?.map((x) => (
        <IboPreTitle key={`${x.id}`} iboPreManagement={x} />
      ))}
    </>
  );
};

export const IboPreManagementListInCellWithCount: React.FC<{
  ibo_pre_managements: API.IboPreManagement[];
  offer_id?: number;
  mapped_detail?: any;
}> = ({ ibo_pre_managements, offer_id, mapped_detail }) => {
  return (
    <>
      {ibo_pre_managements
        ?.filter((x) => offer_id && mapped_detail && mapped_detail[`${x.id}`])
        ?.map((x) => {
          let cls = '';
          if (x.status == 'sent') {
            cls += ' c-blue';
          } else if (x.status == 'open') {
            cls += ' c-red';
          } else if (x.status == 'invoiced') {
            cls += ' c-green';
          } else if (x.status == 'done') {
            cls += ' c-green-dark';
          }
          return (
            <div
              key={`${x.id}`}
              className={cls}
              title={`Total Pcs: ${ni(mapped_detail[`${x.id}`]['ibo_pre_qty_pcs'], true)}`}
            >
              {`${ni(mapped_detail[`${x.id}`]['mapped_offer_item_cnt'], true)}`}
            </div>
          );
        })}
    </>
  );
};

export type SearchFormValueType = Partial<API.Offer>;

const Offer: React.FC = () => {
  const location: any = useLocation();
  const { getDictByCode } = useModel('app-settings');

  const offerNoInUrl = location.query?.offer_no;
  const quoteIdInUrl = location.query?.quote_id;

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [openGfcNoteEditModal, setOpenGfcNoteEditModal] = useState<boolean>(false);
  const [openQuoteSelectionModal, setOpenQuoteSelectionModal] = useState<boolean>(false);
  const [openCustomerSelectionModal, setOpenCustomerSelectionModal] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<API.Offer>();
  const [selectedRowsState, setSelectedRows] = useState<API.Offer[]>([]);

  const columns: ProColumns<API.Offer>[] = [
    {
      title: 'Offer No',
      dataIndex: 'offer_no',
      sorter: true,
      hideInForm: true,
      defaultSortOrder: 'descend',
      width: 100,
      render(__, record) {
        const customerId = record.customer_id;
        return (
          <Row>
            <Col flex="auto">
              <a href={`/quotes/offer-item?offer_id=${record.id}&offer_no=${record.offer_no}`} target="_blank">
                {record.offer_no}
              </a>
            </Col>
            <Col flex="16px">
              {!!customerId && (
                <a
                  href={`${GFC_CUST_PIM_URL}/customers/detail/${customerId}`}
                  target="_blank"
                  title="Open GFC Cust page in new tab."
                >
                  <LinkOutlined />
                </a>
              )}
            </Col>
            <Col flex="16px">
              {!record.quote_id && (
                <EditOutlined
                  className={`cursor-pointer`}
                  title="Select a Customer..."
                  onClick={() => {
                    setCurrentRow(record);
                    setOpenCustomerSelectionModal(true);
                  }}
                />
              )}
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'ibo_status',
      hideInForm: true,
      tooltip: 'Click to Edit',
      align: 'center',
      width: 110,
      render(__, record) {
        return (
          <EditableCell
            dataType="select"
            defaultValue={record.ibo_status || ''}
            style={{ marginRight: 0 }}
            fieldProps={{ style: { lineHeight: 1 } }}
            valueEnum={OfferIboStatusKv}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateOffer(sn(record.id), {
                ibo_status: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            <OfferIboStatusComp status={record.ibo_status} />
          </EditableCell>
        );
      },
    },
    {
      title: '',
      dataIndex: 'gfc_note_edit',
      hideInForm: true,
      align: 'center',
      width: 30,
      render(__, record) {
        return (
          <EditOutlined
            className={`cursor-pointer ${record.gfc_note ? 'c-blue' : 'c-grey'}`}
            onClick={() => {
              setCurrentRow(record);
              setOpenGfcNoteEditModal(true);
            }}
          />
        );
      },
    },
    {
      title: 'Notes',
      dataIndex: 'note',
      ellipsis: true,
      width: 400,
      render(__, entity) {
        return `${entity.note ?? ''}${entity.quote?.customer_fullname ? ` - ${entity.quote?.customer_fullname}` : ''}`;
      },
    },

    {
      title: 'IBO PreOrder',
      dataIndex: 'note',
      ellipsis: true,
      width: 200,
      render(__, entity) {
        return <IboPreManagementListInCell ibo_pre_managements={entity.ibo_pre_managements ?? []} />;
      },
    },
    {
      title: 'Mapped Info',
      dataIndex: 'mapped_offer_item_cnt',
      tooltip: 'x / y: x -> mapped IBO Pre Count / Offer Item Count',
      width: 80,
      render: (dom, record) => {
        const num1 = sn(record.mapped_offer_item_cnt);
        const num2 = sn(record.offer_items_count_valid);

        return num2 ? (
          <span
            className={num1 < num2 ? 'c-red' : 'c-grey'}
            title={`Valid: ${ni(record.offer_items_count_valid)} of ${ni(record.offer_items_count)}`}
          >
            {`${ni(num1, true)} / ${ni(num2, true)}`}
          </span>
        ) : null;
      },
    },

    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 120,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return defaultRender(item);
      },
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 120,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return defaultRender(item);
      },
      render: (__, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'Quote ID',
      dataIndex: 'quote_id',
      align: 'center',
      width: 80,
      render(dom, entity) {
        return (
          <Row wrap={false}>
            <Col flex="auto" className="text-align">
              <Typography.Link
                href={sUrlByTpl(getDictByCode(DictCode.MAG_ADMIN_URL_QUOTE), {
                  quote_id: entity.quote_id,
                })}
                title="Go to FsOne Shop admin page."
                target="_blank"
              >
                {dom}
              </Typography.Link>
            </Col>
            {/* <Col flex="16px">
              <EditOutlined
                className={`cursor-pointer`}
                onClick={() => {
                  setCurrentRow(entity);
                  setOpenQuoteSelectionModal(true);
                }}
              />
            </Col> */}
          </Row>
        );
      },
    },

    {
      title: 'Customer Note',
      dataIndex: 'offer_customer_note',
      className: 'text-sm',
      width: 300,
    },
    {
      title: '%',
      dataIndex: 'percentage',
      className: 'text-sm',
      tooltip: 'Export Price Percentage',
      width: 50,
      align: 'right',
      render(__, entity) {
        return nf2(entity.percentage, false, true);
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      // tip: 'The username is the unique key',
      colSize: 1,
      search: false,
      className: 'text-sm c-grey',
      width: 50,
    },
    /* {
      title: 'Customer ID',
      dataIndex: 'customer_id',
      align: 'center',
      width: 90,
      render(dom, entity) {
        const customerId = entity.customer_id;
        return (
          <Row wrap={false}>
            <Col flex="auto" className="text-align">
              <Typography.Link
                href={`${GFC_CUST_PIM_URL}/customers/detail/${customerId}`}
                title="Open GFC Cust page in new tab."
                target="_blank"
              >
                {dom}
              </Typography.Link>
            </Col>
            <Col flex="16px">
              {!entity.quote_id && (
                <EditOutlined
                  className={`cursor-pointer`}
                  title="Select a Customer..."
                  onClick={() => {
                    setCurrentRow(entity);
                    setOpenCustomerSelectionModal(true);
                  }}
                />
              )}
            </Col>
          </Row>
        );
      },
    }, */
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 45,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({ ...record });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  useEffect(() => {
    searchFormRef.current?.setFieldValue('offer_no', offerNoInUrl);
    actionRef.current?.reload();
  }, [offerNoInUrl]);

  useEffect(() => {
    searchFormRef.current?.setFieldValue('quote_id', quoteIdInUrl);
    actionRef.current?.reload();
  }, [quoteIdInUrl]);

  return (
    <PageContainer
      title={
        <Space size={48}>
          <span>Offer Overview</span>
          <Typography.Link href="/quotes/offer-matrix" target="_blank" className="text-sm">
            Open Matrix
          </Typography.Link>
        </Space>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_offer', {}, {})}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          {/* <div className="d-none">
            <ProFormDigit name={'quote_id'} label="Quote ID" placeholder={'Quote ID'} />
          </div> */}
          <ProFormDigit name={'offer_no'} label="Offer No" width={180} placeholder={'Offer No'} />
          <ProFormSelect name="ibo_status" label="Status" valueEnum={OfferIboStatusKv} />
          <ProFormText name="note" label="Note" placeholder="Search note" />
          <ProFormText name="supplier_add" label="SUPPLIER_ADD" />
          <ProFormDigit name={'quote_id'} label="Quote" width={180} placeholder={'Quote ID'} />
          <ProFormCheckbox name="no_customer_id" label="No Customer?" />
        </ProForm>
      </Card>

      <ProTable<API.Offer, API.PageParams>
        headerTitle={'Offers List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_offer_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        search={false}
        scroll={{ x: 1200 }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        params={{ with: 'quote,linkedIboPreManagement,offerItemsCount,offerItemsCountValid' }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_offer', searchFormValues);
          Util.setSfValues('sf_offer_p', params);

          setLoading(true);
          return getOfferList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Offer) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRowsState?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          selectedRowKeys: selectedRowsState.map((x) => x.id as any),
        }}
        tableAlertRender={false}
        columnEmptyText={''}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>&nbsp;Offers.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            title={<>Are you sure you want to delete selected offers?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const res = await handleRemove(selectedRowsState);
              if (res) {
                setSelectedRows([]);
                actionRef.current?.reload();
              }
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />

      <UpdateGfcNoteFormModal
        modalVisible={openGfcNoteEditModal}
        handleModalVisible={setOpenGfcNoteEditModal}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          setOpenGfcNoteEditModal(false);
        }}
      />
      <QuoteSelectionModal
        offer={currentRow}
        modalVisible={openQuoteSelectionModal}
        handleModalVisible={setOpenQuoteSelectionModal}
        onSelect={(value) => {
          const hide = message.loading('Updating...', 0);
          updateOffer(sn(currentRow?.id), { quote_id: (value?.id ?? null) as any })
            .then((res) => {
              actionRef.current?.reload();
              message.success('Updated successfully.');
            })
            .catch(Util.error)
            .finally(hide);
        }}
      />
      <CustomerSelectionModal
        offer={currentRow}
        modalVisible={openCustomerSelectionModal}
        handleModalVisible={setOpenCustomerSelectionModal}
        onSelect={(value) => {
          const hide = message.loading('Updating...', 0);
          updateOffer(sn(currentRow?.id), { customer_id: (value?.id ?? null) as any })
            .then((res) => {
              actionRef.current?.reload();
              message.success('Updated successfully.');
            })
            .catch(Util.error)
            .finally(hide);
        }}
      />
    </PageContainer>
  );
};

export default Offer;
