import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDatePicker, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateOfferItemShipped } from '@/services/foodstore-one/Offer/offer-item-shipped';
import Util, { sn } from '@/util';
import SkuComp from '@/components/SkuComp';
import SProFormDigit from '@/components/SProFormDigit';
import SDatePicker from '@/components/SDatePicker';

export type FormValueType = Partial<API.OfferItemShipped>;

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOfferItemShipped(id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateFormProps = {
  initialValues?: Partial<API.OfferItemShipped>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItemShipped) => void;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      const newValues: API.OfferItemShipped = { ...(initialValues || {}) };
      formRef.current?.setFieldsValue(newValues);
    }
  }, [initialValues, modalVisible]);

  return (
    <ModalForm<API.OfferItemShipped>
      title={
        <Space size={24}>
          <div>Create a Shipment</div>
          <div>
            <SkuComp sku={initialValues?.item_ean?.sku} />
          </div>
          <Typography.Paragraph
            copyable={{
              text: initialValues?.item_ean?.ean || '',
              tooltips: 'Copy EAN ' + (initialValues?.item_ean?.ean || ''),
            }}
            style={{ marginBottom: 0 }}
          >
            {initialValues?.item_ean?.ean || ''}
          </Typography.Paragraph>
        </Space>
      }
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...initialValues,
          ...value,
        };
        const success = await handleUpdate(sn(initialValues?.id), data);

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProForm.Item name="case_qty" label="Case Qty">
        {initialValues?.case_qty}
      </ProForm.Item>

      <SProFormDigit name="qty" label="Shipped Qty" width="xs" />

      <ProFormText name="wa_no" label="WA No" width="xs" />

      <SDatePicker name="wa_date" label="WA Date" />

      <ProFormTextArea name="note" label="Comment" fieldProps={{ rows: 5 }} />
    </ModalForm>
  );
};

export default UpdateForm;
