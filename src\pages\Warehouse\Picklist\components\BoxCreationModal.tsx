import SProFormDigit from '@/components/SProFormDigit';
import { DictCode } from '@/constants';
import { createBox } from '@/services/foodstore-one/Warehouse/picklist';
import Util from '@/util';
import { ModalForm, ProFormInstance } from '@ant-design/pro-form';
import { message } from 'antd';
import { Dispatch, SetStateAction, useRef } from 'react';
import { useModel } from 'umi';

export type BoxCreationModalProps = {
  picklist: Partial<API.WarehousePicklist>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (boxStep: number) => Promise<boolean | void>;
  onCancel?: () => void;
};

const BoxCreationModal: React.FC<BoxCreationModalProps> = ({
  picklist,
  modalVisible,
  handleModalVisible,
  onSubmit,
}) => {
  const formRef = useRef<ProFormInstance>();

  const { getDictByCode } = useModel('app-settings');
  const defaultBoxSize = getDictByCode(DictCode.PL_ORDERS_IN_BOX);
  const maxBoxSize = getDictByCode(DictCode.PL_MAX_ORDERS_IN_BOX);

  return (
    <ModalForm
      title={'How many boxes to group?'}
      width="400px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      modalProps={{ okText: 'Create New Boxes' }}
      initialValues={{ box_size: defaultBoxSize }}
      onFinish={async (values) => {
        console.log(values);
        if (!picklist.id) {
          message.error('Picklist is not selected!');
          return;
        }
        const hide = message.loading('Creating new box...', 0);
        createBox(picklist.id, { ...values })
          .then((boxStep) => {
            if (formRef.current) formRef.current.resetFields();
            handleModalVisible(false);
            if (onSubmit) onSubmit(boxStep);
          })
          .catch(Util.error)
          .finally(() => hide());
      }}
    >
      <SProFormDigit name="box_size" label="Boxes" max={maxBoxSize} width="sm" />
    </ModalForm>
  );
};

export default BoxCreationModal;
