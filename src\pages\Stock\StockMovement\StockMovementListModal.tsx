import { Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { getStockMovementList } from '@/services/foodstore-one/Stock/stock-movement';
import { DEFAULT_PER_PAGE_PAGINATION, StockMovementReasonOptions } from '@/constants';
import Util, { ni } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormCheckbox, ProFormSelect } from '@ant-design/pro-form';
import EanTitleComp from '@/components/EanTitleComp';
import StockMovementRefComp from './StockMovementRefComp';

export type SearchFormValueType = Partial<API.StockMovement>;
export type StockMovementListModalSearchParamsType = {
  sku?: string;
  ean?: string;
  item_id?: number;
  is_single?: boolean;
  wl_id?: number;
};

type StockMovementListModalProps = {
  searchParams?: StockMovementListModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const StockMovementListModal: React.FC<StockMovementListModalProps> = (props) => {
  const { searchParams, modalVisible } = props;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.StockMovement>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Old WL',
      dataIndex: ['old_wl_name'],
      sorter: false,
      width: 80,
    },
    {
      title: 'New WL',
      dataIndex: ['new_wl_name'],
      width: 80,
      sorter: false,
    },
    {
      title: 'Reason',
      dataIndex: ['reason'],
      width: 130,
      sorter: false,
    },
    {
      title: 'Reason Detail',
      dataIndex: ['reason_text'],
      width: 120,
      sorter: false,
    },
    {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.box_qty),
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.case_qty),
    },
    {
      title: 'Total Piece Qty',
      dataIndex: ['total_piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      render: (dom, record) => ni(record.total_piece_qty),
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      sorter: false,
      width: 100,
      align: 'center',
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Ref',
      dataIndex: ['ref_type'],
      sorter: true,
      width: 130,
      fixed: 'right',
      render: (__, record) => <StockMovementRefComp record={record} />,
    },
    {
      title: 'Process Code',
      dataIndex: ['batch_code'],
      sorter: true,
      width: 100,
      align: 'center',
      className: 'text-xs c-lightgrey',
    },
    {
      title: 'Owner',
      dataIndex: ['owner'],
      sorter: false,
      width: 80,
      align: 'center',
    },
    {
      title: 'Created on',
      dataIndex: ['created_on'],
      sorter: true,
      defaultSortOrder: 'descend',
      width: 120,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
  ];

  useEffect(() => {
    if (modalVisible) {
      actionRef.current?.reload();
      searchFormRef.current?.setFieldValue('move_to_single_only', true);
    }
  }, [modalVisible, searchParams]);

  return (
    <Modal
      title={
        <>
          Stock Movements <EanTitleComp itemEan={searchParams} />
        </>
      }
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      width="1500px"
      footer={false}
      bodyStyle={{ paddingTop: 0, paddingBottom: 0 }}
      maskClosable
      className={searchParams?.is_single ? 'm-single' : 'm-multi'}
    >
      <ProTable<API.StockMovement, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        size="small"
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          setLoading(true);
          return getStockMovementList(
            {
              ...params,
              ...searchFormValues,
              item_id: searchParams?.item_id,
              with: 'refOrderItemJoin,refIbomOrderNo',
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={false}
        columnEmptyText=""
        toolBarRender={(action, rows) => {
          return [
            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              initialValues={searchParams}
              submitter={false}
              key="form"
              size="small"
            >
              <ProFormCheckbox
                name={'move_to_single_only'}
                label="MoveToSingle only?"
                fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
              />
              <ProFormSelect
                name={'reason'}
                label="Reason"
                width={'sm'}
                allowClear
                showSearch
                options={StockMovementReasonOptions as any}
                placeholder={'Reason'}
                fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
              />
              <ProFormSelect
                name={'refFilterMode'}
                label="Filter Mode"
                width={'sm'}
                allowClear
                options={[
                  { value: 'onlyOrderItem', label: 'Only Order Item' },
                  { value: 'notOnlyOrderItem', label: 'Not Order Item' },
                ]}
                placeholder={'Filter Mode'}
                fieldProps={{
                  onChange(value, option) {
                    actionRef.current?.reload();
                  },
                }}
              />
            </ProForm>,
          ];
        }}
      />
    </Modal>
  );
};

export default StockMovementListModal;
