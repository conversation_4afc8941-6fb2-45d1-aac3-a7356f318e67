import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, message, Modal, Row, Space, Spin, Typography } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormList } from '@ant-design/pro-form';
import Util, { ni, sn } from '@/util';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import DateSelector from '@/components/DateSelector';
import EanFilesComp from '@/components/EanFilesComp';
import { getEanDetail } from '@/services/foodstore-one/Item/ean';
import SkuComp from '@/components/SkuComp';

import styles from './CreateOrUpdatePackedReadyQty.less';
import ProTable from '@ant-design/pro-table';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { addOfferItemIboPrePackReadyMapBulk } from '@/services/foodstore-one/Offer/offer-item-packed-ready';
import OfferItemPackedReadyList from './OfferItemPackedReadyList';
import { InfoCircleOutlined } from '@ant-design/icons';
import UpdateNotesDeliveredForm from '../../IboPreList/components/UpdateNotesDeliveredForm';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';

export type FormValueType = Partial<API.OfferItemDelivered>;

export type CreateOrUpdatePackedReadyQtyProps = {
  itemEan: Partial<API.Ean>;
  iboPre: Partial<API.IboPre>;
  offer_id: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  setIboPre?: Dispatch<SetStateAction<API.IboPre | null | undefined>>; // To update parent IboPre data.
  loadIboPreDetail?: (ibo_pre_id?: number) => Promise<API.IboPre | null>;
  onSubmit?: (formData: API.OfferItemDelivered[]) => Promise<boolean | void>;
};

const CreateOrUpdatePackedReadyQty: React.FC<CreateOrUpdatePackedReadyQtyProps> = ({
  itemEan,
  iboPre,
  offer_id,
  modalVisible,
  handleModalVisible,
  loadIboPreDetail,
  setIboPre,
  onSubmit,
}) => {
  const formRef = useRef<ProFormInstance>();
  const [loadingEan, setLoadingEan] = useState<boolean>(false);
  const [loadingSave, setLoadingSave] = useState<boolean>(false);

  const [listReloadTick, setListReloadTick] = useState<number>(0);
  // Editable list
  const listActionRef = useRef<FormListActionType<API.OfferItemIboPrePackReadyMap>>();

  const [openIboPreDeliveryNotesUpdateForm, setOpenIboPreDeliveryNotesUpdateForm] = useState(false);

  // stock stable qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  const resetFormFields = () => {
    const listByRef = listActionRef.current?.getList();
    const newList = listByRef?.map((x) => ({ ...x, qty: null, exp_date: null }));
    formRef.current?.setFieldValue('qty_delivered_list', newList);
  };

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldValue('qty_delivered_list', []);
    }
  }, [modalVisible]);

  useEffect(() => {
    if (modalVisible && itemEan.id) {
      formRef.current?.setFieldValue('qty_delivered_list', []);

      setLoadingEan(true);
      getEanDetail({ id: itemEan.id, with: 'siblings,siblings.eanTextDe' })
        .then((res) => {
          const qtyPackedReadyList: any[] = [];
          if (res.siblings) {
            res.siblings.forEach((x) => {
              const rowObj = {
                ean_id: x.id,
                item_id: x.item_id,
                ean: x.ean,
                sku: x.sku,
                case_qty: x.attr_case_qty,
                exp_date: null,
                qty: null,
                item_ean: x,
              };
              qtyPackedReadyList.push(rowObj);
            });
          }

          formRef.current?.setFieldValue('qty_delivered_list', qtyPackedReadyList);
        })
        .catch((err) => {
          Util.error(err);
        })
        .finally(() => {
          setLoadingEan(false);
        });

      setListReloadTick((prev) => prev + 1);
    }
  }, [modalVisible, itemEan.id]);

  // For styling
  const packedQty = sn(iboPre.offer_item_packed_ready_list_sum_qty);
  const iboPreQty = sn(iboPre.case_qty) * sn(iboPre.qty);
  let qtyCls = 'c-grey';
  if (packedQty == iboPreQty) {
    qtyCls = 'c-green';
  } else if (packedQty > iboPreQty) {
    qtyCls = 'c-orange';
  }

  return (
    <ModalForm<FormValueType>
      title={
        <Row gutter={16}>
          <Col>Update Qty Packed Ready</Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.ean || '',
                tooltips: 'Copy EAN ' + (itemEan?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.ean || ''}
            </Typography.Paragraph>
          </Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.sku || '',
                tooltips: 'Copy SKU ' + (itemEan?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.sku || ''}
            </Typography.Paragraph>
          </Col>
          <Col>{`Qty / Case: ${itemEan.attr_case_qty}`}</Col>
          <Col style={{ marginLeft: 'auto', paddingRight: 36 }}>
            <Button
              type="primary"
              danger={!!iboPre?.note_delivered}
              ghost
              onClick={() => {
                setOpenIboPreDeliveryNotesUpdateForm(true);
              }}
            >
              Delivery Notes
            </Button>
          </Col>
        </Row>
      }
      width="900px"
      size="large"
      visible={modalVisible}
      onVisibleChange={(visible) => {
        handleModalVisible(visible);
      }}
      grid
      formRef={formRef}
      className={styles.createOrUpdatePackedReadyQty}
      onFinish={async (value) => {
        return Promise.resolve(true);
      }}
      modalProps={{
        maskClosable: false,
        className: itemEan?.is_single ? 'm-single' : 'm-multi',
        confirmLoading: loadingEan || loadingSave,
      }}
      submitter={{ submitButtonProps: { style: { display: 'none' } } }}
    >
      <Col flex="180px">
        <EanFilesComp files={itemEan.files} width={160} outlineBorder />
        <div className="text-center" style={{ marginTop: 12, marginBottom: 12 }}>
          <SkuComp sku={itemEan.sku} />
        </div>
        <div
          className="text-center cursor-pointer c-blue"
          onClick={() => {
            handleQtyModalVisible(true);
          }}
        >
          <span>Stock: </span>
          <span>{ni(iboPre.item_ean?.parent_stock_stables_sum_total_piece_qty, true)} pcs</span>
        </div>
      </Col>
      <Col flex="auto" style={{ paddingLeft: 24 }}>
        <Spin spinning={loadingEan}>
          <ProFormList
            actionRef={listActionRef}
            key={'uid'}
            name="qty_delivered_list"
            creatorButtonProps={false}
            creatorRecord={{}}
            deleteIconProps={{ tooltipText: 'Remove' }}
            copyIconProps={false}
            alwaysShowItemLabel={false}
            actionRender={(field, action, doms) => []}
            style={{ minHeight: 180 }}
          >
            {(meta, index, action, count) => {
              // console.log('list render', meta, index, action, count);
              const rowData = action.getCurrentRowData();

              return (
                <Row className="test-xxxx" key={meta.key}>
                  <Col span={8}>
                    <ProForm.Item
                      name="ean"
                      label={
                        <Typography.Text ellipsis copyable>
                          {itemEan.ean_text_de?.name}
                        </Typography.Text>
                      }
                    >
                      {rowData.ean}
                    </ProForm.Item>
                  </Col>

                  <Col span={2}>
                    <ProForm.Item name="case_qty" label=" ">
                      {rowData.case_qty}
                    </ProForm.Item>
                  </Col>

                  <Col span={7}>
                    <ProForm.Item name="qty" label="Delivered Qty">
                      <NumpadExtSelector inputProps={{ style: { width: 140 }, inputMode: 'none' }} isMobile />
                    </ProForm.Item>
                  </Col>
                  <Col span={7}>
                    <ProForm.Item name="exp_date" label="Exp. Date">
                      <DateSelector
                        showBodyScroll
                        onChange={function (value: string): void {
                          //
                        }}
                        isMobile
                      />
                    </ProForm.Item>
                  </Col>
                </Row>
              );
            }}
          </ProFormList>
          <Row>
            <Col span={6} className="text-left" style={{ fontSize: 16 }}>
              <span>IboPre Qty: </span>
              <span style={{ fontWeight: 'bold' }}>
                {iboPre.case_qty == 1 ? ni(iboPre.qty) : `${ni(iboPre.qty)} x ${ni(iboPre.case_qty)}`}
              </span>
            </Col>
            <Col span={7} offset={1} style={{ fontSize: 16 }} className={qtyCls}>
              <span>Picked Qty: </span>
              <span style={{ fontWeight: 'bold' }}>{ni(iboPre.offer_item_packed_ready_list_sum_qty, true)} pcs</span>
            </Col>
          </Row>
        </Spin>
      </Col>
      <Col span={24} style={{ paddingTop: 24, paddingBottom: 24 }}>
        <ProTable<API.OfferItem>
          rowKey="id"
          search={false}
          options={{ fullScreen: false, density: false, reload: false, search: false, setting: false }}
          pagination={{
            showSizeChanger: true,
            hideOnSinglePage: true,
            defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
          }}
          cardProps={{ bodyStyle: { padding: 0 } }}
          columns={[
            {
              title: 'Customer Name',
              dataIndex: ['offer', 'quote', 'customer_fullname'],
              width: 200,
            },
            {
              title: 'Offer No',
              dataIndex: ['offer', 'offer_no'],
              width: 100,
            },
            {
              title: 'Qty',
              dataIndex: ['qty'],
              width: 100,
              render(dom, entity) {
                return (
                  <div>
                    <span>{ni(entity.packed_ready_qty_pcs, true)}</span> /{' '}
                    <span>{ni(sn(entity.case_qty) * sn(entity.qty), true)}</span>
                  </div>
                );
              },
            },
            {
              title: '',
              valueType: 'option',
              render(dom, entity) {
                const is_enougth_packed_ready_qty_pcs =
                  sn(entity.packed_ready_qty_pcs) >= sn(entity.case_qty) * sn(entity.qty);

                return (
                  <Space>
                    <Button
                      className={is_enougth_packed_ready_qty_pcs ? '' : 'btn-green'}
                      type={is_enougth_packed_ready_qty_pcs ? 'default' : 'primary'}
                      onClick={() => {
                        const listByRef = listActionRef.current?.getList() || [];
                        const rows: API.OfferItemIboPrePackReadyMap[] = [];

                        for (const row of listByRef) {
                          if (row.qty && row.exp_date) {
                            row.offer_item_id = entity.id;
                            row.ibo_pre_id = iboPre.id;
                            rows.push(row);
                            break;
                          }
                        }

                        if (rows.length < 1) {
                          message.info('Nothing to save! Please fill data correctly.');
                          return;
                        }

                        Modal.confirm({
                          okButtonProps: { size: 'large' },
                          cancelButtonProps: { size: 'large' },
                          icon: <InfoCircleOutlined style={{ fontSize: 52 }} />,
                          width: '80%',
                          title: (
                            <h1>
                              Add to{' '}
                              <span className="text-md c-red">{`#${entity.offer?.offer_no} - ${entity.offer?.quote?.customer_fullname}`}</span>
                              ?
                            </h1>
                          ),
                          content: (
                            <div>
                              <ProTable
                                size="large"
                                columns={[
                                  {
                                    dataIndex: ['item_ean', 'ean_text_de', 'name'],
                                    title: 'Name',
                                    width: 300,
                                    onCell: (entity) => ({
                                      style: { fontSize: 24 },
                                    }),
                                  },
                                  {
                                    dataIndex: ['case_qty'],
                                    title: 'Case Qty',
                                    width: 100,
                                    align: 'center',
                                    render(dom, entity) {
                                      return <span>{entity.case_qty}</span>;
                                    },
                                    onCell: (entity) => ({
                                      style: { fontSize: 24 },
                                    }),
                                  },
                                  {
                                    dataIndex: ['qty'],
                                    title: 'Qty',
                                    width: 100,
                                    render(dom, entity) {
                                      return (
                                        <span>
                                          {ni(entity.qty)} {entity.item_ean?.is_single ? 'pcs' : 'boxes'}
                                        </span>
                                      );
                                    },
                                    onCell: (entity) => ({
                                      style: { fontSize: 24 },
                                    }),
                                  },
                                  {
                                    dataIndex: ['piece_qty'],
                                    title: 'Pcs Qty',
                                    width: 100,
                                    render(dom, entity) {
                                      return <span>{ni(sn(entity.qty) * sn(entity.case_qty))} pcs</span>;
                                    },
                                    onCell: (entity) => ({
                                      style: { fontSize: 24 },
                                    }),
                                  },
                                  {
                                    dataIndex: ['exp_date'],
                                    title: 'Exp. Date',
                                    width: 100,
                                    align: 'center',
                                    render(dom, entity) {
                                      return <span>{Util.dtToDMY(entity.exp_date)}</span>;
                                    },
                                    onCell: (entity) => ({
                                      style: { fontSize: 24 },
                                    }),
                                  },
                                ]}
                                dataSource={rows}
                                search={false}
                                options={false}
                                pagination={{ hideOnSinglePage: true, defaultPageSize: 100 }}
                                bordered
                                locale={{ emptyText: <></> }}
                              />
                            </div>
                          ),
                          onOk: async () => {
                            const hide = message.loading('Adding...', 0);

                            setLoadingSave(true);
                            await addOfferItemIboPrePackReadyMapBulk(rows)
                              .then((success) => {
                                if (success) {
                                  message.success('Added successfully.');
                                  loadIboPreDetail?.(iboPre.id);
                                  setListReloadTick((prev) => prev + 1);

                                  resetFormFields();

                                  onSubmit?.(rows);
                                }
                              })
                              .catch(Util.error)
                              .finally(() => {
                                setLoadingSave(false);
                                hide();
                              });

                            return true;
                          },
                          onCancel: () => {
                            //
                          },
                        });
                      }}
                    >
                      Save
                    </Button>
                  </Space>
                );
              },
            },
          ]}
          dataSource={[...(iboPre.offer_items ?? [])]}
          columnEmptyText={''}
          locale={{ emptyText: <></> }}
        />
      </Col>

      <OfferItemPackedReadyList
        ibo_pre_id={iboPre.id}
        offer_id={offer_id}
        reloadTick={listReloadTick}
        cbDelete={() => loadIboPreDetail?.(iboPre?.id)}
      />

      <UpdateNotesDeliveredForm
        modalVisible={openIboPreDeliveryNotesUpdateForm}
        handleModalVisible={setOpenIboPreDeliveryNotesUpdateForm}
        initialValues={iboPre}
        onSubmit={async (values) => {
          setIboPre?.((prev) => ({ ...prev, note_delivered: values.note_delivered }));
          handleModalVisible(false);
          onSubmit?.([]);
          return Promise.resolve(true);
        }}
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: iboPre?.item_ean?.id,
          item_id: iboPre?.item_ean?.item_id,
          parent_id: iboPre?.item_ean?.parent_id,
          is_single: iboPre?.item_ean?.is_single,
          sku: iboPre?.item_ean?.sku,
          ean: iboPre?.item_ean?.ean,
          ean_text_de: iboPre?.item_ean?.ean_text_de,
          mag_inventory_stocks_sum_quantity: iboPre?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: iboPre?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: iboPre?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          loadIboPreDetail?.(iboPre?.id);
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />
    </ModalForm>
  );
};

export default CreateOrUpdatePackedReadyQty;
