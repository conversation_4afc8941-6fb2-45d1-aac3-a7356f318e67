import { But<PERSON>, message, Drawer, Card, Typography, Row, Col, Space, Popover } from 'antd';
import React, { useState, useRef, useEffect, useMemo, useCallback, CSSProperties } from 'react';
import { <PERSON>er<PERSON>oolbar, PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { nf2, ni, skuToItemId, sn } from '@/util';
import CreateForm from './components/CreateForm';
import { deleteIbo, exportIboListInXls, getIboList, updateIbo, updateIboAll } from '@/services/foodstore-one/IBO/ibo';

import { DEFAULT_PER_PAGE_PAGINATION, EURO, StockStableStatus, StockStableStatusOptionsKv } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormGroup } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import {
  CloseOutlined,
  EditOutlined,
  FileExcelOutlined,
  HighlightOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import SPrices from '@/components/SPrices';
import moment from 'moment';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import type { DefaultOptionType } from 'antd/lib/select';
import { getIBOManagementACList, updateIBOManagement } from '@/services/foodstore-one/IBO/ibo-management';
import EditableCell from '@/components/EditableCell';
import { EanPriceRecordType, updateEanAttributePartial } from '@/services/foodstore-one/Item/ean';
import { getImportACList } from '@/services/foodstore-one/Import/import';
import UpdatePriceAttributeForm from '@/pages/Item/EanList/components/UpdatePriceAttributeForm';
import type { TrademarkChangeCallbackHandlerTypeParamType } from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import ExpDate from '@/pages/Report/Order/components/ExpDate';
import { useLocation, useModel } from 'umi';
import useIbomOptions from '@/hooks/BasicData/useIbomOptions';
import _ from 'lodash';

type BulkStockStatusForm = {
  status?: StockStableStatus;
};

export type IboListProps = {
  refreshTick?: number;
  filterType?: 'light' | 'query';
  route?: any;
};
/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Ibo[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteIbo({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type SearchFormValueType = Partial<API.Ibo>;

const IboList: React.FC<IboListProps> = ({ refreshTick, filterType, route }) => {
  const { appSettings, getDictByCode } = useModel('app-settings');
  const { priceTypes } = appSettings;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.Ibo>();
  const [selectedRowsState, setSelectedRows] = useState<API.Ibo[]>([]);

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [ibomList, setIbomList] = useState<DefaultOptionType[]>([]);
  const [importedXlsList, setImportedXlsList] = useState<DefaultOptionType[]>([]);

  // Update EAN price
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);

  // bulk stock stable status update
  const bulkStockStatusFormRef = useRef<ProFormInstance<BulkStockStatusForm>>();
  const [openBulkStockStatusForm, setOpenBulkStockStatusForm] = useState<boolean>(false);

  // IBO Pre selection modal
  const [openIboSelectionForm, setOpenIboSelectionForm] = useState<boolean>(false);
  const { ibomOptions, searchIbomOptions } = useIbomOptions(undefined);

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
  });

  const onHeaderCell = useCallback((__: any) => {
    const defaultHeaderProps = {
      className: 'text-sm',
      style: {
        paddingTop: 4,
        paddingBottom: 4,
        fontWeight: 'normal',
      },
    };
    return defaultHeaderProps;
  }, []);

  const smColumns: ProColumns<Partial<API.StockStable>>[] = useMemo(
    () => [
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'name'],
        editable: false,
        width: 70,
        align: 'center',
        onHeaderCell,
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 70,
        align: 'center',
        editable: false,
        render: (dom, record) => {
          return <ExpDate date={record.exp_date} />;
        },
        onHeaderCell,
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
        onHeaderCell,
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 70,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
        onHeaderCell,
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 70,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
        onHeaderCell,
      },
      {
        title: 'Status',
        dataIndex: ['status'],
        dataType: 'select',
        width: 90,
        editable: false,
        valueEnum: StockStableStatusOptionsKv,
        onHeaderCell,
        render: (dom, r) => {
          return <span className={r.status != StockStableStatus.STATUS_AVAILABLE_SALE ? 'c-red' : ''}>{dom}</span>;
        },
      },
    ],
    [onHeaderCell],
  );

  const priceColFsOne: ProColumns<EanPriceRecordType> = {
    title: 'FS_ONE',
    dataIndex: ['item_ean', 'ean_prices', 1],
    valueType: 'digit',
    sorter: false,
    align: 'center',
    width: 70,
    hideInSearch: true,
    className: 'cursor-pointer',
    tooltip: 'Click to open Price Update Modal...',
    render: (dom, recordParam) => {
      const record = recordParam.item_ean?.parent;
      if (!record) return null;
      const vat = record.item?.vat?.value || 0;
      const priceSingle =
        Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0)) /
        (record?.attr_case_qty ? record?.attr_case_qty : 1);

      const gfcStyle: CSSProperties = {};

      return (
        <>
          <Row gutter={4}>
            <Col span={24}>
              <SPrices price={priceSingle} vat={vat} style={{ ...gfcStyle }} />
            </Col>
          </Row>
        </>
      );
    },
    onCell(recordParam) {
      const record = recordParam.item_ean?.parent;
      let cls = '';
      const unitPriceSingle = sn(_.get(_.find(record?.ean_prices, { price_type_id: 1 }), 'price', 0), 2);
      const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

      if (!(unitPriceSingle > 0)) cls += ' bg-lightgrey';

      return {
        className: cls,
        onClick() {
          setCurrentRow(recordParam);
          handleUpdatePricesModalVisible(true);
        },
      };
    },
  };

  const priceColMulti: ProColumns<EanPriceRecordType> = {
    title: 'FS_ONE Multi ',
    dataIndex: 'ean_prices_multi',
    valueType: 'digit',
    sorter: false,
    align: 'center',
    width: 70,
    hideInSearch: true,
    tooltip: 'FS_ONE Multi < GFC --> Green',
    render: (dom, recordParam) => {
      const record = recordParam.item_ean;
      if (!record) return null;

      // Get first sibling's unit price
      const sibEan = record.siblings_multi?.[0];
      if (!sibEan) return null;

      const vat = record.item?.vat?.value || 0;
      const caseQty = sn(sibEan?.attr_case_qty ? sibEan?.attr_case_qty : 1);
      const unitPriceSibling = caseQty
        ? sn(sn(_.get(_.find(sibEan?.ean_prices, { price_type_id: 1 }), 'price', 0)) / caseQty, 2)
        : 0;

      return (
        <>
          <Row gutter={4}>
            <Col span={24}>
              <SPrices price={unitPriceSibling} vat={vat} />
            </Col>
          </Row>
          {sn(record.siblings_multi?.length) > 1 && (
            <div
              className="text-sm c-grey"
              style={{ position: 'absolute', top: -5, right: 0 }}
              title={`${record.siblings_multi?.length} multies.`}
            >
              {record.siblings_multi?.length}
            </div>
          )}
        </>
      );
    },
    onCell: (record) => {
      const attr: React.HTMLAttributes<EanPriceRecordType> = {};

      let cls = '';

      // Get first sibling's unit price
      const sibEan = record.siblings_multi?.[0];
      if (!sibEan) return attr;

      const caseQty = sn(sibEan?.attr_case_qty ? sibEan?.attr_case_qty : 1);
      const unitPriceSibling = caseQty
        ? sn(sn(_.get(_.find(sibEan?.ean_prices, { price_type_id: 1 }), 'price', 0)) / caseQty, 2)
        : 0;
      const unitPriceGfc = sn(_.get(_.find(record?.ean_prices, { price_type_id: 2 }), 'price', 0), 2);

      if (!(unitPriceSibling > 0)) cls += ' bg-lightgrey';

      if (unitPriceGfc && unitPriceSibling && unitPriceGfc > unitPriceSibling) {
        cls += ' bg-green3';
      }

      attr.className = cls;
      return attr;
    },
  };

  const columns: ProColumns<API.Ibo>[] = [
    {
      title: 'Location',
      dataIndex: ['warehouse_location', 'name'],
      fixed: 'left',
      sorter: true,
      ellipsis: true,
      width: 75,
    },
    {
      title: 'Item Name',
      dataIndex: ['item_ean', 'item', 'name'],
      fixed: 'left',
      sorter: true,
      ellipsis: true,
      width: 130,
    },
    {
      title: 'EAN Name',
      dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
      sorter: true,
      ellipsis: true,
      width: 270,
      tooltip: 'Orange color indicates the inherited value from its item.',
      render: (dom, record) => {
        const eanName = record?.item_ean?.ean_texts?.[0]?.name;
        return (
          <Typography.Text type={eanName ? undefined : 'warning'}>
            {eanName ?? record?.item_ean?.item?.name ?? <CloseOutlined style={{ color: '#cc2200' }} />}
          </Typography.Text>
        );
      },
    },
    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      key: 'ean',
      sorter: true,
      copyable: true,
      width: 120,
    },
    {
      title: 'Single EAN',
      dataIndex: ['item_ean', 'parent', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 120,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      sorter: true,
      tooltip: 'open EAN in EAN Summary.',
      width: 100,
      render: (dom, record) => {
        return (
          <Typography.Link
            href={`/item/ean-all-summary?sku=${skuToItemId(record.item_ean?.sku)}_`}
            target="_blank"
            copyable
          >
            {record.item_ean?.sku}
          </Typography.Link>
        );
      },
    },
    {
      title: 'Item No (Xls)',
      dataIndex: ['xls_data_ean', 'article_no'],
      tooltip: 'Item No of Single EAN in supplier Xls file data',
      width: 100,
      render(__, record) {
        return record.xls_data_ean?.article_no ? (
          <div style={{ paddingRight: 10 }}>
            {record.xls_data_ean?.article_no}
            {record.xls_data_ean?.article_no != record.product_no && (
              <a
                href="javascript:void"
                className="cursor-pointer"
                title="Take this Item No in Xls"
                style={{ position: 'absolute', top: 0, right: 0 }}
                onClick={async () => {
                  const hide = message.loading("Updating single ean's Item No by this value...", 0);
                  return updateEanAttributePartial({
                    id: record.item_ean?.parent_id,
                    mode: 'updateProductNo',
                    supplier_id: record.supplier_id,
                    product_no: record.xls_data_ean?.article_no,
                    item_id: record.item_ean?.item_id,
                  })
                    .then((res) => {
                      message.destroy();
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                <HighlightOutlined />
              </a>
            )}
          </div>
        ) : null;
      },
      onCell(record) {
        let tdCls = '';
        const articleNoInXls = record.xls_data_ean?.article_no;
        if (articleNoInXls) {
          if (!record.product_no) tdCls = 'bg-green3';
          else if (record.xls_data_ean?.article_no != record.product_no) tdCls = 'bg-light-red';
        }

        return {
          className: tdCls,
        };
      },
    },
    {
      title: 'Item No',
      dataIndex: ['product_no'],
      tooltip: 'ItemNo of Single EAN. Click to edit on empty cell.',
      width: 100,
      sorter: true,
      render(dom, record) {
        const defaultValue = record.product_no;
        return defaultValue ? (
          dom
        ) : (
          <EditableCell
            dataType="text"
            defaultValue={defaultValue}
            style={{ marginRight: 0 }}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && record.item_ean?.parent_id) {
                cancelEdit?.();
                return;
              }
              return updateEanAttributePartial({
                id: record.item_ean?.parent_id,
                mode: 'updateProductNo',
                supplier_id: record.supplier_id,
                product_no: newValue,
                item_id: record.item_ean?.item_id,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {dom}
          </EditableCell>
        );
      },
    },
    {
      title: 'Price (Xls)',
      dataIndex: ['xls_data', 'price'],
      valueType: 'digit',
      sorter: true,
      align: 'right',
      width: 80,
      className: 'relative',
      render(__, record) {
        return record.xls_data?.price ? (
          <div style={{ paddingRight: 10 }}>
            {nf2(record.xls_data?.price)}
            <a
              href="javascript:void"
              className="cursor-pointer"
              title="Take this price"
              style={{ position: 'absolute', top: 0, right: 0 }}
              onClick={() => {
                const hide = message.loading('Update IBO price by this price.');
                updateIbo({ id: record.id, price: record.xls_data?.price })
                  .then((res) => {
                    message.success('Updated Price successfully.');
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              }}
            >
              <HighlightOutlined />
            </a>
          </div>
        ) : null;
      },
      onCell(record) {
        let tdCls = '';
        const priceXls = record.xls_data?.price;
        const lastBp = record.last_bp;
        if (priceXls && lastBp) {
          if (+priceXls > +lastBp) tdCls = 'bg-light-red';
          else if (+priceXls < +lastBp) tdCls = 'bg-green3';
        }

        return {
          className: tdCls,
        };
      },
    },
    {
      title: 'Price',
      dataIndex: 'price',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      width: 100,
      render: (dom, record) => {
        const vat = record?.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={10}>
                <SPrices price={record.price} vat={vat} hideGross />
              </Col>
              <Col span={11}>
                <SPrices price={(record?.price ?? 0) * (record?.item_ean?.attr_case_qty ?? 0)} vat={vat} hideGross />
              </Col>
            </Row>
            <a
              href="javascript:void"
              className="cursor-pointer"
              title="Update EAN prices..."
              style={{ position: 'absolute', top: 0, right: 0 }}
              onClick={() => {
                setCurrentRow({ ...record });
                handleUpdatePricesModalVisible(true);
              }}
            >
              <EditOutlined />
            </a>
          </>
        );
      },
    },
    {
      title: 'Last BP',
      dataIndex: 'last_bp',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      width: 60,
      render: (dom, record) => nf2(record.last_bp),
    },
    {
      title: `GGP / GOGP`,
      dataIndex: ['ggp'],
      width: 200,
      align: 'center',
      className: 'bl2',
      hideInSearch: true,
      tooltip: {
        title: (
          <table className="text-sm">
            <tr>
              <td>GGP (30)</td>
              <td>GOGP (30)</td>
              <td>Qty Single (30)</td>
              <td>Qty Multi (30)</td>
            </tr>
            <tr>
              <td>GGP (365)</td>
              <td>GOGP (365)</td>
              <td>Qty Single (365)</td>
              <td>Qty Multi (365)</td>
            </tr>
          </table>
        ),
        overlayStyle: { width: 400, maxWidth: 400 },
      },
      render(__, entityOrg) {
        const entity = entityOrg.item_ean as EanPriceRecordType;
        if (!entity) return null;
        return (
          <>
            <Row style={{ textAlign: 'right', minHeight: 18, fontSize: 11 }}>
              <Col span={6} title={`${nf2(entity.gp_sum_30, true)} / ${ni(entity.order_count_30, true)}`}>
                {nf2(entity.ggp_avg_30)}
              </Col>
              <Col span={6} title={`${nf2(entity.gogp_sum_30, true)} / ${ni(entity.gogp_order_count_30, true)}`}>
                {nf2(entity.gogp_avg_30)}
              </Col>
              <Col span={6}>{ni(entity.gp_single_qty_ordered_sum_30)}</Col>
              <Col span={6}>{ni(entity.gp_multi_qty_ordered_sum_30)}</Col>
            </Row>
            <Row
              style={{
                textAlign: 'right',
                minHeight: 18,
                borderTop: '1px solid #eee',
                lineHeight: '18px',
                fontSize: 11,
              }}
            >
              <Col span={6} title={`${nf2(entity.gp_sum_365, true)} / ${ni(entity.order_count_365, true)}`}>
                {nf2(entity.ggp_avg_365)}
              </Col>
              <Col
                span={6}
                title={`${nf2(entity.gogp_sum_365, true)} / ${ni(entity.gogp_order_count_365, true)} --> ${
                  entity.gogp_order_ids
                }`}
              >
                {nf2(entity.gogp_avg_365)}
              </Col>
              <Col span={6}>{ni(entity.gp_single_qty_ordered_sum_365)}</Col>
              <Col span={6}>{ni(entity.gp_multi_qty_ordered_sum_365)}</Col>
            </Row>
          </>
        );
      },
    },
    { ...priceColFsOne },
    { ...priceColMulti },
    {
      title: 'Exp. Days',
      dataIndex: ['exp_date2'],
      valueType: 'text',
      align: 'right',
      hideInSearch: true,
      width: 60,
      render: (dom, record) => {
        const daysLeft = record.exp_date ? -moment().diff(moment(record.exp_date), 'days') : 0;
        let cls = '';
        if (daysLeft < 40) cls = 'c-red';
        else if (daysLeft <= 65) cls = 'c-orange';

        return <>{record?.exp_date && <span className={cls}>{daysLeft}</span>}</>;
      },
      onCell(record) {
        const ret = {};
        const xlsLifespan = record.xls_data?.shelf_life;
        const daysLeft = record.exp_date ? -moment().diff(moment(record.exp_date), 'days') : 0;
        if (xlsLifespan && +xlsLifespan > daysLeft) {
          return { className: 'bg-light-red' };
        }

        return ret;
      },
    },
    {
      title: 'XLS',
      dataIndex: ['xls_data', 'shelf_life'],
      sorter: true,
      align: 'right',
      width: 60,
      onCell(record) {
        const ret = {};
        const xlsLifespan = record.xls_data?.shelf_life;
        const daysLeft = record.exp_date ? -moment().diff(moment(record.exp_date), 'days') : 0;
        if (xlsLifespan && +xlsLifespan > daysLeft) {
          return { className: 'bg-light-red' };
        }

        return ret;
      },
    },
    {
      title: 'Pkg. Qty',
      dataIndex: 'box_qty',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      width: 80,
      render: (dom, record) => Util.numberFormat(record.box_qty),
    },
    {
      title: 'Qty',
      dataIndex: 'qty',
      valueType: 'digit',
      showSorterTooltip: false,
      tooltip: 'Qty of Single EAN',
      sorter: true,
      align: 'right',
      width: 60,
      render: (dom, record) => Util.numberFormat(record.qty),
    },
    {
      title: 'Value',
      dataIndex: 'value',
      valueType: 'digit',
      showSorterTooltip: false,
      tooltip: 'Value x Qty',
      sorter: true,
      align: 'right',
      width: 100,
      render: (dom, record) => nf2(sn(record.qty) * sn(record.price)),
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      valueType: 'dateRange',
      sorter: true,
      align: 'right',
      width: 80,
      search: {
        transform: (value: any, namePath: string, allValues: any) => {
          return { exp_date_start: value[0], exp_date_end: value[1] };
        },
      },
      render: (dom, record) => {
        return <>{Util.dtToDMY(record?.exp_date)}</>;
      },
      onCell(record) {
        const ret = {};
        const xlsLifespan = record.xls_data?.shelf_life;
        const daysLeft = record.exp_date ? -moment().diff(moment(record.exp_date), 'days') : 0;
        if (xlsLifespan && +xlsLifespan > daysLeft) {
          return { className: 'bg-light-red' };
        }

        return ret;
      },
    },
    {
      title: 'IBOM',
      dataIndex: ['ibom', 'supplier', 'name'],
      sorter: true,
      ellipsis: true,
      width: 130,
      showSorterTooltip: false,
      tooltip: '#{IBOM Order No} | {Sup. Name} ({Sup. No})',
      render: (dom, record) => (
        <Typography.Text>
          #{record?.ibom?.order_no || '-'} | {record?.ibom?.supplier?.name}{' '}
          {record?.ibom?.supplier?.supplier_no ? `(${record?.ibom?.supplier?.supplier_no})` : ''}
        </Typography.Text>
      ),
    },

    {
      title: 'EAN info',
      hideInSearch: true,
      children: [
        {
          title: 'Weight',
          dataIndex: ['item_ean', 'weight'],
          valueType: 'digit',
          sorter: true,
          hideInSearch: true,
          align: 'right',
          width: 80,
          render: (dom, record) => Util.numberFormat(record?.item_ean?.weight),
        },
        {
          title: 'Width',
          dataIndex: ['item_ean', 'width'],
          valueType: 'digit',
          width: 80,
          align: 'right',
          sorter: true,
          hideInSearch: true,
          render: (dom, record) => Util.numberFormat(record?.item_ean?.width),
        },
        {
          title: 'Height',
          dataIndex: ['item_ean', 'height'],
          key: 'ean_detail.height',
          valueType: 'digit',
          width: 80,
          align: 'right',
          sorter: true,
          hideInSearch: true,
          render: (dom, record) => Util.numberFormat(record?.item_ean?.height),
        },
        {
          title: 'Length',
          dataIndex: ['item_ean', 'length'],
          key: 'ean_detail.length',
          valueType: 'digit',
          width: 80,
          align: 'right',
          sorter: true,
          hideInSearch: true,
          render: (dom, record) => Util.numberFormat(record?.item_ean?.length),
        },
        {
          title: 'Case Qty',
          dataIndex: ['attr_case_qty'],
          valueType: 'digit',
          sorter: true,
          ellipsis: true,
          align: 'right',
          width: 80,
          render: (dom, record) => Util.numberFormat(record?.item_ean?.attr_case_qty),
        },
      ],
    },

    {
      title: 'Created on',
      dataIndex: 'created_on',
      valueType: 'dateRange',
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 100,
      className: 'text-sm c-grey',
      search: {
        transform: (value: any, namePath: string, allValues: any) => {
          return { created_on_start: value[0], created_on_end: value[1] };
        },
      },
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Stocks',
      dataIndex: 'stock_stables',
      width: 500,
      align: 'center',
      render: (dom, record) => {
        if (!record?.stock_stables || !record?.stock_stables?.length) return <></>;
        return (
          <>
            <ProTable
              columns={smColumns}
              cardProps={{ bodyStyle: { padding: '0 0' } }}
              rowKey="id"
              headerTitle={false}
              search={false}
              options={false}
              pagination={false}
              scroll={{ y: 'auto' }}
              dataSource={record?.stock_stables ?? []}
              columnEmptyText={''}
              locale={{ emptyText: <></> }}
              size="small"
            />
          </>
        );
      },
    },
    {
      title: 'Offer',
      dataIndex: ['offers'],
      width: 120,
      render(__, record) {
        return record.expecting_offer_items?.map((x) => {
          return (
            <Row key={x.id}>
              <Col span={14}>Offer #{x.offer_no}</Col>
              <Col span={10} className="text-right">
                {x.qty}
              </Col>
            </Row>
          );
        });
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      align: 'center',
      width: 50,
      fixed: 'right',
      className: 'text-sm c-grey',
      defaultSortOrder: 'descend',
    },

    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 60,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          {[
            <a
              key="edit"
              onClick={() => {
                handleUpdateModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              Edit
            </a>,
            <a key="view" target="_blank" href={`/stock/stock-movement?ibo_id=${record.id}`}>
              History
            </a>,
          ]}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (refreshTick && actionRef.current) {
      actionRef.current.reload();
    }
  }, [refreshTick, actionRef]);

  const loadImportedXlsList = () => {
    const formValues = searchFormRef.current?.getFieldsValue();
    getImportACList({ with: 'validSupplierData', ibom_id: formValues.ibom_id, is_buying_active: 1, pageSize: 50 })
      .then((res) => {
        setImportedXlsList(res);
      })
      .catch(Util.error);
  };

  useEffect(() => {
    // Load XLS data list by initial IBOM ID.
    const formValues = searchFormRef.current?.getFieldsValue();
    getIBOManagementACList({}, {}).then((res) => {
      setIbomList(res);
      searchFormRef.current?.setFieldValue('import_id', '');
      if (formValues.ibom_id) {
        searchFormRef.current?.setFieldValue(
          'import_id',
          res.find((x: API.IBOManagement) => x.id == formValues.ibom_id)?.import_id,
        );
      }
    });

    if (formValues.ibom_id) {
      loadImportedXlsList();
    }
  }, []);

  const buyingHistoryComp = useMemo(() => {
    return currentRow?.id ? (
      <ImportedPrices
        itemEan={{
          item_id: currentRow?.item_ean?.item_id,
          ean: currentRow?.item_ean?.ean,
          parent: currentRow?.item_ean?.parent,
          item: {
            vat: currentRow?.item_ean?.item?.vat,
          },
          ean_prices: currentRow?.item_ean?.ean_prices,
          is_single: currentRow?.item_ean?.is_single,
          latest_ibo: currentRow?.item_ean?.latest_ibo,
          attr_case_qty: currentRow?.item_ean?.attr_case_qty,
        }}
      />
    ) : undefined;
  }, [
    currentRow?.id,
    currentRow?.item_ean?.attr_case_qty,
    currentRow?.item_ean?.ean,
    currentRow?.item_ean?.ean_prices,
    currentRow?.item_ean?.is_single,
    currentRow?.item_ean?.item?.vat,
    currentRow?.item_ean?.item_id,
    currentRow?.item_ean?.latest_ibo,
    currentRow?.item_ean?.parent,
  ]);

  const handleIbomChange = useCallback(
    (value?: any) => {
      const formValues = searchFormRef.current?.getFieldsValue();
      searchFormRef.current?.setFieldValue(
        'import_id',
        ibomList.find((x: any) => x.id == formValues.ibom_id)?.import_id,
      );
      loadImportedXlsList();
      actionRef.current?.reload();
    },
    [ibomList],
  );

  const location: any = useLocation();
  useEffect(() => {
    if (location.query?.BIO_only) {
      searchFormRef.current?.setFieldValue('BIO_only', true);
      actionRef.current?.reload();
    }

    const ibomId = location.query?.ibom_id;
    if (ibomId) {
      searchIbomOptions({ id: ibomId }).then((res) => {
        setIbomList((prev) => {
          if (!prev.find((x) => x.value == ibomId)) {
            return [...res, ...prev];
          } else return prev;
        });
        searchFormRef.current?.setFieldValue('ibom_id', sn(ibomId));
        actionRef.current?.reload();
      });
    }
  }, [location.query, searchIbomOptions]);

  return (
    <PageContainer>
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ibo', {
            ean_search_mode: 'contain_siblings',
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormGroup size="small" rowProps={{ gutter: 0 }}>
            <ProFormSelect
              name={'ibom_id'}
              showSearch
              label="IBOM"
              width={120}
              options={ibomList}
              fieldProps={{
                dropdownMatchSelectWidth: false,
                onChange(value) {
                  handleIbomChange(value);
                },
              }}
            />
            <ProFormSelect
              showSearch
              placeholder="Xls"
              width={'sm'}
              options={importedXlsList}
              fieldProps={{
                dropdownMatchSelectWidth: false,
                onChange(value) {
                  const ibomId = searchFormRef.current?.getFieldValue('ibom_id');
                  updateIBOManagement({ id: ibomId, import_id: value, mode: 'updateImportId' })
                    .then((res) => {
                      message.success('Mapped supplier Xls data successfully.');
                    })
                    .then((res) => {
                      actionRef.current?.reload();
                    })
                    .catch(Util.error);
                },
              }}
              name="import_id"
              label="Xls"
            />
            <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
            <ProFormSelect
              name="producers[]"
              label="Producers"
              placeholder="Please select producers"
              mode="multiple"
              request={getProducerListSelectOptions}
              width={180}
            />
            {formElements}
            <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
            <ProFormSelect
              name={'ean_search_mode'}
              options={[
                {
                  value: 'eq',
                  label: 'Equal',
                },
                {
                  value: 'contain_left',
                  label: 'Contain',
                },
                {
                  value: 'contain_siblings',
                  label: 'Contain & siblings',
                },
              ]}
              width="xs"
              fieldProps={{ dropdownMatchSelectWidth: false }}
            />
            <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          </ProFormGroup>
          <ProFormGroup size="small">
            <ProFormCheckbox
              name="noTrademark"
              label="No Trademark?"
              fieldProps={{ onChange: (value) => actionRef.current?.reload() }}
            />
            <ProFormCheckbox name={'no_price'} label="No Price?" placeholder={'No price?'} />
            <ProFormCheckbox name={'no_item_no'} label="No Item No?" placeholder={'No Item No?'} />
            <ProFormCheckbox name={'BIO_only'} label="BIO Only?" placeholder={'BIO Only?'} />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<API.Ibo, API.PageParams>
        style={{ marginTop: 20 }}
        headerTitle={
          <>
            Item Buying Orders &nbsp;
            <InfoCircleOutlined title='Row Light Red Background --> "BIO" exists in EAN name' />
          </>
        }
        actionRef={actionRef}
        rowKey="id"
        bordered
        revalidateOnFocus={false}
        sticky
        scroll={{ x: '100%' }}
        size="small"
        onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ibo_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => {
          let cls = record.item_ean?.is_single ? 'row-single' : 'row-multi';
          const isBIO = record.item_ean?.ean_texts
            ? record.item_ean?.ean_texts?.findIndex((x) => x.name?.includes('BIO')) >= 0
            : false;
          if (record.item_ean?.item?.name?.includes('BIO') || isBIO) {
            cls += ' reset-tds-bg bg-light-red2';
          }

          return cls;
        }}
        options={{ fullScreen: true }}
        search={false}
        request={(params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ibo', searchValues);
          Util.setSfValues('sf_ibo_p', params);

          const iboParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            trademarks: [searchValues.trademark?.value],
            with: 'supplierAndProductNo,stockStables,expectingOfferItems,itemEan.detail,itemEan.siblingsMulti,itemEan.parent.detail',
          };

          return getIboList(iboParam, { ...newSort }, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedRowsState.map((x) => sn(x.id)),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
        toolBarRender={() => [
          <Button
            key="export-xls"
            type="primary"
            size="small"
            icon={<FileExcelOutlined />}
            onClick={() => {
              const hide = message.loading('Downloading list in Xls...', 0);

              const searchValues = searchFormRef.current?.getFieldsValue();
              Util.setSfValues('sf_ibo', searchValues);

              const iboParam = {
                ...Util.mergeGSearch(searchValues),
                trademarks: [searchValues.trademark?.value],
                with: 'supplierAndProductNo,xlsData',
              };
              exportIboListInXls(iboParam)
                .then((res) => {
                  hide();
                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            Export as Xls
          </Button>,
        ]}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                &nbsp;IBOs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popover
            title="Select status"
            trigger="click"
            open={openBulkStockStatusForm}
            onOpenChange={(visible) => {
              setOpenBulkStockStatusForm(visible);
            }}
            content={
              <ProForm<BulkStockStatusForm>
                formRef={bulkStockStatusFormRef}
                size="small"
                onFinish={async (values) => {
                  const hide = message.loading('Updating...', 0);
                  updateIboAll({
                    mode: 'stockStatus',
                    ibos: selectedRowsState.map((x) => ({ id: x.id })),
                    data: { stockStatus: values.status },
                  })
                    .then((res) => {
                      setOpenBulkStockStatusForm(false);
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .finally(() => {
                      hide();
                    });
                }}
                submitter={{
                  searchConfig: { submitText: 'Update' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name="status"
                  label="Stock Status"
                  valueEnum={StockStableStatusOptionsKv}
                  required
                  rules={[
                    {
                      required: true,
                      message: 'Status is required',
                    },
                  ]}
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenBulkStockStatusForm(true)}
            >
              Update Stock Status
            </Button>
          </Popover>

          <Popover
            title="Move..."
            trigger="click"
            open={openIboSelectionForm}
            onOpenChange={(visible) => {
              setOpenIboSelectionForm(visible);
            }}
            content={
              <ProForm<{ ibom_id?: number }>
                size="small"
                onFinish={async (values) => {
                  if (!values.ibom_id) {
                    message.error('Please select IBO Management!');
                    return;
                  }

                  const hide = message.loading('Moving selected SKUs to the selected IBO Management...', 0);
                  updateIboAll({
                    ibos: selectedRowsState.map((x) => ({
                      id: x.id,
                      ibom_id: sn(values.ibom_id),
                    })),
                  })
                    .then((offer) => {
                      message.success(`Moved to IBO Management #${values.ibom_id} successfully.`);
                      actionRef.current?.reload();
                      setOpenIboSelectionForm(false);
                      setSelectedRows([]);
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Move' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name={'ibom_id'}
                  showSearch
                  label="IBOM"
                  width={200}
                  options={ibomOptions}
                  request={(params) => {
                    return searchIbomOptions({
                      ...params,
                    });
                  }}
                  fieldProps={{
                    dropdownMatchSelectWidth: false,
                  }}
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              title="Move to other IBO Management..."
              onClick={() => setOpenIboSelectionForm(true)}
            >
              Move...
            </Button>
          </Popover>

          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        buyingHistoryComp={buyingHistoryComp}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={{ ...currentRow?.item_ean, ...currentRow?.item_ean?.parent }}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.Ibo>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.Ibo>[]}
          />
        )}
      </Drawer>

      <Drawer
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        width={700}
        open={showImportedPrices}
        className={currentRow?.item_ean?.is_single ? 'm-single' : 'm-multi'}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && (
          <ImportedPrices
            itemEan={{
              item_id: currentRow.item_ean?.item_id,
              ean: currentRow.item_ean?.ean,
              parent: currentRow.item_ean?.parent,
              item: {
                vat: currentRow.item_ean?.item?.vat,
              },
              ean_prices: currentRow.item_ean?.ean_prices,
              is_single: currentRow.item_ean?.is_single,
              latest_ibo: currentRow.item_ean?.latest_ibo,
              attr_case_qty: currentRow.item_ean?.attr_case_qty,
            }}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default IboList;
