F:

cd F:\htdocs\laravel-app

echo "------------------------------"


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Category --table-name=category

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanCategory --table-name=ean_category

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanFile --table-name=ean_file
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanPrice --table-name=ean_price
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanText --table-name=ean_text
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel File --table-name=file
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Ibo --table-name=ibo
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Ibom --table-name=ibom

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Item --table-name=item
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemCategory --table-name=item_category
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemEan --table-name=item_ean
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemSupplierMap --table-name=item_supplier_map
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel PriceType --table-name=price_type
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ProductEntry --table-name=product_entry
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ProductEntryDetail --table-name=product_entry_detail
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Supplier --table-name=supplier

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Trademark --table-name=trademark
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel User --table-name=user
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Vat --table-name=vat
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehouseLocation --table-name=warehouse_location


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel IboDraft --table-name=ibo_draft
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel IboDraftDetail --table-name=ibo_draft_detail
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Ibo --table-name=ibo

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel UsHash --table-name=us_hash

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Import --table-name=import
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportFile --table-name=import_file

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ZImportSupplierMarry --table-name=z_import_supplier_marry

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel StockMovement --table-name=stock_movement

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagInventoryStock --table-name=mag_inventory_stock  --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagStoreConfig --table-name=xmag_store_config  --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagStoreWebsite --table-name=xmag_store_website  --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagProductAttribute --table-name=xmag_product_attribute --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagProductAttributeSet --table-name=xmag_product_attribute_set --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrder --table-name=xmag_order --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderItem --table-name=xmag_order_item --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagDsStat --table-name=xmag_ds_stat --output-path=Models/Magento --namespace=App\Models\Magento

rem @since 2022-10-16
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ScrapPrice --table-name=scrap_price --output-path=Models --namespace=App\Models\ScrapPrice

rem @since 2022-10-24
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanTask --table-name=ean_task --output-path=Models --namespace=App\Models\EanTask


rem @since 2022-11-14
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Item --table-name=item
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemEan --table-name=item_ean

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel StockStable --table-name=stock_stable --output-path=Models --namespace=App\Models\StockStable

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportCurrentBpAggregated --table-name=import_current_bp_aggregated --output-path=Models --namespace=App\Models

rem @since 2022-10-24
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanTask --table-name=ean_task --output-path=Models --namespace=App\Models\EanTask


rem @since 2022-12-13
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagProductFile --table-name=xmag_product_file --output-path=Models/Magento --namespace=App\Models\Magento

rem @since 2022-12-13
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagSyncProcessing --table-name=xmag_sync_processing --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagSyncLog --table-name=xmag_sync_log --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrder --table-name=xmag_order --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderShipmentComment --table-name=xmag_order_shipment_comment --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehousePicklist --table-name=warehouse_picklist --output-path=Models/Warehouse --namespace=App\Models\Warehouse
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehousePicklistDetail --table-name=warehouse_picklist_detail --output-path=Models/Warehouse --namespace=App\Models\Warehouse

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehousePicklistShip --table-name=warehouse_picklist_ship --output-path=Models/Warehouse --namespace=App\Models\Warehouse

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel StockStable --table-name=stock_stable --output-path=Models --namespace=App\Models\StockStable

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel StockStableUsLog --table-name=stock_stable_us_log --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehousePicklistShippingImported --table-name=warehouse_picklist_shipping_imported --output-path=Models/Warehouse --namespace=App\Models\Warehouse

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel StockStableBooked --table-name=stock_stable_booked --output-path=Models --namespace=App\Models\StockStableBooked


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderItemIdx --table-name=xmag_order_item_idx --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel GaFirstUserSourceMedium --table-name=xga_first_user_source_medium --output-path=Models/Google --namespace=App\Models\Google


rem "Email"
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EmailAccount --table-name=email_account --output-path=Models\Email --namespace=App\Models\Email
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Email --table-name=email --output-path=Models\Email --namespace=App\Models\Email
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EmailServer --table-name=email_server --output-path=Models\Email --namespace=App\Models\Email

rem "Sys text module"
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SysTextModule --table-name=sys_text_module --output-path=Models\Sys --namespace=App\Models\Sys

rem "CRM"
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CrmCase --table-name=crm_case --output-path=Models\Crm --namespace=App\Models\Crm
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CrmCaseEmail --table-name=crm_case_email --output-path=Models\Crm --namespace=App\Models\Crm
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CrmCaseNote --table-name=crm_case_note --output-path=Models\Crm --namespace=App\Models\Crm


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportSupplierEan --table-name=import_supplier_ean --output-path=Models\Import --namespace=App\Models\Import

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemEanGdsn --table-name=item_ean_gdsn --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemEanUpdateLog --table-name=item_ean_update_log --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportSearchFilter --table-name=import_search_filter --output-path=Models\Import --namespace=App\Models\Import
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ShippingWarning --table-name=shipping_warning --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel GdsnProvider --table-name=gdsn_provider --output-path=Models\Gdsn --namespace=App\Models\Gdsn
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel GdsnProviderTrademark --table-name=gdsn_provider_trademark --output-path=Models\Gdsn --namespace=App\Models\Gdsn

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagShippingProvider --table-name=xmag_shipping_provider --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagShippingDescProviderMap --table-name=xmag_shipping_desc_provider_map --output-path=Models/Magento --namespace=App\Models\Magento


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrder --table-name=xmag_order --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderExtra --table-name=xmag_order_extra --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OpenapiCallLog --table-name=xopenapi_call_log --output-path=Models/Openai --namespace=App\Models\Openai

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel TrademarkSupplierPriceSetting --table-name=trademark_supplier_price_setting --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderExt --table-name=xmag_order_ext --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel IboPre --table-name=ibo_pre --output-path=Models --namespace=App\Models
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel IboPreManagement --table-name=ibo_pre_management --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportSupplierData --table-name=import_supplier_data --namespace=App\Models\Import --output-path=Models\Import --namespace=App\Models\Import
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportSupplierDataPrice --table-name=import_supplier_data_price --namespace=App\Models\Import --output-path=Models\Import --namespace=App\Models\Import

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemEanPriceHistory --table-name=item_ean_price_history --namespace=App\Models --output-path=Models --namespace=App\Models

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderLabel --table-name=xmag_order_label --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagQuote --table-name=xmag_quote --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagQuoteItem --table-name=xmag_quote_item --output-path=Models/Magento --namespace=App\Models\Magento

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Offer --table-name=offer --output-path=Models/Offer --namespace=App\Models\Offer
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferItem --table-name=offer_item --output-path=Models/Offer --namespace=App\Models\Offer

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehousePicklistTimeTracking --table-name=warehouse_picklist_time_tracking --output-path=Models/Warehouse --namespace=App\Models\Warehouse


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProduct --table-name=mop_product --output-path=Models/MopProduct --namespace=App\Models\MopProduct
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProductFile --table-name=mop_product_file --output-path=Models/MopProduct --namespace=App\Models\MopProduct
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProductText --table-name=mop_product_text --output-path=Models/MopProduct --namespace=App\Models\MopProduct
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProductPrice --table-name=mop_product_price --output-path=Models/MopProduct --namespace=App\Models\MopProduct

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagProduct --table-name=xmag_product --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel TmpImport --table-name=tmp_import --output-path=Models/Import --namespace=App\Models\Import

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferItemIboPreMap --table-name=offer_item_ibo_pre_map --output-path=Models/Offer --namespace=App\Models\Offer

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanPriceStable --table-name=ean_price_stable

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Trademark --table-name=trademark
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel TrademarkGroup --table-name=trademark_group


pause