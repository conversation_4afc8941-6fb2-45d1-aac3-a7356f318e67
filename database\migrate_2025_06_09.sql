ALTER TABLE `ibom`
    DROP FOREIGN KEY `fk_ibom_supplier_id`;

ALTER TABLE `ibom`
    ADD CONSTRAINT `fk_ibom_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON UPDATE CASCADE ON DELETE CASCADE ;


ALTER TABLE `item_supplier_map`
    DROP FOREIGN KEY `fk_ism_item_id`;

ALTER TABLE `item_supplier_map`
    ADD CONSTRAINT `fk_ism_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `item_supplier_map`
    DROP FOREIGN KEY `fk_ism_supplier_id`;

ALTER TABLE `item_supplier_map`
    ADD CONSTRAINT `fk_ism_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;





