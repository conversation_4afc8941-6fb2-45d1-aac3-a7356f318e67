import { getIboDraftDetail } from '@/services/foodstore-one/IBO/ibo-draft-detail';
import { getIbomLabel } from '@/services/foodstore-one/IBO/ibo-management';
import Util, { ni } from '@/util';
import ProDescriptions, { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import { Col, Modal, Row, Spin, Typography } from 'antd';
import { Dispatch, SetStateAction, useCallback, useEffect, useState } from 'react';
import EanSaleStatsTable from './EanSaleStatsTable';
import StockMovementList from './StockMovementList';

type EanIboDetailModalProps = {
  ibomId?: number;
  iboDraftDetail?: API.IboDraftDetail;
  locationId?: number;
  // parentSearchFormRef?: React.MutableRefObject<ProFormInstance<SearchFormValueTypeIboPre> | undefined>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const EanIboDetailModal: React.FC<EanIboDetailModalProps> = ({
  ibomId,
  iboDraftDetail,
  locationId,
  modalVisible,
  handleModalVisible,
}) => {
  const { item_ean } = iboDraftDetail || {};

  const [loading, setLoading] = useState(false);

  const [data, setData] = useState<API.IboDraftDetail>(); // IboDraftDetail entity.
  const ibo = data?.ibo;
  const ibom = data?.ibom;

  const loadIboDetailData = useCallback(() => {
    if (iboDraftDetail?.id) {
      setLoading(true);
      getIboDraftDetail(iboDraftDetail.id, { eqSku: iboDraftDetail?.item_ean?.sku })
        .then((res) => {
          setData(res);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [iboDraftDetail?.id]);

  useEffect(() => {
    if (modalVisible) {
      loadIboDetailData();
    } else {
      setData({});
    }
  }, [loadIboDetailData, modalVisible]);

  const orderColumns: ProDescriptionsItemProps<API.IboDraftDetail>[] = [
    {
      title: 'Delivered',
      dataIndex: ['delivered_pcs'],
      render(__, entity) {
        return ni(entity.delivered_pcs);
      },
    },
    {
      title: 'Warehouse',
      dataIndex: ['stock_pcs'],
      render(__, entity) {
        return ni(entity.item_ean?.stock_mix_qty, true) + (entity?.item_ean?.is_single ? ' pcs' : ' boxes');
      },
    },
    {
      title: 'Sold',
      dataIndex: ['sold_pcs'],
      render(__, entity) {
        return (
          <>
            <span>{ni(entity.sold_pcs, true)} pcs&nbsp;</span>
            <span className="c-gray" title="All sales pcs for this SKU">
              ({ni(entity.sold_pcs_all, true)} pcs)
            </span>
          </>
        );
      },
    },
    {
      title: 'Warehouse Location',
      dataIndex: ['warehouse_location', 'name'],
      render(__, entity) {
        return (
          (entity.warehouse_location?.name ?? '') +
          `${entity.exp_date ? ', ' : ''} ${entity.exp_date ? Util.dtToDMY(entity.exp_date) : ''}`
        );
      },
    },
  ];

  return (
    <>
      <Modal
        title={
          <>
            IBO {ibo?.id ?? 'N/A'} - {ibom ? getIbomLabel(ibom) : null} |{' '}
            <Typography.Text copyable>{item_ean?.sku}</Typography.Text>
          </>
        }
        open={modalVisible}
        onCancel={() => handleModalVisible(false)}
        width="1400px"
        footer={false}
      >
        <Row style={{ marginBottom: 24 }}>
          <Col span="24">
            <Spin spinning={loading}>
              <ProDescriptions<API.IboDraftDetail>
                column={{ sm: 1, md: 1, lg: 2, xl: 4, xxl: 4 }}
                dataSource={data}
                columns={orderColumns}
                bordered
                size="middle"
              />
            </Spin>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span="10">{modalVisible && <EanSaleStatsTable itemEan={item_ean} eqSku={item_ean?.sku} />}</Col>
          <Col span="14">
            {!!ibo?.id && modalVisible && (
              <StockMovementList
                searchParams={{
                  ibo_id: ibo?.id,
                  is_single: item_ean?.is_single,
                  sku: item_ean?.sku,
                }}
              />
            )}
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default EanIboDetailModal;
