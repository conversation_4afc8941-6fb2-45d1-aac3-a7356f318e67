import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message, Select, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import { updateIboPre } from '@/services/foodstore-one/IBO/ibo-pre';
import { useModel } from 'umi';
import { DictType } from '@/constants';
import SkuComp from '@/components/SkuComp';
import ImportEanDisabledList from './ImportEanDisabledList';

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateIboPre(id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.IboPre>;

export type UpdateNotesDeliveredFormProps = {
  initialValues?: Partial<API.IboPre>;
  supplierAdd?: string;
  modalVisible: boolean;
  showDisabledEanList?: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPre) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;

  buyingHistoryComp?: JSX.Element;
};

const UpdateNotesDeliveredForm: React.FC<UpdateNotesDeliveredFormProps> = (props) => {
  const { initialValues, supplierAdd, showDisabledEanList, modalVisible } = props;
  const { item_ean } = initialValues ?? {};

  const { getDictOptions } = useModel('app-settings');
  const formRef = useRef<ProFormInstance>();

  const shortcutTextOptions = getDictOptions(DictType.PreOrderDeliveryNotePrefix);

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldValue('shortcut_option', null);
    }
  }, [modalVisible]);

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, initialValues]);

  return (
    <ModalForm<FormValueType>
      title={
        <Space size={16}>
          <span>Update Pre IBO - Delivery Notes</span>
          <SkuComp sku={item_ean?.sku} />
          <Typography.Text copyable>{item_ean?.ean}</Typography.Text>
        </Space>
      }
      width="600px"
      visible={modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      initialValues={initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      modalProps={{ okText: 'Update' }}
      onFinish={async (value) => {
        const success = await handleUpdate(sn(initialValues?.id), { ...value });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormTextArea
        name="note_delivered"
        label={<span>Delivery Notes</span>}
        fieldProps={{ rows: 7 }}
        formItemProps={{ style: { marginBottom: 0 } }}
        extra={
          <div className="absolute" style={{ top: -48, right: 0 }}>
            <ProFormSelect
              options={shortcutTextOptions}
              style={{ maxWidth: 350 }}
              name="shortcut_option"
              fieldProps={{
                onChange(value, option) {
                  if (value) {
                    let oldValue = formRef.current?.getFieldValue('note_delivered') ?? '';
                    let newValue = value + '\n' + oldValue;
                    formRef.current?.setFieldValue('note_delivered', newValue);
                  }
                },
                dropdownMatchSelectWidth: false,
              }}
              placeholder={'Shortcut Text'}
              allowClear
              showSearch
            />
          </div>
        }
      />
      {showDisabledEanList && (
        <ImportEanDisabledList searchParams={{ ean: item_ean?.ean, supplier_add: supplierAdd }} />
      )}
    </ModalForm>
  );
};

export default UpdateNotesDeliveredForm;
