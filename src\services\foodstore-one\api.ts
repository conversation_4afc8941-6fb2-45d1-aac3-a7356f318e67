// @ts-ignore
/* eslint-disable */
import qs from 'qs';
import { request } from 'umi';

export const paramsSerializer = (paramsTmp: any) => {
  return qs.stringify(paramsTmp, {
    encoder(str, defaultEncoder, charset, type) {
      if (type == 'value' && typeof str == 'boolean') return str ? 1 : '';
      return str;
    },
  });
};

/** user info GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/currentUser', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** There are no annotations provided by the backend here GET /api/notices */
export async function getNotices(options?: { [key: string]: any }) {
  return request<API.NoticeIconList>('/api/notices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** user info GET /api/countries */
export async function getCountries(options?: { [key: string]: any }): Promise<any[]> {
  return request<API.BaseResult>('/api/countries', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

const urlPrefixMagento = '/api/magento';
/** rule GET /api/magento/store/storeConfig */
export async function dsStoreConfig() {
  return request<API.BaseResult>(`${urlPrefixMagento}/store/storeConfig`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** productAttributes GET /api/magento/store/productAttributes */
export async function dsProductAttributes() {
  return request<API.BaseResult>(`${urlPrefixMagento}/store/productAttributes`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** productAttributeSet GET /api/magento/store/productAttributeSet */
export async function dsProductAttributeSet() {
  return request<API.BaseResult>(`${urlPrefixMagento}/store/productAttributeSet`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** orders GET /api/magento/orders/latest */
export async function dsOrders() {
  return request<API.BaseResult>(`${urlPrefixMagento}/orders/latest`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** 
 * Sync quotes partially.
 * 
 * GET /api/magento/quotes/latest */
export async function dsQuotes() {
  return request<API.BaseResult>(`${urlPrefixMagento}/quotes/latest`, {
    method: 'GET',
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}



/** orders GET /api/magento/orders/full */
export async function dsFullOrders() {
  return request<API.BaseResult>(`${urlPrefixMagento}/orders/full`, {
    method: 'GET',
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** 
 * Sync full quotes
 * 
 * GET /api/magento/quotes/full */
export async function dsFullQuotes() {
  return request<API.BaseResult>(`${urlPrefixMagento}/quotes/full`, {
    method: 'GET',
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** orders GET /api/magento/order-comments/full */
export async function dsFullOrderShipmentComments() {
  return request<API.BaseResult>(`${urlPrefixMagento}/order-comments/full`, {
    method: 'GET',
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** store config info GET /api/magento-data/store/storeConfig */
export async function getMagentoStoreConfig(options?: { [key: string]: any }): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/magento-data/store/storeConfig', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** store websites info GET /api/magento-data/store/storeWebsites */
export async function getMagentoStoreWebsites(options?: { [key: string]: any }): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/magento-data/store/storeWebsites', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** store websites info GET /api/app-settings */
export async function getAppSettings(options?: { [key: string]: any }): Promise<API.AppSettings> {
  return request<API.BaseResult>('/api/app-settings', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** post POST /api/magento/stock/${sku}/us/stock-qty */
export async function usUpdateStockQty(sku: string, data?: { [key: string]: any }): Promise<API.Ean> {
  return request<API.AppApiResponse>(`/api/magento/stock/${sku}/us/stock-qty`, {
    method: 'POST',
    data,
  }).then((res) => res.message);
}


/** 
 * Download file
 *  GET /api/download */
export async function downloadFileB64(params?: { b64: boolean, type: string, key: string }) {
  return request<API.ResultObject<{ b64: string }>>('/api/download', {
    method: 'GET',
    params,
  }).then((res) => res.message?.b64);
}
