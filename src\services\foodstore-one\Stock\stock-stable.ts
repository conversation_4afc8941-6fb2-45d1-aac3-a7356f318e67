/* eslint-disable */
import { StockMovementReason } from '@/constants';
import qs from 'qs';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/stock/stock-stable';

/** rule GET /api/stock/stock-stable */
export async function getStockStable(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** rule GET /api/stock/stock-stable/ean/{id} */
export async function getStockByEanId(eanId?: number, options?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/ean/${eanId}`, {
    method: 'GET',
    params: {
      ...options,
    },
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res?.message);
}


/** put PUT /api/stock/stock-stable/updateExpDate */
export async function updateStockStableExpDate(data: API.StockStable, options?: { [key: string]: any }) {
  return request<API.StockStable>(`${urlPrefix}/updateExpDate`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/stock/stock-stable/updateStatus */
export async function updateStockStableStatus(data: API.StockStable, options?: { [key: string]: any }) {
  return request<API.StockStable>(`${urlPrefix}/updateStatus`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/stock/stock-stable/updateStatusBatch */
export async function updateStockStableStatusBatch(data: { eanIds: number[], status: 1 | 0 }, options?: { [key: string]: any }) {
  return request<API.StockStable>(`${urlPrefix}/updateStatusBatch`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}


/**
 * update stockStable data
 *
 * PUT /api/stock/stock-stable/{id}
 */
export async function updateStockStable(id: number, data: Partial<API.StockStable>, options?: { [key: string]: any }) {
  return request<API.StockStable>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/stock/stock-stable/import-ibo */
export async function ImportStockStable(data: API.StockStable, options?: { [key: string]: any }) {
  return request<API.StockStable>(`${urlPrefix}/import-ibo`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/stock/stock-stable */
export async function addStockStable(data: API.StockStable, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}

/** post POST /api/stock/stock-stable/createByReturn */
export async function addStockStableByReturn(data: { order_id?: number, item_ids?: (number | undefined)[] }, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/createByReturn`, {
    method: 'POST',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}



/** delete DELETE /api/stock/stock-stable/{id} */
export async function deleteStockStable(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export type StockStableCorrectionParamType = {
  id?: number;
  ibo_id?: number;  // @since 2023-04-18

  ean_id?: number;
  wl_id?: number;
  exp_date?: string;

  box_qty_edit?: number;
  piece_qty_edit?: number;

  reason: StockMovementReason;
  warehouse_location?: Partial<API.WarehouseLocation>;
};

export type StockStableMoveParamType = {
  ean_id?: number;
  wl_id?: number;
  exp_date?: string;

  old_wl_id?: number;

  box_qty_edit?: number;
  piece_qty_edit?: number;

  ibo_id?: number; // @since 2023-04-18
  id?: number; // stock stable ID
};

/** stock correction PUT /api/stock/stock-stable/correction */
export async function stockStableCorrection(data: StockStableCorrectionParamType, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/correction`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}

/** stock move PUT /api/stock/stock-stable/move */
export async function stockStableMove(data: StockStableMoveParamType, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/move`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}

/** stock move PUT /api/stock/stock-stable/bulk-move */
export async function stockStableBulkMove(rows: API.StockStable[], tmpFile?: API.Downloadable | null, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/bulk-move`, {
    method: 'PUT',
    data: {
      rows: rows,
      tmpFile: tmpFile,
    },
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}

/** 
 * Bulk deduct qty rows. (allow WH user to deduct bulk qtys per Order)
 * 
 * PUT /api/stock/stock-stable/bulk-deduct */
export async function stockStableBulkDeduct(rows: API.StockStable[], orderItem?: Partial<API.OrderItem>, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/bulk-deduct`, {
    method: 'PUT',
    data: {
      rows: rows,
      orderItem,
    },
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * Deduct qty by specific reason
 * 
 * PUT /api/stock/stock-stable/deduct 
 * 
 */
export async function stockStableDeduct(row: API.StockStable, offer_item_id?: number, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/deduct`, {
    method: 'PUT',
    data: {
      row: row,
      offer_item_id
    },
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * stock move 
 * Move selected stocks to location B
 * 
 * PUT 
 * /api/stock/stock-stable/bulk-move-to-target */
export async function stockStableBulkMove2Target(rows: API.StockStable[], new_wl_id: number, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/bulk-move-to-target`, {
    method: 'PUT',
    data: {
      rows: rows,
      new_wl_id,
    },
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}
export async function stockStableBulkMove2TargetByName(rows: API.StockStable[], new_wl_name: string, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/bulk-move-to-target`, {
    method: 'PUT',
    data: {
      rows: rows,
      new_wl_name,
    },
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * all stock move to B from A
 * PUT /api/stock/stock-stable/bulk-move-all */
export async function stockStableBulkMoveAll(old_wl_id: number | string, new_wl_id: number | string, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/bulk-move-all`, {
    method: 'PUT',
    data: {
      old_wl_id,
      new_wl_id,
    },
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}



export async function exportStockStableList(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}


/** rule GET /api/stock/stock-stable/fixWrongIboMapping */
export async function fixWrongIboMapping(eanId?: number, options?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/fixWrongIboMapping`, {
    method: 'GET',
    params: {
      ...options,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res?.message);
}