import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useState } from 'react';

export type NavigationActionType = 'prevModal' | 'nextModal' | 'prev' | 'next';
export type NavigationModelsType = 'item' | 'category' | 'text' | 'price' | 'attribute' | 'picture';

const defaultModals: NavigationModelsType[] = ['item', 'text', 'price', 'attribute', 'picture'];

export type HandleNavFuncType = (
  currentModel?: NavigationModelsType,
  action?: NavigationActionType,
  eanId?: number,
  itemId?: number,
) => void;

export type SetStatesParamType = {
  item?: Dispatch<SetStateAction<boolean>>;
  text?: Dispatch<SetStateAction<boolean>>;
  price?: Dispatch<SetStateAction<boolean>>;
  attribute?: Dispatch<SetStateAction<boolean>>;
  picture?: Dispatch<SetStateAction<boolean>>;
  category?: Dispatch<SetStateAction<boolean>>;
  setCurrentRow: Dispatch<SetStateAction<API.Ean | undefined>>;
  modals?: NavigationModelsType[];
};

export default (dataSource: API.Ean[], dispatchers: SetStatesParamType) => {
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);
  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);

  const handleNavigation: HandleNavFuncType = useCallback(
    (currentModel?: NavigationModelsType, action?: NavigationActionType, eanId?: number, itemId?: number) => {
      if (!dataSource?.length) return;

      const defaultDispatchers = {
        item: handleUpdateItemModalVisible,
        attribute: handleUpdateModalVisible,
        picture: handleUpdatePicturesModalVisible,
        price: handleUpdatePricesModalVisible,
        text: handleUpdateTextsModalVisible,
        category: handleUpdateCategoriesModalVisible,
      };

      const modals = dispatchers?.modals ?? defaultModals;
      if (action == 'nextModal' || action == 'prevModal') {
        const modalInd = modals.findIndex((x) => x == currentModel);
        const nextModalInd = (modalInd + (action == 'nextModal' ? 1 : -1) + modals.length) % modals.length;

        if (dispatchers[modals[modalInd]]) dispatchers[modals[modalInd]]?.(false);
        else defaultDispatchers[modals[modalInd]]?.(false);

        if (dispatchers[modals[nextModalInd]]) dispatchers[modals[nextModalInd]]?.(true);
        else defaultDispatchers[modals[nextModalInd]]?.(true);
      } else {
        const ind = dataSource.findIndex((x) => x.id == eanId);
        if (ind >= 0) {
          const nextInd = (ind + (action == 'next' ? 1 : -1) + dataSource.length) % dataSource.length;
          dispatchers.setCurrentRow(dataSource[nextInd]);
        }
      }
    },
    [dispatchers, dataSource],
  );

  return {
    handleNavigation,
    updateModalVisible,
    handleUpdateModalVisible,
    updatePricesModalVisible,
    handleUpdatePricesModalVisible,
    updateItemModalVisible,
    handleUpdateItemModalVisible,
    updateCategoriesModalVisible,
    handleUpdateCategoriesModalVisible,
    updateTextsModalVisible,
    handleUpdateTextsModalVisible,
    updatePicturesModalVisible,
    handleUpdatePicturesModalVisible,
  };
};
