import type { ButtonProps } from 'antd';
import { Popconfirm, message } from 'antd';
import { Drawer } from 'antd';
import { But<PERSON>, Card, Col, Row, Typography } from 'antd';
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ColumnsState } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import { skuToItemId } from '@/util';
import { ni } from '@/util';
import Util, { nf2, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { exportSalesStatsList, getSalesStatsList } from '@/services/foodstore-one/Report/sales-stat';
import {
  TRADEMARK_FILTER_ALL_ITEM,
  TRADEMARK_FILTER_DEFAULT,
  getTrademarkListSelectOptions,
} from '@/services/foodstore-one/BasicData/trademark';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import type { ResizeCallbackData } from 'react-resizable';
import { Resizable } from 'react-resizable';
import type { ColumnType } from 'antd/lib/table';
import IboDetailModal from './components/IboDetailModal';
import SProFormDateRange from '@/components/SProFormDateRange';
import type { DefaultOptionType } from 'antd/lib/select';
import ExpDate from './components/ExpDate';
import OrderListModal from '@/pages/Magento/Order/components/OrderListModal';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CloudUploadOutlined,
  ExpandOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import ButtonGroup from 'antd/lib/button/button-group';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import EanBadgeIcon from '@/components/EanBadge';
import UpdatePriceAttributeForm from '@/pages/Item/EanList/components/UpdatePriceAttributeForm';
import UpdateTextsForm from '@/pages/Item/EanList/components/UpdateTextsForm';
import { usProductFull } from '@/services/foodstore-one/Item/ean';
import { getSupplierList } from '@/services/foodstore-one/supplier';

// import OrderDetailListModal from './components/OrderDetailListModal';

enum ColumnSection {
  all = '',
  shop = '_s',
  ebay = '_e',
}

const TrademarkNavButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  style: { width: 24, height: 24, fontSize: 14 },
};

export const ResizableTitle = (
  props: React.HTMLAttributes<any> & {
    onResize: (e: React.SyntheticEvent<Element>, data: ResizeCallbackData) => void;
    width: number;
  },
) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

export type SearchFormValueType = {
  // colMode?: 'qty' | 'turnover' | 'gp' | 'cturnover' | 'ebayFee';
  trademark?: DefaultOptionType;
  ean?: string;
  sku?: string;
  ean_type_search?: string;
  order_id?: number;
  sale_types?: ColumnSection[];

  // Date range selector
  start_date?: string;
  end_date?: string;
  dr_selection?: string;
};

export type OrderModalSearchParamsType = SearchFormValueType & {
  dateRange?: DateRangeType;
  source?: string;
  columnSection?: ColumnSection;
} & { filtered_only?: boolean };

type RecordType = API.OrderItem & Record<string, any>;

const ColumnSectionKv = {
  [ColumnSection.all]: 'All Sales',
  [ColumnSection.ebay]: 'EBay',
  [ColumnSection.shop]: 'Shop',
};

const defaultSearchFormValues = {
  lastInterval: 7,
  intervalType: 'd',
  colMode: 'qty',
  sku: '',
  ean: '',
  trademark: { value: TRADEMARK_FILTER_ALL_ITEM, label: `Default Groups` }, // All items as a default.
  sale_types: [ColumnSection.all],
};

const SalesStat: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);

  const [columnSections /*  setColumnSections */] = useState<ColumnSection[]>(() => [
    ColumnSection.all,
    ColumnSection.shop,
    ColumnSection.ebay,
  ]);

  // trademarks dropdown options
  const [trademarks, setTrademarks] = useState<DefaultOptionType[]>([]);

  // table column states
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>(() => {
    return {
      [`landed_pcs${ColumnSection.all}`]: { show: false },
      [`landed_pcs${ColumnSection.shop}`]: { show: false },
      [`landed_pcs${ColumnSection.ebay}`]: { show: false },

      [`ebay_fee_pcs${ColumnSection.all}`]: { show: false },
      [`ebay_fee_pcs${ColumnSection.shop}`]: { show: false },
      [`ebay_fee_pcs${ColumnSection.ebay}`]: { show: false },
      // sys stock related
      [`mix_total_piece_qty`]: { show: false },
      [`ss_mix_bp`]: { show: false },

      // Avg Net Turnover
      [`avg_turnover`]: { show: false },
    };
  });

  const [colStatesChanged, setColStatesChanged] = useState<Record<string, number>>(() => ({
    [`bp_pcs${ColumnSection.all}`]: 0,
    [`bp_pcs${ColumnSection.ebay}`]: 0,
    [`bp_pcs${ColumnSection.shop}`]: 0,
  }));

  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [openIBOModal, setOpenIBOModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RecordType>();

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});
  // EAN Text modal
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);

  const handleResize: any = useCallback(
    (index: number) =>
      (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
        setColumns((prev) => {
          const newColumns = [...prev];
          newColumns[index] = {
            ...newColumns[index],
            width: size.width,
          };
          return newColumns;
        });
      },
    [],
  );

  // Check if "Default Group" in Trademark filter is set
  const isGroupByTrademark = () => {
    const sfValues = searchFormRef.current?.getFieldsValue() || {};
    return !sfValues.trademark?.value || sfValues.trademark?.value == TRADEMARK_FILTER_DEFAULT;
  };

  /**
   * Handler to open the orders list modal.
   *
   * @param columnField
   * @param intervalType
   * @param dateRange
   * @param options
   */
  const handleQuantityOnClick = useCallback(
    (columnField: string, columnSection: ColumnSection, options?: Record<string, any>) => {
      const sfValues = searchFormRef.current?.getFieldsValue() || {};
      const params: OrderModalSearchParamsType = {
        source: 'salesReport',
        dateRange: {
          from: sfValues.start_date,
          to: sfValues.end_date,
        },
        ean_type_search: sfValues.ean_type_search,
        filtered_only: true,
        order_id: sfValues.order_id,
        ...options,
      };
      if (isGroupByTrademark()) {
        params.ean = sfValues.ean;
        params.sku = sfValues.sku;
      }
      setModalSearchParams(params);
      setOpenOrderListModal(true);
    },
    [],
  );

  // reload
  useEffect(() => {
    getTrademarkListSelectOptions({}, 1)
      .then((res) => setTrademarks(res))
      .catch(Util.error);
  }, []);

  // show/hide columns by sale types change: All/Shop/EBay
  const handleSaleTypesChange = () => {
    // Get search form values.
    const sfValues = searchFormRef.current?.getFieldsValue() || {};
    const colGroups: Record<string, ColumnsState> = {
      [`colGroup${ColumnSection.all}`]: { show: false },
      [`colGroup${ColumnSection.ebay}`]: { show: false },
      [`colGroup${ColumnSection.shop}`]: { show: false },
    };
    sfValues.sale_types?.forEach((x) => {
      colGroups[`colGroup${x}`] = { show: true };
    });

    setColStates((prev) => ({ ...prev, ...colGroups }));

    Util.setSfValues('sf_order_sales_stat', sfValues);
  };

  const isGroupMode = isGroupByTrademark();

  const handleThFirstLevelClick = (col: 'mix_qty' | 'avg_turnover') => {
    if (col == 'mix_qty') {
      setColStates((prev: any) => ({
        ...prev,
        [`mix_total_piece_qty`]: { show: !prev.mix_total_piece_qty?.show },
        [`ss_mix_bp`]: { show: !prev.ss_mix_bp?.show },
      }));
    } else if (col == 'avg_turnover') {
      setColStates((prev: any) => ({
        ...prev,
        [`avg_turnover`]: { show: !prev.avg_turnover?.show },
      }));
    }
    /* setColStatesChanged((prev: any) => ({
      ...prev,
      [`bp_pcs${section}`]: sn(prev[`bp_pcs${section}`]) + 1,
    }));
    if (colStatesChanged[`bp_pcs${section}`] == 0) {
      actionRef.current?.reload?.();
    } */
  };

  const defaultColumns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        dataIndex: ['uid'],
        align: 'center',
        width: 30,
        fixed: 'left',
        hideInTable: isGroupMode,
        render: (dom, record) => {
          return record.uid == 'total' || !record.item_ean?.id ? null : (
            <Popconfirm
              key="upsync"
              placement="topRight"
              title={
                <>
                  Are you sure you want to up sync the EAN？ <br />
                  <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
                </>
              }
              overlayStyle={{ width: 350 }}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                if (!record.item_ean?.id) return;
                const hide = message.loading(`Up syncing ...`, 0);
                usProductFull(record.item_ean?.id)
                  .then((res) => {
                    if (res.sku) {
                      message.success('Successfully up synced on shop!');
                    } else {
                      message.error(res.upSyncMessage || 'Failed to up sync EAN!');
                    }
                  })
                  .catch((e) => {
                    message.error(e.message ?? 'Failed to upsync!');
                  })
                  .finally(() => {
                    hide();
                  });
              }}
            >
              <CloudUploadOutlined className="btn-gray" />
            </Popconfirm>
          );
        },
      },
      {
        title: 'SKU',
        dataIndex: ['sku'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 80,
        copyable: true,
        defaultSortOrder: 'descend',
        fixed: 'left',
        hideInTable: isGroupMode,
        render: (dom, record) => {
          return record.uid == 'total' ? null : (
            <Typography.Link href={`/item/ean-all-summary?sku=${skuToItemId(record.sku)}_`} target="_blank" copyable>
              {record.sku}
            </Typography.Link>
          );
        },
      },
      {
        title: 'Trademark',
        dataIndex: ['trademark_name'],
        sorter: true,
        align: 'left',
        width: 120,
        fixed: 'left',
        hideInTable: !isGroupMode,
      },
      {
        title: 'Item No',
        dataIndex: ['item_no'],
        sorter: true,
        align: 'left',
        width: 90,
        fixed: 'left',
        hideInTable: isGroupMode,
      },
      {
        title: 'EAN',
        dataIndex: ['ean'],
        sorter: false,
        align: 'left',
        className: 'p-0',
        width: 45,
        hideInTable: isGroupMode,
        render: (dom, record, index) =>
          record.item_ean?.ean ? (
            <>
              <Typography.Text
                copyable={{
                  text: record.item_ean?.ean,
                  tooltips: record.item_ean?.ean
                    ? `Copy. ${record.item_ean ? (record.item_ean.is_single ? 'Single ' : '') : ''}EAN: ${
                        record.item_ean?.ean
                      }`
                    : undefined,
                }}
              />
              {!record.item_ean.is_single && (
                <Typography.Text
                  copyable={{
                    text: record.item_ean?.parent?.ean,
                    tooltips: record.item_ean?.parent?.ean
                      ? `Copy single EAN: ${record.item_ean?.parent?.ean}`
                      : undefined,
                  }}
                />
              )}
            </>
          ) : null,
      },
      {
        title: 'Name',
        dataIndex: ['item_ean', 'ean_text_de', 'internal_short_name'],
        align: 'left',
        ellipsis: false,
        width: 250,
        tooltip: 'Internal Short Name or Full Name. Click to edit.',
        hideInTable: isGroupMode,
        onHeaderCell: (column: ColumnType<RecordType>) => ({
          width: column.width,
          onResize: handleResize((column as any).index) as React.ReactEventHandler<any>,
        }),
        render: (dom, record, index) => {
          const shortName = record.item_ean?.ean_text_de?.internal_short_name;
          const name = shortName ? shortName : record.item_ean?.ean_text_de?.name ?? record.name;

          return (
            <Typography.Text
              title={shortName ? record.item_ean?.ean_text_de?.name ?? record.name : ''}
              ellipsis={{ tooltip: name }}
            >
              {name}
            </Typography.Text>
          );
        },
        onCell: (record) => {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleUpdateTextsModalVisible(true);
            },
          };
        },
      },
      /* {
        title: 'Name',
        dataIndex: ['name'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 250,
        hideInTable: isGroupByTrademark,
        onHeaderCell: (column: ColumnType<RecordType>) => ({
          width: column.width,
          onResize: handleResize((column as any).index) as React.ReactEventHandler<any>,
        }),
        render: (dom, record, index) => record.item_ean?.ean_text_de?.name ?? record.name,
      }, */
      {
        dataIndex: ['item_ean', 'fs_special_badge'],
        sorter: false,
        width: 30,
        align: 'center',
        className: 'p-0 cursor-pointer',
        hideInTable: isGroupMode,
        tooltip: (
          <>
            Special Info: <br />
            discount | badge | title prefix
          </>
        ),
        render: (dom, record) =>
          record.uid !== 'total' && (
            <EanBadgeIcon
              itemEan={{
                fs_special_discount: record.item_ean?.fs_special_discount,
                fs_special_badge: record.item_ean?.fs_special_badge,
                fs_special_badge2: record.item_ean?.fs_special_badge2,
              }}
            />
          ),
        onCell: (record: RecordType) => {
          if (record.uid !== 'total') {
            return {
              onClick: () => {
                setCurrentRow({ ...record });
                handleUpdatePricesModalVisible(true);
              },
              className: 'cursor-pointer',
            };
          } else return {};
        },
      },
      {
        title: 'Range',
        dataIndex: ['qty_range'],
        sorter: true,
        align: 'right',
        width: 60,
        hideInTable: isGroupMode,
        render: (dom, record) => {
          return ni(record.qty_range);
        },
      },
      {
        title: 'Shortest Exp',
        dataIndex: ['mix_min_exp_date'],
        sorter: true,
        align: 'center',
        ellipsis: false,
        width: 80,
        render: (dom, record) => <ExpDate date={record?.mix_min_exp_date} />,
      },
      /* {
        title: 'AVG BP',
        dataIndex: 'ibo_avg_bp',
        sorter: true,
        align: 'right',
        width: 80,
        className: 'br2 b-grey',
        render: (dom, record) => {
          return nf2(record.ibo_avg_bp * record.attr_case_qty);
        },
        onCell: (record) => {
          if (record.item_ean) {
            return {
              className: 'cursor-pointer',
              onClick: (e) => {
                setCurrentRow(record);
                setOpenIBOModal(true);
              },
            };
          }
          return {};
        },
      }, */
      {
        title: (
          <>
            <div>AVG Net/Net Turnover</div>
            <Button
              type="link"
              size="small"
              style={{ position: 'absolute', right: -4, top: -4, zIndex: 50 }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleThFirstLevelClick(`avg_turnover`);
              }}
              title="Show / Hide detail"
            >
              <ExpandOutlined className="text-xs" />
            </Button>
          </>
        ),
        dataIndex: 'avg_cturnover',
        sorter: false,
        align: 'right',
        width: 90,
        className: 'br2 b-grey',
        showSorterTooltip: false,
        tooltip: 'Please click to see details.',
        render: (dom, record) => {
          return nf2(record.cturnover / record.qty);
        },
        onCell: (record) => {
          if (record.qty && record.sku) {
            return {
              className: 'cursor-pointer',
              onClick: () => {
                const options: any = {};
                if (isGroupMode) {
                  options.trademark = record.trademark_id ?? null;
                  options.trademark_name = record.trademark_name;
                } else {
                  options.sku = record.sku;
                }

                handleQuantityOnClick('avg_cturnover', ColumnSection.all, options);
              },
            };
          } else return {};
        },
      },
      {
        title: 'AVG Net Turnover',
        dataIndex: 'avg_turnover',
        sorter: false,
        align: 'right',
        width: 90,
        className: 'br2 b-grey c-grey',
        render: (dom, record) => {
          return nf2(record.turnover / record.qty);
        },
      },
      /* {
        title: 'Sys Stock',
        dataIndex: ['mix_qty'],
        sorter: true,
        align: 'right',
        width: 80,
        tooltip: 'Please click to view stock details.',
        showSorterTooltip: false,
        className: 'cursor-pointer',
        render: (dom, record) => {
          return Util.numberFormat(record.mix_qty);
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: () => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Magento Stock',
        dataIndex: ['mag_qty'],
        valueType: 'digit',
        sorter: true,
        align: 'right',
        width: 80,
        tooltip: 'Magento Stock. Please down sync to get the latest stock info.',
        showSorterTooltip: false,
        className: 'br2 b-black',
        render: (dom, record) => {
          return Util.numberFormat(record.mag_qty);
        },
      }, */
      // New design
      {
        title: 'BP (pcs)',
        dataIndex: ['ss_mix_bp_avg'],
        sorter: true,
        align: 'right',
        width: 70,
        showSorterTooltip: false,
        tooltip: <>Click to view buying history (in item view mode)...</>,
        render: (dom, record) => (record.total_piece_qty ? nf2(record.ss_bp / record.total_piece_qty) : null),
        onCell: (record) => {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              if (!isGroupMode) {
                setCurrentRow(record);
                setShowImportedPrices(true);
              }
            },
          };
        },
      },
      {
        title: (
          <>
            <div>Sys Stock</div>
            <Button
              type="link"
              size="small"
              style={{ position: 'absolute', right: -4, top: -4, zIndex: 50 }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleThFirstLevelClick(`mix_qty`);
              }}
              title="Show / Hide detail"
            >
              <ExpandOutlined className="text-xs" />
            </Button>
          </>
        ),
        dataIndex: ['mix_qty'],
        sorter: false,
        align: 'right',
        width: 80,
        tooltip: (
          <>
            <ul style={{ listStyle: 'circle' }}>
              <li>Please click to view stock details.</li>
              <li>Figures in italic is boxes.</li>
              <li>Total is pcs.</li>
              <li>
                <span color="c-red">Red</span> means Sys Stock != Magento. Stock.
              </li>
            </ul>
          </>
        ),
        showSorterTooltip: false,
        render: (dom, record) => {
          // Check stock difference between Sys & Magento
          let cls = '';
          if (sn(record.mix_qty) - sn(record.mag_res_calc) != sn(record.mag_qty)) {
            cls = 'c-red';
          }
          if (record.uid != 'total' && !record.is_single) {
            cls += ' italic';
          }
          return (
            <span className={cls}>{Util.numberFormat(record.is_single ? record.total_piece_qty : record.box_qty)}</span>
          );
        },
        onCell: (record: RecordType) => {
          if (record.uid !== 'total') {
            return {
              onClick: () => {
                setCurrentRow({ ...record });
                handleQtyModalVisible(true);
              },
              className: 'cursor-pointer',
            };
          } else return {};
        },
      },
      {
        title: 'Qty (pcs)',
        dataIndex: ['mix_total_piece_qty'],
        sorter: true,
        align: 'right',
        width: 70,
        showSorterTooltip: false,
        render: (dom, record) => ni(record.total_piece_qty),
      },
      {
        title: 'BP * Qty (pcs)',
        dataIndex: ['ss_mix_bp'],
        sorter: true,
        align: 'right',
        width: 80,
        showSorterTooltip: false,
        render: (dom, record) => nf2(record.ss_bp),
      },
      {
        title: 'Magento Stock',
        dataIndex: ['mag_qty'],
        valueType: 'digit',
        sorter: true,
        align: 'right',
        width: 100,
        tooltip: (
          <div className="text-sm">
            Please down sync to get the latest stock info.
            <br />
            Grey color indicates a qty in order processing.
          </div>
        ),
        // 'Magento Stock. Please down sync to get the latest stock info. \n\nGrey color indicates a reserved stock.',
        showSorterTooltip: false,
        // className: 'br2 b-black',
        render: (dom, record) => {
          // Check stock difference between Sys & Magento
          let cls = '';
          if (sn(record.mix_qty) - sn(record.mag_res_calc) != sn(record.mag_qty)) {
            cls = 'c-red';
          }

          return (
            <>
              <Row gutter={8}>
                <Col flex={'60px'} className={cls}>
                  {ni(sn(record.mag_qty) - sn(record.mag_res_qty))}
                </Col>
                <Col flex="auto" className="italic c-blue text-center">
                  {/* {ni(sn(record.mag_qty) - sn(record.mag_res_qty))}{' '} */}
                  {/* {sn(record.mag_res_qty) ? `${ni(record.mag_res_qty)}` : ''} */}
                  {ni(record.qty_processing)}
                </Col>
              </Row>
            </>
          );
        },
      },
    ],
    [handleQuantityOnClick, handleResize, isGroupMode],
  );

  const buildColumns = useCallback(() => {
    const handleThClick = (col: 'bp_pcs', section: ColumnSection) => {
      setColStates((prev: any) => ({
        ...prev,
        [`landed_pcs${section}`]: { show: !prev[`landed_pcs${section}`]?.show },
        [`ebay_fee_pcs${section}`]: { show: !prev[`ebay_fee_pcs${section}`]?.show },
      }));
      setColStatesChanged((prev: any) => ({
        ...prev,
        [`bp_pcs${section}`]: sn(prev[`bp_pcs${section}`]) + 1,
      }));
      if (colStatesChanged[`bp_pcs${section}`] == 0) {
        actionRef.current?.reload?.();
      }
    };

    const getColumnDef = (section: ColumnSection, sectionIndex: number): ProColumns<RecordType> => {
      const groupColumnTitle = ColumnSectionKv[section];

      const ind = section;

      const onCellHandler = (record: any, index?: number) =>
        record.uid !== 'total'
          ? {
              onClick: (e: any) => {
                const options: any = {
                  order_type: section,
                };
                if (isGroupMode) {
                  options.trademark = record.trademark_id ?? null;
                  options.trademark_name = record.trademark_name;
                } else {
                  options.sku = record.sku;
                }
                handleQuantityOnClick('', section, options);
              },
            }
          : {};

      const defaultAttr: Partial<ProColumns<RecordType>> = {
        align: 'right',
        width: 70,
        sorter: false,
        ellipsis: true,
      };

      const newCol: ProColumns<RecordType> = {
        title: groupColumnTitle,
        dataIndex: [`colGroup${ind}`],
        align: 'center',
        ellipsis: true,
        className: sectionIndex >= 0 ? 'bl2' : '',
        children: [
          {
            title: 'Qty | Qty (pcs)',
            dataIndex: `qty${ind}`,
            ...defaultAttr,
            width: 100,
            className: defaultAttr.className + ' cursor-pointer' + (sectionIndex >= 0 ? ' bl2' : ''),
            onCell: onCellHandler,
            render: (dom, record) => (
              <Row>
                <Col span="12">{ni(record[`qty${ind}`])}</Col>
                <Col span="12">{ni(record[`qty_pcs${ind}`])}</Col>
              </Row>
            ),
          },
          /* 
          // currently disable.
          {
            title: 'Order Turnover',
            dataIndex: `turnover${ind}`,
            ...defaultAttr,
            tooltip: 'Turnover in Magento Order. Gross Total including shipping fee & vat',
            render: (dom, record) => nf2(record[`turnover${ind}`]),
          }, */
          {
            title: 'Net Turnover',
            dataIndex: `cturnover${ind}`,
            ...defaultAttr,
            render: (dom, record) => nf2(record[`cturnover${ind}`]),
          },
          {
            title: 'ADV',
            dataIndex: `adv${ind}`,
            ...defaultAttr,
            render: (dom, record) => nf2(record[`adv${ind}`]),
            hideInTable: true,
          },
          {
            title: 'ebay Fee',
            dataIndex: `ebay_fee${ind}`,
            ...defaultAttr,
            hideInTable: ind == ColumnSection.shop,
            tooltip: 'eBay Fee in Magento Order. Gross Total including shipping fee & vat',
            render: (dom, record) => nf2(record[`ebay_fee${ind}`]),
          },
          {
            title: 'GP',
            dataIndex: `gp${ind}`,
            ...defaultAttr,
            render: (dom, record) => nf2(record[`gp${ind}`]),
          },
          {
            title: 'VK / pcs',
            dataIndex: `vk_pcs${ind}`,
            ...defaultAttr,
            render: (dom, record) => (
              <span
                title={`Net: Gross: €${nf2(record[`vk_pcs${ind}`], true)}, Gross: €${nf2(
                  record[`vk_pcs_gross${ind}`],
                  true,
                )}`}
              >
                {nf2(record[`vk_pcs${ind}`])}
              </span>
            ),
          },
          {
            title: (
              <>
                <div>BP/pcs</div>
                <Button
                  type="link"
                  size="small"
                  style={{ position: 'absolute', right: -12, top: -12, zIndex: 50 }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleThClick(`bp_pcs`, ind);
                  }}
                  title="Show / Hide detail"
                >
                  <ExpandOutlined className="text-xs" />
                </Button>
              </>
            ),
            dataIndex: `bp_pcs${ind}`,
            ...defaultAttr,
            ellipsis: false,
            tooltip: (
              <>
                Landed / pcs + Ebay fee / pcs + Adv / pcs <br />
                <br />
                Click to view buying history (in item view mode)...
              </>
            ),
            render: (dom, record) => nf2(record[`bp_pcs${ind}`]),
            onCell: (record) => {
              return record.item_ean
                ? {
                    className: 'cursor-pointer',
                    onClick: () => {
                      if (!isGroupMode) {
                        setCurrentRow(record);
                        setShowImportedPrices(true);
                      }
                    },
                  }
                : {};
            },
          },
          {
            title: 'Landed / pcs',
            dataIndex: `landed_pcs${ind}`,
            ...defaultAttr,
            className: defaultAttr.className + ' text-xs',
            render: (dom, record) => nf2(record[`landed_pcs${ind}`]),
          },
          {
            title: 'Ebay fee / pcs',
            dataIndex: `ebay_fee_pcs${ind}`,
            ...defaultAttr,
            className: defaultAttr.className + ' text-xs',
            hideInTable: ind == ColumnSection.shop,
            render: (dom, record) => nf2(record[`ebay_fee_pcs${ind}`]),
          },
          {
            title: 'ADV / pcs',
            dataIndex: `adv_pcs${ind}`,
            ...defaultAttr,
            className: defaultAttr.className + ' text-xs',
            hideInTable: true,
            render: (dom, record) => nf2(record[`adv_pcs${ind}`]),
          },
          {
            title: 'GP / pcs',
            dataIndex: `gp_pcs${ind}`,
            ...defaultAttr,
            render: (dom, record) => nf2(record[`gp_pcs${ind}`]),
          },
        ],
      };

      return newCol;
    };

    const newCols = [];
    for (let ind = 0; ind < columnSections.length; ind++) {
      newCols.push(getColumnDef(columnSections[ind], ind));
    }

    const arr = [...defaultColumns];
    setColumns([...arr, ...newCols]);
  }, [colStatesChanged, columnSections, defaultColumns, handleQuantityOnClick, isGroupMode]);

  /**
   * Prev / Next navigation in Trademark filter
   * @param mode
   */
  const handleTrademarkNavigation = (mode: 'prev' | 'next') => {
    setLoading(true);
    const curTrademarkObj = searchFormRef.current?.getFieldValue('trademark');
    const curTrademarkId = curTrademarkObj.value ?? TRADEMARK_FILTER_DEFAULT;
    const index = trademarks.findIndex((x) => x.value === curTrademarkId) ?? 0;
    const dir: number = mode == 'prev' ? -1 : 1;
    let nextIndex = (index + dir + trademarks.length) % trademarks.length;
    if (trademarks[nextIndex]?.value == 'sep') {
      nextIndex = (nextIndex + dir + trademarks.length) % trademarks.length;
    }
    searchFormRef.current?.setFieldValue('trademark', trademarks[nextIndex]);
    if (isGroupByTrademark()) {
      buildColumns();
    }
    actionRef.current?.reload();
  };

  useEffect(() => {
    handleSaleTypesChange();
    buildColumns();
  }, [buildColumns]);

  return (
    <PageContainer
      extra={
        <Button
          key="exportXls"
          type="primary"
          size="small"
          icon={<FileExcelOutlined />}
          onClick={() => {
            const hide = message.loading('Downloading list in Xls...', 0);
            exportSalesStatsList({ listMode: 'detail' }, {}, {})
              .then((res) => {
                hide();
                if (res.url) {
                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                }
              })
              .catch(Util.error)
              .finally(() => {
                hide();
              });
          }}
        >
          Export as Xls
        </Button>
      }
    >
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_order_sales_stat', defaultSearchFormValues)}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue(defaultSearchFormValues as any);
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name="trademark"
            label="Trademark"
            placeholder="Please select trademark"
            mode="single"
            showSearch
            options={trademarks}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              maxTagCount: 1,
              labelInValue: true,
              onChange: () => {
                if (isGroupByTrademark()) {
                  buildColumns();
                }
                actionRef.current?.reload();
              },
            }}
            width={180}
            disabled={loading}
          />
          <ButtonGroup>
            <Button
              {...TrademarkNavButtonProps}
              onClick={() => handleTrademarkNavigation('prev')}
              icon={<ArrowLeftOutlined />}
              disabled={loading}
            />
            <Button
              {...TrademarkNavButtonProps}
              onClick={() => handleTrademarkNavigation('next')}
              icon={<ArrowRightOutlined />}
              disabled={loading}
            />
          </ButtonGroup>

          <SProFormDateRange label="Date" formRef={searchFormRef} style={{ marginLeft: 16 }} disabled={loading} />
          <ProFormGroup size="small">
            <ProFormSelect
              name="ean_type_search"
              placeholder="Select type"
              label="Type"
              options={[
                { value: '', label: 'All' },
                { value: 'base', label: 'Single' },
                { value: 've', label: 'Multi' },
              ]}
              width={70}
              disabled={loading}
            />
            <ProFormText name={'sku'} label="SKU" width={85} placeholder={'SKU'} disabled={loading} />
            <ProFormText name={'order_id'} label="Order ID" width={70} placeholder={'Order ID'} disabled={loading} />
            <ProFormCheckbox.Group
              name={'sale_types'}
              options={[
                { value: ColumnSection.all, label: 'All' },
                { value: ColumnSection.shop, label: 'Shop' },
                { value: ColumnSection.ebay, label: 'EBay' },
              ]}
              fieldProps={{ onChange: () => handleSaleTypesChange() }}
            />
            <ProFormSelect
              name={'supplier_id'}
              width={100}
              placeholder={'Supplier'}
              disabled={loading}
              tooltip="Supplier to pick up Item No"
              request={(params) =>
                getSupplierList({ pageSize: 200 }, {}, {}).then((res) =>
                  res.data.map((x) => ({
                    value: x.id,
                    label: x.name,
                  })),
                )
              }
            />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<RecordType, API.PageParams>
        headerTitle={'Sales Statistics'}
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        bordered
        scroll={{ x: 800 }}
        // scroll={{ x: 'max-content' }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_order_sales_stat', searchFormValues);
          setLoading(true);
          return getSalesStatsList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademark: searchFormValues.trademark?.value,
              columnSections,
            },
            sort,
            filter,
          )
            .then((res) => {
              const totalRow: RecordType = {
                uid: 'total',
                trademark_name: 'Total',
                qty_ordered: 0,
                mix_qty: 0,
                mag_qty: 0,
                avg_turnover: 0,
                avg_cturnover: 0,
                vk_pcs: 0,
                gp_pcs: 0,
                landed: 0,
                landed_pcs: 0,

                // sys stock bp related
                ss_bp: 0,
                ss_mix_bp: 0,
                mix_total_piece_qty: 0,
              };

              // initializing
              for (const suffix of columnSections) {
                totalRow[`qty${suffix}`] = 0;
                totalRow[`qty_pcs${suffix}`] = 0;
                totalRow[`mag_qty${suffix}`] = 0;
                totalRow[`gp${suffix}`] = 0;
                totalRow[`turnover${suffix}`] = 0;
                totalRow[`cturnover${suffix}`] = 0;
                totalRow[`ebay_fee${suffix}`] = 0;
                totalRow[`landed${suffix}`] = 0;
                totalRow[`bp${suffix}`] = 0;

                res.data.forEach((row: RecordType) => {
                  const qty = sn(row[`qty${suffix}`]);
                  if (!qty) return;
                  totalRow[`qty${suffix}`] += qty;
                  totalRow[`qty_pcs${suffix}`] += sn(row[`qty_pcs${suffix}`]);
                  totalRow[`mag_qty${suffix}`] += sn(row[`mag_qty${suffix}`]);
                  totalRow[`gp${suffix}`] += sn(row[`gp${suffix}`], 2);
                  totalRow[`turnover${suffix}`] += sn(row[`turnover${suffix}`], 2);
                  totalRow[`cturnover${suffix}`] += sn(row[`cturnover${suffix}`], 2);

                  totalRow[`ebay_fee${suffix}`] += sn(row[`ebay_fee${suffix}`], 2);
                  totalRow[`landed${suffix}`] += sn(row[`landed${suffix}`], 2);

                  // BP calculation
                  const bp =
                    sn(row[`landed${suffix}`], 2) + sn(row[`ebay_fee${suffix}`], 2) + sn(row[`adv${suffix}`], 2);
                  row[`bp${suffix}`] = bp;
                  totalRow[`bp${suffix}`] += sn(row[`bp${suffix}`], 2);

                  // BP / pcs
                  row[`landed_pcs${suffix}`] = sn(row[`landed${suffix}`] / qty, 2);
                  row[`ebay_fee_pcs${suffix}`] = sn(row[`ebay_fee${suffix}`] / qty, 2);
                  row[`bp_pcs${suffix}`] =
                    sn(row[`landed_pcs${suffix}`], 2) +
                    sn(row[`ebay_fee_pcs${suffix}`], 2) +
                    sn(row[`adv_pcs${suffix}`], 2);
                });
              }

              res.data.forEach((row: RecordType) => {
                totalRow.ss_mix_bp += sn(row.ss_bp);
                totalRow.mix_total_piece_qty += sn(row.total_piece_qty);
                totalRow.mix_qty += sn(row.total_piece_qty);
              });

              res.data.splice(0, 0, totalRow);

              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: 10000,
        }}
        columns={columns}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
        columnEmptyText=""
        /* components={{
          header: {
            cell: ResizableTitle,
          },
        }} */
        rowSelection={false}
        rowClassName={(record) => {
          if (record.uid == 'total') {
            return 'total-row';
          } else return record.ean_id ? (record?.is_single ? 'row-single' : 'row-multi') : '';
        }}
      />
      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_qty,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_res_qty,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />
      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
      />
      {currentRow?.item_ean?.id && (
        <IboDetailModal
          eanId={currentRow?.item_ean?.id}
          modalVisible={openIBOModal}
          handleModalVisible={setOpenIBOModal}
        />
      )}

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow?.item_ean || {}}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        initialValues={currentRow?.item_ean || {}}
        isRefetchInitialValues
        onSubmit={async (values) => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
      />

      <Drawer
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        width={700}
        open={showImportedPrices}
        className={currentRow?.item_ean?.is_single ? 'm-single' : 'm-multi'}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
        // closable={false}
      >
        {currentRow?.item_ean && (
          <ImportedPrices
            itemEan={currentRow.item_ean}
            iboListProps={{
              filters: {
                filterMode: 'latest',
                pageSize: 8,
              },
            }}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default SalesStat;
