import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { getComparedList, getImportACList } from '@/services/foodstore-one/Import/import';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormGroup, ProFormSwitch } from '@ant-design/pro-form';
import { ProFormDependency, ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Button, Card, Col, Drawer, Row, Typography } from 'antd';
import type { DefaultOptionType } from 'antd/lib/select';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import Util, { nf2, sn } from '@/util';
import SPrices from '@/components/SPrices';
import _ from 'lodash';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';

export type FormValueType = {
  master?: number;
  slaves?: number[];
};

const defaultSlavesIds = [0, 1, 2, 3];
const SupplierDataCompare: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [files, setFiles] = useState<DefaultOptionType[]>([]);
  const [indexSlaveIdMap, setIndexSlaveIdMap] = useState<any>({});
  const [loading, setLoading] = useState(false);

  // imported prices table
  const [currentRow, setCurrentRow] = useState<API.ImportedSupplierRow>();
  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);

  useEffect(() => {
    getImportACList().then((res) => setFiles(res));
  }, []);

  const handleFinish = async (values: FormValueType) => {
    actionRef.current?.reload();
  };

  const columns: ProColumns<API.ImportedSupplierRow>[] = useMemo(() => {
    const newColumns: ProColumns<API.ImportedSupplierAndIbo>[] = [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 40,
        align: 'center',
        render: (item, record, index, action) => {
          return (
            ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
            index +
            1
          );
        },
      },
    ];

    const includedCols: string[] = [];

    // Name column
    includedCols.push('name');
    newColumns.push({
      dataIndex: 'name',
      title: (
        <>
          <div className="c-green">Name</div>
        </>
      ),
      width: 150,
      ellipsis: false,
      filters: true,
      render: (dom, record: API.ImportedSupplierRow) => {
        return (
          <>
            {record.name ? (
              <Typography.Paragraph copyable={{ text: record.name }} className="margin-0">
                <span className={record.ean_exist ? 'c-green' : ''}>{record.name}</span>
              </Typography.Paragraph>
            ) : (
              <div className={record.ean_exist ? 'c-green' : ''}>{record.name}</div>
            )}
          </>
        );
      },
    });

    // EAN (sys) column
    includedCols.push('ean');
    newColumns.push({
      dataIndex: 'ean',
      title: (
        <>
          <div className="c-green">EAN</div>
        </>
      ),
      width: 150,
      ellipsis: false,
      filters: true,
      render: (dom, record: API.ImportedSupplierRow) => {
        return (
          <>
            {record.ean ? (
              <Typography.Paragraph copyable={{ text: record.ean }} className="margin-0">
                <span className={record.ean_exist ? 'c-green' : ''}>{record.ean}</span>
              </Typography.Paragraph>
            ) : (
              <div className={record.ean_exist ? 'c-green' : ''}>{record.ean}</div>
            )}
          </>
        );
      },
    });

    // Trademark column
    includedCols.push('trademark');
    newColumns.push({
      dataIndex: 'trademark',
      title: (
        <>
          <div className="c-green">Trademark</div>
        </>
      ),
      width: 150,
      ellipsis: false,
      filters: true,
      align: 'left',
      render: (dom, record: API.ImportedSupplierRow) => {
        return (
          <>
            <div>{record.trademark}</div>
          </>
        );
      },
    });

    // System EAN price
    // ------------------------------------------------------------------------
    newColumns.push({
      title: 'Standardpreis (FS_ONE)',
      dataIndex: ['item_ean', 'ean_prices', 0, 'price_type_id'],
      valueType: 'digit',
      sorter: false,
      align: 'right',
      width: 110,
      hideInSearch: true,
      className: 'cursor-pointer',
      render: (dom, record) => {
        const vat = record?.item_ean?.item?.vat?.value || 0;
        const price = Util.safeNumber(
          _.get(_.find(record?.item_ean?.ean_prices, { price_type_id: 1 }), 'price', 0).toFixed(2),
        );
        return (
          <Row gutter={4}>
            <Col span={12}>
              <SPrices price={price} vat={vat} />
            </Col>
            {!record?.item_ean?.is_single && (
              <Col span={12}>
                <SPrices price={price * (record?.item_ean?.attr_case_qty ?? 0)} vat={vat} />
              </Col>
            )}
          </Row>
        );
      },
      onCell: (record: API.ImportedSupplierRow) => {
        return {
          onClick: (ev: any) => {
            setCurrentRow({ ...record });
            setShowImportedPrices(true);
          },
        };
      },
    });

    // Master price
    newColumns.push({
      dataIndex: 'price',
      title: (
        <>
          <div className="c-green">Master Price</div>
        </>
      ),
      width: 80,
      ellipsis: false,
      filters: true,
      align: 'right',
      render: (dom, record: API.ImportedSupplierRow) => {
        return (
          <>
            <div>{nf2(record.price)}</div>
          </>
        );
      },
      onCell: (record, rowIndex) => {
        let minValue = Number.MAX_VALUE;
        let selectedSid = null;
        ['', ...Object.values(indexSlaveIdMap)].forEach((sid) => {
          const priceVal = sn(record[`price${sid}`]);
          if (priceVal > 0 && minValue > priceVal) {
            minValue = priceVal;
            selectedSid = sid;
          }
        });
        let cls = '';
        if (selectedSid === '') {
          cls = 'bg-green2';
        }
        return {
          className: cls,
        };
      },
    });

    Object.keys(indexSlaveIdMap).forEach((index) => {
      const slaveId = indexSlaveIdMap[index];
      if (!slaveId) return;
      newColumns.push({
        dataIndex: 'price' + slaveId,
        title: (
          <>
            <div className="c-green">Price {Number(index) + 1}</div>
          </>
        ),
        width: 80,
        ellipsis: false,
        filters: true,
        align: 'right',
        render: (dom, record: API.ImportedSupplierRow) => {
          return (
            <>
              <div>{nf2(record[`price${slaveId}`])}</div>
            </>
          );
        },
        onCell: (record, rowIndex) => {
          let minValue = Number.MAX_VALUE;
          let selectedSid = null;
          ['', ...Object.values(indexSlaveIdMap)].forEach((sid) => {
            const priceVal = sn(record[`price${sid}`]);
            if (priceVal > 0 && minValue > priceVal) {
              minValue = priceVal;
              selectedSid = sid;
            }
          });
          let cls = '';
          if (selectedSid == slaveId) {
            cls = 'bg-green2';
          }
          return {
            className: cls,
          };
        },
      });
    });

    return newColumns;
  }, [indexSlaveIdMap]);
  return (
    <PageContainer content="Import Supplier Data Compare">
      <Card style={{ marginBottom: 16 }}>
        <ProForm<FormValueType>
          formRef={formRef}
          initialValues={Util.getSfValues('sf_supplier_compare')}
          className="search-form"
          submitter={{
            submitButtonProps: { loading },
            render: (props, doms) => {
              return [
                <Button type="primary" key="submit" onClick={() => formRef.current?.submit()}>
                  Compare
                </Button>,
                <Button type="default" key="rest" onClick={() => formRef.current?.resetFields()}>
                  Reset
                </Button>,
              ];
            },
          }}
          layout="inline"
          onFinish={handleFinish}
        >
          <ProFormDependency name={defaultSlavesIds.map((x) => ['slaves', `s${x}`])}>
            {(values) => {
              const usedIds = Object.values(values.slaves || {});
              return (
                <>
                  <ProFormSelect
                    showSearch
                    placeholder="Select"
                    fieldProps={{
                      dropdownMatchSelectWidth: 400,
                    }}
                    required
                    rules={[{ required: true }]}
                    options={files.filter((x) => !usedIds.includes(x.id))}
                    width="sm"
                    name="master"
                    label="Master file"
                  />
                </>
              );
            }}
          </ProFormDependency>
          {defaultSlavesIds.map((id) => {
            const depsNames = defaultSlavesIds.filter((x) => x != id).map((x) => ['slaves', `s${x}`]);
            return (
              <ProFormDependency key={id} name={['master', ...depsNames]}>
                {(values) => {
                  const usedIds = [...Object.values(values.slaves || {}), values.master];
                  return (
                    <>
                      <ProFormSelect
                        showSearch
                        placeholder="Select"
                        fieldProps={{
                          dropdownMatchSelectWidth: 400,
                        }}
                        options={files.filter((x) => !usedIds.includes(x.id))}
                        width={150}
                        label={`File ${id + 1}`}
                        name={['slaves', `s${id}`]}
                      />
                    </>
                  );
                }}
              </ProFormDependency>
            );
          })}
          <ProFormGroup>
            <ProFormSwitch
              name={'only_existing_ean'}
              label="Only existing EAN"
              initialValue={true}
              fieldProps={{
                onChange: () => {
                  formRef.current?.setFieldsValue({ only_not_existing_ean: false });
                  actionRef.current?.reload();
                },
              }}
            />
            <ProFormText name={'ean'} label="EAN" />
            <ProFormText name={'name'} label="Name" />
            <ProFormText name={'trademark'} label="Trademark" />
            <ProFormCheckbox name="only_master" label="Only in Master" />
            <ProFormCheckbox name="only_file1" label="Only in File 1" />
            <ProFormCheckbox name="is_diff_m1" label="Price difference in Master & File 1" />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<API.ImportedSupplierRow, API.PageParams>
        headerTitle={'Imported List'}
        actionRef={actionRef}
        rowKey="key"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        // rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        request={async (params, sort, filter) => {
          const formValues = formRef.current?.getFieldsValue();
          Util.setSfValues('sf_supplier_compare', formValues);
          if (!formValues.master) {
            return new Promise((resolve) => resolve([]));
          }
          const map: any = {};
          Object.keys(formValues.slaves || {}).forEach((sIndex) => {
            map[sIndex.substring(1)] = formValues.slaves[sIndex];
          });
          setIndexSlaveIdMap(map);
          setLoading(true);
          return getComparedList(
            formValues.master || 0,
            { ...params, ...formValues, slaves: Object.values(map) },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
      />

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.item_ean?.ean ?? currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow?.item_ean} />}
      </Drawer>
    </PageContainer>
  );
};

export default SupplierDataCompare;
