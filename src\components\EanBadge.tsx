import { HeartTwoTone } from '@ant-design/icons';
import { useMemo } from 'react';
import { useModel } from 'umi';

type EanBadgeIconProps = {
  itemEan?: Pick<API.Ean, 'fs_special_discount' | 'fs_special_badge' | 'fs_special_badge2'>;
};

const EanBadgeIcon: React.FC<EanBadgeIconProps> = ({ itemEan, ...rest }) => {
  const { appSettings } = useModel('app-settings');
  const { dict } = appSettings;

  const { fs_special_discount, fs_special_badge, fs_special_badge2 } = itemEan || {};

  const badgeStr = useMemo(() => {
    let str = '';
    if (fs_special_discount || fs_special_badge) {
      str = `${fs_special_discount ?? ''} | ${fs_special_badge ? dict?.[`${fs_special_badge}`]?.label : ''} | ${
        fs_special_badge2 ? dict?.[`${fs_special_badge2}`]?.label : ''
      }`;
    }
    return str;
  }, [fs_special_discount, fs_special_badge, dict, fs_special_badge2]);

  return badgeStr ? <HeartTwoTone twoToneColor="#eb2f96" title={badgeStr} /> : null;
};

export default EanBadgeIcon;
