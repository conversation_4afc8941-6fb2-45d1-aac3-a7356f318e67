import type { Dispatch, SetStateAction } from 'react';
import { useMemo } from 'react';
import { useState } from 'react';
import React, { useRef } from 'react';
import type { InputRef } from 'antd';
import { Alert, Divider } from 'antd';
import { Button, Col, Input, message, Row, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormGroup, ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanBatch } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _ from 'lodash';
import { PlusOutlined } from '@ant-design/icons';
import { useModel } from 'umi';
import { DictType } from '@/constants';
import { addDict, getDictList } from '@/services/foodstore-one/Sys/sys-dict';

export type FormValueType = Partial<API.Ean>;

export type UpdateLabelAndBadgeFormBulkProps = {
  eanIds: number[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateLabelAndBadgeFormBulk: React.FC<UpdateLabelAndBadgeFormBulkProps> = (props) => {
  const [loading, setLoading] = useState(false);

  const { loadAppSetting, getDictOptions } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();

  // Ref object for Title prefix dropdown.
  const titlePrefixRef = useRef<InputRef>(null);
  const [newTitlePrefix, setNewTitlePrefix] = useState<string>('');

  // price related
  const titlePrefixOptions = useMemo(() => getDictOptions(DictType.TitlePrefix, 'label'), [getDictOptions]);

  const onTitlePrefixAddClick = async (value?: string, type?: string) => {
    if (!value) {
      message.error('Please fill new name.');
    }
    titlePrefixRef.current?.input?.setAttribute('disabled', 'true');
    addDict({
      code: value,
      value: value,
      type,
    })
      .then((res) => {
        console.log(res);
        formRef.current?.setFieldValue('fs_special_badge2', res.code);
        loadAppSetting();
        setNewTitlePrefix('');
      })
      .catch((err) => Util.error(err, 5));
  };

  return (
    <ModalForm<Partial<API.Ean>>
      title={<>{`Update ${props.eanIds.length} EANs' Discount / Badge / Label`}</>}
      width={1000}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      formRef={formRef}
      onFinish={async (value) => {
        if (!formRef.current) return;
        try {
          setLoading(true);

          const postData = {
            ids: props.eanIds,
            mode: 'discountBatch',
            data: null,
          };

          const data = value;
          if (!('fs_special_discount' in data)) {
            data.fs_special_discount = null;
          }

          if (!('fs_special_badge' in data)) {
            data.fs_special_badge = null;
          }
          if (!('fs_special_badge2' in data)) {
            data.fs_special_badge2 = null;
          }
          postData.data = data as any;

          const hide = message.loading('Batch updating...', 0);
          updateEanBatch(postData as any)
            .then((res) => {
              if (props.onSubmit) {
                props.onSubmit?.(value);
                if ((value as any).closeModal) props.handleModalVisible(false);
              }
            })
            .catch(Util.error)
            .finally(() => {
              setLoading(false);
              hide();
            });
        } catch (err) {
          Util.error(err);
          setLoading(false);
        }
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              <Alert
                type="warning"
                message="Successful save will be followed by up sync."
                style={{ marginRight: 30 }}
              />
              <Button
                type="primary"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
                disabled={loading}
              >
                Save
              </Button>
              <Button
                type="default"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
                disabled={loading}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <Row gutter={8}>
          {/* <Col>
            <SProFormDigit
              name="fs_ebay_price_std"
              label={'Ebay-Std (€)'}
              placeholder="Ebay-Std Price"
              width={105}
              fieldProps={{
                precision: 2,
              }}
              formItemProps={{ style: { opacity: 0.5 } }}
            />
          </Col>
          <Col>
            <ProFormDependency
              key={'fs_ebay_price_std_deps'}
              name={['ean_prices', 'fs_ebay_price_std']}
            >
              {(depValues) => {
                const standardPrice = depValues.ean_prices?.find(
                  (x: API.EanPrice) => x.price_type_id == 1,
                );
                const ebayPrice = sn(formRef.current?.getFieldValue('fs_ebay_price_std'));
                return (
                  <Space style={{ paddingTop: 36 }} size={16}>
                    <div>
                      {ebayPrice > 0 && standardPrice?.price && (
                        <span className="dark-blue">
                          {nf2((ebayPrice * 100) / standardPrice?.price, true, true)}%
                        </span>
                      )}
                    </div>
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => {
                        formRef.current?.setFieldValue(
                          'fs_ebay_price_std',
                          sn(standardPrice.price) * 1.15,
                        );
                      }}
                    >
                      Set 15%
                    </Button>
                  </Space>
                );
              }}
            </ProFormDependency>
          </Col> */}
          <Col>
            <ProFormSelect
              name="fs_special_discount"
              label="Discount"
              placeholder="Discount"
              width={105}
              options={['-10%', '-20%', '-25%', '-30%', '-40%', '-50%', '-60%', '-70%', '-80%', '-90%']}
              fieldProps={{
                onChange(value, option) {
                  // updateEbaySpecialPrice();
                },
              }}
            />
          </Col>
          {/* <Col>
            <ProFormDependency
              key={'fs_special_discount_dep'}
              name={['ean_prices', 'fs_special_discount']}
            >
              {(depValues) => {
                const discount = sn((depValues.fs_special_discount || '').slice(0, -1)) ?? 0;
                if (depValues.ean_prices) {
                  // V1: by EAN price
                  const prices = Util.fPrices(
                    sn(
                      depValues.ean_prices.find((x: API.EanPrice) => x.price_type_id == 1)?.price,
                    ) *
                      1.15 *
                      (1 + discount / 100),
                    props.initialValues?.item?.vat?.value,
                  );

                  return discount && sn(prices[1]) > 0 ? (
                    <ProFormItem label=" ">
                      <span className="dark-blue">{`${nf2(prices[0])} / ${nf2(prices[1])}`}</span>
                    </ProFormItem>
                  ) : undefined;
                }
                return undefined;
              }}
            </ProFormDependency>
          </Col> */}
          <Col>
            <ProFormSelect
              name="fs_special_badge"
              label="Special badge"
              placeholder="Badge"
              showSearch
              width={150}
              request={async (params) => {
                return getDictList({ params, type: DictType.ProductBadge, pageSize: 500 }).then((res) =>
                  res.data.map((x) => ({ value: x.code, label: x.value })),
                );
              }}
            />
          </Col>
          <Col>
            <ProFormSelect
              name="fs_special_badge2"
              label="Text Label/Badge"
              placeholder="Text Label/Badge"
              showSearch
              width={150}
              options={(titlePrefixOptions as API.Dict[])
                ?.filter((x) => x.type == DictType.TitlePrefix)
                ?.map((x) => ({ label: x.value, value: x.code }))}
              fieldProps={{
                dropdownRender(menu) {
                  return (
                    <>
                      {menu}
                      <Divider style={{ margin: '8px 0' }} />
                      <Space style={{ padding: '0 8px 4px' }}>
                        <Input
                          placeholder="Please enter item"
                          ref={titlePrefixRef}
                          value={newTitlePrefix}
                          onChange={(e) => setNewTitlePrefix(e.target.value)}
                        />
                        <Button
                          type="text"
                          icon={<PlusOutlined />}
                          onClick={() => {
                            onTitlePrefixAddClick(titlePrefixRef.current?.input?.value, DictType.TitlePrefix);
                          }}
                        />
                      </Space>
                    </>
                  );
                },
              }}
            />
          </Col>
        </Row>
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateLabelAndBadgeFormBulk;
