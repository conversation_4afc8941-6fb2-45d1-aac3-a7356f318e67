.eanListContainer {
  :global {
    .ant-table.ant-table-small .ant-table-title,
    .ant-table.ant-table-small .ant-table-footer,
    .ant-table.ant-table-small .ant-table-thead > tr > th,
    .ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
    .ant-table.ant-table-small tfoot > tr > th,
    .ant-table.ant-table-small tfoot > tr > td {
      padding: 3px 5px;
    }
    .textForm .ant-pro-form-list-container {
      display: flex;
      flex-wrap: wrap;
    }

    .ant-pro-form-query-filter .ant-col {
      flex: 0 0 auto !important;
      max-width: 100%;
    }

    .ant-pro-form-query-filter > .ant-row > .ant-col:last-child {
      margin-left: auto;
    }
    .ant-pro-form-query-filter .ant-row.ant-form-item {
      margin-bottom: 12px;
    }
    .website-icon {
      margin-right: 0;
      padding: 0 4px 0 3px;
      font-size: 10px;
      line-height: 12px;
      border-radius: 100%;
    }

    .image-card {
      /* border: 1px solid #9c5fe3; */
      border: 1px solid #bbb;
      border-radius: 2px;
      img {
        object-fit: cover;
      }
      .card-actions {
        .type-action {
          padding: 0 3px;
          border: 1px solid #ddd;
          border-radius: 2px;
          cursor: pointer;
          opacity: 0;

          &.active {
            background: #ddd;
            opacity: 1;
          }
        }
      }
      &.parent-img {
        border-color: #ccc;
        border-style: dashed;
      }
      &:hover {
        .card-actions .type-action {
          opacity: 0.8;
          &:hover {
            border: 1px solid #aaa;
          }
          &.active {
            opacity: 1;
          }
        }
      }
    }

    .image-card.readonly {
      .card-actions {
        .type-action {
          border: 1px solid #ddd;
          cursor: default;
          &.active {
            opacity: 1;
          }
        }
      }
      &:hover {
        .card-actions .type-action {
          opacity: 0;
          &:hover {
            border: 1px solid #ddd;
          }
          &.active {
            opacity: 1;
          }
        }
      }
    }
  }
}
