import { NoStockSkuType, bookPicklist, exportPicklistPdf } from '@/services/foodstore-one/Warehouse/picklist';
import Util, { skuToItemId, sn } from '@/util';
import { InfoCircleFilled, InfoCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal, Typography, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';

export type RecordType = {
  sku?: string;
  qty?: number;
};

export type NoStockSkuModalProps = {
  picklistId: number;
  skus: NoStockSkuType[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  skipPdfDownload?: boolean;
  successCb?: (res?: any) => void;
};

const NoStockSkuModal: React.FC<NoStockSkuModalProps> = ({
  picklistId,
  skus,
  modalVisible,
  handleModalVisible,
  successCb,
  skipPdfDownload,
}) => {
  const columns: ProColumns<RecordType>[] = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      align: 'left',
      copyable: true,
      width: 120,
      render(__, entity) {
        return (
          <Typography.Link href={`/item/ean-all-summary?sku=${skuToItemId(entity.sku)}_`} target="_blank" copyable>
            {entity.sku}
          </Typography.Link>
        );
      },
    },
    {
      title: 'Qty',
      dataIndex: 'qty',
      align: 'right',
      ellipsis: true,
      width: 150,
    },
  ];

  return (
    <Modal
      title={<span>Out of Stocks</span>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={500}
      maskClosable={false}
      okText="Continue to book"
      okButtonProps={{ title: 'Will add new stocks in B0000 WL.' }}
      onOk={() => {
        if (!skus) {
          message.info('No sku list to be processed.');
          return;
        }
        const hide = message.loading('Booking again...');
        bookPicklist(sn(picklistId), { useStockAdjustment: true, noStockSkus: skus })
          .then((res) => {
            if (res.isOk) {
              message.success('Booked successfully.');
              if (!skipPdfDownload) {
                const hide2 = message.loading('Downloading picklist data as PDF format...', 0);
                exportPicklistPdf(sn(picklistId), { mode: 'groupByOrder' })
                  .then((res2) => {
                    hide2();
                    if (res2.url) {
                      window.open(`${API_URL}/api/${res2.url}`, '_blank');
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide2();
                  });
              }
              if (successCb) {
                successCb();
              }
              handleModalVisible(false);
            } else if (res.notEnoughSkus) {
            }
          })
          .catch(Util.error)
          .finally(() => {
            hide();
          });
      }}
    >
      <ProTable<RecordType, API.PageParams>
        rowKey="sku"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        size="small"
        scroll={{ x: 400 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        dataSource={skus}
        columns={columns}
        pagination={{ defaultPageSize: 200, hideOnSinglePage: true }}
        rowSelection={false}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default NoStockSkuModal;
