import {
  CheckCircleOutlined,
  CheckSquareOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DollarOutlined,
  DownloadOutlined,
  DownOutlined,
  EditTwoTone,
  FileExcelOutlined,
  FileTextOutlined,
  FileWordOutlined,
  LinkOutlined,
  LoadingOutlined,
  PictureOutlined,
  SaveOutlined,
  SnippetsOutlined,
  SyncOutlined,
  TrademarkOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Col, Drawer, Modal, Progress, Row, Popover } from 'antd';
import { Card } from 'antd';
import { Button, message, Dropdown, Space, Menu, Popconfirm, Typography } from 'antd';
import type { CSSProperties } from 'react';
import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateAttributeForm from './components/UpdateAttributeForm';
import SDatePicker from '@/components/SDatePicker';

import Util, { casePrice, nf2, ni, sn, sUrlByTpl } from '@/util';
import CreateForm from './components/CreateForm';
import WebsiteIcons from './components/WebsiteIcons';
import {
  getEanList,
  deleteEan,
  exportEanList,
  exportEanListAlt,
  exportEanPriceList,
  dsGetCustomAttribute,
  usProductFull,
  EAN_DEFAULT_SUMMARY_WITH,
  exportEanWithCodeList,
  updateEanBatch,
} from '@/services/foodstore-one/Item/ean';
import type { StockStableStatus } from '@/constants';
import { DictCode, EURO, ItemEANStatus, ScrapSystemIds, StockStableStatusOptionsKv } from '@/constants';
import { DEFAULT_PER_PAGE_PAGINATION, ItemEANStatusOptions } from '@/constants';
import UpdateCategoriesForm from './components/UpdateCategoriesForm';
import type { DataNode } from 'antd/lib/tree';
import { getCategoryList } from '@/services/foodstore-one/Item/category';
import UpdateTextsForm from './components/UpdateTextsForm';
import UpdatePicturesForm from './components/UpdatePicturesForm';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormGroup } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
import * as UpdateItemForm from '../ItemList/components/UpdateForm';
import { IRouteComponentProps, useLocation, useModel } from 'umi';
import SocialLinks from './components/SocialIcons';
import type { DefaultOptionType } from 'antd/lib/select';
import UpdateCategoriesFormBulk from './components/UpdateCategoriesFormBulk';
import UpdateAttributesFormBulk from './components/UpdateAttributesFormBulk';
import StockStableQtyModal from './components/StockStableQtyModal';
import { scrapWoSPrice } from '@/services/foodstore-one/Scrap/scrap-price';
import SPrices from '@/components/SPrices';
import ImportedPrices from './components/ImportedPrices';
import UpdatePriceAttributeForm from './components/UpdatePriceAttributeForm';
import UpdateVatFormBulk from './components/UpdateVatFormBulk';
import type { ResizeCallbackData } from 'react-resizable';
import { ResizableTitle } from './EanAllPic';
import EanTasksModals from './components/EanTasksModal';
import useModalNavigation from './hooks/useModalNavigation';
import usePageContainerTitle from './hooks/usePageContainerTitle';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import type { TrademarkChangeCallbackHandlerTypeParamType } from './hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from './hooks/useTrademarkFormFilter';
import { getImportACList } from '@/services/foodstore-one/Import/import';
import UpdatePriceAttributeFormBulk from './components/UpdatePriceAttributeFormBulk';
import UpdateBulkForm from '../ItemList/components/UpdateBulkForm';
import useIbomOptions from '@/hooks/BasicData/useIbomOptions';
import useEanSpecialFilter from './hooks/useEanSpecialFilter';
import AddOfferFormBulk from './components/AddOfferFormBulk';
import SProFormDigit from '@/components/SProFormDigit';
import EanFilesComp from '@/components/EanFilesComp';

/**
 *  Delete EANs
 *
 * @param selectedRows
 */
export const handleEanRemoveBatch = async (selectedRows: API.Ean[]) => {
  const hide = message.loading('Deleting', 0);
  if (!selectedRows) return true;

  try {
    await deleteEan({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!');
    return false;
  }
};

export const StockQtyComp = ({
  availableQty,
  blockedQty,
  mode,
  is_single,
}: {
  availableQty?: number;
  blockedQty?: number;
  is_single?: boolean;
  mode?: string;
}) => {
  if (!sn(blockedQty) && !sn(availableQty)) return null;

  return (
    <Row gutter={2} title={`Total = ${ni(sn(availableQty) + sn(blockedQty))} ${is_single ? ' pcs' : ' boxes'}`}>
      <Col>{ni(availableQty, true)}</Col>
      {sn(blockedQty) ? <Col>+</Col> : null}
      {sn(blockedQty) ? <Col className="italic">{ni(blockedQty)}</Col> : null}
    </Row>
  );
};

export const TaskIcon: React.FC<{ count?: number }> = ({ count }) => {
  return <CheckSquareOutlined style={{ color: count ? 'red' : 'lightgrey' }} />;
};

type BulkStockStatusForm = {
  status?: StockStableStatus;
};

export type SearchFormValueType = Partial<API.Ean>;

export type EanSummaryComponentProps = IRouteComponentProps & {
  eanType?: 'default' | 'base' | 've';
};

const getColClassByPrices = (
  record: API.Ean,
  priceTypes?: API.PriceType[],
  expectedType?: string | number,
  okClassName?: string,
) => {
  const vat = sn(record.item?.vat?.value || 0);
  const priceLists: any = {};
  let minValue = Number.MAX_VALUE;
  let typeWithMinValue = null;
  ScrapSystemIds.forEach((scrapName) => {
    const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });
    if (scrapPriceObj) {
      const price = casePrice(scrapPriceObj?.price, record.attr_case_qty);
      if (price) {
        priceLists[scrapName] = price;
        if (minValue > price) {
          minValue = price;
          typeWithMinValue = scrapName;
        }
      }
    }
  });
  (priceTypes ?? [])
    .filter((x) => x.id == 1)
    .forEach((pt) => {
      let price = sn(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0));
      price = casePrice(price, record.attr_case_qty) * (1 + vat / 100);
      if (price) {
        priceLists[`${pt.id}`] = price;
        if (minValue > price) {
          minValue = price;
          typeWithMinValue = `${pt.id}`;
        }
      }
    });

  if (Object.keys(priceLists).length >= 2 && typeWithMinValue == expectedType) {
    return okClassName ?? 'bg-green3';
  }
  return '';
};

const EanAllSummary: React.FC<EanSummaryComponentProps> = (eanComponentProps) => {
  const eanTypeProp = eanComponentProps.eanType || 'default';
  const { appSettings } = useModel('app-settings');
  const { getDictByCode } = useModel('app-settings');
  const { priceTypes } = appSettings;

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);
  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  // batch
  const [visibleUpdateVatFormBulk, handleVisibleUpdateVatFormBulk] = useState<boolean>(false);
  const [batchUpSyncModalVisible, handleBatchUpSyncModalVisible] = useState<boolean>(false);
  const [batchUpSyncProgress, setBatchUpSyncProgress] = useState(0);
  const [batchModalData, setBatchModalData] = useState<{ title: string; desc?: string }>({
    title: 'Batch Up Syncing...',
    desc: 'Batch UpSync is in progress. Please wait...',
  });

  // bulk updates
  const [visibleUpdateCategoriesFormBulk, handleVisibleUpdateCategoriesFormBulk] = useState<boolean>(false);
  const [visibleUpdateAttributesFormBulk, handleVisibleUpdateAttributesFormBulk] = useState<boolean>(false);
  const [visibleUpdatePriceFormBulk, handleVisibleUpdatePriceFormBulk] = useState<boolean>(false);
  const [visibleUpdateTrademarkFormBulk, handleVisibleUpdateTrademarkFormBulk] = useState<boolean>(false);
  const [visibleAddOfferFormBulk, handleVisibleAddOfferFormBulk] = useState<boolean>(false);

  // Ean Tasks model
  const [visibleEanTasksModal, setVisibleEanTasksModal] = useState<boolean>(false);

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});
  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const [selectedRows, setSelectedRows] = useState<API.Ean[]>([]);

  // datasource for inline editing
  const [datasource, setDatasource] = useState<API.Ean[]>([]);

  const exportSupplierRef = useRef<any>(null);

  // hook for modal navigation
  // ------------------------------------------------------------- //
  const { handleNavigation } = useModalNavigation(datasource, {
    item: handleUpdateItemModalVisible,
    attribute: handleUpdateModalVisible,
    picture: handleUpdatePicturesModalVisible,
    price: handleUpdatePricesModalVisible,
    text: handleUpdateTextsModalVisible,
    category: handleUpdateCategoriesModalVisible,
    modals: ['item', 'text', 'price', 'attribute', 'picture', 'category'],
    setCurrentRow,
  });

  // ibom filter
  const { ibom, formElements: formElementsIbom } = useIbomOptions(undefined, searchFormRef);

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
    ibom_id: ibom?.id,
  });

  const [loadingExport, setLoadingExport] = useState(false);
  const location: any = useLocation();

  const handleTextsClick = (record: API.Ean) => {
    setCurrentRow({ ...record });
    handleUpdateTextsModalVisible(true);
  };

  // Xls file selection
  const [files, setFiles] = useState<DefaultOptionType[]>([]);
  const [supplierXlsFileId, setSupplierXlsFileId] = useState<number>();

  // bulk stock stable status update
  const bulkStockStatusFormRef = useRef<ProFormInstance<BulkStockStatusForm>>();
  const [openBulkStockStatusForm, setOpenBulkStockStatusForm] = useState<boolean>(false);

  /**
   * UpSync this EAN fully.
   */
  const handleUpSync = useCallback(async (id: number) => {
    const hide = message.loading(`Up syncing ...`, 0);
    return usProductFull(id)
      .then((res) => {
        if (res.sku) {
          message.success('Successfully up synced on shop!');
        } else {
          message.error(res.upSyncMessage || 'Failed to up sync EAN!');
        }
      })
      .catch((e) => {
        message.error(e.message ?? 'Failed to upsync!');
      })
      .finally(() => {
        hide();
      });
  }, []);

  const [columns, setColumns] = useState<ProColumns<API.Ean>[]>([]);
  const pricesColDefs = useMemo<ProColumns<API.Ean>[]>(
    () =>
      priceTypes.map(
        (pt: any): ProColumns<API.Ean> => ({
          title: pt.name,
          dataIndex: ['ean_prices', pt?.id ?? 0],
          valueType: 'digit',
          sorter: false,
          align: 'center',
          width: 100,
          hideInSearch: true,
          className: 'cursor-pointer',
          shouldCellUpdate(record, prevRecord) {
            return (
              !_.isEqual(record.ean_prices, prevRecord.ean_prices) ||
              !_.isEqual(record.parent?.ean_prices, prevRecord.parent?.ean_prices)
            );
          },
          render: (dom, record) => {
            const vat = record.item?.vat?.value || 0;
            /* const priceSingle = Util.safeNumber(
              _.get(
                _.find(record?.parent?.ean_prices, { price_type_id: pt.id }),
                'price',
                0,
              ).toFixed(2),
            ); */
            const priceSingle =
              Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0)) /
              (record?.attr_case_qty ? record?.attr_case_qty : 1);

            const price = Util.safeNumber(
              _.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0).toFixed(2),
            );

            const supXlsFileId = sn(searchFormRef.current?.getFieldValue('supplierXlsFileId'));
            const gfcStyle: CSSProperties = {};

            // GFC styling
            if (record.idInXlsFile) {
              if (supXlsFileId && pt.id == 2) {
                if (sn(priceSingle, 2) <= sn(record.priceInXlsFile, 2)) {
                  gfcStyle.color = 'red';
                } else {
                  if ((sn(priceSingle, 2) < sn(sn(record.priceInXlsFile, 2) * 1.1), 2)) {
                    gfcStyle.color = 'orange';
                  }
                }
              }
            }

            return (
              <Row gutter={4}>
                <Col span={12}>
                  <SPrices price={priceSingle} vat={vat} style={{ ...gfcStyle }} />
                </Col>
                {!record.is_single && (
                  <Col span={12}>
                    <SPrices price={price} vat={vat} style={{ ...gfcStyle }} />
                  </Col>
                )}
              </Row>
            );
          },
          onCell(record, index) {
            return {
              onClick(e) {
                setCurrentRow(record);
                handleUpdatePricesModalVisible(true);
              },
            };
          },
        }),
      ),
    [priceTypes],
  );

  const orgColumns: ProColumns<API.Ean>[] = useMemo(
    () => [
      {
        title: 'Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        align: 'center',
        ellipsis: true,
        width: 50,
        showSorterTooltip: false,
        valueEnum: ItemEANStatusOptions as any,
        render: (__, record) => {
          let ele = null;
          if (record.status == ItemEANStatus.ACTIVE) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          } else {
            ele = <CloseOutlined style={{ color: 'gray' }} />;
          }
          return ele;
        },
      },
      {
        title: 'Shops',
        dataIndex: 'product_websites',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        width: 50,
        showSorterTooltip: false,
        render: (__, record) => (
          <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
        ),
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 80,
        render: (dom, record) => <EanFilesComp files={record.files} />,
      },
      {
        title: 'Item ID',
        dataIndex: 'item_id',
        fixed: 'left',
        sorter: true,
        ellipsis: true,
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: 'Name DE',
        dataIndex: ['ean_texts', 0, 'name'],
        width: 180,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        fixed: 'left',
        tooltip: 'Orange color indicates the inherited value from its item.',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record, index) => {
          const defaultValue = record?.ean_texts?.[0]?.name ?? record?.item?.name;
          return (
            <a onClick={() => handleTextsClick(record)}>
              <Typography.Text type={record?.ean_texts?.[0]?.name ? undefined : 'warning'} title={defaultValue}>
                {defaultValue ?? <CloseOutlined style={{ color: '#cc2200' }} />}
              </Typography.Text>
            </a>
          );
        },
      },
      {
        title: 'VAT',
        dataIndex: ['item', 'vat', 'value'],
        sorter: false,
        width: 50,
        ellipsis: true,
        align: 'right',
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.item?.vat, prevRecord.item?.vat),
        render: (__, record) =>
          sn(record?.item?.vat?.value) > 0 ? `${nf2(record?.item?.vat?.value, false, true)}%` : '',
      },

      {
        title: 'EAN',
        dataIndex: 'ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
        render: (dom, record) => {
          return (
            <a
              onClick={async () => {
                let urlKey = record?.mag_url?.value;
                if (!urlKey)
                  urlKey = await dsGetCustomAttribute(record?.id || 0, {
                    force_update: 0,
                    attribute_code: 'url_key',
                  }).catch((e) => {
                    message.error('Not found SKU on the shop.');
                  });

                if (urlKey) {
                  window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                }
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Single EAN',
        dataIndex: ['parent', 'ean'],
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.parent?.ean, prevRecord.parent?.ean),
      },
      {
        title: '-',
        dataIndex: ['mag_url', 'value'],
        valueType: 'text',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 50,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.mag_url, prevRecord.mag_url),
        render: (dom, record) => {
          return (
            <Space>
              <LinkOutlined
                // style={{ color: record?.mag_url?.value ? 'green' : 'c-lightgrey' }}
                title="Go to shop."
                className={record?.mag_url?.value ? 'green' : 'c-lightgrey'}
                onClick={async () => {
                  let urlKey = record?.mag_url?.value;
                  if (!urlKey)
                    urlKey = await dsGetCustomAttribute(record?.id || 0, {
                      force_update: 0,
                      attribute_code: 'url_key',
                    }).catch((e) => {
                      message.error('Not found SKU on the shop.');
                    });

                  if (urlKey) {
                    window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                  }
                }}
              />
              <Dropdown
                key="social-links-menu"
                overlay={
                  <Menu
                    items={[
                      {
                        key: 'all',
                        label: <SocialLinks ean={record.ean || ''} />,
                      },
                    ]}
                  />
                }
              >
                <a onClick={(e) => e.preventDefault()}>
                  <DownOutlined />
                </a>
              </Dropdown>
            </Space>
          );
        },
      },
      {
        title: 'Qty/case',
        dataIndex: 'attr_case_qty',
        valueType: 'digit',
        sorter: true,
        align: 'right',
        hideInSearch: true,
        width: 60,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.attr_case_qty, prevRecord.attr_case_qty),
        render: (dom, record) => Util.numberFormat(record.attr_case_qty),
      },
      {
        title: 'SKU',
        dataIndex: 'sku',
        sorter: true,
        ellipsis: true,
        hideInSearch: true,
        width: 100,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.sku, prevRecord.sku),
        render: (dom, record) => {
          return (
            <a
              href={sUrlByTpl(getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE), {})}
              target="_blank"
              rel="noreferrer"
              title="Go to Shop Admin"
            >
              <Typography.Text copyable={{ text: record.sku || '' }}>{`${record.sku || ''}`}</Typography.Text>
            </a>
          );
        },
      },
      {
        title: 'Categories',
        dataIndex: ['categories'],
        width: 80,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record) => {
          let categories = record?.categories || [];
          if (!record?.note?.catMode) {
            categories = [...(record?.categories || []), ...(record.item?.categories || [])] ?? [];
            categories = _.uniqBy(categories, 'id');
          }
          const categoriesStr = categories.map((x) => x.name).join(', ');
          return (
            <a
              onClick={() => {
                setCurrentRow({ ...record });
                handleUpdateCategoriesModalVisible(true);
              }}
              title={categoriesStr}
            >
              {categoriesStr || <div>&nbsp;</div>}
            </a>
          );
        },
      },
      {
        title: 'Task',
        dataIndex: ['ean_tasks_count'],
        width: 40,
        align: 'center',
        hideInSearch: true,
        className: 'cursor-pointer',
        render: (__, record) => <TaskIcon count={record.ean_tasks_count} />,
        onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentRow({ ...record });
              setVisibleEanTasksModal(true);
            },
          };
        },
      },
      {
        title: 'Latest BP',
        dataIndex: ['latest_ibo', 'price'],
        width: 100,
        align: 'center',
        hideInSearch: true,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.latest_ibo, prevRecord.latest_ibo),
        tooltip: 'Click to view history. Lates BP > XLS Price: Red, Lates BP < XLS Price: Green',
        render: (__, record) => {
          const vat = record.item?.vat?.value || 0;
          const isSingle = record.is_single;

          const priceStyle: CSSProperties = {};
          const priceXls = sn(record.priceInXlsFile, 2);
          const latestIboPrice = sn(record?.latest_ibo?.price, 2);

          let priceColTooltip: string = '';
          if (record.idInXlsFile) {
            if (priceXls > latestIboPrice) {
              priceStyle.color = 'red';
            } else {
              if (priceXls < latestIboPrice) {
                priceStyle.color = 'green';
              }
            }

            priceColTooltip = `EAN pcs price: ${nf2(priceXls)}${EURO}`;
          }

          return (
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12} title={priceColTooltip}>
                <SPrices price={latestIboPrice} vat={vat} style={priceStyle} />
              </Col>
              {!isSingle && (
                <Col span={12}>
                  <SPrices price={latestIboPrice * (record?.attr_case_qty ?? 0)} vat={vat} style={priceStyle} />
                </Col>
              )}
            </Row>
          );
        },
      },
      {
        title: 'Price in XLS',
        tooltip: (
          <div className="text-sm">
            Click to open EAN Price page on the new tab.
            <br />
            Red: Round(Value,2) &gt; LastBuying(Value,2)
            <br />
            Green: Round(Value,2) &lt; LastBuying(Value,2)
          </div>
        ),
        dataIndex: ['ps_price'],
        width: 65,
        render: (__, recordM) => {
          const record: API.Ean = recordM.is_single ? recordM : recordM.parent || {};
          const existsMinPriceInXls = record.ps_price && record.ps_supplier_id;

          if (!existsMinPriceInXls) return <span className="text-sm c-grey">n/a</span>;

          const latestIboPrice = sn(recordM?.latest_ibo?.price, 2);
          const psPriceInXls = sn(record.ps_price, 2);

          let cls = '';
          if (latestIboPrice) {
            if (psPriceInXls > latestIboPrice) cls = 'bg-light-red';
            else if (psPriceInXls < latestIboPrice) cls = 'bg-green3';
          }

          return (
            <Space direction="vertical" size={0}>
              <span className={cls}>{nf2(record.ps_price)}</span>
              <span className="text-sm c-grey">
                {record.ps_supplier_id ? `${record.ps_supplier_id} - ${record.ps_supplier_name}` : ''}
              </span>
            </Space>
          );
        },
        onCell: (recordM) => {
          const record: API.Ean = recordM.is_single ? recordM : recordM.parent || {};
          const existsMinPriceInXls = record.ps_price && record.ps_supplier_id;

          if (existsMinPriceInXls) {
            return {
              className: 'cursor-pointer',
              onClick: (e) => {
                window.open(`/item/ean-all-prices?trademarkId=${recordM.item?.trademark_id}`);
              },
            };
          } else {
            return {};
          }
        },
      },
      {
        title: 'XLS Price',
        dataIndex: ['priceInXlsFile'],
        width: 100,
        align: 'center',
        hideInSearch: true,
        hideInTable: !supplierXlsFileId,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.priceInXlsFile, prevRecord.priceInXlsFile),
        render: (__, record) => {
          const vat = record.item?.vat?.value || 0;
          const isSingle = record.is_single;
          const priceXls = sn(record.priceInXlsFile, 2);

          return (
            <Row gutter={4} style={{ minHeight: 24 }}>
              <Col span={12}>
                <SPrices price={priceXls} vat={vat} />
              </Col>
              {!isSingle && (
                <Col span={12}>
                  <SPrices price={priceXls * (record?.attr_case_qty ?? 0)} vat={vat} />
                </Col>
              )}
            </Row>
          );
        },
      },
      {
        title: 'Qty',
        dataIndex: ['stock_mix_qty'],
        width: 80,
        hideInSearch: true,
        className: 'cursor-pointer',
        tooltip: 'Qty in Stock Warehouse. Please click to view stock details. Italic --> blocked qty.',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record) => {
          return (
            <StockQtyComp
              availableQty={record?.stock_mix_qty}
              blockedQty={record?.stock_mix_qty_b}
              is_single={record.is_single}
            />
          );
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: (ev: any) => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      ...pricesColDefs,
      ...(ScrapSystemIds.map((scrapName, ind) => ({
        title: `${scrapName} price`,
        dataIndex: ['scrap_prices', ind, 'price'],
        width: 80,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer',
        shouldCellUpdate: (record: API.Ean, prevRecord: API.Ean) =>
          !_.isEqual(record.scrap_prices, prevRecord.scrap_prices),
        render: (__, record: API.Ean) => {
          const vat = record.item?.vat?.value || 0;
          const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });

          return (
            <>
              <Row gutter={4} style={{ minHeight: 24 }}>
                <Col span={12} className="text-sm italic c-grey" style={{ paddingTop: 3 }}>
                  {!record?.is_single && nf2(scrapPriceObj?.price)}
                </Col>
                <Col span={12}>
                  <SPrices
                    price={casePrice(scrapPriceObj?.price, record.attr_case_qty)}
                    vat={vat}
                    hideNet
                    isGross
                    direction="horizontal"
                  />
                </Col>
              </Row>
            </>
          );
        },
        onCell: (record: API.Ean) => {
          const scrapPriceObj = _.find(record.scrap_prices, { system: scrapName });
          return {
            onClick: (ev: any) => {
              if (scrapPriceObj?.link) {
                window.open(scrapPriceObj?.link, '_blank');
              }
            },
            className: 'cursor-pointer ' + getColClassByPrices(record, [{ id: 1 }], scrapName),
          };
        },
      })) as ProColumns<API.Ean>[]),
      {
        title: 'Updated on',
        sorter: true,
        dataIndex: 'updated_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        className: 'text-xs c-grey',
        width: 100,
        renderFormItem: (item, { type, defaultRender }) => {
          return defaultRender(item);
        },
        render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
      },

      {
        title: 'ID',
        dataIndex: 'id',
        width: 50,
        search: false,
        sorter: true,
        align: 'center',
        className: 'text-xs c-grey',
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 110,
        shouldCellUpdate(record, prevRecord) {
          return false;
        },
        render: (dom, record, index) => {
          const options = [
            <a
              key="update-item"
              title="Update item"
              onClick={() => {
                handleUpdateItemModalVisible(true);
                setCurrentRow({ ...record });
              }}
            >
              <SnippetsOutlined className="btn-gray" />
            </a>,
            <a
              key="texts"
              title="Update texts"
              onClick={() => {
                handleUpdateTextsModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <FileTextOutlined />
            </a>,
            <a
              key="config"
              title="Update attributes"
              onClick={() => {
                handleUpdateModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <EditTwoTone />
            </a>,
            <a
              key="files"
              title="Update pictures"
              onClick={() => {
                handleUpdatePicturesModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <PictureOutlined />
            </a>,
            <Popconfirm
              key="upsync"
              placement="topRight"
              title={
                <>
                  Are you sure you want to up sync the EAN？ <br />
                  <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
                </>
              }
              overlayStyle={{ width: 350 }}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                if (!record.id) return;
                handleUpSync(record.id);
              }}
            >
              <CloudUploadOutlined className="btn-gray" />
            </Popconfirm>,
          ];
          return <Space>{options.map((option) => option)}</Space>;
        },
      },
    ],
    [getDictByCode, handleUpSync, pricesColDefs, supplierXlsFileId],
  );

  useEffect(() => {
    setColumns(orgColumns);
  }, [orgColumns]);

  const handleResize =
    (index: number) =>
    (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const newColumns = [...columns];
      newColumns[index] = {
        ...newColumns[index],
        width: size.width,
      };
      setColumns(newColumns);
    };

  const mergeColumns: any /* ProColumns<API.Ean>[] */ = (columns ?? []).map((col, index) => ({
    ...col,
    onHeaderCell: (column: ProColumns<API.Ean>) => ({
      width: column.width,
      onResize: handleResize(index) as React.ReactEventHandler<any>,
    }),
  }));

  // vats
  const vats = appSettings.vats;

  // Category trees
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  const handleExportMenuClick: MenuProps['onClick'] = async (e) => {
    setLoadingExport(true);
    const hide = message.loading('Exporting...');
    const formValues = searchFormRef.current?.getFieldsValue();
    switch (e.key) {
      case 'export-core':
        exportEanList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-core-alt':
        exportEanListAlt(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-price':
        exportEanPriceList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'sync-sku':
        break;
    }
  };

  useEffect(() => {
    getImportACList({ is_buying_active: 1 })
      .then((res) => setFiles(res))
      .catch(Util.error);

    setSupplierXlsFileId(sn(Util.getSfValues('sf_ean_grid_all')?.supplierXlsFileId));
  }, []);

  const { pageTitle } = usePageContainerTitle(eanComponentProps.route);

  const { filterId: filter_id, renderedEle } = useEanSpecialFilter(loading, 'all');

  useEffect(() => {
    actionRef.current?.reload();
  }, [filter_id]);

  return (
    <PageContainer
      className={styles.eanListContainer}
      title={
        <>
          {pageTitle}
          {renderedEle}
        </>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues(
            'sf_ean_grid_all',
            {
              status: 1,
              min_stock_qty: -999,
            },
            { sku: location.query?.sku ?? undefined },
          )}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { disabled: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Popconfirm
                  key="export-xls"
                  icon={false}
                  title={
                    <div id="eas-export">
                      <ProFormSelect
                        showSearch
                        placeholder="Select a supplier"
                        width={300}
                        request={async (params) => {
                          const res = await getSupplierList(
                            { ...params, pageSize: 100, forDropdown: true },
                            { name: 'ascend' },
                            {},
                          );
                          if (res && res.data) {
                            const tmp = res.data.map((x: API.Item) => ({
                              label: `${x.id} - ${x.name}`,
                              value: x.id,
                            }));
                            return tmp;
                          }
                          return [];
                        }}
                        fieldProps={{
                          onChange(value, option) {
                            exportSupplierRef.current = value;
                          },
                          getPopupContainer: (props) => document.getElementById('eas-export') as any,
                        }}
                        name="by-supplier"
                      />
                    </div>
                  }
                  okText="Export"
                  cancelText="Cancel"
                  placement="left"
                  onConfirm={() => {
                    const hide = message.loading(`Downloading...`, 0);
                    const formValues = searchFormRef.current?.getFieldsValue();
                    exportEanWithCodeList(
                      { ...formValues, supplierId: exportSupplierRef.current },
                      tableConfig?.sorter,
                      tableConfig?.filters,
                    )
                      .then((res) => {
                        if (res) {
                          const downloadUrl = `${API_URL}${(res as any).data.file}`;
                          window.location.href = downloadUrl;
                        }
                      })
                      .catch(Util.error)
                      .finally(() => {
                        hide();
                      });
                  }}
                >
                  <Button icon={<FileExcelOutlined />}>Export</Button>
                </Popconfirm>,
                <Dropdown
                  key="export-menu"
                  disabled={loadingExport}
                  overlay={
                    <Menu
                      onClick={handleExportMenuClick}
                      items={[
                        {
                          label: 'Download EANs',
                          key: 'export-core',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          label: 'Download prices',
                          key: 'export-price',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          type: 'divider',
                        },
                        {
                          label: 'Download As Excel',
                          key: 'export-core-alt',
                          icon: <DownloadOutlined type="primary" />,
                        },
                      ]}
                    />
                  }
                >
                  <Button loading={loadingExport}>
                    <Space>
                      {!loadingExport && <DownloadOutlined type="primary" />}
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>,
              ];
            },
          }}
        >
          {eanTypeProp == 'default' && (
            <ProFormSelect
              name="ean_type_search"
              placeholder="Select type"
              label="Type"
              options={[
                { value: '', label: 'All' },
                { value: 'base', label: 'Single' },
                { value: 've', label: 'Multi' },
              ]}
              fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
            />
          )}
          {/* <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} /> */}
          {/* <ProFormText name={'ft_name1'} label="Name" width={180} placeholder={'Search by keywords'} /> */}
          <ProFormText name={'name2'} label="Name" width={180} placeholder={'Search by keywords'} />
          {/* <ProFormSelect
            name="producers[]"
            label="Producers"
            placeholder="Please select producers"
            mode="multiple"
            request={getProducerListSelectOptions}
            width={180}
          /> */}
          {formElements}
          <ProFormCheckbox name="noTrademark" label="No Trademark?" />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 2, label: 'Active or Exist Stable Price' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
          />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormSelect
            name="create_type"
            placeholder="All"
            label="Creation mode"
            options={[
              { value: '', label: 'All' },
              { value: '1', label: 'Manually' },
              { value: '2', label: 'Imported' },
            ]}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormGroup size="small">
            <SDatePicker name="created_on_start" label="Date of creation" />
            <SDatePicker name="created_on_end" addonBefore="~" />
            {formElementsIbom}
            <SProFormDigit
              name={'min_stock_qty'}
              label="Min. Stock Qty"
              width={80}
              placeholder={'Min. Stock Qty'}
              min={Number.MIN_SAFE_INTEGER}
              allowClear
              fieldProps={{ controls: false }}
            />
            <ProFormSelect
              name="product_website_filter"
              label="Websites"
              width={130}
              placeholder={'Websites'}
              options={[
                { value: 'FS_ONE_YES', label: 'FS_ONE (Yes)' },
                { value: 'FS_ONE_NO', label: 'FS_ONE (No)' },
                { value: 'GFC_YES', label: 'GFC (Yes)' },
                { value: 'GFC_NO', label: 'GFC (No)' },
                { value: 'RS_YES', label: 'RS (Yes)' },
                { value: 'RS_NO', label: 'RS (No)' },
              ]}
            />
            {/* <ProFormSelect
              name="product_websites"
              label="Websites"
              width={130}
              mode="multiple"
              placeholder={'Websites'}
              options={appSettings.storeWebsites
                ?.filter((x) => x.code != 'admin')
                ?.map((x) => ({
                  value: `${x.id}`,
                  label: x.name,
                }))}
            /> */}
            <ProFormSelect
              showSearch
              placeholder="Select"
              fieldProps={{
                dropdownMatchSelectWidth: 400,
                onChange(value, option) {
                  setSupplierXlsFileId(value);
                  actionRef.current?.reload();
                },
              }}
              tooltip="Lightgreen Background color indicates EANs existed in Xls File. Otherwise light orange."
              options={files}
              width="sm"
              name="supplierXlsFileId"
              label="Supplier Xls file"
            />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<API.Ean, API.PageParams>
        headerTitle={'EANs List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        onChange={handleTableChange}
        dataSource={datasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_grid_all_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) =>
          (record.is_single ? 'row-single' : 'row-multi') +
          (supplierXlsFileId ? (record.idInXlsFile ? ' bg-green3' : ' bg-light-orange2') : '')
        }
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_grid_all', searchFormValues);
          Util.setSfValues('sf_ean_grid_all_p', params);

          setLoading(true);
          return getEanList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
              ean_type: eanTypeProp || 'base',
              special_filter_id: filter_id,
              with: EAN_DEFAULT_SUMMARY_WITH,
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);
              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Ean) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={mergeColumns}
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Button
            type="primary"
            onClick={() => {
              const hide = message.loading('Exporting docx...', 0);
              exportEanList({ mode: 'docx', ids: selectedRows.map((x) => x.id).join(',') }, {}, {})
                .then((res) => {
                  message.success('Exported successfully.');
                  window.location.href = `${API_URL}/api/${res.data.url}`;
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
            style={{ marginRight: 64 }}
            icon={<FileWordOutlined />}
          >
            Export Docx
          </Button>

          <Button
            type="default"
            onClick={() => {
              handleVisibleAddOfferFormBulk(true);
            }}
          >
            Make Offer
          </Button>
          <Popover
            placement="top"
            title="Select status"
            trigger="click"
            open={openBulkStockStatusForm}
            onOpenChange={(visible) => {
              setOpenBulkStockStatusForm(visible);
            }}
            content={
              <ProForm<BulkStockStatusForm>
                formRef={bulkStockStatusFormRef}
                size="small"
                onFinish={async (values) => {
                  const hide = message.loading('Updating...', 0);
                  updateEanBatch({
                    mode: 'stockStatusBatch',
                    ids: selectedRows.map((x) => sn(x.id)),
                    stockStatus: values.status,
                    data: [],
                  })
                    .then((res) => {
                      setOpenBulkStockStatusForm(false);
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                    });
                }}
                submitter={{
                  searchConfig: { submitText: 'Update' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name="status"
                  label="Stock Status"
                  valueEnum={StockStableStatusOptionsKv}
                  required
                  rules={[
                    {
                      required: true,
                      message: 'Status is required',
                    },
                  ]}
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenBulkStockStatusForm(true)}
            >
              Update Stock Status
            </Button>
          </Popover>
          <Button
            type="primary"
            icon={<DollarOutlined />}
            onClick={() => {
              handleVisibleUpdatePriceFormBulk(true);
            }}
          >
            Set Prices
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateAttributesFormBulk(true);
            }}
          >
            Status & Websites
          </Button>
          <Button
            type="primary"
            icon={<TrademarkOutlined />}
            onClick={() => {
              handleVisibleUpdateTrademarkFormBulk(true);
            }}
          >
            Set Trademark
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateCategoriesFormBulk(true);
            }}
          >
            Categories
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateVatFormBulk(true);
            }}
          >
            Vat
          </Button>

          <Popconfirm
            title={<>Are you sure you want to up sync selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              setBatchUpSyncProgress(0);
              setBatchModalData({
                title: 'Batch Up Syncing...',
                desc: 'Batch UpSync is in progress. Please wait...',
              });
              handleBatchUpSyncModalVisible(true);

              const totalCount = selectedRows.length;
              const skip = 100 / totalCount;

              for (const x of selectedRows) {
                await usProductFull(Number(x.id))
                  .then((res) => {
                    setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
                  })
                  .catch((error) => {
                    Util.error(error);
                    handleBatchUpSyncModalVisible(false);
                    return;
                  });
              }

              setBatchUpSyncProgress(100);
              handleBatchUpSyncModalVisible(false);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync
            </Button>
          </Popconfirm>

          {ScrapSystemIds.map((id) => (
            <Popconfirm
              key={id}
              title={<>Are you sure you want to scrap prices from WoS?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ maxWidth: 350 }}
              onConfirm={async () => {
                setBatchUpSyncProgress(0);
                setBatchModalData({
                  title: `Getting prices from ${id}...`,
                  desc: 'Batch action is in progress. Please wait...',
                });
                handleBatchUpSyncModalVisible(true);

                const totalCount = selectedRows.length;
                const skip = 100 / totalCount;

                for (const x of selectedRows) {
                  await scrapWoSPrice(x.ean || '', id)
                    .then((res) => {
                      setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
                    })
                    .catch((error) => {
                      Util.error(error);
                      setTimeout(() => {
                        handleBatchUpSyncModalVisible(false);
                      }, 1000);
                      return;
                    });
                  await Util.waitTime(400);
                }

                setBatchUpSyncProgress(100);
                actionRef.current?.reload();
                message.success('Successfully updated!');
                setTimeout(() => {
                  handleBatchUpSyncModalVisible(false);
                }, 1000);
              }}
            >
              <Button type="default" icon={<SyncOutlined />}>
                {id} prices
              </Button>
            </Popconfirm>
          ))}
          <Popconfirm
            title={<>Are you sure you want to delete selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleEanRemoveBatch(selectedRows);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <UpdateAttributesFormBulk
        modalVisible={visibleUpdateAttributesFormBulk}
        handleModalVisible={handleVisibleUpdateAttributesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateAttributesFormBulk(false);
        }}
      />

      <UpdateCategoriesFormBulk
        modalVisible={visibleUpdateCategoriesFormBulk}
        handleModalVisible={handleVisibleUpdateCategoriesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        treeData={treeData}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateCategoriesFormBulk(false);
        }}
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async () => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateAttributeForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
        gdsn
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />

      <UpdateCategoriesForm
        modalVisible={updateCategoriesModalVisible}
        handleModalVisible={handleUpdateCategoriesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={currentRow || {}}
        treeData={treeData}
        onSubmit={async (values) => {
          setCurrentRow((prev) => ({
            ...prev,
            ...values,
          }));

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateCategoriesModalVisible(false);
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        handleNavigation={handleNavigation}
        initialValues={currentRow || {}}
        onSubmit={async (values) => {
          /* setCurrentRow((prev) => ({
            ...prev,
            ...values,
          })); */

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
        gdsn
      />

      <UpdatePicturesForm
        modalVisible={updatePicturesModalVisible}
        handleModalVisible={handleUpdatePicturesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={{
          id: currentRow?.id,
          parent_id: currentRow?.parent_id,
          files: currentRow?.files || [],
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          ean_texts: currentRow?.ean_texts,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePicturesModalVisible(false);
        }}
        gdsn
      />

      <UpdateItemForm.default
        modalVisible={updateItemModalVisible}
        handleModalVisible={handleUpdateItemModalVisible}
        initialValues={{
          ...currentRow?.item,
          eanId: currentRow?.id,
          ean: currentRow?.parent?.ean,
          sku: currentRow?.parent?.sku,
        }}
        handleNavigation={handleNavigation}
        onSubmit={async (value) => {
          setCurrentRow((prev) => ({ ...prev, item: { ...prev?.item, ...value } }));
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateItemModalVisible(false);
        }}
        gdsn
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          parent_id: currentRow?.parent_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          ean_texts: currentRow?.ean_texts,
          ean_text_de: currentRow?.ean_text_de,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <EanTasksModals
        modalVisible={visibleEanTasksModal}
        handleModalVisible={setVisibleEanTasksModal}
        reloadList={() => actionRef.current?.reload()}
        itemEan={{
          id: currentRow?.id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
      />

      <UpdateVatFormBulk
        modalVisible={visibleUpdateVatFormBulk}
        handleModalVisible={handleVisibleUpdateVatFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        vats={vats || []}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateVatFormBulk(false);
        }}
      />

      <UpdatePriceAttributeFormBulk
        modalVisible={visibleUpdatePriceFormBulk}
        handleModalVisible={handleVisibleUpdatePriceFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        supplierXlsFileId={supplierXlsFileId}
        onSubmit={async (values) => {
          // we skip upsync...
          // if (actionRef.current) {
          // await handleBatchUpSync();
          actionRef.current?.reload();
          // }
        }}
        onCancel={() => {
          handleVisibleUpdatePriceFormBulk(false);
        }}
      />

      <UpdateBulkForm
        modalVisible={visibleUpdateTrademarkFormBulk}
        handleModalVisible={handleVisibleUpdateTrademarkFormBulk}
        initialValues={{ selectedRows: selectedRows.map((x) => x.item || {}) }}
        onSubmit={async (value) => {
          actionRef.current?.reload();
        }}
        onCancel={() => {
          handleVisibleUpdateTrademarkFormBulk(false);
        }}
      />

      <AddOfferFormBulk
        modalVisible={visibleAddOfferFormBulk}
        handleModalVisible={handleVisibleAddOfferFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        supplierXlsFileId={supplierXlsFileId}
        onSubmit={async (values) => {
          //
        }}
        onCancel={() => {
          handleVisibleAddOfferFormBulk(false);
        }}
      />

      <Modal title={batchModalData.title} centered open={batchUpSyncModalVisible} closable={false} footer={null}>
        <p>
          <LoadingOutlined /> {batchModalData.desc ?? 'Batch UpSync is in progress. Please wait...'}
        </p>
        <Progress percent={batchUpSyncProgress} style={{ width: '100%' }} status="active" />
      </Modal>

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EanAllSummary;
