import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addIBOManagement } from '@/services/foodstore-one/IBO/ibo-management';
import { message } from 'antd';
import Util from '@/util';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import SDatePicker from '@/components/SDatePicker';
import { IbomOwner, IbomOwnerOptions } from '@/constants';

const handleAdd = async (fields: API.IBOManagement) => {
  const hide = message.loading('Adding...');
  const data = {
    ...fields,
    order_date: Util.dtToYMD(fields.order_date),
    received_date: Util.dtToYMD(fields.received_date),
  };
  try {
    await addIBOManagement(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IBOManagement) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Item Buying Order Management'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.IBOManagement);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        showSearch
        placeholder="Select a supplier"
        request={async (params) => {
          const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Supplier) => ({
              label: `${x.supplier_no} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        rules={[
          {
            required: true,
            message: 'Supplier is required',
          },
        ]}
        width="md"
        name="supplier_id"
        label="Supplier"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Order no is required',
          },
        ]}
        width="md"
        name="order_no"
        label="Order No"
      />
      <SDatePicker width="md" name="order_date" label="Order Date" />
      <SDatePicker width="md" name="received_date" label="Received Date" />
      <ProFormSelect
        width="md"
        name="owner"
        label="Owner"
        initialValue={IbomOwner.WHC}
        required
        options={IbomOwnerOptions}
        rules={[
          {
            required: true,
            message: 'Owner is required',
          },
        ]}
      />
      <ProFormTextArea width="lg" name="notes" label="Notes" />
    </ModalForm>
  );
};

export default CreateForm;
