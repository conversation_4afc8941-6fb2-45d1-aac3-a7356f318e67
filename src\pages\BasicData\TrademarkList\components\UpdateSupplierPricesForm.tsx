import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, Row, message } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import { ProFormList, ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { updateTrademark } from '@/services/foodstore-one/BasicData/trademark';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import SProFormDigit from '@/components/SProFormDigit';
import { DeleteOutlined } from '@ant-design/icons';

const handleUpdate = async (fieldsParam: FormValueType) => {
  const hide = message.loading('Updating...', 0);
  const fields = { ...fieldsParam, mode: 'partial' };
  /* if (fields?.supplier_price_settings) {
    if (fields.producers[0].id) {
      // @ts-ignore
      fields.producers = fields.producers.map((x) => x.id);
    }
  } */
  try {
    await updateTrademark(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Trademark>;

export type UpdateSupplierPricesFormProps = {
  initialValues?: Partial<API.Trademark>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Trademark) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateSupplierPricesForm: React.FC<UpdateSupplierPricesFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const suppliersListActionRef = useRef<FormListActionType>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Supplier Price Setting'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      initialValues={props.initialValues || {}}
      formRef={formRef}
      size="small"
      className="form-list-sm"
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormList
        actionRef={suppliersListActionRef}
        name={['supplier_price_settings']}
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: 'Add',
        }}
        creatorRecord={(ind: number) => {
          return {
            uid: Util.genNewKey(),
          };
        }}
        itemContainerRender={(doms, listMeta) => <div>{doms}</div>}
        deleteIconProps={{ tooltipText: 'Remove' }}
        copyIconProps={{ tooltipText: 'Copy row' }}
        actionRender={(field, action, doms) => [
          <Button
            key={'delete'}
            type="link"
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => {
              action.remove(field.key as number);
            }}
          />,
        ]}
        key={'uid'}
      >
        <Row gutter={8}>
          <Col span={16}>
            <ProFormSelect
              showSearch
              placeholder="Select a supplier"
              request={async (params) => {
                const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
                if (res && res.data) {
                  const tmp = res.data.map((x: API.Item) => ({
                    label: `${x.id} - ${x.name}`,
                    value: x.id,
                  }));
                  return tmp;
                }
                return [];
              }}
              name="supplier_id"
              label="Supplier"
              formItemProps={{ style: { marginBottom: 8 } }}
            />
          </Col>
          <Col span={8}>
            <SProFormDigit
              name={'price_percentage'}
              label="Price %"
              fieldProps={{ precision: 2, step: 1 }}
              formItemProps={{ style: { marginBottom: 8 } }}
              addonAfter={'%'}
            />
          </Col>
        </Row>
      </ProFormList>
    </ModalForm>
  );
};

export default UpdateSupplierPricesForm;
