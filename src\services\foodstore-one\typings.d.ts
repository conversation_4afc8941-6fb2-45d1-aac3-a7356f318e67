type Nullable<T> = T | null;

type JSONValue = Record<string, any>;

type ZeroOrOne = 0 | 1;

declare namespace FormProductEntry {
  type Item = {
    id?: number;
    name?: string;
    description?: string;
    eanList?: Ean[];
    suppliers?: API.Supplier[];
  };
  /** Ean */
  type Ean = {
    id?: number;
    item_id?: number;
    ean?: string;
    weight?: number;
    height?: number;
    width?: number;
    length?: number;
    attr_case_qty?: number;
    item?: Item;
  };
  /** WarehouseLocation */
  type WarehouseLocation = {
    id?: number;
    code?: string;
    name?: string;
    priority?: number;
  };

  type IBOManagement = {
    id?: number;
    supplier_id?: string;
    order_no?: string;
    order_date?: string;
    received_date?: string;
    owner?: string;
  };
}

declare namespace API {
  type BaseResult = {
    status?: 'success' | 'error' | 'info';
    code?: number;
    message?: any;
    messageFlash?: Record<string, any>;
  };

  type PaginatedResult<T> = {
    data: T[];
    totalRows?: T[];
    avgRow?: T;
    success: boolean;
    total: number;
    pagination?: any;
  };

  type ResultList<T> = Omit<BaseResult, 'message'> & {
    message: PaginatedResult<T>;
  };

  type ResultObject<T> = Omit<BaseResult, 'message'> & {
    message: T;
  };

  type Result<T> = Omit<BaseResult, 'message'> & {
    message: PaginatedResult<T>;
  };

  type Downloadable = {
    type?: string;
    key?: string;
    file?: string;
    url?: string;
    sqls?: any[];
    extra?: any; // extra data
  } & {
    b64?: string; // base 64 data
  };

  type ResultDownloadable = ResultObject<Downloadable>;

  type CurrentUser = {
    user_id?: number;
    username?: string;
    email?: string;
    name?: string;
    avatar?: string;
    status?: number;
    role?: number;
    created_on?: string;
    updated_on?: string;
    last_login_on?: string;
    nonce?: string;
    client_detail?: string;
    settings?: Record<string, any>;
    password?: string;
    confirmPassword?: string;

    initials?: string;
  };

  type Pagination = {
    totalRows: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
    hasMore?: boolean;
  };

  type LoginResult = {
    Authorization?: string;
    type?: string;
  };

  type UserListItem = CurrentUser;
  type UserList = {
    data: UserListItem[];
    success: boolean;
    total: number;
  };
  type userParams = {
    page?: number;
    perPage?: number;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
    keyword?: string;
    with?: string;
  } & Record<string, any>;

  type PageParamsExt = {
    current?: number;
    pageSize?: number;
    keyword?: string;
  } & Record<string, any>;

  /**
   * -------------------------------- Biz -----------------------------------------------
   */
  type Dict = {
    code?: string; //PK
    type?: string;
    label?: string;
    sort?: number;
    value?: any;
    status?: 0 | 1;
    settings?: Record<string, any>;
    desc?: string;
    parent_code?: string;
    casted_value?: any; // specific data

    parent?: Dict;
  } & { category_code?: string };

  type DictList = PaginatedResult<Dict>;

  type CountryRegion = {
    id?: number;
    country_code?: string;
    code?: string;
    default_name?: string;

    // relations
    country?: Country;
  };

  type CountryRegionList = PaginatedResult<CountryRegion>;

  type Country = {
    id?: number;
    code?: string;
    iso3_code?: string;
    name?: string;
    dial_code?: string;

    // relations
    country_regions?: CountryRegion[];
  };

  type CountryList = PaginatedResult<Country>;

  type HtmlFieldType =
    | 'number'
    | 'number2'
    | 'text'
    | 'textarea'
    | 'select'
    | 'switch'
    | 'multiselect'
    | 'checkbox'
    | 'radio'
    | 'date'
    | 'divider';

  type WarehouseLocation = {
    id?: number;
    code?: string;
    name?: string;
    priority?: number;
    address?: string;
  } & CreatorData &
    UpdaterData;

  type WarehouseLocationList = {
    data: WarehouseLocation[];
    success: boolean;
    total: number;
  };

  type ItemSupplierPageParams = {
    current?: number;
    pageSize?: number;
    keyword?: string;
    with?: string;
  };

  type Supplier = {
    id?: number;
    name?: string;
    supplier_no?: string;
    import_setting?: JSONValue & {
      input?: ImportSettingsInputType;
    };

    pivot?: {
      supplier_id?: number;
      item_id?: number; // with item table.
      ean_id?: number; // with item_ean table.
      product_no?: string;
    };
  } & CreatorData &
    UpdaterData;

  type EanSupplierPivot = {
    supplier_id?: number;
    ean_id?: number; // with item_ean table.
    product_no?: string;
  };

  type SupplierList = {
    data: Supplier[];
    success: boolean;
    total: number;
  };

  type Vat = {
    id?: number;
    name?: string;
    value?: number;
    magento_id?: number;
    status?: number;
  };

  type VatList = {
    data: Vat[];
    success: boolean;
    total: number;
  };

  type PriceType = {
    id?: number;
    name?: string;
    status?: number;
  };

  type PriceTypeList = {
    data: PriceType[];
    success: boolean;
    total: number;
  };

  type TrademarkGroup = {
    id?: number;
    name?: string;
  } & {
    trademarks?: Trademark[];
  }

  type TrademarkGroupList = PaginatedResult<TrademarkGroup>;

  type Trademark = {
    id?: number;
    name?: string;
    path?: string;
    mag_id?: number;
    group_id?: number | null;
    name_pdf?: string;
    sort_pdf?: number;
    logo_file_id?: number;
  } & {
    // relations
    producers?: Producer[];
    supplier_price_settings?: TrademarkSupplierPriceSetting[];
    logo_file?: File;
    group?: TrademarkGroup;
  } & {
    qty?: number; // used in dropdown list
  };

  type TrademarkList = {
    data: Trademark[];
    success: boolean;
    total: number;
  };

  type TrademarkSupplierPriceSetting = {
    trademark_id: number;
    supplier_id: number;
    price_percentage: number;
  } & {
    trademark?: Trademark;
    supplier?: Supplier;
  } & {
    uid?: string;
    supplier_name?: string;
  };

  type Producer = {
    id?: number;
    name?: string;
    street?: string;
    zip?: string;
    city?: string;
    country?: string;
    full_name?: string;
    trademarks?: Trademark[];
  };

  type ProducerList = {
    data: Producer[];
    success: boolean;
    total: number;
  };

  type Category = {
    id?: number;
    name?: string;
    parent_id?: number;
    foreign_id?: number;
    sort?: number;
  };

  type CategoryList = {
    data: Category[];
    success: boolean;
    total: number;
  };

  type CategoryTree = {
    key?: number;
    title?: string;
    sort?: number;
    children?: CategoryTree[];
  };

  type CategoryTreeList = {
    data: CategoryTree[];
    success: boolean;
    total: number;
  };

  type EanPrice = {
    ean_id?: number;
    price_type_id?: number;
    price?: number;
    min_qty?: number;
    currency?: string;
    start_date?: string;
    end_date?: string;

    price_type?: PriceType;

    // dummy
    id?: number;
  };

  type EanPriceStable = {
    id?: number;
    cur_import_id?: number;
    last_import_id?: number;
    ean?: string;
    cur_price?: number;
    last_price?: number;
    created_on?: string;
    updated_on?: string;
    last_updated_on?: string;
    is_deleted?: number;
    deleted_on?: string;
    case_qty?: number;
    ve_pallet?: number;
    exp_date?: string;
    shelf_life?: number;
    hs_code?: string;
    detail?: string | Record<string, any>;
  } & {
    cur_import?: Import;
    last_import?: Import;
    item_ean?: Ean;
  }

  type EanPriceList = {
    data: EanPrice[];
    success: boolean;
    total: number;
  };

  type EanTextSettings = {
    useOwnText?: boolean; // use own Text or inherit parent text?
  };

  type EanText = {
    ean_id?: number;
    lang?: string;
    name?: string;
    internal_short_name?: string; // Internal usage only.
    name_cat?: string;
    short_description?: string;
    description1?: string;

    meta_title?: string;
    meta_keywords?: string;
    meta_description?: string;
    // description2?: string;
    // ingredients?: string;

    official_title?: string;
    official_title_ebay?: string;
    official_producer?: number; // producer ID.
    official_producer_gdsn?: string; // GDSN producer contact: name & address
    official_producer_obj?: Producer;
    official_country?: string;
    official_ingredients?: string;
    official_ingredients_pic?: number;
    official_nutrition?: string;
    official_nutrition_pic?: number;
    official_usage?: string;
    official_warning?: string;
    nutrition_pic?: File;
    ingredients_pic?: File;
    fs_ebay_title_special?: string;
    fs_ebay_title_special_alt?: string;
    fs_export_google_description?: string;
    name_pdf?: string;
    settings?: EanTextSettings;

    // virtual column for ts
    i_pic?: File[];
    n_pic?: File[];
  };

  type EanTextList = {
    data: EanText[];
    success: boolean;
    total: number;
  };

  // EanFile: Pivot's type
  type EanFile = {
    ean_id?: number;
    file_id?: number;
    is_main?: number;
    types?: string;
    us_modes?: number[];
    label?: string;
    position?: number;
    is_bio?: number | boolean;
    mag_id?: number;
    is_parent_file?: number;
  };

  type File = {
    id?: number;
    file_name?: string;
    clean_file_name?: string;
    path?: string;
    org_path?: string;
    size?: number;
    type?: string;
    created_at?: string;
    updated_at?: string;

    // pivot
    pivot?: EanFile;
  } & UploadFile;

  type FileList = {
    data: File[];
    success: boolean;
    total: number;
  };

  type IBOManagement = {
    id?: number;
    supplier_id?: string;
    order_no?: string;
    order_date?: string;
    received_date?: string;
    import_id?: number;
    notes?: string;
    // inherited values
    supplier_name?: string;
    supplier?: Supplier;
  } & CreatorData &
    UpdaterData & {
      BIOExists?: number | boolean;
      unbooked_count?: number;
      item_ids_w_o_trademark?: string;  // item Ids without trademark
      skus_w_o_price?: string;  // SKUs without price
      items_with_blocked_stock?: string;  // item Ids with blocked stock
      items_count_w_o_ibo_price?: number;
    };

  type IBOManagementList = {
    data: IBOManagement[];
    success: boolean;
    total: number;
  };

  /** Item */
  type Item = {
    id?: number;
    name?: string;
    description?: string;
    hs_code?: string;
    supplier_id?: any;
    trademark_id?: number;
    tmp_trademark?: string;
    shelf_life?: number;
    special_filter?: string | string[] | null;
    fs_bio_certificate?: string;  // global scope
    fs_bio_origin?: string;       // store scope

    suppliers?: Supplier[];
    trademark?: API.Trademark;
    vat?: Vat;
    categories?: Category[];
    item_eans?: Ean[];

    // virtual column
    mode?: 'categories';
    item_eans_count?: number;
  } & CreatorData &
    UpdaterData;

  type ItemList = {
    data: Item[];
    success: boolean;
    total: number;
  };

  /** ProductEntry */
  type ProductEntry = {
    id?: number;
    item_id?: number;
    ean_id?: number;
    ean?: string;
    attr_case_qty?: number;
    pkg_qty?: number;
    qty?: number;
    exp_date?: string;
    warehouse_location_id?: number;
    ibom_id?: number;
    wl?: WarehouseLocation;
    item?: Item;
    ean_detail?: Ean;
    ibom?: IBOManagement;
    item_ean?: Ean;
    product_entry_details?: ProductEntryDetail[];
  } & CreatorData &
    UpdaterData;

  type ProductEntryList = {
    data: ProductEntry[];
    success: boolean;
    total: number;
  };

  /** ProductEntryDetail */
  type ProductEntryDetail = {
    id?: number;
    product_entry_id?: number;
    item_id?: number;
    ean_id?: number;
    ean?: string;
    attr_case_qty?: number;
    pkg_qty?: number;
    qty?: number;
    price?: number;
    exp_date?: string;
    warehouse_location_id?: number;
    ibom_id?: number;
    wl?: WarehouseLocation;
    item?: Item;
    ean_detail?: Ean;
    ibom?: IBOManagement;
    product_entry?: ProductEntry;
  } & CreatorData &
    UpdaterData;

  type ProductEntryDetailList = {
    data: ProductEntryDetail[];
    success: boolean;
    total: number;
  };

  /** Item Supplier */
  type ItemSupplier = {
    key?: string; // Generated key by {itemId}-{supplierId}
    id?: number;
    name?: string;
    description?: string;
    supplier_id?: number;
    supplier_name?: string;
  } & CreatorData &
    UpdaterData;

  type ItemSupplierList = {
    data: ItemSupplier[];
    success: boolean;
    total: number;
  };

  type MagCustomAttribute = {
    attribute_code?: string;
    value?: any;
  };

  // specific: API error message
  type UpSyncData = {
    upSyncMessage?: string;
  };

  type EanNote = {
    catMode?: boolean; // false: both usage, true: own EAN's category
    usAllowance?: number; // Up Sync allowance setting
  };

  /** Ean */
  type Ean = {
    id?: number;
    item_id?: number;
    parent_id?: number;
    ean?: string;
    ean_ebay?: number;
    sku?: string;
    status?: number;
    not_deliverable?: number;
    weight?: number;
    height?: number;
    width?: number;
    length?: number;
    attr_case_qty?: number;
    minimum_order_qty?: number;
    item_base?: number;
    item_base_unit?: string;
    'item.name'?: string;

    attribute_set_code?: string;
    product_type?: string;
    visibility?: string;
    product_online?: string;
    product_websites?: string | [] | string[];
    new_from_date?: string;
    new_to_date?: string;
    note?: EanNote | Record<string, any>;

    fs_ebay_price_std?: number;
    fs_ebay_price_special?: number;
    fs_ebay_exp_special?: string;

    fs_special_discount?: string | null;
    fs_special_badge?: string | null;
    fs_special_badge2?: string | null;

    virtual_stock_qty_gfc?: number; // For GFC virtual
    mag_inventory_stocks_quantity_gfc?: number; // GFC stock qty on Magento

    item?: API.Item;
    parent?: Ean;
    ean_prices?: EanPrice[];
    ean_price_stables?: EanPriceStable[];
    ean_price_stables_deleted?: EanPriceStable[];
    ean_price_stable?: EanPriceStable;
    categories?: Category[];
    ean_texts?: EanText[];
    ean_text_de?: EanText; // shortcut of lang=DE
    mag_custom_attributes?: MagCustomAttribute[];
    files?: File[];
    latest_ibo?: Ibo;
    latest_own_ibo?: Ibo;
    mag_url?: { ean_id?: number; value?: string; attribute_code?: string };
    scrap_price?: ScrapPrice;
    scrap_prices?: ScrapPrice[];

    // virtual column
    mode?:
    | 'categories'
    | 'texts'
    | 'files'
    | 'pre-files'
    | 'm_status'
    | 'virtualQtyForGFC'
    | 'updateProductNo'
    | 'siblings.ean_text_de'
    | 'updateByGDSN'
    | 'settings.useOwnText'
    | 'general'
    | 'price';
    base_quantity_total?: number;
    quantity_total?: number;
    box_quantity_total?: number;

    siblings?: Ean[];
    siblings_multi?: Ean[];

    is_single?: boolean;
    gdsn?: boolean;
    fs_export_kaufland?: boolean;
    fs_export_idealo?: boolean;
    fs_export_billiger?: boolean;
    fs_export_google?: boolean;
    wish_qty?: number;
    wish_note?: string;

    // summary columns
    ibos_item_min_exp_date?: string;
    ibos_min_exp_date?: string;
    stock_movements_sum_box_qty?: number;
    stock_movements_sum_piece_qty?: number;
    parent_stock_movements_sum_total_piece_qty?: number;

    // stock stable
    stock_stables?: StockStable[];
    stock_stables_sum_box_qty?: number;
    stock_stables_sum_piece_qty?: number;
    stock_stables_min_exp_date?: string;

    parent_stock_stables?: StockStable[];
    parent_stock_stables_sum_total_piece_qty?: number;
    parent_stock_stables_sum_box_qty?: number;
    parent_stock_stables_min_exp_date?: string;

    mag_inventory_stocks_sum_quantity?: number;
    mag_inventory_stocks_sum_res_quantity?: number;
    mag_inventory_stocks_sum_res_cal?: number;

    upsync_qty?: number; // Up syncable number. Need to verify!
    stock_mix_qty?: number; // Mixed stock qty. Pcs for single or box for Multi!
    stock_mix_qty_b?: number; // Blocked mixed stock qty. Pcs for single or box for Multi!


    stock_movements_grouped?: any;

    ean_tasks?: EanTask[];
    ean_tasks_count?: number;

    m_status?: number; // Active: 1 or Passive: 2

    website_ids?: string;

    stock_movements_oldest_ibo?: Partial<StockMovement> & Partial<Ibo>;
  } & {
    ean_price_gfc?: EanPrice;
    mag_files?: Shop.MagProductFile[];
    ean_suppliers?: Supplier[];
    expecting_offer_items?: (OfferItem & Offer)[];
    open_quotation_qty?: number; // sum of open Quotation in Status Expecting items

    // ean_price_stable
    ps_price?: number;
    ps_import_id?: number;
    ps_supplier_id?: number;
    ps_supplier_name?: string;
    ps_updated_on?: string;

    // 3 earliest IBOs.
    ibo_earliest3?: {
      exp_date?: string;
      created_on?: string;
      booking_date?: string;
      qty?: number;
      mix_qty?: number;
      ids?: string;
      statuses?: string;
    }[];
    ibo_earliest3_item?: {
      exp_date?: string;
      created_on?: string;
      booking_date?: string;
      qty?: number;
      mix_qty?: number;
      ids?: string;
      statuses?: string;
    }[];
    ibo_pres?: IboPre[];
    ibo_pres_sum_qty?: number;
    ibos_item_min_price?: number;
  } & {
    gdsn_item?: ItemEanGdsn;
    mag_product?: MagProduct;
    latest_ibo_xls_data?: zImportSupplierDataTpl;
    latest_ibo_xls_data_ean?: zImportSupplierDataTpl;
    import_ean_disabled_list?: ImportEanDisabled[];
  } & {
    idInXlsFile?: number; // ID in selected supplier XLS file.
    priceInXlsFile?: number; // price in supplier Xls file.
  } & {
    // Pre IBO count related to eanStablePrice's current import
    ibo_pre_open_qty1?: number;
  } & {
    xls_case_qty?: number;
    xls_ve_pallet?: number;
    eps_xls_data?: zImportSupplierDataTpl;
  } & CreatorData &
    UpdaterData &
    UpSyncData;

  /** Ean List */
  type EanList = {
    data: Ean[];
    success: boolean;
    total: number;
  };

  type MagProduct = {
    id?: number;
    sku?: string;
    status?: number;
    not_deliverable?: number;
    attribute_set_id?: number;
    visibility?: number;
    name?: string;
    price?: number;
    weight?: number;
    website_ids?: number[];
    created_on?: string;
    updated_on?: string;
  } & {
    item_ean?: Ean;
  }

  type ItemEanGdsnDetail = {
    hs_code?: string;
    country_no?: string;
    country_code?: string;
    life_span_arrival?: string;
    life_span_production?: string;
    storage_instruction?: string;
    usage_instruction?: string;
    health_compulsory?: string;

    trademark?: string; // trademark
    ean_text?: {
      official_ingredients?: string;
      official_title?: string;
      official_nutrition?: string;
    };
    temp?: {
      code?: string;
      max?: number;
      min?: number;
      max_unit?: string;
      min_unit?: string;
    }[];
    vat?: {
      code?: string;
      id?: number;
      value?: number;
    };
    images?: string[];

    // Nutrient
    nutrient?: {
      preparationStateCode?: string;
      dailyValueIntakeReference?: string;
      dailyValueIntakeReference?: string;
      nutrientBasisQuantity?: {
        value?: string | number;
        measurementUnitCode?: string;
      };
    };
    nutrients?: {
      nutrientTypeCode?: string;
      dailyValueIntakePercent?: string;
      measurementPrecisionCode?: string;
      dailyValueIntakePercentMeasurementPrecisionCode?: string;
      quantityContained?: {
        value?: any;
        measurementUnitCode?: any;
      }[];
      diets?: {
        code?: string;
      }[];
    }[];

    net_content?: string | number;
    net_content_unit?: string;

    short_description?: string; // short description
    description1?: string; // long description
    tradeItemMarketingMessages?: string[]; // marketingInformation/tradeItemMarketingMessage
    tradeItemFeatureBenefits?: string[]; // marketingInformation/tradeItemFeatureBenefit
    tradeItemKeyWords?: string; // marketingInformation/tradeItemKeyWords
    brandMarketingDescription?: string; // marketingInformation/brandMarketingDescription
  } & Record<string, any>;

  type ItemEanGdsnDetail3 = {
    bio_origin?: string;
    bio_certificate?: string;
  };

  type ItemEanGdsnContactChannel = {
    code?: string;
    value?: string;
  };

  type ItemEanGdsnContact = {
    code?: string;
    name?: string;
    address?: string;
    channels?: ItemEanGdsnContactChannel[];
  };

  type ItemEanGdsn = {
    id?: number;
    ean?: string;
    package_ean?: string;
    case_qty?: number;
    name?: string;
    desc?: string;
    desc_short?: string;
    brand?: string;

    length?: number;
    width?: number;
    height?: number;
    item_base?: number;
    item_base_unit?: string;
    gross_weight?: number;
    net_weight?: number;

    gdsn_item_id?: number;
    sync_last_change_dt?: string;
    detail?: ItemEanGdsnDetail;
    detail3?: ItemEanGdsnDetail3;
    contact_xml?: string;
    contacts?: ItemEanGdsnContact[];
  };

  type ItemEanPriceHistory = {
    id?: number;
    ean_id?: number;
    price_type_id?: number;
    price?: number;
    created_on?: string;
    updated_on?: string;
    deleted_on?: string;
    detail?: {
      discount?: number, basePrice?: number, fs_special_badge?: string, discountLabel?: string;
    };
  } & {
    item_ean?: Ean;
  }

  /** IboDraft */
  type IboDraft = {
    id?: number;
    name?: string;
    type?: number;
    user_id?: number;
    ibom_id?: number;

    ibom?: IBOManagement;
    user?: User;
    ibo_draft_details?: IboDraftDetail[];
  } & CreatorData &
    UpdaterData;

  type IboDraftList = {
    data: IboDraft[];
    success: boolean;
    total: number;
  };

  /** IboDraftDetail */
  type IboDraftDetail = {
    id?: number;
    ibo_draft_id?: number;
    ean_id?: number;
    case_qty?: number;
    box_qty?: number;
    qty?: number;
    exp_date?: string;
    ibom_id?: number;
    price?: number;
    warehouse_location_id?: number;
    ean?: string;
    parent_ean?: string;
    ean_name?: string;
    is_booked?: number;
    mark?: number; // 1: marked

    ibom?: IBOManagement;
    ibo?: Ibo;
    item_ean?: Ean;
    warehouse_location?: WarehouseLocation;
    ibo_draft?: IboDraft;
  } & {
    supplier_id?: number;
    product_no?: string;
  } & CreatorData &
    UpdaterData & {
      // detail info
      sold_pcs?: number;  // pcs qty for the related IBO. Refer to idx table
      sold_pcs_all?: number; // all pcs qty for this SKU.
      stock_pcs?: number;
      delivered_pcs?: number;
    }

  type IboDraftDetailList = {
    data: IboDraftDetail[];
    success: boolean;
    total: number;
  };

  type ImportSettingsInputType = {
    col_ean?: string;
    col_multi_ean?: string;
    col_price?: string;
    col_trademark?: string;
    col_hs_code?: string;
    col_name?: string;
    col_case_qty?: string;
    col_ignores?: string;
    col_shelf_life?: string;
    col_vat?: string;
    col_qty?: string;
    col_box_qty?: string;
    col_exp_date?: string;
    col_article_no?: string;
    col_maker?: string;
    col_gln?: string;
    col_uvp?: string;
    // @since 2023-12-08
    col_nan?: string;
    col_category?: string;
    col_price_pallet?: string;
    col_price_valid_from?: string;
    col_price_valid_to?: string;
    col_ve_pallet?: string;
    col_languages?: string;
    col_bbd?: string;

    data_start_row?: number;
    header_row?: number;

    // more setting for column usage
    calc_percentage?: number; // used to calculate price.
    calc_name?: string[]; // used for complex name concatenation.

    // Bezugsweg: Lager | Strecke
    filter_ref_path?: string;
  };

  /** Import */
  type Import = {
    id?: number;
    type?: number;
    status?: number;
    supplier_id?: number;
    supplier_add?: string;
    is_active?: number;
    is_buying_active?: number;
    is_master?: number;
    parent_id?: number;
    sort_buying?: number;
    table_name?: string;
    settings?: Record<string, any> & {
      input?: ImportSettingsInputType;
    };

    supplier?: Supplier;
    files?: File[];
    import_search_filters?: ImportSearchFilter[];
    parent?: Import;
  } & CreatorData &
    UpdaterData;

  type ImportList = {
    data: Import[];
    success: boolean;
    total: number;
  };

  type ImportSearchFilter = {
    id?: number;
    supplier_id?: number;
    trademark?: string;
    keyword_title?: string;
    is_all_supplier?: boolean;
  } & {
    supplier?: Supplier;
  };

  /**
   * Aggregated EAN info.
   */
  type ZImportSupplierXlsEan = {
    id?: number;
    ean?: string;
    multi_ean?: string;
    name?: string;
    article_no?: string;
    case_qty?: number;
    hs_code?: string;
    trademark?: string;
    vat?: number;
    price?: number;
    uvp?: number;
    price_pallet?: number;
    ve_pallet?: number;
    exp_date?: string;
    maker?: string;
    shelf_life?: string;
    gln?: string;
    price_valid_from?: string;
    price_valid_to?: string;
    bbd?: string;
    category?: string;
    nan?: string;
    import_id?: number;
    import_ref_id?: number;
  } & CreatorData & UpdaterData & {
    item_ean?: API.Ean;
    import?: API.Import;
    eanPriceStable?: API.EanPriceStable;
  };

  type ImportEanDisabled = {
    id?: number;
    ean?: string;
    supplier_add?: string;
    start_date?: string;
    end_date?: string;
  } & {
    item_ean?: Ean;
  }

  type zImportSupplierDataTpl = {
    id?: number; // PK in table.
    ean?: string;
    price?: number;
    article_no?: string;
    shelf_life?: number;
    uvp?: number;
  } & {
    ve_pallet?: number;
    case_qty?: number;
  };

  /** Ibo */
  type Ibo = {
    id?: number;
    ean_id?: number;
    qty?: number;
    box_qty?: number;
    warehouse_location_id?: number;
    ibom_id?: number;
    price?: number;
    ibo_draft_id?: number;
    ibo_draft_detail_id?: number;
    exp_date?: string;

    ibom?: IBOManagement;
    item_ean?: Ean;
    warehouse_location?: WarehouseLocation;
    ibo_draft?: IboDraft;
    stock_stables?: StockStable[];
    expecting_offer_items?: (OfferItem & Offer)[];
  } & {
    last_bp?: number;
    xls_data?: zImportSupplierDataTpl; // mapping by article_no
    xls_data_ean?: zImportSupplierDataTpl; // mapping by ean
  } & CreatorData &
    UpdaterData & {
      supplier_id?: number;
      product_no?: string;
    };

  type IboList = {
    data: Ibo[];
    success: boolean;
    total: number;
  };

  type IboPreManagement = {
    id?: number;
    supplier_id?: number;
    order_no?: number;
    status?: string;
    note?: string;
    note2?: string;
    inbound_no?: string;
    note_supplier?: string;
    note_customer?: string;
  } & CreatorData &
    UpdaterData & {
      supplier_name?: string;
      ibo_pres_sum?: number;  // not used
      ibo_pres_sum_with_note?: number; // not used
      packed_ready_qty_without_note?: number;// not used
      ibo_pres_count?: number;
      linked_count_with_offer?: number;
      linked_processing_count_with_offer?: number;
    } & {
      supplier?: Supplier;
      ibo_pres?: IboPre[];
      files?: File[];
      offer_ids?: string | number[]; // calc_value
      linked_offers?: Offer[]; // calc_value
    };

  type OfferItemPackedReadyListType = {
    ibo_pre_id?: number;
    offer_item_id?: number;
    ean_id?: number;
    case_qty?: number;
    qty?: number;
    exp_date?: string;
    updated_on?: string;
  }

  type IboPre = {
    id?: number;
    ibo_pre_management_id?: number;
    type?: number;  // Creation type: 1: normal, 2: by importing XLS 
    ean_id?: number;
    item_id?: number;
    supplier_id?: number;
    import_id?: number;
    case_qty?: number;
    qty?: number;
    price_xls?: number;
    status?: string;
    nan?: string;
    qty_pkg?: number;
    qty_pallet?: number;
    price_pallet?: number;
    note_delivered?: string;
    detail?: any;
    sent_date?: string;
    invoiced_date?: string;
  } & CreatorData &
    UpdaterData & {
      item?: Item;
      item_ean?: Ean;
      import?: Import;
      supplier?: Supplier;
      ibo_pre_management?: IboPreManagement;
      offer_items?: OfferItem[];
      offer_item_packed_ready_list?: OfferItemPackedReadyListType[];
      offer_item_packed_ready_list_sum_qty?: number;
    } & {
      product_no?: string;
      sum_qty?: number;
      sum_price?: number;
      xls_info?: {
        ean?: string;
        multi_ean?: string;
        case_qty?: string;
      }
    } & {
      offer_item_ids?: string;  // csv or null
      offer_item_ids?: string; // csv or null
      qty_pcs?: number;
      offer_ids?: string;
    };

  /** imported supplier data */
  type ImportedSupplierRow = {
    id?: number;
    ean?: string;
    multi_ean?: string;
    name?: string;
    price?: number;
    trademark?: string;
    hs_code?: string;
    vat?: string;
    qty?: number;
    box_qty?: number;
    case_qty?: number;
    shelf_life?: number;
    exp_date?: string;

    // included ones: virtual
    ean_exist?: number;
    multi_ean_exist?: number;

    // for editable: it's virtual
    name_sys?: string;
    item_name_sys?: string;

    // virtual new
    item_ean?: Ean;
  } & Record<string, any>;

  type ImportedSupplierRowList = {
    data: ImportedSupplierRow[];
    success: boolean;
    total: number;
  };

  type ImportedSupplierAndIbo = {
    id?: number;
    ean?: string;
    ean_sys?: string;
    name?: string;
    name_sys?: string;
    item_name_sys?: string;

    qty?: number;
    box_qty?: number;
    case_qty?: number;
    price?: number;
    price_g?: number;
    exp_date?: string;
    exp_date_g?: string;

    i_qty?: number;
    i_case_qty?: number;
    i_price?: number;
    i_exp_date_g?: string;
    i_price?: number;
    i_exp_date_g?: string;

    ibo_id?: number;
    xls_id?: number;

    // married
    marry_ibo_id?: number;
    marry_xls_id?: number;
    marry_ean_org?: string;
  } & Record<string, any>;

  type ImportedSupplierAndIboList = PaginatedResult<ImportedSupplierAndIbo>;

  // Married data type in z_import_supplier_marry table.
  type ImportSupplierMarry = {
    xls_id?: number;
    ibo_id?: number;
    ean?: string;
    imported_ean?: string;
    imported_table_name?: string;

    item_ean?: Ean;
    import?: Import;
    ibo?: Ibo;
  } & CreatorData &
    UpdaterData & {
      import_id?: number;
    };

  type ImportSupplierMarryList = PaginatedResult<ImportSupplierMarry>;

  type StockMovement = {
    id?: number;
    item_id?: number;
    ean_id?: number;
    ean?: string;
    parent_ean_id?: number;
    parent_ean?: string;

    old_wl_id?: number;
    old_wl_name?: string;
    new_wl_id?: number;
    new_wl_name?: string;

    piece_qty?: number;
    box_qty?: number;
    case_qty?: number;
    total_piece_qty?: number;

    exp_date?: string;
    owner?: string;

    reason?: string;
    reason_text?: string;
    ref_type?: string & 'IBO' | 'StockStb' | 'OrderItem';
    ref_id?: number;
    batch_code?: number;

    // relations:
    item?: Item;
    item_ean?: Ean;
    parent_item_ean?: Ean;
    old_warehouse_location?: WarehouseLocation;
    new_warehouse_location?: WarehouseLocation;
  } & CreatorData & {
    // ref related columns
    ref_order_id?: number;
    ref_increment_id?: number;
    ref_ibom_id?: number;
    ref_ibom_order_no?: number;
  };

  type StockMovementList = PaginatedResult<StockMovement>;

  type StockStable = {
    id?: number;
    item_id?: number;
    ean_id?: number;
    parent_ean_id?: number;
    wl_id?: number;

    piece_qty?: number;
    box_qty?: number;
    status?: number;
    case_qty?: number;
    total_piece_qty?: number;

    exp_date?: string;
    ibo_id?: number;

    // relations:
    item?: Item;
    item_ean?: Ean;
    parent_item_ean?: Ean;
    warehouse_location?: WarehouseLocation;
    ibo?: Ibo;
  } & CreatorData &
    UpdaterData & { single_piece_qty?: number } & {
      piece_qty_old?: number;
      box_qty_old?: number;
      is_no_stock?: boolean;
      remainingQty?: number; // frontend usage only.
    } & {
      // virtual attributes for frontend.
      order_item?: OrderItem;
    };

  type StockStableList = PaginatedResult<StockStable>;

  type StockStableProblem = {
    id?: number;
    stock_stable_id?: number;
    item_id?: number;
    ean_id?: number;
    parent_ean_id?: number;
    wl_id?: number;

    piece_qty?: number;
    box_qty?: number;
    case_qty?: number;
    total_piece_qty?: number;

    exp_date?: string;

    order_id?: number;
    order_item_id?: number;
    sku?: string;
    qty_missing?: number;
    qty_ordered?: number;
    picklist_id?: number;

    status?: string;
    note?: string;
    detail?: string | Record<string, any>;
  } & CreatorData & UpdaterData & {
    // relations
    item?: Item;
    item_ean?: Ean;
    parent_item_ean?: Ean;
    warehouse_location?: WarehouseLocation;
    ibo?: Ibo;
    warehouse_picklist?: WarehousePicklist;
    stock_stable?: StockStable;
  }

  type StockStableBooked = {
    id?: number;
    item_id?: number;
    ean_id?: number;
    parent_ean_id?: number;
    wl_id?: number;
    piece_qty?: number;
    box_qty?: number;
    case_qty?: number;
    total_piece_qty?: number;
    exp_date?: string;
    ibo_id?: number;
    status?: number;
    order_id?: number;
    order_item_id?: number;
    sku?: string;
    qty_ordered?: number;
    qty_packed?: number;
    stock_stable_id?: number;
    picklist_id?: number;
    returned?: number;
    detail?: Record<string, any>;
  } & CreatorData & {
    // relations
    warehouse_location?: WarehouseLocation;
    stock_stable?: StockStable;
    item_ean?: Ean;
    ibo?: Ibo;
  } & {
    // virtual attributes for frontend.
    order_item?: OrderItem;
  };

  type AggregatedStockMovement = {
    is_single?: boolean;
    total_piece_qty_per_item?: number;
    sys_status?: number;
  };

  type MagInventoryStock = {
    sku?: string;
    source?: string;
    id?: number;
    item_id?: number;
    case_qty?: number;
    quantity?: number;
    res_quantity?: number;
    res_cal?: number;
    status?: number;
  };
  type StockCompare = StockMovement &
    MagInventoryStock &
    AggregatedStockMovement & {
      virtual_stock_qty_gfc?: number; // For GFC virtual
      mag_inventory_stocks_quantity_gfc?: number; // GFC stock qty on Magento
    } & {
      vat_value?: number;
      m_status?: number;
      website_ids?: string;
      m_website_ids?: string;
      sys_price1?: number;
      m_price1?: number;
      sys_price2?: number;
      m_price2?: number;
      sys_price3?: number;
      m_price3?: number;
    };

  type StockCompareList = PaginatedResult<StockCompare>;

  type StoreConfig = {
    id?: number;
    code?: string;
    website_id?: number;
    locale?: string;
    base_currency_code?: string;
    default_display_currency_code?: string;
    timezone?: string;
    weight_unit?: string;
    base_url?: string;
    base_link_url?: string;
  };

  type StoreConfigList = PaginatedResult<StoreConfig>;

  type OrderAddress = {
    address_type?: 'billing' | 'shipping' | string;
    city?: string;
    company?: string;
    country_id?: string;
    customer_address_id?: number;
    email?: string;
    entity_id?: number;
    firstname?: string;
    lastname?: string;
    parent_id?: number; // order's entity_id
    postcode?: string;
    street?: string[];
    telephone?: string;
  };

  type Order = {
    entity_id?: number;
    store_id?: number;
    status?: string;
    increment_id?: string;
    total_qty_ordered?: number;
    total_item_count?: number;
    grand_total?: number;
    subtotal?: number;
    tax_amount?: number;
    subtotal_incl_tax?: number;
    total_paid?: number;
    subtotal_invoiced?: number;
    total_invoiced?: number;
    total_due?: number;
    weight?: number;
    store_name?: string;
    state?: string;
    shipping_description?: string;
    shipping_amount?: number;
    shipping_tax_amount?: number;
    shipping_incl_tax?: number;
    is_virtual?: number;
    global_currency_code?: string;
    order_currency_code?: string;
    created_at?: string;
    updated_at?: string;
    customer_firstname?: string;
    customer_lastname?: string;
    customer_email?: string;
    sa_firstname?: string;
    sa_lastname?: string;
    sa_email?: string;
    sa_country_code?: string;
    sa_city?: string;
    sa_zip?: string;
    sa_street?: string;
    sa_telephone?: string;
    sa_company?: string;

    mag_order_items?: OrderItem[];
    mag_order_shipment_comments_count?: number;
    country?: Country;
    shipping_imported_list?: WarehousePicklistShippingImported[];
    latest_shipping?: WarehousePicklistShippingImported;
    picklist_details?: WarehousePicklistDetail[];
    stock_stables_booked?: StockStableBooked[];
    emails?: Email[];
    crm_cases?: CrmCase[];
    latest_order_parcel?: OrderParcel;

    customer_initials?: string;
    customer_fullname?: string;
    detail?: Record<string, any> & {
      billing_address?: OrderAddress;
      shipping_address?: OrderAddress;
      payment?: Record<string, any> & {
        additional_information?: Record<string, any>;
      };
      extension_attributes?: {
        payment_additional_info?: { key?: string; value?: any }[];
      };
    };

    sa_initials?: string;
    sa_fullname?: string;
  } & {
    emails_count?: number;
    crm_cases_count?: number;
    stock_stables_booked_count?: number;
    packed_count?: number;
  } & {
    inv_fullname?: string;
    sa_full?: string;
  } & {
    warn_sa_street?: boolean;
    warn_sa_zip?: boolean;
    warn_sa_zip_de_wrong?: boolean;
    warn_sa_company?: boolean;
    warn_sa_fullname?: boolean;
    warn_sa_fullname_wrong?: boolean;
    warn_sa_street_long?: boolean;
    warn_sa_street_no?: boolean;
    warnings_def?: string; // validation messages for defined rules.
  } & {
    extra?: OrderExtra;
    ext?: OrderExt;
    labels?: OrderLabel[];
    user_action_logs?: MagOrderUserActionLog[];
    order_user_action_log_count_label_printed?: number;
    order_user_action_log_count_delivery_note_printed?: number;
  } & {
    weight_map?: {
      weight?: number;
      zeroCnt?: number;
    };
  };

  type ShippingProvider = {
    id?: number;
    name?: string;
    mag_code?: string;
  };

  type ShippingDescProvider = {
    id?: number;
    shipping_desc?: string;
    provider_name?: string;
  } & {
    shipping_provider?: ShippingProvider;
    orders_cnt?: number;
  };

  type ShippingAddressStatusType = 'Open' | 'Problem' | 'Done';

  type OrderExtra = {
    entity_id?: number;
    shipping_provider_name_old?: string;
    shipping_provider_name?: string;
    shipping_provider_change_notes?: string;
    shipping_provider_change_user?: number;
    shipping_notes?: string;
    shipping_address_status?: ShippingAddressStatusType;
    shipping_address_check_detail?: string;

    note1_status?: string;
    note1?: string;
    note1_created_on?: string;
    note1_created_by?: number;
    detail?: Record<string, any>;
    weight_confirmed?: number;
  } & {
    order?: Order;
    shipping_provider?: ShippingProvider;
    shipping_provider_old?: ShippingProvider;
  };

  type OrderExt = {
    entity_id?: number;
    ff?: number;
    fp?: number;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_term?: string;
    utm_content?: string;
    gclid?: string;
    kaufland_order_id?: string;
  } & {
    order?: Order;
  };

  type OrderLabelServiceNameType = 'DHL' | 'GLS' | 'DPD' | 'Delivery Note';

  type OrderLabel = {
    id?: number;
    entity_id?: number;
    service_name?: OrderLabelServiceNameType;
    track_id?: string;
    ref_no?: string;
    pos?: number;
    parcel_no?: string;
    file_path?: string;
    file_type?: string;
    detail?: Record<string, any>;
  } & CreatorData & {
    url?: string;
  };

  type OrderShipmentComment = {
    entity_id?: number;
    parent_id?: number;
    is_customer_notified?: number;
    is_visible_on_front?: number;
    comment?: string;
    created_at?: string;
  };

  type OrderItemIdx = {
    order_item_id?: number; // PK
    sku?: string;
    case_qty?: number;
    qty_ordered?: number;
    bp?: number;
    bp_type?: number;
    ibo_id?: number;
  };

  type OrderItem = {
    item_id?: number;
    order_id?: number;
    product_id?: number;
    product_type?: string;
    store_id?: number;
    sku?: string;
    name?: string;
    original_price?: number;
    price?: number;
    tax_amount?: number;
    price_incl_tax?: number;
    weight?: number;

    qty_ordered?: number;
    qty_shipped?: number;
    qty_invoiced?: number;
    qty_canceled?: number;
    qty_refunded?: number;
    quote_item_id?: number;
    tax_percent?: number;
    free_shipping?: number;

    // relation
    item_ean?: Ean;
    picklist_detail?: WarehousePicklistDetail;
    oi_idx?: OrderItemIdx;

    created_at?: string;
    updated_at?: string;
    detail?: Record<string, any>;
  } & {
    landed_pcs?: number;
    open_qty_ordered?: number; // all open ordered qty
    weight?: number;
    is_single?: boolean;
  } & {
    // relations
    stock_stable_booked_list?: StockStableBooked[];
  };

  type OrderItemList = PaginatedResult<OrderItem>;

  type MagQuoteCurrency = {
    global_currency_code: string;
    base_currency_code: string;
    store_currency_code: string;
    quote_currency_code: string;
    store_to_base_rate: number;
    store_to_quote_rate: number;
    base_to_global_rate: number;
    base_to_quote_rate: number;
  }

  type MagQuoteCustomer = {
    id: number;
    customer_id: number;
    region: {
      region_code: null | string;
      region: null | string;
      region_id: number;
    };
    region_id: number;
    country_id: string;
    street: string[];
    telephone: string;
    postcode: string;
    city: string;
    firstname: string;
    lastname: string;
    default_shipping: boolean;
    default_billing: boolean;
    created_at?: string;
    updated_at?: string;
  }


  type MagQuoteBillingAddress = {
    id: number;
    region: null | string;
    region_id: null | number;
    region_code: null | string;
    country_id: null | number;
    street: string[];
    telephone: string;
    postcode: string;
    city: null | string;
    firstname: null | string;
    lastname: null | string;
    customer_id: number;
    email: string;
    same_as_billing: boolean;
    save_in_address_book: boolean;
  }

  type MagQuote = {
    id?: number;
    store_id?: string;
    status?: number;
    customer_id?: number;
    customer_firstname?: string;
    customer_lastname?: string;
    customer_email?: string;
    quote_customer_note?: string;
    customer_is_guest?: string;
    customer_note_notify?: string;
    customer_tax_class_id?: string;
    items_count?: number;
    items_qty?: number;
    orig_order_id?: number;
    is_virtual?: number;
    is_active?: number;
    shipping_configure?: number;
    created_at?: string;
    updated_at?: string;
    copying_history?: any[];
    currency?: MagQuoteCurrency;
    customer?: MagQuoteCustomer;
    billing_address?: MagQuoteBillingAddress;
  } & {
    // relation
    store?: StoreConfig;
    mag_quote_items?: MagQuoteItem[];
    offers?: Offer[];
  } & {
    // calc columns
    grand_total?: number;
    offers_count?; number;
  } & {
    // appends col
    customer_fullname?: string;
  }

  type MagQuoteItem = {
    item_id?: number;
    quote_id?: number;
    sku?: string;
    name?: string;
    qty?: number;
    price?: number;
    product_type?: string;
  } & {
    mag_quote?: MagQuote;
    item_ean?: Ean;
  }

  type StoreWebsite = {
    id?: number;
    code?: string;
    name?: number;
    default_group_id?: number;
  };

  type StoreWebsiteList = PaginatedResult<StoreWebsite>;

  type MagProductAttribute = {
    attribute_id?: number;
    attribute_code?: string;
    default_frontend_label?: string;
    options?: { value: string; label: string }[];
    scope?: string;
    is_visible?: boolean;
    is_wysiwyg_enabled?: boolean;
    frontend_input?: string;
  };

  type MagProductAttributeSet = {
    attribute_set_id?: number;
    attribute_set_name?: string;
    entity_type_id?: number;
  };

  type MagDsStat = {
    table_name?: string;
    type?: string;
    last_sync_at?: string;
    sync_count?: number;
  };

  type AppSettings = {
    storeConfig?: API.StoreConfig[];
    storeWebsites?: API.StoreWebsite[];
    productAttributes?: Record<string, MagProductAttribute>;
    productAttributeSet?: MagProductAttributeSet[];
    magDsStat?: Record<string, MagDsStat>;
    vats: Vat[];
    priceTypes: PriceType[];
    dict?: Record<string, any>;
    drSelection: Record<string, string[]>;
    itemSpecialFilters?: string[];
    serverTz?: number;
    parcelShippingStatus?: Record<string, any>;
    FY_START_MONTH?: number;
  };

  type WarehousePicklist = {
    id?: number;
    date?: string;
    is_pre?: 1 | 0;
    note?: string;
    booking_detail?: Record<string, any>;
    is_full_stock_stable_updated?: number;
    last_stock_stable_updated_on?: string;
    last_stock_stable_updated_by?: number;
    order_shipment_created_on?: number;
    label_files?: string[];
    status_pre_picking?: number;
    status_picking?: number;
    status_packing?: number;
    detail?: Record<string, any>;

    // relation
    user?: CurrentUser;
    last_stock_stable_updater?: CurrentUser;
    pre_pdf_file?: File;
    final_pdf_file?: File;
    final_pdf_file2?: File;
    time_tracks?: WarehousePicklistTimeTrack[];
    active_time_tracks?: WarehousePicklistTimeTrack[]; // reserved

    // virtual
    username?: string;
    user_initials?: string;
    items_count?: number;
    orders_count?: number;
    unbooked_count?: number;

    // Virtual 2
    single_orders_count?: number;
    multi_orders_count?: number;
    ms_orders_count?: number;
    labeled_orders_count?: number;

    // boxes
    box_steps?: string; // CSV format
    boxed_order_count?: number;
    unboxed_order_count?: number;

    order_ids?: number[];
    order2labels?: Record<number | string, OrderLabel[]>;
  } & {
    time_tracks_sum_minute?: number;
    pre_picking_time_tracks_sum_minute?: number;
    picking_time_tracks_sum_minute?: number;
    packing_time_tracks_sum_minute?: number;
  } & CreatorData &
    UpdaterData;


  type WarehousePicklistDetail = {
    picklist_id?: number;
    order_id?: number;
    order_item_id?: number;
    order_status?: string;
    sku?: string;
    status?: string;
    is_stock_stable_updated?: number; // 1 or 0
    stock_stable_updated_on?: string;
    stock_stable_dump?: Record<string | number, any>;

    box_step?: number;
    box_no?: number;
    box_updated_on?: string;
    box_updated_by?: number;

    // relations
    warehouse_picklist?: WarehousePicklist;
    mag_order_item?: OrderItem;
    mag_order?: Order;
  } & {
    sum_qty_ordered?: number;
  } & Partial<OrderItem> &
    CreatorData &
    UpdaterData;

  type ScrapPrice = {
    id?: number;
    ean?: string;
    system?: 'WoS' | 'SwO';
    name?: string;
    price?: number;
    link?: string;

    note?: {
      title?: string;
      link?: string;
      price?: string;
      priceUnit?: string;
      priceUnit?: string;
      priceBulk?: string;
      priceBulkDesc?: string;
    };
  } & UpdaterData;

  type EanTask = {
    id?: number;
    category_code?: string;
    user_id?: number;
    task?: string;
    ean?: string;
    status?: number;

    // relation
    user?: User;
    created_user?: User;
    item_ean?: Ean;
    category_dict?: Dict;
  } & UpdaterData &
    CreatorData;

  type EanTaskList = PaginatedResult<EanTask>;

  type EmailAccountSettings = {
    imapSince?: string;
  };

  type EmailAccount = {
    id?: number;
    email?: string;
    server_id?: number;
    status?: number;
    sender_name?: string;
    pop_type?: 'IMAP' | 'POP';

    settings?: EmailAccountSettings;
    email_server?: EmailServer;
  } & CreatorData &
    UpdaterData;

  type EmailAccountList = PaginatedResult<EmailAccount>;

  type EmailServer = {
    id?: number;
    domain?: string;
    imap_host?: string;
    imap_port?: number;
    imap_port_ssl?: number;
    imap_ssl?: number;
    pop_host?: string;
    pop_port?: number;
    pop_port_ssl?: number;
    smtp_host?: string;
    smtp_port?: number;
    smtp_user?: string;
    smtp_password?: string;
    settings?: Record<string, any>;
    is_oauth?: number;
  };

  type EmailServerList = PaginatedResult<EmailServer>;

  type EmailAttachment = {
    org_name?: string;
    name?: string;
    ext?: string;
    mime_type?: string;
    size?: number;
    subtype?: string;
    file_path?: string;
    id?: string;
  };

  type Email = {
    id?: number;
    email_account_id?: number;
    message_id?: string;
    mail_id?: number;
    box?: string;
    sender?: string;
    sender_host?: string;
    sender_name?: string;
    receiver?: string;
    subject?: string;
    date?: string;
    date_str?: string;

    text_html?: string;
    text_plain?: string;
    has_attachments?: boolean;
    is_seen?: boolean;
    is_answered?: boolean;
    is_recent?: boolean;
    is_flagged?: boolean;
    is_deleted?: boolean;
    is_draft?: boolean;
    from_host?: string;
    from_name?: string;
    to?: string[];
    reply_to?: string[];
    cc?: string[];
    bcc?: string[];
    attachments?: EmailAttachment[];
    mime_version?: string;
    content_type?: string;
    is_hidden?: number; // Hidden status

    // extra info
    order_id?: number;
    tracking_no?: string;
    ext_order?: string;
    ext_order_id?: string;
    status?: string;

    crm_case_id?: Nullable<number>;

    // relations
    email_account?: EmailAccount;
    crm_case?: CrmCase;
  } & CreatorData & { text?: string };

  type EmailList = PaginatedResult<Email>;

  type CaseReasonTextItemType = {
    uid?: string;
    sku?: string;
    reason_text?: string;
  };

  type CrmCase = {
    id?: number;
    status?: Nullable<string>;
    order_id?: Nullable<number>;
    notes?: string;
    reason?: Nullable<string>;
    // reason_text?: string;
    reason_text?: CaseReasonTextItemType[];

    order?: Order;
    parcels?: Parcel[];
    parcel?: Parcel;
  } & CreatorData &
    UpdaterData;

  type CrmCaseNote = {
    id?: number;
    case_id?: number;
    note?: string;

    crm_case?: CrmCase;
  } & CreatorData &
    UpdaterData;

  type CrmCaseNoteList = PaginatedResult<CrmCaseNote>;

  type Offer = {
    id?: number;
    offer_no?: number;
    note?: string;
    ibo_status?: string;
    quote_id?: number;
    customer_id?: number;
    offer_customer_note?: string;
    gfc_note?: string;
    recv_note?: string; // note for received pallets
    percentage?: number;
  } & UpdaterData & CreatorData & {
    offer_items?: OfferItem[];
    quote?: MagQuote;
    ibo_pre_managements?: IboPreManagement[]; // calculated array
    offer_recv_weights?: OfferRecvWeight[];
    offer_recv_files?: File[];

    // mapped x/y rows
    mapped_offer_item_cnt?: number;
    offer_items_count?: number;
    offer_items_count_valid?: number; // valid one, that's items count which have any qty.
    offer_recv_weights_count?: number;
    offer_recv_weights_sum_pallet_weight?: number;

    // Mapped info: ibo_pre_map
    mapped_offer_detail?: Record<string | number, any>;
  };

  type OfferItem = {
    id?: number;
    offer_id?: number;
    sku?: string;
    ean_id?: number;
    item_id?: number;
    ean?: string;
    case_qty?: number;
    pcs_pallet?: number;
    qty?: number;
    price?: number;
    price_special?: number;
    price_stable?: number;
    customer_note?: string;
    note?: string;
    marked?: ZeroOrOne; // 0 or 1
  } & UpdaterData & CreatorData & {
    offer?: Offer;
    item?: Item;
    item_ean?: Ean;
    item_ean_sku?: Ean;
    ibo_pres_all?: IboPre[];
    ibo_pres_sent?: IboPre[];
    ibo_pres_open?: IboPre[];
    ibo_pres_open_sent?: IboPre[];
    ibo_pres?: IboPre[]; // mapped ibo pres
    offer_item_delivered_list?: OfferItemDelivered[];
    offer_item_shipped_list?: OfferItemShipped[];
  } & {
    // calculated columns
    ibo_pre_piece_qty_total?: number;
    ibo_pre_mapped_count?: number;
    delivered_qty_pcs?: number; // delivered qty
    packed_ready_qty_pcs?: number; // packed ready qty
    shipped_qty_pcs?: number; // shipped qty
  };


  type OfferItemDelivered = {
    id?: number;
    offer_item_id?: number;
    item_id?: number;
    ean_id?: number;
    case_qty?: number;
    qty?: number;
    exp_date?: string;
  } & UpdaterData & CreatorData & {
    offer_item?: OfferItem;
    item?: Item;
    item_ean?: Ean;
  }

  type OfferItemShipped = {
    id?: number;
    offer_item_id?: number;
    ibo_pre_id?: number;
    ean_id?: number;
    case_qty?: number;
    qty?: number;
    exp_date?: string;
    wa_no?: number;
    wa_date?: string;
    note?: string;
  } & UpdaterData & CreatorData & {
    offer_item?: OfferItem;
    item?: Item;
    item_ean?: Ean;
  }

  // offer_item_ibo_pre_map
  type OfferItemIboPreMap = {
    offer_item_id?: number;
    ibo_pre_id?: number;
    case_qty?: number;
    qty?: number;
  }


  // offer_item_ibo_pre_pack_ready_map
  // describes between OfferItem and IBO Pre received.
  type OfferItemIboPrePackReadyMap = {
    offer_item_id?: number;
    ibo_pre_id?: number;
    ean_id?: number;
    case_qty?: number;   // Pcs qyt of IBO Pre filled by user
    qty?: number;   // Pcs qyt of IBO Pre filled by user
    exp_date?: string;// Exp. Date of IBO Pre filled by user
    updated_on?: string;
  } & {
    offer_item?: OfferItem;
    ibo_pre?: IboPre;
    item_ean?: Ean;  // EAN in PreIBO in map assigning
  } & {
    // calc values
    offer_item_shipped_ids?: string | array;
    offer_item_shipped_list?: OfferItemShipped[];
    offer_item_shipped_qty?: number;
  }

  type OfferRecvWeight = {
    id?: number;
    offer_id?: number;
    pallet_weight?: number;
  } & {
    offer?: Offer;
  }


  type SysTextModule = {
    number: number;
    text?: string;
  };

  type SysTextModuleList = PaginatedResult<SysTextModule>;

  type MagSyncLog = {
    id?: number;
    sync_type?: 0 | 1;
    category?: string;
    name?: string;
    note?: string;
    status?: string;
    req_method?: string;
    detail?: Record<string, any>;
    batch_code?: number;
    action_type?: string;
    updated_on?: string;
  } & CreatorData;

  type MagOrderUserActionLog = {
    id?: number;
    type?: string;
    note?: string;
    order_id?: number;
    detail?: Record<string, any>;
  } & CreatorData & {
    // relations
    mag_order?: Order;
    user?: User;
  };

  type WarehousePicklistShippingImported = {
    id?: number;
    picklist_id?: number;
    order_id?: number;
    parcel_no?: string;
    status?: string;
    status_date?: string;
    imported_date?: string;
    pickup_date?: string;
    processing_status?: string;
    mag_ship_id?: number;
    carrier_code?: string;
    title?: string;
  };

  type WarehousePicklistTimeTrack = {
    id?: number;
    picklist_id?: number;
    type?: number;
    qty_employee?: number;
    minute?: number;
    start_datetime?: string;
    end_datetime?: string;
  } & UpdaterData & CreatorData & {
    // calculated value: minute * qty_employee
    minute_total?: number;
  } & {
    warehouse_picklist?: WarehousePicklist;
  };

  type CustomerPrice = {
    customer_name?: string;
    sku?: string;
    customer_sku?: string;
    price?: string;
    start_date?: string;
    end_date?: string;
  }

  type Parcel = WarehousePicklistShippingImported;


  type OrderParcelServiceName = 'DHL' | 'DPD' | 'GLS'

  type OrderParcel = {
    id?: number;
    service_name?: OrderParcelServiceName;
    parcel_no?: string;
    ref_no?: string;
    track_id?: string;
    order_id?: number;
    status?: string;
    push_id?: string;
    detail?: Record<string, any>;
    weight?: number;
    shipping_updated_on?: string;
    created_on?: string;
    updated_on?: string;
  } & {
    order?: Order;
    parcel_logs?: OrderParcelLog[];
  }

  type OrderParcelLog = {
    id?: number;
    parcel_id?: number;
    status?: string;
    note?: string;
    detail?: Record<string, any>;
    shipping_updated_on?: string;
    created_on?: string;
    updated_on?: string;
  } & {
    parcel?: OrderParcel;
  }


  type GdsnProvider = {
    provider_gln?: string;
    name?: string;
    notes?: string;
  } & {
    trademarks?: Trademark[];
    ean_count?: number;
  } & {
    trademark_ids?: number[];
  };
  type GdsnProviderList = PaginatedResult<GdsnProvider>;

  type GdsnProviderTrademark = {
    provider_gln?: string;
    trademark_id?: number;
  } & {
    gdsn_provider?: GdsnProvider;
    trademark?: Trademark;
  };

  type SysLog = {
    id?: number;
    category?: string;
    name?: string;
    note?: string;
    status?: string;
    ean_id?: number;
    ref1?: string;
    ref2?: string;
    request_uri?: string;
    request_method?: string;
    created_by?: number;
    created_on?: string;
  } & CreatorData & {
    user?: User;
    item_ean?: Ean;
  }

  /* ---------------------------------- File Manager ---------------------------------- */
  type FmCapability = {
    canDelete?: boolean;
    canRename?: boolean;
    canCopy?: boolean;
    canEdit?: boolean;
    canDownload?: boolean;
    canListChildren?: boolean;
    canAddChildren?: boolean;
    canRemoveChildren?: boolean;
  };

  type FmFile = {
    id?: string;
    name?: string;
    createdTime?: string;
    modifiedTime?: string;
    capabilities?: FmCapability;
    type?: 'dir' | 'file';
    parentId?: string;
    isDir?: boolean;
    // ancestors?: FmFile[];
  };

  /* ---------------------------------- ---------------------------------- */

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** total */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** business contract error code */
    errorCode: string;
    /** business misinformation */
    errorMessage?: string;
    /** Whether the business request is successful */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** The total number of contents of the list */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };

  type getFakeCaptchaParams = {
    phone?: string;
  };

  type ruleParams = {
    /** rule */
    current?: number;
    /** Page size */
    pageSize?: number;
  };

  type CreatorData = {
    created_by?: any;
    created_on?: string;
  };
  type UpdaterData = {
    updated_by?: any;
    updated_on?: string;
  };

  type AppApiResponse = {
    code?: number;
    message?: any;
    status?: string;
  };
}

/* --------------------------- Shop ------------------------------------ */
declare namespace Shop {
  /** CustomAttribute */
  type CustomAttribute = {
    attribute_code?: string;
    value?: any;
  };

  type ExtensionAttribute = {
    website_ids?: any[];
    category_links?: T<{ position?: number; category_id?: number }>[];
    stock_item?: StockItem;
  };

  type MediaGalleryType = 'image' | 'small_image' | 'thumbnail';

  type StockItem = {
    item_id?: number;
    product_id?: number;
    stock_id?: number;
    qty?: number;
    is_in_stock?: boolean;
    is_qty_decimal?: boolean;
    min_qty?: number;
    min_sale_qty?: number;
    max_sale_qty?: number;
  } & Record<string, any>;

  /** Magento MediaGalleryEntry */
  type MediaGalleryEntry = {
    id?: number;
    media_type?: string;
    label?: string;
    position?: number;
    disabled?: boolean;
    types?: MediaGalleryType[];
    file?: string;
  };

  /** Magento Product */
  type Product = {
    id?: number;
    sku?: string;
    name?: string;
    attribute_set_id?: number;
    price?: number;
    status?: number;
    visibility?: number;
    type_id?: string;
    type_id?: string;
    extension_attributes?: any;
    product_links?: any;
    options?: any[];
    media_gallery_entries?: MediaGalleryEntry[];
    tier_prices?: any[];
    custom_attributes?: CustomAttribute[];
  };

  type MagProductFile = MediaGalleryEntry & {
    sku?: string;
    created_on?: string;
  };
}
