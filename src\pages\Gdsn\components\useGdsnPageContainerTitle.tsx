import { BarcodeOutlined, FireOutlined } from '@ant-design/icons';
import { Space } from 'antd';
import { useMemo } from 'react';
import type { ReactElement } from 'react-markdown/lib/react-markdown';
import { Link } from 'umi';

export type RoutePropParamType = {
  path: string;
  name?: string;
};

export default (route: RoutePropParamType) => {
  const pageTitleEle = useMemo(() => {
    const tmpTitle: string | ReactElement = route.name ?? '';

    switch (route.path) {
      case '/item/gdsn/message-items':
        return (
          <>
            <BarcodeOutlined /> {tmpTitle}
          </>
        );
      case '/item/gdsn/message-provider':
        return (
          <>
            <FireOutlined /> {tmpTitle}
          </>
        );
      default:
        return tmpTitle;
    }
  }, [route]);

  const btnLinks = useMemo(() => {
    return (
      <Space size={12} style={{ fontSize: 14, marginLeft: 50 }}>
        <Link
          to="/item/gdsn/message-items"
          title="Show GDSN Message Items"
          hidden={route.path == '/item/gdsn/message-items'}
        >
          <BarcodeOutlined /> GDSN Items
        </Link>
        <Link
          to="/item/gdsn/message-provider"
          title="Show GDSN Provider"
          hidden={route.path == '/item/gdsn/message-provider'}
        >
          <FireOutlined /> GDSN Provider
        </Link>
      </Space>
    );
  }, [route.path]);

  const pageTitle = useMemo(() => {
    return (
      <>
        <div style={{ display: 'inline-block', width: 260 }}>{pageTitleEle}</div>
        {btnLinks}
      </>
    );
  }, [btnLinks, pageTitleEle]);

  return { pageTitle };
};
