/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/google';

/** rule GET /api/google/analytics */
export async function getGoogleAnalytics(params?: API.PageParams): Promise<any> {
  return request<API.BaseResult>(`${urlPrefix}/analytics`, {
    method: 'GET',
    params: {
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** rule GET /api/google/dsReportFirstUserMediumByDate */
export async function dsReportFirstUserMediumByDate(params?: API.PageParams): Promise<any> {
  return request<API.BaseResult>(`${urlPrefix}/dsReportFirstUserMediumByDate`, {
    method: 'GET',
    params: {
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}
