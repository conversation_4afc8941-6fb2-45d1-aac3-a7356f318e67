import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useCallback } from 'react';
import { useMemo } from 'react';
import React, { useRef } from 'react';
import { getIboList } from '@/services/foodstore-one/IBO/ibo';
import { Modal } from 'antd';
import { Col, message, Row, Button } from 'antd';
import Util, { sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import SPrices from '@/components/SPrices';
// import { CloseOutlined } from '@ant-design/icons';

import { updateStockStable } from '@/services/foodstore-one/Stock/stock-stable';
import EanTitleComp from '@/components/EanTitleComp';

type HeaderFiltersType = {
  item_name?: string;
  ean_name?: string;
};

type ExtraColumType = {
  headerFilters?: HeaderFiltersType;
  setHeaderFilters: Dispatch<SetStateAction<HeaderFiltersType>>;
};

export type ModalInitialValueType = {
  id: number; // stockStable ID
  ibo_id?: number;
  itemEan?: Partial<API.Ean>;
};

export type SelectIboModalProps = {
  initialValue: ModalInitialValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  selectCallback: (data: API.StockStable) => void;
};

type ColumnDataType = API.Ibo & ExtraColumType;

const SelectIboModal: React.FC<SelectIboModalProps> = (props) => {
  const { initialValue, modalVisible, handleModalVisible, selectCallback } = props;

  /* const [headerFilters, setHeaderFilters] = useState<HeaderFiltersType>({
    item_name: '',
    ean_name: '',
  }); */

  const actionRef = useRef<ActionType>();

  /* useEffect(() => {
    console.log('[2] select callback changed.');
  }, [selectCallback]); */

  const handleIboSelection = useCallback(
    (iboId: number) => {
      updateStockStable(initialValue.id, { ibo_id: iboId })
        .then((res) => {
          message.success('Updated successfully.');
          selectCallback(res);
        })
        .catch(Util.error);
    },
    [initialValue.id, selectCallback],
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  /* const debouncedHandleSearch = useCallback(
    debounce(() => {
      actionRef.current?.reload();
    }, 330),
    [],
  ); */

  const columns: ProColumns<ColumnDataType>[] = useMemo(() => {
    const newColumns: ProColumns<ColumnDataType>[] = [
      /* {
        title: 'Item Name',
        dataIndex: ['item_ean', 'item', 'name'],
        children: [
          {
            title: (
              <>
                <ProFormText
                  fieldProps={{
                    // ref: itemNameRef,
                    value: headerFilters?.item_name ?? '',
                    onChange: (e) => {
                      const value = e.target.value;
                      setHeaderFilters((prev) => ({ ...prev, item_name: value }));
                      debouncedHandleSearch();
                    },
                  }}
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </>
            ),
            dataIndex: ['item_ean', 'item', 'name'],
            sorter: false,
            ellipsis: true,
            width: 300,
            render: (dom) => {
              return <>{dom}</>;
            },
          },
        ],
      },
      {
        title: 'EAN Name',
        dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
        width: 300,
        children: [
          {
            title: (
              <>
                <ProFormText
                  fieldProps={{
                    value: headerFilters?.ean_name ?? '',
                    onChange: (e) => {
                      const value = e.target.value;
                      setHeaderFilters((prev) => ({ ...prev, ean_name: value }));
                      debouncedHandleSearch();
                    },
                  }}
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </>
            ),
            dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
            width: 300,
            ellipsis: true,
            render: (dom, record) => {
              const eanName = record?.item_ean?.ean_texts?.[0]?.name;
              return (
                <Typography.Text type={eanName ? undefined : 'warning'}>
                  {eanName ?? record?.item_ean?.item?.name ?? (
                    <CloseOutlined style={{ color: '#cc2200' }} />
                  )}
                </Typography.Text>
              );
            },
          },
        ],
      },
      {
        title: 'EAN',
        dataIndex: ['item_ean', 'ean'],
        key: 'ean',
        copyable: true,
        width: 150,
      },
      {
        title: 'Single EAN',
        dataIndex: ['item_ean', 'parent', 'ean'],
        copyable: true,
        hideInSearch: true,
        width: 150,
      },
      {
        title: 'SKU',
        dataIndex: ['item_ean', 'sku'],
        ellipsis: true,
        copyable: true,
        width: 80,
      }, */
      {
        title: 'Supplier',
        dataIndex: ['ibom', 'supplier', 'name'],
        ellipsis: true,
      },
      {
        title: 'Order No',
        dataIndex: ['ibom', 'order_no'],
        ellipsis: true,
        align: 'center',
        width: 100,
      },
      {
        title: 'Price',
        dataIndex: 'price',
        valueType: 'digit',
        align: 'right',
        width: 100,
        render: (dom, record) => {
          const vat = record?.item_ean?.item?.vat?.value || 0;
          return (
            <>
              <Row
                gutter={4}
                /* title="View prices list..."
                className="cursor-pointer"
                onClick={() => {
                  setCurrentRow({ ...record });
                  setShowImportedPrices(true);
                }} */
                style={{ minHeight: 24 }}
              >
                <Col span={12}>
                  <SPrices price={record.price} vat={vat} hideGross />
                </Col>
                <Col span={12}>
                  <SPrices price={(record?.price ?? 0) * (record?.item_ean?.attr_case_qty ?? 0)} vat={vat} hideGross />
                </Col>
              </Row>
            </>
          );
        },
      },
      {
        title: 'Pkg. Qty',
        dataIndex: 'box_qty',
        valueType: 'digit',
        align: 'right',
        width: 80,
        render: (dom, record) => Util.numberFormat(record.box_qty),
      },
      {
        title: 'Qty',
        dataIndex: 'qty',
        valueType: 'digit',
        showSorterTooltip: false,
        tooltip: 'Qty of Single EAN',
        align: 'right',
        width: 80,
        render: (dom, record) => Util.numberFormat(record.qty),
      },
      {
        title: 'ID',
        dataIndex: 'id',
        valueType: 'digit',
        align: 'center',
        className: 'text-sm c-grey',
        width: 100,
      },
      {
        title: '',
        dataIndex: 'option',
        valueType: 'option',
        width: 100,
        fixed: 'right',
        align: 'center',
        render: (dom, record) =>
          record.id == initialValue.ibo_id ? (
            <Button size="small" type="primary" ghost onClick={() => handleIboSelection(sn(record.id))}>
              Reselect
            </Button>
          ) : (
            <Button size="small" type="primary" onClick={() => handleIboSelection(sn(record.id))}>
              Select
            </Button>
          ),
      },
    ];
    return newColumns;
  }, [handleIboSelection, initialValue.ibo_id]);

  useEffect(() => {
    if (modalVisible) {
      actionRef.current?.reload();
    }
  }, [modalVisible]);

  return (
    <>
      <Modal
        title={
          <>
            Select an IBO
            <EanTitleComp itemEan={initialValue.itemEan} />
          </>
        }
        width="900px"
        open={modalVisible}
        onCancel={() => handleModalVisible(false)}
        bodyStyle={{ paddingTop: 0 }}
        footer={false}
      >
        <ProTable<ColumnDataType, API.PageParams>
          actionRef={actionRef}
          rowKey="id"
          revalidateOnFocus={false}
          options={{ fullScreen: false, setting: false, density: false, reload: true }}
          sticky
          scroll={{ x: 800 }}
          size="small"
          bordered
          cardProps={{ bodyStyle: { padding: 0 } }}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: 20,
          }}
          search={false}
          request={async (params, sort, filter) => {
            const formValues = {
              ...params,
              // ...headerFilters,
            };

            // @version 1. We strictly check IBO.
            /* if (initialValue.itemEan?.is_single) {
              formValues.item_id = initialValue.itemEan?.item_id;
            } else {
              formValues.ean_id = initialValue.itemEan?.id;
            } */

            // @version 2. We check IBO entries widely because stocks can be opened or re-packed in stock control modal.
            formValues.item_id = initialValue.itemEan?.item_id;

            return getIboList(formValues, { ...sort, updated_on: 'descend' }, filter).finally(() => {});
          }}
          onRequestError={Util.error}
          columns={columns}
        />
      </Modal>
    </>
  );
};

export default SelectIboModal;
