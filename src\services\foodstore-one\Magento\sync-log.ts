/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/magento/sync-log';

/** rule GET /api/magento/sync-log */
export async function getMagSyncLogList(
  params: API.PageParams,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.MagSyncLog>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
