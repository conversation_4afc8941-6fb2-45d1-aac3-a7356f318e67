import { useCallback, useEffect, useMemo, useState } from 'react';
import { type ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { TRADEMARK_FILTER_DEFAULT, getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
import Util from '@/util';
import ButtonGroup from 'antd/lib/button/button-group';
import type { ButtonProps } from 'antd';
import { Button } from 'antd';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';
import useTrademarkGroupOptions from '@/hooks/BasicData/useTrademarkGroupOptions';

const TrademarkNavButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  style: { width: 24, height: 24, fontSize: 14 },
};

export type TrademarkChangeCallbackHandlerTypeParamType = 'reload';
export type TrademarkChangeCallbackHandlerType = (type?: TrademarkChangeCallbackHandlerTypeParamType) => void;

const useTrademarkFormFilter = (
  formRefCur?: ProFormInstance,
  trademarkCallbackHandler?: any,
  selectProps?: ProFormSelectProps & {
    renderMode?: string;
    parentLoading?: boolean;
    ibom_id?: number;
    showGroup?: boolean;
    appendMode?: number;
    // UI
    width?: number | string;

    // Offer Item
    acMode?: 'forOfferItem';
    offer_id?: number;
    includeQtySuffix?: boolean;
  },
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [trademarks, setTrademarks] = useState<DefaultOptionType[]>([]); // trademarks dropdown options
  // selected trademark
  const [trademark, setTrademark] = useState<DefaultOptionType>();

  const searchTrademarks = useCallback(
    (params?: any) => {
      setLoading(true);
      getTrademarkListSelectOptions(
        {
          ...params,
          acMode: selectProps?.acMode,
          ibom_id: selectProps?.ibom_id ?? formRefCur?.getFieldValue('ibom_id'),
          offer_id: selectProps?.offer_id ?? formRefCur?.getFieldValue('offer_id'),
        },
        selectProps?.appendMode,
        selectProps?.includeQtySuffix,
      )
        .then((res) => setTrademarks(res))
        .catch(Util.error)
        .finally(() => setLoading(false));
    },
    [
      formRefCur,
      selectProps?.ibom_id,
      selectProps?.offer_id,
      selectProps?.acMode,
      selectProps?.appendMode,
      selectProps?.includeQtySuffix,
    ],
  );

  useEffect(() => {
    searchTrademarks();
  }, [searchTrademarks]);

  useEffect(() => {
    setTrademark(formRefCur?.getFieldValue('trademark'));
  }, [formRefCur]);

  // Trademark group related
  const {} = useTrademarkGroupOptions();

  /**
   * Prev / Next navigation in Trademark filter
   * @param mode
   */
  const handleTrademarkNavigation = useCallback(
    (mode: 'prev' | 'next') => {
      const curTrademarkObj = formRefCur?.getFieldValue('trademark');
      const curTrademarkId = curTrademarkObj?.value ?? TRADEMARK_FILTER_DEFAULT;
      const index = trademarks.findIndex((x) => x.value === curTrademarkId) ?? 0;
      const dir: number = mode == 'prev' ? -1 : 1;
      let nextIndex = (index + dir + trademarks.length) % trademarks.length;
      if (trademarks[nextIndex]?.value == 'sep') {
        nextIndex = (nextIndex + dir + trademarks.length) % trademarks.length;
      }
      formRefCur?.setFieldValue('trademark', trademarks[nextIndex]);
      setTrademark(trademarks[nextIndex]);
      trademarkCallbackHandler?.('reload');
    },
    [trademarkCallbackHandler, formRefCur, trademarks],
  );

  const size = selectProps?.fieldProps?.size; // Form element size
  const parentLoading = selectProps?.parentLoading ?? false; // parent component's loading

  const formElements = useMemo(() => {
    return formRefCur ? (
      <>
        <ProFormSelect
          name="trademark"
          label={selectProps?.label ?? 'Trademark'}
          placeholder="Please select trademark"
          mode="single"
          showSearch
          options={trademarks}
          fieldProps={{
            dropdownMatchSelectWidth: false,
            maxTagCount: 1,
            labelInValue: true,
            size: size,
            onChange: (value) => {
              setTrademark(value);
              trademarkCallbackHandler?.('reload');
            },
          }}
          width={selectProps?.width ?? 180}
          disabled={loading || parentLoading}
        />
        <ButtonGroup style={{ marginRight: 8 }}>
          <Button
            {...(size == 'small' ? TrademarkNavButtonProps : {})}
            onClick={() => handleTrademarkNavigation('prev')}
            icon={<ArrowLeftOutlined />}
            disabled={loading || parentLoading}
          />
          <Button
            {...(size == 'small' ? TrademarkNavButtonProps : {})}
            onClick={() => handleTrademarkNavigation('next')}
            icon={<ArrowRightOutlined />}
            disabled={loading || parentLoading}
          />
        </ButtonGroup>
      </>
    ) : null;
  }, [
    formRefCur,
    selectProps?.label,
    trademarks,
    size,
    loading,
    parentLoading,
    trademarkCallbackHandler,
    handleTrademarkNavigation,
    selectProps?.width,
  ]);

  return { formElements, trademarks, searchTrademarks, loading, handleTrademarkNavigation, trademark };
};

export default useTrademarkFormFilter;
