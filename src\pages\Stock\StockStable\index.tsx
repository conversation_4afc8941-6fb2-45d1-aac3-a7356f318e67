import { <PERSON><PERSON>, <PERSON>, Col, Drawer, Popover, Row, Space, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { FooterToolbar, PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import {
  exportStockStableList,
  getStockStable,
  stockStableBulkMove2Target,
  stockStableBulkMoveAll,
  updateStockStableStatus,
} from '@/services/foodstore-one/Stock/stock-stable';
import { DEFAULT_PER_PAGE_PAGINATION, StockStableStatus, StockStableStatusOptions } from '@/constants';
import Util, { nf2, ni, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import SDatePicker from '@/components/SDatePicker';
import _ from 'lodash';
import SPrices from '@/components/SPrices';
import ImportedPrices from '@/pages/Item/EanList/components/ImportedPrices';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import SelectIboModal from './components/SelectIboModal';
import { EditOutlined, FileExcelOutlined, FilePdfOutlined } from '@ant-design/icons';
import { IRouteComponentProps } from 'umi';
import ExportPdfStockStableSettingFormModal from './components/ExportPdfStockStableSettingFormModal';
import EditableCell from '@/components/EditableCell';

export type SearchFormValueType = Partial<API.StockStable>;

type BulkStockMoveFormValueType = {
  new_wl_id?: number;
};

type BulkStockMoveAllFormValueType = {
  old_wl_id?: number;
  new_wl_id?: number;
};

const StockStableList: React.FC<IRouteComponentProps> = (props) => {
  const location = props.location;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);

  const [currentRow, setCurrentRow] = useState<SearchFormValueType & { openParent?: boolean }>();
  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<API.StockStable[]>([]);

  // stock qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  // open select IBO modal
  const [openSelectIboModalVisible, setOpenSelectIboModalVisible] = useState<boolean>(false);

  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});

  // bulk stock movements
  const [openBulkStockMoveForm, setOpenBulkStockMoveForm] = useState<boolean>(false);
  const [openBulkStockMoveAllForm, setOpenBulkStockMoveAllForm] = useState<boolean>(false);

  // Export PDF Option Form
  const [openExportPdfForm, setOpenExportPdfForm] = useState<boolean>(false);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  useEffect(() => {
    getWarehouseLocationACList({})
      .then((res) => {
        setWarehouseLocations(res);
      })
      .catch(Util.error);
  }, []);

  useEffect(() => {
    if (location.query.wl_id || location.query.sku) {
      searchFormRef.current?.setFieldsValue({
        wl_id: location.query.wl_id ? sn(location.query.wl_id) : undefined,
        sku: location.query.sku,
      });
      actionRef.current?.reload();
    }
  }, [location.query.sku, location.query.wl_id]);

  const columns: ProColumns<API.StockStable>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Item',
      align: 'center',
      dataIndex: ['item', 'name'],
      hideInSearch: true,
      formItemProps: { tooltip: 'Search by item name.' },
      children: [
        {
          title: 'Name',
          dataIndex: ['item', 'name'],
          sorter: true,
          ellipsis: true,
          hideInSearch: false,
          width: 180,
        },
        /* {
          title: 'VAT',
          dataIndex: ['item', 'vat', 'value'],
          sorter: false,
          width: 60,
          ellipsis: true,
          align: 'right',
          hideInSearch: true,
          render: (dom, record) =>
            record?.item?.vat?.value >= 0 ? `${record?.item?.vat?.value}%` : '',
        }, */
        {
          title: 'Trademark',
          dataIndex: ['item', 'trademark', 'name'],
          sorter: false,
          width: 100,
          ellipsis: true,
          hideInSearch: true,
          render: (dom, record) => (record?.item?.trademark?.name ? `${record?.item?.trademark?.name}` : ''),
        },
      ],
    },
    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 130,
    },
    {
      title: 'Single EAN',
      dataIndex: ['parent_item_ean', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 130,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      width: 100,
    },
    {
      title: 'Warehouse',
      dataIndex: ['warehouse_location', 'name'],
      width: 80,
      sorter: true,
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      width: 100,
      align: 'center',
      sorter: true,
      defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },

    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 70,
      align: 'right',
      className: 'c-grey bl2',
      editable: false,
    },
    {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      tooltip: 'Click to view stock detail.',
      render: (dom, record) => ni(record.box_qty),
      onCell: (record) => {
        if (record.id) {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleQtyModalVisible(true);
            },
          };
        }
        return {};
      },
    },
    {
      title: 'Total Piece Qty',
      dataIndex: ['total_piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      tooltip: 'Click to view stock detail.',
      render: (dom, record) => ni(record.total_piece_qty),
      onCell: (record) => {
        if (record.id) {
          return {
            className: 'cursor-pointer',
            onClick: (e) => {
              setCurrentRow(record);
              handleQtyModalVisible(true);
            },
          };
        }
        return {};
      },
    },
    {
      title: 'BP / IBOM',
      dataIndex: ['ibo_id'],
      width: 120,
      align: 'left',
      editable: false,
      className: 'bl2',
      render: (dom, record: API.StockStable) => {
        return record.ibo ? (
          <Row gutter={4}>
            <Col span={10}>{record.ibo.price ? `€${nf2(record.ibo.price)}` : ''}</Col>
            <Col
              span={14}
              className=" ant-table-cell-ellipsis"
              title={'IBO ID: #' + record.ibo.id}
            >{`#${record.ibo.ibom?.order_no} | ${record.ibo.ibom?.supplier?.name}`}</Col>
          </Row>
        ) : null;
      },
    },
    {
      title: 'BP',
      dataIndex: ['ibo', 'price'],
      width: 100,
      align: 'right',
      className: 'bl2',
      render: (__, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        const isSingle = record.item_ean?.is_single;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12}>
                <SPrices price={record?.ibo?.price} vat={vat} />
              </Col>
              {!isSingle && (
                <Col span={12}>
                  <SPrices price={(record?.ibo?.price ?? 0) * (record?.case_qty ?? 0)} vat={vat} />
                </Col>
              )}
            </Row>
          </>
        );
      },
    },
    {
      title: 'BP * Qty',
      dataIndex: ['ibo', 'priceTotal'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row gutter={4} style={{ minHeight: 24 }}>
              <Col span={24}>
                <SPrices price={sn(record?.ibo?.price) * sn(record.total_piece_qty)} vat={vat} />
              </Col>
            </Row>
          </>
        );
      },
    },

    /* {
      title: 'Latest BP',
      dataIndex: ['item_ean', 'latest_ibo', 'price'],
      width: 100,
      align: 'right',
      hideInSearch: true,
      shouldCellUpdate: (record, prevRecord) =>
        !_.isEqual(record.item_ean?.latest_ibo, prevRecord.item_ean?.latest_ibo),
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        const isSingle = record.item_ean?.is_single;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                setCurrentRow({ ...record });
                setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12}>
                <SPrices price={record?.item_ean?.latest_ibo?.price} vat={vat} />
              </Col>
              {!isSingle && (
                <Col span={12}>
                  <SPrices
                    price={
                      (record?.item_ean?.latest_ibo?.price ?? 0) *
                      (record?.item_ean?.attr_case_qty ?? 0)
                    }
                    vat={vat}
                  />
                </Col>
              )}
            </Row>
          </>
        );
      },
    },
    {
      title: 'Latest BP * Qty',
      dataIndex: ['item_ean', 'latest_ibo', 'price'],
      width: 100,
      align: 'right',
      hideInSearch: true,
      shouldCellUpdate: (record, prevRecord) =>
        !_.isEqual(record.item_ean?.latest_ibo, prevRecord.item_ean?.latest_ibo),
      render: (dom, record) => {
        const vat = record.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row gutter={4} style={{ minHeight: 24 }}>
              <Col span={24}>
                <SPrices
                  price={sn(record?.item_ean?.latest_ibo?.price) * sn(record.total_piece_qty)}
                  vat={vat}
                />
              </Col>
            </Row>
          </>
        );
      },
    }, */
    {
      title: 'Updated on',
      dataIndex: ['updated_on'],
      sorter: true,
      width: 120,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 100,
      editable: false,
      align: 'center',
      tooltip: 'Click to change.',
      render: (dom, record, index, action) => {
        const defaultValue = record.status;
        return (
          <EditableCell
            dataType="select"
            defaultValue={defaultValue}
            dataOptions={StockStableStatusOptions}
            rules={[
              {
                required: true,
                message: 'Status is required',
              },
            ]}
            fieldProps={{ dropdownMatchSelectWidth: false }}
            width={80}
            convertValue={(value) => (value === null ? value : value)}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              return updateStockStableStatus({
                id: record.id,
                status: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            <span
              className={record.status != StockStableStatus.STATUS_AVAILABLE_SALE ? 'c-orange' : ''}
              style={{ verticalAlign: 'middle' }}
            >
              {StockStableStatusOptions.find((x) => x.value == record.status)?.label}
            </span>
          </EditableCell>
        );
      },
    },
    {
      title: 'IBO',
      dataIndex: ['ibo_id'],
      width: 110,
      render: (dom, record) => (
        <Row wrap={false}>
          <Col flex={'50px'}>{record.ibo_id}</Col>
          <Col flex={'30px'}>
            <a
              key="select-ibo"
              title="Select an IBO..."
              onClick={() => {
                setCurrentRow(record);
                setOpenSelectIboModalVisible(true);
              }}
            >
              <EditOutlined />
            </a>
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_stock_stable')}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Button
                  key="export-xls"
                  icon={<FileExcelOutlined />}
                  onClick={() => {
                    const hide = message.loading('Exporting XLS...', 0);
                    const formValues = searchFormRef.current?.getFieldsValue();
                    exportStockStableList(
                      formValues,
                      tableConfig?.sorter
                        ? {
                            [tableConfig.sorter.columnKey]: tableConfig.sorter.order,
                          }
                        : {
                            exp_date: 'descend',
                          },
                      tableConfig?.filters,
                    )
                      .then((res: any) => {
                        if (res) {
                          // window.location.href = `${API_URL}/api/${res.url}`;
                          console.log(res);
                          window.open(`${API_URL}/${res.url}`, '_blank');
                        }
                      })
                      .finally(() => {
                        hide();
                      });
                  }}
                >
                  XLS Export
                </Button>,
                <Button
                  key="export-pdf"
                  type="primary"
                  htmlType="button"
                  loading={loading}
                  disabled={loading}
                  onClick={() => setOpenExportPdfForm(true)}
                  icon={<FilePdfOutlined />}
                >
                  Export PDF
                </Button>,
                <Popover
                  key="move-stock-all"
                  title="Move Stocks"
                  trigger="click"
                  open={openBulkStockMoveAllForm}
                  onOpenChange={(visible) => {
                    setOpenBulkStockMoveAllForm(visible);
                  }}
                  content={
                    <ProForm<BulkStockMoveAllFormValueType>
                      size="small"
                      onFinish={async (values) => {
                        const hide = message.loading(`Moving all stocks...`, 0);
                        stockStableBulkMoveAll(sn(values.old_wl_id), sn(values.new_wl_id))
                          .then((res) => {
                            message.success('Moved successfully.');
                            setOpenBulkStockMoveAllForm(false);
                            setSelectedRows([]);
                            actionRef.current?.reload();
                          })
                          .catch(Util.error)
                          .finally(hide);
                      }}
                      submitter={{
                        searchConfig: { submitText: 'Move All' },
                        render(__, dom) {
                          return [dom[1]];
                        },
                      }}
                    >
                      <ProFormSelect
                        name="old_wl_id"
                        rules={[
                          {
                            required: true,
                            message: 'Old Warehouse Location is required',
                          },
                        ]}
                        request={(params) => {
                          return getWarehouseLocationACList(params).catch(Util.error);
                        }}
                        width="md"
                        label="Old Location"
                        placeholder="Old Location"
                        showSearch
                      />
                      <ProFormSelect
                        name="new_wl_id"
                        rules={[
                          {
                            required: true,
                            message: 'New Warehouse Location is required',
                          },
                        ]}
                        request={(params) => {
                          return getWarehouseLocationACList(params).catch(Util.error);
                        }}
                        width="md"
                        label="New Location"
                        placeholder="New Location"
                        showSearch
                      />
                    </ProForm>
                  }
                >
                  <Button type="primary" htmlType="button" ghost onClick={() => setOpenBulkStockMoveAllForm(true)}>
                    Move Stocks All
                  </Button>
                </Popover>,
              ];
            },
          }}
        >
          {/* <ProFormSelect
            name="wl_id"
            options={warehouseLocations}
            width="xs"
            label="Location"
            placeholder="Location"
            showSearch
          /> */}
          <ProFormText name={'like_wl_name'} label="Location" width={'xs'} placeholder={'Location'} />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" placeholder={'EAN'} width={180} />
          <ProFormText name={'name'} label="Item Name" width={'sm'} placeholder={'Item Name'} />
          <SDatePicker name="exp_date_start" label="Exp. Date" width={120} />
          <SDatePicker name="exp_date_end" addonBefore="~" width={120} />
        </ProForm>
      </Card>

      <ProTable<API.StockStable, API.PageParams>
        headerTitle={'Stock Warehouse'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        size="small"
        scroll={{ x: 800 }}
        rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        onChange={handleTableChange}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_stock_stable', searchFormValues);

          if (searchFormValues.exp_date_start) {
            searchFormValues.exp_date_start = Util.dtToYMD(searchFormValues.exp_date_start);
          } else {
            searchFormValues.exp_date_start = undefined;
          }
          if (searchFormValues.exp_date_end) {
            searchFormValues.exp_date_end = Util.dtToYMD(searchFormValues.exp_date_end);
          } else {
            searchFormValues.exp_date_end = undefined;
          }
          setLoading(true);
          return getStockStable(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.StockStable) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        columns={columns}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
        columnEmptyText=""
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;Stocks.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popover
            title="Move Stocks"
            trigger="click"
            open={openBulkStockMoveForm}
            onOpenChange={(visible) => {
              setOpenBulkStockMoveForm(visible);
            }}
            content={
              <ProForm<BulkStockMoveFormValueType>
                size="small"
                onFinish={async (values) => {
                  const hide = message.loading('Moving stocks...', 0);
                  stockStableBulkMove2Target(
                    selectedRows.map((x) => ({
                      id: x.id,
                      ean_id: x.ean_id,
                      wl_id: x.wl_id,
                      exp_date: x.exp_date,
                      ibo_id: x.ibo_id,
                      piece_qty: x.piece_qty,
                      box_qty: x.box_qty,
                    })),
                    sn(values.new_wl_id),
                  )
                    .then((res) => {
                      message.success('Moved successfully.');
                      setOpenBulkStockMoveForm(false);
                      setSelectedRows([]);
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Move' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name="new_wl_id"
                  rules={[
                    {
                      required: true,
                      message: 'New Warehouse Location is required',
                    },
                  ]}
                  request={(params) => {
                    return getWarehouseLocationACList(params).catch(Util.error);
                  }}
                  width="md"
                  label="New Location"
                  placeholder="New Location"
                  showSearch
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenBulkStockMoveForm(true)}
            >
              Move Stocks
            </Button>
          </Popover>
        </FooterToolbar>
      )}
      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />
      <SelectIboModal
        modalVisible={openSelectIboModalVisible}
        handleModalVisible={setOpenSelectIboModalVisible}
        initialValue={{
          id: sn(currentRow?.id),
          ibo_id: currentRow?.ibo_id,
          itemEan: currentRow?.item_ean,
        }}
        selectCallback={(data: API.StockStable) => {
          actionRef.current?.reload();
          setOpenSelectIboModalVisible(false);
        }}
      />

      <ExportPdfStockStableSettingFormModal
        modalVisible={openExportPdfForm}
        handleModalVisible={setOpenExportPdfForm}
        getParentParams={() => {
          return {
            ssIds: selectedRows?.length ? selectedRows.map((x) => x.id) : null,
            ...searchFormRef.current?.getFieldsValue(),
          };
        }}
      />

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.item_ean?.id && <ImportedPrices itemEan={currentRow?.item_ean} />}
      </Drawer>
    </PageContainer>
  );
};

export default StockStableList;
