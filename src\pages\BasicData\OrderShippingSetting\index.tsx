import { Row, Col, Card } from 'antd';
import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import OrderShippingProviderList from './OrderShippingProviderList';
import OrderShippingDescProviderMapList from './OrderShippingDescProviderMapList';

const OrderShippingSettingPage: React.FC = () => {
  return (
    <PageContainer>
      <Row gutter={36}>
        <Col flex="400px">
          <Card bodyStyle={{ padding: 0 }}>
            <OrderShippingProviderList />
          </Card>
        </Col>
        <Col flex="600px">
          <Card bodyStyle={{ padding: 0 }}>
            <OrderShippingDescProviderMapList />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default OrderShippingSettingPage;
