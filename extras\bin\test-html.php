<?php

use App\Lib\Func;
use App\Models\ItemEan;
use Illuminate\Support\Facades\DB;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';


print_r(array_merge([[1=>1]], [[2 => 1]]));
var_dump(!NULL);
var_dump(!'');

$tplStr = '<tr data-bind="visible" data-bind="visible: txtReferenceValueKJ &amp;&amp; txtReferenceValueKCAL || txtConsumptionUnitValueKJ &amp;&amp; txtConsumptionUnitValueKCAL">';

$patterns = ['/( data-bind=".*")*/'];

print_r(preg_replace($patterns, '', $tplStr));

$testStr = "Um Zusatzleistungen zu kombinieren, addieren Sie die entsprechenden numerischen ID’s";
echo PHP_EOL;
var_dump(Func::characterLimiter($testStr, 39));