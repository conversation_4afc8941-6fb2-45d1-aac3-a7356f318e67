import { getIboPreList } from '@/services/foodstore-one/IBO/ibo-pre';
import Util, { nf2, nf3, ni, sn, sUrlByTpl } from '@/util';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Card, Col, message, Modal, Popover, Row, Tag, Typography } from 'antd';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { RecordType } from '.';
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { SearchFormValueType as SearchFormValueTypeIboPre } from '@/pages/Offer/OfferItemList';
import { useModel } from 'umi';
import { DictCode, EURO, OfferIboStatus, OfferIboStatusKv } from '@/constants';
import { dsGetCustomAttribute } from '@/services/foodstore-one/Item/ean';
import { LinkOutlined, PlusOutlined } from '@ant-design/icons';
import SPrices from '@/components/SPrices';
import useIboPreManagementOptions from '@/hooks/BasicData/useIboPreManagementOptions';
import { getSupplierList } from '@/services/foodstore-one/supplier';

type RowType = API.IboPre;

export type SearchFormValueType = Partial<API.IboPre>;

type IboPreListModalProps = {
  searchParams?: { statuses?: string[] };
  parentSearchFormRef?: React.MutableRefObject<ProFormInstance<SearchFormValueTypeIboPre> | undefined>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const IboPreListModal: React.FC<IboPreListModalProps> = (props) => {
  const { searchParams, parentSearchFormRef } = props;
  const { getDictByCode } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [currentIboPreManagement, setCurrentIboPreManagement] = useState<API.IboPreManagement>();

  const {
    iboPreManagementOptions,
    searchIboPreManagementOptions,
    loading: loadingIboPreManagement,
  } = useIboPreManagementOptions();

  useEffect(() => {
    searchIboPreManagementOptions();
  }, [searchIboPreManagementOptions]);

  useEffect(() => {
    const selectedId = searchFormRef.current?.getFieldValue('ibo_pre_management_id');
    const tmp = iboPreManagementOptions?.find((x: any) => x.id == selectedId);
    if (tmp) {
      setCurrentIboPreManagement({ ...tmp });
    }
  }, [iboPreManagementOptions]);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'PreOrder No',
      dataIndex: ['order_no'],
      width: 45,
      align: 'center',
      render(dom, record) {
        // return `#${record.ibo_pre_management?.order_no} | ${record.supplier?.name}`;
        // return `${record.ibo_pre_management?.order_no}`;
        return `${record.ibo_pre_management?.id}`;
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['supplier', 'name'],
      width: 60,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: 'Item',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      align: 'left',
      width: 300,
      render: (dom, recordWrap) => {
        const record = recordWrap.item_ean;
        if (!record) return null;
        return (
          <Row gutter={8} wrap={false}>
            <Col flex="auto">
              <Typography.Text ellipsis title={`${record?.ean_text_de?.name || ''}`}>
                <a
                  href={sUrlByTpl(getDictByCode(DictCode.MAG_SEARCH_URL), {
                    q: record.item_id,
                  })}
                  target="_blank"
                  rel="noreferrer"
                  title="Search on website"
                >
                  {`${record.item_id || ''}_`}
                </a>
                {`--- ${record?.ean_text_de?.name || ''}`}
              </Typography.Text>
            </Col>
            <Col flex="0 0 20px">
              <Typography.Text
                title={`${record?.ean_text_de?.name || ''}`}
                copyable={{ text: `${record?.ean_text_de?.name || ''}` }}
              >
                {''}
              </Typography.Text>
            </Col>
            <Col flex="0 0 20px">
              <Typography.Link
                href={`/item/ean-all-summary?sku=${record.item_id}_`}
                title="Search EANs on new tab."
                target="_blank"
              >
                <LinkOutlined />
              </Typography.Link>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'VAT',
      dataIndex: ['item_ean', 'item', 'vat', 'value'],
      sorter: false,
      width: 50,
      ellipsis: true,
      align: 'right',
      hideInSearch: true,
      render: (dom, record) => (sn(record?.item_ean?.item?.vat?.value) ? `${record?.item_ean?.item?.vat?.value}%` : ''),
    },

    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 120,
      render: (dom, record) => {
        return (
          <a
            onClick={async () => {
              let urlKey = record?.item_ean?.mag_url?.value;
              if (!urlKey)
                urlKey = await dsGetCustomAttribute(record?.item_ean?.id || 0, {
                  force_update: 0,
                  attribute_code: 'url_key',
                }).catch(() => {
                  message.error('Not found SKU on the shop.');
                });

              if (urlKey) {
                window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
              }
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Package EAN (XLS)',
      dataIndex: ['xls_info', 'multi_ean'],
      copyable: true,
      tooltip: 'Package EAN in XLS data.',
      showSorterTooltip: false,
      width: 120,
    },
    {
      title: 'Last Sale (pcs)',
      dataIndex: ['last30_sales_qty'],
      width: 140,
      align: 'center',
      hideInSearch: true,
      tooltip: (
        <>
          <table style={{ textAlign: 'center' }} className="text-sm">
            <tr>
              <td>Sold pcs last 30 /365 days</td>
              <td>AVG Net Price Last 30 days</td>
            </tr>
            <tr>
              <td>
                <b>Stock Available Qty + Stock blocked Qty - processing orders Qty</b>
              </td>
              <td>Discount %</td>
            </tr>
          </table>
        </>
      ),
      render(dom, recordWrap) {
        const record: API.Ean & Record<string, any> = recordWrap.item_ean ?? {};
        const newStockQty = sn(record?.stock_mix_qty) - sn(record.processing_qty);
        const last30_sales_qty = sn(record.last30_sales_qty);
        const last30AvgPrice = last30_sales_qty ? sn(record.last30_cturover) / last30_sales_qty : 0;

        const last365_sales_qty = sn(record.last365_sales_qty);
        const last365AvgPrice = last365_sales_qty ? sn(record.last365_cturover) / last365_sales_qty : 0;

        return (
          <div style={{ textAlign: 'right' }}>
            <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
              <Col span={14} className="text-right">
                {!!last365_sales_qty || !!last30_sales_qty
                  ? `${ni(record.last30_sales_qty, !!last365_sales_qty)} / ${ni(record.last365_sales_qty)}`
                  : null}
              </Col>
              {last30AvgPrice || last365AvgPrice ? (
                <Col span={10} title={`Net Turnover last 30 days: ${nf2(record.last30_cturover)}${EURO}`}>
                  <Popover
                    content={
                      <table style={{ textAlign: 'left' }} className="text-sm">
                        <tr>
                          <td style={{ width: 100 }}>AVG (30)</td>
                          <td>
                            {last30AvgPrice ? (
                              <SPrices
                                price={last30AvgPrice}
                                vat={recordWrap.item_ean?.item?.vat?.value}
                                direction="horizontal"
                                showZero
                                showCurrency
                                noTextSmallCls
                              />
                            ) : null}
                          </td>
                        </tr>
                        <tr>
                          <td>AVG (365)</td>
                          <td className="text-right">
                            {last365AvgPrice ? (
                              <SPrices
                                price={last365AvgPrice}
                                vat={recordWrap.item_ean?.item?.vat?.value}
                                direction="horizontal"
                                showZero
                                showCurrency
                                noTextSmallCls
                              />
                            ) : null}
                          </td>
                        </tr>
                      </table>
                    }
                    trigger="hover"
                  >
                    &nbsp;
                    <SPrices
                      price={last30AvgPrice != 0 ? last30AvgPrice : last365AvgPrice}
                      vat={recordWrap.item_ean?.item?.vat?.value}
                      direction="horizontal"
                      showZero
                      showCurrency
                      noTextSmallCls
                      hideGross
                    />
                  </Popover>
                </Col>
              ) : null}
            </Row>
            <Row gutter={12} style={{ minHeight: 20, lineHeight: 1 }}>
              <Col
                span={14}
                title={`${ni(record?.stock_mix_qty, true)} - ${ni(record.processing_qty, true)} = ${ni(
                  newStockQty,
                  true,
                )}`}
                style={{ fontWeight: 'bold', textAlign: 'left' }}
              >
                {ni(newStockQty)}
              </Col>
              <Col span={10} className={record.fs_special_discount ? 'c-red' : ''}>
                {record.fs_special_discount}
              </Col>
            </Row>
          </div>
        );
      },
    },
    {
      title: 'Qty/Pkg (XLS)',
      dataIndex: ['xls_info', 'case_qty'],
      width: 60,
      hideInSearch: true,
      align: 'right',
      className: 'bl2',
    },
    {
      title: 'Qty (pcs)',
      dataIndex: ['sum_qty'],
      width: 70,
      align: 'right',
      hideInSearch: true,
      tooltip: 'Red Color --> Order Qty is not matched with Qty / Pkg',
      render(dom, record) {
        return ni(record.qty);
      },
      onCell: (record) => {
        let cls = '';
        const caseQtyXls = sn(record.xls_info?.case_qty);
        if (record.qty && caseQtyXls) {
          if (record.qty % caseQtyXls != 0) {
            cls += ' bg-light-red2';
          }
        }
        return {
          className: cls,
        };
      },
    },
    {
      title: 'Qty (VE)',
      dataIndex: ['qty_ve_calc'],
      width: 60,
      hideInSearch: true,
      align: 'right',
      className: 'bl2',
      render(__, record) {
        const caseQtyXls = sn(record.xls_info?.case_qty);
        if (caseQtyXls) {
          return <span title={`${sn(record.qty) / caseQtyXls}`}>{ni(sn(record.qty) / caseQtyXls, false)}</span>;
        }
        return null;
      },
    },

    {
      title: 'Price',
      dataIndex: ['price_xls'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render(__, record) {
        return sn(record.price_xls) > 0 ? nf3(record.price_xls) + EURO : null;
      },
    },
    {
      title: 'Price Total',
      dataIndex: ['price_t'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render(__, record) {
        return sn(record.price_xls) > 0 ? nf2(sn(record.price_xls) * sn(record.qty)) + EURO : null;
      },
    },
    {
      title: 'Item No',
      dataIndex: ['product_no'],
      width: 90,
      hideInSearch: true,
      align: 'center',
      render(dom, record) {
        return record.product_no;
      },
    },
    /* {
      title: 'NAN',
      dataIndex: ['nan'],
      width: 90,
      hideInSearch: true,
      align: 'center',
    }, */

    {
      title: 'Pallet (pcs)',
      dataIndex: ['qty_pallet'],
      width: 90,
      hideInSearch: true,
      align: 'right',
      onCell: (record) => {
        return {
          style: { opacity: 0.3 },
        };
      },
    },
    {
      title: 'Pallet Price',
      dataIndex: ['price_pallet'],
      width: 90,
      hideInSearch: true,
      align: 'right',
    },
    {
      title: 'Pallet Price Total',
      dataIndex: ['total'],
      width: 80,
      align: 'right',
      hideInSearch: true,
      render(dom, record) {
        if (record.qty && record.qty_pkg && record.qty % record.qty_pkg == 0 && record.price_pallet) {
          return nf2((sn(record.price_pallet) * record.qty) / sn(record.qty_pkg)) + EURO;
        }

        return null;
      },
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      className: 'text-sm',
      width: 60,
      fixed: 'right',
      render(dom, record) {
        let color = '';
        if (record.status == 'done') {
          color = 'green';
        }
        return <Tag color={color}>{record.status}</Tag>;
      },
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 60,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({
              ...record,
            });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <Modal
      title={
        <>
          IBO Pre List{' '}
          {searchParams?.statuses ? ' - ' + searchParams?.statuses.map((x) => OfferIboStatusKv[x]).join(', ') : null}
        </>
      }
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      width="1500px"
      footer={false}
    >
      <Card bordered={false} bodyStyle={{ padding: 0 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          size="small"
          initialValues={Util.getSfValues('sf_ibo_pre', {
            ean_search_mode: 'contain_siblings',
          })}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            name={'ibo_pre_management_id'}
            showSearch
            label="Pre Order"
            width={400}
            options={iboPreManagementOptions}
            request={searchIboPreManagementOptions}
            fieldProps={{
              loading: loadingIboPreManagement,
              dropdownMatchSelectWidth: false,
              onChange(value, option) {
                setCurrentIboPreManagement(option as API.IboPreManagement);
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          <ProFormText name={'sku'} label="SKU" width={150} placeholder={'SKU'} />
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            request={async (params) => {
              const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
              if (res && res.data) {
                const tmp = res.data.map((x: API.Supplier) => ({
                  label: `${x.supplier_no} - ${x.name}`,
                  value: x.id,
                }));
                return tmp;
              }
              return [];
            }}
            width="sm"
            name="supplier_id"
            label="Supplier"
          />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, reload: true, density: false, search: false }}
        search={false}
        sticky
        scroll={{ x: 1300 }}
        bordered
        cardProps={{
          headStyle: { padding: 0 },
          bodyStyle: { padding: 0 },
        }}
        request={async (params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const parentSearchValues = parentSearchFormRef?.current?.getFieldsValue();
          const searchValues = searchFormRef.current?.getFieldsValue();

          const iboParam = {
            ...params,
            ...parentSearchValues,
            ...searchValues,
            trademarks: [parentSearchValues?.trademark?.value],
            with: 'mode1,item_no,supplier,iboPreManagement',
          };

          return getIboPreList(iboParam, { ...newSort }, filter);
        }}
        columns={columns}
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        tableAlertRender={false}
        rowSelection={false}
        rowClassName={(record) => {
          const defaultCls = 'disable-selection reset-tds-bg ';
          let rowCls = '';
          if (sn(record.id) <= 0) {
            rowCls = defaultCls + 'total-row';
          } else
            rowCls =
              defaultCls +
              (record.item_ean?.id && record.item_ean
                ? record?.item_ean?.is_single
                  ? 'row-single'
                  : 'row-multi'
                : '');
          return rowCls;
        }}
        pagination={{
          pageSize: 100,
        }}
        columnEmptyText=""
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          handleModalVisible={handleModalVisible}
          initialValues={{ ibo_pre_management_id: currentIboPreManagement?.id, status: OfferIboStatus.Open }}
          onSubmit={async (value) => {
            handleModalVisible(false);

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
        />
      )}

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        // buyingHistoryComp={buyingHistoryComp}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </Modal>
  );
};
export default IboPreListModal;
