/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/basic-data/price-type';

/** rule GET /api/basic-data/price-type */
export async function getPriceTypeList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/basic-data/price-type */
export async function updatePriceType(data: API.PriceType, options?: { [key: string]: any }) {
  return request<API.PriceType>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/price-type */
export async function addPriceType(data: API.PriceType, options?: { [key: string]: any }) {
  return request<API.PriceType>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/price-type/{id} */
export async function deletePriceType(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
