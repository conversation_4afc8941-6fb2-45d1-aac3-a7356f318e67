import SProFormDigit from '@/components/SProFormDigit';
import { uploadImportedFileTmpXls } from '@/services/foodstore-one/Import/import';
import Util from '@/util';
import { UploadOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText, ProFormUploadButton } from '@ant-design/pro-form';
import type { UploadFile } from 'antd';
import { Button, Modal, Space, message } from 'antd';
import type { RcFile } from 'antd/lib/upload';
import type { Dispatch, SetStateAction } from 'react';
import { useRef, useState } from 'react';

export type FormValueTmpType = {
  files?: UploadFile[];
  headerRowNo?: number;
  nutritionCols?: string;
};

export type TmpXlsImportModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const TmpXlsImportModal: React.FC<TmpXlsImportModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<FormValueTmpType>>();

  const [loading, setLoading] = useState(false);

  const handleFinish = async (values: FormValueTmpType) => {
    const data = new FormData();
    if (values?.files) {
      data.append(`files[]`, values?.files[0].originFileObj as RcFile);
    }

    if (values.headerRowNo) {
      data.set('headerRowNo', `${values.headerRowNo}`);
    }

    if (values.nutritionCols) {
      data.set('nutritionCols', `${values.nutritionCols}`);
    }

    setLoading(true);
    uploadImportedFileTmpXls(data)
      .then((res) => {
        message.success('Imported to tmp_import table and updated EAN data.');
        formRef.current?.resetFields();
        props.handleModalVisible(false);
      }).catch(Util.error)
      .finally(() => setLoading(false));
  };

  return (
    <Modal
      title={<>Upload XLS File to Update EAN Data</>}
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      width="500px"
      footer={false}
    >
      <ProForm<FormValueTmpType>
        formRef={formRef}
        initialValues={{}}
        labelCol={{ span: 6 }}
        submitter={{
          submitButtonProps: { loading },
          render: (__, doms) => {
            return (
              <Space style={{ alignItems: 'baseline' }}>
                <Button
                  type="primary"
                  key="button"
                  ghost
                  icon={<UploadOutlined />}
                  onClick={() => formRef.current?.submit()}
                >
                  Upload & Update EANs Data
                </Button>
              </Space>
            );
          },
        }}
        layout="horizontal"
        onFinish={handleFinish}
      >
        <SProFormDigit name="headerRowNo" label="Header Row No" initialValue={1} width="xs" />
        <ProFormText
          name="nutritionCols"
          label="Nutrition Cols"
          placeholder="e.g. A-E"
          initialValue="E-K"
          width="xs"
          help="Specify column range. e.g. A-E"
        />
        <ProFormUploadButton
          max={1}
          name="files"
          label="File"
          title="Select File"
          accept=".xls,.xlsx,.csv,.txt"
          required
          rules={[
            {
              required: true,
              message: 'File is required',
            },
          ]}
          fieldProps={{
            beforeUpload: (file) => {
              return false;
            },
          }}
        />
      </ProForm>
    </Modal>
  );
};

export default TmpXlsImportModal;
