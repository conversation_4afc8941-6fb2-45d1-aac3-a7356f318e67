import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/basic-data/order-shipping-provider';

/**
 * Get providers list.
 *
 * GET /api/basic-data/order-shipping-provider
 */
export async function getOrderShippingProviderList(params: API.PageParams, sort: any, filter: any) {
  return request<API.ResultList<API.ShippingProvider>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/basic-data/order-shipping-provider  */
export async function updateOrderShippingProvider(
  id: number,
  data: API.ShippingProvider,
  options?: Record<string, any>,
) {
  return request<API.ResultObject<API.ShippingProvider>>(`${urlPrefix}/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/basic-data/order-shipping-provider  */
export async function addOrderShippingProvider(data: API.ShippingProvider, options?: Record<string, any>) {
  return request<API.ResultObject<API.ShippingProvider>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** put DELETE /api/basic-data/order-shipping-provider  */
export async function deleteOrderShippingProvider(id: number, options?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/` + id, {
    method: 'DELETE',
    ...(options || {}),
  });
}
