import type { ProFormFieldProps } from '@ant-design/pro-form';

import styles from './index.less';
import { Dispatch, HtmlHTMLAttributes, SetStateAction, useEffect, useRef, useState } from 'react';
import { Popover } from 'antd';
import ProCard from '@ant-design/pro-card';
import SNumpadExt from '../NumpadExt';

type NumpadExtSelectorType = {
  onChange?: (value: string) => void;
  eleOptions?: ProFormFieldProps & { showDecimal?: boolean };
  showBodyScroll?: boolean;
  initialOpen?: boolean;
  disableAutoClose?: boolean; // Disable to close dialog in case 4 or 5 letters are selected.
  actionsRender?: (value: string, setOpen?: Dispatch<SetStateAction<boolean>>) => any;
  buttonRender?: (value: string) => any;
  inputProps?: HtmlHTMLAttributes<HTMLInputElement>;
  isMobile?: boolean;
  value?: any;
};

/**
 * Numpad
 */
const NumpadExtSelector: React.FC<NumpadExtSelectorType> = ({
  onChange,
  showBodyScroll,
  initialOpen,
  actionsRender,
  buttonRender,
  inputProps,
  isMobile,
  value: initialValue,
  ...rest
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>('');

  // Numpad for EAN field
  const numpadFieldRef = useRef<HTMLInputElement>();

  useEffect(() => {
    setValue(initialValue || '');
    (numpadFieldRef.current as any).value = initialValue || '';
    onChange?.(initialValue);
  }, [initialValue]);

  useEffect(() => {
    if (numpadFieldRef.current) {
      (numpadFieldRef.current as any).value = value;
    }
  }, [value]);

  useEffect(() => {
    setOpen(!!initialOpen);
  }, [initialOpen]);

  return (
    <Popover
      placement="bottom"
      open={open}
      onOpenChange={setOpen}
      trigger={['click']}
      overlayStyle={{ zIndex: 1060 }}
      overlayInnerStyle={{ maxWidth: 420, padding: 0 }}
      content={
        <ProCard
          className={styles.content}
          bodyStyle={{
            padding: 0,
            ...(showBodyScroll ? { maxHeight: 'calc(100vh - 250px)', overflowY: 'auto' } : {}),
          }}
          headStyle={{
            padding: 0,
          }}
        >
          <SNumpadExt
            fieldRef={numpadFieldRef}
            onChange={(value) => {
              setValue(value);
              onChange?.(value);
            }}
            onOk={(value) => {
              setValue(value);
              onChange?.(value);
              setOpen(false);
            }}
            isMobile
          />
        </ProCard>
      }
    >
      {buttonRender ? (
        buttonRender(value)
      ) : (
        <input
          ref={numpadFieldRef as any}
          {...inputProps}
          onChange={(e) => {
            setValue(e.target.value);
            onChange?.(e.target.value);
          }}
          className="ant-input ant-input-lg"
          disabled={false}
          placeholder=""
        />
      )}
    </Popover>
  );
};

export default NumpadExtSelector;
