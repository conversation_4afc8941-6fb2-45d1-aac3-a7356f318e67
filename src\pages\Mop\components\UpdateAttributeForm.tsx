import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Modal, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormRadio, ProFormSwitch } from '@ant-design/pro-form';
import { ProFormGroup, ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { getShopProduct, usProduct } from '@/services/foodstore-one/Item/ean';
import Util, { sn } from '@/util';
import _ from 'lodash';
import { ReloadOutlined } from '@ant-design/icons';
import { useModel } from 'umi';
import type { HandleNavFuncType } from '../hooks/useMopModalNavigation';
import useMopUpdateModalActions from '../hooks/useMopUpdateModalActions';
import MopModalNavigation from './MopModalNavigation';
import { updateMopProduct } from '@/services/foodstore-one/Mop/mop-product';
import type { DefaultOptionType } from 'antd/lib/select';
import SProFormDigit from '@/components/SProFormDigit';
import { MopProductStatus, MopProductStatusOptions } from '@/constants';

export const CustomerGroupOptions: DefaultOptionType[] = [
  {
    value: 'All',
    label: 'All',
  },
  {
    value: 'A',
    label: 'A',
  },
  {
    value: 'B',
    label: 'B',
  },
  {
    value: 'C',
    label: 'C',
  },
];

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    const res = await updateMopProduct(sn(fields.id), fields);
    hide();
    message.success('Updated successfully.');
    if (res.upSyncMessage) {
      message.error('Up sync error: ' + res.upSyncMessage);
    }
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.MopProduct>;

export type UpdateAttributeFormProps = {
  initialValues?: Partial<API.MopProduct>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.MopProduct) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  reloadList?: (updatedRow: Partial<API.MopProduct>) => Promise<boolean | void>;
  handleNavigation?: HandleNavFuncType;
};

const UpdateAttributeForm: React.FC<UpdateAttributeFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);
  const [loadingLive, setLoadingLive] = useState(false);

  const { appSettings } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current && modalVisible) {
      const newFormValues = { ...(initialValues || {}) };

      formRef.current.setFieldsValue(newFormValues);
    }
  }, [initialValues, modalVisible]);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useMopUpdateModalActions(
    initialValues?.id ?? 0,
    initialValues?.sku ?? '',
    formRef.current,
  );

  return (
    <ModalForm<Partial<API.MopProduct>>
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update Stocklot Product Attributes</span>
          <Typography.Paragraph
            copyable={{
              text: initialValues?.sku || '',
              tooltips: 'Copy SKU ' + (initialValues?.sku || ''),
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {' '}
            - {initialValues?.sku || ''}
          </Typography.Paragraph>
          <MopModalNavigation
            modalName="attribute"
            eanId={initialValues?.id}
            handleNavigation={props.handleNavigation}
            style={{ marginLeft: 50 }}
          />
          {actionButtons}
        </div>
      }
      disabled={loading}
      width={930}
      visible={props.modalVisible}
      onVisibleChange={handleModalVisible}
      grid
      formRef={formRef}
      onFinish={async (value) => {
        if (!formRef.current) return Promise.resolve(false);
        setLoading(true);
        if (formRef.current?.isFieldsTouched()) {
          const newData = { ...value, id: initialValues?.id };

          const success = await handleUpdate(newData);
          if (success) {
            await runActionsCallback();

            setLoading(false);
            if ((value as any).closeModal) handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          setLoading(false);
          handleModalVisible(false);
        }
        formRef.current.getFieldInstance('up-sync').value = '';
        return Promise.resolve(true);
      }}
      modalProps={{
        confirmLoading: loading,
        // className: initialValues?.is_single ? 'm-single' : 'm-multi',
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {actionButtons}
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Save & Close
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
        {hiddenFormElements}
      </div>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText
          rules={[
            {
              required: true,
              message: 'SKU is required',
            },
          ]}
          width="md"
          name="sku"
          label="SKU"
          colProps={{ xl: 6 }}
        />

        <ProFormSelect
          name="customer_group"
          label="Customer Group"
          colProps={{ xl: 6 }}
          mode="single"
          options={CustomerGroupOptions}
        />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText
          rules={[
            {
              required: true,
              message: 'Name is required',
            },
          ]}
          width="xl"
          name="name"
          label="Name"
          colProps={{ xl: 16 }}
        />
        <SProFormDigit
          name="price"
          label={'Price (€)'}
          placeholder="Price"
          width={105}
          fieldProps={{
            step: 0.1,
            precision: 7,
          }}
          colProps={{ xl: 8 }}
        />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }} title="Additional info">
        <ProFormSelect
          name="attribute_set_code"
          label="Attribute Set"
          colProps={{ xl: 6 }}
          required
          options={appSettings.productAttributeSet?.map((x) => ({
            value: x.attribute_set_id,
            label: x.attribute_set_name,
          }))}
          rules={[
            {
              required: true,
              message: 'Attribute Set is required',
            },
          ]}
        />

        <ProFormSelect
          name="product_type"
          label="Product Type"
          colProps={{ xl: 6 }}
          required
          initialValue={'simple'}
          options={[
            {
              value: 'simple',
              label: 'Simple Product',
            },
          ]}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
          rules={[
            {
              required: true,
              message: 'Product Type is required',
            },
          ]}
        />

        <ProFormSelect
          name="visibility"
          label="Visibility"
          colProps={{ xl: 6 }}
          required
          initialValue={4}
          options={appSettings.productAttributes?.visibility?.options}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
          rules={[
            {
              required: true,
              message: 'Visibility is required',
            },
          ]}
        />
        <ProFormSelect
          name="product_websites"
          label="Product Websites"
          colProps={{ xl: 6 }}
          mode="multiple"
          options={appSettings.storeWebsites
            ?.filter((x) => x.code != 'admin')
            ?.map((x) => ({
              value: `${x.id}`,
              label: x.name,
            }))}
        />
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }} title="Product status">
        <ProFormRadio.Group
          name="status"
          label="Status"
          initialValue={MopProductStatus.DRAFT}
          options={MopProductStatusOptions}
          colProps={{ span: 'auto' }}
        />

        <ProFormSwitch
          name={'m_status'}
          label={
            <Space>
              <span>Live in Magento?</span>
              <Button
                type="link"
                loading={loadingLive}
                disabled={loadingLive}
                style={{ padding: 0, margin: 0, height: 20 }}
                onClick={() => {
                  setLoadingLive(true);
                  getShopProduct(initialValues?.sku ?? '')
                    .then((productPartial) => {
                      formRef.current?.setFieldsValue({ m_status: productPartial.status });
                    })
                    .catch(Util.error)
                    .finally(() => setLoadingLive(false));
                }}
                icon={<ReloadOutlined />}
              />
            </Space>
          }
          getValueProps={(value) => ({ value: value == 1 })}
          checkedChildren="Live"
          unCheckedChildren="Offline"
          colProps={{ span: 'auto' }}
          fieldProps={{
            loading: loadingLive,
            onClick: (status, e) => {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to change?',
                  onOk: async () => {
                    resolve(true);
                    setLoadingLive(true);
                    usProduct(initialValues?.sku ?? '', { status: status ? 1 : 2 })
                      .then((productPartial) => {
                        formRef.current?.setFieldsValue({ m_status: productPartial.status });
                      })
                      .catch(Util.error)
                      .finally(() => setLoadingLive(false));
                    return true;
                  },
                  onCancel: () => {
                    reject(true);
                    formRef.current?.setFieldsValue({ m_status: !status });
                  },
                });
              });
            },
          }}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateAttributeForm;
