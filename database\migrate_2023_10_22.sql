CREATE TABLE `import_search_filter`
(
    `id`              bigint(20)   NOT NULL AUTO_INCREMENT,
    `supplier_id`     bigint(20) unsigned DEFAULT NULL,
    `trademark`       varchar(255) NOT NULL,
    `keyword_title`   varchar(255)        DEFAULT NULL,
    `is_all_supplier` tinyint(1)          DEFAULT NULL,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `FK_import_search_filter_supplier_id` (`supplier_id`),
    <PERSON><PERSON>Y `IDX_import_search_filter_trademark` (`trademark`),
    CONSTRAINT `FK_import_search_filter_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;