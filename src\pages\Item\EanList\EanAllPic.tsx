import {
  CheckCircleOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  EditTwoTone,
  EyeFilled,
  EyeInvisibleFilled,
  FileSyncOutlined,
  FileTextOutlined,
  ImportOutlined,
  LinkOutlined,
  PictureOutlined,
  SnippetsOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Tag, Image } from 'antd';
import { Card } from 'antd';
import { Button, message, Drawer, Dropdown, Space, Menu, Popconfirm, Typography } from 'antd';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateAttributeForm from './components/UpdateAttributeForm';
import SDatePicker from '@/components/SDatePicker';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import WebsiteIcons from './components/WebsiteIcons';
import {
  getEanList,
  deleteEan,
  exportEanList,
  exportEanListAlt,
  exportEanPriceList,
  dsGetCustomAttribute,
  usProductFull,
  dsProductImages,
  dsProductImagesInfo,
  EAN_DEFAULT_PIC_WITH,
} from '@/services/foodstore-one/Item/ean';
import { DEFAULT_PER_PAGE_PAGINATION, ItemEANStatus, ProductWebsiteShortCode } from '@/constants';
import { ItemEANStatusOptions } from '@/constants';
import UpdateCategoriesForm from './components/UpdateCategoriesForm';
import type { DataNode } from 'antd/lib/tree';
import { getCategoryList } from '@/services/foodstore-one/Item/category';
import UpdateTextsForm from './components/UpdateTextsForm';
import UpdatePicturesForm from './components/UpdatePicturesForm';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormDigit, ProFormGroup } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
// import { getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
// import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import * as UpdateItemForm from '../ItemList/components/UpdateForm';
import { IRouteComponentProps, useLocation, useModel } from 'umi';
import SocialLinks from './components/SocialIcons';
import ImportedPrices from './components/ImportedPrices';
import type { DefaultOptionType } from 'antd/lib/select';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import StockStableQtyModal from './components/StockStableQtyModal';
import PictureImportModal from './components/PictureImportModal';
import ImagesCell from './components/ImagesCell';

import type { ResizeCallbackData } from 'react-resizable';
import { Resizable } from 'react-resizable';
import EanTasksModals from './components/EanTasksModal';
import { PRODUCT_IMAGE_TYPES } from './components/ImageCard';
import { TaskIcon } from './EanAllSummary';
import UpdatePriceAttributeForm from './components/UpdatePriceAttributeForm';
import useModalNavigation from './hooks/useModalNavigation';
import usePageContainerTitle from './hooks/usePageContainerTitle';
import type { TrademarkChangeCallbackHandlerTypeParamType } from './hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from './hooks/useTrademarkFormFilter';

/**
 *  Delete node
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.Ean[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEan({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export const ResizableTitle = (
  props: React.HTMLAttributes<any> & {
    onResize: (e: React.SyntheticEvent<Element>, data: ResizeCallbackData) => void;
    width: number;
  },
) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

export type SearchFormValueType = Partial<API.Ean>;

const EanAllPic: React.FC<IRouteComponentProps> = (compProps) => {
  const eanGridType = 'default';

  const [createModalVisible, setCreateModalModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);
  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  // Ean Tasks model
  const [visibleEanTasksModal, setVisibleEanTasksModal] = useState<boolean>(false);
  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [visibleImportPicModal, setVisibleImportPicModal] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});
  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const [selectedRowsState, setSelectedRows] = useState<API.Ean[]>([]);
  const [ibomList, setIbomList] = useState<DefaultOptionType[]>([]);
  // datasourc for inline editing
  const [datasource, setDatasource] = useState<API.Ean[]>([]);

  // hook for modal navigation
  // ------------------------------------------------------------- //
  const { handleNavigation } = useModalNavigation(datasource, {
    item: handleUpdateItemModalVisible,
    attribute: handleUpdateModalVisible,
    picture: handleUpdatePicturesModalVisible,
    price: handleUpdatePricesModalVisible,
    text: handleUpdateTextsModalVisible,
    setCurrentRow,
    modals: ['item', 'text', 'attribute', 'picture'],
  });

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
  });

  const [loadingExport, setLoadingExport] = useState(false);
  const [showMagImage, setShowMagImage] = useState(false);

  const { appSettings } = useModel('app-settings');

  const location: any = useLocation();

  const handleTextsClick = (record: API.Ean) => {
    setCurrentRow({ ...record });
    handleUpdateTextsModalVisible(true);
  };

  const [columns, setColumns] = useState<ProColumns<API.Ean>[]>([
    {
      title: 'Status',
      dataIndex: 'status',
      hideInForm: false,
      sorter: false,
      filters: false,
      fixed: 'left',
      align: 'center',
      ellipsis: true,
      width: 50,
      showSorterTooltip: false,
      valueEnum: ItemEANStatusOptions as any,
      render: (__, record) => {
        let ele = null;
        if (record.status == ItemEANStatus.ACTIVE) {
          ele = <CheckCircleOutlined style={{ color: 'green' }} />;
        } else {
          ele = <CloseOutlined style={{ color: 'gray' }} />;
        }
        return ele;
      },
    },
    {
      title: 'Shops',
      dataIndex: 'product_websites',
      hideInForm: false,
      sorter: false,
      filters: false,
      fixed: 'left',
      width: 50,
      showSorterTooltip: false,
      render: (__, record) => (
        <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
      ),
    },
    {
      title: 'Image',
      dataIndex: ['files', 0, 'url'],
      valueType: 'image',
      // fixed: 'left',
      align: 'left',
      hideInSearch: true,
      sorter: false,
      width: 400,
      render: (dom, record) => {
        return <ImagesCell itemEan={record} />;
      },
    },
    {
      title: 'Magento Image',
      dataIndex: ['mag_files'],
      valueType: 'image',
      align: 'left',
      hideInSearch: true,
      hideInTable: true,
      sorter: false,
      width: 400,
      render: (dom, record) => {
        return record?.mag_files
          ?.filter((x) => sn(x.position) < 200)
          ?.map((file) => (
            <div
              key={file.id}
              style={{
                backgroundColor: 'white',
                padding: '3px',
                width: 88,
                display: 'inline-block',
                margin: '4px 4px',
                overflow: 'hidden',
              }}
              className={`image-card readonly`}
            >
              <Image
                src={`${MEDIA_BASE_URL}${file.file}`}
                preview={{
                  src: `${MEDIA_BASE_URL}${file.file}`,
                }}
                width={80}
                height={80}
              />
              <Space className="card-actions text-sm" size={4}>
                {PRODUCT_IMAGE_TYPES.map((t) => {
                  const exists = (file.types || [])?.findIndex?.((x) => `${x}` == `${t.value}`) > -1;
                  return (
                    <div key={t.value} className={`type-action${exists ? ' active' : ''}`}>
                      {t.label}
                    </div>
                  );
                })}
              </Space>
            </div>
          ));
      },
    },
    /* {
      title: 'Item ID',
      dataIndex: 'item_id',
      fixed: 'left',
      sorter: true,
      ellipsis: true,
      hideInSearch: true,
      hideInTable: true,
    }, */
    {
      title: 'Name DE',
      dataIndex: ['ean_texts', 0, 'name'],
      width: 180,
      align: 'left',
      ellipsis: true,
      hideInSearch: true,
      fixed: 'left',
      tooltip: 'Orange color indicates the inherited value from its item.',
      shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
      render: (dom, record) => {
        const defaultValue = record?.ean_texts?.[0]?.name ?? record?.item?.name;
        return (
          <a onClick={() => handleTextsClick(record)}>
            <Typography.Text type={record?.ean_texts?.[0]?.name ? undefined : 'warning'} title={defaultValue}>
              {record?.ean_texts?.[0]?.name ?? record?.item?.name ?? <CloseOutlined style={{ color: '#cc2200' }} />}
            </Typography.Text>
          </a>
        );
      },
    },
    {
      title: 'GDSN Image',
      dataIndex: ['gdsn_item', 'detail', 'images'],
      valueType: 'image',
      sorter: false,
      width: 80,
      render: (__, record: any) => {
        return record?.gdsn_item?.detail?.images ? (
          <Image.PreviewGroup>
            {record?.gdsn_item?.detail?.images?.map((file: string, ind: number) => (
              <Image
                key={file}
                src={file}
                preview={{
                  src: file,
                }}
                wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                width={40}
              />
            ))}
          </Image.PreviewGroup>
        ) : (
          <></>
        );
      },
    },
    {
      title: 'Trademark',
      dataIndex: ['item', 'trademark', 'name'],
      sorter: false,
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      render: (dom, record) => (record?.item?.trademark?.name ? `${record?.item?.trademark?.name}` : ''),
    },
    {
      title: 'EAN',
      dataIndex: 'ean',
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              setCurrentRow({
                ...record,
              });
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Single EAN',
      dataIndex: ['parent', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
      showSorterTooltip: false,
    },
    {
      title: '-',
      dataIndex: ['mag_url', 'value'],
      valueType: 'text',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      showSorterTooltip: false,
      width: 50,
      render: (dom, record) => {
        return (
          <Space>
            <LinkOutlined
              // style={{ color: record?.mag_url?.value ? 'green' : 'c-lightgrey' }}
              title="Go to shop."
              className={record?.mag_url?.value ? 'green' : 'c-lightgrey'}
              onClick={async () => {
                let urlKey = record?.mag_url?.value;
                if (!urlKey)
                  urlKey = await dsGetCustomAttribute(record?.id || 0, {
                    force_update: 0,
                    attribute_code: 'url_key',
                  }).catch(() => {
                    message.error('Not found SKU on the shop.');
                  });

                if (urlKey) {
                  window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                }
              }}
            />
            <Dropdown
              key="social-links-menu"
              overlay={
                <Menu
                  items={[
                    {
                      key: 'all',
                      label: <SocialLinks ean={record.ean || ''} title={record?.ean_texts?.[0]?.name} />,
                    },
                  ]}
                />
              }
            >
              <a onClick={(e) => e.preventDefault()}>
                <DownOutlined />
              </a>
            </Dropdown>
          </Space>
        );
      },
    },
    {
      title: 'Qty/case',
      dataIndex: 'attr_case_qty',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      hideInSearch: true,
      showSorterTooltip: false,
      width: 60,
      render: (dom, record) => Util.numberFormat(record.attr_case_qty),
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      showSorterTooltip: false,
      width: 100,
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              setCurrentRow({
                ...record,
              });
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Task',
      dataIndex: ['ean_tasks_count'],
      width: 40,
      align: 'center',
      hideInSearch: true,
      className: 'cursor-pointer',
      render: (__, record) => <TaskIcon count={record.ean_tasks_count} />,
      onCell: (record: API.Ean) => {
        return {
          onClick: () => {
            setCurrentRow({ ...record });
            setVisibleEanTasksModal(true);
          },
        };
      },
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      ellipsis: true,
      className: 'text-sm c-grey',
      width: 80,
      renderFormItem: (item, { defaultRender }) => {
        return defaultRender(item);
      },
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
      search: false,
      sorter: true,
      align: 'center',
      className: 'text-sm c-grey',
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 110,
      render: (dom, record) => {
        const options = [
          <a
            key="update-item"
            title="Update item"
            onClick={() => {
              handleUpdateItemModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <SnippetsOutlined className="btn-gray" />
          </a>,
          <a
            key="texts"
            title="Update texts"
            onClick={() => {
              handleUpdateTextsModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <FileTextOutlined />
          </a>,
          <a
            key="config"
            title="Update attributes"
            onClick={() => {
              handleUpdateModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <EditTwoTone />
          </a>,

          <a
            key="files"
            title="Update pictures"
            onClick={() => {
              handleUpdatePicturesModalVisible(true);
              setCurrentRow({
                ...record,
              });
            }}
          >
            <PictureOutlined />
          </a>,
          <Popconfirm
            key="upsync"
            placement="topRight"
            title={
              <>
                Are you sure you want to up sync the EAN？ <br />
                <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
              </>
            }
            overlayStyle={{ width: 350 }}
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              if (!record.id) return;
              const hide = message.loading(`Up syncing ...`, 0);
              usProductFull(record.id)
                .then((res) => {
                  // console.log('---- Up sync result ---');
                  //console.log(res);
                  if (res.sku) {
                    message.success('Successfully up synced on shop!');
                  } else {
                    message.error(res.upSyncMessage || 'Failed to up sync EAN!');
                  }
                })
                .catch((e) => {
                  message.error(e.message ?? 'Failed to upsync!');
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <CloudUploadOutlined className="btn-gray" />
          </Popconfirm>,
        ];
        return <Space>{options.map((option) => option)}</Space>;
      },
    },
  ]);

  const handleResize: any =
    (index: number) =>
    (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const newColumns = [...columns];
      newColumns[index] = {
        ...newColumns[index],
        width: size.width,
      };
      setColumns(newColumns);
    };

  const mergeColumns: any /* ProColumns<API.Ean>[] */ = columns.map((col: any, index) => {
    return {
      ...col,
      hideInTable: col.dataIndex?.[0] == 'mag_files' ? !showMagImage : col.hideInTable,
      onHeaderCell: (column: ProColumns<API.Ean>) => ({
        width: column.width,
        onResize: handleResize(index) as React.ReactEventHandler<any>,
      }),
    };
  });

  // Category trees
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  useEffect(() => {
    getIBOManagementACList({}, {}).then((res) => {
      setIbomList(res);
    });
  }, []);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  const handleExportMenuClick: MenuProps['onClick'] = async (e) => {
    setLoadingExport(true);
    const hide = message.loading('Exporting...');
    const formValues = searchFormRef.current?.getFieldsValue();
    switch (e.key) {
      case 'export-core':
        exportEanList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-core-alt':
        exportEanListAlt(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-price':
        exportEanPriceList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'sync-sku':
        break;
    }
  };

  const { pageTitle } = usePageContainerTitle((compProps as any).route);

  return (
    <PageContainer className={styles.eanListContainer} title={pageTitle}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ean_grid_all_pic' + eanGridType, {
            status: 1,
            sku: location.query?.sku || '',
            minimum_order_qty: 1,
          })}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Dropdown
                  key="export-menu"
                  disabled={loadingExport}
                  overlay={
                    <Menu
                      onClick={handleExportMenuClick}
                      items={[
                        {
                          label: 'Download EANs',
                          key: 'export-core',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          label: 'Download prices',
                          key: 'export-price',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          type: 'divider',
                        },
                        {
                          label: 'Download As Excel',
                          key: 'export-core-alt',
                          icon: <DownloadOutlined type="primary" />,
                        },
                      ]}
                    />
                  }
                >
                  <Button loading={loadingExport}>
                    <Space>
                      {!loadingExport && <DownloadOutlined type="primary" />}
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>,
                <Button
                  key="import-pic"
                  type="primary"
                  className="btn-green"
                  icon={<ImportOutlined />}
                  onClick={() => setVisibleImportPicModal(true)}
                >
                  Import
                </Button>,
              ];
            },
          }}
        >
          {eanGridType == 'default' && (
            <ProFormSelect
              name="ean_type_search"
              placeholder="All"
              label="Type"
              options={[
                { value: '', label: 'All' },
                { value: 'base', label: 'Single' },
                { value: 've', label: 'Multi' },
              ]}
              fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
            />
          )}
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          {formElements}
          <ProFormCheckbox name="noTrademark" label="No Trademark?" />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormSelect
            name="create_type"
            placeholder="All"
            label="Creation mode"
            options={[
              { value: '', label: 'All' },
              { value: '1', label: 'Manually' },
              { value: '2', label: 'Imported' },
            ]}
            fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
          />
          <ProFormGroup size="small">
            <SDatePicker name="created_on_start" label="Date of creation" />
            <SDatePicker name="created_on_end" addonBefore="~" />
            <ProFormSelect name={'ibom_id'} showSearch label="IBOM" options={ibomList} />
            <ProFormDigit name={'minimum_order_qty'} label="Min. Qty" width={80} placeholder={'Min. Qty'} />
            <ProFormSelect
              name="product_websites"
              label="Websites"
              width={130}
              mode="multiple"
              placeholder={'Websites'}
              options={appSettings.storeWebsites
                ?.filter((x) => x.code != 'admin')
                ?.map((x) => ({
                  value: `${x.id}`,
                  label: x.name,
                }))}
            />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<API.Ean, API.PageParams>
        headerTitle={'EAN Picture list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        onChange={handleTableChange}
        dataSource={datasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(
            Util.getSfValues('sf_ean_grid_all_pic_p' + eanGridType)?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_grid_all_pic' + eanGridType, searchFormValues);
          Util.setSfValues('sf_ean_grid_all_pic_p' + eanGridType, params);

          setLoading(true);
          return getEanList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
              ean_type: eanGridType,
              with: EAN_DEFAULT_PIC_WITH,
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);
              // Update the seleted row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Ean) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRowsState?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        columns={mergeColumns}
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRowsState.map((x) => x.id as React.Key),
          onChange: (dom, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        toolBarRender={() => [
          <Button
            key="showMagentoImage"
            type="link"
            icon={showMagImage ? <EyeInvisibleFilled /> : <EyeFilled />}
            onClick={() => setShowMagImage((prev) => !prev)}
          >
            {showMagImage ? 'Hide Magento Image' : 'Show Magento Image'}
          </Button>,
        ]}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                &nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            title={<>Are you sure you want to get EAN images info from Magento?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              if (selectedRowsState.length) {
                const hide = message.loading(
                  'Getting EAN images info from Magento shop... It would take some time.',
                  0,
                );
                dsProductImagesInfo({ skus: selectedRowsState.map((x) => x.sku as string) })
                  .then(() => {
                    actionRef.current?.reloadAndRest?.();
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }
            }}
          >
            <Button type="primary" icon={<SyncOutlined />}>
              Magento pictures info
            </Button>
          </Popconfirm>
          <Popconfirm
            title={
              <>
                Are you sure you want to download pictures?
                <br />
                All local images will be removed.
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              if (selectedRowsState.length) {
                const hide = message.loading('Downloading EAN images... It would take some time.', 0);
                dsProductImages({ skus: selectedRowsState.map((x) => x.sku as string) })
                  .then(() => {
                    actionRef.current?.reloadAndRest?.();
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }
            }}
          >
            <Button type="primary" className="btn-green" icon={<FileSyncOutlined />}>
              Download pictures
            </Button>
          </Popconfirm>
          <Popconfirm
            title={<>Are you sure you want to delete selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={setCreateModalModalVisible}
        onSubmit={async () => {
          setCreateModalModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateAttributeForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
        gdsn
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />

      <UpdateCategoriesForm
        modalVisible={updateCategoriesModalVisible}
        handleModalVisible={handleUpdateCategoriesModalVisible}
        initialValues={currentRow || {}}
        treeData={treeData}
        onSubmit={async () => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateCategoriesModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        onSubmit={async (values) => {
          setCurrentRow((prev) => ({
            ...prev,
            ...values,
          }));

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
        gdsn
      />

      <UpdatePicturesForm
        modalVisible={updatePicturesModalVisible}
        handleModalVisible={handleUpdatePicturesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={{
          id: currentRow?.id,
          parent_id: currentRow?.parent_id,
          files: currentRow?.files || [],
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePicturesModalVisible(false);
        }}
        gdsn
      />

      <UpdateItemForm.default
        modalVisible={updateItemModalVisible}
        handleModalVisible={handleUpdateItemModalVisible}
        initialValues={{ ...currentRow?.item, eanId: currentRow?.id, ean: currentRow?.parent?.ean }}
        handleNavigation={handleNavigation}
        onSubmit={async (value) => {
          setCurrentRow((prev) => ({ ...prev, item: { ...prev?.item, ...value } }));
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateItemModalVisible(false);
        }}
        gdsn
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          parent_id: currentRow?.parent_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <EanTasksModals
        modalVisible={visibleEanTasksModal}
        handleModalVisible={setVisibleEanTasksModal}
        reloadList={() => actionRef.current?.reload()}
        itemEan={{
          id: currentRow?.id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
      />

      <PictureImportModal modalVisible={visibleImportPicModal} handleModalVisible={setVisibleImportPicModal} />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.Ean>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.Ean>[]}
          />
        )}
      </Drawer>

      <Drawer
        width={600}
        title={`Buying Price History - ${currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EanAllPic;
