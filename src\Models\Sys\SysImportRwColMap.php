<?php

namespace App\Models\Sys;

use App\Models\BaseModel;
use App\Models\Supplier;

/**
 * @property integer $id
 * @property string $supplier_name_xls
 * @property string $supplier_name
 * @property string $supplier_add
 * @property integer $sort
 *
 * @property Supplier $supplier
 */
class SysImportRwColMap extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_import_rw_col_map';

    /**
     * @var array
     */
    protected $fillable = ['supplier_name_xls', 'supplier_name', 'supplier_add', 'sort'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_name', 'name');
    }
}
