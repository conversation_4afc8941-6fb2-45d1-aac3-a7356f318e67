import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import type { OrderShippingByProviderReportRowType } from '@/services/foodstore-one/Report/order-report';
import {
  createPicklistFromOrdersListForShippingByProviderReport,
  getOrderShippingByProviderReport,
} from '@/services/foodstore-one/Report/order-report';
import type { DateRangeType } from '@/util';
import Util, { ni, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Space, message, notification } from 'antd';
import type { Key, MutableRefObject } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import OrderListModal from './OrderListModal';
import PrePicklistSummaryListModal from './PrePicklistSummaryListModal';
import { getProblematicOrderStats } from '@/services/foodstore-one/Magento/order';
import { ReloadOutlined } from '@ant-design/icons';

const sections = ['m1', 'm', 'ms', 's1', 's'];
export const sectionLabels: Record<string, string> = {
  m1: 'Multi (OnePack)',
  m: 'Multi',
  ms: 'Multi + Single',
  s1: 'Single (OnePack)',
  s: 'Single',
};

export type OrderModalSearchParamsType = {
  dateRange?: DateRangeType;
  parentSection?: string;
  section?: string;
  shipping_provider_name?: string;
};

type OrderShippingReportByProviderType = {
  searchFormRef?: MutableRefObject<ProFormInstance>;
};

const OrderShippingReportByProvider: React.FC<OrderShippingReportByProviderType> = (props) => {
  const { searchFormRef } = props;
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<Key[]>([]);

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});

  // pre warehouse picklist modal
  const [openPrePicklistSummaryListModal, setOpenPrePicklistSummaryListModal] = useState<boolean>(false);
  const [openPrePicklistSummaryListModal2, setOpenPrePicklistSummaryListModal2] = useState<boolean>(false);

  // count of orders with errors
  const [statsLoading, setStatsLoading] = useState<boolean>(false);
  const [orderCountWithError, setOrderCountWithError] = useState<number>(0);
  const [missingItemsQty, setMissingItemsQty] = useState<number>(0);
  const [orderCountToBeOpened, setOrderCountToBeOpened] = useState<number>(0); // Get number of pre-picklist count

  const fetchProblematicOrderStats = useCallback(() => {
    setStatsLoading(true);
    getProblematicOrderStats()
      .then((res) => {
        setOrderCountWithError(res.orderCountWithErrors);
        setMissingItemsQty(res.missingItemsQty);
        setOrderCountToBeOpened(res.orderCountToBeOpened);
      })
      .catch(Util.error)
      .finally(() => {
        setStatsLoading(false);
      });
  }, []);

  useEffect(() => {
    fetchProblematicOrderStats();
  }, [fetchProblematicOrderStats]);

  const columns: ProColumns<OrderShippingByProviderReportRowType>[] = useMemo(() => {
    /**
     * Handler to open the orders list modal.
     *
     * @param columnField
     * @param intervalType
     * @param dateRange
     * @param options
     */
    const handleOnClick = (
      parentSection: string,
      section: string,
      dateRange: { from?: string; to?: string },
      options?: Record<string, any>,
    ) => {
      setModalSearchParams({
        source: 'shippingReportByProvider',
        parentSection,
        section,
        dateRange,
        ...searchFormRef?.current?.getFieldsValue(),
        ...options,
      });
      setOpenOrderListModal(true);
    };

    /**
     * Handler to open the orders list modal.
     *
     * @param columnField
     * @param intervalType
     * @param dateRange
     * @param options
     */
    const handleCreatePicklistOnClick = (
      parentSection: string,
      section: string,
      dateRange: { from?: string; to?: string },
      options?: Record<string, any>,
    ) => {
      const hide = message.loading('Creating a picklist from the lists below...', 0);
      createPicklistFromOrdersListForShippingByProviderReport({
        ...{
          source: 'shippingReportByProvider',
          parentSection,
          section,
          dateRange,
          ...searchFormRef?.current?.getFieldsValue(),
          ...options,
        },
        with: 'warnings,warnings_def,extra',
        ...Util.mergeGSearch({}),
      })
        .then((res) => {
          const picklistName = `#${res?.id}-${res?.username}-${res?.date} ${res?.note ?? ''}`;
          notification.success({
            description: `${picklistName} created successfully.`,
            duration: 0,
            placement: 'top',
            message: undefined,
          });
          actionRef.current?.reload();
        })
        .catch(Util.error)
        .finally(hide);
    };

    const cols: ProColumns<OrderShippingByProviderReportRowType>[] = [
      {
        dataIndex: 'shipping_provider_name',
        title: 'Provider',
        width: 120,
        render(dom, record) {
          return record?.level == 1 ? dom : null;
        },
      },
      {
        dataIndex: 'order_date',
        title: 'Date',
        width: 90,
        render(dom, record) {
          return (
            <>
              {Util.dtToDMY(record.order_date)}
              {record.children ? (
                <div className="absolute" style={{ top: 0, left: 0 }}>
                  <Button
                    type="default"
                    size="small"
                    title="Create a picklist"
                    className="ant-btn-xs btn-green"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleCreatePicklistOnClick(
                        '',
                        '',
                        {},
                        { shipping_provider_name: record.shipping_provider_name },
                      );
                    }}
                  >
                    P
                  </Button>
                </div>
              ) : (
                <div className="absolute" style={{ top: 0, right: 0 }}>
                  <Button
                    type="default"
                    size="small"
                    title="Create a picklist"
                    className="ant-btn-xs"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleCreatePicklistOnClick(
                        '',
                        '',
                        { from: record.order_date, to: record.order_date },
                        { shipping_provider_name: record.shipping_provider_name },
                      );
                    }}
                  >
                    P
                  </Button>
                </div>
              )}
            </>
          );
        },
      },
    ];

    cols.push({
      dataIndex: 'new',
      title: 'New',
      className: 'bl2',
      children: sections.map((s, ind) => {
        return {
          dataIndex: `new_${s}_qty`,
          width: 60,
          className: ind == 0 ? 'bl2' : '',
          title: (
            <div className="rotate-rl relative" style={{ left: 'calc(50% - 8px)', position: 'relative' }}>
              {sectionLabels[s]}
            </div>
          ),
          render(dom, record: any) {
            const qtyNew = sn(record[`new_${s}_qty`] ?? 0);
            const qtyLabeled = sn(record[`labeled_${s}_qty`] ?? 0);
            const qty = qtyNew - qtyLabeled;

            const qtyMissing = sn(record[`new_${s}_missing`] ?? 0);

            return qty ? (
              <div className="text-center">
                {!!record.children?.length && (
                  <div className="absolute" style={{ top: 0, left: 0 }}>
                    <Button
                      type="default"
                      size="small"
                      title="Create a picklist"
                      className="ant-btn-xs"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleCreatePicklistOnClick(
                          'new',
                          `${s}`,
                          { from: record.order_date, to: record.order_date },
                          { shipping_provider_name: record.shipping_provider_name },
                        );
                      }}
                    >
                      P
                    </Button>
                  </div>
                )}
                <span>{ni(qty)}</span>
                {/* <span> / </span> <span style={{ fontSize: 9 }}>{ni(qtyMissing, true)}</span> */}
              </div>
            ) : null;
          },
          onCell: (record: any) => {
            const qtyNew = sn(record[`new_${s}_qty`] ?? 0);
            const qtyLabeled = sn(record[`labeled_${s}_qty`] ?? 0);
            const qty = qtyNew - qtyLabeled;

            const qtyMissing = sn(record[`new_${s}_missing`] ?? 0);

            if (qty) {
              return {
                className: 'cursor-pointer' + (qtyMissing ? ' bg-light-orange' : ''),
                onClick: () => {
                  handleOnClick(
                    'new',
                    `${s}`,
                    { from: record.order_date, to: record.order_date },
                    { shipping_provider_name: record.shipping_provider_name },
                  );
                },
              };
            } else return {};
          },
        };
      }),
    });

    cols.push({
      dataIndex: 'hold',
      title: 'On Hold',
      className: 'bl2',
      children: sections.map((s, ind) => {
        return {
          dataIndex: `hold_${s}_qty`,
          width: 60,
          title: (
            <div className="rotate-rl relative" style={{ left: 'calc(50% - 8px)', position: 'relative' }}>
              {sectionLabels[s]}
            </div>
          ),
          className: ind == 0 ? 'bl2' : '',
          render(dom, record: any) {
            return <div className="text-center">{ni(record[`hold_${s}_qty`])}</div>;
          },
          onCell: (record: any) => {
            // const qtyNew = sn(record[`new_${s}_qty`] ?? 0);
            // const qtyLabeled = sn(record[`labeled_${s}_qty`] ?? 0);
            const qty = sn(record[`hold_${s}_qty`] ?? 0);
            if (qty) {
              return {
                className: 'cursor-pointer',
                onClick: () => {
                  handleOnClick(
                    'hold',
                    `${s}`,
                    { from: record.order_date, to: record.order_date },
                    { shipping_provider_name: record.shipping_provider_name },
                  );
                },
              };
            } else return {};
          },
        };
      }),
    });

    cols.push({
      dataIndex: 'label',
      title: 'Label Requested',
      className: 'bl2',
      children: sections.map((s, ind) => {
        return {
          dataIndex: `labeled_${s}_qty`,
          width: 60,
          title: (
            <div className="rotate-rl relative" style={{ left: 'calc(50% - 8px)', position: 'relative' }}>
              {sectionLabels[s]}
            </div>
          ),
          className: ind == 0 ? 'bl2' : '',
          render(dom, record: any) {
            const qtyLabeled = sn(record[`labeled_${s}_qty`] ?? 0);
            const qtyPacked = sn(record[`packed_${s}_qty`] ?? 0);
            const qty = qtyLabeled - qtyPacked;
            return <div className="text-center">{ni(qty)}</div>;
          },
          onCell: (record: any) => {
            const qtyLabeled = sn(record[`labeled_${s}_qty`] ?? 0);
            const qtyPacked = sn(record[`packed_${s}_qty`] ?? 0);
            const qty = qtyLabeled - qtyPacked;
            if (qty) {
              return {
                className: 'cursor-pointer',
                onClick: () => {
                  handleOnClick(
                    'labeled',
                    `${s}`,
                    { from: record.order_date, to: record.order_date },
                    { shipping_provider_name: record.shipping_provider_name },
                  );
                },
              };
            } else return {};
          },
        };
      }),
    });

    cols.push({
      dataIndex: 'packed',
      title: 'Packed',
      className: 'bl2',
      children: sections.map((s, ind) => {
        return {
          dataIndex: `packed_${s}_qty`,
          width: 60,
          title: (
            <div className="rotate-rl relative" style={{ left: 'calc(50% - 8px)', position: 'relative' }}>
              {sectionLabels[s]}
            </div>
          ),
          className: ind == 0 ? 'bl2' : '',
          render(dom, record: any) {
            return <div className="text-center">{ni(record[`packed_${s}_qty`])}</div>;
          },
          onCell: (record: any) => {
            const qtyPacked = sn(record[`packed_${s}_qty`] ?? 0);
            const qty = qtyPacked;
            if (qty) {
              return {
                className: 'cursor-pointer',
                onClick: () => {
                  handleOnClick(
                    'packed',
                    `${s}`,
                    { from: record.order_date, to: record.order_date },
                    { shipping_provider_name: record.shipping_provider_name },
                  );
                },
              };
            } else return {};
          },
        };
      }),
    });

    return cols;
  }, [searchFormRef]);

  return (
    <>
      <ProTable<OrderShippingByProviderReportRowType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Shipping Summary</span>
            <Space size={16} style={{ opacity: statsLoading ? 0.5 : 1 }}>
              <Button
                type={orderCountToBeOpened ? 'primary' : 'default'}
                key="open-pre-pre-summary"
                size="small"
                title="Open Pre Picklist List Summary modal of all open orders. \nQty: Number of orders that includes items to be opened"
                onClick={() => {
                  setOpenPrePicklistSummaryListModal(true);
                }}
              >
                Pre Picklist{orderCountToBeOpened ? ` (${orderCountToBeOpened})` : ''}
              </Button>
              <Button
                type={orderCountWithError ? 'primary' : 'default'}
                ghost={!!orderCountWithError}
                danger={!!orderCountWithError}
                size="small"
                title="Open processing orders with address errors in new tab."
                onClick={() => {
                  window.open(`/orders/master?hasWarnings=1`, '_blank');
                }}
              >
                Errors{` (${ni(orderCountWithError, true)})`}
              </Button>
              <Button
                type={missingItemsQty ? 'primary' : 'default'}
                ghost={!!missingItemsQty}
                danger={!!missingItemsQty}
                size="small"
                title="Open processing orders with missing items."
                onClick={() => {
                  setOpenPrePicklistSummaryListModal2(true);
                }}
              >
                Missing Items{` (${ni(missingItemsQty, true)})`}
              </Button>
              <Button
                type="link"
                size="small"
                title="Reload stats only."
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchProblematicOrderStats();
                  actionRef.current?.reload();
                }}
              />
            </Space>
          </Space>
        }
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        bordered
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(
            Util.getSfValues('sf_order_shipping_by_provider_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef?.current?.getFieldsValue();
          Util.setSfValues('sf_order_shipping_by_provider', searchFormValues);
          Util.setSfValues('sf_order_shipping_by_provider_p', params);

          setLoading(true);
          return getOrderShippingByProviderReport(
            {
              ...params,
              sections: sections,
              with: 'warnings,warnings_def,extra',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        onRow={(record) => {
          let cls = '';
          if (record.level == 1) {
            cls += ' bt2';
            if (expandedRowKeys.findIndex((uid) => uid == record.uid) >= 0) {
              cls += ' expanded';
            }
          }

          return { className: cls };
        }}
        expandable={{
          expandRowByClick: false,
          showExpandColumn: true,
          expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            setExpandedRowKeys(expandedKeys as any);
          },
          rowExpandable(record) {
            return sn(record.children?.length) > 0;
          },
          indentSize: 0,
          expandedRowClassName: (record, index, indent) => 'row-expanded',
        }}
        toolBarRender={(action, rows) => []}
        columnEmptyText=""
      />

      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
        reloadParent={() => actionRef.current?.reload()}
      />

      <PrePicklistSummaryListModal
        modalVisible={openPrePicklistSummaryListModal}
        handleModalVisible={setOpenPrePicklistSummaryListModal}
        reloadParent={() => actionRef.current?.reload()}
        noStocksOnly={false}
      />

      <PrePicklistSummaryListModal
        modalVisible={openPrePicklistSummaryListModal2}
        handleModalVisible={setOpenPrePicklistSummaryListModal2}
        reloadParent={() => actionRef.current?.reload()}
        noStocksOnly={missingItemsQty > 0}
      />
    </>
  );
};

export default OrderShippingReportByProvider;
