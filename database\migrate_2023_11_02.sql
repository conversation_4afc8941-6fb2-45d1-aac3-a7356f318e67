-- =====================================================================
-- GDSN Shipping provider
-- =====================================================================
CREATE TABLE `xmag_shipping_provider`
(
    `id`       int(11)      NOT NULL AUTO_INCREMENT,
    `name`     varchar(255) NOT NULL,
    `mag_code` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `QU_xmag_shipping_provider_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('1', 'DPD', NULL);
insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('2', 'DPD Express', NULL);
insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('3', 'DHL', NULL);
insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('4', 'DHL Express', NULL);
insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('5', 'GLS', NULL);
insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('6', 'GLS Express', NULL);
insert into `xmag_shipping_provider` (`id`, `name`, `mag_code`)
values ('7', 'Other', NULL);


CREATE TABLE `xmag_shipping_desc_provider_map`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `shipping_desc` varchar(255) DEFAULT NULL COMMENT 'shipping description shortcut in xmag_order shipping_description',
    `provider_name` varchar(255) DEFAULT NULL COMMENT 'FK: name in xmag_shipping_provider',
    PRIMARY KEY (`id`),
    KEY `IDX_xmag_shipping_desc_provider_map_shipping_desc` (`shipping_desc`),
    KEY `FK_xmag_shipping_desc_provider_map_provider_name` (`provider_name`),
    CONSTRAINT `FK_xmag_shipping_desc_provider_map_provider_name` FOREIGN KEY (`provider_name`) REFERENCES `xmag_shipping_provider` (`name`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

drop table `xmag_order_extra`;
CREATE TABLE `xmag_order_extra`
(
    `entity_id`                      int(11) NOT NULL COMMENT 'PK: Order ID',
    `shipping_provider_name_old`     varchar(255) DEFAULT NULL COMMENT 'FK: assigned provider',
    `shipping_provider_name`         varchar(255) DEFAULT NULL COMMENT 'FK: assigned provider',
    `shipping_provider_change_notes` text         DEFAULT NULL COMMENT 'Notes for shifted shipping provider',
    `shipping_provider_change_user`  int(11)      DEFAULT NULL COMMENT 'Changed by: UserId',
    `shipping_notes`                 varchar(255) DEFAULT NULL COMMENT 'Shipping notes',
    `shipping_address_status`        varchar(31)  DEFAULT NULL COMMENT 'Result of shipping address: Open,Problem,Done',
    `shipping_address_check_detail`  text         DEFAULT NULL COMMENT 'Address check result detail',
    PRIMARY KEY (`entity_id`),
    KEY `xmag_order_extra_provider_name_old` (`shipping_provider_name_old`),
    KEY `xmag_order_extra_provider_name` (`shipping_provider_name`),
    CONSTRAINT `FK_xmag_order_extra_entity_id` FOREIGN KEY (`entity_id`) REFERENCES `xmag_order` (`entity_id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `xmag_order_extra_provider_name` FOREIGN KEY (`shipping_provider_name`) REFERENCES `xmag_shipping_provider` (`name`) ON UPDATE CASCADE,
    CONSTRAINT `xmag_order_extra_provider_name_old` FOREIGN KEY (`shipping_provider_name_old`) REFERENCES `xmag_shipping_provider` (`name`) ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

ALTER TABLE `xmag_shipping_desc_provider_map`
    DROP INDEX `IDX_xmag_shipping_desc_provider_map_shipping_desc`,
    ADD UNIQUE INDEX `IDX_xmag_shipping_desc_provider_map_shipping_desc` (`shipping_desc`);

insert into xmag_shipping_desc_provider_map(shipping_desc)
select LEFT(`shipping_description`, 40)
from xmag_order
where shipping_description is not null
  and not exists(select 1
                 from xmag_shipping_desc_provider_map m
                 where m.shipping_desc = LEFT(xmag_order.`shipping_description`, 40))
group by LEFT(`shipping_description`, 40);

-- =====================================================================
-- GDSN Provider (system)
-- =====================================================================
CREATE TABLE `gdsn_provider`
(
    `provider_gln` varchar(255) NOT NULL COMMENT 'Provider GLN in GDSN',
    `name`         varchar(255) DEFAULT NULL COMMENT 'FsOne Provider Name',
    PRIMARY KEY (`provider_gln`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `gdsn_provider_trademark`
(
    `provider_gln` varchar(255) NOT NULL,
    `trademark_id` int(11)      NOT NULL,
    PRIMARY KEY (`provider_gln`, `trademark_id`),
    KEY `FK_gdsn_provider_trademark_trademark_id` (`trademark_id`),
    CONSTRAINT `FK_gdsn_provider_trademark_provider_gln` FOREIGN KEY (`provider_gln`) REFERENCES `gdsn_provider` (`provider_gln`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_gdsn_provider_trademark_trademark_id` FOREIGN KEY (`trademark_id`) REFERENCES `trademark` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;