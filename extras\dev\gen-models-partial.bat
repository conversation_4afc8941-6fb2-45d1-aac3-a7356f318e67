F:

cd F:\htdocs\laravel-app

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagQuote --table-name=xmag_quote --output-path=Models/Magento --namespace=App\Models\Magento
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagQuoteItem --table-name=xmag_quote_item --output-path=Models/Magento --namespace=App\Models\Magento

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Offer --table-name=offer --output-path=Models/Offer --namespace=App\Models\Offer
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferItem --table-name=offer_item --output-path=Models/Offer --namespace=App\Models\Offer
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel WarehousePicklistTimeTracking --table-name=warehouse_picklist_time_tracking --output-path=Models/Warehouse --namespace=App\Models\Warehouse

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProduct --table-name=mop_product --output-path=Models/MopProduct --namespace=App\Models\MopProduct
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProductFile --table-name=mop_product_file --output-path=Models/MopProduct --namespace=App\Models\MopProduct
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProductText --table-name=mop_product_text --output-path=Models/MopProduct --namespace=App\Models\MopProduct
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MopProductPrice --table-name=mop_product_price --output-path=Models/MopProduct --namespace=App\Models\MopProduct

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagProduct --table-name=xmag_product --output-path=Models/Magento --namespace=App\Models\Magento

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel TmpImport --table-name=tmp_import --output-path=Models/Import --namespace=App\Models\Import

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferItemIboPreMap --table-name=offer_item_ibo_pre_map --output-path=Models/Offer --namespace=App\Models\Offer

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EanPriceStable --table-name=ean_price_stable

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Trademark --table-name=trademark
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel TrademarkGroup --table-name=trademark_group

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderParcel --table-name=xmag_order_parcel --output-path=Models/Magento --namespace=App\Models\Magento
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderParcelLog --table-name=xmag_order_parcel_log --output-path=Models/Magento --namespace=App\Models\Magento

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel StockStableProblem --table-name=stock_stable_problem --output-path=Models --namespace=App\Models\StockStableBooked

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagOrderUserActionLog --table-name=xmag_order_user_action_log --output-path=Models/Magento --namespace=App\Models\Magento

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferItemDelivered --table-name=offer_item_delivered --output-path=Models/Offer --namespace=App\Models\Offer

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferRecvWeight --table-name=offer_recv_weight --output-path=Models/Offer --namespace=App\Models\Offer

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferRecvFile --table-name=offer_recv_file --output-path=Models/Offer --namespace=App\Models\Offer

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SysLog --table-name=sys_log --output-path=Models\Sys --namespace=App\Models\Sys

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferItemShipped --table-name=offer_item_shipped --output-path=Models/Offer --namespace=App\Models\Offer

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel IboPreManagementFile --table-name=ibo_pre_management_file --output-path=Models --namespace=App\Models

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ItemStats --table-name=item_stats --output-path=Models --namespace=App\Models

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CustomerPrice --table-name=customer_price --output-path=Models\Customer --namespace=App\Models\Customer

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ImportEanDisabled --table-name=import_ean_disabled --output-path=Models\Import --namespace=App\Models\Import

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagCustomer --table-name=xmag_customer --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel MagCustomerAddress --table-name=xmag_customer_address --output-path=Models/Magento --namespace=App\Models\Magento
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ZImportSupplierXlsEan --table-name=z_import_supplier_xls_ean --output-path=Models/Import --namespace=App\Models\Import


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SysImportRwColMap --table-name=sys_import_rw_col_map --output-path=Models\Sys --namespace=App\Models\Sys

rem "Done"
