import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
import { updateProducer } from '@/services/foodstore-one/BasicData/producer';

const handleUpdate = async (fieldsParam: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  const fields = { ...fieldsParam };
  if (fields?.trademarks) {
    if (fields.trademarks[0].id) {
      // @ts-ignore
      fields.trademarks = fields.trademarks.map((x) => x.id);
    }
  }

  try {
    await updateProducer(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!');
    return false;
  }
};

export type FormValueType = Partial<API.Producer>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Producer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Trademark) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  countries: any[];
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update trademark'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormText name="street" label="Street" width="md" />
      <ProFormText name="zip" label="Zip" width="md" />
      <ProFormText name="city" label="City" width="md" />
      <ProFormSelect
        name="country"
        label="Country"
        showSearch
        options={props.countries}
        formItemProps={{ initialValue: 'DE' }}
        width="md"
      />
      <ProFormSelect
        name="trademarks"
        label="Trademarks"
        placeholder="Please select trademarks"
        mode="multiple"
        request={getTrademarkListSelectOptions}
      />
    </ModalForm>
  );
};

export default UpdateForm;
