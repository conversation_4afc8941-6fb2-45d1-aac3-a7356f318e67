import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Pop<PERSON>,
  <PERSON>,
  Space,
  Spin,
  Tag,
  Typography,
  notification,
} from 'antd';
import { Card } from 'antd';
import { message } from 'antd';
import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';

import Util, { nf2, nf3, ni, sUrlByTpl, skuToItemId, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormRadio, ProFormText, ProFormTextArea } from '@ant-design/pro-form';

import styles from './OrderDetail.less';

import { useLocation, useModel } from 'umi';
import {
  CheckSquareFilled,
  CommentOutlined,
  EditOutlined,
  EyeOutlined,
  FilePdfOutlined,
  FileTextFilled,
  FileTextOutlined,
  LinkOutlined,
  PrinterOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { exportPicklistDeliveryNoteByEntityId } from '@/services/foodstore-one/Warehouse/picklist';
import {
  DictCode,
  EURO,
  MagentoOrderStatusOptions,
  MagOrderExtraStatus,
  MagOrderExtraStatusOptions,
  OrderUserActionLogNote,
  OrderUserActionLogType,
  StockStableStatusOptionsKv,
  UserRole,
} from '@/constants';
import ExpDate from '@/pages/Report/Order/components/ExpDate';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import { FullAddress, isOrderAddressEditable } from '.';
import OrderShipmentCommentsList from '@/pages/Warehouse/PicklistDetail/components/OrderShipmentCommentsList';
import printJS from 'print-js';
import UpdateOrderExtraFormModal from './components/UpdateOrderExtraFormModal';
import { exportShippingLabels, getOrdersList, updateOrderExtra } from '@/services/foodstore-one/Magento/order';
import useFileDownloadAndPrintApi from '@/hooks/BasicData/useFileDownloadAndPrintApi';
import { addStockStableByReturn } from '@/services/foodstore-one/Stock/stock-stable';
import UpdateAddressesModal from './components/UpdateAddressesModal';
import SEbayOrderNo from './components/SEbayOrderNo';
import StockStableQtyByLocationModal from './components/StockStableQtyByLocationModal';
import { createOrderUserActionLog } from '@/services/foodstore-one/Magento/order-user-action-log';
import SOrderNoLinkToMagAdmin from './components/SOrderNoLinkToMagAdmin';
import MagOrderUserActionLogModal from '@/pages/Audit/MagOrderUserActionLog/MagOrderUserActionLogModal';

export const isBookReturned = (order?: API.Order) => {
  const returnQtys = order?.mag_order_items?.reduce((prev, x) => prev + sn(x.detail?.qty), 0);
  return sn(order?.total_qty_ordered) == returnQtys;
};

export const isBookReturnedByItem = (orderItem?: API.OrderItem) => {
  return sn(orderItem?.qty_ordered) == sn(orderItem?.detail?.qty);
};

type SearchFormValueType = Partial<API.Order>;
type Note1FormValueType = Partial<API.OrderExtra>;

const OrderDetail: React.FC = (props) => {
  const location: any = useLocation();
  const { initialState } = useModel('@@initialState');

  const { getDictByCode, storeWebsiteKv, getParcelUrl } = useModel('app-settings');
  const { downloadB64AndCallDirectPrintApi, callDirectPrintApi } = useFileDownloadAndPrintApi();

  const searchFormRef = useRef<ProFormInstance>();
  const note1FormRef = useRef<ProFormInstance<Note1FormValueType>>();
  // const formRef = useRef<ProFormInstance<FormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingP, setLoadingP] = useState<boolean>(false);
  const [openOrderUserActionLogModal, setOpenOrderUserActionLogModal] = useState<boolean>(false);

  // order
  const [order, setOrder] = useState<API.Order>();

  // stock qty modal
  /* const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [currentOrderItemRow, setCurrentOrderItemRow] = useState<API.OrderItem>();  */
  const [openUpdateOrderExtraModal, setOpenUpdateOrderExtraModal] = useState<boolean>(false);

  // comments modal
  const [openCommentsModal, setOpenCommentsModal] = useState<boolean>(false);

  const [warningsDefCount, setWarningsDefCount] = useState<number>(0);

  const [openUpdateAddressModal, setOpenUpdateAddressModal] = useState<boolean>(false);

  const loadOrderDetail = useCallback(async (params?: Record<string, any>) => {
    const searchFormValues = searchFormRef.current?.getFieldsValue();
    Util.setSfValues('sf_order_detail', searchFormValues);
    if (searchFormValues.entity_id || searchFormValues.increment_id_suffix) {
      setLoading(true);

      return getOrdersList(
        {
          ...params,
          ...searchFormValues,
          with: 'itemEan.magInventoryStocksQty,itemEan.files,itemEan.stockStables,warnings,warnings_def,extra,ext,latestShipping,labels,orderDetail,emailStat,orderItem.stockStableBookedList,orderItem.picklistDetail,userActionLogsCount',
          limit: 1,
        },
        {},
        {},
      )
        .then((res) => {
          if (res) {
            const firstRow = res.data[0];
            let isAllBookedCheck = true;
            for (const x of res?.data ?? []) {
              for (const item of x?.mag_order_items ?? []) {
                // console.log(item?.picklist_detail?.is_stock_stable_updated == 1);
                isAllBookedCheck &&= item?.picklist_detail?.is_stock_stable_updated == 1;
              }
            }
            // console.log('all booked?', isAllBookedCheck);
            setWarningsDefCount(res.data.reduce((prev, current) => prev + (current.warnings_def ? 1 : 0), 0));
            setOrder(firstRow);
            return { success: true, data: firstRow?.mag_order_items, total: firstRow?.mag_order_items || 0 };
          }
          return [];
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    } else {
      setOrder(undefined);
      return [];
    }
  }, []);

  useEffect(() => {
    if (location.query.entity_id) {
      searchFormRef.current?.setFieldValue('entity_id', location.query.entity_id);
      loadOrderDetail();
    }
  }, [loadOrderDetail, location.query]);

  useEffect(() => {
    loadOrderDetail();
  }, [loadOrderDetail]);

  useEffect(() => {
    if (order?.entity_id) {
      createOrderUserActionLog({
        type: OrderUserActionLogType.OrderDetail,
        note: `Search Order #${order?.entity_id}`,
        order_id: order?.entity_id,
      });
    }
  }, [order?.entity_id]);

  // Stock stable row
  const [currentSSRow, setCurrentSSRow] = useState<API.StockStable>();
  // Stock detail modal on location
  const [openStockStableQtyByLocationModal, setOpenStockStableQtyByLocationModal] = useState<boolean>(false);

  const smColumns: ProColumns<Partial<API.StockStable | API.StockStableBooked>>[] = useMemo(
    () => [
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'name'],
        editable: false,
        width: 70,
        align: 'center',
        render: (dom, entity) => {
          return (
            <span
              className="cursor-pointer c-blue"
              onClick={() => {
                // Check if stock_stable or stock_stable_booked?
                setCurrentSSRow(
                  (entity as any)?.stock_stable_id ? { ...entity, id: (entity as any)?.stock_stable_id } : entity,
                );
                setOpenStockStableQtyByLocationModal(true);
              }}
            >
              {entity?.warehouse_location?.name}
            </span>
          );
        },
      },
      {
        title: 'Priority',
        dataIndex: ['warehouse_location', 'priority'],
        editable: false,
        width: 90,
        align: 'right',
        render: (dom, record) => {
          return ni(record.warehouse_location?.priority);
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 80,
        align: 'center',
        editable: false,
        render: (dom, record) => {
          return <ExpDate date={record.exp_date} />;
        },
      },
      {
        title: 'IBO ID',
        dataIndex: ['ibo', 'id'],
        width: 90,
        align: 'left',
        editable: false,
        render: (__, record) => {
          return record.ibo ? (
            <>
              <Typography.Link
                href={`/ibo/ibo-register?ibomId=${record.ibo?.ibom_id}&sku=${record.item_id}_`}
                target="_blank"
              >
                #{record.ibo_id}
              </Typography.Link>
              &nbsp; (
              <a
                href={sUrlByTpl(getDictByCode(DictCode.LOTUS_PATH), {
                  orderNo: record.ibo?.ibom?.order_no,
                })}
                target="_blank"
              >
                {record.ibo?.ibom?.order_no}
              </a>
              )
            </>
          ) : null;
        },
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 80,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'Status',
        dataIndex: ['status'],
        dataType: 'select',
        width: 90,
        editable: false,
        valueEnum: StockStableStatusOptionsKv,
      },
    ],
    [getDictByCode],
  );

  const columns: ProColumns<API.OrderItem>[] = useMemo(
    () => [
      {
        title: 'SKU',
        dataIndex: 'sku',
        width: 100,
        tooltip: 'Click to view stock detail.',
        copyable: true,
        onCell: (recordOI) => {
          return {
            /* className: 'cursor-pointer c-blue',
            onClick: (e) => {
              setCurrentOrderItemRow(recordOI);
              handleQtyModalVisible(true);
            }, */
          };
        },
        render: (__, entity) => {
          const isUserOrAdmin =
            initialState?.currentUser?.role == UserRole.USER || initialState?.currentUser?.role == UserRole.ADMIN;

          return isUserOrAdmin ? (
            <Typography.Link href={`/item/ean-all-summary?sku=${skuToItemId(entity.sku)}_`} target="_blank" copyable>
              {entity.sku}
            </Typography.Link>
          ) : (
            <Typography.Text copyable>{entity.sku}</Typography.Text>
          );
        },
      },
      {
        title: 'EAN',
        dataIndex: ['item_ean', 'ean'],
        width: 140,
        copyable: true,
      },
      { title: 'Name', dataIndex: 'name', width: 300 },
      { title: 'Ordered Qty', dataIndex: 'qty_ordered', width: 60 },
      {
        title: 'Open Qty / Stock',
        dataIndex: 'open_qty_ordered',
        width: 100,
        tooltip:
          'Open orders qty pcs & Stock qty pcs. If Open > Stock Qty, RED background and you need to pay attention to control stocks .',
        render: (dom, r) => {
          const openQtyPcs = sn(r.open_qty_ordered);
          const stockQtyPcs = sn(
            r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
          );
          return (
            <Row gutter={8} className="c-grey text-right">
              <Col span={10}>{ni(openQtyPcs, true)}</Col>
              <Col span={14}>{ni(stockQtyPcs, true)}</Col>
            </Row>
          );
        },
        onCell: (r) => {
          const openQtyPcs = sn(r.open_qty_ordered);
          const stockQtyPcs = sn(
            r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
          );
          return {
            className: openQtyPcs > stockQtyPcs ? 'bg-red' : '',
          };
        },
      },
      {
        title: 'Gross Price',
        dataIndex: 'price_incl_tax',
        width: 60,
        align: 'right',
        tooltip: 'Price included tax',
        render: (dom, r) => {
          return nf2(r.price_incl_tax) + EURO;
        },
      },
      {
        title: 'Total Amount',
        dataIndex: 'price_incl_tax',
        width: 70,
        align: 'right',
        render: (dom, r) => {
          return nf2(sn(r.price_incl_tax) * sn(r.qty_invoiced)) + EURO;
        },
      },
      {
        title: 'Discount',
        dataIndex: ['item_ean', 'fs_special_discount'],
        width: 70,
        align: 'center',
        render: (dom, r) => <span className="c-orange">{r.item_ean?.fs_special_discount}</span>,
      },
      {
        title: 'Stocks',
        dataIndex: 'stock_stables',
        width: 700,
        render: (__, orderItem) => {
          const stocksListDataSource = orderItem.picklist_detail?.is_stock_stable_updated
            ? orderItem?.stock_stable_booked_list ?? []
            : orderItem?.item_ean?.stock_stables ?? [];

          if (stocksListDataSource.length) {
            const eanObj = orderItem.item_ean;
            stocksListDataSource.forEach((x) => {
              x.item_ean = {
                id: eanObj?.id,
                item_id: eanObj?.item_id,
                parent_id: eanObj?.parent_id,
                is_single: eanObj?.is_single,
                ean: eanObj?.ean,
                sku: eanObj?.sku,
                ean_text_de: eanObj?.ean_text_de,
                attr_case_qty: eanObj?.attr_case_qty,
                item_base: eanObj?.item_base,
                item_base_unit: eanObj?.item_base_unit,
                files: eanObj?.files,
              };

              x.order_item = {
                item_id: orderItem.item_id,
                order_id: orderItem.order_id,
                qty_ordered: orderItem.qty_ordered,
                sku: orderItem.sku,
              };
            });
          }

          const smColumnsNew = smColumns.filter((x) =>
            orderItem.picklist_detail?.is_stock_stable_updated ? x.title != 'Status' : true,
          );

          if (!stocksListDataSource || !stocksListDataSource?.length) return <></>;
          return (
            <ProTable
              columns={smColumnsNew}
              cardProps={{ bodyStyle: { padding: '0 0' } }}
              rowKey="id"
              headerTitle={false}
              search={false}
              options={false}
              pagination={false}
              scroll={{ y: 'auto' }}
              dataSource={stocksListDataSource}
              columnEmptyText={''}
              locale={{ emptyText: <></> }}
              size="small"
            />
          );
        },
      },
      {
        title: 'Mag. IDs',
        dataIndex: 'product_id',
        align: 'center',
        width: 90,
        tooltip: 'Product ID / Order Item ID in Magento',
        showSorterTooltip: false,
        className: 'text-sm c-grey',
        render: (__, r) => `${r.product_id} / ${r.item_id}`,
      },
      {
        title: 'Booked?',
        dataIndex: 'is_stock_stable_updated',
        align: 'center',
        width: 50,
        showSorterTooltip: false,
        className: 'p-0',
        ellipsis: true,
        fixed: 'right',
        render: (__, r) =>
          r.picklist_detail?.is_stock_stable_updated ? (
            <CheckSquareFilled className={isBookReturnedByItem(r) ? 'c-red' : 'c-green'} />
          ) : null,
      },
    ],
    [smColumns],
  );

  const orderColumns: ProDescriptionsItemProps<API.Order>[] = [
    {
      title: 'Store',
      dataIndex: ['store_id'],
      ellipsis: true,
      valueEnum: storeWebsiteKv,
      render(dom, entity, index, action, schema) {
        return storeWebsiteKv?.[sn(entity.store_id)];
      },
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      ellipsis: true,
      render: (__, record) => {
        const status = record.status;
        let color = 'default';
        switch (status ?? '') {
          case 'complete':
          case 'closed':
            color = 'success';
            break;
          case 'processing':
            color = 'blue';
            break;
          case 'pending':
            color = 'orange';
            break;
          case 'canceled':
            color = 'red';
            break;
        }

        return <Tag color={color as any}>{MagentoOrderStatusOptions[status || '-'] ?? '-'}</Tag>;
      },
    },
    {
      title: 'Ordered Qty',
      dataIndex: ['total_qty_ordered'],
      tooltip: 'Total ordered Qty in the order.',
      render: (__, record) => {
        const picklistQtyOrdered = record.mag_order_items?.reduce(
          (accumulator, currentValue) => accumulator + sn(currentValue.qty_ordered),
          0,
        );
        return picklistQtyOrdered != record.total_qty_ordered ? (
          <span>
            <b>{picklistQtyOrdered}</b> of {record.total_qty_ordered}
          </span>
        ) : (
          <b>{picklistQtyOrdered}</b>
        );
      },
    },
    {
      title: 'Name',
      dataIndex: ['sa_fullname'],
      ellipsis: true,
      render(dom, record) {
        return (
          <Typography.Text mark={record?.warn_sa_fullname || record?.warn_sa_fullname_wrong}>
            {record.sa_fullname}
          </Typography.Text>
        );
      },
    },
    {
      title: 'Delivery Address',
      dataIndex: 'sa_full',
      tooltip: 'Orange colored rows are in warnings list. Highlighted parts may be wrong!',
      ellipsis: true,
      span: 2,
      render(dom, record) {
        return (
          <Row>
            <Col flex="auto">
              <FullAddress order={record} type="shipping" />
            </Col>
            <Col flex="40">
              <Button
                type="link"
                size="small"
                icon={isOrderAddressEditable(record.status) ? <EditOutlined /> : <EyeOutlined />}
                style={{
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 12,
                  color: isOrderAddressEditable(record.status) ? 'auto' : 'grey',
                }}
                title={isOrderAddressEditable(record.status) ? 'Update addresses' : 'View addresses'}
                onClick={() => {
                  setOpenUpdateAddressModal(true);
                }}
              />
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Parcel',
      dataIndex: ['shipping_imported_list'],
      tooltip: 'Green indicates a processed shipping on Magento',
      render(dom, record) {
        return record.latest_shipping
          ? [record.latest_shipping]?.map((x) => {
              let cls = 'text-sm';
              if (x.mag_ship_id) {
                cls += ' c-green';
              }
              const title = x.carrier_code || x.title ? `${x.title} | ${x.carrier_code}` : '';
              return (
                <div key={x.id} className={cls} title={title}>
                  <a
                    href={`${getDictByCode(DictCode.MAG_ADMIN_URL_TRACKING)}${x.parcel_no}`}
                    target="_blank"
                    rel="noreferrer"
                    className={cls}
                  >
                    {x.parcel_no}
                  </a>
                </div>
              );
            })
          : null;
      },
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      tooltip: 'Weight in Magento order',
      ellipsis: true,
      span: 1,
      render(dom, record) {
        return `${nf3(record.weight, true, true)}kg`;
      },
    },
  ];

  const handlePrintLabel = async (
    service_name: 'GLS' | 'DHL' | 'GLS Express' | 'DPD',
    options?: { returnLabel?: boolean; guaranteed24Service?: boolean },
  ) => {
    if (!order?.entity_id) return;
    const hide = message.loading(`Downloading ${service_name} Label as PDF format...`, 0);
    setLoadingP(true);
    exportShippingLabels(order?.entity_id, { service_name, ...options })
      .then((res) => {
        createOrderUserActionLog({
          type: OrderUserActionLogType.OrderDetail,
          note: `${service_name} ${options?.returnLabel ? 'Return ' : ''}Label Create`,
          order_id: order?.entity_id,
          detail: {
            service_name: service_name,
            ...options,
          },
        });

        hide();
        if (res.labels) {
          setOrder((prev) => ({ ...prev, labels: res.labels }));
        }
        if (res.files?.length) {
          if (Util.isFirefox()) {
            // We open files on new tabs
            for (const file of res.files) {
              window.open(`${API_URL}/api/${file.url}`, '_blank');
            }
          } else {
            for (const file of res.files) {
              printJS({
                printable: file.b64,
                type: 'pdf',
                base64: true,
                showModal: true,
                onPrintDialogClose: () => {
                  createOrderUserActionLog({
                    type: OrderUserActionLogType.OrderDetail,
                    note: OrderUserActionLogNote.PrintTried,
                    order_id: order?.entity_id,
                    detail: {
                      service_name: service_name,
                      ...options,
                    },
                  });
                },
              });
              break;
            }
            if (res.files.length > 1) {
              notification.info({
                message: 'More labels exists. You can check them on "Print existing label" button.',
                duration: 0,
                placement: 'top',
              });
            }
          }
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoadingP(false);
      });
  };

  return (
    <PageContainer
      className={styles.orderDetailContainer}
      title={
        <Row wrap={false}>
          <Col flex="250px">
            <span>Order Detail </span>
            {!!order?.entity_id && (
              <>
                <a
                  href={`/orders/order-detail-packing?entity_id=${order?.entity_id}`}
                  title="Open Order Detail (WH) in new tab"
                  target="_blank"
                  style={{ marginLeft: 24 }}
                >
                  <LinkOutlined />
                </a>
                {/* <Typography.Link
                  style={{ display: 'inline-block', marginBottom: 0, marginLeft: 16 }}
                  href={`/monitor/order-user-action-log?order_id=${order?.entity_id || ''}`}
                  target="_blank"
                  title="View logs in the new tab"
                >
                  <FileTextOutlined />
                </Typography.Link> */}

                <Typography.Link
                  style={{ display: 'inline-block', marginBottom: 0, marginLeft: 16 }}
                  onClick={() => {
                    setOpenOrderUserActionLogModal(true);
                  }}
                  target="_blank"
                  title="View logs..."
                >
                  <FileTextOutlined />
                </Typography.Link>
              </>
            )}
          </Col>
          <Col flex="600px">
            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              initialValues={Util.getSfValues('sf_order_detail', {}, {})}
              submitter={{
                submitButtonProps: { loading: loading, htmlType: 'submit' },
                searchConfig: { submitText: 'Search' },
                onSubmit: () => {
                  loadOrderDetail();
                },
                render: (form, dom) => {
                  return [dom[1]];
                },
              }}
            >
              <ProFormText name={'entity_id'} label="Order ID" width="xs" placeholder={'Order ID'} />
              <ProFormText name={'increment_id_suffix'} label="Increment ID" width={150} placeholder={'Increment ID'} />
            </ProForm>
          </Col>
          <Col>
            <Space size={16}>
              <Button
                type="primary"
                icon={<PrinterOutlined />}
                title="Download delivery notes in PDF"
                style={{ marginRight: 32 }}
                disabled={loadingP}
                onClick={async () => {
                  if (!order?.labels?.length) {
                    message.info('No created label yet.');
                  } else {
                    setLoadingP(true);
                    const hide2 = message.loading('Direct Printing last labels...', 0);
                    const lastLabel = order.labels[0];
                    const maxPos = sn(lastLabel.pos);
                    for (let i = 0; i < maxPos; i++) {
                      if (order.labels[i]) {
                        await downloadB64AndCallDirectPrintApi(order.labels[i])
                          .then((res) => {})
                          .catch(Util.error);
                      }
                    }
                    hide2();
                    setLoadingP(false);
                  }

                  setLoadingP(true);
                  const hide = message.loading('Direct Printing delivery note...', 0);
                  await exportPicklistDeliveryNoteByEntityId(order?.entity_id, { includeB64: true })
                    .then(async (res) => {
                      hide();
                      if (res.length) {
                        for (const file of res) {
                          if (file.url) {
                            // window.open(`${API_URL}/api/${file.url}`, '_blank');
                          }
                          if (file.b64) {
                            await callDirectPrintApi(
                              { service_name: 'Delivery Note', entity_id: order?.entity_id },
                              file.b64,
                            );
                          }
                        }
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoadingP(false);
                    });
                }}
              >
                Print Label & Delivery note
              </Button>
              <Button
                type="primary"
                icon={<PrinterOutlined />}
                title="Download delivery notes in PDF"
                style={{ marginRight: 32 }}
                disabled={loadingP}
                onClick={() => {
                  const hide = message.loading('Direct Printing delivery note...', 0);
                  setLoadingP(true);
                  exportPicklistDeliveryNoteByEntityId(order?.entity_id, { includeB64: true })
                    .then(async (res) => {
                      hide();
                      if (res.length) {
                        for (const file of res) {
                          if (file.url) {
                            // window.open(`${API_URL}/api/${file.url}`, '_blank');
                          }
                          if (file.b64) {
                            await callDirectPrintApi(
                              { service_name: 'Delivery Note', entity_id: order?.entity_id },
                              file.b64,
                            );
                          }
                        }
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoadingP(false);
                    });
                }}
              >
                Print Delivery note
              </Button>
              <Button
                type="primary"
                icon={<PrinterOutlined />}
                title="Download delivery notes in PDF"
                style={{ marginRight: 32 }}
                disabled={loadingP || !order?.labels?.length}
                onClick={async () => {
                  if (!order?.labels?.length) return Promise.reject();

                  const hide = message.loading('Direct Printing last labels...', 0);
                  setLoadingP(true);
                  const lastLabel = order.labels[0];
                  const maxPos = sn(lastLabel.pos);
                  for (let i = 0; i < maxPos; i++) {
                    if (order.labels[i]) {
                      await downloadB64AndCallDirectPrintApi(order.labels[i]);
                    }
                  }
                  setLoadingP(false);
                  hide();
                }}
              >
                Print Last Created Label
              </Button>
            </Space>
          </Col>
          <Col flex="auto">
            <div key="set-notes" style={{ justifySelf: 'end' }}>
              {order?.entity_id ? (
                <Popover
                  key="set-note1"
                  trigger={['click']}
                  destroyTooltipOnHide
                  content={
                    <ProForm<Note1FormValueType>
                      layout="vertical"
                      formRef={note1FormRef}
                      isKeyPressSubmit
                      size="small"
                      className="note1-form"
                      initialValues={{
                        note1_status: order?.extra?.note1_status || '',
                        note1: order?.extra?.note1 || '',
                      }}
                      submitter={{
                        submitButtonProps: { loading: loading, htmlType: 'submit' },
                        searchConfig: { submitText: 'Save' },
                        onSubmit: () => {
                          if (order?.entity_id) {
                            const hide = message.loading('Updating...', 0);
                            updateOrderExtra(order?.entity_id, note1FormRef.current?.getFieldsValue() ?? {})
                              .then((res) => {
                                message.success('Updated successfully.');
                                setOrder((prev) => ({ ...prev, extra: res.extra }));

                                createOrderUserActionLog({
                                  type: OrderUserActionLogType.OrderDetail,
                                  note: `${res.extra?.note1_status} & Update Notes`,
                                  order_id: order?.entity_id,
                                });
                              })
                              .catch(Util.error)
                              .finally(hide);
                          }
                        },
                        onReset: (value) => {
                          note1FormRef.current?.setFieldsValue({ note1: '', note1_status: '' });
                        },
                      }}
                    >
                      <ProFormRadio.Group name="note1_status" label="Status" options={MagOrderExtraStatusOptions} />
                      <ProFormTextArea
                        name={'note1'}
                        label="Note"
                        width="md"
                        placeholder={'Note'}
                        fieldProps={{ rows: 6 }}
                      />
                    </ProForm>
                  }
                >
                  <Button
                    type={'primary'}
                    className={order?.extra?.note1_status == MagOrderExtraStatus.OnHold ? 'btn-yellow' : 'btn-green'}
                    title={
                      order?.extra?.note1_status == MagOrderExtraStatus.Success
                        ? 'Set your review note.'
                        : order.extra?.note1 || ''
                    }
                  >
                    {order?.extra?.note1_status ? order?.extra?.note1_status : 'OK'}
                  </Button>
                </Popover>
              ) : null}
            </div>
          </Col>
        </Row>
      }
      extra={
        <>
          {warningsDefCount > 0 && order?.warnings_def ? (
            <div style={{ position: 'absolute', top: 60, left: '50%', marginLeft: -150 }}>
              <Alert
                key="warn-def"
                message={
                  <div>
                    <span>WARNING: Invalid delivery addresses detected! </span>
                    <Popover
                      title="Delivery address warning"
                      content={order.warnings_def.split('^').map((x, ind) => (
                        <div key={ind}>{x}</div>
                      ))}
                    >
                      <WarningOutlined style={{ marginLeft: 16, color: 'red' }} />
                    </Popover>
                  </div>
                }
                type="error"
                style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
              />
            </div>
          ) : null}
        </>
      }
    >
      <Card
        style={{ marginBottom: 16 }}
        bodyStyle={{ paddingTop: 1 }}
        bordered={false}
        title={
          <Space size={24}>
            <span>Order Info</span>
            {order && (
              <>
                <SOrderNoLinkToMagAdmin entity_id={order.entity_id} copyable />
                <Typography.Link
                  style={{ display: 'inline-block', marginBottom: 0 }}
                  href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${
                    order?.entity_id
                  }/`}
                  target="_blank"
                  copyable
                >
                  {order.increment_id}
                </Typography.Link>
                <div style={{ display: 'inline-block', marginBottom: 0 }}>{Util.dtToDMY(order?.created_at)}</div>
                <Tag color="green" style={{ marginLeft: 16, marginRight: 16 }} title="Shipping provider">
                  {order?.extra?.shipping_provider_name ?? 'N/A'}
                </Tag>
                {order.increment_id?.startsWith('EB') || order.increment_id?.startsWith('KL') ? (
                  <SEbayOrderNo order={order} />
                ) : null}
                {sn(order?.mag_order_shipment_comments_count) > 0 ? (
                  <Badge size="small" offset={[6, -2]} count={sn(order?.mag_order_shipment_comments_count)}>
                    <CommentOutlined
                      style={{ color: 'red' }}
                      className="cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenCommentsModal(true);
                      }}
                    />{' '}
                  </Badge>
                ) : (
                  <></>
                )}
              </>
            )}
          </Space>
        }
        extra={
          order ? (
            <Space size={8}>
              <span>Delivery Note Printed:</span>
              <span style={{ fontSize: 16, fontWeight: 'bold' }}>{`${
                order?.order_user_action_log_count_delivery_note_printed ?? 0
              }`}</span>
              <span> Label Printed:</span>
              <span style={{ fontSize: 16, fontWeight: 'bold' }}>{`${
                order?.order_user_action_log_count_label_printed ?? 0
              }`}</span>
            </Space>
          ) : null
        }
      >
        <Row style={{ marginTop: 16 }} gutter={16}>
          <Col sm={24} md={24} lg={24} xl={24} xxl={16}>
            <Spin spinning={loading}>
              <ProDescriptions<API.Order>
                column={{ sm: 1, md: 1, lg: 2, xl: 4, xxl: 4 }}
                dataSource={order}
                columns={orderColumns}
                bordered
                size="middle"
              />
            </Spin>
          </Col>
          <Col sm={24} md={24} lg={24} xl={24} xxl={8}>
            <ProDescriptions<API.Order>
              column={{ sm: 1, md: 1, lg: 1, xl: 1, xxl: 2 }}
              dataSource={order}
              labelStyle={{ width: 100 }}
              columns={[
                {
                  title: 'Mails',
                  dataIndex: ['emails_count'],
                  ellipsis: true,
                  render: (dom, r) => (
                    <a href={`/email/list?order_id=${order?.entity_id}`} target="_blank" rel="noreferrer">
                      {ni(r.emails_count)}
                    </a>
                  ),
                },
                {
                  title: 'Cases',
                  dataIndex: ['crm_cases_count'],
                  ellipsis: true,
                  render: (dom, r) => (
                    <a href={`/email/list?order_id=${order?.entity_id}`} target="_blank" rel="noreferrer">
                      {ni(r.crm_cases_count)}
                    </a>
                  ),
                },
              ]}
              bordered
              size="middle"
            />
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Spin spinning={loading}>
              <ProTable<API.OrderItem, API.PageParams>
                actionRef={actionRef}
                rowKey="entity_id"
                headerTitle="Items List"
                revalidateOnFocus={false}
                options={{ fullScreen: true, density: false, setting: false }}
                search={false}
                sticky
                size="small"
                scroll={{ x: 800 }}
                cardProps={{ bodyStyle: { padding: 0 } }}
                onRequestError={Util.error}
                dataSource={order?.mag_order_items ?? []}
                request={(params, sort, filter) => {
                  return loadOrderDetail() as any;
                }}
                pagination={{ defaultPageSize: 500 }}
                columns={columns}
                toolBarRender={(action, rows) => [
                  <Button
                    type="primary"
                    key="export-delivery-note"
                    size="small"
                    icon={<FilePdfOutlined />}
                    title="Download delivery notes in PDF"
                    style={{ marginRight: 32 }}
                    disabled={loadingP}
                    onClick={() => {
                      const hide = message.loading('Downloading delivery note as PDF format...', 0);
                      setLoadingP(true);
                      exportPicklistDeliveryNoteByEntityId(order?.entity_id)
                        .then((res) => {
                          hide();
                          if (res.length) {
                            res.forEach((file) => {
                              if (file.url) {
                                window.open(`${API_URL}/api/${file.url}`, '_blank');
                              }
                            });
                          }
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                          setLoadingP(false);
                        });
                    }}
                  >
                    Delivery note
                  </Button>,

                  <Popover
                    key="re-print"
                    trigger={['click', 'hover']}
                    content={order?.labels?.map((x) => (
                      <Row key={x.id} gutter={12} style={{ width: 320, alignItems: 'center' }}>
                        <Col className="text-sm" span={4}>
                          <Typography.Text ellipsis>{x.service_name}</Typography.Text>
                        </Col>
                        <Col className="text-sm" span={14}>
                          <Typography.Link
                            copyable
                            href={getParcelUrl(x.parcel_no)}
                            target="_blank"
                            // style={{ textDecoration: x.ref_no?.endsWith(',Return') ? 'line-through' : '' }}
                          >
                            {x.track_id}
                          </Typography.Link>
                        </Col>
                        <Col span={1} className="text-sm c-red">
                          <span title="Return Label">{x.ref_no?.endsWith(',Return') ? 'R' : ''}</span>
                        </Col>
                        <Col span={5}>
                          <Space size={3}>
                            <Typography.Link href={`${API_URL}/api/${x.url}`} title="Open Label PDF" target="_blank">
                              <LinkOutlined />
                            </Typography.Link>
                            <Button
                              type="link"
                              size="small"
                              icon={<PrinterOutlined />}
                              title="Print Label"
                              style={{ height: 20 }}
                              onClick={() => {
                                downloadB64AndCallDirectPrintApi(x, 'browserPrinter');
                              }}
                            />
                            <Button
                              type="link"
                              size="small"
                              icon={<PrinterOutlined style={{ color: 'green' }} />}
                              title="Print label directly"
                              style={{ height: 20 }}
                              onClick={() => {
                                downloadB64AndCallDirectPrintApi(x);
                              }}
                            />
                          </Space>
                        </Col>
                      </Row>
                    ))}
                  >
                    <Button
                      type="primary"
                      size="small"
                      icon={<PrinterOutlined />}
                      title="Re-print Labels"
                      disabled={!order?.labels?.length}
                    >
                      {`Print existing label ${order?.labels?.length ? ` (${order?.labels?.length})` : ''}`}
                    </Button>
                  </Popover>,

                  <Button
                    type="primary"
                    key="export-labels-GLS"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print GLS Labels in PDF"
                    disabled={loadingP}
                    onClick={() => {
                      handlePrintLabel('GLS');
                    }}
                  >
                    GLS
                  </Button>,

                  <Popconfirm
                    key="export-labels-GLS2"
                    title={<>Are you sure to create a Guaranteed NextDay label at extra costs? </>}
                    okText="Yes"
                    cancelText="No"
                    overlayStyle={{ maxWidth: 300 }}
                    onConfirm={() => {
                      handlePrintLabel('GLS', { guaranteed24Service: true });
                    }}
                  >
                    <Button
                      type="default"
                      key="export-labels-GLS2"
                      size="small"
                      icon={<PrinterOutlined />}
                      title="Print GLS2  Labels in PDF"
                      disabled={loadingP}
                    >
                      GLS 24H
                    </Button>
                  </Popconfirm>,

                  <Button
                    type="primary"
                    key="export-labels-DHL"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print DHL Labels in PDF"
                    disabled={loadingP}
                    onClick={() => {
                      handlePrintLabel('DHL');
                    }}
                  >
                    DHL
                  </Button>,
                  <Button
                    type="primary"
                    key="export-labels-DPD"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print DPD Labels in PDF"
                    disabled={loadingP}
                    onClick={() => {
                      handlePrintLabel('DPD');
                    }}
                  >
                    DPD
                  </Button>,

                  <Button
                    type="default"
                    danger
                    key="export-return-labels-GLS"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print GLS Return Labels in PDF"
                    style={{ marginLeft: 32 }}
                    disabled={loadingP}
                    onClick={() => {
                      handlePrintLabel('GLS', { returnLabel: true });
                    }}
                  >
                    GLS Return
                  </Button>,

                  <Button
                    type="default"
                    danger
                    key="export-return-labels-DHL"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print DHL Return Labels in PDF"
                    disabled={loadingP}
                    onClick={() => {
                      handlePrintLabel('DHL', { returnLabel: true });
                    }}
                  >
                    DHL Return
                  </Button>,

                  <Button
                    type="default"
                    danger
                    key="export-return-labels-DPD"
                    size="small"
                    icon={<PrinterOutlined />}
                    title="Print DPD Return Labels in PDF"
                    disabled={loadingP}
                    onClick={() => {
                      handlePrintLabel('DPD', { returnLabel: true });
                    }}
                  >
                    DPD Return
                  </Button>,

                  <Popconfirm
                    key="return-book"
                    title={
                      <>
                        Are you sure you want to book-return?
                        <br />
                        <br />
                        Take care. ALL items of this order are booked. In case customer returned less, please correct
                        manually.
                      </>
                    }
                    okText="Yes"
                    cancelText="No"
                    overlayStyle={{ maxWidth: 300 }}
                    onConfirm={() => {
                      if (isBookReturned(order)) {
                        message.error('Already book-returned!');
                        return;
                      }

                      setLoadingP(true);
                      const hide = message.loading('Book-returning ', 0);
                      addStockStableByReturn({
                        order_id: order?.entity_id,
                        item_ids: order?.mag_order_items?.map((x) => x.item_id),
                      })
                        .then((res) => {
                          loadOrderDetail();
                          message.success('Successfully booked!');
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                          setLoadingP(false);
                        });
                    }}
                  >
                    <Button
                      type="default"
                      key="return-book"
                      size="small"
                      danger
                      title="Book return?"
                      style={{ marginLeft: 32 }}
                      disabled={isBookReturned(order)}
                    >
                      Book Return
                    </Button>
                  </Popconfirm>,
                  <Button
                    key="create-shipment"
                    type="primary"
                    size="small"
                    className="btn-green"
                    htmlType="button"
                    onClick={() => {
                      setOpenUpdateOrderExtraModal(true);
                    }}
                  >
                    Create Shipment
                  </Button>,
                ]}
                onRow={(record) => {
                  let cls = '',
                    title = '';
                  /* const warning_def = (record as API.Order).warnings_def || '';
                  if (warning_def) {
                    cls += ' reset-tds-bg bg-red';
                    title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
                  } */
                  cls += ' ' + (record.item_ean?.is_single ? 'row-single' : 'row-multi');

                  return { title: title, className: cls };
                }}
                rowSelection={false}
                columnEmptyText=""
              />
            </Spin>
          </Col>
        </Row>
      </Card>
      <Modal
        title={`Order shipment comments`}
        width={600}
        open={openCommentsModal}
        onCancel={() => setOpenCommentsModal(false)}
        footer={false}
      >
        <OrderShipmentCommentsList orderId={sn(order?.entity_id)} />
      </Modal>

      <UpdateOrderExtraFormModal
        modalVisible={openUpdateOrderExtraModal}
        handleModalVisible={setOpenUpdateOrderExtraModal}
        order={{
          shipping_description: order?.shipping_description,
          weight: order?.weight,
          entity_id: order?.entity_id,
          shipping_imported_list: order?.shipping_imported_list,
          latest_shipping: order?.latest_shipping,
          sa_fullname: order?.sa_fullname,
          sa_full: order?.sa_full,
          sa_company: order?.sa_company,
        }}
        values={{ ...order?.extra }}
        onSubmit={async (values) => {
          actionRef.current?.reload();
        }}
      />

      <StockStableQtyByLocationModal
        modalVisible={openStockStableQtyByLocationModal}
        handleModalVisible={setOpenStockStableQtyByLocationModal}
        wl={currentSSRow?.warehouse_location}
        initialValues={currentSSRow}
        orderItem={currentSSRow?.order_item}
      />

      <UpdateAddressesModal
        modalVisible={openUpdateAddressModal}
        handleModalVisible={setOpenUpdateAddressModal}
        initialValues={order}
        onSubmit={async (value) => actionRef.current?.reload()}
      />

      {!!order?.entity_id && (
        <MagOrderUserActionLogModal
          searchParams={{
            order_id: order?.entity_id,
            increment_id: order?.increment_id,
          }}
          modalVisible={openOrderUserActionLogModal}
          handleModalVisible={setOpenOrderUserActionLogModal}
        />
      )}
    </PageContainer>
  );
};

export default OrderDetail;
