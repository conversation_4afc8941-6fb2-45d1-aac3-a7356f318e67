import {
  getOffer2SupplierDataMatrix,
  Offer2SupplierDataMatrixResultType,
  updateOffer2SupplierDataMatrix,
} from '@/services/foodstore-one/Offer/offer-to-supplier-data-matrix';
import Util, { isNumeric, sn, sShortImportDbTableName } from '@/util';
import { ReloadOutlined } from '@ant-design/icons';
import ProForm, { ProFormInstance, ProFormSwitch, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { Card, message } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { OfferIboStatusComp } from './OfferList';
import { trim } from 'lodash';

type SearchFormValueType = {};
type RecordType = API.Offer & Record<string, any>;

type Offer2SupplierDataMatrixProps = any;

/**
 * @deprecated
 * @param param0
 * @returns
 */
const Offer2SupplierDataMatrix: React.FC<Offer2SupplierDataMatrixProps> = ({}) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const editFormRef = useRef<ProFormInstance<RecordType>>();

  const [dataSource, setDataSource] = useState<RecordType[]>(() => []);
  const [editableKeys, setEditableRowKeys] = useState<(React.Key | number)[]>(() => []);

  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<Offer2SupplierDataMatrixResultType>();

  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  const loadMatrix = useCallback(() => {
    const params = {
      ...searchFormRef.current?.getFieldsValue(),
    };
    Util.setSfValues('sf_offer_to_supplier_data_matrix', params);

    setLoading(true);
    getOffer2SupplierDataMatrix(params)
      .then((res) => {
        setData(res);
        setDataSource(res.offers);
        console.log('dataSource', res.offers);
        setEditableRowKeys(res.offers?.map((x) => x.id as number));
      })
      .catch(Util.error)
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const importCols = useMemo<ProColumns<RecordType>[]>(() => {
    const cols: ProColumns<RecordType>[] = [];
    if (data?.importIds && data?.importAssoc) {
      data?.importIds.forEach((importId, index) => {
        const importXls: API.Import = data?.importAssoc[importId];
        cols.push({
          title: (
            <div className="rotate-rl-tmp" style={{ paddingRight: 20 }}>{`#${importXls.supplier?.name} | ${
              importXls?.supplier_add ? importXls?.supplier_add : sShortImportDbTableName(importXls?.table_name, true)
            }`}</div>
          ),
          dataIndex: [`${importId}`, 'value'],
          width: 150,
          align: 'left',
          fieldProps(form, config) {
            /* console.log(config.rowIndex, config.rowKey);
            console.log(form.getFieldValue([...(config.rowKey as any), 'value'])); */
            const rowData = config.entity;

            const value = form.getFieldValue([...(config.rowKey as any), 'value']);
            const valueNo = sn(value);
            let cls = '';
            if (isNumeric(value)) {
              if (valueNo == 0) {
                cls += ' fake-none bg-gray';
              } else if (valueNo >= 1 && valueNo <= 99) {
                cls += ' bg-yellow';
              } else if (valueNo >= 100) {
                cls += ' bg-green2';
              } else {
                cls += ' bg-red';
              }
            } else {
              if (value) {
                const valueArr = value.split(',');

                let isAllEg100 = true;
                let isAllNumeric = true;
                for (const vx of valueArr) {
                  const v = trim(vx);

                  if (!isNumeric(v)) {
                    isAllNumeric = false;
                    isAllEg100 = false;
                    break;
                  } else {
                    if (sn(v) >= 100) {
                    } else {
                      isAllEg100 = false;
                    }
                  }
                }

                if (isAllEg100) {
                  cls += ' bg-green2';
                } else {
                  cls += ' bg-red';
                }
              } else {
                // empty string, so we check if No Value entered, but this Offer No has got this Supplier in any SKU as Price Stable.
                if (rowData[`exist_price_${importId}`]) {
                  cls += ' bg-light-orange';
                }
              }
            }

            return {
              bordered: false,
              style: { paddingLeft: 0, paddingRight: 0, height: 24 },
              placeholder: '',
              className: cls,
            };
          },
          onCell: (entity, index) => {
            return {
              onClick: () => {
                /* const hide = message.loading('Updating...', 0);
                assignIboPre2OfferItem({ offerItemId: sn(entity.id), iboPreId: iboPreId })
                  .then((res) => {
                    message.success('Assigned successfully.');
                    loadMatrix();
                  })
                  .catch(Util.error)
                  .finally(() => hide()); */
              },
              onChange: (e) => {},
              onBlur: () => {
                const formValues = editFormRef.current?.getFieldsValue();
                console.log('record', entity, index);
                console.log('formValues', formValues);

                if (formValues && entity.id && entity[`${importId}`].value != formValues[entity.id][importId].value) {
                  const newValue = formValues[entity.id][importId].value;

                  updateOffer2SupplierDataMatrix({ importIds: [importId], ds: formValues })
                    .then((res) => {
                      message.success('Saved successfully.', 1);
                      setDataSource((prev) => {
                        const newRows: any = [...prev];
                        if (newRows[`${index}`]) {
                          newRows[`${index}`][`${importId}`].value = newValue;
                        }
                        return newRows;
                      });
                    })
                    .catch(Util.error);
                }
              },
            };
          },
        });
      });
    }
    return cols;
  }, [data?.importIds, data?.importAssoc]);

  const orgColumns: ProColumns<RecordType>[] = useMemo(() => {
    return [
      {
        title: 'Offer',
        dataIndex: ['offer_no'],
        width: 60,
        hideInSearch: true,
        editable: false,
        align: 'center',
      },
      {
        title: 'Offer Note',
        dataIndex: ['note'],
        width: 220,
        hideInSearch: true,
        editable: false,
        ellipsis: true,
        render(__, entity) {
          return `${entity.note}${entity?.quote?.customer_fullname ? ` | ${entity?.quote?.customer_fullname}` : ''}`;
        },
      },
      {
        title: 'Ibo Status',
        dataIndex: 'ibo_status',
        hideInForm: true,
        align: 'center',
        width: 90,
        editable: false,
        render(__, record) {
          return <OfferIboStatusComp status={record.ibo_status} />;
        },
      },
      ...importCols,
    ];
  }, [importCols]);

  useEffect(() => {
    loadMatrix();
  }, [loadMatrix]);

  useEffect(() => {
    setColumns([...orgColumns]);
  }, [orgColumns]);

  return (
    <>
      <PageContainer title="Offer x Supplier Data Matrix">
        <Card style={{ marginBottom: 16 }}>
          <ProForm<SearchFormValueType>
            layout="inline"
            formRef={searchFormRef}
            isKeyPressSubmit
            className="search-form"
            initialValues={Util.getSfValues('sf_offer_to_supplier_data_matrix', {}, {})}
            submitter={{
              searchConfig: { submitText: 'Search' },
              submitButtonProps: { htmlType: 'submit' },
              onSubmit: (values) => loadMatrix(),
              onReset: (values) => loadMatrix(),
            }}
          >
            <ProFormSwitch
              name="inc_closed"
              label="Show Closed Offer?"
              fieldProps={{
                onChange() {
                  loadMatrix();
                },
              }}
            />
            <ProFormText name="offer_no" label="Offer No" />
          </ProForm>
        </Card>

        <ProTable<RecordType, API.PageParams>
          headerTitle={
            <>
              Offer x Supplier Data&nbsp;&nbsp;&nbsp;
              <ReloadOutlined
                className="text-sm"
                onClick={() => {
                  loadMatrix();
                }}
              />
            </>
          }
          actionRef={actionRef}
          rowKey="id"
          revalidateOnFocus={false}
          options={false}
          size="small"
          loading={loading}
          bordered
          columnEmptyText=""
          scroll={{ x: 1200 }}
          dataSource={dataSource}
          onDataSourceChange={setDataSource}
          editable={{
            formProps: {
              formRef: editFormRef,
            },
            type: 'multiple',
            editableKeys,

            /* actionRender: (row, config) => {
              return [
                <Button
                  key="save"
                  type="primary"
                  size="small"
                  onClick={() => {
                    handleSaveRow(row);
                  }}
                >
                  Create
                </Button>,
              ];
            }, */
            onChange: setEditableRowKeys,
            // deletePopconfirmMessage: 'Are you sure you want to delete?',
            // onlyAddOneLineAlertMessage: 'You can only add one.',
          }}
          /* toolBarRender={() => [
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => {
                const dataByRef = editFormRef.current?.getFieldsValue();
                const hide = message.loading('Updating table...', 0);
                updateOffer2SupplierDataMatrix({ importIds: data?.importIds || [], ds: dataByRef })
                  .then((res) => {
                    message.success('Saved successfully.');
                    loadMatrix();
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Save
            </Button>,
          ]} */
          pagination={{
            showSizeChanger: true,
            hideOnSinglePage: true,
            defaultPageSize: 20,
          }}
          search={false}
          columns={columns}
          tableAlertRender={false}
        />
      </PageContainer>
    </>
  );
};

export default Offer2SupplierDataMatrix;
