import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Space } from 'antd';
import { SOrderId } from '../Order';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { useRef, useState } from 'react';
import { useModel } from 'umi';
import Util, { nf3, sn } from '@/util';
import { getOrderParcelList } from '@/services/foodstore-one/Magento/order-parcel';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';

export type SearchFormValueType = Partial<API.OrderParcel>;

const OrderParcelList: React.FC = () => {
  const { appSettings } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  //   const [currentRow, setCurrentRow] = useState<API.OrderParcel>();

  const columns: ProColumns<API.OrderParcel>[] = [
    {
      title: 'Provider',
      dataIndex: ['service_name'],
      align: 'center',
      width: 100,
    },
    {
      title: 'Date',
      dataIndex: ['created_on'],
      defaultSortOrder: 'descend',
      align: 'center',
      width: 110,
      render: (__, record) => Util.dtToDMYHHMMTz(record.created_on),
    },
    {
      title: 'Parcel No',
      dataIndex: ['parcel_no'],
      align: 'center',
      width: 130,
      copyable: true,
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      align: 'center',
      width: 150,
      render: (__, record) => record.status,
    },
    {
      title: 'Track ID',
      dataIndex: ['track_id'],
      align: 'center',
      width: 150,
      render: (__, record) => record.track_id,
    },
    {
      title: 'Ref. No',
      dataIndex: ['ref_no'],
      width: 150,
      render: (__, record) => record.ref_no,
    },
    {
      title: 'Push ID',
      dataIndex: ['push_id'],
      align: 'center',
      width: 110,
      copyable: true,
      render: (__, record) => record.push_id,
    },
    {
      title: 'Weight',
      dataIndex: ['weight'],
      align: 'right',
      width: 70,
      render: (__, record) => nf3(record.weight),
    },
    {
      title: 'Order ID',
      dataIndex: 'order_id',
      sorter: true,
      align: 'center',
      width: 110,
      render(__, entity) {
        return <SOrderId order={entity?.order} />;
      },
    },
    {
      title: 'Updated on',
      defaultSortOrder: 'descend',
      align: 'center',
      width: 110,
      render: (__, record) => Util.dtToDMYHHMMTz(record.shipping_updated_on),
    },
    {
      title: 'Detail',
      dataIndex: ['detail'],
      render: (__, record) => (record.detail ? JSON.stringify(record.detail) : null),
    },
    {
      title: '',
      valueType: 'option',
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 60,
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name={'service_name'}
            label="Shipping Mode"
            allowClear
            options={['DHL', 'DPD', 'GLS']}
            placeholder={'Shipping Mode'}
            width={'xs'}
          />
          <ProFormSelect
            name={'status'}
            label="Status"
            allowClear
            valueEnum={appSettings?.parcelShippingStatus || {}}
            placeholder={'Status'}
            width={'sm'}
          />
          {/* <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={140} placeholder={'EAN'} /> */}
          <ProFormText name={'order_id'} label="Order ID" width={'xs'} placeholder={'Order ID'} />
          <ProFormText
            name={'increment_id_suffix'}
            label="Increment ID"
            width={150}
            placeholder={'Increment ID'}
            tooltip="Can search by suffix number. e.g. 137 -> 000000137 or EBDE000000137"
          />
          <ProFormText name={'name_address'} label="Name & Address" width={'sm'} placeholder={'Name / Address'} />
          {/* <ProFormText name={'ebay_order_id'} label="Ebay/KL Order" width={150} placeholder={'Ebay/KL Order'} /> */}
        </ProForm>
      </Card>

      <ProTable<API.OrderParcel, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Parcels List</span>{' '}
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues('sf_order_parcels_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_order_parcels', searchFormValues);
          Util.setSfValues('sf_order_parcels_p', params);

          setLoading(true);
          return getOrderParcelList(
            {
              ...params,
              with: 'order,parcelLogs',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              // calc weight info
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        onRow={() => {
          let cls = '',
            title = '';
          return { title: title, className: cls };
        }}
        expandable={{
          expandRowByClick: true,
          indentSize: 200,
          childrenColumnName: 'parcel_logs',
          rowExpandable(record) {
            return sn(record?.parcel_logs?.length) > 0;
          },
        }}
        columnEmptyText=""
      />
    </PageContainer>
  );
};

export default OrderParcelList;
