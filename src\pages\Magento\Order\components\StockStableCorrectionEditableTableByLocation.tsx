import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, message, Popconfirm, Row, Space, Spin } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import _ from 'lodash';
import Util, { nf2, ni, sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { StockMovementReason, StockStableStatusOptions } from '@/constants';
import SProFormDigit from '@/components/SProFormDigit';
import type { StockStableCorrectionParamType } from '@/services/foodstore-one/Stock/stock-stable';
import { addStockStable, stockStableBulkDeduct } from '@/services/foodstore-one/Stock/stock-stable';
import { stockStableCorrection } from '@/services/foodstore-one/Stock/stock-stable';
import type { DefaultOptionType } from 'antd/lib/select';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import type { Rule } from 'antd/lib/form';
import SDatePicker from '@/components/SDatePicker';
import { InfoCircleOutlined, SaveFilled, SaveOutlined } from '@ant-design/icons';
import SelectIboModal from '@/pages/Stock/StockStable/components/SelectIboModal';
import StockStableMoveSettingForm from '@/pages/Item/EanList/components/StockStableMoveSettingForm';
import StockStableUpdateExpForm from '@/pages/Item/EanList/components/StockStableUpdateExpForm';
import { createOrUpdateStockStableProblem } from '@/services/foodstore-one/Stock/stock-stable-problem';
import { StockStableProblemStatus } from '@/pages/Stock/StockStableProblem';

export type FormValueType = Partial<API.Ean>;

export type StockStableRowType = API.StockStable & {
  key?: string;
  box_qty_edit?: number;
  piece_qty_edit?: number;
  is_single?: boolean;
};

export type StockStableCorrectionEditableTableByLocationProps = {
  ean?: Partial<API.Ean>;
  initialData?: Partial<StockStableRowType>[];
  itemMode?: boolean;
  wl_id?: number;
  ssProblem?: API.StockStableProblem;
  orderItem?: Partial<API.OrderItem>;
  reload?: () => void;
};

const StockStableCorrectionEditableTableByLocation: React.FC<StockStableCorrectionEditableTableByLocationProps> = (
  props,
) => {
  const { ean: itemEan, initialData, itemMode, wl_id, ssProblem, orderItem } = props;

  const [loading, setLoading] = useState<boolean>(false);

  const editableFormRef = useRef<ProFormInstance>();
  const [data, setData] = useState<Partial<StockStableRowType>[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  // stock move form.
  const [moveModalVisible, handleMoveModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<Partial<StockStableRowType>>();
  const [warehouseLocations, setWarehouseLocations] = useState<DefaultOptionType[]>([]);

  const [expDateModalVisible, handleExpDateModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  // open select IBO modal
  const [openSelectIboModalVisible, setOpenSelectIboModalVisible] = useState<boolean>(false);

  useEffect(() => {
    const newData = [...(initialData || [])];
    for (const x of newData) {
      x.box_qty_edit = null as any;
      x.piece_qty_edit = null as any;
    }
    setData(newData);
    setEditableRowKeys(newData.map((x) => x.key as React.Key));
  }, [initialData]);

  const isEditable = itemMode !== true && !!ssProblem?.qty_missing;

  useEffect(() => {
    if (isEditable) {
      setEditableRowKeys(data.map((x) => x.key as React.Key));
    } else {
      setEditableRowKeys([]);
    }
  }, [isEditable, data]);

  useEffect(() => {
    getWarehouseLocationACList({})
      .then((res) => {
        setWarehouseLocations(res);
      })
      .catch(Util.error);
  }, []);

  const locations = useMemo(() => {
    const ret: any = {};
    if (warehouseLocations.length) {
      for (const w of warehouseLocations) {
        ret[w.id] = { text: w.label, value: w.id };
      }
    }
    return ret;
  }, [warehouseLocations]);

  const handleTakenSave = () => {
    const rows = editableFormRef.current?.getFieldsValue();

    const hide = message.loading('Batch stock deducting...', 0);

    const ssIds = Object.keys(rows);
    const ssList = ssIds
      .map((ssId) => ({
        id: ssId,
        ...rows[ssId],
      }))
      .filter((x) => sn(x.box_qty_edit) > 0 || sn(x.piece_qty_edit) > 0);
    if (ssList.length < 1) {
      message.info('Please fill qty to be deducted!');
      return;
    }

    stockStableBulkDeduct(ssList, orderItem)
      .then((res) => {
        message.success('Stocks have been deducted successfully.');
        setData((prev) => {
          const newPrev = [...prev];

          ssIds.forEach((ssId) => {
            const existedOne = newPrev.find((x) => `${x.id}` === `${ssId}`);
            if (existedOne) {
              existedOne.box_qty_edit = null as any;
              existedOne.piece_qty_edit = null as any;
            }
          });

          return newPrev;
        });

        props.reload?.();
      })
      .catch(Util.error)
      .finally(() => {
        hide();
      });
  };

  const hiddenColumns: ProColumns<Partial<StockStableRowType>>[] = useMemo(() => [], []);

  const columnsAll: ProColumns<Partial<StockStableRowType>>[] = useMemo(
    () => [
      ...hiddenColumns,
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'id'],
        dataType: 'select',
        valueEnum: locations,
        editable: (value: any, record: any, index: number) => {
          return `${record.key}`.startsWith?.('newKey');
        },
        formItemProps: (form, { rowIndex }) => {
          const rules: Rule[] = [{ required: true }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        width: 80,
        align: 'left',
        render: (dom, record) => record.warehouse_location?.name,
        renderFormItem(schema, { record }, form, action) {
          return (
            <ProFormSelect
              options={warehouseLocations || []}
              style={{ marginBottom: 0 }}
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                showSearch: true,
                onInputKeyDown: (e: any) => {
                  if (Util.isTabPressed(e)) {
                    const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
                    if (dropdownWrapper?.children.length == 1) {
                      const id = e.target.getAttribute('aria-activedescendant');
                      const value = sn(document.getElementById(id)?.innerText);

                      setData((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.key == record?.key);
                        if (current) {
                          (current as any).warehouse_location = { id: value };
                        }
                        return newList;
                      });
                    }
                  }
                },
              }}
            />
          );
        },
      },
      {
        title: 'Priority',
        dataIndex: ['warehouse_location', 'priority'],
        width: 80,
        align: 'right',
        editable: false,
        className: 'text-sm c-grey',
        render(__, entity) {
          return ni(entity.warehouse_location?.priority);
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 90,
        align: 'left',
        valueType: 'date',
        /* editable: (value: any, record: any, index: number) => {
          return `${record.key}`.startsWith?.('newKey');
        }, */
        editable: false,
        formItemProps: (form, { rowIndex }) => {
          const rules: Rule[] = [{ required: true }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender, ...rest }, form) => {
          return <SDatePicker placeholder="EXP. Date" formItemProps={{ style: { marginBottom: 0, marginRight: 0 } }} />;
        },
        render: (dom, record) => {
          return Util.dtToDMY(record?.exp_date);
          /* return (
            <>
              <span style={{ verticalAlign: 'middle' }}>{Util.dtToDMY(record?.exp_date)}</span>
              <Button
                type="link"
                title="Change Exp. Date..."
                size="small"
                icon={<EditOutlined />}
                style={{ float: 'right' }}
                onClick={() => {
                  setCurrentRow({ ...record });
                  handleExpDateModalVisible(true);
                }}
              />
            </>
          ); */
        },
      },

      {
        title: 'Case Qty',
        dataIndex: ['case_qty'],
        width: 70,
        align: 'right',
        className: 'c-grey',
        editable: false,
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        render: (__, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (__, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
      {
        title: 'Status',
        dataIndex: 'status',
        width: 80,
        editable: false,
        align: 'center',
        tooltip: 'Click to change.',
        render: (dom, record) => {
          const defaultValue = record.status;
          return StockStableStatusOptions.find((x) => x.value == defaultValue)?.label;
        },
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 60,
        editable: false,
        align: 'center',
      },

      {
        title: (
          <>
            Taken for this Order{' '}
            <Popconfirm
              title={
                <>
                  <h3>Are you sure you want to book stocks you've taken?</h3>
                  <p>Stocks will be deducted.</p>
                </>
              }
              okText="Yes"
              cancelText="No"
              overlayStyle={{ maxWidth: 350 }}
              onConfirm={async () => {
                handleTakenSave();
              }}
            >
              <Button type="link" icon={<SaveFilled />} className="btn-green" style={{ width: 24, height: 24 }} />
            </Popconfirm>
          </>
        ),
        dataIndex: 'taken',
        width: 60,
        editable: false,
        align: 'center',
        children: [
          {
            title: 'Pieces',
            dataIndex: 'piece_qty_edit',
            width: 100,
            className: 'p0',
            editable: (value, row, index) => !!row?.item_ean?.is_single,
            formItemProps: () => {
              return {};
            },
            renderFormItem: (item, { record }) => {
              return (
                <SProFormDigit readonly={!isEditable} min={0} formItemProps={{ style: { margin: 0 } }} placeholder="" />
              );
            },
            render: (dom, record) => null,
          },
          {
            title: 'Boxes',
            dataIndex: 'box_qty_edit',
            width: 100,
            className: 'p0',
            editable: (value, row, index) => !row.item_ean?.is_single,
            formItemProps: () => {
              return {};
            },
            renderFormItem: (item, { record }) => {
              return (
                <SProFormDigit readonly={!isEditable} min={0} formItemProps={{ style: { margin: 0 } }} placeholder="" />
              );
            },
            render: (dom, record) => null,
          },
        ],
      },

      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'left',
        fixed: 'right',
        width: 220,
        render: () => {
          return null;
        },
      },
    ],
    [hiddenColumns, locations, warehouseLocations, isEditable],
  );

  const getQtyMissing = (row?: StockStableRowType) => {
    const editedValues = editableFormRef.current?.getFieldsValue();

    const qty_missing =
      sn(row?.case_qty) > 1 ? editedValues[`${row?.id}`]?.box_qty_edit : editedValues[`${row?.id}`]?.piece_qty_edit;

    return sn(qty_missing);
  };

  const handleMissingItems = (row?: StockStableRowType) => {
    const qty_missing = getQtyMissing(row);
    if (!qty_missing) {
      message.error('Please fill a missing Qty!');
      return;
    }

    const hide2 = message.loading('Updating...', 0);
    createOrUpdateStockStableProblem({
      stock_stable_id: row?.id,
      sku: itemEan?.sku,
      qty_ordered: ssProblem?.qty_ordered,
      order_id: ssProblem?.order_id,
      order_item_id: ssProblem?.order_item_id,
      status: StockStableProblemStatus.New,
      qty_missing,
      qty_picked: sn(ssProblem?.case_qty) > 1 ? ssProblem?.box_qty : ssProblem?.piece_qty,
    })
      .then((res) => {
        message.success('Created successfully.');

        setData((prev) => {
          const newPrev = [...prev];
          const existedOne = newPrev.find((x) => x.id == row?.id);
          if (existedOne) {
            existedOne.box_qty_edit = null as any;
            existedOne.piece_qty_edit = null as any;
          }

          return newPrev;
        });
      })
      .catch(Util.error)
      .finally(() => {
        hide2();
      });
  };

  const handleOpenBox = (row: StockStableCorrectionParamType & { box_qty?: number }) => {
    if (!isEditable) {
      message.error('Please switch "Include All" off!');
      return;
    }

    if (sn(row?.box_qty) < 0) {
      message.error('No box is available to open!');
      return;
    }

    const hide = message.loading('Opening a box...', 0);
    setLoading(true);
    stockStableCorrection({
      ...row,
      ean_id: props.ean?.id,
    })
      .then((res) => {
        hide();
        if (res.message === false) {
          message.info('Nothing to update!');
        } else {
          message.success('Successfully opened!');
          props.reload?.();
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  return (
    <>
      <Spin spinning={loading}>
        <EditableProTable
          rowKey={'key'}
          headerTitle={
            <>
              Qty by Location & Exp.Date&nbsp;
              <InfoCircleOutlined title="Light yellow background: Min Exp.date of Single. Light red background: Min Exp.date of Multi." />
            </>
          }
          cardProps={{ bodyStyle: { padding: 0 } }}
          bordered={false}
          columns={columnsAll}
          dataSource={data}
          search={false}
          actionRef={actionRef}
          editableFormRef={editableFormRef}
          value={data}
          onChange={setData}
          toolbar={{
            search: false,
            actions: undefined,
            menu: undefined,
            // settings: undefined,
          }}
          scroll={{ x: '100%' }}
          // todo
          pagination={{ defaultPageSize: 20 }}
          className="w-full"
          rowClassName={(record) => {
            let cls = record.is_single ? 'row-single' : 'row-multi';
            if (record.is_single) {
              const minExp = data.reduce((prev, x) => {
                return x.is_single && x.exp_date && prev > x.exp_date ? x.exp_date : prev;
              }, '9999-99-99');
              if (minExp == record.exp_date) cls += ' bg-light-orange2';
            } else {
              const minExp = data.reduce((prev, x) => {
                return !x.is_single && x.exp_date && prev > x.exp_date ? x.exp_date : prev;
              }, '9999-99-99');
              if (minExp == record.exp_date) cls += ' bg-light-red2';
            }
            return cls;
          }}
          size="small"
          controlled
          /* recordCreatorProps={{
            newRecordType: 'dataSource',
            position: 'top',
            creatorButtonText: 'Add new stock',
            record: (index: number, dataSource2: StockStableRowType[]) => {
              return { key: 'newKey' + Date.now().toString() + '-' + index };
            },
          }} */
          recordCreatorProps={false}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              // const isMovable = sn(props.ean?.is_single ? row.single_piece_qty : row.box_qty) > 0;

              return [
                <Space key={'actions'}>
                  <Popconfirm
                    title={
                      <>
                        <h3>
                          Are you sure you want to create a missing stock with
                          {sn(row.case_qty) > 1 ? ` ${getQtyMissing(row)} boxes` : ` ${getQtyMissing(row)} pcs`} on{' '}
                          {`${row.warehouse_location?.name}?`}
                        </h3>
                      </>
                    }
                    okText="Yes"
                    cancelText="No"
                    overlayStyle={{ maxWidth: 350 }}
                    onConfirm={async () => {
                      handleMissingItems(row);
                    }}
                  >
                    <Button
                      type="primary"
                      size="small"
                      className=""
                      danger
                      ghost
                      title="Correction"
                      disabled={!isEditable}
                    >
                      I do not find these items
                    </Button>
                  </Popconfirm>
                </Space>,
              ];
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
            deleteText: (
              <Button type="default" size="small" danger>
                Delete
              </Button>
            ),
          }}
        />
      </Spin>
      <StockStableMoveSettingForm
        modalVisible={moveModalVisible}
        handleModalVisible={handleMoveModalVisible}
        ean={itemEan}
        initialData={currentRow || {}}
        onSubmit={async () => {
          props.reload?.();
        }}
        onCancel={() => {
          handleMoveModalVisible(false);
        }}
      />
      <StockStableUpdateExpForm
        modalVisible={expDateModalVisible}
        handleModalVisible={handleExpDateModalVisible}
        ean={itemEan}
        initialData={currentRow || {}}
        onSubmit={async () => {
          props.reload?.();
        }}
        onCancel={() => {
          handleExpDateModalVisible(false);
        }}
      />
      <SelectIboModal
        modalVisible={openSelectIboModalVisible}
        handleModalVisible={setOpenSelectIboModalVisible}
        initialValue={{
          id: sn(currentRow?.id),
          ibo_id: currentRow?.ibo_id,
          itemEan: currentRow?.item_ean,
        }}
        selectCallback={(cbData: API.StockStable) => {
          setOpenSelectIboModalVisible(false);
          props.reload?.();
        }}
      />
    </>
  );
};

export default StockStableCorrectionEditableTableByLocation;
