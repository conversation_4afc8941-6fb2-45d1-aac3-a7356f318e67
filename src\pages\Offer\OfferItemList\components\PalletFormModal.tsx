import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, message, Modal, Row, Spin } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ModalForm,
  ProFormDependency,
  ProFormList,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-form';
import Util, { ni, sn } from '@/util';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import { LS_TOKEN_NAME } from '@/constants';
import { UploadChangeParam } from 'antd/lib/upload';
import { deleteOfferPicture, getOfferById, updateOfferRecvData } from '@/services/foodstore-one/Offer/offer';
import styles from './PalletFormModal.less';
import { ActionType, ProColumns } from '@ant-design/pro-table';
import { DeleteOutlined } from '@ant-design/icons';

export type FormValueType = Partial<API.Offer>;

export type PalletFormModalProps = {
  offerId: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Offer) => void;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const PalletFormModal: React.FC<PalletFormModalProps> = ({
  offerId,
  modalVisible,
  handleModalVisible,
  onSubmit,
  onCancel,
}) => {
  const formRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const [offer, setOffer] = useState<API.Offer>();

  const [listReloadTick, setListReloadTick] = useState<number>(0);

  // Editable table
  const priceActionRef = useRef<FormListActionType>();

  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const columns: ProColumns<API.OfferRecvWeight>[] = [
    {
      dataIndex: ['pallet_weight'],
      listKey: 'pallet_weight',
      fieldProps: {
        placeholder: 'Pallet Weight',
      },
    },
  ];

  useEffect(() => {
    setListReloadTick((prev) => prev + 1);
  }, [modalVisible]);

  useEffect(() => {
    if (modalVisible && offerId) {
      setLoading(true);
      getOfferById(offerId, { with: 'offerRecvWeights,offerRecvWeightSummary,offerRecvFiles' })
        .then((res) => {
          setOffer(res);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [offerId, modalVisible]);

  useEffect(() => {
    if (offer) {
      formRef.current?.setFieldValue('files', offer.offer_recv_files || []);
      const weights =
        offer.offer_recv_weights?.map((x) => {
          return {
            ...x,
            uid: x.id,
            pallet_weight: sn(x.pallet_weight),
          };
        }) || [];

      formRef.current?.setFieldValue('offer_recv_weights', weights);
      formRef.current?.setFieldValue('recv_note', offer.recv_note || '');
    }
  }, [offer]);

  return (
    <ModalForm<FormValueType>
      title={
        <Row gutter={16}>
          <Col>{`Update Pallet Info - Offer #${offer?.offer_no || ''}`}</Col>
          <Col></Col>
        </Row>
      }
      width="900px"
      size="large"
      visible={modalVisible}
      onVisibleChange={(visible) => {
        handleModalVisible(visible);
      }}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 4 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data: API.Offer = {
          recv_note: value.recv_note,
          offer_recv_weights: value.offer_recv_weights,
        };

        const success = await updateOfferRecvData(offerId, data).catch(Util.error);

        if (success) {
          message.success('Updated successfully.');
          if (onSubmit) onSubmit(value);
        }
      }}
      modalProps={{ maskClosable: false }}
    >
      <Spin spinning={loading}>
        <Row className={styles.palletFormModal}>
          <Col span={12}>
            <ProFormDependency name={['offer_recv_weights']}>
              {(depValues) => {
                const sum = depValues.offer_recv_weights?.filter((x: API.OfferRecvWeight) =>
                  x.pallet_weight ? true : false,
                )?.length;
                return (
                  <div>
                    Total Pallets <span style={{ fontWeight: 'bold', fontSize: 16 }}>{ni(sum, true)}</span>
                  </div>
                );
              }}
            </ProFormDependency>

            <ProFormDependency name={['offer_recv_weights']}>
              {(depValues) => {
                const sum = depValues.offer_recv_weights?.reduce(
                  (prev: number, x: API.OfferRecvWeight) => sn(prev) + sn(x.pallet_weight),
                  0,
                );
                return (
                  <div>
                    Total Weight <span style={{ fontWeight: 'bold', fontSize: 16 }}>{ni(sum, true)}kg</span>
                  </div>
                );
              }}
            </ProFormDependency>

            <h3 style={{ marginTop: 36, marginBottom: 8 }}>Weights per Pallet</h3>
            <ProFormList
              actionRef={priceActionRef}
              key={'uid'}
              name="offer_recv_weights"
              creatorButtonProps={{
                position: 'bottom',
                creatorButtonText: 'Add Weight',
                style: { maxWidth: 180, marginTop: 24 },
              }}
              creatorRecord={(index: number) => {
                return {
                  uid: Util.genNewKey(),
                  pallet_weight: '',
                };
              }}
              deleteIconProps={{ tooltipText: 'Remove' }}
              copyIconProps={false}
              alwaysShowItemLabel={false}
              actionRender={(field, action, doms) => [
                <Button
                  key={'delete'}
                  type="link"
                  size="large"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    action.remove((field as any).key);
                  }}
                />,
              ]}
            >
              <Row gutter={6} align={'middle'}>
                <Col>
                  <ProForm.Item name="pallet_weight" style={{ marginTop: 4, marginBottom: 4 }}>
                    <NumpadExtSelector inputProps={{ style: { width: 140 }, inputMode: 'none' }} isMobile />
                  </ProForm.Item>
                </Col>
              </Row>
            </ProFormList>
          </Col>
          <Col span={12}>
            <ProFormTextArea name="recv_note" label="Note" />
            <ProFormUploadButton
              name="files"
              label="Pictures"
              title="Select File"
              accept="image/*"
              action={`${API_URL}/api/offer/upload-picture/${offerId}`}
              fieldProps={{
                headers: {
                  Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
                },
                listType: 'picture',
                beforeUpload: async (file, fList) => {
                  return Promise.resolve(file);
                },
                onChange: (info: UploadChangeParam, updateState = true) => {
                  if (info.file.status == 'done') {
                    info.file.url = info.file.response.url;
                    info.file.uid = info.file.response.uid;
                    (info.file as any).id = info.file.response.uid;
                    (info.file as any).file_name = info.file.response.file_name;
                    (info.file as any).clean_file_name = info.file.response.clean_file_name;
                    (info.file as any).path = info.file.response.path;
                    (info.file as any).org_path = info.file.response.org_path;
                    (info.file as any).pivot = info.file.response.pivot;

                    const newFiles = [...info.fileList];
                    formRef.current?.setFieldsValue({ files: newFiles });
                  }
                },
                onRemove: async (file: API.File) => {
                  console.log('onRemove, file: ', file);
                  if (!file.id) return Promise.resolve(true);
                  const { confirm } = Modal;
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: 'Are you sure you want to delete?',
                      onOk: async () => {
                        resolve(true);
                        const hide = message.loading('Deleting...', 0);
                        const res = await deleteOfferPicture(file.id)
                          .then((re) => {
                            message.success('Deleted successfully.');
                            return re;
                          })
                          .catch(Util.error)
                          .finally(() => hide());
                        return res?.message;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                },
              }}
            />
          </Col>
        </Row>
      </Spin>
    </ModalForm>
  );
};

export default PalletFormModal;
