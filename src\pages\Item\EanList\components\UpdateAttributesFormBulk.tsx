import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { Button, message, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSwitch, ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import _ from 'lodash';
import { useModel } from 'umi';
import { MinusOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { updateEanBatch } from '@/services/foodstore-one/Item/ean';

export type UpdateAttributesFormBulkProps = {
  eanIds: number[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean) => void;
};

const handleBulkUpdate = async (data: any, key?: string) => {
  const hide = message.loading(`Bulk ${key} updating...`, 0);
  try {
    await updateEanBatch(data as any);
    hide();
    message.success(`Bulk ${key} Updated successfully.`);
  } catch (error) {
    hide();
    Util.error(error);
  }
};

const UpdateAttributesFormBulk: React.FC<UpdateAttributesFormBulkProps> = (props) => {
  const { appSettings } = useModel('app-settings');
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      title={`Batch update ${props.eanIds.length} EANs`}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      layout="horizontal"
      modalProps={{
        maskClosable: false,
      }}
      formRef={formRef}
      submitter={{
        render: (__, doms) => {
          return (
            <Space>
              <Button
                type="default"
                key="close-reload"
                onClick={() => {
                  props.handleModalVisible(false);
                  if (props.onSubmit) props.onSubmit({} as API.Ean);
                }}
              >
                Close & Reload
              </Button>
              <Button type="default" key="close" onClick={() => props.handleModalVisible(false)}>
                Close
              </Button>
            </Space>
          );
        },
      }}
      onFinish={async (value) => {
        return false;
      }}
    >
      <ProFormText name="mode" formItemProps={{ style: { display: 'none' } }} />

      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSwitch name={'status'} label="Status" colProps={{ span: 'auto' }} initialValue={true} />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                status: formRef.current?.getFieldValue('status'),
              },
            };
            handleBulkUpdate(data, 'Status');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSwitch
          name={'not_deliverable'}
          label="Not Deliverable"
          colProps={{ span: 'auto' }}
          initialValue={false}
        />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                not_deliverable: formRef.current?.getFieldValue('not_deliverable'),
              },
            };
            handleBulkUpdate(data, 'Not Deliverable');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSwitch
          name={'fs_export_kaufland'}
          label="Export to Kaufland?"
          colProps={{ span: 'auto' }}
          initialValue={true}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                fs_export_kaufland: formRef.current?.getFieldValue('fs_export_kaufland'),
              },
            };
            handleBulkUpdate(data, 'Status');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSwitch
          name={'fs_export_idealo'}
          label="Export to Idealo?"
          colProps={{ span: 'auto' }}
          initialValue={true}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                fs_export_idealo: formRef.current?.getFieldValue('fs_export_idealo'),
              },
            };
            handleBulkUpdate(data, 'Status');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSwitch
          name={'fs_export_billiger'}
          label="Export to Billiger?"
          colProps={{ span: 'auto' }}
          initialValue={true}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                fs_export_billiger: formRef.current?.getFieldValue('fs_export_billiger'),
              },
            };
            handleBulkUpdate(data, 'Status');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSwitch
          name={'fs_export_google'}
          label="Export to Google?"
          colProps={{ span: 'auto' }}
          initialValue={true}
          getValueFromEvent={(value) => (value ? 1 : 0)}
        />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                fs_export_google: formRef.current?.getFieldValue('fs_export_google'),
              },
            };
            handleBulkUpdate(data, 'Status');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSelect
          name="product_websites"
          label="Product Websites"
          colProps={{ xl: 12 }}
          mode="multiple"
          required
          options={appSettings.storeWebsites
            ?.filter((x) => x.code != 'admin')
            ?.map((x) => ({
              value: `${x.id}`,
              label: x.name,
            }))}
          rules={[
            {
              required: true,
              message: 'Product websites is required',
            },
          ]}
        />
        <Space className="ant-space-align-start">
          <Button
            type="primary"
            onClick={async () => {
              const productWebsites = formRef.current?.getFieldValue('product_websites');
              if (!productWebsites) {
                message.error('Please select website!');
                return;
              }
              const data = {
                ids: props.eanIds,
                mode: 'update',
                data: {
                  product_websites: formRef.current?.getFieldValue('product_websites'),
                },
              };
              handleBulkUpdate(data, 'product websites');
            }}
            icon={<SaveOutlined />}
          >
            Update
          </Button>
          <Button
            type="default"
            onClick={() => {
              const productWebsites = formRef.current?.getFieldValue('product_websites');
              if (!productWebsites) {
                message.error('Please select website!');
                return;
              }
              const data = {
                ids: props.eanIds,
                mode: 'add',
                data: {
                  product_websites: formRef.current?.getFieldValue('product_websites'),
                },
              };
              handleBulkUpdate(data, 'product websites');
            }}
            icon={<PlusOutlined />}
          >
            Add
          </Button>
          <Button
            type="default"
            danger
            onClick={() => {
              const productWebsites = formRef.current?.getFieldValue('product_websites');
              if (!productWebsites) {
                message.error('Please select website!');
                return;
              }
              const data = {
                ids: props.eanIds,
                mode: 'delete',
                data: {
                  product_websites: formRef.current?.getFieldValue('product_websites'),
                },
              };
              handleBulkUpdate(data, 'product websites');
            }}
            icon={<MinusOutlined />}
          >
            Delete
          </Button>
        </Space>
      </ProFormGroup>

      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormSelect
          name="attribute_set_code"
          label="Attribute Set"
          colProps={{ xl: 6 }}
          options={appSettings.productAttributeSet?.map((x) => ({
            value: x.attribute_set_id,
            label: x.attribute_set_name,
          }))}
        />
        <ProFormSelect
          name="product_type"
          label="Product Type"
          colProps={{ xl: 6 }}
          initialValue={'simple'}
          options={[
            {
              value: 'simple',
              label: 'Simple Product',
            },
          ]}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
        />
        <ProFormSelect
          name="visibility"
          label="Visibility"
          colProps={{ xl: 8 }}
          initialValue={4}
          options={appSettings.productAttributes?.visibility?.options}
          getValueProps={(value) => {
            return { value: value ? `${value}` : value };
          }}
        />
        <Button
          type="primary"
          onClick={async () => {
            const data = {
              ids: props.eanIds,
              mode: 'update',
              data: {
                attribute_set_code: formRef.current?.getFieldValue('attribute_set_code') ?? null,
                product_type: formRef.current?.getFieldValue('product_type') ?? null,
                visibility: formRef.current?.getFieldValue('visibility') ?? null,
              },
            };
            handleBulkUpdate(data, 'Product Type & Visability');
          }}
          icon={<SaveOutlined />}
        >
          Update
        </Button>
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateAttributesFormBulk;
