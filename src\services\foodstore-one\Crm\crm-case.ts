/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/crm/crmCase';

/**  get list
 * GET /api/crm/crmCase */
export async function getCrmCaseList(
  params: API.PageParams & Partial<API.CrmCase>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.CrmCase>> {
  return request<API.Result<API.CrmCase>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** Add
 * POST /api/crm/crmCase */
export async function addCrmCase(
  data: API.CrmCase & { emailId?: number },
  options?: { [key: string]: any },
): Promise<API.CrmCase> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.CrmCase>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}
/** Update
 * PUT /api/crm/crmCase/{id} */
export async function updateCrmCase(
  caseId: number,
  data: API.CrmCase,
  options?: { [key: string]: any },
): Promise<API.CrmCase> {
  const config: RequestConfig = {
    method: 'PUT',
    data: {
      ...data,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.CrmCase>>(`${urlPrefix}/${caseId}`, config).then((res) => {
    return res.message;
  });
}
