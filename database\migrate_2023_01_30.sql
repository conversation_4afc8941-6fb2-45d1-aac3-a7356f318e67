CREATE TABLE `ean_supplier` (
                                `ean_id` BIGINT(20) UNSIGNED NOT NULL,
                                `supplier_id` BIGINT(20) UNSIGNED NOT NULL,
                                `product_no` VARCHAR(255) DEFAULT NULL,
                                PRIMARY KEY (`ean_id`,`supplier_id`),
                                <PERSON><PERSON>Y `FK_ean_supplier_supplier_id` (`supplier_id`),
                                KEY `IDX_ean_supplier_product_no` (`product_no`),
                                CONSTRAINT `FK_ean_supplier_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE CASCADE,
                                CONSTRAINT `FK_ean_supplier_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE CASCADE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

update ean_text set `official_ingredients` = REGEXP_REPLACE(`official_ingredients`, '( data-bind=".*")', '')
    ,`official_nutrition` = REGEXP_REPLACE(`official_nutrition`, '( data-bind=".*")', '')
;