import { EyeOutlined, LinkOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Card, Tag, Typography, Popover } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { ni, sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getIboPreManagementList, deleteIboPreManagement } from '@/services/foodstore-one/IBO/ibo-pre-management';
import { DEFAULT_PER_PAGE_PAGINATION, IboPreManagementStatus, IboPreManagementStatusKv } from '@/constants';
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import useSupplierOptions from '@/hooks/BasicData/useSupplierOptions';

export const SIboPreManagementStatusComp: React.FC<{ status?: string }> = ({ status }) => {
  let cls = '';
  if (status == IboPreManagementStatus.Sent) {
    cls = 'blue';
  } else if (status == IboPreManagementStatus.Open) {
    cls = 'error';
  } else if (status == IboPreManagementStatus.Invoiced) {
    cls = 'success';
  }

  return status ? <Tag color={cls}>{IboPreManagementStatusKv[status]}</Tag> : null;
};

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.IboPreManagement[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteIboPreManagement(selectedRows.map((row) => row.id)?.join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type SearchFormValueType = Partial<API.IboPreManagement>;

const IboPreManagement: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();

  const { searchSupplierOptions } = useSupplierOptions();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.IboPreManagement>();
  const [selectedRowsState, setSelectedRows] = useState<API.IboPreManagement[]>([]);

  const columns: ProColumns<API.IboPreManagement>[] = [
    {
      title: 'Supplier',
      dataIndex: 'supplier_name',

      width: 150,
      /* render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow({ ...entity });
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      }, */
    },
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      hideInForm: true,
      width: 100,
      render: (dom, record) => dom,
      /* record.order_no ? (
          <a
            href={`/ibo/item-buying-order?ibom_id=${record.id}`}
            target="_blank"
            rel="noreferrer"
            title="Open IBOs filtered on new tab"
          >
            {record.order_no}
          </a>
        ) : null, */
    },
    {
      title: 'Notes',
      dataIndex: 'note',
      ellipsis: true,
      width: 200,
    },
    {
      title: '',
      dataIndex: 'detail_link',
      width: 20,
      render(__, entity) {
        return (
          <Typography.Link
            href={`/ibo/ibo-pre?ibo_pre_management_id=${entity.id}`}
            target="_blank"
            title="Open IBO Pre detail page in new tab"
          >
            <LinkOutlined />
          </Typography.Link>
        );
      },
    },
    {
      title: 'Inbound No',
      dataIndex: 'inbound_no',
      width: 100,
    },
    {
      title: 'Notes (Supplier)',
      dataIndex: 'note_supplier',
      className: 'text-sm',
      width: 200,
    },

    {
      title: 'Created on',
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      className: 'c-grey text-sm',
      sorter: true,
      defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      // tip: 'The username is the unique key',
      colSize: 1,
      search: false,
      align: 'center',
      width: 70,
      render(__, entity) {
        return <SIboPreManagementStatusComp status={entity.status} />;
      },
    },
    {
      title: 'Qty',
      dataIndex: 'qty_detail',
      tooltip: (
        <span>
          x / y: x {'->'} Items which have either scanned items (from Wholesale IBO PreOrder or SKU has some notes) /
          Sum(SKU) <br /> <br /> if Status = Sent/Invoices {'-->'} Qty = 0: light orange. // Qty {'< Sum'} light yellow
          // Qty = Sum light blue.
          <br /> <br /> Note: Only items linked with any Offer!
        </span>
      ),
      width: 80,
      render: (dom, record) => {
        // const num1 = sn(record.ibo_pres_sum_with_note) + sn(record.packed_ready_qty_without_note);
        const num1 = sn(record.linked_processing_count_with_offer);
        const num2 = sn(record.linked_count_with_offer);

        return num2 ? <span className={''}>{`${ni(num1, true)} / ${ni(num2, true)}`}</span> : null;
      },
      onCell: (record) => {
        let cls = '';
        const num1 = sn(record.linked_processing_count_with_offer);
        const num2 = sn(record.linked_count_with_offer);
        if (num2) {
          if (record.status == IboPreManagementStatus.Invoiced || record.status == IboPreManagementStatus.Sent) {
            if (num1 == 0) {
              cls += ' bg-orange1';
            }
            if (num1 < num2) {
              cls += ' bg-yellow1';
            }
            if (num1 == num2) {
              cls += ' bg-blue1';
            }
          }
        }

        return {
          className: cls,
        };
      },
    },

    {
      title: 'Linked Offers',
      dataIndex: 'link_to_offer',
      search: false,
      width: 210,
      ellipsis: false,
      render: (__, record) => {
        return (
          !!record.linked_offers?.length && (
            <>
              {record.linked_offers.map((offer) => (
                <Popover
                  title={`Offer #${offer.offer_no} Notes`}
                  content={<div>{offer.note}</div>}
                  key={offer.id}
                  overlayStyle={{ maxWidth: 400 }}
                >
                  <Typography.Link
                    href={`/quotes/offer-item?offer_id=${offer.id}&offer_no=${offer.offer_no}`}
                    target="_blank"
                    style={{ marginRight: 8, display: 'block' }}
                  >
                    #{offer.offer_no}
                    {offer.quote?.customer_fullname ? ` - ${offer.quote?.customer_fullname}` : ''}
                  </Typography.Link>
                </Popover>
              ))}
            </>
          )
        );
      },
    },
    {
      title: 'Notes PreOrder',
      dataIndex: 'note_customer',
      className: 'text-sm',
      width: 250,
    },
    /* {
      title: 'Updated on',

      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      sorter: true,
      width: 110,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    }, */
    {
      title: '',
      dataIndex: 'id',
      tooltip: 'Open WholeSale (IBO Pre) Register in new tab',
      colSize: 1,
      width: 20,
      search: false,
      render(__, entity) {
        return (
          <Typography.Link
            href={`/ibo/pre-ibo-register?ibo_pre_management_id=${entity.id}`}
            target="_blank"
            style={{ marginRight: 8, display: 'block' }}
          >
            <EyeOutlined />
          </Typography.Link>
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      // tip: 'The username is the unique key',
      colSize: 1,
      search: false,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 80,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({ ...record });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer title="Pre Order Overview">
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ibo_pre_management', {})}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            request={async (params) => {
              return searchSupplierOptions(params);
            }}
            width="md"
            name="supplier_id"
            label="Supplier"
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormText name={'note'} label="Note" width={150} placeholder={''} />
          <ProFormText name={'inbound_no'} label="Inbound No" width={120} placeholder={''} />
          <ProFormText name={'note_supplier'} label="Notes (Supplier)" width={150} placeholder={''} />
          <ProFormText name={'note_customer'} label="Notes (Customer)" width={150} placeholder={''} />
          <ProFormSelect
            name="status"
            label="Status"
            mode="single"
            valueEnum={IboPreManagementStatusKv}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          ></ProFormSelect>
        </ProForm>
      </Card>

      <ProTable<API.IboPreManagement, API.PageParams>
        headerTitle={'Pre Order Overview'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        params={{ with: 'qtyDetail' }}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ibo_pre_management', searchValues);
          Util.setSfValues('sf_ibo_pre_management_p', params);

          const newParams = {
            ...params,
            ...Util.mergeGSearch(searchValues),
          };

          return getIboPreManagementList(newParams, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          selectedRowKeys: selectedRowsState.map((x) => x.id as any),
        }}
        columnEmptyText={''}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              Chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              Pre Order Overview &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              const res = await handleRemove(selectedRowsState);
              if (res) {
                setSelectedRows([]);
                actionRef.current?.reload();
              }
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.IboPreManagement>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.IboPreManagement>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default IboPreManagement;
