import _ from 'lodash';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/item/ean-task';

/** get GET /api/item/ean-task */
export async function getEanTaskList(
  params: API.PageParamsExt,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.EanTask>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put POST /api/item/ean-task/{id} */
export async function addEanTask(data?: API.EanTask, options?: Record<string, any>) {
  return request<API.EanTask>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/item/ean-task/{id} */
export async function updateEanTask(id?: number, data?: API.EanTask, options?: Record<string, any>) {
  return request<API.EanTask>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/item/ean-task */
export async function deleteEanTask(options?: Record<string, any>) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options.id : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
