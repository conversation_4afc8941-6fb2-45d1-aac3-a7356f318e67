/* eslint-disable */
import { sn } from '@/util';
import { request } from 'umi';

const urlPrefix = '/api/basic-data/trademark';

/** rule GET /api/basic-data/trademark */
export async function getTrademarkList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export const TRADEMARK_FILTER_DEFAULT = -10;
export const TRADEMARK_FILTER_ALL_ITEM = -1;

export const TRADEMARK_SPECIAL_FILTERS = [
  {
    label: `Default Groups`,
    value: TRADEMARK_FILTER_DEFAULT,
  },
  {
    label: `All items`,
    value: TRADEMARK_FILTER_ALL_ITEM,
  },
  {
    value: 'sep',
    type: 'separator',
    label: '-------------------------',
    children: [],
    disabled: true,
  },
];

export async function getTrademarkListSelectOptions(params: API.PageParams, appendMode?: number, includeQtySuffix?: boolean) {
  const res = await getTrademarkList({ ...params, pageSize: 2000 }, { name: 'ascend' }, {});
  const defaultList: any[] = [];
  // default options
  if (appendMode == 1) {
    TRADEMARK_SPECIAL_FILTERS.forEach((element) => {
      defaultList.push(element);
    });
  }

  // actual options
  if (res && res.data) {
    res.data.forEach((x: API.Trademark) => {
      defaultList.push({
        label: `${x.name}${includeQtySuffix && sn(x.qty) ? ` (${x.qty})` : ''}`,
        value: x.id,
      });
    });
  }
  return defaultList;
}

/** put PUT /api/basic-data/trademark */
export async function updateTrademark(data: API.Trademark, options?: { [key: string]: any }) {
  return request<API.Trademark>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/trademark */
export async function addTrademark(data: API.Trademark, options?: { [key: string]: any }) {
  return request<API.Trademark>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/trademark/{id} */
export async function deleteTrademark(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}


