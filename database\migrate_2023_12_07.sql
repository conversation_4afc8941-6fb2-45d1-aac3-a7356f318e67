CREATE TABLE `ibo_pre_management`
(
    `id`          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `supplier_id` BIGINT UNSIGNED COMMENT 'FK: Order No',
    `order_no`    INT             NOT NULL COMMENT 'Order No',
    `status`      VARCHAR(31) COMMENT 'Status: open, etc',
    `created_on`  DATETIME,
    `created_by`  INT,
    `updated_on`  DATETIME,
    `updated_by`  INT,
    PRIMARY KEY (`id`),
    INDEX `IDX_ibo_pre_management_order_no` (`order_no`),
    INDEX `IDX_ibo_pre_management_status` (`status`),
    CONSTRAINT `FK_ibo_pre_management_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON UPDATE CASCADE ON DELETE SET NULL
);


ALTER TABLE `ibo_pre`
    ADD COLUMN `ibo_pre_management_id` BIGINT UNSIGNED NULL AFTER `id`,
    ADD CONSTRAINT `FK_ibo_pre_management_id` FOREIGN KEY (`ibo_pre_management_id`) REFERENCES `ibo_pre_management` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;


ALTER TABLE `ibo_pre`
    ADD COLUMN `nan`          VARCHAR(255) NULL AFTER `status`,
    ADD COLUMN `qty_pkg`      INT          NULL AFTER `nan`,
    ADD COLUMN `qty_pallet`   INT          NULL AFTER `qty_pkg`,
    ADD COLUMN `price_pallet` DOUBLE       NULL AFTER `qty_pallet`;
