import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nl2br } from '@/util';
import { getMagOrderShipmentCommentList } from '@/services/foodstore-one/Magento/order-shipment-comment';

export type OrderShipmentCommentsListProps = {
  orderId: number;
  refreshTick?: number;
  filterType?: 'light' | 'query';
};

export type SearchFormValueType = Partial<API.OrderShipmentComment>;

const OrderShipmentCommentsList: React.FC<OrderShipmentCommentsListProps> = ({ orderId, refreshTick, filterType }) => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.OrderShipmentComment>[] = [
    {
      title: '',
      dataIndex: 'comment',
      valueType: 'digit',
      sorter: true,
      render: (dom, record) => <div dangerouslySetInnerHTML={{ __html: nl2br(record?.comment) }} />,
    },
    {
      title: 'Created',
      dataIndex: ['created_at'],
      valueType: 'dateRange',
      sorter: true,
      align: 'center',
      ellipsis: true,
      defaultSortOrder: 'descend',
      width: 120,
      render: (dom, record) => {
        return <>{Util.dtToDMYHHMM(record?.created_at)}</>;
      },
    },
  ];

  useEffect(() => {
    if (orderId) {
      actionRef.current?.reload();
    }
  }, [orderId]);

  return (
    <ProTable<API.OrderShipmentComment, API.PageParams>
      cardProps={{ bodyStyle: { padding: 0 } }}
      actionRef={actionRef}
      rowKey="entity_id"
      bordered
      revalidateOnFocus={false}
      sticky
      scroll={{ x: '100%' }}
      size="small"
      loading={loading}
      onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
      params={{ orderId: orderId }}
      pagination={{
        hideOnSinglePage: true,
        showSizeChanger: false,
        pageSize: 20,
      }}
      options={false}
      search={false}
      columns={columns}
      request={getMagOrderShipmentCommentList}
      tableAlertRender={false}
    />
  );
};

export default OrderShipmentCommentsList;
