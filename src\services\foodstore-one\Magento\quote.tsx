/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/lib/select';
import { MagentoQuoteStatusKv } from '@/constants';
import Util, { sn } from '@/util';

const urlPrefix = '/api/magento-data/quotes';

/**
 * Get quotes list
 *
 * GET /api/magento-data/quotes
 */
export async function getQuotesListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.MagQuote> & { imports?: API.Import[] }>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
    imports: res.message.imports ?? [],
  }));
}

/**
 * Get quote customers list
 *
 * GET /api/magento-data/quotes/customers
 */
export async function getQuoteCustomersListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.MagQuote>>>(`${urlPrefix}/customers`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function exportQuotesList(params: API.PageParamsExt, sort?: any, filter?: any) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export`, {
    method: 'GET',
    params: {
      ...params,
      sort,
      ...filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/**
 *
 * GET /api/magento-data/quotes/statusACList
 */
export async function getQuoteStatusACList(
  params?: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<(DefaultOptionType & { cnt?: number })[]> {
  return request<API.BaseResult>(`${urlPrefix}/statusACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => {
    const ret = Object.keys(MagentoQuoteStatusKv).map((x) => ({
      value: sn(x),
      label: (
        <div style={{ display: 'flex' }}>
          <div style={{ flex: 'auto' }}>{MagentoQuoteStatusKv[x]}</div>
          {res.message[x] && (
            <div style={{ flex: '0 0 65px', textAlign: 'right', paddingRight: 10 }}>
              ({Util.numberFormat(res.message[x])})
            </div>
          )}
        </div>
      ),
      cnt: res.message[x],
    }));
    return ret;
  });
}

/**
 * Copy current quote into Offer
 *
 * PUT /api/magento-data/quotes/{id}/copyToOffer
 */
export async function copyToOffer(entity_id: number, data?: { offer_id?: number }, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Offer>>(`${urlPrefix}/${entity_id}/copyToOffer`, {
    method: 'PUT',
    data,
    ...(options || {}),
  }).then((res) => res.message);
}
