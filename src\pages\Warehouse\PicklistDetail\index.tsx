import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  message,
  Modal,
  Popconfirm,
  Row,
  Col,
  Tag,
  Space,
  Typography,
  InputNumber,
  Popover,
} from 'antd';
import type { Key } from 'react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import {
  DictCode,
  LS_TOKEN_NAME,
  MagentoOrderStatusOptions,
  ShippingServiceNameType,
  StockStableStatusOptionsKv,
} from '@/constants';
import Util, { nf2, ni, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { useLocation, useModel } from 'umi';
import {
  CheckSquareFilled,
  CommentOutlined,
  DeleteOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  HistoryOutlined,
  ImportOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  LoadingOutlined,
  MinusOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  PrinterOutlined,
  PushpinOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { NoStockSkuType } from '@/services/foodstore-one/Warehouse/picklist';
import {
  bookPicklist,
  checkParcelsAndCreateShipment,
  createPicklist,
  deletePicklist,
  exportPicklistCsv,
  exportPicklistDeliveryNote,
  exportPicklistPdf,
  exportShippingLabelsInPicklist,
  exportShippingMergedLabelsInPicklist,
  getPicklistACList,
  preparePicklistOption,
} from '@/services/foodstore-one/Warehouse/picklist';
import { getPickListDetails } from '@/services/foodstore-one/Warehouse/picklist-detail';
import type { DefaultOptionType } from 'antd/lib/select';
import OrderShipmentCommentsList from './components/OrderShipmentCommentsList';
import { isArray } from 'lodash';
import type { UploadChangeParam, UploadFile } from 'antd/lib/upload';
import ExpDate from '@/pages/Report/Order/components/ExpDate';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import { FullAddress, SOrderId, SParcelLink } from '@/pages/Magento/Order';
import UpdateOrderExtraFormModal from '@/pages/Magento/Order/components/UpdateOrderExtraFormModal';
import ToughStocksModal from './components/ToughStocksModal';
import { downloadFileB64 } from '@/services/foodstore-one/api';
import printJS from 'print-js';
import { isBookReturnedByItem } from '@/pages/Magento/Order/OrderDetail';
import usePicklistAlert from './hooks/usePicklistAlert';
import NoStockSkuModal from './components/NoStockSkuModal';
import OrderParcelLogListModal from '@/pages/Magento/OrderParcel/components/OrderParcelLogListModal';
import { checkParcelStatus } from '@/services/foodstore-one/Magento/order-parcel';

export type SearchFormValueType = Partial<API.WarehousePicklist>;

const PicklistDetail: React.FC = () => {
  const location: any = useLocation();
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const { getDictByCode, getParcelUrl } = useModel('app-settings');

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingUpload, setLoadingUpload] = useState<boolean>(false);
  const [picklist, setPicklist] = useState<API.WarehousePicklist[]>([]);
  const [selectedPicklist, setSelectedPicklist] = useState<DefaultOptionType>();
  const [expandedRowKeys, setExpandedRowKeys] = useState<Key[]>([]);

  // collapsible expandedAll button
  const [dataSource, setDataSource] = useState<API.Order[]>([]);

  // comments modal
  const [currentOrder, setCurrentOrder] = useState<API.Order>();
  const [openCommentsModal, setOpenCommentsModal] = useState<boolean>(false);
  // stock qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [currentOrderItemRow, setCurrentOrderItemRow] = useState<API.OrderItem>();
  const [openUpdateOrderExtraModal, setOpenUpdateOrderExtraModal] = useState<boolean>(false);
  // const [openTimeTrackingModal, setOpenTimeTrackingModal] = useState<boolean>(false);

  const [allBooked, setAllBooked] = useState<boolean>(false);

  // tough stock list
  const [openToughSSModal, setOpenToughSSModal] = useState<boolean>(false);
  const [toughSSList, setToughSSList] = useState<API.StockStable[]>([]);
  const [tmpPrePdfFile, setTmpPrePdfFile] = useState<API.Downloadable | null>(null);

  // no stock SKUs
  const [openNoStockSKUModal, setOpenNoStockSKUModal] = useState<boolean>(false);
  const [noStockSKUList, setNoStockSKUList] = useState<NoStockSkuType[]>([]);

  const [firstDataLoaded, setFirstDataLoaded] = useState<boolean>(false);

  // Parcel track logs modal
  const [orderParcelRow, setOrderParcelRow] = useState<API.OrderParcel>();
  const [openOrderParcelLog, setOpenOrderParcelLog] = useState<boolean>(false);

  const loadPicklistACList = useCallback((params?: any) => {
    getPicklistACList({ pageSize: 100, ...params, is_pre: 0 }, { id: 'descend' })
      .then((res) => {
        setPicklist(res);

        // to do
        // const defaultPicklist = res.find((x) => x.id == 98);
        if (!params?.isSearch) {
          const defaultPicklist = res[0];
          if (res && defaultPicklist) {
            if (defaultPicklist) {
              setSelectedPicklist(preparePicklistOption(defaultPicklist));
              setAllBooked(defaultPicklist.is_full_stock_stable_updated == 1 || defaultPicklist.unbooked_count == 0);
            }
          }
        }
      })
      .catch(Util.error);
  }, []);

  useEffect(() => {
    loadPicklistACList();
  }, [loadPicklistACList]);

  useEffect(() => {
    const idInUrl = sn(location.query?.id);
    const tmpFunc = async () => {
      let selected: any = null;

      const res = await getPicklistACList({ pageSize: 1, id: idInUrl }, { id: 'descend' });

      if (res && res[0]) {
        selected = res[0];
        setPicklist((prev) => (prev.findIndex((x) => x.id == idInUrl) >= 0 ? prev : [selected, ...prev]));
      }

      if (selected) {
        setSelectedPicklist(preparePicklistOption(selected));
        setAllBooked(selected.is_full_stock_stable_updated == 1 || selected.unbooked_count == 0);
      }
    };

    if (location.query.id && firstDataLoaded) {
      tmpFunc();
    }
  }, [location.query.id, firstDataLoaded]);

  useEffect(() => {
    if (selectedPicklist?.value) {
      console.log('----> []', selectedPicklist);
      searchFormRef.current?.setFieldValue('picklist_id', selectedPicklist?.value);
      actionRef.current?.reload();
    }
  }, [selectedPicklist?.value]);

  const columns: ProColumns<API.Order>[] = useMemo(
    () => [
      {
        title: 'Order ID',
        dataIndex: 'entity_id',
        sorter: true,
        showSorterTooltip: false,
        hideInSearch: true,
        align: 'left',
        width: 90,
        defaultSortOrder: 'ascend',
        render: (dom, record) => <SOrderId order={record} />,
      },
      {
        title: '',
        dataIndex: 'mag_order_shipment_comments_count',
        sorter: false,
        hideInSearch: true,
        align: 'center',
        width: 30,
        render: (dom, record) =>
          sn(record?.mag_order_shipment_comments_count) > 0 ? (
            <Badge size="small" offset={[6, -2]} count={sn(record?.mag_order_shipment_comments_count)}>
              <CommentOutlined
                style={{ color: 'red' }}
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentOrder(record);
                  setOpenCommentsModal(true);
                }}
              />{' '}
            </Badge>
          ) : (
            <></>
          ),
      },
      {
        title: 'Store',
        dataIndex: ['store_id'],
        sorter: true,
        showSorterTooltip: false,
        align: 'center',
        ellipsis: true,
        width: 80,
      },
      {
        title: 'Increment ID',
        dataIndex: ['increment_id'],
        sorter: true,
        showSorterTooltip: false,
        width: 120,
        render(dom, record) {
          return record?.entity_id ? (
            <Typography.Link
              href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${
                record?.entity_id
              }/`}
              target="_blank"
              copyable
            >
              {record.increment_id}
            </Typography.Link>
          ) : (
            dom
          );
        },
      },
      {
        title: 'Status',
        dataIndex: ['status'],
        sorter: true,
        showSorterTooltip: false,
        align: 'center',
        ellipsis: true,
        width: 120,
        render: (_, record) => {
          const status = record.status;
          let color = 'default';
          switch (status ?? '') {
            case 'complete':
            case 'closed':
              color = 'success';
              break;
            case 'processing':
              color = 'blue';
              break;
            case 'pending':
              color = 'orange';
              break;
            case 'canceled':
              color = 'red';
              break;
          }

          return <Tag color={color as any}>{MagentoOrderStatusOptions[status || '-'] ?? '-'}</Tag>;
        },
      },
      {
        title: 'Ordered Qty',
        dataIndex: ['total_qty_ordered'],
        sorter: false,
        align: 'right',
        width: 120,
        tooltip: 'Total ordered Qty in the picklist.',
        render: (_, record) => {
          const picklistQtyOrdered = record.mag_order_items?.reduce(
            (accumulator, currentValue) => accumulator + sn(currentValue.qty_ordered),
            0,
          );
          return picklistQtyOrdered != record.total_qty_ordered ? (
            <span>
              <b>{picklistQtyOrdered}</b> of {record.total_qty_ordered}
            </span>
          ) : (
            <b>{picklistQtyOrdered}</b>
          );
        },
      },
      {
        title: 'Name',
        dataIndex: ['sa_initials'],
        sorter: false,
        align: 'center',
        ellipsis: true,
        width: 80,
        render: (dom, record) => (
          <Typography.Text mark={record?.warn_sa_fullname || record?.warn_sa_fullname_wrong}>
            {record.sa_initials}
          </Typography.Text>
        ),
      },
      /* {
        title: 'Zip',
        dataIndex: ['sa_zip'],
        sorter: false,
        align: 'center',
        ellipsis: true,
        width: 80,
      },
      {
        title: 'City',
        dataIndex: ['sa_city'],
        sorter: false,
        align: 'left',
        ellipsis: true,
        width: 170,
      }, */
      {
        title: 'Delivery Address',
        dataIndex: 'sa_full',
        align: 'left',
        width: 400,
        tooltip: 'Orange colored rows are in warnings list. Highlighted parts may be wrong!',
        ellipsis: true,
        render(dom, record) {
          return (
            <>
              <FullAddress order={record} type="shipping" />
            </>
          );
        },
        onCell(record) {
          return { style: { paddingRight: 16 } };
        },
      },
      {
        title: 'Shipped Parcel',
        dataIndex: ['shipping_imported_list'],
        align: 'left',
        width: 150,
        tooltip: 'Green indicates a processed shipment on Magento',
        render(__, record) {
          return record.latest_shipping
            ? [record.latest_shipping]?.map((x) => {
                let cls = 'text-sm';
                if (x.mag_ship_id) {
                  cls += ' c-green';
                }
                const title = x.carrier_code || x.title ? `${x.title} | ${x.carrier_code}` : '';
                return (
                  <div key={x.id} className={cls} title={title}>
                    <a
                      href={`${getDictByCode(DictCode.MAG_ADMIN_URL_TRACKING)}${x.parcel_no}`}
                      target="_blank"
                      rel="noreferrer"
                      className={cls}
                    >
                      {x.parcel_no}
                    </a>
                  </div>
                );
              })
            : null;
        },
      },
      {
        title: 'Parcel Labels',
        dataIndex: ['labels'],
        align: 'left',
        width: 200,
        // tooltip: 'Green indicates a processed shipping on Magento',
        render(dom, record) {
          return record.labels
            ? record.labels?.map((x) => {
                return (
                  <Row key={x.id} gutter={4} style={{ alignItems: 'center' }}>
                    <Col className="text-sm" span={4}>
                      <Typography.Text ellipsis>{x.service_name}</Typography.Text>
                    </Col>
                    <Col className="text-sm" span={13}>
                      <SParcelLink
                        parcel_no={x.parcel_no}
                        track_id={x.track_id}
                        service_name={x.service_name}
                        showTrackId
                      />
                    </Col>
                    <Col span={1} className="text-sm c-red">
                      <span title="Return Label">{x.ref_no?.endsWith(',Return') ? 'R' : ''}</span>
                    </Col>
                    <Col span={2}>
                      <Typography.Link href={`${API_URL}/api/${x.url}`} title="Open Label PDF" target="_blank">
                        <LinkOutlined />
                      </Typography.Link>
                    </Col>
                    <Col span={2}>
                      <Button
                        type="link"
                        size="small"
                        icon={<PrinterOutlined />}
                        title="Re-print Label"
                        style={{ height: 16 }}
                        onClick={() => {
                          downloadFileB64({ type: 'pdf', b64: true, key: x.file_path || '' }).then((b64) => {
                            printJS({ printable: b64, type: 'pdf', base64: true, showModal: true });
                          });
                        }}
                      />
                    </Col>
                  </Row>
                );
              })
            : null;
        },
      },
      {
        title: 'Shipping Log',
        dataIndex: ['shipping_log'],
        width: 180,
        render: (__, record) => {
          const orderParcel = record.latest_order_parcel;
          return (
            <Row>
              <Col flex="auto">
                <div style={{ lineHeight: 1.3 }}>{orderParcel?.status}</div>
                <div style={{ lineHeight: 1.3 }}>{Util.dtToDMYHHMM(orderParcel?.shipping_updated_on)}</div>
              </Col>
              <Col flex="0 0 32px">
                <Space direction="horizontal" size={8} style={{ lineHeight: 1 }}>
                  <ReloadOutlined
                    title="Import parcel status"
                    className="c-blue"
                    size={24}
                    onClick={() => {
                      const hide = message.loading('Checking parcels tracking data...');
                      checkParcelStatus({
                        order_id: record.entity_id,
                      })
                        .then((res) => {
                          message.success('Checked successfully.');
                          actionRef.current?.reload();
                        })
                        .catch(Util.error)
                        .finally(() => hide());
                    }}
                  />
                  {orderParcel && (
                    <HistoryOutlined
                      size={24}
                      className="c-blue"
                      title="View parcel tracking history."
                      onClick={() => {
                        setOrderParcelRow(orderParcel);
                        setOpenOrderParcelLog(true);
                      }}
                    />
                  )}
                </Space>
              </Col>
            </Row>
          );
        },
      },
      {
        title: '',
        dataIndex: ['mainActions'],
        sorter: false,
        align: 'right',
        ellipsis: true,
        width: 180,
        render: (__, record) => (
          <Space size={16}>
            <Button
              type="primary"
              size="small"
              ghost
              htmlType="button"
              onClick={() => {
                setCurrentOrder(record);
                setOpenUpdateOrderExtraModal(true);
              }}
            >
              Create Shipment
            </Button>
            <Button
              type="link"
              size="small"
              icon={<FilePdfOutlined />}
              title="Download delivery notes in PDF"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                const hide = message.loading('Downloading delivery note as PDF format...', 0);
                setLoading(true);
                exportPicklistDeliveryNote(searchFormRef.current?.getFieldValue('picklist_id'), {
                  entity_id: record.entity_id,
                })
                  .then((res) => {
                    hide();
                    if (isArray(res) && res.length) {
                      const file = res[0];
                      if (file.url) {
                        window.open(`${API_URL}/api/${file.url}`, '_blank');
                      }
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                    setLoading(false);
                  });
              }}
            />
          </Space>
        ),
      },
    ],
    [getDictByCode, getParcelUrl],
  );

  const onHeaderCell = useCallback((__: any) => {
    const defaultHeaderProps = {
      className: 'text-sm',
      style: {
        paddingTop: 4,
        paddingBottom: 4,
        fontWeight: 'normal',
      },
    };
    return defaultHeaderProps;
  }, []);

  const smColumns: ProColumns<Partial<API.StockStable>>[] = useMemo(
    () => [
      {
        title: 'WL',
        dataIndex: ['warehouse_location', 'name'],
        editable: false,
        width: 90,
        align: 'center',
        onHeaderCell,
      },
      {
        title: 'Priority',
        dataIndex: ['warehouse_location', 'priority'],
        editable: false,
        width: 90,
        align: 'right',
        onHeaderCell,
        render: (dom, record) => {
          return ni(record.warehouse_location?.priority);
        },
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 90,
        align: 'center',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return <ExpDate date={record.exp_date} />;
        },
      },
      {
        title: 'IBO ID',
        dataIndex: ['ibo', 'id'],
        width: 60,
        align: 'left',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          const title = `IBO Overview: #${record.ibo?.ibom_id}, Price: ${nf2(
            record.ibo?.price,
            true,
          )}, Exp. Date: ${Util.dtToDMY(record.ibo?.exp_date)}`;
          return record.ibo ? <span title={title}>#{record.ibo_id}</span> : null;
        },
      },
      /* {
        title: 'BP',
        dataIndex: ['ibo', 'price'],
        width: 70,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => (record.ibo?.price ? <span>€{nf2(record.ibo.price)}</span> : null),
      }, */
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 90,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 80,
        align: 'right',
        editable: false,
        onHeaderCell,
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'Status',
        dataIndex: ['status'],
        dataType: 'select',
        width: 90,
        editable: false,
        valueEnum: StockStableStatusOptionsKv,
        onHeaderCell,
      },
    ],
    [onHeaderCell],
  );

  const [warningsDefCount, setWarningsDefCount] = useState<number>(0);

  const expandedRowRender = (record: API.Order) => {
    return (
      <ProTable<API.OrderItem>
        columns={[
          {
            title: 'SKU',
            dataIndex: 'sku',
            width: 100,
            tooltip: 'Click to view stock detail.',
            copyable: true,
            onHeaderCell,
            onCell: (recordOI) => {
              return {
                className: 'cursor-pointer c-blue',
                onClick: (e) => {
                  setCurrentOrderItemRow(recordOI);
                  handleQtyModalVisible(true);
                },
              };
            },
          },
          {
            title: 'EAN',
            dataIndex: ['item_ean', 'ean'],
            width: 140,
            onHeaderCell,
            copyable: true,
          },
          { title: 'Name', dataIndex: 'name', width: 200, onHeaderCell },
          { title: 'Ordered Qty', dataIndex: 'qty_ordered', width: 100, onHeaderCell },
          {
            title: 'Open Qty / Stock',
            dataIndex: 'open_qty_ordered',
            width: 100,
            onHeaderCell,
            tooltip:
              'Open orders qty pcs & Stock qty pcs. If Open > Stock Qty, RED background and you need to pay attention to control stocks .',
            render: (dom, r) => {
              const openQtyPcs = sn(r.open_qty_ordered);
              const stockQtyPcs = sn(
                r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
              );
              return (
                <Row gutter={8} className="c-grey text-right">
                  <Col span={10}>{ni(openQtyPcs, true)}</Col>
                  <Col span={14}>{ni(stockQtyPcs, true)}</Col>
                </Row>
              );
            },
            onCell: (r) => {
              const openQtyPcs = sn(r.open_qty_ordered);
              const stockQtyPcs = sn(
                r.item_ean?.is_single ? r.item_ean?.stock_stables_sum_piece_qty : r.item_ean?.stock_stables_sum_box_qty,
              );
              return {
                className: openQtyPcs > stockQtyPcs ? 'bg-red' : '',
              };
            },
          },
          {
            title: 'Price',
            dataIndex: 'price',
            width: 70,
            align: 'right',
            onHeaderCell,
            render: (dom, r) => {
              // return r?.item_ean?.attr_case_qty ? nf2(sn(r.price) / sn(r?.item_ean?.attr_case_qty)) : nf2(r.price);
              return nf2(r.price);
            },
          },
          {
            title: 'Discount',
            dataIndex: ['item_ean', 'fs_special_discount'],
            width: 70,
            onHeaderCell,
            align: 'center',
            render: (dom, r) => <span className="c-orange">{r.item_ean?.fs_special_discount}</span>,
          },
          {
            title: 'Stocks',
            dataIndex: 'stock_stables',
            width: 700,
            onHeaderCell,
            render: (dom, orderItem) => {
              if (orderItem.picklist_detail?.is_stock_stable_updated) {
                return (
                  <ProTable
                    columns={smColumns.filter((x) => x.title != 'Status')}
                    cardProps={{ bodyStyle: { padding: '0 0' } }}
                    rowKey="id"
                    headerTitle={false}
                    search={false}
                    options={false}
                    pagination={false}
                    scroll={{ y: 'auto' }}
                    dataSource={orderItem?.stock_stable_booked_list ?? []}
                    columnEmptyText={''}
                    locale={{ emptyText: <></> }}
                    size="small"
                  />
                );
              } else {
                if (!orderItem?.item_ean?.stock_stables || !orderItem?.item_ean?.stock_stables?.length) return <></>;
                return (
                  <ProTable
                    columns={smColumns}
                    cardProps={{ bodyStyle: { padding: '0 0' } }}
                    rowKey="id"
                    headerTitle={false}
                    search={false}
                    options={false}
                    pagination={false}
                    scroll={{ y: 'auto' }}
                    dataSource={orderItem?.item_ean?.stock_stables ?? []}
                    columnEmptyText={''}
                    locale={{ emptyText: <></> }}
                    size="small"
                  />
                );
              }
            },
          },
          {
            title: 'Mag. IDs',
            dataIndex: 'product_id',
            align: 'center',
            width: 110,
            tooltip: 'Product ID / Order Item ID in Magento',
            showSorterTooltip: false,
            onHeaderCell,
            className: 'text-sm c-grey',
            render: (__, r) => `${r.product_id} / ${r.item_id}`,
          },
          {
            title: 'Booked?',
            dataIndex: 'is_stock_stable_updated',
            align: 'center',
            width: 50,
            showSorterTooltip: false,
            onHeaderCell,
            className: 'p-0',
            ellipsis: true,
            fixed: 'right',
            render: (__, r) =>
              r.picklist_detail?.is_stock_stable_updated ? (
                <CheckSquareFilled className={isBookReturnedByItem(r) ? 'c-red' : 'c-green'} />
              ) : null,
          },
        ]}
        cardProps={{ bodyStyle: { padding: '0 0 0 100px' } }}
        rowKey="item_id"
        size="small"
        headerTitle={false}
        search={false}
        options={false}
        pagination={false}
        dataSource={record?.mag_order_items ?? []}
        rowClassName={(oiRecord) => (oiRecord.item_ean?.is_single ? 'row-single' : 'row-multi')}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
    );
  };

  const handleCreate = (data: any) => {
    Modal.confirm({
      title: <>Create a picklist (No shipment)</>,
      content: (
        <Row gutter={16} style={{ marginTop: 16, alignItems: 'center' }}>
          <Col flex={'120px'}>
            Max Orders <InfoCircleOutlined title="If empty, new picklist with all processing orders will be created." />{' '}
          </Col>
          <Col flex="auto">
            <InputNumber id="orderLimit" width="xs" />
          </Col>
        </Row>
      ),
      onOk: async () => {
        const limit = (document.getElementById('orderLimit') as HTMLInputElement)?.value;
        const newData = data ?? {};
        newData.limit = limit;
        const hide = message.loading('Creating a new picklist...');
        setLoading(true);
        createPicklist(newData)
          .then((newRow) => {
            hide();
            message.success('Successfully created!');
            loadPicklistACList();
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });
      },
    });
  };

  const handlePrintLabel = async (service_name: ShippingServiceNameType) => {
    if (!selectedPicklist?.value) {
      message.info('Picklist is not selected.');
      return;
    }
    const hide = message.loading(`Downloading ${service_name} Label as PDF format...`, 0);
    setLoading(true);
    exportShippingLabelsInPicklist(sn(selectedPicklist?.value), { service_name })
      .then((res) => {
        hide();
        actionRef.current?.reload();
        /* if (res.files?.length) {
          for (const file of res.files) {
            printJS({ printable: file.b64, type: 'pdf', base64: true, showModal: true });
          }
        } */
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  const handlePrintLabelMerged = () => {
    if (!selectedPicklist?.value) {
      message.info('Picklist is not selected.');
      return;
    }
    const hide = message.loading(`Create merged Label as PDF format...`, 0);
    setLoading(true);
    exportShippingMergedLabelsInPicklist(sn(selectedPicklist?.value))
      .then((res) => {
        hide();
        // actionRef.current?.reload();

        if (res.file) {
          if (Util.isFirefox()) {
            window.open(`${API_URL}/api/${res.file?.url}`, '_blank');
          } else {
            printJS({ printable: res.file?.b64, type: 'pdf', base64: true, showModal: true });
          }
        }

        if ('label_files' in res) {
          setPicklist((prev) => {
            const newPrev = [...prev];
            const existedRow = newPrev.find((x) => x.id == selectedPicklist.id);
            if (existedRow) existedRow.label_files = res.label_files;
            return newPrev;
          });
          setSelectedPicklist((p) => ({ ...p, label_files: res.label_files } as any));
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  const { alertsElements } = usePicklistAlert();

  return (
    <PageContainer
      extra={
        warningsDefCount > 0 || alertsElements ? (
          <div style={{ position: 'absolute', top: 10, left: '50%', marginLeft: -150, zIndex: 10 }}>
            {warningsDefCount > 0 && (
              <Alert
                key="warn-def"
                message={`WARNING: ${warningsDefCount} invalid delivery addresses detected in orange colored rows!`}
                type="error"
                style={{ paddingTop: 2, paddingBottom: 2, border: '1px solid #f00', color: '#f00' }}
              />
            )}
            {alertsElements}
          </div>
        ) : undefined
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            name={'picklist_id'}
            label="Picklist"
            allowClear
            showSearch
            options={picklist.map((x) => preparePicklistOption(x))}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onSearch: (value) => {
                console.log('onSearch: ', value);
                loadPicklistACList({ keyWords: value, isSearch: true });
              },
              onChange: (value, option) => {
                setSelectedPicklist(option as any);
                if (value) {
                  setAllBooked(
                    (option as any).is_full_stock_stable_updated == 1 ||
                      (option as Partial<API.WarehousePicklist>).unbooked_count == 0,
                  );
                } else {
                  setAllBooked(true);
                }
                actionRef.current?.reload();
              },
            }}
            placeholder={'Picklist'}
            width={'sm'}
          />
          <ProFormSelect
            name={'picklist_order_status'}
            label="Order Status"
            allowClear
            options={Object.keys(MagentoOrderStatusOptions).map((x) => ({
              value: x,
              label: MagentoOrderStatusOptions[x],
            }))}
            placeholder={'Order Status'}
            width={'sm'}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormText name={'entity_id'} label="Order ID" width={'xs'} placeholder={'Order ID'} />
        </ProForm>
      </Card>

      <ProTable<API.WarehousePicklistDetail | API.Order, API.PageParams>
        headerTitle={<div style={{ minWidth: 400 }}>{selectedPicklist?.label ?? ''}</div>}
        actionRef={actionRef}
        rowKey="entity_id"
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, setting: false }}
        search={false}
        sticky
        size="small"
        scroll={{ x: 800 }}
        onRequestError={Util.error}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_picklist_detail', searchFormValues);

          if (!searchFormValues.picklist_id) {
            return Promise.resolve([]);
          }

          setLoading(true);

          return getPickListDetails(
            {
              ...params,
              ...searchFormValues,
              listMode: 'byOrder',
              with: 'magInventoryStocksQty,warnings,warnings_def,extra,latestShipping,labels,stockStableBookedList,latestOrderParcel',
            },
            sort,
            filter,
          )
            .then((res) => {
              if (res) {
                let isAllBookedCheck = true;
                for (const x of res?.data ?? []) {
                  for (const item of x?.mag_order_items ?? []) {
                    // console.log(item?.picklist_detail?.is_stock_stable_updated == 1);
                    isAllBookedCheck &&= item?.picklist_detail?.is_stock_stable_updated == 1;
                  }
                }
                // console.log('all booked?', isAllBookedCheck);
                setAllBooked(isAllBookedCheck);
                setExpandedRowKeys(res?.data?.map?.((x_1: API.Order) => x_1.entity_id as any));
                setDataSource(res?.data?.map?.((x_2: API.Order) => ({ entity_id: x_2.entity_id } as any)));
                setWarningsDefCount(res.data.reduce((prev, current) => prev + (current.warnings_def ? 1 : 0), 0));

                setFirstDataLoaded(true);
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        columns={columns}
        toolBarRender={(action, rows) => [
          <div key="1" style={{ display: 'flex', flexWrap: 'wrap', columnGap: 8, rowGap: 4 }}>
            {[
              <Button
                type="primary"
                key="export-pdf-pre"
                size="small"
                icon={<FilePdfOutlined />}
                disabled={!selectedPicklist?.value}
                title="Download pre summary in PDF"
                onClick={() => {
                  const hide = message.loading('Downloading pre picklist data as PDF format...', 0);
                  setLoading(true);
                  exportPicklistPdf(searchFormRef.current?.getFieldValue('picklist_id'), { isPreList: true })
                    .then((res) => {
                      hide();

                      if (res.extra?.toughSS?.length) {
                        setToughSSList(res.extra?.toughSS || []);
                        setOpenToughSSModal(true);
                        setTmpPrePdfFile({ type: res.type, key: res.key, url: res.url });
                      } else {
                        setToughSSList([]);
                        setOpenToughSSModal(false);
                        setTmpPrePdfFile(null);
                      }

                      if (res.url) {
                        window.open(`${API_URL}/api/${res.url}`, '_blank');
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                Pre Summary
              </Button>,
              <Button
                type="primary"
                key="export-pdf"
                size="small"
                icon={<FilePdfOutlined />}
                disabled={!selectedPicklist?.value}
                title="Download summary in PDF"
                onClick={() => {
                  const hide = message.loading('Downloading picklist data as PDF format...', 0);
                  setLoading(true);
                  exportPicklistPdf(searchFormRef.current?.getFieldValue('picklist_id'))
                    .then((res) => {
                      hide();
                      if (res.url) {
                        window.open(`${API_URL}/api/${res.url}`, '_blank');
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                Summary
              </Button>,
              <Button
                type="primary"
                key="export-pdf2"
                size="small"
                icon={<FilePdfOutlined />}
                disabled={!selectedPicklist?.value}
                title="Download summary by Orders in PDF"
                onClick={() => {
                  const hide = message.loading('Downloading picklist data as PDF format...', 0);
                  setLoading(true);
                  exportPicklistPdf(searchFormRef.current?.getFieldValue('picklist_id'), { mode: 'groupByOrder' })
                    .then((res) => {
                      hide();
                      if (res.url) {
                        window.open(`${API_URL}/api/${res.url}`, '_blank');
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                Summary 2
              </Button>,
              <Button
                type="primary"
                key="export-delivery-note"
                size="small"
                icon={<FilePdfOutlined />}
                disabled={!selectedPicklist?.value}
                title="Download delivery notes in PDF"
                onClick={() => {
                  const hide = message.loading('Downloading delivery note as PDF format...', 0);
                  setLoading(true);
                  exportPicklistDeliveryNote(searchFormRef.current?.getFieldValue('picklist_id'))
                    .then((res) => {
                      hide();
                      if (isArray(res) && res.length) {
                        res.forEach((file) => {
                          if (file.url) {
                            window.open(`${API_URL}/api/${file.url}`, '_blank');
                          }
                        });
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                Delivery note
              </Button>,
              <Popconfirm
                key="book-popconfirm"
                title={
                  <>
                    Are you sure you want to book selected picklist?
                    <br />
                    <br />
                    Book warehouse stocks for orders in this picklist. <br />
                    Booking can be done partially.
                  </>
                }
                okText="Yes"
                cancelText="No"
                disabled={!selectedPicklist?.value || allBooked}
                onConfirm={() => {
                  const hide = message.loading('Booking picklist data...', 0);
                  setLoading(true);
                  bookPicklist(searchFormRef.current?.getFieldValue('picklist_id'), {})
                    .then((res) => {
                      hide();
                      if (res.isOk) {
                        message.success('Booked successfully.');
                        actionRef.current?.reload();
                      } else if (res.notEnoughSkus) {
                        message.warning('Out of stocks: ' + Object.keys(res.notEnoughSkus).join(', '));
                        setNoStockSKUList(
                          Object.keys(res.notEnoughSkus).map((sku) => ({
                            sku: sku,
                            qty: res.notEnoughSkus[sku],
                          })),
                        );
                        setOpenNoStockSKUModal(true);
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                <Button
                  type="primary"
                  key="book"
                  size="small"
                  className="btn-green"
                  title="Book order items."
                  icon={<PushpinOutlined />}
                  disabled={!selectedPicklist?.value || allBooked}
                >
                  Book
                </Button>
              </Popconfirm>,
              <Button
                type="default"
                key="export-csv-1"
                size="small"
                icon={<FileExcelOutlined />}
                disabled={!selectedPicklist?.value || loading}
                title="Export picklist in CSV."
                onClick={() => {
                  const hide = message.loading('Exporting picklist data in CSV format...', 0);
                  setLoading(true);
                  exportPicklistCsv(searchFormRef.current?.getFieldValue('picklist_id'))
                    .then((res) => {
                      hide();
                      if (res.url) {
                        window.location.href = `${API_URL}/api/${res.url}`;
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                Export
              </Button>,
              <ProFormUploadButton
                key="csv-imported"
                max={1}
                name="file"
                title="Import"
                accept=".csv,.xls,.xlsx"
                required
                icon={loadingUpload ? <LoadingOutlined /> : <UploadOutlined />}
                disabled={!selectedPicklist?.value || loadingUpload || loading}
                rules={[
                  {
                    required: true,
                    message: 'File is required',
                  },
                ]}
                buttonProps={{
                  size: 'small',
                  title:
                    "Import shipping data in CSV.\nIf any order in 'processing' and parcel number exist,\nshipments will be created in Magento. \nNote: Order will be complete once the shipment is created.",
                }}
                formItemProps={{ style: { marginBottom: 0 }, className: 'upload-btn-sm' }}
                action={`${API_URL}/api/warehouse/picklist/import-csv/${selectedPicklist?.value}`}
                fieldProps={{
                  headers: {
                    Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
                  },
                  itemRender: (originNode: React.ReactElement, file: UploadFile, fList, actions) => null,
                  beforeUpload: async (file, fList) => {
                    setLoadingUpload(true);
                    return Promise.resolve(file);
                  },
                  onChange: (info: UploadChangeParam, updateState = true) => {
                    if (info.file.status == 'done') {
                      const resData = info.file.response;
                      if (resData.code == 200) {
                      } else {
                        message.error('Failed to upload file!');
                      }
                      Util.flashSysMsg(resData);
                      setLoadingUpload(false);
                      // console.log(info.file.xhr.response);
                    } else if (info.file.status == 'error') {
                      Util.error(info.file?.response?.message ?? 'Failed to upload a file!');
                      setLoadingUpload(false);
                    }
                  },
                }}
              />,
              <Button
                type="primary"
                key="createMulti"
                size="small"
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  handleCreate({ mode: 'multi' });
                }}
              >
                Create “Multipacke”
              </Button>,
              <Button
                type="primary"
                key="createSingle"
                size="small"
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  handleCreate({ mode: 'single' });
                }}
              >
                Create “Single Unit”
              </Button>,
              <Button
                type="default"
                key="createBoth"
                size="small"
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  handleCreate({});
                }}
              >
                Create both
              </Button>,
              <Popconfirm
                key="delete"
                title={<>Are you sure you want to remove selected picklist?</>}
                okText="Yes"
                cancelText="No"
                disabled={!selectedPicklist?.value}
                onConfirm={() => {
                  if (!selectedPicklist?.value) return;
                  const hide = message.loading(`Deleting ...`, 0);
                  deletePicklist({
                    id: selectedPicklist?.value,
                  })
                    .then((res) => {
                      message.success('Successfully deleted!');
                      setPicklist((prev) => prev.filter((x) => x.id != selectedPicklist?.value));
                      setSelectedPicklist(undefined);
                      searchFormRef.current?.setFieldsValue({ picklist_id: undefined });
                      actionRef.current?.reload();
                      setExpandedRowKeys([]);
                    })
                    .catch((e) => {
                      message.error(e.message ?? 'Failed to delete!');
                    })
                    .finally(() => {
                      hide();
                    });
                }}
              >
                <Button
                  type="ghost"
                  key="delete"
                  size="small"
                  disabled={!selectedPicklist?.value}
                  icon={<DeleteOutlined />}
                >
                  Delete
                </Button>
              </Popconfirm>,

              <div key="break1" className="break" />,

              <Button
                type="primary"
                key="export-labels-GLS"
                size="small"
                icon={<PrinterOutlined />}
                title="Generate GLS Shipping Labels in PDF"
                onClick={() => {
                  handlePrintLabel('GLS');
                }}
              >
                GLS
              </Button>,

              <Button
                type="primary"
                key="export-labels-DHL"
                size="small"
                icon={<PrinterOutlined />}
                title="Generate Print DHL Shipping Labels in PDF"
                onClick={() => {
                  handlePrintLabel('DHL');
                }}
              >
                DHL
              </Button>,
              <Button
                type="primary"
                key="export-labels-DPD"
                size="small"
                icon={<PrinterOutlined />}
                title="Generate Print DPD Shipping Labels in PDF"
                onClick={() => {
                  handlePrintLabel('DPD');
                }}
              >
                DPD
              </Button>,
              <Button
                type="default"
                key="export-merged-labels"
                size="small"
                icon={<PrinterOutlined />}
                title="Generate merged labels in PDF"
                onClick={() => {
                  handlePrintLabelMerged();
                }}
              >
                Print Merged Labels
              </Button>,
              <Popover
                key="re-print-full-labels"
                trigger={['click', 'hover']}
                content={selectedPicklist?.label_files?.map((x: string) => {
                  const fileUrl = encodeURI(`${API_URL}/api/download?type=pdf&key=${x}`);
                  return (
                    <Row key={x} gutter={12} style={{ width: 180, alignItems: 'center' }}>
                      <Col className="text-sm" span={21}>
                        <Typography.Link ellipsis href={fileUrl} target="_blank" title="Open file on new tab">
                          {Util.sFileName(x)}
                        </Typography.Link>
                      </Col>
                      <Col span={3}>
                        <Button
                          type="link"
                          size="small"
                          icon={<PrinterOutlined />}
                          title="Print Label"
                          style={{ height: 20 }}
                          onClick={() => {
                            downloadFileB64({ type: 'pdf', b64: true, key: x || '' }).then((b64) => {
                              printJS({ printable: b64, type: 'pdf', base64: true, showModal: true });
                            });
                          }}
                        />
                      </Col>
                    </Row>
                  );
                })}
              >
                <Button
                  type="default"
                  size="small"
                  icon={<PrinterOutlined />}
                  title="Re-print Full"
                  disabled={!selectedPicklist?.label_files?.length}
                >
                  {`Print Existing Merged Label ${
                    selectedPicklist?.label_files?.length ? ` (${selectedPicklist?.label_files?.length})` : ''
                  }`}
                </Button>
              </Popover>,
              <Button
                type="primary"
                key="check-parcels-create-shipments"
                size="small"
                icon={<ImportOutlined />}
                title="Check Parcels & Create Shipments"
                style={{ marginLeft: 48 }}
                onClick={() => {
                  const hide = message.loading('Checking parcels status and creating shipments...');

                  checkParcelsAndCreateShipment(selectedPicklist?.id)
                    .then((res) => {
                      message.success('Successfully updated!');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                Check Parcels & Create Shipments
              </Button>,
            ]}
          </div>,
        ]}
        expandable={{
          expandedRowRender,
          expandRowByClick: false,
          defaultExpandAllRows: true,
          indentSize: 0,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            setExpandedRowKeys(expandedKeys as any);
          },
          showExpandColumn: true,
          columnWidth: 50,
          columnTitle: (
            <div style={{ paddingLeft: 16 }}>
              {expandedRowKeys.length == dataSource.length ? (
                <MinusOutlined
                  onClick={() => {
                    setExpandedRowKeys([]);
                  }}
                />
              ) : (
                <PlusOutlined
                  onClick={() => {
                    setExpandedRowKeys(dataSource.map((x) => x.entity_id as any));
                  }}
                />
              )}
            </div>
          ),
        }}
        onRow={(record) => {
          let cls = '',
            title = '';
          const warning_def = (record as API.Order).warnings_def || '';
          if (warning_def) {
            cls += ' reset-tds-bg bg-red';
            title = 'Delivery address warning: \n' + warning_def.replaceAll('^', '\n');
          }
          return { title: title, className: cls };
        }}
        pagination={{ defaultPageSize: 30, showSizeChanger: true }}
        rowSelection={false}
        columnEmptyText=""
      />

      <Modal
        title={`Order shipment comments`}
        width={600}
        open={openCommentsModal}
        onCancel={() => setOpenCommentsModal(false)}
        footer={false}
      >
        <OrderShipmentCommentsList orderId={sn(currentOrder?.entity_id)} />
      </Modal>

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentOrderItemRow?.item_ean?.id,
          item_id: currentOrderItemRow?.item_ean?.item_id,
          parent_id: currentOrderItemRow?.item_ean?.parent_id,
          is_single: currentOrderItemRow?.item_ean?.is_single,
          sku: currentOrderItemRow?.item_ean?.sku,
          ean: currentOrderItemRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentOrderItemRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentOrderItemRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentOrderItemRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <UpdateOrderExtraFormModal
        modalVisible={openUpdateOrderExtraModal}
        handleModalVisible={setOpenUpdateOrderExtraModal}
        order={{
          shipping_description: currentOrder?.shipping_description,
          weight: currentOrder?.weight,
          entity_id: currentOrder?.entity_id,
          shipping_imported_list: currentOrder?.shipping_imported_list,
          latest_shipping: currentOrder?.latest_shipping,
          sa_fullname: currentOrder?.sa_fullname,
          sa_full: currentOrder?.sa_full,
          sa_company: currentOrder?.sa_company,
        }}
        values={{ ...currentOrder?.extra }}
        onSubmit={async (values) => {
          actionRef.current?.reload();
        }}
      />

      <ToughStocksModal
        handleModalVisible={setOpenToughSSModal}
        modalVisible={openToughSSModal}
        tmpFile={tmpPrePdfFile}
        stocks={toughSSList}
        successCb={() => {
          actionRef.current?.reload();
        }}
      />
      {selectedPicklist && noStockSKUList && (
        <NoStockSkuModal
          handleModalVisible={setOpenNoStockSKUModal}
          modalVisible={openNoStockSKUModal}
          picklistId={selectedPicklist?.id}
          skus={noStockSKUList}
          skipPdfDownload
          successCb={() => {
            actionRef.current?.reload();
          }}
        />
      )}

      {orderParcelRow && (
        <OrderParcelLogListModal
          initialValue={orderParcelRow}
          modalVisible={openOrderParcelLog}
          handleModalVisible={setOpenOrderParcelLog}
          cbReloadParent={() => actionRef.current?.reload()}
        />
      )}

      {/* <PicklistTimeTrackingModal
        handleModalVisible={setOpenTimeTrackingModal}
        modalVisible={openTimeTrackingModal}
        initialValues={selectedPicklist as API.WarehousePicklist}
      /> */}
    </PageContainer>
  );
};

export default PicklistDetail;
