/* eslint-disable */
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';

const urlPrefix = '/api/item/category';

/** get GET /api/item/category */
export async function getCategoryList(params: API.PageParams, sort: any, filter: any) {
  const newSorter = {};
  if (sort) {
    Object.keys(sort).forEach((k) => {
      switch (k) {
        case 'supplier_name':
          newSorter['s.name'] = sort[k];
          break;
        default:
          newSorter[`a.${k}`] = sort[k];
          break;
      }
    });
  }
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(newSorter),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message,
    success: res.status == 'success',
  }));
}

/** put PUT /api/item/category */
export async function updateCategory(data: API.Category, options?: { [key: string]: any }) {
  return request<API.Category>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/item/category */
export async function addCategory(data: API.Category, options?: { [key: string]: any }) {
  return request<API.Category>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/item/category */
export async function deleteCategory(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/item/ac-list
 *
 * get the autocomplet lists.
 *
 */
export async function getCategoryACList(params: { [key: string]: string }, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      sort_detail: JSON.stringify(sort ?? { name: 'ascend' }),
    },
    withToken: true,
  }).then((res) => res.message.map((x: API.Category) => ({ ...x, value: x.name, label: `${x.name}` })));
}

/** post POST /api/item/create-with-ean */
export async function addCategoryWithEan(data: API.Category, options?: { [key: string]: any }) {
  return request<API.Category>(`/api/item/create-with-ean`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}
