ALTER TABLE `xmag_order`
    ADD KEY `IDX_xmag_order_created_at` (`created_at`);


CREATE TABLE `item_stats`
(
    `item_id`                                  bigint(20) unsigned NOT NULL,
    `last365_sales_qty`                        int(11)        DEFAULT NULL,
    `last365_cturover`                         decimal(20, 4) DEFAULT NULL,
    `last30_sales_qty`                         int(11)        DEFAULT NULL,
    `last30_cturover`                          decimal(20, 4) DEFAULT NULL,
    `gp_single_gp_avg_365`                     decimal(20, 4) DEFAULT NULL,
    `gp_single_qty_ordered_sum_365`            int(11)        DEFAULT NULL,
    `gp_single_gp_avg_30`                      decimal(20, 4) DEFAULT NULL,
    `gp_single_qty_ordered_sum_30`             int(11)        DEFAULT NULL,
    `gp_single_cturnover_30`                   decimal(20, 4) DEFAULT NULL,
    `gp_single_cturnover_365`                  decimal(20, 4) DEFAULT NULL,
    `gp_multi_gp_avg_365`                      decimal(20, 4) DEFAULT NULL,
    `gp_multi_qty_ordered_sum_365`             int(11)        DEFAULT NULL,
    `gp_multi_gp_avg_30`                       decimal(20, 4) DEFAULT NULL,
    `gp_multi_qty_ordered_sum_30`              int(11)        DEFAULT NULL,
    `gp_multi_cturnover_30`                    decimal(20, 4) DEFAULT NULL,
    `gp_multi_cturnover_365`                   decimal(20, 4) DEFAULT NULL,
    `gp_sum_30`                                decimal(20, 4) DEFAULT NULL,
    `order_count_30`                           int(11)        DEFAULT NULL,
    `ggp_avg_30`                               decimal(20, 4) DEFAULT NULL,
    `gp_sum_365`                               decimal(20, 4) DEFAULT NULL,
    `order_count_365`                          int(11)        DEFAULT NULL,
    `ggp_avg_365`                              decimal(20, 4) DEFAULT NULL,
    `gogp_order_count_30`                      int(11)        DEFAULT NULL,
    `gogp_order_count_365`                     int(11)        DEFAULT NULL,
    `gogp_sum_30`                              decimal(20, 4) DEFAULT NULL,
    `gogp_sum_365`                             decimal(20, 4) DEFAULT NULL,
    `gogp_avg_30`                              decimal(20, 4) DEFAULT NULL,
    `gogp_avg_365`                             decimal(20, 4) DEFAULT NULL,
    `gogp_order_ids`                           longtext       DEFAULT NULL,
    `stockRelated1`                            decimal(20, 4) DEFAULT NULL,
    `stockRelated2`                            decimal(20, 4) DEFAULT NULL,
    `parent_stock_stables_sum_total_piece_qty` int(11)        DEFAULT NULL,
    `updated_on`                               datetime       DEFAULT NULL,
    PRIMARY KEY (`item_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

