import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useMemo } from 'react';
import React, { useRef } from 'react';
import { Modal } from 'antd';
import Util from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
// import { CloseOutlined } from '@ant-design/icons';

import { getOrderParcelLogList } from '@/services/foodstore-one/Magento/order-parcel-log';

type ModalInitialValueType = API.OrderParcel;

export type OrderParcelLogListModalProps = {
  initialValue: ModalInitialValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  cbReloadParent?: () => void;
};

type ColumnDataType = API.OrderParcelLog;

const OrderParcelLogListModal: React.FC<OrderParcelLogListModalProps> = (props) => {
  const { initialValue, modalVisible, handleModalVisible, cbReloadParent } = props;

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<ColumnDataType>[] = useMemo(() => {
    const newColumns: ProColumns<ColumnDataType>[] = [
      {
        title: 'Provider',
        dataIndex: ['parcel', 'service_name'],
        align: 'center',
        width: 100,
      },
      {
        title: 'Parcel No',
        dataIndex: ['parcel', 'parcel_no'],
        align: 'center',
        width: 130,
        copyable: true,
      },
      {
        title: 'Date',
        dataIndex: ['shipping_updated_on'],
        defaultSortOrder: 'ascend',
        sorter: true,
        align: 'center',
        width: 110,
        render: (__, record) => Util.dtToDMYHHMMTz(record.shipping_updated_on),
      },
      {
        title: 'Status',
        dataIndex: ['status'],
        align: 'center',
        width: 150,
        render: (__, record) => record.status,
      },

      {
        title: 'Description',
        dataIndex: ['note'],
        align: 'center',
        width: 150,
        render: (__, record) => record.note,
      },

      {
        title: 'Detail',
        dataIndex: ['detail'],
        render: (__, record) => (record.detail ? JSON.stringify(record.detail) : null),
      },
    ];
    return newColumns;
  }, []);

  useEffect(() => {
    if (modalVisible) {
      actionRef.current?.reload();
    }
  }, [modalVisible]);

  return (
    <>
      <Modal
        title={<>Parcel Tracking Logs - {initialValue.ref_no}</>}
        width="1400px"
        open={modalVisible}
        onCancel={() => handleModalVisible(false)}
        bodyStyle={{ paddingTop: 0 }}
        footer={false}
      >
        <ProTable<ColumnDataType, API.PageParams>
          actionRef={actionRef}
          rowKey="id"
          revalidateOnFocus={false}
          options={{ fullScreen: false, setting: false, density: false, reload: true }}
          sticky
          scroll={{ x: 800 }}
          size="small"
          bordered
          cardProps={{ bodyStyle: { padding: 0 } }}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: 20,
          }}
          search={false}
          request={async (params, sort, filter) => {
            return getOrderParcelLogList(
              { ...params, order_id: initialValue.order_id, with: 'parcel' },
              { ...sort },
              filter,
            ).finally(() => {});
          }}
          onRequestError={Util.error}
          columns={columns}
        />
      </Modal>
    </>
  );
};

export default OrderParcelLogListModal;
