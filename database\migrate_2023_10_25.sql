CREATE TABLE `shipping_warning`
(
    `id`     int(11)    NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `name`   varchar(255)        DEFAULT NULL,
    `street` varchar(255)        DEFAULT NULL,
    `zip`    varchar(31)         DEFAULT NULL,
    `email`  varchar(255)        DEFAULT NULL,
    `is_and` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Is AND operator?',
    `reason` varchar(255)        DEFAULT NULL COMMENT 'Reason text of this definition',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='Define warning contact & addresses. Used to check fake shipping address';