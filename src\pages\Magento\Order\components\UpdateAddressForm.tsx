import React, { useEffect } from 'react';
import { Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDependency, ProFormSelect } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { getCountriesDEOptions } from '@/services/foodstore-one/countries';
import { isOrderAddressEditable } from '..';

export type AddressFormValueType = Partial<API.OrderAddress>;

/* const handleUpdate = async (entity_id: number, fields: AddressFormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await usOrderAddressUpdate(entity_id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
}; */

export type UpdateAddressFormProps = {
  orderEntityId: number;
  orderStatus: string;
  initialValues?: AddressFormValueType;
  onSubmit?: (formData: API.OrderAddress) => Promise<boolean | void>;
  formRef: React.MutableRefObject<ProFormInstance<AddressFormValueType> | undefined>;
};

const UpdateAddressForm: React.FC<UpdateAddressFormProps> = (props) => {
  const { formRef } = props;
  // const formRef = useRef<ProFormInstance<AddressFormValueType>>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  const isReadOnly = !isOrderAddressEditable(props.orderStatus) || !props.initialValues?.entity_id;

  return (
    <ProForm
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      size="small"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      /* onFinish={async (value) => {
        if (isReadOnly) {
          message.info(`You cannot update ${props.initialValues?.address_type} address.`);
          return;
        }

        const success = await handleUpdate(sn(props.orderEntityId), {
          ...props.initialValues,
          ...value,
        });

        if (success) {
          if (props.onSubmit) props.onSubmit(value);
        }
      }} */
      submitter={false}
      /* submitter={{
        render: (p, dom) => {
          return (
            <Button
              type="primary"
              size="small"
              style={{ float: 'right' }}
              onClick={() => {
                p.submit();
              }}
              icon={<UploadOutlined />}
              disabled={isReadOnly}
            >
              Save & UpSync
            </Button>
          );
        },
      }} */
      readonly={isReadOnly}
    >
      <ProFormText
        width="md"
        name="company"
        label="Company"
        placeholder="Company"
        addonAfter={
          <ProFormDependency name={['company']}>
            {(depValues) => {
              return (
                <Typography.Text className="text-sm" copyable={{ text: depValues.company }} style={{ marginLeft: 0 }}>
                  {''}
                </Typography.Text>
              );
            }}
          </ProFormDependency>
        }
        fieldProps={{ maxLength: 40 }}
      />
      <ProFormText
        width="sm"
        name="firstname"
        label="First Name"
        placeholder="First Name"
        addonAfter={
          <ProFormDependency name={['firstname']}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.firstname }} />;
            }}
          </ProFormDependency>
        }
      />
      <ProFormText
        width="sm"
        name="lastname"
        label="Last Name"
        placeholder="Last Name"
        addonAfter={
          <ProFormDependency name={['lastname']}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.lastname }} />;
            }}
          </ProFormDependency>
        }
      />

      <ProFormText
        width="md"
        name={['street', 0]}
        label="Street 1"
        placeholder="Street 1"
        addonAfter={
          <ProFormDependency name={[['street', 0]]}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.street[0] }} />;
            }}
          </ProFormDependency>
        }
      />
      <ProFormText
        width="md"
        name={['street', 1]}
        label="Street 2"
        placeholder="Street 2"
        addonAfter={
          <ProFormDependency name={[['street', 1]]}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.street[1] }} />;
            }}
          </ProFormDependency>
        }
      />
      <ProFormText
        width="xs"
        name="postcode"
        label="Zip"
        placeholder="Zip"
        addonAfter={
          <ProFormDependency name={['postcode']}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.postcode }} />;
            }}
          </ProFormDependency>
        }
      />
      <ProFormText
        width="sm"
        name="city"
        label="City"
        placeholder="City"
        addonAfter={
          <ProFormDependency name={['city']}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.city }} />;
            }}
          </ProFormDependency>
        }
      />
      <ProFormSelect
        name={['country_id']}
        label="Country"
        showSearch
        width="sm"
        options={getCountriesDEOptions()}
        initialValue={'DE'}
      />
      <ProFormText
        width="md"
        name="email"
        label="Email"
        placeholder="Email"
        addonAfter={
          <ProFormDependency name={['email']}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.email }} />;
            }}
          </ProFormDependency>
        }
      />
      <ProFormText
        width="sm"
        name="telephone"
        label="Telephone"
        placeholder="Telephone"
        addonAfter={
          <ProFormDependency name={['telephone']}>
            {(depValues) => {
              return <Typography.Text className="text-sm" copyable={{ text: depValues.telephone }} />;
            }}
          </ProFormDependency>
        }
      />
    </ProForm>
  );
};

export default UpdateAddressForm;
