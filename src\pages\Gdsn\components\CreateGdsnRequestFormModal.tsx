import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { createGdsnSubscription, getGdsnMessageProviderList } from '@/services/foodstore-one/Item/ean';
import { message } from 'antd';
import Util, { ni, sn } from '@/util';
import { debounce } from 'lodash';
import { LoadingOutlined } from '@ant-design/icons';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';

export type FormValueType = Record<string, any>;

export type CreateGdsnRequestFormModalProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.GdsnMessage) => Promise<boolean | void>;
};

const CreateGdsnRequestFormModal: React.FC<CreateGdsnRequestFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { trademarks } = useTrademarkFormFilter();

  const { modalVisible, handleModalVisible, onSubmit } = props;

  const [loadingSearch, setLoadingSearch] = useState<boolean>(false);
  const [provider, setProvider] = useState<API.GdsnMessageProvider>();

  const handleSearch = async (v: string) => {
    if (!v) {
      return;
    }
    message.destroy();
    setLoadingSearch(true);
    await getGdsnMessageProviderList(
      {
        perPage: 1,
        page: 1,
        providerMode: 1,
        provider_gln: v,
        with: 'gdsnMessage.file_url,gdsnProvider',
      },
      {},
      {},
    )
      .then((res) => {
        const resProvider = res.data[0];
        setProvider(resProvider);
      })
      .catch(Util.error)
      .finally(() => setLoadingSearch(false));
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearch = useCallback(
    debounce((newValue) => handleSearch(newValue), 330),
    [],
  );

  useEffect(() => {
    if (provider?.id) {
      formRef.current?.setFieldValue('provider', {
        name: provider.gdsn_provider?.name || '',
        trademark_ids: provider.gdsn_provider?.trademarks?.map((x) => sn(x.id)),
        notes: provider.gdsn_provider?.notes || '',
      });
    } else {
      formRef.current?.setFieldValue('provider', {
        name: '',
        trademark_ids: [],
        notes: '',
      });
    }
  }, [provider]);

  return (
    <ModalForm
      title={'New GDSN Request'}
      width="600px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      labelAlign="left"
      formRef={formRef}
      modalProps={{ closable: false }}
      onFinish={async (value) => {
        const hide = message.loading('Creating GDSN subscription...', 0);
        await createGdsnSubscription(value)
          .then((res) => {
            message.success('Created successfully');
            if (formRef.current) formRef.current.resetFields();
            onSubmit?.(res);
          })
          .catch(Util.error)
          .finally(hide);
      }}
      submitter={{ submitButtonProps: { disabled: loadingSearch } }}
    >
      <ProFormText
        width="md"
        name="provider_gln"
        label="Provider"
        fieldProps={{
          onChange(e) {
            debouncedHandleSearch(e.target.value);
          },
        }}
        addonAfter={loadingSearch ? <LoadingOutlined /> : null}
        help={
          provider?.id ? (
            <div style={{ marginBottom: 8 }} className="text-sm">
              <div>Name: {provider?.provider_name || 'N/A'}</div>
              <div>
                Requested Count: {ni(provider?.requested_count)}, EANs (GDSN) Count: {ni(provider?.gtin_count || 0)},
                Latest Date: {Util.dtToDMYHHMM(provider?.last_requested_date)}
              </div>
            </div>
          ) : null
        }
      />

      <ProFormText width="xs" name="target_market_country_code" label="Target Market Code" initialValue={276} />

      {!provider?.id ? (
        <>
          <ProFormText width="lg" name={['provider', 'name']} label="Provider Name (Sys)" />
          <ProFormSelect
            name={['provider', 'trademark_ids']}
            label="Trademarks (Sys)"
            options={trademarks}
            mode="multiple"
          />
          <ProFormTextArea width="lg" name={['provider', 'notes']} label="Provider Notes (Sys)" />
        </>
      ) : null}
    </ModalForm>
  );
};

export default CreateGdsnRequestFormModal;
