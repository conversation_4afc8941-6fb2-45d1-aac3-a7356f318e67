/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/item/item-supplier';

/** get GET /api/item/item-supplier */
export async function getItemSupplierList(params: API.ItemSupplierPageParams, sort: any, filter: any) {
  const newSorter = {};
  if (sort) {
    Object.keys(sort).forEach((k) => {
      switch (k) {
        case 'supplier_name':
          newSorter['s.name'] = sort[k];
          break;
        default:
          newSorter[`a.${k}`] = sort[k];
          break;
      }
    });
  }
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      with: 'supplier',
      sort_detail: JSON.stringify(newSorter),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/item/item-supplier */
export async function updateItemSupplier(data: API.ItemSupplier, options?: { [key: string]: any }) {
  return request<API.ItemSupplier>(`${urlPrefix}/` + data.key, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/item/item-supplier */
export async function addItemSupplier(data: API.ItemSupplier, options?: { [key: string]: any }) {
  return request<API.ItemSupplier>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/item/item-supplier */
export async function deleteItemSupplier(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
