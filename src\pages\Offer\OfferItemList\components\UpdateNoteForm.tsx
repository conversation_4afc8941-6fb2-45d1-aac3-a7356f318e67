import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { message, Space, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import { useModel } from 'umi';
import { DictType } from '@/constants';
import SkuComp from '@/components/SkuComp';
import { updateOfferItem } from '@/services/foodstore-one/Offer/offer-item';

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOfferItem(id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.OfferItem>;

export type UpdateNoteFormProps = {
  initialValues?: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItem) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;

  buyingHistoryComp?: JSX.Element;
};

const UpdateNoteForm: React.FC<UpdateNoteFormProps> = (props) => {
  const { initialValues } = props;
  const { item_ean } = initialValues ?? {};

  const { getDictOptions } = useModel('app-settings');
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, initialValues]);

  return (
    <ModalForm<FormValueType>
      title={
        <Space size={16}>
          <span>Update Note of Offer Item</span>
          <SkuComp sku={item_ean?.sku} />
          <Typography.Text copyable>{item_ean?.ean}</Typography.Text>
        </Space>
      }
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      initialValues={initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      modalProps={{ okText: 'Update' }}
      onFinish={async (value) => {
        const success = await handleUpdate(sn(initialValues?.id), { ...value });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormTextArea
        name="note"
        label={<span>Notes</span>}
        fieldProps={{ rows: 7 }}
        formItemProps={{ style: { marginBottom: 0 } }}
      />
    </ModalForm>
  );
};

export default UpdateNoteForm;
