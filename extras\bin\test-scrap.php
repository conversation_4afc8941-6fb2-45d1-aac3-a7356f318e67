<?php

use \App\Service\Scrap\ScrapPriceService;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

$ean = '5034709003685';
$ean = '4017100219900';

/** @var \Psr\Container\ContainerInterface $container */
/** @var \App\Service\Scrap\ScrapPriceService $service */
$service = $container->get(ScrapPriceService::class);

print_r($service->getPriceByEan($ean));

/*$url = 'https://www.worldofsweets.de/index.php?lang=0&queryFromSuggest=&userInput=&listorderby=relevance&listorder=desc&cl=fcfatsearch_productlist&searchparam=' . $ean;

/*
$client = new Client();
$crawler = $client->request('GET', $url);
$html = $crawler->html();
file_put_contents(APP_PATH . DS . 'test.html', $html);
* /


$html = file_get_contents(APP_PATH . DS . 'test.html');
/** @var Crawler $crawler * /
$crawler = new Crawler($html);

$doms = $crawler->filter('div.product-list > .product')->slice(0, 5);
if ($doms->count() < 3) {
    /* * @var Crawler $node * /
    $node = $doms->first();

    $item = [];
    try {
        $titleObj = $node->filter('div.product-title a')->first();
        $item['title'] = $titleObj?->text();
        $item['link'] = $titleObj?->link()?->getUri();

        $productCont = $node->filter('div.product-price-container')?->first();
        if ($productCont) {
            $item['price'] = $productCont?->filter('.product-prices .product-price.is--new')?->first()?->text();
            $item['priceUnit'] = $productCont?->filter('.product-price.is--unit')?->first()?->text();

            $bulkCont = $productCont->filter('.product-price-bulk')?->first();
            if ($bulkCont) {
                $item['priceBulk'] = $bulkCont?->filter('.product-price.is--new')?->first()?->text();
                $item['priceBulkDesc'] = $bulkCont?->filter('.product-price.is--bulk-unit')?->first()?->text();
            }
        }
    } catch (Exception $exception) {

    }

    print_r($item);
}*/
