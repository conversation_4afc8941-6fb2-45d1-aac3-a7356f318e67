SELECT (SELECT GROUP_CONCAT(w.reason SEPARATOR '^')
        FROM shipping_warning w
        WHERE IF(w.is_and,
                 IF(IFNULL(w.zip, '') = '', 1, INSTR(xmag_order.`sa_zip`, w.zip))
                     AND IF(IFNULL(w.street, '') = '', 1, INSTR(xmag_order.`sa_street`, w.street))
                     AND IF(IFNULL(w.email, '') = '', 1, INSTR(xmag_order.`sa_email`, w.email))
                     AND IF(IFNULL(w.name, '') = '', 1, INSTR(REPLACE(CONCAT(xmag_order.`sa_firstname`, xmag_order.`sa_lastname`), ' ', ''), replace(w.name, ' ', '')))
                  ,
                 IF(IFNULL(w.zip, '') = '', 0, INSTR(xmag_order.`sa_zip`, w.zip))
                     OR IF(IFNULL(w.street, '') = '', 0, INSTR(xmag_order.`sa_street`, w.street))
                     OR IF(IFNULL(w.email, '') = '', 0, INSTR(xmag_order.`sa_email`, w.email))
                     OR
                 IF(IFNULL(w.name, '') = '', 0,
                    INSTR(REPLACE(CONCAT(xmag_order.`sa_firstname`, xmag_order.`sa_lastname`), ' ', ''),
                          replace(w.name, ' ', '')))
                  )) AS warnings_def
     , xmag_order.sa_firstname
     , xmag_order.sa_lastname
     , xmag_order.sa_street
     , xmag_order.sa_zip
FROM `xmag_order`
WHERE entity_id >= 5102;