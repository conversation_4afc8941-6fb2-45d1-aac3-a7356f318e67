ALTER TABLE `offer`
    ADD COLUMN `percentage` DECIMAL(10, 2) default 90 AFTER `recv_note`;

delete
from sys_dict
where code = 'OFFER_DEFAULT_PERCENTAGE';

INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`, `desc`)
VALUES ('OFFER_DEFAULT_PERCENTAGE', 'Offer', 'Default Percentage', '90',
        'Default Price percentage.');

CREATE TABLE `customer_price`
(
    `customer_name` VARCHAR(255) NOT NULL COMMENT 'PK: Customer name',
    `sku`           VARCHAR(255) NOT NULL COMMENT 'PK: SKU in FsOne',
    `customer_sku`  VARCHAR(255) COMMENT 'SKU in Customer',
    `price`         DECIMAL(20, 4),
    `start_date`    DATE COMMENT 'Valid from Date',
    `end_date`      DATE COMMENT 'Valid to Date',
    PRIMARY KEY (`customer_name`, `sku`)
);