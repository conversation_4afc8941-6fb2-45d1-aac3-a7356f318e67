import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { message } from 'antd';
import Util from '@/util';
import { getSupplierAddACList } from '@/services/foodstore-one/Import/import';
import SDatePicker from '@/components/SDatePicker';
import { addImportEanDisabled } from '@/services/foodstore-one/Import/import-ean-disabled';
import moment from 'moment';
import { DT_FORMAT_YMD } from '@/constants';

const handleAdd = async (fields: API.ImportEanDisabled) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addImportEanDisabled(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.ImportEanDisabled>;

export type CreateFormProps = {
  initialValues?: Partial<API.ImportEanDisabled>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.ImportEanDisabled) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, initialValues } = props;

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldsValue({
        ...initialValues,
        start_date: moment().format(DT_FORMAT_YMD),
        end_date: moment().add(21, 'days').format(DT_FORMAT_YMD),
      });
    }
  }, [modalVisible, initialValues]);

  return (
    <ModalForm<FormValueType>
      title={'Create Disabled EAN'}
      width="600px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      initialValues={initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      labelCol={{ span: 6 }}
      modalProps={{ okText: 'Create' }}
      onFinish={async (value) => {
        const success = await handleAdd({ ...value });

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProFormSelect request={getSupplierAddACList} name="supplier_add" label="SUPPLIER_ADD" showSearch />
      <ProFormText name="ean" label="EAN" />
      <SDatePicker name="start_date" label="Start Date" />
      <SDatePicker name="end_date" label="End Date" />
    </ModalForm>
  );
};

export default CreateForm;
