import React, { useCallback, useRef, useState } from 'react';
import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { Menu, Spin, message } from 'antd';
import { history, useModel } from 'umi';
import { stringify } from 'querystring';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import { outLogin } from '@/services/foodstore-one/login';
import type { MenuInfo } from 'rc-menu/lib/interface';
import { FormattedMessage } from 'umi';
import SAvatar from '../SAvatar';
import ChangePassword from '@/pages/UsersList/components/ChangePassword';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { changeUserPassword } from '@/services/foodstore-one/user';
import Util from '@/util';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

/**
 * Logout now.
 */
export const loginOut = async () => {
  try {
    await outLogin();
  } catch {}
  const { query = {}, search, pathname } = history.location;
  const { redirect } = query;

  // Note: There may be security issues, please note
  if (window.location.pathname !== '/user/login' && !redirect) {
    history.replace({
      pathname: '/user/login',
      search: stringify({
        redirect: pathname + search,
      }),
    });
  }
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const { initialState, setInitialState } = useModel('@@initialState');

  // Change password functionalities
  const updateFormRef = useRef<ProFormInstance>();
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        setInitialState((s) => ({ ...s, currentUser: undefined }));
        loginOut();
        return;
      } else if (key === 'change-password') {
        handleUpdateModalVisible(true);
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
      {menu && (
        <Menu.Item key="center">
          <UserOutlined />
          <FormattedMessage id="menu.account.center" />
        </Menu.Item>
      )}
      {menu && (
        <Menu.Item key="settings">
          <SettingOutlined />
          <FormattedMessage id="menu.account.settings" />
        </Menu.Item>
      )}
      {menu && <Menu.Divider />}

      <Menu.Item key="change-password">Change password</Menu.Item>

      <Menu.Item key="logout">
        <LogoutOutlined />
        <FormattedMessage id="menu.account.logout" />
      </Menu.Item>
    </Menu>
  );
  return (
    <>
      <HeaderDropdown overlay={menuHeaderDropdown}>
        <span className={`${styles.action} ${styles.account}`}>
          <SAvatar
            size="small"
            className={styles.avatar}
            src={currentUser.avatar}
            text={currentUser.initials ?? ''}
            alt="avatar"
          />
          <span className={`${styles.name} anticon`}>{currentUser.name}</span>
        </span>
      </HeaderDropdown>
      <ModalForm
        title={'Change password'}
        width="500px"
        visible={updateModalVisible}
        onVisibleChange={handleUpdateModalVisible}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
        initialValues={currentUser}
        formRef={updateFormRef}
        onFinish={async (value) => {
          if (!currentUser?.user_id) return;
          const hide = message.loading('Changing password...', 0);

          const fields = { ...value, user_id: currentUser?.user_id };
          try {
            await changeUserPassword(fields);
            message.success('Password has been changed successfully.');
            handleUpdateModalVisible(false);
          } catch (error) {
            Util.error(error);
          } finally {
            hide();
          }
        }}
      >
        <ChangePassword values={currentUser} />
      </ModalForm>
    </>
  );
};

export default AvatarDropdown;
