import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useMemo } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import type { InputRef } from 'antd';
import { Divider } from 'antd';
import { Button, Col, Input, message, Row, Space, Typography, Image } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormItem } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormField, ProFormGroup, ProFormList, ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { EanGPInfoType1, getEanList, getLatestIbos, updateEanAttribute } from '@/services/foodstore-one/Item/ean';
import Util, { nf2, ni, sn } from '@/util';
import _ from 'lodash';
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit from '@/components/SProFormDigit';
import SocialLinks from './SocialIcons';
import { DeleteOutlined, InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useModel } from 'umi';
import { deleteScrappedPrice, scrapWoSPrice } from '@/services/foodstore-one/Scrap/scrap-price';
import { DictType, EURO, ScrapSystemIds } from '@/constants';
import type { HandleNavFuncType } from '../hooks/useModalNavigation';
import ModalNavigation from './ModalNavigation';
import { addDict, getDictList } from '@/services/foodstore-one/Sys/sys-dict';
import GdsnItemButton from './GdsnItemButton';
import useGdsnItem from '../hooks/useGdsnItem';
import useUvpPrice from '../hooks/useUvpPrice';
import useUpdateModalActions from '../hooks/useUpdateModalActions';
import UpdatePriceAttributePartialForm from './UpdatePriceAttributePartialForm';
import PriceChangeHistoryList from './PriceChangeHistoryList';
import LatestIboListSummary from './LatestIboListSummary';
import EanSaleStatsMiniTable from './EanSaleStatsMiniTable';
import LatestIboList from './LatestIboList';
import ProCard from '@ant-design/pro-card';

const handleUpdate = async (fieldsParam: FormValueType) => {
  const fields = { ...fieldsParam };

  const hide = message.loading('Updating...', 0);
  try {
    const res = await updateEanAttribute(fields);
    hide();
    message.success('Updated successfully.');
    if (res.upSyncMessage) {
      message.error('Up sync error: ' + res.upSyncMessage);
    }
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!');
    return false;
  }
};

export type FormValueType = Partial<API.Ean>;

export const getSmallItemBaseText = (depValues: FormValueType) => {
  let smallUnit = '';
  const item_base_unit = depValues.item_base_unit ?? 'kg';
  if (item_base_unit == 'kg') {
    smallUnit = 'g';
  } else if (item_base_unit == 'l') {
    smallUnit = 'ml';
  }
  return smallUnit && depValues.item_base ? (
    <>
      <span className={(depValues?.item_base ?? 0) >= 1 ? 'red' : ''} style={{ paddingLeft: 5 }}>
        {' '}
        = {Util.numberFormat((depValues?.item_base || 0) * 1000, true, 5, true) + ' ' + smallUnit}
      </span>
    </>
  ) : (
    <></>
  );
};

export type UpdatePriceAttributeFormProps = {
  initialValues?: Partial<API.Ean & EanGPInfoType1>;
  modalVisible: boolean;
  //refetchMode?: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  reloadList?: (updatedRow: Partial<API.Ean>) => Promise<boolean | void>;
  handleNavigation?: HandleNavFuncType;
  gdsn?: boolean;
};

const UpdatePriceAttributeForm: React.FC<UpdatePriceAttributeFormProps> = (props) => {
  const [loading, setLoading] = useState(false);

  const itemEanProp = props.initialValues;

  const caseQty = sn(itemEanProp?.attr_case_qty || 1);
  const vatPercent = sn(itemEanProp?.item?.vat?.value);

  const { appSettings, loadAppSetting, getDictOptions } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const priceActionRef = useRef<FormListActionType>();

  // Ref object for Title prefix dropdown.
  const titlePrefixRef = useRef<InputRef>(null);
  const [newTitlePrefix, setNewTitlePrefix] = useState<string>('');

  // price related
  const priceTypes = appSettings.priceTypes;
  const titlePrefixOptions = useMemo(() => getDictOptions(DictType.TitlePrefix, 'label'), [getDictOptions]);

  // Refresh price change history component
  const [refreshTickHistory, setRefreshTickHistory] = useState(0);

  useEffect(() => {
    if (props.modalVisible) setRefreshTickHistory((prev) => prev + 1);
  }, [props.modalVisible]);

  useEffect(() => {
    if (formRef.current && props.modalVisible) {
      const newFormValues = { ...(itemEanProp || {}) };
      if (!newFormValues.item_base_unit) newFormValues.item_base_unit = 'kg';
      if (newFormValues.ean_prices) {
        for (const p of newFormValues.ean_prices) {
          const prices = Util.fPrices(p.price, itemEanProp?.item?.vat?.value);
          (p as any).price_gross = prices[1];
          if (p.price_type_id == 1 && !newFormValues.fs_ebay_price_std && p.price) {
            newFormValues.fs_ebay_price_std = sn(p.price) * 1.15;
          }
        }
      }

      formRef.current?.resetFields();
      formRef.current.setFieldsValue(newFormValues);
    }
  }, [itemEanProp, props.modalVisible]);

  const latestIboPrices = Util.fPrices(itemEanProp?.latest_ibo?.price, itemEanProp?.item?.vat?.value);

  // Get Single EAN's price
  const isSingle = itemEanProp?.is_single;

  const onTitlePrefixAddClick = async (value?: string, type?: string) => {
    if (!value) {
      message.error('Please fill new name.');
    }
    titlePrefixRef.current?.input?.setAttribute('disabled', 'true');
    addDict({
      code: value,
      value: value,
      type,
    })
      .then((res) => {
        console.log(res);
        formRef.current?.setFieldValue('fs_special_badge2', res.code);
        loadAppSetting();
        setNewTitlePrefix('');
      })
      .catch((err) => Util.error(err, 5));
  };

  /**
   * Called on change of discount and gross price
   */
  const updateEbaySpecialPrice = () => {
    const formValues = formRef.current?.getFieldsValue();
    const discount = sn((formValues.fs_special_discount || '').slice(0, -1)) ?? 0;

    const prices = Util.fPrices(
      sn(formValues.ean_prices.find((x: API.EanPrice) => x.price_type_id == 1)?.price) * 1.15 * (1 + discount / 100),
      itemEanProp?.item?.vat?.value,
    );
    formRef.current?.setFieldValue('fs_ebay_price_special', prices[1]);
  };

  // ------------------------------------------------------------ //
  // GDSN data
  // ------------------------------------------------------------ //
  const {
    gdsnItem,
    fetchGdsnItem,
    renderTakeButton,
    loading: loadingGdsn,
  } = useGdsnItem(itemEanProp?.ean, props.modalVisible);

  const [gdsnItemsDiff, setGdsnItemsDiff] = useState<any>({});
  const validateGdsnItemsDiff = useCallback(() => {
    const values = formRef.current?.getFieldsValue();
    setGdsnItemsDiff({
      item_base: values.item_base == gdsnItem?.item_base,
      item_base_unit: values.item_base_unit == gdsnItem?.item_base_unit,
    });
  }, [gdsnItem?.item_base, gdsnItem?.item_base_unit]);

  useEffect(() => {
    if (props.modalVisible && itemEanProp?.ean) {
      validateGdsnItemsDiff();
    }
  }, [itemEanProp?.ean, props.modalVisible, validateGdsnItemsDiff]);

  // ------------------------------------------------------------ //
  // UVP price
  // ------------------------------------------------------------ //
  const { /* fetchUvpData, loading: loadingUvp,  */ uvpData } = useUvpPrice(itemEanProp?.ean, props.modalVisible);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback } = useUpdateModalActions(
    itemEanProp?.id ?? 0,
    itemEanProp?.sku ?? '',
    formRef.current,
  );

  // ------------------------------------------------------------ //
  // Latest 3 IBOs
  // ------------------------------------------------------------ //
  const [ibos, setIbos] = useState<API.Ibo[]>([]);
  useEffect(() => {
    if (props.modalVisible && itemEanProp?.ean) {
      getLatestIbos({ ean: itemEanProp.ean })
        .then((res) => setIbos(res))
        .catch(Util.error);
    }
  }, [itemEanProp?.ean, props.modalVisible]);

  // ------------------------------------------------------------ //
  // Load Ean Detail for sibling prices
  // ------------------------------------------------------------ //
  const [siblings, setSiblings] = useState<API.Ean[]>([]);
  const [eanAppended, setEanAppended] = useState<API.Ean>();
  const loadEanDetail = useCallback(async (params: Partial<API.Ean>) => {
    setLoading(true);
    getEanList(
      {
        ...params,
        is_multi: true,
        pageSize: 100,
        with: 'eanPrices',
      },
      { attr_case_qty: 'ascend' },
    )
      .then((res) => {
        setSiblings(res.data);
        formRef.current?.setFieldValue('siblings', res.data);
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    if (itemEanProp?.is_single && itemEanProp?.item_id && props.modalVisible) {
      loadEanDetail({ item_id: itemEanProp?.item_id });
    } else {
      setSiblings([]);
    }
  }, [loadEanDetail, props.modalVisible, itemEanProp?.is_single, itemEanProp?.item_id]);

  const lastSalesStatSummaryEle = useMemo(() => {
    if (!itemEanProp) return null;

    return (
      <div style={{ position: 'absolute', top: 8, right: 100, fontWeight: 'normal' }} className="text-sm">
        <table className="headerSmallTable" cellPadding="0" cellSpacing="0">
          <tr>
            <td>Sales last 30 days:</td>
            <td>
              {sn(itemEanProp.gp_single_qty_ordered_sum_30) > 0 ? (
                <>
                  {ni(itemEanProp.gp_single_qty_ordered_sum_30)} single (
                  {nf2(sn(itemEanProp.gp_single_cturnover_30) / sn(itemEanProp.gp_single_qty_ordered_sum_30))} /{' '}
                  {nf2(itemEanProp.gp_single_gp_avg_30)})
                </>
              ) : null}
            </td>
            <td>
              {sn(itemEanProp.gp_multi_qty_ordered_sum_30) > 0 ? (
                <>
                  {ni(itemEanProp.gp_multi_qty_ordered_sum_30)} multi (
                  {nf2(sn(itemEanProp.gp_multi_cturnover_30) / sn(itemEanProp.gp_multi_qty_ordered_sum_30))} /{' '}
                  {nf2(itemEanProp.gp_multi_gp_avg_30)})
                </>
              ) : null}
            </td>
          </tr>
          <tr>
            <td>Sales last 365 days:</td>
            <td>
              {sn(itemEanProp.gp_single_qty_ordered_sum_365) > 0 ? (
                <>
                  {ni(itemEanProp.gp_single_qty_ordered_sum_365)} single (
                  {nf2(sn(itemEanProp.gp_single_cturnover_365) / sn(itemEanProp.gp_single_qty_ordered_sum_365))} /{' '}
                  {nf2(itemEanProp.gp_single_gp_avg_365)})
                </>
              ) : null}
            </td>
            <td>
              {sn(itemEanProp.gp_multi_qty_ordered_sum_365) > 0 ? (
                <>
                  {ni(itemEanProp.gp_multi_qty_ordered_sum_365)} multi (
                  {nf2(sn(itemEanProp.gp_multi_cturnover_365) / sn(itemEanProp.gp_multi_qty_ordered_sum_365))} /{' '}
                  {nf2(itemEanProp.gp_multi_gp_avg_365)})
                </>
              ) : null}
            </td>
          </tr>
        </table>
      </div>
    );
  }, [itemEanProp]);

  useEffect(() => {
    if (itemEanProp?.ean && props.modalVisible) {
      getEanList(
        {
          ean: itemEanProp.ean,
          pageSize: 1,
          with: 'eanPriceStable',
        },
        { attr_case_qty: 'ascend' },
      )
        .then((res) => {
          setEanAppended(res.data[0]);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [itemEanProp?.ean, props.modalVisible]);

  const priceStable: API.EanPriceStable | null | undefined = useMemo(() => {
    if (eanAppended) {
      return eanAppended.ean_price_stable;
    }
    return null;
  }, [eanAppended]);

  return (
    <ModalForm<Partial<API.Ean>>
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update EAN Prices -&nbsp;</span>
          <Typography.Paragraph
            copyable={{
              text: itemEanProp?.ean || '',
              tooltips: 'Copy EAN ' + (itemEanProp?.ean || ''),
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {itemEanProp?.ean || ''}
          </Typography.Paragraph>
          <Typography.Paragraph
            copyable={{
              text: itemEanProp?.sku || '',
              tooltips: 'Copy SKU ' + (itemEanProp?.sku || ''),
            }}
            style={{ display: 'inline-block', marginBottom: 0 }}
          >
            {itemEanProp?.sku || ''}
          </Typography.Paragraph>
          <SocialLinks
            ean={itemEanProp?.ean || ''}
            title={itemEanProp?.ean_texts?.[0]?.name}
            style={{ marginLeft: 50 }}
          />
          {props.handleNavigation ? (
            <ModalNavigation
              modalName="price"
              eanId={itemEanProp?.id}
              itemId={itemEanProp?.item_id}
              handleNavigation={props.handleNavigation}
              style={{ marginLeft: 50 }}
            />
          ) : null}
          {props.gdsn && (
            <GdsnItemButton
              ean={itemEanProp?.ean}
              eanId={itemEanProp?.id}
              itemId={itemEanProp?.item_id}
              style={{ marginLeft: 50 }}
              fetchGdsnItem={fetchGdsnItem}
            />
          )}
          {actionButtons}
          {itemEanProp?.files ? (
            <div className="relative" style={{ width: 50, marginLeft: 32 }}>
              <div className="absolute" style={{ top: -25, left: 0 }}>
                <Image.PreviewGroup>
                  {itemEanProp.files &&
                    itemEanProp.files.map((file, ind) => (
                      <Image
                        key={file.id}
                        src={file.thumb_url}
                        preview={{
                          src: file.url,
                        }}
                        wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                        width={50}
                        height={50}
                      />
                    ))}
                </Image.PreviewGroup>
              </div>
            </div>
          ) : (
            <></>
          )}
          {lastSalesStatSummaryEle}
        </div>
      }
      disabled={loadingGdsn}
      width={1800}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      formRef={formRef}
      onFinish={async (value) => {
        if (!formRef.current) return;
        setLoading(true);
        if (formRef.current?.isFieldsTouched()) {
          const newData: Partial<API.Ean> = {
            ...value,
            id: itemEanProp?.id,
            sku: itemEanProp?.sku,
          };

          if (!('fs_special_discount' in newData)) {
            newData.fs_special_discount = null;
          }

          if (!('fs_special_badge' in newData)) {
            newData.fs_special_badge = null;
          }
          if (!('fs_special_badge2' in newData)) {
            newData.fs_special_badge2 = null;
          }

          const success = await handleUpdate(newData);
          // Important: We immediately reset upSync field.
          // formRef.current.setFieldsValue({ ...value, upSync: '' });

          if (success) {
            await runActionsCallback();

            setRefreshTickHistory((prev) => prev + 1);

            setLoading(false);
            if ((value as any).closeModal) props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          setLoading(false);
          props.handleModalVisible(false);
        }
        formRef.current.getFieldInstance('up-sync').value = '';
      }}
      submitter={{
        render: (p, dom) => {
          return (
            <Space>
              {actionButtons}
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  formRef.current?.setFieldValue('closeModal', 1);
                  p.submit();
                }}
              >
                Save & Close
              </Button>
              <Button
                type="default"
                size="small"
                onClick={() => {
                  props.handleModalVisible(false);
                }}
              >
                Cancel
              </Button>
            </Space>
          );
        },
      }}
      modalProps={{
        confirmLoading: loading,
        className: itemEanProp?.is_single ? 'm-single' : 'm-multi',
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="closeModal" />
        {hiddenFormElements}
      </div>
      <Row gutter={24} wrap={false} style={{ width: '100%' }}>
        <Col flex="0 0 300px" style={{ marginTop: -25 }}>
          <div style={{ overflowY: 'auto', maxHeight: 160 }}>
            <PriceChangeHistoryList
              eanId={itemEanProp?.id}
              vat={itemEanProp?.item?.vat?.value || 0}
              refreshTick={refreshTickHistory}
              pagination={{
                hideOnSinglePage: true,
              }}
            />
          </div>
          <LatestIboListSummary itemId={itemEanProp?.item_id} />
          {priceStable && (
            <ProCard
              size="small"
              title="Min. Price & Supplier in XLS"
              bodyStyle={{ padding: 0 }}
              headStyle={{ padding: 0 }}
              style={{ marginTop: 24 }}
            >
              {/* <Row>
                <Col span={10}>{itemEanProp.ps_price ? nf2(itemEanProp.ps_price) + EURO : null}</Col>
                <Col>
                  {itemEanProp.ps_supplier_id ? `#${itemEanProp.ps_supplier_id} - ${itemEanProp.ps_supplier_name}` : ''}
                </Col>
              </Row> */}
              <Row>
                <Col span={10}>{priceStable.cur_price ? nf2(priceStable.cur_price) + EURO : null}</Col>
                <Col>
                  {priceStable.cur_import?.supplier
                    ? `#${priceStable.cur_import?.supplier.id} - ${priceStable.cur_import?.supplier.name}`
                    : ''}
                </Col>
              </Row>
              {priceStable.last_price && (
                <Row className="c-grey">
                  <Col span={10}>{priceStable.last_price ? nf2(priceStable.last_price) + EURO : null}</Col>
                  <Col>
                    {priceStable.last_import?.supplier
                      ? `#${priceStable.last_import?.supplier.id} - ${priceStable.last_import?.supplier.name}`
                      : ''}
                  </Col>
                </Row>
              )}
            </ProCard>
          )}
        </Col>
        <Col flex="0 0 950px">
          <ProFormGroup rowProps={{ gutter: 0 }}>
            <SProFormDigit
              colProps={{ span: 'auto' }}
              name="item_base"
              label="Item Base"
              width={120}
              fieldProps={{
                precision: 5,
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              formItemProps={{
                tooltip: 'This is Weight/1000 (Kg)',
                help: (
                  <div>
                    <div>
                      {gdsnItem &&
                        renderTakeButton(
                          Util.numberFormat(gdsnItem.item_base, true, 5, true),
                          () => {
                            formRef.current?.setFieldValue('item_base', gdsnItem.item_base);
                            validateGdsnItemsDiff();
                          },
                          gdsnItemsDiff.item_base,
                        )}
                    </div>
                    <div>
                      {itemEanProp?.is_single ? null : (
                        <ProFormDependency
                          key={'item_base_alt2'}
                          name={['item_base_unit', 'item_base', 'attr_case_qty']}
                        >
                          {(depValues) => {
                            return (
                              <>
                                <InfoCircleOutlined title="Based on parent item." /> &nbsp;
                                {`${depValues.attr_case_qty} x ${Util.numberFormat(
                                  itemEanProp?.parent?.item_base,
                                  true,
                                  5,
                                  true,
                                )} ${depValues.item_base_unit} = ${Util.numberFormat(
                                  Util.safeNumber(itemEanProp?.parent?.item_base) * depValues.attr_case_qty,
                                  true,
                                  5,
                                  true,
                                )} ${depValues.item_base_unit}`}
                              </>
                            );
                          }}
                        </ProFormDependency>
                      )}
                    </div>
                  </div>
                ),
              }}
            />
            <ProFormSelect
              name={'item_base_unit'}
              label="Item Base Unit"
              colProps={{ span: 'auto' }}
              width={90}
              rules={[
                {
                  required: true,
                  message: 'Item Base Unit is required',
                },
              ]}
              initialValue={'kg'}
              convertValue={(value) => (value === null ? 'kg' : value)}
              options={[
                { value: 'kg', label: 'kg' },
                { value: 'l', label: 'L' },
              ]}
              fieldProps={{
                onChange(value) {
                  validateGdsnItemsDiff();
                },
              }}
              help={
                gdsnItem && (
                  <>
                    {renderTakeButton(
                      gdsnItem.item_base_unit,
                      () => {
                        formRef.current?.setFieldValue(['item_base_unit'], gdsnItem.item_base_unit);
                        validateGdsnItemsDiff();
                      },
                      gdsnItemsDiff.item_base_unit,
                    )}{' '}
                    <span className="text-xs">
                      ({`${gdsnItem.detail?.net_content} ${gdsnItem.detail?.net_content_unit}`})
                    </span>
                  </>
                )
              }
            />
            <ProFormDependency key={'item_base_alt'} name={['item_base_unit', 'item_base']}>
              {(depValues) => {
                return (
                  <>
                    <ProFormField label=" " colProps={{ span: 5 }}>
                      {getSmallItemBaseText(depValues)}
                    </ProFormField>
                  </>
                );
              }}
            </ProFormDependency>
            <ProFormField colProps={{ span: 'auto' }}>
              <Space direction="vertical">
                <Space size={17}>
                  <label>Item Name: </label>
                  <Typography.Paragraph
                    copyable={{
                      text: itemEanProp?.item?.name || '',
                      tooltips: 'Copy',
                    }}
                    style={{ display: 'inline-block', marginBottom: 0 }}
                  >
                    {itemEanProp?.item?.name || '-'}
                  </Typography.Paragraph>
                </Space>
                <Space size={16}>
                  <label>EAN Name: </label>
                  <Typography.Paragraph
                    copyable={{
                      text: itemEanProp?.ean_texts?.[0]?.name || '',
                      tooltips: 'Copy EAN Name',
                    }}
                    style={{ display: 'inline-block', marginBottom: 0 }}
                  >
                    {itemEanProp?.ean_texts?.[0]?.name ?? itemEanProp?.ean_text_de?.name ?? '-'}
                  </Typography.Paragraph>
                </Space>
              </Space>
            </ProFormField>
          </ProFormGroup>

          <ProFormGroup
            rowProps={{ gutter: 24 }}
            title={
              <Row>
                <Col flex="530px">
                  {ibos.length > 0 &&
                    ibos.map((ibo, index) => {
                      const price = Util.fPrices(ibo?.price, vatPercent);
                      const p0 = sn(price[1]) * caseQty;

                      let grossPriceDiff = 0;
                      let p1 = 0;
                      if (index < ibos.length - 1) {
                        const pricePrev = Util.fPrices(ibos[index + 1]?.price, vatPercent);
                        if (price[1] && pricePrev[1]) {
                          p1 = sn(pricePrev[1]) * caseQty;
                          grossPriceDiff = p0 - p1;
                        }
                      }
                      return (
                        <Row key={ibo.id}>
                          {index == 0 ? (
                            <Col flex="130px">
                              <span>Prices </span>
                              <span style={{ fontWeight: 'normal', paddingLeft: 12 }}>Last IBOs:</span>
                            </Col>
                          ) : (
                            <Col flex="130px" />
                          )}
                          <Col flex={'200px'}>
                            {`${nf2(sn(price[0]))} / ${nf2(sn(price[1]))}`}
                            {itemEanProp?.is_single
                              ? ``
                              : ` --> ${nf2(sn(price[0]) * caseQty)} / ${nf2(sn(price[1]) * caseQty)}`}
                          </Col>
                          <Col
                            flex="70px"
                            title="Order date"
                            style={{ fontWeight: 'normal', alignSelf: 'center' }}
                            className="text-sm"
                          >
                            {ibo?.ibom?.order_date ? Util.dtToDMY(ibo?.ibom?.order_date) : ''}
                          </Col>
                          <Col
                            flex="70px"
                            title="Exp date of IBO"
                            style={{ fontWeight: 'normal', alignSelf: 'center' }}
                            className="text-sm"
                          >
                            {ibo?.exp_date ? Util.dtToDMY(ibo?.exp_date) : ''}
                          </Col>
                          {grossPriceDiff != 0 && (
                            <Col
                              flex="60px"
                              title={`Diff: ${nf2(grossPriceDiff)} ${EURO}`}
                              style={{ fontWeight: 'normal' }}
                            >
                              {nf2((p0 / p1) * 100 - 100)}%
                            </Col>
                          )}
                        </Row>
                      );
                    })}
                </Col>
                {ScrapSystemIds.map((id) => (
                  <Col key={id}>
                    <Button
                      size="small"
                      type="default"
                      className="btn-success"
                      onClick={async () => {
                        const hide = message.loading('Getting price from ' + id, 0);
                        await scrapWoSPrice(itemEanProp?.ean || '', id)
                          .then((res) => {
                            if (res && props.reloadList) {
                              const merged = [...(itemEanProp?.scrap_prices || [])];
                              const ind = _.findIndex(merged, { system: res.system });
                              if (ind >= 0) {
                                merged[ind] = res;
                              } else {
                                merged.push(res);
                              }
                              props.reloadList({ scrap_prices: merged });
                            }
                            message.success('Updated successfully!');
                          })
                          .catch((error) => {
                            Util.error(error);
                          })
                          .finally(() => hide());
                      }}
                    >
                      Get {id}
                    </Button>
                  </Col>
                ))}

                <Col style={{ fontWeight: 'normal' }} flex="220px">
                  {ScrapSystemIds.map((id) => {
                    const scrap_price = _.find(itemEanProp?.scrap_prices, { system: id });
                    return scrap_price ? (
                      <Row key={id}>
                        <Col span={6} style={{ textAlign: 'center' }}>
                          <a href={scrap_price.link} title={scrap_price.name} target="_blank" rel="noreferrer">
                            {id}
                          </a>
                        </Col>
                        <Col span={8}>
                          {`${nf2(sn(scrap_price.price))} €`}{' '}
                          <DeleteOutlined
                            className="text-sm"
                            style={{ color: 'red' }}
                            title="Delete this record"
                            onClick={async () => {
                              const hide = message.loading('Deleting the price entry...', 0);
                              await deleteScrappedPrice(scrap_price.id)
                                .then((res) => {
                                  if (res && props.reloadList) {
                                    const merged = [...(itemEanProp?.scrap_prices || [])];
                                    _.remove(merged, (x) => x.id == scrap_price.id);
                                    props.reloadList({ scrap_prices: merged });
                                  }
                                  message.success('Deleted successfully!');
                                })
                                .catch((error) => {
                                  Util.error(error);
                                })
                                .finally(() => hide());
                            }}
                          />
                        </Col>
                        <Col span={10} className="text-sm" style={{ textAlign: 'right' }}>{`${Util.dtToDMYHHMM(
                          scrap_price.updated_on,
                        )}`}</Col>
                      </Row>
                    ) : (
                      <Row key={id} />
                    );
                  })}
                  {uvpData && (
                    <>
                      <Row>
                        <Col span={6} style={{ textAlign: 'center' }}>
                          UVP (Xls)
                        </Col>
                        <Col span={8} className="c-green">
                          {uvpData?.uvp ? nf2(uvpData?.uvp) + EURO : ''}
                        </Col>
                        <Col span={10}>{uvpData?.article_no}</Col>
                      </Row>
                    </>
                  )}
                </Col>
              </Row>
            }
          >
            <ProFormList
              actionRef={priceActionRef}
              key={'price_type_id'}
              name="ean_prices"
              creatorButtonProps={{
                position: 'bottom',
                creatorButtonText: 'Add a price',
              }}
              creatorRecord={(index: number) => {
                const list = formRef.current?.getFieldValue('ean_prices');

                const priceType = priceTypes.find((x) => !_.find(list, { price_type_id: x.id }));
                return {
                  price_type_id: priceType?.id ?? new Date().getTime(),
                };
              }}
              deleteIconProps={{ tooltipText: 'Remove' }}
              copyIconProps={{ tooltipText: 'Copy row' }}
              max={priceTypes.length}
            >
              <Row gutter={6} key={'price_type_id'}>
                <Col>
                  <ProFormSelect
                    key="price_type_id"
                    width={100}
                    options={priceTypes?.map((x) => ({
                      label: `${x.name}`,
                      value: x.id,
                    }))}
                    name="price_type_id"
                    label="Price name"
                    rules={[
                      {
                        required: true,
                        message: '',
                      },
                    ]}
                  />
                </Col>
                <Col>
                  <ProFormDependency key={'price_dep'} name={['price', 'price_gross', 'price_type_id']}>
                    {(depValues) => {
                      const singleEanPrices = itemEanProp?.parent?.ean_prices;
                      const singlePrice = sn(
                        _.get(_.find(singleEanPrices, { price_type_id: depValues.price_type_id }), 'price', 0),
                      );
                      const singleGross = Util.fPrices(singlePrice, sn(itemEanProp?.item?.vat?.value), true)?.[1];

                      return (
                        <ProFormField label=" ">
                          <div style={{ width: 140 }} className="dark-blue">
                            <span>{` ${nf2(depValues.price / caseQty, true)} / ${nf2(
                              sn(depValues.price_gross) / caseQty,
                              true,
                            )}`}</span>
                            {sn(latestIboPrices[0]) && caseQty && (
                              <span className="text-sm" style={{ paddingLeft: 16 }}>
                                {Util.numberFormat(
                                  (depValues.price * 100) / caseQty / sn(latestIboPrices[0]),
                                  true,
                                  1,
                                  true,
                                )}{' '}
                                %
                              </span>
                            )}
                            {!isSingle ? (
                              <span className="text-sm" style={{ position: 'absolute', left: 0, bottom: -13 }}>
                                {' '}
                                Single: {nf2(singleGross)}
                              </span>
                            ) : undefined}
                          </div>
                        </ProFormField>
                      );
                    }}
                  </ProFormDependency>
                </Col>
                <Col>
                  <ProFormDependency key={'price_dep'} name={['price', 'price_type_id']}>
                    {(depValues) => {
                      return (
                        <SProFormDigit
                          name="price"
                          label={'Price (€)'}
                          placeholder="Price"
                          width={105}
                          rules={[
                            {
                              required: true,
                              message: '',
                            },
                          ]}
                          fieldProps={{
                            step: 0.1,
                            precision: 7,
                            onChange: (value) => {
                              const prices = Util.fPrices(value, itemEanProp?.item?.vat?.value, true, true);

                              const rows = [...(priceActionRef.current?.getList() || [])];
                              for (const row of rows) {
                                if (row.price_type_id == depValues.price_type_id) {
                                  row.price_gross = prices[1];
                                }
                              }
                              formRef.current?.setFieldsValue({ ean_prices: rows });
                              updateEbaySpecialPrice();
                            },
                          }}
                        />
                      );
                    }}
                  </ProFormDependency>
                </Col>
                <Col>
                  <ProFormDependency key={'price_gross_dep'} name={['price', 'price_type_id']}>
                    {(depValues) => {
                      return (
                        <SProFormDigit
                          name="price_gross"
                          label={'Gross Price(€)'}
                          placeholder="Gross Price"
                          initialValue={Util.fPrices(depValues.price, itemEanProp?.item?.vat?.value)[1]}
                          width={105}
                          fieldProps={{
                            step: 0.1,
                            precision: 7,
                            onChange: (value) => {
                              const prices = Util.fPrices(value, itemEanProp?.item?.vat?.value, true, false);

                              const rows = [...(priceActionRef.current?.getList() || [])];
                              for (const row of rows) {
                                if (row.price_type_id == depValues.price_type_id) {
                                  row.price = prices[0];
                                }
                              }
                              formRef.current?.setFieldsValue({ ean_prices: rows });
                              updateEbaySpecialPrice();
                            },
                          }}
                        />
                      );
                    }}
                  </ProFormDependency>
                </Col>

                <Col>
                  <ProFormDependency key={'diff_net_price_deps'} name={['price', 'price_type_id']}>
                    {(depValues) => {
                      const lastIboPrice = sn(latestIboPrices?.[0]) * sn(itemEanProp?.attr_case_qty);
                      return (
                        <ProFormField label={<InfoCircleOutlined title="Net Price - Last IBO" />}>
                          <div style={{ width: 40 }} className="dark-blue">
                            {nf2(depValues.price - lastIboPrice)}
                          </div>
                        </ProFormField>
                      );
                    }}
                  </ProFormDependency>
                </Col>

                <Col style={{ opacity: 0.7 }}>
                  <SProFormDigit
                    name="min_qty"
                    label={'Qty'}
                    tooltip="Minimum order quantity."
                    placeholder="Min. Qty"
                    initialValue={1}
                    width={60}
                    className="c-grey"
                    rules={[
                      {
                        required: true,
                        message: '',
                      },
                    ]}
                    fieldProps={{ precision: 0 }}
                  />
                </Col>
                <Col style={{ opacity: 0.7 }}>
                  <SDatePicker name="start_date" label="Start date" placeholder="Start date" width={110} />
                </Col>
                <Col style={{ opacity: 0.7 }}>
                  <SDatePicker name="end_date" label="End date" placeholder="End date" width={110} />
                </Col>
              </Row>
            </ProFormList>
          </ProFormGroup>

          {siblings.length ? (
            <>
              {siblings.map((sEan, sInd) => {
                return (
                  <UpdatePriceAttributePartialForm
                    key={sEan.id}
                    index={sInd}
                    vatValue={sn(itemEanProp?.item?.vat?.value)}
                    initialValues={sEan || {}}
                    formRef={formRef}
                    priceTypes={priceTypes}
                    latestIboPrices={latestIboPrices}
                  />
                );
              })}
              <Divider />
            </>
          ) : null}

          <ProFormGroup title="Ebay Prices" rowProps={{ gutter: 24 }}>
            <Row gutter={8}>
              <Col style={{ paddingLeft: 16 }}>
                <ProFormDependency key={'fs_ebay_price_special_deps'} name={['fs_ebay_price_special']}>
                  {(depValues) => {
                    return (
                      depValues.fs_ebay_price_special &&
                      itemEanProp?.parent_stock_stables_min_exp_date && (
                        <ProFormItem label="Special price Exp.Date">
                          <span className="dark-blue">
                            {Util.dtToDMY(itemEanProp?.parent_stock_stables_min_exp_date)}
                          </span>
                        </ProFormItem>
                      )
                    );
                  }}
                </ProFormDependency>
              </Col>
              <Col>
                <ProFormSelect
                  name="fs_special_discount"
                  label="Discount"
                  placeholder="Discount"
                  width={105}
                  options={['-10%', '-20%', '-25%', '-30%', '-40%', '-50%', '-60%', '-70%', '-80%', '-90%']}
                  fieldProps={{
                    onChange(value, option) {
                      updateEbaySpecialPrice();
                    },
                  }}
                />
              </Col>
              <Col>
                <ProFormDependency key={'fs_special_discount_dep'} name={['ean_prices', 'fs_special_discount']}>
                  {(depValues) => {
                    const discount = sn((depValues.fs_special_discount || '').slice(0, -1)) ?? 0;
                    if (depValues.ean_prices) {
                      // V1: by EAN price
                      const prices = Util.fPrices(
                        sn(depValues.ean_prices.find((x: API.EanPrice) => x.price_type_id == 1)?.price) *
                          1.15 *
                          (1 + discount / 100),
                        itemEanProp?.item?.vat?.value,
                      );

                      return discount && sn(prices[1]) > 0 ? (
                        <ProFormItem label=" ">
                          <span className="dark-blue">{`${nf2(prices[0])} / ${nf2(prices[1])}`}</span>
                        </ProFormItem>
                      ) : undefined;
                    }
                    return undefined;
                  }}
                </ProFormDependency>
              </Col>
              <Col>
                <ProFormSelect
                  name="fs_special_badge"
                  label="Special badge"
                  placeholder="Badge"
                  showSearch
                  width={150}
                  request={async (params) => {
                    return getDictList({ params, type: DictType.ProductBadge, pageSize: 500 }).then((res) =>
                      res.data.map((x) => ({ value: x.code, label: x.value })),
                    );
                  }}
                />
              </Col>
              <Col>
                <ProFormSelect
                  name="fs_special_badge2"
                  label="Text Label/Badge"
                  placeholder="Text Label/Badge"
                  showSearch
                  width={150}
                  options={(titlePrefixOptions as API.Dict[])
                    ?.filter((x) => x.type == DictType.TitlePrefix)
                    ?.map((x) => ({ label: x.value, value: x.code }))}
                  fieldProps={{
                    dropdownRender(menu) {
                      return (
                        <>
                          {menu}
                          <Divider style={{ margin: '8px 0' }} />
                          <Space style={{ padding: '0 8px 4px' }}>
                            <Input
                              placeholder="Please enter item"
                              ref={titlePrefixRef}
                              value={newTitlePrefix}
                              onChange={(e) => setNewTitlePrefix(e.target.value)}
                            />
                            <Button
                              type="text"
                              icon={<PlusOutlined />}
                              onClick={() => {
                                onTitlePrefixAddClick(titlePrefixRef.current?.input?.value, DictType.TitlePrefix);
                              }}
                            />
                          </Space>
                        </>
                      );
                    },
                  }}
                />
              </Col>
            </Row>
          </ProFormGroup>
        </Col>
        <Col flex="auto">
          <EanSaleStatsMiniTable itemEan={itemEanProp as any} modalVisible={props.modalVisible} />
          <LatestIboList itemId={itemEanProp?.item_id} />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default UpdatePriceAttributeForm;
