import { getGdsnItemEan } from '@/services/foodstore-one/Item/ean';
import { HighlightOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import { useCallback, useEffect, useState } from 'react';

/* export type useGdsnItem = {
  path: string;
  name?: string;
}; */

export default (ean?: string, load?: boolean) => {
  // GDSN data
  const [loading, setLoading] = useState<boolean>(false);
  const [gdsnItem, setGdsnItem] = useState<API.ItemEanGdsn>();
  const fetchGdsnItem = useCallback(() => {
    if (load && ean) {
      setLoading(true);
      setGdsnItem(undefined);
      getGdsnItemEan(ean)
        .then((res) => {
          setGdsnItem(res);
        })
        .catch((err) => {
          setGdsnItem(undefined);
        })
        .finally(() => setLoading(false));
    } else {
      setGdsnItem(undefined);
    }
  }, [ean, load]);

  const renderTakeButton = useCallback(
    (value?: any, onClick?: any, equal?: boolean, options?: { showCopyable?: boolean; renderExtra?: any }) => {
      return (
        value && (
          <div style={{ marginRight: 40 }}>
            <Typography.Text
              className="text-sm"
              title={value}
              ellipsis
              copyable={
                options?.showCopyable
                  ? {
                      text: value,
                    }
                  : false
              }
              style={{ color: equal ? 'initial' : 'red' }}
            >
              {value}
            </Typography.Text>
            <Button
              type="link"
              size="small"
              title="Take GDSN value."
              style={{ marginLeft: 6 }}
              onClick={onClick}
              icon={<HighlightOutlined />}
            />
            {options?.renderExtra?.()}
          </div>
        )
      );
    },
    [],
  );

  useEffect(() => {
    fetchGdsnItem();
  }, [fetchGdsnItem]);

  return { fetchGdsnItem, gdsnItem, renderTakeButton, loading };
};
