/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/scrap';

/** put PUT /api/scrap/wos-price */
export async function scrapWoSPrice(ean?: string, type?: string) {
  return request<API.BaseResult>(`${urlPrefix}/wos-price`, {
    method: 'POST',
    data: { ean, type },
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/scrap/price/{id} */
export async function deleteScrappedPrice(id?: number) {
  return request<API.BaseResult>(`${urlPrefix}/price/${id}`, {
    method: 'DELETE',
  }).then((res) => res.message);
}
