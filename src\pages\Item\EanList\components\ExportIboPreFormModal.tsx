import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import { Button, Col, Row, Space, Typography, message, Image } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createIboPreBulk, getIboPreOpenGrouped } from '@/services/foodstore-one/IBO/ibo-pre';
import Util, { nf2, ni, sShortImportDbTableName, sn } from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { StockQtyComp } from '../EanAllSummary';
import { InfoCircleOutlined } from '@ant-design/icons';
import EanSummaryMiniTable from './EanSummaryMiniTable';
import Offer2IboPreMatrixTable from './Offer2IboPreMatrixTable';
import ExpDate from '@/pages/Report/Order/components/ExpDate';
import moment from 'moment';
import EanSaleStatsMiniTable from './EanSaleStatsMiniTable';
import LatestIboList from './LatestIboList';
import type { EanPriceRecordType } from '@/services/foodstore-one/Item/ean';

type RecordType = {
  key?: React.Key;
  id?: number;
  price_xls?: number;
  price_xls_org?: number;
  qty?: number;
  case_qty?: number;
  qty_package?: number;
  supplier_id?: number;
  import_id?: number | null;
  status?: string;
} & {
  supplier?: string;
  shelf_life?: string;
  ve_pallet?: string; // ve per pallet
  xls_case_qty?: string; // Case Qty = VE
  pieces_pallet?: string;
};

export type FormValueType = Partial<API.IboPre> & { import?: any };

export type ExportIboPreFormModalProps = {
  initialValues?: EanPriceRecordType;
  modalVisible: boolean;
  xlsImports: API.Import[];
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  gdsn?: boolean;
};

const ExportIboPreFormModal: React.FC<ExportIboPreFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  const { initialValues: itemEanProp, xlsImports, modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (modalVisible && itemEanProp) {
      formRef.current?.setFieldValue('wish_note', itemEanProp?.wish_note);
    }
  }, [itemEanProp, modalVisible]);

  const editableFormRef = useRef<ProFormInstance>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  const [editableKeys1, setEditableRowKeys1] = useState<React.Key[]>([]);

  const [dataSource1, setDataSource1] = useState<RecordType[]>([]);

  const [openIboPreList, setOpenIboPreList] = useState<API.IboPre[]>([]);

  const [matrixRefreshTick, setMatrixRefreshTick] = useState<number>(0);

  useEffect(() => {
    if (modalVisible && itemEanProp) {
      const newDs: RecordType[] = [];
      const keys: React.Key[] = [];
      for (const x of xlsImports) {
        // @ts-ignore
        const p = itemEanProp ? sn(itemEanProp[`xls_bp_${x?.id}`] ?? itemEanProp[`xls_bp2_${x?.id}`] ?? 0) : 0;
        const row = {
          key: `i_${x.id}`,
          case_qty: 1,
          price_xls: p,
          price_xls_org: p,
          qty: 0,
          supplier: `${x.supplier?.name} | ${
            x.supplier_add ? x.supplier_add : sShortImportDbTableName(x.table_name, true)
          }`,
          supplier_tooltip: `${x.supplier?.name} | ${sShortImportDbTableName(x.table_name, true)} | ${
            x.supplier_add
          } | ${x.id}`,
          // supplier_add: x.supplier_add,
          supplier_id: x.supplier?.id,
          import_id: x.id,
          // @ts-ignore
          shelf_life: itemEanProp[`xls_shelf_life_${x?.id}`],
          // @ts-ignore
          ve_pallet: itemEanProp[`xls_ve_pallet_${x?.id}`],
          // @ts-ignore
          xls_case_qty: itemEanProp ? itemEanProp[`xls_case_qty_${x?.id}`] : 0,
          // @ts-ignore
          pieces_pallet: itemEanProp
            ? // @ts-ignore
              sn(itemEanProp[`xls_ve_pallet_${x?.id}`]) * sn(itemEanProp[`xls_case_qty_${x?.id}`])
            : 0,
          status: 'open',
        };
        newDs.push(row as any);
        keys.push(row.key);
      }
      setDataSource1(newDs);
      setEditableRowKeys1(keys);
    }
  }, [itemEanProp, modalVisible, xlsImports]);

  // @note: @2025-03-03 Removed Supplier Master Table feature.
  /* useEffect(() => {
    if (modalVisible && itemEanProp?.ean) {
      setLoading(true);
      getMasterSupplierPrice(itemEanProp?.ean)
        .then((res) => {
          const newDs: RecordType[] = [];
          const keys: React.Key[] = [];

          for (const x of res) {
            const row2: RecordType = {
              key: `s_${x.supplier_id}`,
              case_qty: 1,
              price_xls: x.price,
              price_xls_org: x.price,
              qty: 0,
              supplier: `${x.supplier?.name} | Master`,
              supplier_id: x.supplier?.id,
              import_id: null,
              shelf_life: '',
              ve_pallet: '',
              status: 'open',
            };
            newDs.push(row2);
            keys.push(row2.key as React.Key);
          }
          setDataSource2(newDs);
          setEditableRowKeys2(keys);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [itemEanProp?.ean, modalVisible]); */

  useEffect(() => {
    if (modalVisible) {
      setLoading(true);
      getIboPreOpenGrouped(itemEanProp?.id)
        .then((res) => {
          setOpenIboPreList(res);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
    // itemEanProp is an extra deps.
  }, [itemEanProp?.id, modalVisible, itemEanProp]);

  useEffect(() => {
    const ds = [...dataSource1];
    let keys = [...editableKeys1];
    for (const r of ds) {
      const iboPre = openIboPreList.find((x) => {
        return r.supplier_id == x.supplier_id && r.key == (x.import_id ? `i_${x.import_id}` : `s_${x.supplier_id}`);
      });

      if (iboPre) {
        r.qty = iboPre.qty;
        r.price_xls = iboPre.price_xls;
        r.status = iboPre.status;
        if (iboPre.status != 'open') {
          keys = keys.filter((k) => k != r.key);
        }
      } else {
        r.qty = 0;
        r.status = 'open';
      }
    }
    setDataSource(ds);
    setEditableRowKeys(keys);
  }, [dataSource1, editableKeys1, openIboPreList]);

  const columns: ProColumns<Partial<RecordType>>[] = useMemo(
    () => [
      {
        title: 'Supplier Xls',
        dataIndex: ['supplier'],
        editable: false,
        ellipsis: true,
        width: 150,
        render: (dom, entity) => <span title={`${(entity as any)?.supplier_tooltip}`}>{dom}</span>,
      },
      {
        title: 'Shelf Life',
        dataIndex: ['shelf_life'],
        width: 40,
        editable: false,
        align: 'right',
        render: (dom, record) => ni(record.shelf_life),
      },
      {
        title: 'VE',
        dataIndex: ['xls_case_qty'],
        width: 45,
        editable: false,
        align: 'right',
        render: (dom, record) => ni(record.xls_case_qty),
      },
      {
        title: 'VE Pallet',
        dataIndex: ['ve_pallet'],
        width: 45,
        editable: false,
        align: 'right',
        render: (dom, record) => ni(record.ve_pallet),
      },
      {
        title: 'Pcs Pallet',
        dataIndex: ['pieces_pallet'],
        width: 55,
        editable: false,
        align: 'right',
        render: (dom, record) => ni(record.pieces_pallet),
      },
      {
        title: 'XLS Price',
        dataIndex: ['price_xls_org'],
        width: 55,
        editable: false,
        align: 'right',
        render: (dom, record) => nf2(record.price_xls_org),
        onCell: (record) => ({ style: { paddingRight: 24 } }),
      },
      {
        title: 'Price',
        dataIndex: ['price_xls'],
        width: 95,
        tooltip: 'This price is average price!',
        renderFormItem: (item, { record }) => {
          return (
            <SProFormDigit
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{ precision: 3, size: 'small' }}
              placeholder=""
            />
          );
        },
        render: (dom, record) => nf2(record.price_xls),
      },
      {
        title: 'Qty',
        dataIndex: ['qty'],
        width: 80,
        renderFormItem: (item, { record }) => {
          return (
            <SProFormDigit
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{ size: 'small' }}
              placeholder=""
            />
          );
        },
      },
    ],
    [],
  );

  const minExp = itemEanProp
    ? itemEanProp?.is_single
      ? itemEanProp?.parent_stock_stables_min_exp_date
      : itemEanProp?.stock_stables_min_exp_date
    : null;

  const mDate = minExp ? moment(minExp) : null;
  const minExpDays = mDate && mDate.isValid() ? mDate?.diff(moment(), 'days') : 0;

  const handleEditableTableSave = () => {
    formRef.current?.submit();
  };

  const lastSalesStatSummaryEle = useMemo(() => {
    if (!itemEanProp) return null;

    return (
      <div style={{ position: 'absolute', top: 8, right: 100, fontWeight: 'normal' }} className="text-sm">
        <table className="headerSmallTable" cellPadding="0" cellSpacing="0">
          <tr>
            <td>Sales last 30 days:</td>
            <td>
              {sn(itemEanProp.gp_single_qty_ordered_sum_30) > 0 ? (
                <>
                  {ni(itemEanProp.gp_single_qty_ordered_sum_30)} single (
                  {nf2(sn(itemEanProp.gp_single_cturnover_30) / sn(itemEanProp.gp_single_qty_ordered_sum_30))} /{' '}
                  {nf2(itemEanProp.gp_single_gp_avg_30)})
                </>
              ) : null}
            </td>
            <td>
              {sn(itemEanProp.gp_multi_qty_ordered_sum_30) > 0 ? (
                <>
                  {ni(itemEanProp.gp_multi_qty_ordered_sum_30)} multi (
                  {nf2(sn(itemEanProp.gp_multi_cturnover_30) / sn(itemEanProp.gp_multi_qty_ordered_sum_30))} /{' '}
                  {nf2(itemEanProp.gp_multi_gp_avg_30)})
                </>
              ) : null}
            </td>
          </tr>
          <tr>
            <td>Sales last 365 days:</td>
            <td>
              {sn(itemEanProp.gp_single_qty_ordered_sum_365) > 0 ? (
                <>
                  {ni(itemEanProp.gp_single_qty_ordered_sum_365)} single (
                  {nf2(sn(itemEanProp.gp_single_cturnover_365) / sn(itemEanProp.gp_single_qty_ordered_sum_365))} /{' '}
                  {nf2(itemEanProp.gp_single_gp_avg_365)})
                </>
              ) : null}
            </td>
            <td>
              {sn(itemEanProp.gp_multi_qty_ordered_sum_365) > 0 ? (
                <>
                  {ni(itemEanProp.gp_multi_qty_ordered_sum_365)} multi (
                  {nf2(sn(itemEanProp.gp_multi_cturnover_365) / sn(itemEanProp.gp_multi_qty_ordered_sum_365))} /{' '}
                  {nf2(itemEanProp.gp_multi_gp_avg_365)})
                </>
              ) : null}
            </td>
          </tr>
        </table>
      </div>
    );
  }, [itemEanProp]);

  return (
    <ModalForm<FormValueType>
      title={
        <>
          <Space style={{ alignItems: 'center' }} size={24}>
            <span>Create / Update IBO Pre</span>
            <Typography.Paragraph
              copyable={{
                text: itemEanProp?.ean || '',
                tooltips: 'Copy SKU ' + (itemEanProp?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEanProp?.sku || ''}
            </Typography.Paragraph>
            <Typography.Paragraph
              copyable={{
                text: itemEanProp?.ean || '',
                tooltips: 'Copy EAN ' + (itemEanProp?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEanProp?.ean || ''}
            </Typography.Paragraph>

            {itemEanProp?.files ? (
              <div className="relative" style={{ width: 50 }}>
                <div className="absolute" style={{ top: -25, left: 0 }}>
                  <Image.PreviewGroup>
                    {itemEanProp.files &&
                      itemEanProp.files.map((file, ind) => (
                        <Image
                          key={file.id}
                          src={file.thumb_url}
                          preview={{
                            src: file.url,
                          }}
                          wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                          width={50}
                          height={50}
                        />
                      ))}
                  </Image.PreviewGroup>
                </div>
              </div>
            ) : (
              <></>
            )}
          </Space>
          {lastSalesStatSummaryEle}
        </>
      }
      width={1600}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 5 }}
      labelAlign="left"
      wrapperCol={{ span: 19 }}
      formRef={formRef}
      onFinish={async (value) => {
        const dataByRef = editableFormRef.current?.getFieldsValue();

        if (!formRef.current) return;

        const rows: API.IboPre[] = [];
        dataSource.forEach((x) => {
          rows.push({
            ...x,
            ...dataByRef[sn(x.id)],
            ean_id: itemEanProp?.id,
            item_id: itemEanProp?.item_id,
          });
        });

        setLoading(true);

        createIboPreBulk({ ...value, rows, ean_id: itemEanProp?.id })
          .then((res) => {
            props.onSubmit?.(value);
            message.success('Saved IBO pre-order entries.', 3);

            setMatrixRefreshTick((prev) => prev + 1);
          })
          .catch(Util.error)
          .finally(() => {
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Save & Close' },
        onReset(value) {
          props.handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading, loading: loading },
        submitButtonProps: { disabled: loading, loading: loading, hidden: true },
      }}
      modalProps={{
        confirmLoading: loading,
        className: itemEanProp?.is_single ? 'm-single' : 'm-multi',
      }}
    >
      <Row style={{ marginBottom: 8 }} gutter={24}>
        <Col span="9">
          <Row>
            <Col span="15">
              <Typography.Paragraph
                copyable={{
                  text: itemEanProp?.item?.name || '',
                  tooltips: 'Copy',
                }}
                style={{ display: 'inline-block', marginBottom: 0 }}
              >
                {itemEanProp?.item?.name || '-'}
              </Typography.Paragraph>
            </Col>
            <Col flex="70px">
              Stock <InfoCircleOutlined title="Italic --> blocked qty." />
            </Col>
            <Col>
              <StockQtyComp
                availableQty={itemEanProp?.stock_mix_qty}
                blockedQty={itemEanProp?.stock_mix_qty_b}
                is_single={itemEanProp?.is_single}
              />
              <div>
                {minExp ? (
                  <>
                    <ExpDate date={minExp} /> ({ni(minExpDays)} days)
                  </>
                ) : null}
              </div>
            </Col>
          </Row>
          <Row>
            <Col span="24">
              <EanSummaryMiniTable
                xlsImports={xlsImports}
                itemEan={itemEanProp as any}
                reloadList={() => {
                  // reload parent grid
                  props.onSubmit?.({});
                }}
              />
            </Col>
          </Row>
          <EditableProTable
            rowKey={'key'}
            headerTitle={'IBO Pre List'}
            editableFormRef={editableFormRef}
            columns={columns}
            dataSource={dataSource}
            value={dataSource}
            controlled
            onChange={setDataSource}
            search={false}
            toolbar={{
              search: false,
              actions: [
                <Button key="save" size="small" type="primary" onClick={handleEditableTableSave}>
                  Save
                </Button>,
              ],
              menu: undefined,
            }}
            pagination={false}
            className="w-full"
            rowClassName={(record) => {
              // cheapest price
              const cheapestPrice = sn(record.price_xls_org);
              if (cheapestPrice <= 0) return '';
              for (const x of dataSource) {
                const p = sn(x.price_xls_org);
                if (!p) continue;
                if (p < cheapestPrice) {
                  return '';
                }
              }
              return 'bg-green3';
            }}
            size="small"
            recordCreatorProps={false}
            style={{ marginBottom: 16 }}
            cardProps={{ bodyStyle: { padding: 0 } }}
            editable={{
              type: 'multiple',
              editableKeys,
              onChange: setEditableRowKeys,
              deletePopconfirmMessage: 'Are you sure you want to delete?',
              onlyAddOneLineAlertMessage: 'You can only add one.',
            }}
            loading={loading}
            columnEmptyText=""
          />
          <ProFormTextArea
            name="wish_note"
            label="Notes"
            width="xl"
            formItemProps={{ style: { marginBottom: 0 } }}
            disabled={loading}
          />
        </Col>
        <Col span={7}>
          <Row style={{ marginBottom: 8 }}>
            <Col span="6">Qty / Packages: </Col>
            <Col span="9">{itemEanProp?.siblings_multi?.map((x) => x.attr_case_qty)?.join(' / ')}</Col>
            <Col span="6">Wishing Qty: </Col>
            <Col span="3">{ni(itemEanProp?.wish_qty, true)}</Col>
          </Row>
          {itemEanProp?.siblings_multi?.map((sib) => (
            <Row key={sib.id} style={{ marginBottom: 8 }}>
              <Col span="6">Valid Qtys (x {sib.attr_case_qty}): </Col>
              {!!sib?.attr_case_qty && (
                <Col span="18">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 15, 20, 25, 30, 50, 75]
                    .map((x) => x * sn(sib?.attr_case_qty))
                    .join(', ')}{' '}
                  ...
                </Col>
              )}
            </Row>
          ))}
          <Row style={{ marginBottom: 8 }}>
            <Col span={24}>
              <Offer2IboPreMatrixTable itemEan={itemEanProp as any} refreshTick={matrixRefreshTick} />
            </Col>
          </Row>
        </Col>
        <Col span={8}>
          <EanSaleStatsMiniTable itemEan={itemEanProp as any} modalVisible={modalVisible} />

          <LatestIboList itemId={itemEanProp?.item_id} />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default ExportIboPreFormModal;
