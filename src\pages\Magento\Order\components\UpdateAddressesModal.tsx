import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { Button, Card, Col, Modal, Row, Space, message } from 'antd';
import type { AddressFormValueType } from './UpdateAddressForm';
import UpdateAddressForm from './UpdateAddressForm';
import Util, { sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ArrowLeftOutlined, ArrowRightOutlined, SwapOutlined, UploadOutlined } from '@ant-design/icons';

import { usOrderAddressesUpdate } from '@/services/foodstore-one/Magento/order';
import { isOrderAddressEditable } from '..';

export type FormValueType = Partial<API.Order>;

export type UpdateAddressesModalProps = {
  initialValues?: FormValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderAddress) => Promise<boolean | void>;
};

const UpdateAddressesModal: React.FC<UpdateAddressesModalProps> = (props) => {
  const formRefBilling = useRef<ProFormInstance<AddressFormValueType>>();
  const formRefShipping = useRef<ProFormInstance<AddressFormValueType>>();

  const billingAddress = props.initialValues?.detail?.billing_address;
  const shippingAddress: API.OrderAddress = props.initialValues?.detail?.shipping_address ?? {
    address_type: 'shipping',
    city: props.initialValues?.sa_city,
    company: props.initialValues?.sa_company,
    country_id: props.initialValues?.sa_country_code,
    customer_address_id: props.initialValues?.detail?.shipping_address?.customer_address_id,
    email: props.initialValues?.sa_email,
    entity_id: props.initialValues?.detail?.shipping_address?.entity_id,

    firstname: props.initialValues?.sa_firstname,
    lastname: props.initialValues?.sa_lastname,
    parent_id: props.initialValues?.entity_id,
    postcode: props.initialValues?.sa_zip,
    street: [props.initialValues?.sa_street || ''],
    telephone: props.initialValues?.sa_telephone,
  };

  const isEditable = isOrderAddressEditable(props.initialValues?.status);

  return (
    <Modal
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>Update Order Addresses</span>
        </div>
      }
      width="1000px"
      bodyStyle={{ paddingBottom: 0 }}
      okButtonProps={{ disabled: !isEditable }}
      onOk={() => {
        if (!isEditable) {
          message.info(`You cannot update address for ${props.initialValues?.status} order`);
          return;
        }

        const billingValues = { ...billingAddress, ...formRefBilling.current?.getFieldsValue() };
        const shippingValues = { ...shippingAddress, ...formRefShipping.current?.getFieldsValue() };
        const addresses: API.OrderAddress[] = [];
        if (billingValues?.entity_id) {
          addresses.push(billingValues);
        }
        if (shippingValues?.entity_id) {
          addresses.push(shippingValues);
        }
        if (props.initialValues?.entity_id && addresses.length) {
          const hide = message.loading('Updating addresses...');
          usOrderAddressesUpdate(sn(props.initialValues?.entity_id), addresses)
            .then((res) => {
              message.success('Updated successfully.');
              props.onSubmit?.({});
              props.handleModalVisible(false);
            })
            .catch(Util.error)
            .finally(hide);
        } else {
          message.info('Nothing to update.');
        }
      }}
      okType="primary"
      okText={
        <>
          <UploadOutlined />
          Save & UpSync
        </>
      }
    >
      <Row wrap={false} gutter={36}>
        <Col span={12}>
          <Card
            size="small"
            title={
              <Space size={16}>
                <span>Invoice address</span>
                <Button
                  size="small"
                  type="primary"
                  ghost
                  title="Set Delivery as Invoice"
                  icon={<ArrowRightOutlined />}
                  disabled={!isEditable}
                  onClick={() => {
                    formRefShipping.current?.setFieldsValue(formRefBilling.current?.getFieldsValue() || {});
                  }}
                />
                <Button
                  size="small"
                  type="default"
                  title="Switch Street and Company"
                  icon={<SwapOutlined />}
                  disabled={!isEditable}
                  onClick={() => {
                    const company = formRefBilling.current?.getFieldValue('company');
                    const street0 = formRefBilling.current?.getFieldValue(['street', 0]);
                    formRefBilling.current?.setFieldValue(['street', 0], company);
                    formRefBilling.current?.setFieldValue(['company'], street0);
                  }}
                />
              </Space>
            }
            bordered={false}
          >
            <UpdateAddressForm
              formRef={formRefBilling}
              initialValues={{
                ...billingAddress,
              }}
              orderEntityId={sn(props.initialValues?.entity_id)}
              orderStatus={props.initialValues?.status || ''}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card
            size="small"
            title={
              <Space size={16}>
                <span>Delivery address</span>
                <Button
                  size="small"
                  type="primary"
                  ghost
                  title="Set Invoice as Delivery"
                  icon={<ArrowLeftOutlined />}
                  disabled={!isEditable}
                  onClick={() => {
                    formRefBilling.current?.setFieldsValue(formRefShipping.current?.getFieldsValue() || {});
                  }}
                />
                <Button
                  size="small"
                  type="default"
                  title="Switch Street and Company"
                  icon={<SwapOutlined />}
                  disabled={!isEditable}
                  onClick={() => {
                    const company = formRefShipping.current?.getFieldValue('company');
                    const street0 = formRefShipping.current?.getFieldValue(['street', 0]);
                    formRefShipping.current?.setFieldValue(['street', 0], company);
                    formRefShipping.current?.setFieldValue(['company'], street0);
                  }}
                />
              </Space>
            }
            bordered={false}
          >
            <UpdateAddressForm
              formRef={formRefShipping}
              initialValues={{
                ...shippingAddress,
              }}
              orderEntityId={sn(props.initialValues?.entity_id)}
              orderStatus={props.initialValues?.status || ''}
            />
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default UpdateAddressesModal;
