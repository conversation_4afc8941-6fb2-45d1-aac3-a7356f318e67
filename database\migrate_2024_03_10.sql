ALTER TABLE `trademark`
    CHANGE `mag_id` `mag_id` INT (11) NULL COMMENT 'Magento ID',
    CHAN<PERSON> `name_pdf` `name_pdf` VARCHAR (255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Name in PDF Catalogue',
    <PERSON>AN<PERSON> `sort_pdf` `sort_pdf` INT (11) DEFAULT 99999999 NULL COMMENT 'Sort no in PDF Catalogue',
    ADD COLUMN `logo_file_id` BIGINT NULL COMMENT 'FK: ID of logo file' AFTER `sort_pdf`;



ALTER TABLE `trademark` CHANGE `logo_file_id` `logo_file_id` BIGINT(20) UNSIGNED NULL COMMENT 'FK: ID of logo file', ADD CONSTRAINT `FK_trademark_logo_file_id` FOREIGN KEY (`logo_file_id`) REFERENCES `file`(`id`) ON UPDATE CASCADE ON DELETE SET NULL;
