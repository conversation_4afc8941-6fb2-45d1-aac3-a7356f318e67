import { ProductWebsiteShortCode } from '@/constants';
import { csv2Arr, sn } from '@/util';
import { Tag } from 'antd';

export type WebsiteIconsProps = {
  ean_id?: number;
  product_websites?: number[] | string; // sys website ids
  website_ids?: string | number[]; // magento website ids
};

const WebsiteIcons: React.FC<WebsiteIconsProps> = ({ website_ids, product_websites, ean_id }) => {
  // Safe converting to array

  const website_ids_arr = csv2Arr(website_ids);

  const product_websites_arr = csv2Arr(product_websites);

  return sn(product_websites?.length) > 0 ? (
    <div style={{ marginLeft: '-2px' }}>
      {(product_websites_arr as number[])?.map?.((x) =>
        x <= 2 ? (
          <Tag
            key={x}
            color={website_ids_arr.find((e: any) => e == x) ? '#87d068' : 'error'}
            style={{ marginLeft: 2 }}
            className="website-icon"
          >
            {ProductWebsiteShortCode[x] ?? '-'}
          </Tag>
        ) : (
          <span
            key={x}
            style={{ color: website_ids_arr.find((e: any) => e == x) ? 'green' : '#ff4d4f', marginLeft: 2 }}
            className="website-icon"
          >
            {ProductWebsiteShortCode[x] ?? x}
          </span>
        ),
      )}
    </div>
  ) : null;
};

export default WebsiteIcons;
