CREATE TABLE `stock_stable_problem`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `stock_stable_id` bigint(20) unsigned DEFAULT NULL COMMENT 'Parent ID: PK in stock_stable table. Referenced ID',
    `item_id`         bigint(20) unsigned DEFAULT NULL,
    `ean_id`          bigint(20) unsigned DEFAULT NULL,
    `parent_ean_id`   bigint(20) unsigned DEFAULT NULL,
    `wl_id`           bigint(20) unsigned DEFAULT NULL,
    `piece_qty`       int(11)             DEFAULT 0,
    `box_qty`         int(11)             DEFAULT 0,
    `case_qty`        int(11)             DEFAULT 0,
    `total_piece_qty` int(11)             DEFAULT 0 COMMENT 'piece_qty + box_qty * case_qty',
    `exp_date`        date                DEFAULT NULL,
    `order_id`        int(11)             DEFAULT NULL COMMENT 'Order ID',
    `order_item_id`   int(11)             DEFAULT NULL COMMENT 'Order Item ID',
    `sku`             varchar(50)         DEFAULT NULL COMMENT 'SKU',
    `qty_ordered`     int(11)             DEFAULT NULL COMMENT 'Ordered quantity',
    `picklist_id`     int(11)             DEFAULT NULL COMMENT 'Picklist ID',
    `status`          varchar(31)         DEFAULT NULL,
    `note`            text                DEFAULT NULL,
    `detail`          text                DEFAULT NULL,
    `created_on`      datetime            DEFAULT current_timestamp(),
    `created_by`      bigint(20) unsigned DEFAULT NULL,
    `updated_on`      datetime            DEFAULT NULL,
    `updated_by`      bigint(20)          DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_stock_stable_problem_stock_stable_id` (`stock_stable_id`),
    KEY `FK_stock_stable_problem_item_id` (`item_id`),
    KEY `FK_stock_stable_problem_ean_id` (`ean_id`),
    KEY `FK_stock_stable_problem_parent_ean_id` (`parent_ean_id`),
    KEY `FK_stock_stable_problem_wl_id` (`wl_id`),
    KEY `IDX_stock_stable_problem_exp_date` (`exp_date`),
    KEY `IDX_stock_stable_problem_order_item_id` (`order_item_id`),
    KEY `IDX_stock_stable_problem_sku` (`sku`),
    KEY `IDX_stock_stable_problem_order_id` (`order_id`),
    KEY `IDX_stock_stable_problem_picklist_id` (`picklist_id`),
    KEY `IDX_stock_stable_problem_updated_on` (`updated_on`),
    CONSTRAINT `FK_stock_stable_problem_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_problem_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_problem_parent_ean_id` FOREIGN KEY (`parent_ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_problem_stock_stable_id` FOREIGN KEY (`stock_stable_id`) REFERENCES `stock_stable` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_stock_stable_problem_wl_id` FOREIGN KEY (`wl_id`) REFERENCES `warehouse_location` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;