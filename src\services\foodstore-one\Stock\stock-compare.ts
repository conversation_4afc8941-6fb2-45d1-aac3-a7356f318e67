/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/stock/stock-compare';

/** rule GET /api/stock/stock-compare */
export async function getStockCompareList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** rule GET /api/stock/stock-compare/ds/inventory-stock */
export async function dsMagentoInventoryStock(params?: any) {
  return request<API.BaseResult>(`${urlPrefix}/ds/inventory-stock`, {
    method: 'GET',
    withToken: true,
    params,
    paramsSerializer,
  }).then((res) => res.message);
}
