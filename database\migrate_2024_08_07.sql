CREATE TABLE `trademark_group`
(
    `id`   INT          NOT NULL AUTO_INCREMENT,
    `name` VA<PERSON>HAR(255) NOT NULL,
    PRIMARY KEY (`id`)
);
ALTER TABLE `trademark`
    ADD COLUMN `group_id` INT NULL COMMENT 'Trademark Group ID' AFTER `id`,
    ADD CONSTRAINT `FK_trademark_group_id` FOREIGN KEY (`group_id`) REFERENCES `trademark_group` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;


ALTER TABLE `trademark_group`
    ADD UNIQUE INDEX `UQ_trademark_group_name` (`name`);
