{
  "compilerOptions": {
    "outDir": "build/dist",
    "module": "esnext",
    "target": "esnext",
    "lib": [
      "esnext",
      "dom"
    ],
    "sourceMap": true,
    "baseUrl": ".",
    "jsx": "react-jsx",
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    // "ignoreDeprecations": "5.0",
    // "suppressImplicitAnyIndexErrors": true,
    "noUnusedLocals": true,
    "allowJs": true,
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "strict": true,
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@@/*": [
        "./src/.umi/*"
      ],
      "react": [
        "./node_modules/@types/react"
      ]
    }
  },
  "include": [
    "mock/**/*",
    "src/**/*",
    "playwright.config.ts",
    "tests/**/*",
    "test/**/*",
    "__test__/**/*",
    "typings/**/*",
    "config/**/*",
    ".eslintrc.js",
    ".stylelintrc.js",
    ".prettierrc.js",
    "jest.config.js",
    "mock/*"
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "scripts",
    "src/.umi/*",
    "webpack",
    "jest"
  ]
}