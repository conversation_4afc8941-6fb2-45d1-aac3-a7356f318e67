import { Drawer, Space, Tag } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';
import { getEmailList } from '@/services/foodstore-one/Email/email';
import ViewEmail from './ViewEmail';
import type { ProFormInstance } from '@ant-design/pro-form';
import { Link, useModel } from 'umi';
import { EyeOutlined } from '@ant-design/icons';
import EmailBoxTypeIcon from './EmailBoxTypeIcon';
import AttachmentIconIcon from './AttachmentIcon';

type RelatedEmailsListProps = {
  nEmailId?: number; // not where
  caseId?: number;
  refreshTick?: number;
};

const RelatedEmailsList: React.FC<RelatedEmailsListProps> = (props) => {
  const { getDictByCode } = useModel('app-settings');

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Email>();

  // Search
  const searchFormRef = useRef<ProFormInstance>();

  const columns: ProColumns<API.Email>[] = [
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 20,
      render: (_, record) => [
        <Link key={'link'} to={`/email/detail/${record.id}`} target="_blank">
          <EyeOutlined />
        </Link>,
      ],
    },
    {
      title: '',
      dataIndex: 'box',
      sorter: false,
      width: 20,
      hideInSearch: true,
      render(dom, record, index, action, schema) {
        return <EmailBoxTypeIcon box={record.box} />;
      },
    },
    {
      title: 'From / To',
      dataIndex: 'sender',
      sorter: true,
      ellipsis: true,
      width: 130,
      className: 'text-sm',
      render(dom, record, index, action, schema) {
        return (
          <Space direction="vertical" size={0}>
            <div className="">{record.sender}</div>
            <div className="">{record.receiver}</div>
          </Space>
        );
      },
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      sorter: true,
      ellipsis: true,
      hideInSearch: false,
      width: 200,
      render: (dom, record) => (
        <>
          <a
            onClick={() => {
              setCurrentRow(record);
              setShowDetail(true);
            }}
          >
            {record.subject}
          </a>
        </>
      ),
    },
    {
      title: '',
      dataIndex: 'attachments',
      sorter: false,
      width: 20,
      hideInSearch: true,
      render(__, record) {
        return <AttachmentIconIcon id={record.id} attachments={record.attachments} />;
      },
    },
    /* {
      title: 'Body',
      dataIndex: 'text_plain',
      sorter: true,
      ellipsis: true,
      render: (dom, record) => (
        <>
          <a
            onClick={() => {
              setCurrentRow(record);
              setShowDetail(true);
            }}
          >
            {record.text_plain}
          </a>
        </>
      ),
    }, */
    {
      title: 'Date',
      sorter: true,
      dataIndex: 'date',
      valueType: 'dateTime',
      search: false,
      ellipsis: true,
      width: 80,
      defaultSortOrder: 'descend',
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.date),
    },
    {
      title: 'Case Status',
      dataIndex: ['crm_case', 'status'],
      width: 100,
      ellipsis: true,
      className: 'bl2 b-grey',
      render: (dom, record) =>
        record.crm_case?.status ? (
          <Tag style={{ fontSize: 10 }}>{getDictByCode(record.crm_case.status) ?? dom}</Tag>
        ) : null,
    },
    {
      title: 'Case ID',
      dataIndex: ['crm_case_id'],
      width: 50,
    },
    /* {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      search: false,
      ellipsis: true,
      render: (dom, record) => (record.status ? getDictByCode(record.status) ?? dom : null),
    }, */
  ];

  useEffect(() => {
    if (props.refreshTick) {
      actionRef.current?.reload();
    }
  }, [props.refreshTick]);

  useEffect(() => {
    if (props.caseId) {
      actionRef.current?.reload();
    }
  }, [props.caseId]);

  return (
    <>
      <ProTable<API.Email, API.PageParams>
        headerTitle={'Email list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          pageSize: 20,
        }}
        request={async (params, sort, filter) => {
          if (!props.caseId) return Promise.resolve([]);
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          return getEmailList(
            {
              ...params,
              ...searchFormValues,
              with: 'crmCase,order,parcel',
              crm_case_id: props.caseId,
            },
            Object.keys(sort).length < 1 ? { date: 'descend' } : sort,
            filter,
          ).finally(() => {});
        }}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        columnEmptyText=""
        scroll={{ x: 550 }}
        tableStyle={{ border: '1px solid #eee' }}
        cardProps={{ bodyStyle: { padding: '0' } }}
      />

      <Drawer
        width={'600px'}
        open={showDetail}
        placement="bottom"
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && <ViewEmail email={currentRow} />}
      </Drawer>
    </>
  );
};

export default RelatedEmailsList;
