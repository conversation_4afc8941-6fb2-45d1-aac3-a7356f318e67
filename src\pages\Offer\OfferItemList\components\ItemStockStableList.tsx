import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { getStockStable } from '@/services/foodstore-one/Stock/stock-stable';
import { DEFAULT_PER_PAGE_PAGINATION, StockStableStatusOptions } from '@/constants';
import Util, { ni } from '@/util';
import _ from 'lodash';

type ItemStockStableListPropsType = {
  item_id?: number;
  ean_id?: number;
  perPage?: number;
};

const ItemStockStableList: React.FC<ItemStockStableListPropsType> = (props) => {
  const { item_id, ean_id, perPage } = props;

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.StockStable>[] = [
    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 130,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      width: 100,
    },
    {
      title: 'Warehouse',
      dataIndex: ['warehouse_location', 'name'],
      width: 80,
      sorter: true,
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      width: 100,
      align: 'center',
      sorter: true,
      defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },

    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 70,
      align: 'right',
      className: 'c-grey bl2',
      editable: false,
    },
    {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      tooltip: 'Click to view stock detail.',
      render: (dom, record) => ni(record.box_qty),
    },
    {
      title: 'Total Piece Qty',
      dataIndex: ['total_piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      tooltip: 'Click to view stock detail.',
      render: (dom, record) => ni(record.total_piece_qty),
    },
    {
      title: 'Updated on',
      dataIndex: ['updated_on'],
      sorter: true,
      width: 120,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 100,
      editable: false,
      align: 'center',
      tooltip: 'Click to change.',
      render: (dom, record, index, action) => {
        return StockStableStatusOptions.find((x) => x.value == record.status)?.label;
      },
    },
  ];

  useEffect(() => {
    if (item_id || ean_id) {
      actionRef.current?.reload();
    }
  }, [item_id, ean_id]);

  return (
    <ProTable<API.StockStable, API.PageParams>
      headerTitle={'Stock Warehouse'}
      actionRef={actionRef}
      rowKey="id"
      revalidateOnFocus={false}
      options={{ fullScreen: false, reload: true, density: false, search: false, setting: false }}
      search={false}
      sticky
      bordered={true}
      size="small"
      scroll={{ x: 800 }}
      pagination={{ pageSize: perPage ?? 10, hideOnSinglePage: true }}
      rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
      request={async (params, sort, filter) => {
        setLoading(true);
        return getStockStable(
          {
            ...params,
            item_id: item_id,
            ean_id: ean_id,
          },
          sort,
          filter,
        )
          .then((res) => {
            return res;
          })
          .finally(() => setLoading(false));
      }}
      columns={columns}
      tableAlertRender={false}
      columnEmptyText=""
      locale={{ emptyText: <></> }}
      cardProps={{
        headStyle: { padding: 0 },
        bodyStyle: { padding: 0 },
      }}
    />
  );
};

export default ItemStockStableList;
