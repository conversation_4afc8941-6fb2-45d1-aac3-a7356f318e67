import Util, { ni, sn } from '@/util';
import ProTable from '@ant-design/pro-table';
import { Popover } from 'antd';
import { useEffect, useState } from 'react';

export type TimeTracksPopoverProps = {
  title?: string;
  picklist: Partial<API.WarehousePicklist>;
  timeTracks?: API.WarehousePicklistTimeTrack[];
};

const TimeTracksPopover: React.FC<TimeTracksPopoverProps> = ({ picklist, timeTracks }) => {
  const [dataSource, setDataSource] = useState<API.WarehousePicklistTimeTrack[]>([]);

  useEffect(() => {
    if (timeTracks?.length) {
      const sumRow: API.WarehousePicklistTimeTrack = {
        id: -1,
        minute_total: 0,
      };
      const ds = [];
      timeTracks.forEach((x) => {
        x.minute_total = sn(x.minute) * sn(x.qty_employee);
        ds.push(x);
        sumRow.minute_total = sn(sumRow.minute_total) + x.minute_total;
      });
      if (sumRow.minute_total) {
        ds.push(sumRow);
      }
      setDataSource(ds);
    } else {
      setDataSource([]);
    }
  }, [timeTracks]);

  if (!timeTracks?.length) return null;

  const latestTrack = dataSource?.[0];

  return latestTrack ? (
    <Popover
      title={`Time Logs - Picklist #${picklist.id} ${picklist.note}`}
      content={
        <ProTable<API.WarehousePicklistTimeTrack>
          rowKey="id"
          size="small"
          search={false}
          options={false}
          columns={[
            {
              title: 'Start',
              dataIndex: 'start_datetime',
              align: 'center',
              width: 110,
              render(__, entity) {
                return sn(entity.id) < 0 ? 'Total' : Util.dtToDMYHHMM(entity.start_datetime);
              },
            },
            {
              title: 'End',
              dataIndex: 'end_datetime',
              align: 'center',
              width: 110,
              render(__, entity) {
                if (sn(entity.id) < 0) return null;
                return entity.end_datetime ? Util.dtToDMYHHMM(entity.end_datetime) : '~';
              },
            },
            {
              title: 'Minutes',
              dataIndex: 'minute',
              align: 'center',
              width: 90,
              render: (__, entity) => ni(entity.minute),
            },
            {
              title: 'Qty. Employee',
              dataIndex: 'qty_employee',
              align: 'center',
              width: 90,
            },
            {
              title: 'Total Minutes',
              dataIndex: 'minute_total',
              align: 'center',
              width: 90,
              render: (__, entity) => ni(entity.minute_total),
            },
          ]}
          pagination={{
            defaultPageSize: 200,
            hideOnSinglePage: true,
          }}
          dataSource={dataSource}
          columnEmptyText={''}
        />
      }
    >
      <div>
        {`${Util.dtToHHMM(latestTrack.start_datetime)} ~ ${
          latestTrack.end_datetime ? Util.dtToHHMM(latestTrack.end_datetime) : ''
        }${sn(latestTrack.qty_employee || 1) > 1 ? ` (${latestTrack.qty_employee})` : ''}`}
      </div>
    </Popover>
  ) : null;
};

export default TimeTracksPopover;
