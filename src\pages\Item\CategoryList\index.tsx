import { But<PERSON>, message, Card, Row, Col, Popconfirm } from 'antd';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';

import Util from '@/util';
import { getCategoryList, deleteCategory, addCategory, updateCategory } from '@/services/foodstore-one/Item/category';
import { Tree } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDigit, ProFormText, ProFormTreeSelect } from '@ant-design/pro-form';
import { FolderOutlined } from '@ant-design/icons';

const getParentKey = (key: number | null, tree: any): any => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.map((item: any) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

const getCategoryTitle = (key: number | null, tree: any): any => {
  let title;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.key == key) {
      title = node.title;
      break;
    } else {
      if (node.children.length > 0) {
        title = getCategoryTitle(key, node.children);
        if (title) {
          break;
        }
      }
    }
  }
  return title;
};

const Category: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const updateFormRef = useRef<ProFormInstance>();

  const [treeData, setTreeData] = useState<DataNode[]>([]);

  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  const onSelect = (selectedKeysParam: React.Key[], info: any) => {
    console.log('selected', selectedKeysParam, info);
    if (selectedKeysParam.length < 1) return;
    formRef.current?.setFieldsValue({
      parent_id: { value: info.selectedNodes[0].key, label: info.selectedNodes[0].title },
    });
    updateFormRef.current?.setFieldsValue({
      id: info.selectedNodes[0].key,
      parent_id: {
        value: info.selectedNodes[0].parent_id,
        label: getCategoryTitle(info.selectedNodes[0].parent_id, treeData),
      },
      foreign_id: info.selectedNodes[0].foreign_id,
      name: info.selectedNodes[0].title,
      sort: info.selectedNodes[0].sort,
    });
  };

  const handleAdd = async (values: any) => {
    const hide = message.loading('Adding...');
    const data = { ...values, parent_id: values.parent_id?.value };

    try {
      await addCategory(data);
      await reloadTree();
      message.success('Added successfully');
      formRef.current?.resetFields();
      return true;
    } catch (error: any) {
      Util.error(error);
      return false;
    } finally {
      hide();
    }
  };

  const handleUpdate = async (values: any) => {
    if (isNaN(Number(values.id))) {
      message.error('Please select a category on the left side.');
      return;
    }
    const hide = message.loading('Updating...', 0);
    const data = { ...values, parent_id: values.parent_id.value };
    try {
      await updateCategory(data);
      await reloadTree();
      hide();
      message.success('Updated successfully');
      formRef.current?.resetFields();
      return true;
    } catch (error: any) {
      hide();
      Util.error('Updating failed, please try again!', error);
      return false;
    }
  };

  const handleRemove = async () => {
    const hide = message.loading('Deleting');
    const id = updateFormRef.current?.getFieldValue('id');

    if (!id) return true;
    try {
      await deleteCategory({
        id: id,
      });
      const res = await reloadTree();
      hide();
      message.success('Deleted successfully and will refresh soon');
      return true;
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  return (
    <PageContainer>
      <Card style={{ marginBottom: 20 }}>
        <ProForm<API.Category>
          layout="inline"
          onFinish={async (value: any) => {
            const success = await handleAdd(value);
          }}
          formRef={formRef}
          submitter={{
            render: (props: any) => {
              return [
                <Button type="primary" key="submit" onClick={() => props.form?.submit?.()}>
                  Create
                </Button>,
                <Button type="default" key="rest" onClick={() => props.form?.resetFields()}>
                  Reset
                </Button>,
              ];
            },
          }}
        >
          <ProFormTreeSelect
            label="Parent"
            name="parent_id"
            request={async () => reloadTree()}
            formItemProps={{ style: { width: 350 } }}
            fieldProps={{
              treeDefaultExpandAll: true,
              filterTreeNode: true,
              showSearch: true,
              autoClearSearchValue: true,
              treeData: treeData as any,
              treeNodeFilterProp: 'title',
              labelInValue: true,
              showArrow: true,
              treeLine: true,
            }}
          />

          <ProFormText
            name="name"
            label="Name"
            placeholder="Category name"
            rules={[
              {
                required: true,
                message: 'Name is required',
              },
            ]}
          />
          <ProFormText
            name="foreign_id"
            label="Foreign ID"
            placeholder="Category ID in 3rd party."
            wrapperCol={{ span: 12 }}
            formItemProps={{
              tooltip: '',
            }}
          />
        </ProForm>
      </Card>
      <Card>
        <Row>
          <Col span={12}>
            <Tree
              treeData={treeData}
              showLine
              onSelect={onSelect}
              defaultExpandAll
              autoExpandParent
              icon={<FolderOutlined color="yellow" />}
              titleRender={(nodeData) => <>{nodeData.title}</>}
            />
          </Col>
          <Col>
            <ProForm<API.Category>
              layout="horizontal"
              formRef={updateFormRef}
              onFinish={handleUpdate}
              submitter={{
                render: (props: any) => {
                  return (
                    <Row>
                      <Col style={{ width: 120 }} />
                      <Col>
                        <Button
                          type="primary"
                          key="submit"
                          onClick={() => props.form?.submit?.()}
                          style={{ marginRight: 12 }}
                        >
                          Update
                        </Button>
                        <Popconfirm title="Are you sure you want to delete?" onConfirm={handleRemove}>
                          <Button type="default" key="rest" onClick={() => {}}>
                            Delete
                          </Button>
                        </Popconfirm>
                      </Col>
                    </Row>
                  );
                },
              }}
            >
              <ProFormTreeSelect
                label="Parent"
                name="parent_id"
                request={async () => reloadTree()}
                formItemProps={{ style: { width: 350 } }}
                fieldProps={{
                  treeDefaultExpandAll: true,
                  filterTreeNode: true,
                  showSearch: true,
                  autoClearSearchValue: true,
                  treeData: treeData as any,
                  labelInValue: true,
                  treeNodeFilterProp: 'title',
                  showArrow: true,
                  treeLine: true,
                }}
                labelCol={{ style: { width: 120 } }}
              />
              <ProFormText
                name="name"
                label="Name"
                placeholder="Category name"
                rules={[
                  {
                    required: true,
                    message: 'Name is required',
                  },
                ]}
                labelCol={{ style: { width: 120 } }}
              />
              <ProFormDigit
                name="foreign_id"
                label="Foreign ID"
                placeholder="Foreign ID"
                formItemProps={{
                  tooltip: 'Please fill ID in 3rd party app.',
                }}
                labelCol={{ style: { width: 120 } }}
              />
              <ProFormDigit
                name="sort"
                label="Order"
                placeholder="Order"
                labelCol={{ style: { width: 120 } }}
                formItemProps={{ tooltip: 'Used for sorting in a tree.' }}
              />
              <div style={{ display: 'none' }}>
                <ProFormText name="id" />
              </div>
            </ProForm>
          </Col>
        </Row>
      </Card>
    </PageContainer>
  );
};

export default Category;
