import Util from '@/util';
import { LoadingOutlined } from '@ant-design/icons';
import type { ProFormFieldProps } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormRadio } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import _, { isArray } from 'lodash';
import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';

type EditableCellProps = {
  dataType: API.HtmlFieldType;
  defaultValue: any;
  children?: React.ReactNode;
  precision?: number; // Number only
  dataOptions?: any; // Select only
  fieldProps?: ProFormFieldProps;
  style?: any;
  isDefaultEditing?: boolean;
  triggerUpdate: (value: any, cancelEdit?: () => void, rollbackChange?: () => void) => Promise<void>;
} & Partial<ProFormSelectProps> &
  Partial<ProFormFieldProps>;

const EditableCell: React.FC<EditableCellProps> = ({
  dataType,
  defaultValue,
  triggerUpdate,
  children,

  isDefaultEditing,
  precision,
  dataOptions,
  fieldProps,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [changedValue, setChangedValue] = useState<any>(defaultValue);

  const inputRef = useRef<any>(null);

  useEffect(() => {
    setEditing(!!isDefaultEditing);
  }, [isDefaultEditing]);

  useEffect(() => {
    if (editing) {
      inputRef?.current?.focus();
    }
  }, [editing]);

  useEffect(() => {
    if (dataType == 'number2') {
      setChangedValue(defaultValue);
    }
  }, [editing, defaultValue, dataType]);

  const toggleEdit = () => {
    setEditing((prev) => !prev);
  };

  let editorNode = null;
  const defaultFormItemProps = { style: { ...restProps.style, marginBottom: 0 } };
  const defaultEditorFieldProps: any = {
    allowClear: false,
    ...fieldProps,
    ref: inputRef,
    defaultValue,
    size: 'small',
    onChange: (value: any) => {
      setChangedValue(value);
      if (dataType == 'switch' || dataType == 'select') {
        setLoading(true);
        triggerUpdate?.(
          value,
          () => setEditing(false),
          () => setChangedValue(defaultValue),
        ).finally(() => setLoading(false));
      }
    },
    onKeyDown: (e: any) => {
      if (Util.isEnterKey(e)) {
        inputRef.current.blur();
      }

      fieldProps?.onKeyDown?.(e);
    },
    onBlur: (e: any) => {
      if (dataType == 'switch') {
        if (!isDefaultEditing) {
          setEditing(false);
        }
        return;
      }
      let newValue = null;
      if (dataType == 'number' || dataType == 'number2') {
        newValue = changedValue;
      } else if (dataType == 'select') {
        newValue = changedValue;
      } else if (dataType == 'multiselect') {
        newValue = changedValue;
      } else {
        newValue = e.target.value;
      }

      if (defaultValue == newValue) {
        if (!isDefaultEditing) {
          setEditing(false);
        }
        return;
      }
      setLoading(true);
      triggerUpdate?.(
        newValue,
        () => setEditing(false),
        () => setChangedValue(defaultValue),
      ).finally(() => setLoading(false));
    },
  };

  switch (dataType) {
    case 'number':
      editorNode = (
        <SProFormDigit
          fieldProps={{
            ...defaultEditorFieldProps,
            precision: precision ?? 0,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    case 'number2':
      editorNode = (
        <SProFormDigit
          fieldProps={{
            ...defaultEditorFieldProps,
            value: changedValue,
            precision: precision ?? 0,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    case 'select':
    case 'multiselect':
      editorNode = (
        <ProFormSelect
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          mode={dataType == 'multiselect' ? 'multiple' : 'single'}
          formItemProps={{ ...defaultFormItemProps }}
          options={dataOptions}
          {...restProps}
        />
      );
      break;
    case 'switch':
      editorNode = (
        <ProFormSwitch
          fieldProps={{
            ...defaultEditorFieldProps,
            defaultChecked: isArray(defaultValue) ? !!defaultValue[0] : !!defaultValue,
          }}
          formItemProps={{ ...defaultFormItemProps }}
          {...restProps}
        />
      );
      break;
    case 'radio':
      editorNode = (
        <ProFormRadio.Group
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          options={dataOptions}
          formItemProps={{ ...defaultFormItemProps }}
          {...restProps}
        />
      );
      break;
    case 'checkbox':
      editorNode = (
        <ProFormCheckbox.Group
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          options={dataOptions}
          formItemProps={{ ...defaultFormItemProps }}
          {...restProps}
        />
      );
      break;
    case 'textarea':
      editorNode = (
        <ProFormTextArea
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    default:
      editorNode = (
        <ProFormText
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
  }

  const childNode = editing ? (
    <Spin spinning={loading} size="small" indicator={<LoadingOutlined />}>
      {editorNode}
    </Spin>
  ) : (
    <div className="editable-cell-value-wrap" style={{ ...(restProps.style ?? {}) }} onClick={toggleEdit}>
      {children}
    </div>
  );

  return childNode;
};

export default EditableCell;
