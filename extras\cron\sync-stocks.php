<?php
exit;
/**
 *
 * Down syncing Magento Stocks
 *
 * Sync stock qtys without buffer qty.
 *
 * @package     Cron job script.
 * @since       2023-01-31
 */

use App\Service\Magento\Stock\MagInventoryService;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';
/** @var \Slim\Container $container */

/** @var \App\Service\Magento\Stock\MagInventoryService $service */
$service = $container->get(MagInventoryService::class);

$service->dsInventoryItems();