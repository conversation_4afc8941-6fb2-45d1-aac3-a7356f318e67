import type { UnbookedQtyInPicklistType } from '@/services/foodstore-one/Warehouse/picklist';
import { getUnbookedQtysInPicklist } from '@/services/foodstore-one/Warehouse/picklist';
import Util from '@/util';
import { Alert } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion list of IBOM
 */
export default () => {
  const [loading, setLoading] = useState<boolean>(false);

  // selected ibom
  const [unbookedList, setUbookedList] = useState<UnbookedQtyInPicklistType[]>([]);

  const checkUnbookedQtyInPicklist = useCallback(async (params?: Record<string, any>) => {
    setLoading(true);
    return getUnbookedQtysInPicklist({ ...params })
      .then((res) => {
        setUbookedList(res);
        return res;
      })
      .catch(Util.error)
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    checkUnbookedQtyInPicklist();
  }, [checkUnbookedQtyInPicklist]);

  const alertsElements = useMemo(() => {
    return unbookedList.length ? (
      <Alert
        key="warn-def"
        message={
          <div style={{ maxWidth: 450, whiteSpace: 'initial', maxHeight: 50, overflowY: 'auto' }}>
            Picklist Not booked: <b>{unbookedList.length}</b>.{' '}
            <span className="text-sm">
              {unbookedList.map((x) => `#${x.picklist_id}: ${x.unbooked_qty}`).join(', ')}
            </span>
          </div>
        }
        type="error"
        style={{
          marginTop: 4,
          paddingTop: 2,
          paddingBottom: 2,
          border: '1px solid #f00',
          color: '#f00',
        }}
      />
    ) : null;
  }, [unbookedList]);

  return { checkUnbookedQtyInPicklist, alertsElements, unbookedList, loading };
};
