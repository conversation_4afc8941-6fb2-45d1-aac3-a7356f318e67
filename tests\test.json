{"billing_address": {"address_type": "billing", "city": "Bakum", "country_id": "DE", "email": "<EMAIL>", "entity_id": 15452, "firstname": "<PERSON><PERSON>", "lastname": "<PERSON><PERSON>", "parent_id": 7728, "postcode": "49456", "street": ["Zum See 3"], "telephone": "***********"}, "shipping_address": {"address_type": "shipping", "city": "Bakum", "country_id": "DE", "email": "<EMAIL>", "entity_id": 15451, "firstname": "<PERSON><PERSON>", "lastname": "<PERSON><PERSON>", "parent_id": 7728, "postcode": "49456", "street": ["Zum See 3"], "telephone": "***********"}, "payment": {"account_status": null, "additional_information": ["ebay", "CreditCard", "19-10916-27871", "2.46", null, "[{\"transaction_id\":\"*************\",\"sum\":18.78,\"fee\":0,\"transaction_date\":\"2023-12-12T22:11:28.456Z\"}]", "", "M2E Pro Payment"], "amount_ordered": 18.78, "amount_paid": 18.78, "base_amount_ordered": 18.78, "base_amount_paid": 18.78, "base_shipping_amount": 4.66, "base_shipping_captured": 4.66, "cc_last4": null, "entity_id": 7726, "last_trans_id": "*************", "method": "m2epropayment", "parent_id": 7728, "shipping_amount": 4.66, "shipping_captured": 4.66}, "extension_attributes": {"payment_additional_info": [{"key": "component_mode", "value": "ebay"}, {"key": "payment_method", "value": "CreditCard"}, {"key": "channel_order_id", "value": "19-10916-27871"}, {"key": "channel_final_fee", "value": "2.46"}, {"key": "cash_on_delivery_cost", "value": "null"}, {"key": "transactions", "value": "[{\"transaction_id\":\"*************\",\"sum\":18.78,\"fee\":0,\"transaction_date\":\"2023-12-12T22:11:28.456Z\"}]"}, {"key": "tax_id", "value": ""}, {"key": "method_title", "value": "M2E Pro Payment"}]}}