CREATE TABLE `sys_dict`
(
    `code`        varchar(32)  NOT NULL COMMENT 'UQ-Code',
    `type`        varchar(32)  NOT NULL COMMENT 'type: order type, order category',
    `label`       varchar(512) NOT NULL COMMENT 'code label',
    `status`      tinyint(1)            DEFAULT 1 COMMENT 'active status',
    `sort`        int(11)      NOT NULL DEFAULT 0 COMMENT 'sort value',
    `value`       longtext              DEFAULT NULL COMMENT 'extra value',
    `parent_code` varchar(32)           DEFAULT NULL COMMENT 'Parent Code FK',
    `desc`        text                  DEFAULT NULL COMMENT 'Description',
    `settings`    longtext              DEFAULT NULL COMMENT 'Extra setting in JSON',
    PRIMARY KEY (`code`),
    UNIQUE KEY `IDX_sys_dict_code` (`code`),
    KEY `IDX_sys_dict_type` (`type`),
    KEY `FK_sys_dict_parent_code` (`parent_code`),
    KEY `IDX_sys_dict_label` (`label`),
    CONSTRAINT `FK_sys_dict_parent_code` FOREIGN KEY (`parent_code`) REFERENCES `sys_dict` (`code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='system dictionary table';


ALTER TABLE `warehouse_picklist_detail`
    CHANGE `entity_id` `entity_id` INT(11) NOT NULL COMMENT 'Order ID',
    CHANGE `status` `order_status` VARCHAR(30) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Order status when creating this picklist.',
    ADD COLUMN `order_item_id` INT         NULL COMMENT 'OrderItem ID' AFTER `order_status`,
    ADD COLUMN `status`        VARCHAR(30) NULL COMMENT 'Picklist Item Status' AFTER `order_item_id`;

ALTER TABLE `warehouse_picklist_detail`
    ADD COLUMN `sku` VARCHAR(50) NULL AFTER `order_item_id`,
    ADD INDEX `IDX_warehouse_picklist_detail_order_item_id` (`order_item_id`),
    ADD INDEX `IDX_warehouse_picklist_detail_sku` (`sku`);

ALTER TABLE `warehouse_picklist_detail` CHANGE `entity_id` `order_id` INT(11) NOT NULL COMMENT 'Order ID';

truncate table `warehouse_picklist_detail`;

DELETE from `warehouse_picklist`;

