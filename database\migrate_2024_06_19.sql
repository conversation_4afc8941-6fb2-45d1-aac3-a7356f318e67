CREATE TABLE `ean_price_stable` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ean` varchar(100) DEFAULT NULL,
  `cur_price` decimal(20,4) DEFAULT NULL,
  `cur_import_id` bigint(20) unsigned DEFAULT NULL,
  `last_price` decimal(20,4) DEFAULT NULL,
  `last_import_id` bigint(20) unsigned DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT current_timestamp(),
  `updated_on` datetime DEFAULT NULL,
  `last_updated_on` datetime DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UQ_ean_price_stable_ean` (`ean`,`cur_import_id`),
  KEY `IDX_ean_price_stable_cur_import_id` (`cur_import_id`),
  KEY `IDX_ean_price_stable_last_import_id` (`last_import_id`),
  CONSTRAINT `FK_ean_price_stable_cur_import_id` FOREIGN KEY (`cur_import_id`) REFERENCES `import` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_ean_price_stable_last_import_id` FOREIGN KEY (`last_import_id`) REFERENCES `import` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


ALTER TABLE `item_ean`
    ADD COLUMN `ps_price` DECIMAL (20, 4) NULL COMMENT 'Current Price in ean_price_stable' AFTER `wish_note`,
    ADD COLUMN `ps_import_id` BIGINT NULL COMMENT 'Current XLS Import ID in ean_price_stable' AFTER `ps_price`,
    ADD COLUMN `ps_supplier_id` BIGINT NULL COMMENT 'Current XLS Supplier ID from ean_price_stable' AFTER `ps_import_id`,
    ADD COLUMN `ps_supplier_name` VARCHAR (255) NULL COMMENT 'Current XLS Supplier name from ean_price_stable' AFTER `ps_supplier_id`,
    ADD COLUMN `ps_updated_on` DATETIME NULL COMMENT 'Price updated date from ean_price_stable' AFTER `ps_supplier_name`;


ALTER TABLE `xmag_product`
    CHANGE `price` `price` DECIMAL (20, 4) NULL COMMENT 'FsOne Price (Gross)',
    ADD COLUMN `price_gfc` DECIMAL (20, 4) NULL COMMENT 'GFC Price (Gross)' AFTER `price`;

