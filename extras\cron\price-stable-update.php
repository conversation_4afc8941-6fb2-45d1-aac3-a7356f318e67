<?php
/**
 *
 * Update Price Stable with excluded EANs list
 *
 * - has a feature of singleton of cron. Lock file name which contains PID is "price-stable-update.lock"
 *
 * @usage       Run daily at 01:00 AM or 02:00 AM
 * @package     Cron job script.
 * @since       2025-03-28
 */

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Magento\MagSyncLog;
use App\Repository\Import\ImportRepository;
use Slim\Container;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

$lockFilePath = PRIVATE_DATA_PATH . DS.  "price-stable-update.lock";


Func::getLogger()->error("Price Stable Update PID: " . getmypid());
$logId = FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_PRICE_STABLE_UPDATE, 'Started');

/** @var Container $container */
/** @var ImportRepository $importRepo */
$importRepo = $container->get(ImportRepository::class);

// make logs to error with long processing status
MagSyncLog::query()
    ->where('name', MagSyncLog::NAME_PRICE_STABLE_UPDATE)
    ->whereIn('status', [MagSyncLog::STATUS_STARTED, MagSyncLog::STATUS_PROCESSING])
    ->where("created_on", '<', Func::dtDbDatetimeStr(Func::dtDateByOffset(time(), -1, true)))
    ->update(['status' => MagSyncLog::STATUS_ERROR]);

// singleton check from lock file.
if (DIRECTORY_SEPARATOR !== '\\') {
    try {
        if( $pidsOrFalse = Func::isProcessLocked($lockFilePath) ) {
            FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_PRICE_STABLE_UPDATE, json_encode($pidsOrFalse));
            die();
        }
    } catch (Exception $exception) {
        Func::getLogger()->error($exception->getMessage());
        throw $exception;
    }
}

try {
    $importRepo->storeEanPriceStable();
    FuncModel::updateMagSyncLog($logId, MagSyncLog::STATUS_SUCCESS, 'Updated successfully.');

} catch (Exception $exception) {
    Func::getLogger()->error($exception->getMessage() . $exception->getTraceAsString());
    FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_PRICE_STABLE_UPDATE, [
        'status' => MagSyncLog::STATUS_ERROR,
        'note' => $exception->getMessage(),
    ]);
}








