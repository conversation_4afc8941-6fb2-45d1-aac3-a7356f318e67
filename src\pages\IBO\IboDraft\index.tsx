import { message, Card, Button, Popconfirm, Space, Typography } from 'antd';
import type { KeyboardEvent } from 'react';
import React, { useState, useRef, useCallback, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, EditableFormInstance, ActionType } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';

import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormDependency,
  ProFormItem,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-form';
import SDatePicker from '@/components/SDatePicker';
import type { Rule } from 'antd/lib/form';
import { CheckOutlined, CopyOutlined, DeleteFilled, DeleteOutlined, SaveOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';
import { getEanDetail, updateEanAttributePartial } from '@/services/foodstore-one/Item/ean';
import {
  copyAndCreateIboDraft,
  createOrUpdateDraft,
  deleteIboDraft,
  getIboDraft,
  getIboDraftACList,
} from '@/services/foodstore-one/IBO/ibo-draft';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import { getWarehouseLocationACList } from '@/services/foodstore-one/warehouse-location';
import { useEffect } from 'react';
import EditableCell from '@/components/EditableCell';

const defaultItemEan: API.Ean = {
  id: undefined,
  attr_case_qty: undefined,
  parent: undefined,
  ean_texts: [],
};

type IboDraftDetailFormType = API.IboDraftDetail & {
  uid: React.Key;
  box_qty_all?: number;
  qty_all?: number;
  children?: IboDraftDetailFormType[];
};

type IboDraftFormType = {
  ibo_draft_details?: IboDraftDetailFormType[];
} & API.IboDraft;

const defaultListData: IboDraftDetailFormType[] = new Array(2).fill(2).map((_, index) => {
  return {
    uid: Date.now().toString() + '-' + index,
    item_ean: defaultItemEan,
  };
});

const IboDraft: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);
  const toolbarFormRef = useRef<ProFormInstance>();

  // Editable table form
  const editableFormRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => defaultListData.map((item) => item.uid));
  const [dataSource, setDataSource] = useState<IboDraftDetailFormType[]>(() => defaultListData);

  // Reference data
  const [warehouseLocations, setWarehouseLocations] = useState<any>(undefined);

  // ---------------------------------------------------------------------------------
  // Search EAN
  // ---------------------------------------------------------------------------------
  const handleSearchEan = async (v: string, cb: any) => {
    if (!v) {
      cb(null);
      return;
    }
    message.destroy();
    const hide = message.loading('Searching EAN...', 0);
    await getEanDetail({ eanExact: v, with: 'suppliers' })
      .then((res) => {
        cb(res);
      })
      .catch(() => {
        cb(null);
      })
      .finally(() => hide());
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearchEan = useCallback(
    debounce((newValue, cb) => handleSearchEan(newValue, cb), 330),
    [],
  );

  const hiddenColumns: ProColumns<IboDraftDetailFormType>[] = useMemo(
    () => [
      {
        dataIndex: ['item_ean'],
        hideInTable: false,
        hideInForm: false,
        width: 1,
        fieldProps: { hidden: true },
      },
      {
        dataIndex: ['uid'],
        hideInTable: false,
        hideInForm: false,
        width: 1,
        fieldProps: { hidden: true },
      },
    ],
    [],
  );

  const columns: ProColumns<IboDraftDetailFormType>[] = useMemo(
    () => [
      ...hiddenColumns,
      {
        title: 'Package EAN',
        dataIndex: ['item_ean', 'ean'],
        fieldProps: {
          placeholder: 'Package EAN',
        },
        formItemProps: (form, { rowIndex, entity, filteredValue }) => {
          const rules = [{ required: true, message: 'EAN is required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        width: 150,
        renderFormItem: (item, { recordKey, type, record }, form) => {
          return (
            <ProFormText
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                placeholder: 'Package EAN',
                onChange: (e: any) => {
                  // console.log('onChangeCapture', (e.target as any).value);
                  const curEan: string = e.target.value.replaceAll(/^[0 ]+/g, '');
                  // console.log(curEan, record?.item_ean?.ean ?? null);
                  debouncedHandleSearchEan(curEan, (eanData: API.Ean) => {
                    if (eanData) {
                      setDataSource((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.uid == record?.uid);
                        if (current) {
                          current.item_ean = {
                            id: eanData.id,
                            ean: eanData.ean,
                            attr_case_qty: eanData.attr_case_qty,
                            parent: eanData.parent,
                            ean_texts: eanData.ean_texts,
                          };
                        }
                        return newList;
                      });
                    } else {
                      setDataSource((prev) => {
                        const newList = [...prev];
                        const current = newList.find((x) => x.uid == record?.uid);
                        if (current) {
                          current.item_ean = {
                            ...defaultItemEan,
                            ean: curEan,
                          };
                        }
                        return newList;
                      });
                    }
                  });
                },
                onBlur: (e) => {},
              }}
            />
          );
        },
      },
      {
        title: 'Text of EAN',
        dataIndex: ['item_ean', 'ean_texts', 0, 'name'],
        fieldProps: {
          placeholder: 'Text of EAN',
        },
        formItemProps: (form, { rowIndex, entity, filteredValue }) => {
          const rules = [{ required: true, message: 'Text of EAN is required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        width: 200,
      },
      {
        title: 'Qty of Boxes',
        dataIndex: ['box_qty'],
        valueType: 'digit',
        sorter: false,
        align: 'right',
        width: 130,
        formItemProps: (form, { rowIndex }) => {
          const rules: Rule[] = [{ required: true, message: 'Qty of Boxes Required!' }];
          return {
            rules,
            hasFeedback: true,
          };
        },
        renderFormItem: (item, { defaultRender, ...rest }, form) => {
          return <SProFormDigit placeholder="Qty of Boxes" min={1} formItemProps={{ style: { marginBottom: 0 } }} />;
        },
      },
      {
        title: 'EXP. Date',
        dataIndex: ['exp_date'],
        valueType: 'date',
        sorter: false,
        align: 'right',
        width: 130,
        formItemProps: (form, { rowIndex }) => {
          const rules: Rule[] = [];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender, ...rest }, form) => {
          return <SDatePicker placeholder="EXP. Date" formItemProps={{ style: { marginBottom: 0 } }} />;
        },
      },
      {
        title: 'Pcs / Package',
        dataIndex: ['item_ean', 'attr_case_qty'],
        valueType: 'digit',
        sorter: false,
        align: 'right',
        width: 130,
        formItemProps: (form, { rowIndex }) => {
          const rules = [{ required: true, message: 'Qty Required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender, record }, form) => {
          if (record?.item_ean?.parent?.id) {
            return <ProFormItem ignoreFormItem>{Util.numberFormat(record.item_ean?.attr_case_qty)}</ProFormItem>;
          }
          return <SProFormDigit placeholder="Pcs / Package" formItemProps={{ style: { marginBottom: 0 } }} />;
        },
        render: (dom, record) => Util.numberFormat(record.item_ean?.attr_case_qty),
      },
      {
        title: 'Single EAN',
        dataIndex: ['item_ean', 'parent', 'ean'],
        valueType: 'text',
        sorter: false,
        width: 150,
        formItemProps: (form, { rowIndex }) => {
          const rules = [{ required: true, message: 'Single EAN Required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
        renderFormItem: (item, { defaultRender, record }, form) => {
          if (record?.item_ean?.parent?.id) {
            return <ProFormItem ignoreFormItem>{record.item_ean?.parent?.ean}</ProFormItem>;
          }
          return <ProFormText placeholder="Single EAN" formItemProps={{ style: { marginBottom: 0 } }} />;
        },
      },
      {
        title: 'Location',
        dataIndex: ['warehouse_location', 'id'],
        valueType: 'select',
        fieldProps: {
          options: warehouseLocations || [],
          showSearch: true,
          onKeyDownCapture: async (e: KeyboardEvent) => {
            if (formRef.current?.getFieldValue('type') != 1 && Util.isTabPressed(e)) {
              const htmlId = (e.target as any)?.id || '';
              const id = htmlId.replace('_warehouse_location_id', '');
              const dataSourceLocal = editableFormRef.current?.getRowsData?.() || [];

              if (id && dataSourceLocal[dataSourceLocal.length - 1].uid == id) {
                const newObj = {
                  uid: Date.now().toString() + '-' + dataSourceLocal.length,
                  item_ean: defaultItemEan,
                };
                setDataSource((prev) => [...prev, newObj]);
                setEditableRowKeys((prev) => [...prev, newObj.uid]);
              }
            }
          },
        },
        sorter: false,
        width: 150,
        formItemProps: (form, { rowIndex }) => {
          const rules = [{ required: true, message: 'Location Required!' }];
          return {
            rules,
            hasFeedback: false,
          };
        },
      },
      {
        title: 'Total Qty',
        dataIndex: ['qty'],
        valueType: 'digit',
        sorter: false,
        align: 'right',
        editable: false,
        width: 100,
        render: (dom, record) => {
          const qty = Util.safeInt(record.item_ean?.attr_case_qty) * Util.safeInt(record.box_qty);
          return Util.numberFormat(qty);
        },
      },
      {
        title: ' ',
        dataIndex: ['gap1'],
        valueType: 'text',
        sorter: false,
        editable: false,
        width: 60,
        render: (dom, record) => '',
      },
      {
        title: 'Total Boxes',
        dataIndex: ['box_qty_all'],
        valueType: 'digit',
        sorter: false,
        align: 'right',
        editable: false,
        width: 100,
        render: (dom, record, index) => {
          let total = 0,
            lastIndex = -1,
            cnt = 0;
          const dataSourceLocal = dataSource;
          dataSourceLocal.forEach((x, xInd) => {
            if (x?.item_ean?.ean && x.item_ean.ean == record.item_ean?.ean) {
              total += Util.safeInt(x.box_qty);
              cnt++;
              lastIndex = xInd;
            }
          });
          return cnt >= 1 && lastIndex == index ? Util.numberFormat(total) : '';
        },
      },
      {
        title: 'Total Qty',
        dataIndex: ['qty_all'],
        valueType: 'digit',
        sorter: false,
        align: 'right',
        editable: false,
        width: 100,
        render: (dom, record, index) => {
          let total = 0,
            lastIndex = -1,
            cnt = 0;
          // const dataSourceLocal = editableFormRef.current?.getRowsData?.() || [];
          const dataSourceLocal = dataSource;
          dataSourceLocal.forEach((x, xInd) => {
            if (x?.item_ean?.ean && x.item_ean.ean == record.item_ean?.ean) {
              total += Util.safeInt(x.box_qty) * Util.safeInt(x.item_ean.attr_case_qty);
              cnt++;
              lastIndex = xInd;
            }
          });
          return cnt >= 1 && lastIndex == index ? Util.numberFormat(total) : '';
        },
      },
      {
        title: 'Option',
        valueType: 'option',
        width: 80,
        align: 'center',
        render: (text, record, _, action) => {
          return null;
        },
      },
    ],
    [dataSource, debouncedHandleSearchEan, warehouseLocations, hiddenColumns],
  );

  /**
   * onChange Handler for Draft selection
   *
   * @param id string
   */
  const handleDraftSelectionChange = async (id: string | undefined | null, option?: any) => {
    setLoading(true);
    if (id) {
      getIboDraft(id)
        .then((res) => {
          if (res.ibo_draft_details) {
            formRef.current?.setFieldsValue({ id: res.id, type: res.type });
            toolbarFormRef.current?.setFieldsValue({ ibom: res.ibom });
            setDataSource(
              res.ibo_draft_details?.map?.(
                (x: API.IboDraftDetail) =>
                  ({
                    id: x.id,
                    uid: x.id,
                    ibo_draft_id: x.ibo_draft_id,
                    ean_id: x.ean_id,
                    case_qty: x.case_qty,
                    box_qty: x.box_qty,
                    qty: x.qty,
                    exp_date: x.exp_date,
                    ibom: x.ibom,
                    ibom_id: x.ibom_id,
                    warehouse_location_id: x.warehouse_location_id,
                    warehouse_location: x.warehouse_location,
                    price: x.price,
                    ean: x.ean,
                    parent_ean: x.parent_ean,
                    ean_name: x.ean_name,
                    item_ean: {
                      id: x.item_ean?.id,
                      ean: x.item_ean?.ean ?? x.ean,
                      attr_case_qty: x.item_ean?.attr_case_qty ?? x.case_qty,
                      parent: {
                        id: x.item_ean?.parent?.id,
                        ean: x.item_ean?.parent?.ean ?? x.parent_ean,
                      },
                      ean_texts: [
                        {
                          ean_id: x.item_ean?.ean_texts?.[0]?.name,
                          name: x.item_ean?.ean_texts?.[0]?.name ?? x.ean_name,
                        },
                      ],
                    },
                  } as IboDraftDetailFormType),
              ),
            );
            setEditableRowKeys(res.ibo_draft_details?.map?.((x) => x.id as React.Key));
          } else {
            formRef.current?.resetFields();
            toolbarFormRef.current?.resetFields();
            setDataSource(defaultListData);
            setEditableRowKeys(defaultListData?.map?.((x) => x.uid));
          }
          // console.log('Formatted value', formRef.current?.getFieldsFormatValue());
        })
        .catch((error) => Util.error('Failed to load data.', error))
        .finally(() => setLoading(false));
    } else {
      formRef.current?.resetFields();
      toolbarFormRef.current?.resetFields();
      setDataSource(defaultListData);
      setEditableRowKeys(defaultListData?.map?.((x) => x.uid));
      setLoading(false);
    }
    toolbarFormRef.current?.setFieldsValue({ draft_name: option?.label });
  };

  const handleIboDraftSubmit = async (mode: 'save' | 'book') => {
    if (mode == 'book') {
      await toolbarFormRef.current?.validateFields();
    }

    const formValues = formRef.current?.getFieldsValue() || {};
    const data = {
      ...formValues,
      mode,
      ibo_draft_details: dataSource,
      ...toolbarFormRef.current?.getFieldsValue(),
    };

    setLoading(true);
    return createOrUpdateDraft(data)
      .then((res) => {
        formRef.current?.resetFields();
        formRef.current?.setFieldsValue({ id: res.id });
      })
      .catch((error) => {
        Util.error(error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getWarehouseLocationACList({}).then((res) => {
      setWarehouseLocations(res);
    });
  }, []);

  return (
    <PageContainer>
      <Card>
        <ProForm<IboDraftFormType> layout="inline" formRef={formRef} submitter={false}>
          <ProFormSelect
            name={['id']}
            showSearch
            debounceTime={200}
            label="Draft"
            width={'md'}
            fieldProps={{
              onChange: (value, option) => {
                handleDraftSelectionChange(value, option);
              },
              optionItemRender(item) {
                return (
                  <Typography.Text type={item.type == 1 ? 'success' : undefined}>
                    {item.label} {item.type == 1 ? '(BOOKED)' : ''}
                  </Typography.Text>
                );
              },
            }}
            request={async (params) => {
              const res = await getIboDraftACList(params);
              return res;
            }}
          />
          <div style={{ display: 'none' }}>
            <ProFormText name={['type']} />
          </div>
          <ProFormDependency key={'ibo_draft_dep'} name={['id', 'type']}>
            {(depValues) => {
              const isDeletable = depValues.id && depValues.type == 0;
              return (
                <Space>
                  {depValues.type == 1 && (
                    <ProFormSwitch
                      width="xs"
                      name={['include_ibo']}
                      label="Include IBO?"
                      disabled={!isDeletable}
                      hidden={!isDeletable}
                      tooltip="Delete the related IBOs!"
                    />
                  )}
                  {isDeletable && (
                    <Popconfirm
                      title={<>Are you sure you want to remove the draft?</>}
                      okText="Yes"
                      cancelText="No"
                      disabled={!isDeletable}
                      onConfirm={() => {
                        const hide = message.loading(`Deleting ...`, 0);
                        deleteIboDraft({
                          id: depValues.id,
                          params: {
                            ...formRef.current?.getFieldsValue(),
                          },
                        })
                          .then((res) => {
                            message.success('Successfully deleted!');
                            formRef.current?.resetFields();
                          })
                          .catch((e) => {
                            message.error(e.message ?? 'Failed to delete!');
                          })
                          .finally(() => {
                            hide();
                          });
                      }}
                    >
                      <Button type="ghost" key="delete-draft" disabled={!isDeletable} icon={<DeleteOutlined />}>
                        Delete
                      </Button>
                    </Popconfirm>
                  )}
                  {depValues.id && (
                    <Popconfirm
                      title={<>Are you sure you want to copy and create the current draft?</>}
                      okText="Yes"
                      cancelText="No"
                      overlayStyle={{ maxWidth: 350 }}
                      onConfirm={() => {
                        const hide = message.loading(`Copying ...`, 500000);
                        copyAndCreateIboDraft(depValues.id)
                          .then((res) => {
                            message.success('Successfully copied and created!');
                            formRef.current?.resetFields();
                            formRef.current?.setFieldsValue({ id: res.id });
                          })
                          .catch((e) => {
                            message.error(e.message ?? 'Failed to copy!');
                          })
                          .finally(() => {
                            hide();
                          });
                      }}
                    >
                      <Button type="primary" key="copy-draft" icon={<CopyOutlined />}>
                        Copy & Create
                      </Button>
                    </Popconfirm>
                  )}
                </Space>
              );
            }}
          </ProFormDependency>
        </ProForm>
      </Card>
      <Card style={{ marginTop: 24 }}>
        <EditableProTable<IboDraftDetailFormType>
          editableFormRef={editableFormRef}
          actionRef={actionRef}
          rowKey="uid"
          sticky
          controlled
          debounceTime={200}
          cardProps={{
            style: { marginBottom: '2rem' },
            bodyStyle: { padding: 0 },
          }}
          scroll={{
            x: 600,
          }}
          style={{
            padding: 0,
          }}
          recordCreatorProps={
            formRef.current?.getFieldValue('type') == 1
              ? false
              : {
                  newRecordType: 'dataSource',
                  position: 'bottom',
                  creatorButtonText: 'Add new row',
                  record: (index: number, dataSource2: IboDraftDetailFormType[]) => {
                    return { uid: Date.now().toString() + '-' + index, item_ean: defaultItemEan };
                  },
                }
          }
          loading={loading}
          columns={columns}
          value={dataSource}
          onChange={setDataSource}
          toolBarRender={() => {
            return [
              <ProForm key="toolbar-form" formRef={toolbarFormRef} layout="inline" submitter={false}>
                <ProFormSelect
                  name={['ibom', 'id']}
                  showSearch
                  label="IBOM"
                  required
                  width={'sm'}
                  request={async (params) => {
                    const res = await getIBOManagementACList(params);
                    return res;
                  }}
                  rules={[
                    {
                      required: true,
                      message: 'IBOM is required',
                    },
                  ]}
                />
                <ProFormText name="draft_name" label="Draft Name" />
              </ProForm>,
              <Space key="toolbar-actions" style={{ width: 200, justifyContent: 'right' }}>
                <Button
                  type="primary"
                  key="save"
                  disabled={loading || formRef.current?.getFieldValue('type') == 1}
                  loading={loading}
                  icon={<SaveOutlined />}
                  onClick={() => {
                    handleIboDraftSubmit('save');
                  }}
                >
                  Save
                </Button>
                <Popconfirm
                  title={<>Are you sure you want to book?</>}
                  overlayStyle={{ maxWidth: 350 }}
                  okText="Yes"
                  cancelText="No"
                  disabled={loading || formRef.current?.getFieldValue('type') == 1}
                  onConfirm={() => {
                    const hide = message.loading(`Booking ...`, 0);
                    // validation
                    if (dataSource.length < 1) {
                      message.error('No data!');
                      hide();
                      return;
                    }
                    editableFormRef.current
                      ?.validateFields()
                      .then((values: any) => {
                        const hideInner = message.loading(`Booking ...`, 0);
                        handleIboDraftSubmit('book')
                          .then((res) => {
                            message.success('Successfully booked!');
                          })
                          .catch((e) => {
                            Util.error(e);
                          })
                          .finally(() => {
                            hideInner();
                          });
                      })
                      .catch((error: any) => {
                        if (error.errorFields) {
                          let errors: any[] = [];
                          if (error.errorFields.length > 0) {
                            error.errorFields.forEach((x: any) => {
                              if (x.errors) {
                                errors = [...errors, ...x.errors];
                              }
                            });
                          }
                          // message.error('Data validation error! Please check form data!');
                          if (errors && errors.length) {
                            message.error(
                              errors.map((e, index) => <div key={e}>{e}</div>),
                              8,
                            );
                          } else {
                            message.error('Data validation error! Please check form data!');
                          }
                        } else {
                          Util.error(error);
                        }
                      })
                      .finally(() => hide());
                  }}
                >
                  <Button
                    type="primary"
                    key="book"
                    disabled={loading || formRef.current?.getFieldValue('type') == 1}
                    loading={loading}
                    icon={<CheckOutlined />}
                  >
                    {formRef.current?.getFieldValue('type') == 1 ? 'Rebook' : 'Book'}
                  </Button>
                </Popconfirm>
              </Space>,
            ];
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.delete];
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
            deleteText: <DeleteFilled />,
          }}
        />
      </Card>
      {/* <ProCard title="Debug dataSource" headerBordered collapsible defaultCollapsed>
        <ProFormField
          ignoreFormItem
          fieldProps={{
            style: {
              width: '100%',
            },
          }}
          mode="read"
          valueType="jsonCode"
          text={JSON.stringify(dataSource)}
        />
      </ProCard> */}
    </PageContainer>
  );
};

export default IboDraft;
