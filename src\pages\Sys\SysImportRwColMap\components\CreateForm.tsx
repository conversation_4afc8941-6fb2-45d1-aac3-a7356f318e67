import React, { useEffect, useRef } from 'react';
import { ModalForm, ProFormRadio, ProFormText, ProFormInstance, ProFormDigit } from '@ant-design/pro-form';

export type FormValueType = Partial<API.SysImportRwColMap>;

export type CreateFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<boolean>;
  createModalVisible: boolean;
  values?: Partial<API.SysImportRwColMap>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { onSubmit, onCancel, createModalVisible, values } = props;

  useEffect(() => {
    formRef.current?.setFieldsValue(values);
  }, [values]);

  return (
    <ModalForm
      title={values?.id ? 'Edit Import RW Column Map' : 'New Import RW Column Map'}
      width="640px"
      visible={createModalVisible}
      onVisibleChange={(visible) => !visible && onCancel()}
      layout="horizontal"
      labelCol={{ flex: '180px' }}
      formRef={formRef}
      initialValues={values}
      onFinish={async (value) => {
        const success = await onSubmit(value);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          onCancel();
        }
      }}
    >
      <ProFormText
        name="supplier_name_xls"
        label="Supplier Name (XLS)"
        width="lg"
        rules={[
          {
            required: true,
            message: 'Supplier Name (XLS) is required',
          },
        ]}
        placeholder="Enter supplier name as it appears in XLS files"
      />

      <ProFormRadio.Group
        name="supplier_name"
        label="Supplier Name (System)"
        width="lg"
        options={['RW', 'RW_D']}
        rules={[
          {
            required: true,
            message: 'Supplier Name (System) is required',
          },
        ]}
        placeholder="Enter supplier name as it appears in the system"
      />

      <ProFormText
        name="supplier_add"
        label="Supplier Add"
        width="sm"
        placeholder="Enter supplier additional identifier (optional)"
      />

      <ProFormDigit name="sort" label="Sort" width="xs" placeholder="Enter sort" />
    </ModalForm>
  );
};

export default CreateForm;
