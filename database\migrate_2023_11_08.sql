-- ===================================
-- Fill order extra info.
-- Will be used to default shipping setting for orders.
-- ===================================
insert into xmag_order_extra(entity_id, shipping_provider_name_old)
select entity_id,
       (select provider_name
        from xmag_shipping_desc_provider_map
        where shipping_desc = LEFT(xmag_order.shipping_description, 40)
        limit 1)
from xmag_order
where not exists(select 1 from xmag_order_extra t where t.entity_id = xmag_order.entity_id);

update xmag_order_extra set shipping_provider_name_old = (select
                                                              (select provider_name
                                                               from xmag_shipping_desc_provider_map
                                                               where shipping_desc = LEFT(o.shipping_description, 40)
                                                               limit 1)
                                                          from xmag_order o
                                                          where o.entity_id = xmag_order_extra.entity_id
                                                          limit 1)
where xmag_order_extra.shipping_provider_name_old is null;

update xmag_order_extra set shipping_provider_name = shipping_provider_name_old where shipping_provider_name is null;