import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import { getSupplierAddACList } from '@/services/foodstore-one/Import/import';
import SDatePicker from '@/components/SDatePicker';
import { updateImportEanDisabled } from '@/services/foodstore-one/Import/import-ean-disabled';

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateImportEanDisabled(id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.ImportEanDisabled>;

export type UpdateFormProps = {
  initialValues?: Partial<API.ImportEanDisabled>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.ImportEanDisabled) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;

  buyingHistoryComp?: JSX.Element;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm<FormValueType>
      title={'Update Disabled EAN'}
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      modalProps={{ okText: 'Update' }}
      onFinish={async (value) => {
        const success = await handleUpdate(sn(props.initialValues?.id), { ...value });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormSelect request={getSupplierAddACList} name="supplier_add" label="SUPPLIER_ADD" showSearch />
      <ProFormText name="ean" label="EAN" />
      <SDatePicker name="start_date" label="Start Date" />
      <SDatePicker name="end_date" label="End Date" />
    </ModalForm>
  );
};

export default UpdateForm;
