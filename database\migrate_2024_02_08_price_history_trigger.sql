drop trigger if exists item_ean_after_update;


-- ========================================================================
-- After Update trigger of `item_ean`
-- ========================================================================
DELIMITER $$

CREATE
    TRIGGER `item_ean_after_update`
    AFTER UPDATE
    ON `item_ean`
    FOR EACH ROW
BEGIN
    if IFNULL(NEW.fs_special_discount, 0) != ifnull(OLD.fs_special_discount, 0) then
        SET @price = (select price from ean_price where price_type_id = 1 and ean_id = NEW.id);
        SET @discount = IF(NEW.fs_special_discount is null, 0, CAST(NEW.fs_special_discount AS DOUBLE));

        set @lastId = (select id from item_ean_price_history where ean_id=NEW.id and `type`=2 order by id DESC limit 1);

        if @discount >= 0 then
            INSERT into item_ean_price_history(ean_id, price_type_id, `type`, price, created_on)
            values ( NEW.id, 1, 2, @price, NOW());
        else
            INSERT into item_ean_price_history(ean_id, price_type_id, `type`, price, created_on, detail)
            values ( NEW.id, 1, 2, @price * (100 + @discount) / 100, NOW()
                   , JSON_OBJECT(
                             'discount', @discount,
                             'basePrice', @price,
                             'fs_special_badge', NEW.fs_special_badge,
                             'discountLabel', NEW.fs_special_badge2
                         ));
        end if;

        if @lastId is not null then
            update item_ean_price_history set updated_on = now() where id=@lastId;
        end if;
    end if;
END$$

DELIMITER ;



