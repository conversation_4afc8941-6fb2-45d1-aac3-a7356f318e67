CREATE TABLE `warehouse_picklist_shipping_imported`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `picklist_id`       int(11) NOT NULL COMMENT 'Picklist ID',
    `order_id`          int(11) NOT NULL COMMENT 'Order ID',
    `parcel_no`         varchar(255) DEFAULT NULL COMMENT 'Parcel no',
    `status`            varchar(31)  DEFAULT NULL COMMENT 'Status',
    `status_date`       datetime     DEFAULT NULL COMMENT 'Status change date',
    `imported_date`     datetime     DEFAULT NULL COMMENT 'Imported datetime',
    `pickup_date`       date         DEFAULT NULL COMMENT 'Pickup date',
    `processing_status` varchar(255) DEFAULT NULL COMMENT 'Processing status note',
    `mag_ship_id`       int(11)      DEFAULT NULL COMMENT 'Shipment ID in Magento',
    PRIMARY KEY (`id`),
    KEY `IDX_FK_warehouse_picklist_shipping_imported_picklist_id` (`picklist_id`),
    KEY `IDX_warehouse_picklist_shipping_imported_order_id` (`order_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;