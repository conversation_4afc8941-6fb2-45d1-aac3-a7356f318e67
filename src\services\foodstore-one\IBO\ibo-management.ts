/* eslint-disable */
import Util from '@/util';
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/ibo/ibo-management';

/** get GET /api/ibo/ibo-management */
export async function getIBOManagementList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/ibo/ibo-management */
export async function updateIBOManagement(
  data: API.IBOManagement & { mode?: 'updateImportId' },
  options?: { [key: string]: any },
) {
  return request<API.IBOManagement>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/ibo/ibo-management */
export async function addIBOManagement(data: API.IBOManagement, options?: { [key: string]: any }) {
  return request<API.IBOManagement>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/ibo/ibo-management */
export async function deleteIBOManagement(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export const getIbomLabel = (x: API.IBOManagement, includeIbo?: boolean) => {
  return `#${x.order_no || '-'} | ${x.supplier_name ?? x.supplier?.name} - ${Util.dtToDMY(x.order_date) || 'N/A'}~${Util.dtToDMY(x.received_date) || 'N/A'
    }${x.notes ? ` - ${x.notes}` : ''}`;
}

/**
 * get GET /api/item/ac-list
 *
 * get the autocomplete lists.
 *
 */
export async function getIBOManagementACList(params: { [key: string]: string }, sort?: any) {
  return request<DefaultOptionType>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 100,
      sort_detail: JSON.stringify(!sort || !Object.keys(sort).length ? { order_no: 'descend' } : {}),
    },
    withToken: true,
  }).then((res) =>
    res.message.map((x: API.IBOManagement) => ({
      ...x,
      value: x.id,
      label: getIbomLabel(x),
    })),
  );
}
