DROP TABLE IF EXISTS	z_import_supplier_xls_ean;

CREATE TABLE `z_import_supplier_xls_ean` (
                                             `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                                             `ean` VARCHAR(100) DEFAULT NULL,
                                             `multi_ean` VARCHAR(50) DEFAULT NULL,
                                             `name` VARCHAR(255) DEFAULT NULL,
                                             `case_qty` DOUBLE DEFAULT 0,
                                             `hs_code` VARCHAR(30) DEFAULT NULL,
                                             `article_no` VARCHAR(255) DEFAULT NULL,
                                             `trademark` VARCHAR(255) DEFAULT NULL,
                                             `vat` VARCHAR(10) DEFAULT NULL,
                                             `price` DOUBLE DEFAULT 0,
                                             `uvp` DOUBLE DEFAULT 0,
                                             `price_pallet` DOUBLE DEFAULT 0,
                                             `ve_pallet` DOUBLE DEFAULT 0,
                                             `exp_date` VARCHAR(30) DEFAULT NULL,
                                             `maker` VARCHAR(255) DEFAULT NULL,
                                             `shelf_life` INT(11) DEFAULT NULL,
                                             `gln` VARCHAR(255) DEFAULT NULL,
                                             `price_valid_from` DATE DEFAULT NULL,
                                             `price_valid_to` DATE DEFAULT NULL,
                                             `bbd` DATE DEFAULT NULL,
                                             `category` VARCHAR(255) DEFAULT NULL,
                                             `nan` VARCHAR(255) DEFAULT NULL,
                                             `detail` TEXT DEFAULT NULL COMMENT 'Detail info in JSON',
                                             `import_id` BIGINT(20) DEFAULT NULL COMMENT 'Ref table ID: PK in import table',
                                             `import_ref_id` BIGINT(20) DEFAULT NULL COMMENT 'PK in ref_table',
                                             `created_on` DATETIME DEFAULT NULL,
                                             `updated_on` DATETIME DEFAULT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `UQ_z_import_supplier_xls_ean_ean` (`ean`),
                                             KEY `IDX_z_import_supplier_xls_ean_multi_ean` (`multi_ean`),
                                             KEY `IDX_z_import_supplier_xls_ean_import_id` (`import_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aggregated EANs from all XLS files';


ALTER TABLE `z_import_supplier_xls_ean` ADD INDEX `IDX_z_import_supplier_xls_ean_import_id` (`import_id`);


DELIMITER $$

DROP FUNCTION IF EXISTS `INITCAP`$$

CREATE FUNCTION `INITCAP`(str VARCHAR(8000)) RETURNS VARCHAR(8000) CHARSET utf8mb4 COLLATE utf8mb4_general_ci
BEGIN
    DECLARE len      INT DEFAULT LENGTH(str);
    DECLARE pos      INT DEFAULT 1;
    DECLARE ch       CHAR(1);
    DECLARE ch_ascii INT;

    DECLARE out_str VARCHAR(8000) DEFAULT '';
    DECLARE prev_alphanum INT DEFAULT 0;

    WHILE pos <= len
        DO
            SET ch = SUBSTRING(str, pos, 1);
            SET ch_ascii = ASCII(ch);

            IF prev_alphanum = 1 THEN
                SET out_str = CONCAT(RPAD(out_str, pos - 1), LOWER(ch));  -- RPAD is required to append ' '
            ELSE
                SET out_str = CONCAT(RPAD(out_str, pos - 1), UPPER(ch));
            END IF;

            IF ch_ascii <= 47 OR (ch_ascii BETWEEN 58 AND 64) OR
               (ch_ascii BETWEEN 91 AND 96) OR (ch_ascii BETWEEN 123 AND 126) THEN
                SET prev_alphanum = 0;
            ELSE
                SET prev_alphanum = 1;
            END IF;

            SET pos = pos + 1;
        END WHILE;

    RETURN out_str;

END$$

DELIMITER ;