import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import SProFormDigit from '@/components/SProFormDigit';

export type FormValueType = Partial<{ percentage?: number }>;

export type GfcPriceSettingFormModalProps = {
  initialValues?: FormValueType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit: (formData: FormValueType) => void;
};

/**
 * This component is not used!
 *
 * @param props UpdateFormProps
 * @returns React.FC
 */
const GfcPriceSettingFormModal: React.FC<GfcPriceSettingFormModalProps> = ({
  modalVisible,
  handleModalVisible,
  onSubmit,
  initialValues,
}) => {
  const gfcPriceSettingFormRef = useRef<ProFormInstance<FormValueType>>();

  useEffect(() => {
    if (modalVisible && initialValues?.percentage) {
      gfcPriceSettingFormRef?.current?.setFieldsValue(initialValues || {});
    }
  }, [initialValues, modalVisible]);

  return (
    <ModalForm
      title={'Set GFC Price Percentage'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={gfcPriceSettingFormRef}
      onFinish={async (value) => {
        onSubmit(value);
      }}
    >
      <SProFormDigit
        name="percentage"
        label="Percentage"
        width={120}
        required
        fieldProps={{
          precision: 2,
        }}
        addonAfter="%"
        help="New GFC Price = Supplier Price * {percentage} / 100"
      />
    </ModalForm>
  );
};

export default GfcPriceSettingFormModal;
