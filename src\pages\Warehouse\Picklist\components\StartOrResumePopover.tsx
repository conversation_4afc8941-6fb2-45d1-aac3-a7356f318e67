import { updatePicklist } from '@/services/foodstore-one/Warehouse/picklist';
import Util from '@/util';
import { ActionType } from '@ant-design/pro-table';
import { Button, Popover, message } from 'antd';
import { Dispatch, SetStateAction, useMemo, useState } from 'react';

const successMsgTplMap = {
  startTimeTrack: 'Started successfully.',
  resumeTimeTrack: 'Resumed successfully.',
  finishTimeTrack: 'Finished successfully.',
  pauseTimeTrack: 'Paused successfully.',
};

export type StartOrResumePopoverProps = {
  trackType: 0 | 1 | 2; // 0:pre-picking, 1: picking, 2: packing
  mode: 'startTimeTrack' | 'resumeTimeTrack' | 'finishTimeTrack' | 'pauseTimeTrack';
  picklistId: number;
  qtyEmployee?: number;
  setQtyEmployee?: Dispatch<SetStateAction<number>>;
  cb?: () => void; // callback function after selection action.
  actionRef: React.MutableRefObject<ActionType | undefined>; // For parent table reload
  btnType?: 'small';
};

const StartOrResumePopover: React.FC<StartOrResumePopoverProps> = ({
  trackType,
  mode,
  picklistId,
  qtyEmployee,
  setQtyEmployee,
  actionRef,
  cb,
  btnType: resumeBtnStyle,
}) => {
  const [open, setOpen] = useState<boolean>(false);

  const buttonTitle = useMemo(() => {
    if (mode == 'startTimeTrack') {
      return 'Start time track.';
    } else if (mode == 'resumeTimeTrack') {
      return 'Resume time track.';
    } else if (mode == 'pauseTimeTrack') {
      return 'Pause time track.';
    } else if (mode == 'finishTimeTrack') {
      return 'Finish time track.';
    }
    return '';
  }, [mode]);

  if (mode == 'pauseTimeTrack') {
    return (
      <Button
        type="primary"
        size="small"
        className="btn-yellow ant-btn-xs"
        title={buttonTitle}
        onClick={(e) => {
          const hide = message.loading('Stopping time tracking...');
          updatePicklist(picklistId, {
            mode,
            trackType,
          })
            .then((res) => {
              message.success(successMsgTplMap[mode]);
              actionRef.current?.reload();
            })
            .catch(Util.error)
            .finally(() => {
              hide();
            });
        }}
      >
        Pause
      </Button>
    );
  } else if (mode == 'finishTimeTrack') {
    return (
      <Button
        type="primary"
        size="small"
        className="btn-green ant-btn-xs"
        onClick={async () => {
          const hide = message.loading('Finishing time tracking...');
          updatePicklist(picklistId, {
            mode: mode,
            trackType,
          })
            .then((res) => {
              message.success(successMsgTplMap[mode]);
              actionRef.current?.reload();
            })
            .catch(Util.error)
            .finally(() => {
              hide();
            });
        }}
      >
        Finish
      </Button>
    );
  } else
    return (
      <Popover
        title="Select Qty. of Employee"
        trigger="click"
        open={open}
        onOpenChange={(visible) => {
          setOpen(visible);
        }}
        content={[1, 2, 3, 4, 5].map((x) => {
          return (
            <Button
              key={x}
              type={qtyEmployee == x ? 'primary' : 'default'}
              size="small"
              style={{ marginLeft: 4, marginRight: 4 }}
              onClick={() => {
                setOpen(false);
                setQtyEmployee?.(x);

                const hide = message.loading(
                  mode == 'resumeTimeTrack' ? 'Resuming time tracking...' : 'Starting time tracking...',
                );

                updatePicklist(picklistId, {
                  trackType,
                  mode: mode == 'resumeTimeTrack' || mode == 'startTimeTrack' ? 'startTimeTrack' : mode,
                  timeTrack: {
                    type: trackType,
                    qty_employee: x,
                  },
                })
                  .then((res) => {
                    message.success(successMsgTplMap[mode]);
                    if (cb) cb();
                    else {
                      actionRef.current?.reload();
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              {x}
            </Button>
          );
        })}
      >
        {resumeBtnStyle == 'small' ? (
          <Button type="primary" ghost size="small" title={buttonTitle} className="ant-btn-xs">
            {mode == 'startTimeTrack' ? 'S' : 'R'}
          </Button>
        ) : (
          <Button type="primary" size="small" title={buttonTitle} className="ant-btn-xs">
            {mode == 'startTimeTrack' ? 'Start' : 'Resume'}
          </Button>
        )}
      </Popover>
    );
};

export default StartOrResumePopover;
