<?php

use App\Lib\Func;
use App\Models\ItemEan;
use App\Models\Magento\MagProductAttributeSet;
use Illuminate\Support\Facades\DB;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

/** @var \App\Repository\Import\ImportRepository $repo */
$repo = Func::getContainer()->get(\App\Repository\Import\ImportRepository::class);


/** @var ItemEan $itemEan */
$itemEan = ItemEan::query()->where('sku', 'SKU_3_14_1')
    ->with(['eanTextDe'])
    ->firstOrFail();

$text = $itemEan->eanTextDe->description1;
var_dump($text);
var_dump(Func::removeNonAsciiCode($text));