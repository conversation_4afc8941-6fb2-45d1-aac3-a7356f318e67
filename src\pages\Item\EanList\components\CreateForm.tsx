import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEan } from '@/services/foodstore-one/Item/ean';
import { message } from 'antd';
import Util from '@/util';
import { getItemList } from '@/services/foodstore-one/Item/item';

const handleAdd = async (fields: API.Ean) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addEan(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New EAN'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Ean);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'EAN is required',
          },
        ]}
        width="md"
        name="ean"
        label="EAN"
      />
      <ProFormSelect
        showSearch
        placeholder="Select an Item"
        request={async (params) => {
          const res = await getItemList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Item) => ({
              label: `${x.id} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        rules={[
          {
            required: true,
            message: 'Item is required',
          },
        ]}
        width="md"
        name="item_id"
        label="Item"
      />
      <ProFormDigit
        width="sm"
        name="attr_case_qty"
        label="Qty per Case"
        min={1}
        addonAfter="g"
        fieldProps={{ precision: 0 }}
      />
      <ProFormDigit width="sm" name="weight" label="Weight" min={1} addonAfter="kg" fieldProps={{ precision: 3 }} />
      <ProFormDigit width="sm" name="width" label="Width" min={1} addonAfter="cm" fieldProps={{ precision: 1 }} />
      <ProFormDigit width="sm" name="height" label="height" addonAfter="cm" min={1} fieldProps={{ precision: 1 }} />
      <ProFormDigit width="sm" name="length" label="Length" min={1} addonAfter="cm" fieldProps={{ precision: 1 }} />
    </ModalForm>
  );
};

export default CreateForm;
