ALTER TABLE `xmag_order`
    ADD COLUMN `s_sys_category` VARCHAR(2)  NULL COMMENT 'Shipping System category: s,m,s1,m1,ms: Multi + Single' AFTER `sa_prefix`,
    ADD COLUMN `s_sys_status`   VARCHAR(31) NULL COMMENT 'Shipping System Status: new,labeled, packed, etc.' AFTER `s_sys_category`;

/*select xmag_order.entity_id,
       xmag_order.total_item_count,
       xmag_order.total_qty_ordered,
       t.*,
       (case
            when t.multi_qty = 1 AND t.single_qty = 0 then 'm1'
            when t.multi_qty > 1 AND t.single_qty = 0 then 'm'
            when t.multi_qty = 0 AND t.single_qty = 1 then 's1'
            when t.multi_qty = 0 AND t.single_qty > 1 then 's'
            else 'ms'
           end) AS s_sys_category
from xmag_order
         LEFT join (select xmag_order_item.order_id,
                           SUM(IF(item_ean.id = item_ean.parent_id, xmag_order_item.qty_ordered, 0)) as single_qty,
                           SUM(IF(item_ean.id = item_ean.parent_id, 0, xmag_order_item.qty_ordered)) as multi_qty,
                           SUM(xmag_order_item.qty_ordered)                                          as qty_ordered
                    from xmag_order_item
                             left join item_ean on item_ean.sku = xmag_order_item.sku
                    group by xmag_order_item.order_id) AS t on t.order_id = xmag_order.entity_id
;*/

update xmag_order
    LEFT join (select xmag_order_item.order_id,
                      SUM(IF(item_ean.id = item_ean.parent_id, xmag_order_item.qty_ordered, 0)) as single_qty,
                      SUM(IF(item_ean.id = item_ean.parent_id, 0, xmag_order_item.qty_ordered)) as multi_qty,
                      SUM(xmag_order_item.qty_ordered)                                          as qty_ordered
               from xmag_order_item
                        left join item_ean on item_ean.sku = xmag_order_item.sku
               group by xmag_order_item.order_id) AS t on t.order_id = xmag_order.entity_id
SET s_sys_category = (case
                          when t.multi_qty = 1 AND t.single_qty = 0 then 'm1'
                          when t.multi_qty > 1 AND t.single_qty = 0 then 'm'
                          when t.multi_qty = 0 AND t.single_qty = 1 then 's1'
                          when t.multi_qty = 0 AND t.single_qty > 1 then 's'
                          else 'ms'
    end)
;

