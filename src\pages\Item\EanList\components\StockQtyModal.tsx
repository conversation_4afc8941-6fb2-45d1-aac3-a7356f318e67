/* eslint-disable @typescript-eslint/dot-notation */
import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Card, Col, Row, Space, Spin, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import _ from 'lodash';
import SocialLinks from './SocialIcons';
import { getStockByEanId } from '@/services/foodstore-one/Stock/stock-movement';
import Util, { ni, sn } from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import StockCorrectionForm from './StockCorrectionForm';
import { useModel } from 'umi';
import { InfoCircleOutlined } from '@ant-design/icons';

export type FormValueType = Partial<API.Ean>;
export type StockQtyDataType = {
  total_row?: Partial<API.StockMovement>;
  rows_by_exp_date?: Partial<API.StockMovement & { oldest_ibo?: API.Ibo }>[];
  rows?: Partial<API.StockMovement>[];
  qty_processing: number;
};

export type StockQtyModalProps = {
  initialValues?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean, isParent?: boolean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};
/**
 * @deprecated
 *
 * @param props
 * @returns
 */
const StockQtyModal: React.FC<StockQtyModalProps> = (props) => {
  const itemEan = props.initialValues;

  const [loading, setLoading] = useState<boolean>(false);
  const { appSettings } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const [data, setData] = useState<StockQtyDataType>({ qty_processing: 0 });
  const [refresh, setRefresh] = useState<boolean>(false);
  const [itemMode, setItemMode] = useState<boolean>(false);
  // const isSingleEan = !!props?.initialValues?.id && props?.initialValues?.id == props?.initialValues?.parent_id;

  useEffect(() => {
    if (!props.modalVisible || !props.initialValues?.id) return;
    setLoading(true);
    getStockByEanId(props.initialValues?.id, {
      item_id: props.initialValues.item_id,
      item_mode: formRef.current?.getFieldValue('include_all') ? 1 : 0,
    })
      .then((res) => setData(res))
      .finally(() => setLoading(false));
  }, [props.initialValues?.id, props.modalVisible, refresh, props.initialValues?.item_id]);

  const columns: ProColumns<API.StockMovement & { oldest_ibo?: API.Ibo }>[] = useMemo(
    () => [
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        align: 'center',
        render: (dom, record) => {
          return Util.dtToDMY(record?.exp_date);
        },
      },
      {
        title: 'Recv. Date',
        dataIndex: ['oldest_ibo', 'ibom', 'received_date'],
        align: 'center',
        tooltip: 'IBOM Received Date of the first connected IBO.',
        render: (dom, record) => {
          return Util.dtToDMY(record?.oldest_ibo?.ibom?.received_date);
        },
      },
      {
        title: 'IBOM',
        dataIndex: ['oldest_ibo', 'ibom', 'order_no'],
        align: 'center',
        tooltip: 'IBOM info of the first connected IBO.',
        render: (dom, record) => {
          const ibom = record?.oldest_ibo?.ibom;
          return `#${ibom?.order_no ?? '-'} | ${ibom?.supplier?.name ?? '-'}`;
        },
      },

      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 90,
        align: 'right',
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 90,
        align: 'right',
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 120,
        align: 'right',
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
    ],
    [],
  );

  return (
    <>
      <ModalForm
        title={
          <>
            Stock Detail
            <Typography.Paragraph
              copyable={{
                text: props.initialValues?.ean || '',
                tooltips: 'Copy EAN ' + (props.initialValues?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {' '}
              - {props.initialValues?.ean || ''}
            </Typography.Paragraph>
            <SocialLinks
              ean={props.initialValues?.ean || ''}
              title={props.initialValues?.ean_texts?.[0]?.name}
              style={{ marginLeft: 50 }}
            />
          </>
        }
        width={1200}
        visible={props.modalVisible}
        onVisibleChange={props.handleModalVisible}
        layout="inline"
        modalProps={{
          maskClosable: true,
          className: props.initialValues?.is_single ? 'm-single' : 'm-multi',
        }}
        formRef={formRef}
        onFinish={async (values) => {
          if (props.onSubmit) {
            props.onSubmit(values);
          }
          props.handleModalVisible(false);
        }}
        submitter={{
          render: (propsSubmitter, defaultDoms) => {
            return [defaultDoms[0]];
          },
        }}
      >
        <Spin spinning={loading}>
          <ProFormSwitch
            width="xs"
            name={['include_all']}
            label="Include All"
            tooltip="Include All EANs in this item"
            fieldProps={{
              onChange: (value) => {
                setItemMode(value);
                setRefresh((prev) => !prev);
              },
            }}
          />
          <Card title="Summary" bordered={false}>
            <Row>
              <Col span={10}>
                <Space>
                  <div>Total Qty:</div>
                  <div>
                    <span className="text-md bold">{ni(data?.total_row?.piece_qty, true)}</span> pcs
                  </div>
                  <div>
                    + <span className="text-md bold">{ni(data?.total_row?.box_qty, true)}</span> boxes
                  </div>
                  <div>
                    <span className="text-md bold"> = {ni(data?.total_row?.total_piece_qty, true)}</span> pcs
                  </div>
                </Space>
              </Col>
              <Col span={10}>
                <Space>
                  <div>
                    Magento Stock Qty{' '}
                    <InfoCircleOutlined
                      title={`Last synced at ${Util.dtToDMYHHMM(
                        appSettings?.magDsStat?.['xmag_inventory_stockbase']?.['last_sync_at'],
                        '-',
                      )}\n\n Formula: Stock Qty=Salable Qty + Reserved Qty`}
                    />
                  </div>
                  <div>
                    <span className="text-md bold" title="Salable Qty + Reserved Qty">
                      {ni(itemEan?.mag_inventory_stocks_sum_quantity, true)}
                      {sn(itemEan?.mag_inventory_stocks_sum_res_quantity)
                        ? ` = ${ni(
                            sn(itemEan?.mag_inventory_stocks_sum_quantity) -
                              sn(itemEan?.mag_inventory_stocks_sum_res_quantity),
                            true,
                          )} + ${ni(itemEan?.mag_inventory_stocks_sum_res_quantity, true)}`
                        : ''}
                    </span>
                    {itemEan?.is_single ? ' pcs' : ' boxes'}
                  </div>
                </Space>
              </Col>
              {data.qty_processing ? <Col span={4}>Qty in processing: {ni(data.qty_processing)}</Col> : null}
            </Row>
          </Card>

          <ProTable
            headerTitle={'Qty by Exp.Date'}
            rowKey={'exp_date'}
            columns={columns}
            dataSource={data?.rows_by_exp_date || []}
            search={false}
            toolbar={{
              search: false,
              actions: [],
              settings: undefined,
            }}
            pagination={false}
            size="small"
            className="w-full"
          />
          <StockCorrectionForm
            ean={{
              id: props.initialValues?.id,
              item_id: props.initialValues?.item_id,
              ean: props.initialValues?.ean,
              sku: props.initialValues?.sku,
              is_single: props.initialValues?.is_single,
            }}
            reload={() => setRefresh((prev) => !prev)}
            itemMode={itemMode}
            initialData={data?.rows}
          />
        </Spin>
      </ModalForm>
    </>
  );
};

export default StockQtyModal;
