import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';

import Util, { sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { <PERSON><PERSON><PERSON>oolbar, PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Card, Space, message } from 'antd';
import { useRef, useState } from 'react';
import { IRouteComponentProps } from 'umi';

import UpdateForm from './components/UpdateForm';
import CreateForm from './components/CreateForm';
import { deleteImportEanDisabled, getImportEanDisabledList } from '@/services/foodstore-one/Import/import-ean-disabled';
import SkuComp from '@/components/SkuComp';
import { updateEanPriceStable } from '@/services/foodstore-one/Import/import';
import { ReconciliationOutlined } from '@ant-design/icons';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.IboPre[]) => {
  const hide = message.loading('Deleting...');
  if (!selectedRows) return true;

  try {
    await deleteImportEanDisabled(selectedRows.map((row) => row.id).join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type RecordType = Partial<API.ImportEanDisabled>;
export type SearchFormValueType = Partial<API.ImportEanDisabled>;

export type ImportEanDisabledListPageProps = {
  refreshTick?: number;
  filterType?: 'light' | 'query';
} & IRouteComponentProps;

const ImportEanDisabledListPage: React.FC<ImportEanDisabledListPageProps> = (props) => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [selectedRowsState, setSelectedRows] = useState<RecordType[]>([]);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'EAN',
      dataIndex: ['ean'],
      width: 200,
      copyable: true,
    },
    {
      title: 'SUPPLIER_ADD',
      dataIndex: ['supplier_add'],
      width: 200,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      width: 100,
      ellipsis: true,
      copyable: true,
      render(__, entity) {
        return entity?.item_ean ? <SkuComp sku={entity?.item_ean?.sku} /> : null;
      },
    },
    {
      title: 'Item Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      width: 300,
      ellipsis: true,
    },

    {
      title: 'From Date',
      dataIndex: ['start_date'],
      sorter: true,
      width: 100,
      ellipsis: true,
      align: 'center',
      render: (dom, record) => (record.start_date == '1900-01-01' ? null : Util.dtToDMY(record.start_date)),
    },
    {
      title: 'To Date',
      dataIndex: ['end_date'],
      sorter: true,
      width: 100,
      ellipsis: true,
      align: 'center',
      render: (dom, record) => (record.end_date == '2099-12-31' ? null : Util.dtToDMY(record.end_date)),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({
              ...record,
            });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer
      title={
        <Space size={24}>
          <div>{props.route.name}</div>
        </Space>
      }
    >
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_import_ean_disabled', {
            ean_search_mode: 'contain_siblings',
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          {/* <ProFormSelect
            name={'ibo_pre_management_id'}
            showSearch
            label="Pre Order"
            width={400}
            options={iboPreManagementOptions}
            request={searchIboPreManagementOptions}
            fieldProps={{
              filterOption: (inputValue: string, option?: any) => true,
              loading: loadingIboPreManagement,
              dropdownMatchSelectWidth: false,
              onChange(value, option) {
                setCurrentIboPreManagement(option as API.IboPreManagement);
                actionRef.current?.reload();
              },
            }}
          /> */}
          <ProFormText name={'ean'} label="EAN" width={150} placeholder={'EAN'} />
          <ProFormText name={'supplier_add'} label="SUPPLIER_ADD" width={180} placeholder={'SUPPLIER_ADD'} />
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        style={{ marginTop: 20 }}
        headerTitle="Disabled EAN List"
        actionRef={actionRef}
        rowKey="id"
        bordered
        revalidateOnFocus={false}
        sticky
        scroll={{ x: '100%' }}
        size="small"
        onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_import_ean_disabled_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
        options={{ fullScreen: true }}
        search={false}
        request={(params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_import_ean_disabled', searchValues);
          Util.setSfValues('sf_import_ean_disabled_p', params);

          const newParams = {
            ...params,
            ...searchValues,
            with: 'itemEan',
          };

          return getImportEanDisabledList(newParams, { ...newSort }, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        toolBarRender={() => [
          <Button
            key="esp-update"
            type="primary"
            icon={<ReconciliationOutlined />}
            style={{ marginRight: 24 }}
            className="btn-green"
            title="Update ean price stable data of EANs from the active imported data."
            onClick={() => {
              const hide = message.loading('Updating the data...', 0);
              updateEanPriceStable()
                .then((res) => {
                  message.success('Updated successfully.');
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          >
            Update Ean Price Stable
          </Button>,
          <Button
            key="new"
            type="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            New
          </Button>,
        ]}
        rowSelection={{
          selectedRowKeys: selectedRowsState.map((x) => sn(x.id)),
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                &nbsp;Disabled EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={async (value) => {
          handleCreateModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default ImportEanDisabledListPage;
