import { Typo<PERSON>, Row, Col } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';
import { getIboList } from '@/services/foodstore-one/IBO/ibo';

import type { ProFormInstance } from '@ant-design/pro-form';
import SPrices from '@/components/SPrices';
import moment from 'moment';

export type IboListProps = {
  eanId?: number;
  itemId?: number;
  refreshTick?: number;
  filterType?: 'light' | 'query';
};

export type SearchFormValueType = Partial<API.Ibo>;

const IboList: React.FC<IboListProps> = ({ eanId, itemId, refreshTick, filterType }) => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.Ibo>[] = [
    {
      title: 'IBOM',
      dataIndex: ['ibom', 'supplier', 'name'],
      sorter: true,
      width: 130,
      showSorterTooltip: false,
      tooltip: '#{IBOM Order No} | {Sup. Name} ({Sup. No})',
      render: (dom, record) => (
        <Typography.Text>
          #{record?.ibom?.order_no || '-'} | {record?.ibom?.supplier?.name}{' '}
          {record?.ibom?.supplier?.supplier_no ? `(${record?.ibom?.supplier?.supplier_no})` : ''}
        </Typography.Text>
      ),
    },

    {
      title: 'Order Date',
      dataIndex: ['ibom', 'order_date'],
      valueType: 'dateRange',
      sorter: true,
      align: 'center',
      ellipsis: true,
      render: (dom, record) => Util.dtToDMY(record.ibom?.order_date),
    },
    {
      title: 'Price',
      dataIndex: 'price',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      width: 90,
      render: (dom, record) => {
        const vat = record?.item_ean?.item?.vat?.value || 0;
        return (
          <>
            <Row
              gutter={4}
              title="View prices list..."
              className="cursor-pointer"
              onClick={() => {
                // setCurrentRow({ ...record });
                // setShowImportedPrices(true);
              }}
              style={{ minHeight: 24 }}
            >
              <Col span={12}>
                <SPrices price={record.price} vat={vat} hideGross />
              </Col>
              <Col span={12}>
                <SPrices price={(record?.price ?? 0) * (record?.item_ean?.attr_case_qty ?? 0)} vat={vat} hideGross />
              </Col>
            </Row>
          </>
        );
      },
    },
    {
      title: 'Pkg. Qty',
      dataIndex: 'box_qty',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      render: (dom, record) => Util.numberFormat(record.box_qty),
    },
    {
      title: 'Qty',
      dataIndex: 'qty',
      valueType: 'digit',
      showSorterTooltip: false,
      tooltip: 'Qty of Single EAN',
      sorter: true,
      align: 'right',
      render: (dom, record) => Util.numberFormat(record.qty),
    },
    /* {
      title: 'Location',
      dataIndex: ['warehouse_location', 'name'],
      sorter: true,
      ellipsis: true,
      width: 100,
    }, */
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      valueType: 'dateRange',
      sorter: true,
      align: 'right',
      ellipsis: true,
      search: {
        transform: (value: any, namePath: string, allValues: any) => {
          return { exp_date_start: value[0], exp_date_end: value[1] };
        },
      },
      render: (dom, record) => {
        return <>{Util.dtToDMY(record?.exp_date)}</>;
      },
    },
    {
      title: 'Exp. Days',
      dataIndex: ['exp_date2'],
      valueType: 'text',
      align: 'right',
      hideInSearch: true,
      width: 60,
      render: (dom, record) => {
        const daysLeft = record.exp_date ? -moment().diff(moment(record.exp_date), 'days') : 0;
        let cls = '';
        if (daysLeft < 40) cls = 'c-red';
        else if (daysLeft <= 65) cls = 'c-orange';

        return <>{record?.exp_date && <span className={cls}>{daysLeft}</span>}</>;
      },
    },
  ];

  useEffect(() => {
    if (eanId) {
      actionRef.current?.reload();
    }
  }, [eanId]);

  useEffect(() => {
    if (itemId) {
      actionRef.current?.reload();
    }
  }, [itemId]);

  return (
    <ProTable<API.Ibo, API.PageParams>
      cardProps={{ bodyStyle: { padding: 0 } }}
      headerTitle="Item Buying Orders"
      actionRef={actionRef}
      rowKey="id"
      bordered
      revalidateOnFocus={false}
      sticky
      scroll={{ x: '100%' }}
      size="small"
      loading={loading}
      onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
      pagination={{
        hideOnSinglePage: true,
        showSizeChanger: false,
        pageSize: 20,
      }}
      rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
      options={false}
      search={false}
      request={(params, sort, filter) => {
        let sortStr = JSON.stringify(sort || {});
        sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
        const newSort = Util.safeJsonParse(sortStr);
        const searchValues = searchFormRef.current?.getFieldsValue();

        return getIboList({ ...params, ...searchValues, with: 'lowestBuying', ean_id: eanId }, { ...newSort }, filter);
      }}
      columns={columns}
      tableAlertRender={false}
    />
  );
};

export default IboList;
