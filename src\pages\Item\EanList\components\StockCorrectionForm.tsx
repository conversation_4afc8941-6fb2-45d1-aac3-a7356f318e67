import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Space, Spin } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import _ from 'lodash';
import Util, { nf2, ni, sn } from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { StockMovementReason, StockStableStatus, StockStableStatusOptions } from '@/constants';
import SProFormDigit from '@/components/SProFormDigit';
import type { StockCorrectionParamType } from '@/services/foodstore-one/Stock/stock';
import { stockCorrection } from '@/services/foodstore-one/Stock/stock';
import StockMoveSettingForm from './StockMoveSettingForm';
import EditableCell from '../EditableCell';
import { updateStockStableStatus } from '@/services/foodstore-one/Stock/stock-stable';

export type FormValueType = Partial<API.Ean>;

export type StockMovementRowType = API.StockStable & {
  key?: string;
  box_qty_edit?: number;
  piece_qty_edit?: number;

  new_wl_id?: number;
  new_wl_name?: string;
};

export type StockCorrectionFormProps = {
  ean?: Partial<API.Ean>;
  initialData?: Partial<StockMovementRowType>[];
  itemMode?: boolean;
  reload?: () => void;
};

/**
 * @deprecated
 *
 * @param props
 * @returns
 */
const StockCorrectionForm: React.FC<StockCorrectionFormProps> = (props) => {
  const [loading, setLoading] = useState<boolean>(false);

  const editableFormRef = useRef<ProFormInstance>();
  const [data, setData] = useState<Partial<StockMovementRowType>[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  // stock move form.
  const [moveModalVisible, handleMoveModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<Partial<StockMovementRowType>>();

  useEffect(() => {
    const newData = [...(props?.initialData || [])];
    for (const x of newData) {
      x.box_qty_edit = x.box_qty;
      x.piece_qty_edit = x.piece_qty;
    }
    setData(newData);
    setEditableRowKeys(newData.map((x) => x.key as React.Key));
  }, [props?.initialData]);

  const isEditable = props?.itemMode !== true;
  useEffect(() => {
    if (isEditable) {
      setEditableRowKeys(data.map((x) => x.key as React.Key));
    } else {
      setEditableRowKeys([]);
    }
  }, [isEditable, data]);

  const hiddenColumns: ProColumns<Partial<StockMovementRowType>>[] = useMemo(() => [], []);

  const columnsAll: ProColumns<Partial<StockMovementRowType>>[] = useMemo(
    () => [
      ...hiddenColumns,
      {
        title: 'WL',
        dataIndex: ['new_wl_name'],
        editable: false,
        width: 90,
        align: 'center',
      },
      {
        title: 'Exp. Date',
        dataIndex: ['exp_date'],
        width: 90,
        align: 'center',
        editable: false,
        render: (dom, record) => {
          return Util.dtToDMY(record?.exp_date);
        },
      },
      {
        title: 'Total Pcs Qty',
        dataIndex: ['total_piece_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.total_piece_qty);
        },
      },
      {
        title: 'Pcs Qty',
        dataIndex: ['piece_qty'],
        width: 80,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.piece_qty);
        },
      },
      {
        title: 'New Pcs Qty',
        dataIndex: ['piece_qty_edit'],
        width: 110,
        align: 'right',
        formItemProps: () => {
          return {};
        },
        editable: (value, row, index) => !!props.ean?.is_single,
        renderFormItem: (item, { record }) => {
          return false && sn(record?.piece_qty) <= 0 ? (
            <></>
          ) : (
            <SProFormDigit readonly={!isEditable} formItemProps={{ style: { marginBottom: 0 } }} placeholder="" />
          );
        },
      },
      {
        title: 'Box Qty',
        dataIndex: ['box_qty'],
        width: 90,
        align: 'right',
        editable: false,
        render: (dom, record) => {
          return ni(record?.box_qty);
        },
      },
      {
        title: 'New Box Qty',
        dataIndex: ['box_qty_edit'],
        width: 110,
        align: 'right',
        formItemProps: () => {
          return {};
        },
        editable: (value, row, index) => !props.ean?.is_single,
        renderFormItem: (item, { record }) => {
          return false && sn(record?.box_qty) <= 0 ? (
            <></>
          ) : (
            <SProFormDigit readonly={!isEditable} formItemProps={{ style: { marginBottom: 0 } }} placeholder="" />
          );
        },
      },
      {
        title: 'Status',
        dataIndex: 'status',
        width: 80,
        editable: false,
        align: 'center',
        tooltip: 'Click to change.',
        render: (dom, record, index, action) => {
          const defaultValue = record.status;
          return (
            <EditableCell
              dataType="select"
              defaultValue={defaultValue}
              dataOptions={StockStableStatusOptions}
              rules={[
                {
                  required: true,
                  message: 'Status is required',
                },
              ]}
              fieldProps={{ dropdownMatchSelectWidth: false }}
              width={80}
              convertValue={(value) => (value === null ? value : value)}
              triggerUpdate={async (newValue: any, cancelEdit) => {
                return updateStockStableStatus({
                  ean_id: props.ean?.id,
                  warehouse_location: { id: record.wl_id },
                  exp_date: record.exp_date,
                  status: newValue,
                })
                  .then((res) => {
                    message.destroy();
                    message.success('Updated successfully.');
                    props.reload?.();
                    cancelEdit?.();
                  })
                  .catch(Util.error);
              }}
            >
              <span className={record.status != StockStableStatus.STATUS_AVAILABLE_SALE ? 'c-orange' : ''}>
                {StockStableStatusOptions.find((x) => x.value == record.status)?.label}
              </span>
            </EditableCell>
          );
        },
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 220,
        render: () => {
          return null;
        },
      },
    ],
    [hiddenColumns, isEditable, props],
  );

  const handleCorrection = (row: StockCorrectionParamType) => {
    if (!isEditable) {
      message.error('Please switch "Include All" off!');
      return;
    }

    if (sn(row?.box_qty_edit) < 0 && sn(row?.piece_qty_edit) < 0) {
      message.error('Invalid qtys! Please fill New Box Qty or New Piece Qty correctly');
      return;
    }

    const hide = message.loading('Updating...', 0);
    setLoading(true);
    stockCorrection({
      ...row,
      ean_id: props.ean?.id,
    })
      .then((res) => {
        hide();
        if (res.message === false) {
          message.info('Nothing to update! Please adjust new Qty.');
        } else {
          message.success('Successfully updated');
          props.reload?.();
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  return (
    <>
      <Spin spinning={loading}>
        <EditableProTable
          rowKey={'key'}
          headerTitle={'Qty by Location & Exp.Date'}
          columns={columnsAll}
          dataSource={data}
          search={false}
          editableFormRef={editableFormRef}
          value={data}
          onChange={setData}
          toolbar={{
            search: false,
            actions: undefined,
            menu: undefined,
            // settings: undefined,
          }}
          scroll={{ x: '100%' }}
          pagination={false}
          className="w-full"
          size="small"
          controlled
          recordCreatorProps={false}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              return [
                <Space key={'actions'}>
                  <Button
                    type="primary"
                    size="small"
                    className=""
                    title="Correction by sold"
                    ghost
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Out });
                    }}
                    disabled={!isEditable}
                  >
                    Sold
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    className=""
                    title="Correction"
                    ghost
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Correction });
                    }}
                    disabled={!isEditable}
                  >
                    Save
                  </Button>
                  <Button
                    type="default"
                    size="small"
                    className=""
                    title="Correction by damage"
                    danger
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Damage });
                    }}
                    disabled={!isEditable}
                  >
                    Dam.
                  </Button>
                  <Button
                    type="dashed"
                    size="small"
                    className=""
                    title="Correction by sample"
                    onClick={() => {
                      handleCorrection({ ...row, reason: StockMovementReason.Sample });
                    }}
                    disabled={!isEditable}
                  >
                    Sam.
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    className=""
                    title="Move stock to another location"
                    onClick={() => {
                      setCurrentRow({ ...row });
                      handleMoveModalVisible(true);
                    }}
                    disabled={!isEditable}
                  >
                    Move
                  </Button>
                </Space>,
              ];
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
          }}
        />
      </Spin>
      <StockMoveSettingForm
        modalVisible={moveModalVisible}
        handleModalVisible={handleMoveModalVisible}
        ean={props.ean}
        initialData={currentRow || {}}
        onSubmit={async () => {
          props.reload?.();
        }}
        onCancel={() => {
          handleMoveModalVisible(false);
        }}
      />
    </>
  );
};

export default StockCorrectionForm;
