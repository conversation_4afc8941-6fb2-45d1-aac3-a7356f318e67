CREATE TABLE `xmag_order_parcel`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT,
    `service_name`        varchar(31)    DEFAULT NULL COMMENT 'Shipping Service Name. DPD, DHL, GLS',
    `parcel_no`           varchar(31)    DEFAULT NULL COMMENT 'Parcel No',
    `ref_no`              varchar(255)   DEFAULT NULL COMMENT 'Shipping reference No generated by FsOne',
    `track_id`            varchar(31)    DEFAULT NULL COMMENT 'Track Id',
    `order_id`            int(11)        DEFAULT NULL COMMENT 'Magento Order ID',
    `status`              varchar(255)   DEFAULT NULL COMMENT 'Current parcel status from shipping provider',
    `push_id`             varchar(255)   DEFAULT NULL COMMENT 'API''s push ID. used in DPD now.',
    `detail`              text           DEFAULT NULL COMMENT 'Detail info in JSON',
    `weight`              decimal(20, 4) DEFAULT NULL COMMENT 'Weight in KG',
    `shipping_updated_on` datetime       DEFAULT NULL COMMENT 'Updated Date time from Shipping Provider',
    `created_on`          datetime       DEFAULT NULL,
    `updated_on`          datetime       DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_xmag_order_parcel_pk2` (`service_name`, `parcel_no`, `ref_no`),
    KEY `IDX_xmag_order_parcel_push_id` (`push_id`) COMMENT 'API log ID to track in DPD',
    KEY `IDX_xmag_order_parcel_order_id` (`order_id`) COMMENT 'Shop''s order ID',
    KEY `IDX_xmag_order_parcel_status` (`status`),
    KEY `IDX_xmag_order_parcel_shipping_updated_on` (`shipping_updated_on`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `xmag_order_parcel_log`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT,
    `parcel_id`           int(11) NOT NULL COMMENT 'FK in xmag_order_parcel',
    `status`              varchar(255) DEFAULT NULL COMMENT 'Status',
    `note`                text         DEFAULT NULL COMMENT 'note from shipping provider',
    `detail`              text         DEFAULT NULL COMMENT 'detail info in JSON',
    `shipping_updated_on` datetime     DEFAULT NULL COMMENT 'Updated on shipping service',
    `updated_on`          datetime     DEFAULT NULL,
    `created_on`          datetime     DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_xmag_order_parcel_log_status` (`status`),
    KEY `FK_xmag_order_parcel_log_parcel_id` (`parcel_id`),
    CONSTRAINT `FK_xmag_order_parcel_log_parcel_id` FOREIGN KEY (`parcel_id`) REFERENCES `xmag_order_label` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

