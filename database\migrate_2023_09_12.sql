ALTER TABLE `file`
    <PERSON>ANGE `file_name` `file_name` VA<PERSON><PERSON>R(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    CHANGE `clean_file_name` `clean_file_name` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL;

-- Important: Because we check NULL or 0 in upsync.
ALTER TABLE `item_ean` CHANGE `virtual_stock_qty_gfc` `virtual_stock_qty_gfc` DOUBLE NULL COMMENT 'Virtual Stock Qty for GFC.';