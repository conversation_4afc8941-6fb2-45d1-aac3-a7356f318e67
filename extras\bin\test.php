<?php

use App\Lib\Func;
use App\Lib\FuncDate;
use App\Models\ItemEan;
use App\Models\Magento\MagProductAttributeSet;
use App\Service\Item\ItemEan\ItemEanService;

error_reporting(E_ALL);


// var_dump(Func::randomPassword(30));
// var_dump(Func::passwordHash('a'));
// exit;

// $tmp = (strtotime('2024-05-23 18:01:14') - strtotime($trackRow->start_datetime)) / 60;

/*var_dump(1.1 * null);

var_dump(date('ymd_His'));

exit;

var_dump(1.2 * "5" * 2);

var_dump(1 && $t= 0);
var_dump($t);

var_dump(sprintf("123=%s", 123));
var_dump(sprintf("123=", 123));


$url = 'https://foodstore.one/kelloggs-crunchy-nut-720g-mega-family-xxl?ff=8&fp=491&utm_source=Idealo&utm_medium=portal&utm_campaign=Idealo_std&utm_term=239_407&utm_content=239_407';

$result = [];
parse_str(explode('?', $url)[1] ?? '', $result);
print_r($result);

exit;*/

/*$tmp[2] = 2;
$tmp[0] = 0;
$tmp[4] = 4;
$tmp[3] = 3;
ksort($tmp);
print_r($tmp);
print_r(array_values($tmp));
exit;*/


/*print_r(date('Y-m-d', strtotime("first day of -2 months")) . PHP_EOL);

var_dump((new DateTime('2023-08-30'))->diff(new DateTime())->format('%r%a'));
var_dump((new DateTime('2023-08-26'))->diff(new DateTime())->format('%r%a'));*/

require __DIR__ . '/../../src/App/App.php';

/** @var ItemEanService $eanService */
$eanService = $container->get(ItemEanService::class);

$data = $eanService->getEanPricesListByPage(1, 10, []);

print_r($data);

exit;

// $list = FuncDate::getYmList('2024-07-01', 12);
// print_r($list);


$dt = '2024-08-16T22:36:52+02:00';

$converted = FuncDate::dtDbDatetimeStr(Func::dtStrToTimeYmd($dt, DATE_RFC3339), DATE_FORMAT_YMD_HIS);

$nums = [
    1,
    2.2,
    -0.1,
    -4,
    "123.45 $",
    "123.00 $",
    "123.00$",
    "123$",
    "-123$",
    // German format
    "1,200.39$",
    "1.200,39$",
    "1,200.39",
    "1.200,39",
    "2,3$",
    "2,39",
];

foreach ($nums as $num) {
    echo sprintf("% 10s --> % 7s   %5s", $num, Func::safeDouble($num), is_numeric($num)) . PHP_EOL;
}

$data = (new \Mpdf\Barcode())->getBarcode("C128A", 1);
var_dump($data);

$fileName = "OKay/BL-01.html";

var_dump(Func::getSafeFilePath($fileName));

/** @var \App\Service\MagentoApi\MagentoApiService $mService */
$mService = $container->get('magento_api_service');

$opt = $mService->genOtp();

print_r("Epoch Ts: " . time() . PHP_EOL);
print_r("Opt: " . $opt . PHP_EOL);
print_r(sprintf("%05d", 8465));

var_dump(base64_decode('Mjc2MDA4MzUxNTpKWkIyRFUwZmUyWHVFVFhLNUlQZQ=='));

return;

$input = "{Super duper extra text.} {Awesome another text!} {And here we go again...}";
$matches = array();
preg_match_all('/\{([^}]+)\}/', $input, $matches);
print_r($matches);

$input = "a) Super duper extra text. b). Awesome another text! c)And here we go again...";
$matches = array();
// preg_match_all('/\{([^}]+)\}/', $input, $matches);
// preg_match_all('/\{([^}]+)\}/', $input, $matches);
preg_match_all('/[a|b|c]\)(.+)/', $input, $matches);
print_r($matches);


$string = "(731) some text here with number 2 (220) some 54 number other text here";
preg_match_all("/\((\d{3})\) *([^( ]*(?> +[^( ]+)*)/", $string, $matches);
print_r($matches);

$keywords = preg_split("/[\s,]+/", "hypertext language, programming");
print_r($keywords);

$matches = preg_split("/[a|b|c]\)/", trim($input), -1, PREG_SPLIT_OFFSET_CAPTURE);
print_r($matches);

/** @var \App\Models\Openai\OpenapiCallLog $log */
$log = \App\Models\Openai\OpenapiCallLog::findOrFail(20);
print_r($log->toArray());
print_r($log->answers);

exit;

/** @var \App\Repository\Import\ImportRepository $repo */
$repo = Func::getContainer()->get(\App\Repository\Import\ImportRepository::class);


$code = MagProductAttributeSet::query()
    ->where('attribute_set_name', 'Default')->value('attribute_set_id');


exit;


$qb = \App\Models\Ibo::getLatestIboBuilder();
$latestIbo = $qb->where('ean_id', 748)->first();
print_r($latestIbo->toArray());


exit;


/**
 * Parsing emails input text.
 */
var_dump(Func::parseEmailStrList('<EMAIL>'));
exit;

/*var_dump(+null);
var_dump(+'1');
var_dump(+'1d');
var_dump(+0.00000000);

exit;*/

var_dump(date('d.m.y', strtotime('2023-02-01 00:00:00')));

var_dump(ltrim(" 00 ABC", "0 "));
var_dump(ltrim(null, "0 "));
return;

/** @var \App\Repository\Import\ImportRepository $repo */
$repo = Func::getContainer()->get(\App\Repository\Import\ImportRepository::class);
$builder = $repo->getQueryLatestImportedPrice();
$result = $builder->get()->toArray();
// print_r($result);

$repo->storeLatestBPFromImportedTables();
exit;


// $temp = Func::buildMagentoFieldsParam(\App\Service\Magento\Order\MagOrderService::FIELDS_COLS['order']);
// var_dump($temp);


// $mService = $container->get('magento_api_service');
// print_r($mService->genOtp());
exit;
// print_r(\App\Lib\Country::getCountriesDE());
// exit;
var_dump(true ? '-' : 22);
var_dump('' ?? 22);
// exit;
/** @var \Monolog\Logger $logger */
// $logger = $container->get('logger');

/** @var \App\Service\MagentoApi\MagentoApiService $mService */
$mService = $container->get('magento_api_service');

/*$res = $mService->authenticateAdmin();
// var_dump($res);
var_dump($res);
print_r($res);*/


// $res = $mService->getProject('SKU_5');
// print_r($res);

$newProductData = [
    "name" => "Twix  Schokoriegel"
];
$res = $mService->updateProduct('SKU_5', $newProductData);
print_r($res);
echo($res['name'] === $newProductData['name'] ? 'SUCCESS' : 'FAILED');
exit;


var_dump(Func::randomPassword(30));
var_dump(Func::passwordHash('a'));

/** @var Illuminate\Database\Capsule\Manager $em */
$em = $container->get('dbORM');

print_r(\App\Models\User::query()->count());

$result = $container->get('category_repository')->getTreeFlat([]);
print_r($result);
exit;

/** @var ItemEan $itemEan */
//$itemEan = ItemEan::query()->find(100);

/*ItemEan::query()->whereIn('id', [87])->update([
    'sku' => $container->get('dbORM')->getDatabaseManager()->raw("CONCAT(item_id, '_', id)")
]);*/

/*$table = $em->table('user');
// $table = $em->getDatabaseManager()->table('user');
$cnt = $table->count();
var_dump($cnt);

print_r($em::table('user')->count());*/

$user = new \App\Models\User();


exit;

/** @var \App\Service\Project\ProjectService $service */
$service = $container->get('project_service');


echo $service->checkSlug('v3') . PHP_EOL;
echo $service->checkSlug('v3-v4') . PHP_EOL;
echo $service->checkSlug('v3-v5') . PHP_EOL;
echo $service->checkSlug('v3', 40) . PHP_EOL;
echo $service->checkSlug('v3-v4', 4) . PHP_EOL;


function checkSlug($str)
{
    print_r('------------') . PHP_EOL;
    print_r($str);
    echo PHP_EOL;
    $matches = [];
    print_r(preg_match('/-v[0-9]$/i', $str, $matches));
    echo PHP_EOL;
    print_r($matches);
    echo PHP_EOL;

    if ($matches) {
        print_r(substr($str, 0, -strlen($matches[0])));
        echo PHP_EOL;
    }
}

$str = "project-v1-v2";
checkSlug($str);

checkSlug("project");
checkSlug("project-");
checkSlug("project-v5");


var_export('0' ?? NULL);
var_export('0' ?: NULL);

echo 'okay' . PHP_EOL;
var_dump(json_encode([1002.31, 2002.42]));


$test = null;
var_dump(isset($test));


