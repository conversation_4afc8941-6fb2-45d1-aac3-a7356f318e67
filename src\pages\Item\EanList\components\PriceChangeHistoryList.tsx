import { Row, Col, TablePaginationConfig } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { sn } from '@/util';

import SPrices from '@/components/SPrices';
import { getAllEanPriceHistoryList } from '@/services/foodstore-one/Item/ean';
import { useModel } from 'umi';

export type PriceChangeHistoryListProps = {
  eanId?: number;
  vat?: number;
  refreshTick?: number;
  pageSize?: number;
  pagination?: TablePaginationConfig;
};

export type SearchFormValueType = Partial<API.ItemEanPriceHistory>;

const PriceChangeHistoryList: React.FC<PriceChangeHistoryListProps> = ({
  eanId,
  refreshTick,
  vat,
  pagination,
  pageSize,
}) => {
  const { appSettings } = useModel('app-settings');
  const { priceTypes } = appSettings;

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.ItemEanPriceHistory>[] = [
    /* {
      title: 'SKU',
      dataIndex: ['sku'],
      align: 'center',
      ellipsis: true,
      width: 100,
      render: (dom, record) => priceTypes?.find((x) => x.id == record.price_type_id)?.name,
    }, */
    {
      title: 'Price Type',
      dataIndex: ['price_type_id'],
      valueType: 'dateRange',
      sorter: true,
      align: 'center',
      ellipsis: true,
      className: 'text-sm',
      width: 60,
      render: (dom, record) => priceTypes?.find((x) => x.id == record.price_type_id)?.name,
    },
    {
      title: 'Price',
      dataIndex: 'price',
      valueType: 'digit',
      sorter: true,
      align: 'right',
      width: 90,
      render: (dom, record) => {
        return (
          <Row
            gutter={4}
            // className="cursor-pointer"
            onClick={() => {
              // setCurrentRow({ ...record });
              // setShowImportedPrices(true);
            }}
            style={{ minHeight: 24 }}
          >
            <Col span={12}>
              <SPrices
                price={record.item_ean?.attr_case_qty ? sn(record?.price) / record.item_ean?.attr_case_qty : 0}
                vat={vat}
              />
            </Col>
            {!record.item_ean?.is_single && (
              <Col span={12}>
                <SPrices price={record?.price} vat={vat} />
              </Col>
            )}
          </Row>
        );
      },
    },
    {
      title: 'Updated on',
      dataIndex: ['created_on'],
      sorter: true,
      defaultSortOrder: 'descend',
      align: 'center',
      width: 90,
      ellipsis: true,
      className: 'text-sm',
      render: (dom, record) => {
        return <>{Util.dtToDMYHHMM(record?.created_on)}</>;
      },
    },
  ];

  useEffect(() => {
    if (eanId) {
      actionRef.current?.reload();
    }
  }, [eanId]);

  useEffect(() => {
    if (refreshTick) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  return (
    <ProTable<API.ItemEanPriceHistory, API.PageParams>
      cardProps={{ bodyStyle: { padding: 0 } }}
      headerTitle="Latest Price Change History"
      actionRef={actionRef}
      rowKey="id"
      bordered
      revalidateOnFocus={false}
      sticky
      scroll={{ x: '100%' }}
      size="small"
      loading={loading}
      onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
      pagination={
        pagination ?? {
          hideOnSinglePage: true,
          showSizeChanger: false,
        }
      }
      rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
      options={{ density: false, fullScreen: false, reload: true, search: false, setting: false }}
      search={false}
      request={(params, sort, filter) => {
        let sortStr = JSON.stringify(sort);
        sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
        let newSort = Util.safeJsonParse(sortStr);
        if (Object.keys(newSort).length < 1) {
          newSort = { id: 'descend' };
        }
        // const searchValues = searchFormRef.current?.getFieldsValue();
        params.pageSize = pageSize ?? params.pageSize;
        return getAllEanPriceHistoryList(
          {
            ...params,
            ean_id: eanId,
          },
          { ...newSort },
          { ...filter },
        );
      }}
      columns={columns}
      tableAlertRender={false}
    />
  );
};

export default PriceChangeHistoryList;
