import { AC_PER_PAGE_PAGINATION, ImportStatus } from '@/constants';
import Util from '@/util';
import _ from 'lodash';
import qs from 'qs';
import type { RequestConfig } from 'umi';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/import/master';

/** get GET /api/import/master/get/{id} */
export async function getUploadedData(id?: number, params?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/get/${id}`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/** get GET /api/import/master/get/{id} */
export async function getUploadedDataList(params: API.PageParamsExt, sort: any, filter: any) {
  const imported = params.imported ?? 1;
  return request<API.BaseResult>(`${urlPrefix}/get/${params.id}`, {
    method: 'GET',
    params: {
      imported,
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
    },
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
    withToken: true,
  }).then((res) =>
    imported == 1
      ? {
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
        makers: res.message?.makers || [],
        categories: res.message?.categories || [],
      }
      : {
        ...res,
        data: res.message.excelData.rows,
        success: res.status == 'success',
        total: res.message.excelData.pagination.totalRows,
        makers: res.message?.makers || [],
        categories: res.message?.categories || [],
      },
  );
}

/** get GET /api/import/master/get/{id}/ibo */
export async function getUploadedDataWithIboList(params: API.PageParamsExt, sort: any, filter: any) {
  const imported = params.imported ?? 1;
  return request<API.BaseResult>(`${urlPrefix}/get/${params.id}/ibo`, {
    method: 'GET',
    params: {
      imported,
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: sort,
      filter_detail: filter,
    },
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** post POST /api/import/master/upload-file */
export async function uploadImportedFile(data?: Record<string, any>) {
  const url = `${urlPrefix}/upload-file`;
  const config: RequestConfig = {
    method: 'POST',
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.BaseResult>(url, config).then((res) => res.message);
}

/** post POST /api/import/master/upload-file-tmp-xls */
export async function uploadImportedFileTmpXls(data?: Record<string, any>) {
  const url = `${urlPrefix}/upload-file-tmp-xls`;
  const config: RequestConfig = {
    method: 'POST',
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.BaseResult>(url, config).then((res) => res.message);
}



/** get GET /api/import/master/all */
export async function getImportList(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}/all`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(
        _.isEmpty(sort)
          ? {
            'import.created_on': 'descend',
          }
          : sort,
      ),
      filter_detail: JSON.stringify(filter),
      with: 'supplier,files',
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
/** get GET /api/import/master/get-prices/{ean} */
export async function getImportedPricesList(params: API.PageParamsExt, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}/get-prices/${params.ean}`, {
    method: 'GET',
    params: {
      ...params,
      item_name: params.item?.name,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(
        _.isEmpty(sort)
          ? {
            'import.updated_on': 'descend',
          }
          : sort,
      ),
      filter_detail: JSON.stringify(filter),
      with: 'supplier,files',
    },
    withToken: true,
  })
    .then((res) => ({
      data: res.message.data,
      success: res.status == 'success',
      total: res.message.pagination.totalRows,
    }))
    .catch(Util.error);
}

/** put PUT /api/import/master/{id}/attributes */
export async function updateImport(id?: number, data?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/attributes`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/** put PUT /api/import/master/{id} */
export async function importSupplierData(id: number, data?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/** delete DELETE /api/import/master/{id} */
export async function deleteImportedSupplierData(id: number) {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
  }).then((res) => res.message);
}

/** put PUT /api/import/master/{id}/eans */
export async function importEANsFromSupplierData(id?: number, data?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/eans`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/** put PUT /api/import/master/{id}/importPrices */
export async function importPricesFromSupplierData(id?: number, ibomId?: number, data?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/importPrices`, {
    method: 'PUT',
    data: { ...data, ibomId },
  }).then((res) => res.message);
}

/** put PUT /api/import/master/{id}/importArticleNo */
export async function importArticleNoFromSupplierData(id?: number, data?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/importArticleNo`, {
    method: 'PUT',
    data: { ...data },
  }).then((res) => res.message);
}

/** put PUT /api/import/master/{id}/tmp-trademark */
export async function updateItemTmpTrademarkFromSupplierData(id?: number, data?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/tmp-trademark`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/** put
 *
 * PUT /api/import/master/{id}/marry
 *
 * @param id  import table ID(PK).
 */
export async function updateMarryTwoEANs(
  id?: number,
  data?: {
    ean?: string; // sys EAN
    ean_id?: number; // sys EAN ID
    imported_ean?: string;
    xls_id?: number;
    ibo_id?: number;
  },
) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/marry`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/** delete DELETE /api/import/master/{id}/marry */
export async function deleteMarriedTwoEANs(
  id?: number,
  list?: {
    xls_id?: number;
    ibo_id?: number;
  }[],
) {
  return request<API.BaseResult>(`${urlPrefix}/${id}/marry`, {
    method: 'DELETE',
    data: { list },
  }).then((res) => res.message);
}

export async function getImportACList(params?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/all`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize ?? 1000,
      page: params?.current,
      status: ImportStatus.STATUS_IMPORTED,
      sort_detail: JSON.stringify(
        params?.sort
          ? params?.sort
          : {
            'import.created_on': 'descend',
          },
      ),
      with: 'files',
    },
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.Import) => ({
      ...x,
      value: x.id,
      label: `${x.table_name?.substring(18)} | ${x.files?.[0]?.clean_file_name || ''}`,
    })),
  );
}

export async function getComparedList(id: number, params: API.PageParamsExt, sort: any, filter: any) {
  console.log(params);
  return request<API.BaseResult>(`${urlPrefix}/${id}/compared`, {
    method: 'GET',
    params: {
      ...filter,
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      status: ImportStatus.STATUS_IMPORTED,
      sort_detail: JSON.stringify({
        'import.created_on': 'descend',
      }),
      with: 'files',
      sort,
    },
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getXlsTrademarkACList(id?: number, params?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/get/${id}/xls-trademarks`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
    },
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.ImportedSupplierRow) => ({
      ...x,
      value: x.trademark,
      label: `${x.trademark}`,
    })),
  );
}

/** post POST /api/import/master/updateEanPriceStable */
export async function updateEanPriceStable(data?: Record<string, any>) {
  const url = `${urlPrefix}/updateEanPriceStable`;
  const config: RequestConfig = {
    method: 'POST',
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.BaseResult>(url, config).then((res) => res.message);
}


/** post POST /api/import/master/updateEanXls */
export async function updateEanXlsData(data?: Record<string, any>) {
  const url = `${urlPrefix}/updateEanXls`;
  const config: RequestConfig = {
    method: 'POST',
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.BaseResult>(url, config).then((res) => res.message);
}

/**
 * Get all married EANs list
 *
 * @param id
 * @param params
 * @returns
 */
export async function getMarriedList(
  params?: API.PageParams & Record<string, any>,
  sort?: any,
  filter?: any,
): Promise<API.ImportSupplierMarryList> {
  return request<API.ResultList<API.ImportSupplierMarry>>(`${urlPrefix}/getMarriedList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/**
 * Get all married EANs list
 *
 * @param id
 * @param params
 * @returns
 */
export async function getLastMarriedByEan(params?: {
  imported_ean?: string;
  ean?: string;
}): Promise<API.ImportSupplierMarry> {
  return getMarriedList({ ...params, pageSize: 1, page: 1 }, { created_on: 'descend' }).then((res) => res.data?.[0]);
}



/**
 * Get SUPPLIER_ADD AC list
 *
 * @param id
 * @param params
 * @returns
 */
export async function getSupplierAddACList(
  params: API.PageParams & { keyWord?: string },
  sort?: any,
  filter?: any,
): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}/getSupplierAddACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize ?? AC_PER_PAGE_PAGINATION,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.Import) => ({
      ...x,
      value: `${x.supplier_add}`,
      label: `${x.supplier_add ?? '-'}`,
    })),
  );
}
