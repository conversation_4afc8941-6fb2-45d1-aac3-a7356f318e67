import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import Util, { ni, sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { But<PERSON>, Col, Modal, Row, Popconfirm, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef } from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import { ProFormInstance } from '@ant-design/pro-form';
import {
  deleteOfferItemIboPrePackReadyMap,
  getOfferItemIboPrePackReadyMapList,
} from '@/services/foodstore-one/Offer/offer-item-packed-ready';

export type SearchFormValueType = Partial<API.OfferItemIboPrePackReadyMap>;

type IboPrePackedReadyListModalProps = {
  iboPre?: Partial<API.IboPre>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  cbDelete?: (data?: any) => void;
};

const IboPrePackedReadyListModal: React.FC<IboPrePackedReadyListModalProps> = (props) => {
  const { iboPre, modalVisible, handleModalVisible, cbDelete } = props;

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  useEffect(() => {
    if (modalVisible && iboPre?.id) {
      actionRef.current?.reload();
    }
  }, [modalVisible, iboPre?.id]);

  const columns: ProColumns<API.OfferItemIboPrePackReadyMap>[] = [
    {
      title: 'Offer No',
      dataIndex: ['offer_item', 'offer', 'offer_no'],
      width: 60,
    },
    {
      title: 'Offer Qty (pcs)',
      dataIndex: ['offer_item', 'qty'],
      width: 80,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record.offer_item?.case_qty) * sn(record.offer_item?.qty));
      },
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 65,
      align: 'right',
      className: 'bl2',
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },

    {
      title: 'Qty (pcs)',
      dataIndex: ['qty_pcs'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record?.case_qty) * sn(record?.qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 60,
      fixed: 'right',
      render: (_, record) => [
        <Popconfirm
          key="delete"
          title="Are you sure you want to delete?"
          okButtonProps={{ size: 'large' }}
          cancelButtonProps={{ size: 'large' }}
          onConfirm={() => {
            deleteOfferItemIboPrePackReadyMap({
              offer_item_id: record.offer_item_id,
              ibo_pre_id: record.ibo_pre_id,
              ean_id: record.ean_id,
              exp_date: record.exp_date,
            })
              .then((res) => {
                message.success('Deleted successfully.');
                actionRef.current?.reload();
                cbDelete?.();
              })
              .catch(Util.error);
          }}
        >
          <Button type="default" key="rest" icon={<DeleteOutlined />} danger />
        </Popconfirm>,
      ],
    },
  ];

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={900}
      // bodyStyle={{ paddingTop: 0 }}
      maskClosable={false}
      footer={false}
      title={
        <Row gutter={32}>
          <Col>Qty Packed Ready List for IBO Pre#{iboPre?.id}</Col>
          <Col>IBO Pre Qty: {ni(sn(iboPre?.case_qty) * sn(iboPre?.qty), true)} pcs</Col>
        </Row>
      }
    >
      <ProTable<API.OfferItemIboPrePackReadyMap, API.PageParams>
        headerTitle={null}
        actionRef={actionRef}
        rowKey={(entity) => `${entity.offer_item}_${entity.ibo_pre_id}_${entity.ean_id}_${entity.exp_date}`}
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: false, setting: false }}
        size="large"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        toolBarRender={() => []}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            ibo_pre_id: iboPre?.id,
            with: 'iboPre,itemEan',
          };

          return getOfferItemIboPrePackReadyMapList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record?.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
    </Modal>
  );
};

export default IboPrePackedReadyListModal;
