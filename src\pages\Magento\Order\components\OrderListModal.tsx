/* eslint-disable @typescript-eslint/dot-notation */
import { <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { exportOrdersList, getOrdersList } from '@/services/foodstore-one/Magento/order';
import Util, { nf2, ni, sn } from '@/util';
import type { OrderModalSearchParamsType } from '@/pages/Report/Order/OrderTrademarkProducerList';
import { MagentoOrderStatus } from '..';
import { DAYS, EURO } from '@/constants';
// import { FooterToolbar } from '@ant-design/pro-layout';
import { ExportOutlined } from '@ant-design/icons';

type RowType = API.Order &
  API.OrderItem & {
    entity_id: number;
    qty_ordered: number;

    turnover: number;
    net_turnover: number;
    cturnover: number;
    ebay_fee: number;
    gp: number;
    bp: number;

    turnover_pcs: number;
    net_turnover_pcs: number;
    cturnover_pcs: number;
    ebay_fee_pcs: number;
    gp_pcs: number;
    bp_pcs: number;
  } & {
    cnt?: number;
  };

const defaultRow: RowType = {
  entity_id: -1,
  qty_ordered: 0,

  turnover: 0,
  net_turnover: 0,
  cturnover: 0,
  ebay_fee: 0,
  gp: 0,
  bp: 0,

  turnover_pcs: 0,
  net_turnover_pcs: 0,
  cturnover_pcs: 0,
  ebay_fee_pcs: 0,
  gp_pcs: 0,
  bp_pcs: 0,
};

export type SearchFormValueType = Partial<API.Order>;

type OrderListModalProps = {
  searchParams?: OrderModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const OrderListModal: React.FC<OrderListModalProps> = (props) => {
  const { searchParams } = props;
  const actionRef = useRef<ActionType>();
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [summaryRow, setSummaryRow] = useState<RowType>();

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Increment ID',
      dataIndex: ['increment_id'],
      sorter: true,
      align: 'left',
      ellipsis: true,
      className: 'text-sm p-0',
      width: 120,
      render(dom, record, index, action, schema) {
        if (record?.entity_id <= 0) return 'Total';
        else return dom;
      },
    },
    {
      title: 'Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      width: 200,
      sorter: false,
      className: 'p-0 text-xs',
      ellipsis: true,
      copyable: true,
      /* render(dom, record) {
        const name = record?.item_ean?.ean_text_de?.name;
        return record.mag_order_items?.length && name ? (
          <Typography.Text ellipsis copyable>
            {dom}
          </Typography.Text>
        ) : null;
      }, */
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      width: 90,
      sorter: false,
      copyable: true,
    },
    {
      title: 'Ordered Qty',
      dataIndex: 'qty_ordered',
      width: 65,
      align: 'right',
      sorter: false,
      render: (dom, recordIn) => {
        return ni(recordIn[`qty_ordered`]);
      },
    },
    {
      title: 'GP',
      dataIndex: `gp`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        if (recordIn?.mag_order_items) return;

        let cls = '';
        const gp = sn(recordIn.gp);
        if (gp >= 5) cls += 'c-green';
        else if (gp >= 1) {
        } else if (gp >= 0) cls += 'c-orange';
        else cls += 'c-red';

        return <span className={cls}>{nf2(recordIn.gp)}</span>;
      },
    },
    {
      title: '%',
      dataIndex: 'percentage',
      align: 'right',
      width: 70,
      sorter: false,
      tooltip: '(1 - ([Net/NetTurnover/pcs]   /   [Landed / pcs] ))  * 100 & "%".',
      render: (dom, recordIn) => {
        if (sn(recordIn.landed_pcs) != 0 && sn(recordIn.cturnover_pcs) != 0) {
          const v = 100 * (1 - sn(recordIn.cturnover_pcs) / sn(recordIn.landed_pcs));
          if (v) return nf2(v) + '%';
        }
        return null;
      },
    },
    {
      title: 'Discount',
      dataIndex: ['item_ean', 'fs_special_discount'],
      tooltip: 'Note: current EAN discount setting',
      align: 'right',
      width: 70,
      sorter: false,
      /* render: (dom, recordIn) => {
        return '';
      }, */
    },

    {
      title: 'Store',
      dataIndex: ['store', 'code'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 70,
      className: 'text-sm p-0',
      render: (_, record) => {
        if (record?.entity_id <= 0 || !record.mag_order_items?.length) return '';
        else return (record as any)?.store?.code;
      },
    },

    {
      title: 'status',
      dataIndex: ['status'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 100,
      render: (_, record) => {
        if (!record.mag_order_items?.length || record?.entity_id <= 0) return <></>;
        return <MagentoOrderStatus status={record.status} />;
      },
    },

    {
      title: 'Gross Turnover',
      dataIndex: `turnover`,
      align: 'right',
      width: 80,
      sorter: false,
      className: 'bl2 b-grey',
      render: (dom, recordIn) => {
        return nf2(recordIn.turnover);
      },
    },
    {
      title: 'Net Turnover',
      dataIndex: `net_turnover`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.net_turnover);
      },
    },
    {
      title: 'Provision',
      dataIndex: `ebay_fee`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.ebay_fee);
      },
    },
    {
      title: 'Net/Net Turnover',
      dataIndex: `cturnover`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.cturnover);
      },
    },

    // AVG columns
    {
      title: 'Gross Turnover / pcs',
      dataIndex: `turnover_pcs`,
      align: 'right',
      width: 80,
      sorter: false,
      className: 'bl2 b-grey',
      render: (dom, recordIn) => {
        return nf2(recordIn.turnover_pcs);
      },
    },
    {
      title: 'Net Turnover / pcs',
      dataIndex: `net_turnover_pcs`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.net_turnover_pcs);
      },
    },
    {
      title: 'Provision / pcs',
      dataIndex: `ebay_fee_pcs`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.ebay_fee_pcs);
      },
    },
    {
      title: 'Net/Net Turnover / pcs',
      dataIndex: `cturnover_pcs`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.cturnover_pcs);
      },
    },
    {
      title: 'Landed / pcs',
      dataIndex: ['landed_pcs'],
      align: 'right',
      width: 80,
      render: (dom, recordIn) => {
        return nf2(recordIn.landed_pcs);
      },
    },
    {
      title: 'GP / pcs',
      dataIndex: `gp_pcs`,
      align: 'right',
      width: 80,
      sorter: false,
      render: (dom, recordIn) => {
        return nf2(recordIn.gp_pcs);
      },
    },
    {
      title: 'Created on',
      dataIndex: ['created_at'],
      sorter: true,
      defaultSortOrder: 'descend',
      width: 100,
      className: 'text-sm c-grey',
      render: (dom, record) => Util.dtToDMYHHMMTz(record.created_at),
    },
  ];

  const handleExportSelectedRows = async () => {
    const hide = message.loading('Exporting...');
    return exportOrdersList(
      {
        ...Util.getSfValues('sf_gSearch'),
        ...searchParams,
      },
      { created_at: 'descend' },
    )
      .then((res) => {
        window.location.href = `${API_URL}/api/${res.url}`;
      })
      .finally(() => {
        hide();
      });
  };

  useEffect(() => {
    if (props.modalVisible) {
      actionRef.current?.reload();
    }
  }, [props.modalVisible]);

  let modalTitle = '';
  if (searchParams?.wd !== undefined && searchParams?.wd >= 0) {
    modalTitle += ' ' + DAYS[searchParams?.wd] ?? '';
  }
  if (searchParams?.dateRange && (searchParams?.dateRange?.from || searchParams?.dateRange?.to)) {
    modalTitle +=
      (modalTitle ? ', ' : '') +
      `${Util.dtToDMY(searchParams?.dateRange.from) ?? ' '} ~ ${Util.dtToDMY(searchParams?.dateRange.to) ?? ' '}`;
  }

  return (
    <Modal
      title={
        <>
          Orders List: {searchParams?.trademark_name ?? (searchParams?.trademark as any)?.label ?? ''} &nbsp;{' '}
          {searchParams?.sku ? <Typography.Text copyable>{searchParams?.sku}</Typography.Text> : null} &nbsp;
          <span className="text-sm">{modalTitle}</span>
          {summaryRow && sn(summaryRow?.cnt) > 0 ? (
            <span style={{ marginLeft: 80 }}>{`${ni(summaryRow?.cnt)} Orders with AVG GP: ${nf2(
              summaryRow.gp / sn(summaryRow.cnt),
            )}${EURO}`}</span>
          ) : null}
          <Button
            style={{ float: 'right', display: 'block', marginRight: 32 }}
            key={'export all'}
            type="primary"
            icon={<ExportOutlined />}
            onClick={() => {
              handleExportSelectedRows();
            }}
          >
            Export
          </Button>
        </>
      }
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      width="1500px"
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <ProTable<RowType, API.PageParams>
        actionRef={actionRef}
        rowKey="entity_id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, reload: true, density: false, search: false }}
        search={false}
        sticky
        scroll={{ x: 1300 }}
        cardProps={{
          headStyle: { padding: 0 },
          bodyStyle: { padding: 0 },
          extra: searchParams?.sku ? (
            <div
              className="text-sm"
              style={{
                position: 'absolute',
                right: 100,
                width: 700,
                top: 16,
                textAlign: 'center',
              }}
            >{`Each order only includes "${searchParams?.sku}". So stats figures on Order row is just aggregation of order items of this SKU.`}</div>
          ) : undefined,
        }}
        request={async (params, sort, filter) => {
          return getOrdersList(
            {
              ...params,
              ...Util.getSfValues('sf_gSearch'),
              ...searchParams,
            },
            sort,
            filter,
          ).then((res) => {
            const totalRow = { ...defaultRow };

            const keys: React.Key[] = [];
            res.data.forEach((row: any) => {
              keys.push(row.entity_id);
              if (row.mag_order_items?.length) {
                // calc sum of all order items row.
                const tmpItemsTotal = { ...defaultRow };
                row.mag_order_items.forEach((item: RowType) => {
                  tmpItemsTotal.qty_ordered = sn(tmpItemsTotal.qty_ordered) + sn(item.qty_ordered);
                  tmpItemsTotal.turnover = sn(tmpItemsTotal.turnover) + sn(item.turnover);
                  tmpItemsTotal.net_turnover = sn(tmpItemsTotal.net_turnover) + sn(item.net_turnover);
                  tmpItemsTotal.ebay_fee = sn(tmpItemsTotal.ebay_fee) + sn(item.ebay_fee);
                  tmpItemsTotal.cturnover = sn(tmpItemsTotal.cturnover) + sn(item.cturnover);
                  tmpItemsTotal.gp = sn(tmpItemsTotal.gp) + sn(item.gp);
                  item.bp = sn(item.oi_idx?.bp) * sn(item.oi_idx?.case_qty) * item.qty_ordered;
                  tmpItemsTotal.bp = sn(tmpItemsTotal.bp) + item.bp;

                  item.turnover_pcs = item.turnover / item.qty_ordered;
                  item.net_turnover_pcs = item.net_turnover / item.qty_ordered;
                  item.ebay_fee_pcs = item.ebay_fee / item.qty_ordered;
                  item.cturnover_pcs = item.cturnover / item.qty_ordered;
                  item.gp_pcs = item.gp / item.qty_ordered;
                  item.bp_pcs = item.bp / item.qty_ordered;

                  // Key setting
                  item.entity_id = sn(item.item_id);
                });

                row.qty_ordered = tmpItemsTotal.qty_ordered;
                row.turnover = tmpItemsTotal.turnover;
                row.net_turnover = tmpItemsTotal.net_turnover;
                row.ebay_fee = tmpItemsTotal.ebay_fee;
                row.cturnover = tmpItemsTotal.cturnover;
                row.gp = tmpItemsTotal.gp;
                row.bp = tmpItemsTotal.bp;

                row.turnover_pcs = row.turnover / tmpItemsTotal.qty_ordered;
                row.net_turnover_pcs = row.net_turnover / tmpItemsTotal.qty_ordered;
                row.ebay_fee_pcs = row.ebay_fee / tmpItemsTotal.qty_ordered;
                row.cturnover_pcs = row.cturnover / tmpItemsTotal.qty_ordered;
                row.gp_pcs = row.gp / tmpItemsTotal.qty_ordered;
                row.bp_pcs = row.bp / tmpItemsTotal.qty_ordered;

                // Grand total
                totalRow.qty_ordered = totalRow.qty_ordered + tmpItemsTotal.qty_ordered;
                totalRow.turnover = totalRow.turnover + tmpItemsTotal.turnover;
                totalRow.net_turnover = totalRow.net_turnover + tmpItemsTotal.net_turnover;
                totalRow.ebay_fee = totalRow.ebay_fee + tmpItemsTotal.ebay_fee;
                totalRow.cturnover = totalRow.cturnover + tmpItemsTotal.cturnover;
                totalRow.gp = totalRow.gp + tmpItemsTotal.gp;
                totalRow.bp = totalRow.bp + tmpItemsTotal.bp;
              }
            });

            setExpandedRowKeys(keys);

            totalRow.turnover_pcs = totalRow.turnover / totalRow.qty_ordered;
            totalRow.net_turnover_pcs = totalRow.net_turnover / totalRow.qty_ordered;
            totalRow.ebay_fee_pcs = totalRow.ebay_fee / totalRow.qty_ordered;
            totalRow.cturnover_pcs = totalRow.cturnover / totalRow.qty_ordered;
            totalRow.gp_pcs = totalRow.gp / totalRow.qty_ordered;
            totalRow.bp_pcs = totalRow.bp / totalRow.qty_ordered;
            totalRow.cnt = res.data.length;

            setSummaryRow(totalRow);

            if (sn(res.data.length) > 1) {
              res.data.splice(0, 0, totalRow);
              /**
               * currentPage
               * perPage
               * totalPages
               * totalRows
               */
              /* const p = res.pagination;
              p.totalRows += p.totalPages * 1;
              p.perPage += 1;
              res.pagination = p;
              res.total = p.totalRows; */
            }

            return res as any;
          });
        }}
        columns={columns}
        expandable={{
          showExpandColumn: true,

          childrenColumnName: 'mag_order_items',

          expandRowByClick: false,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            setExpandedRowKeys(expandedKeys as any);
          },
        }}
        tableAlertRender={false}
        rowSelection={false}
        rowClassName={(record) => {
          // const defaultCls = !!record.mag_order_items?.length ? '' : 'disable-selection ';
          const defaultCls = 'disable-selection reset-tds-bg ';
          let rowCls = '';
          if (record.entity_id <= 0) {
            rowCls = defaultCls + 'total-row';
          } else
            rowCls =
              defaultCls +
              (record.item_ean?.id && record.item_ean
                ? record?.item_ean?.is_single
                  ? 'row-single'
                  : 'row-multi'
                : '');
          if (!record.mag_order_items?.length) {
            rowCls += ' bg-grey';
          } else {
            rowCls += ' bg-light-blue2';
          }

          return rowCls;
        }}
        pagination={{
          pageSize: 200,
        }}
        columnEmptyText=""
      />
      {/* {selectedRowKeys?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a>
                &nbsp;orders.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Button
            type="primary"
            icon={<ExportOutlined />}
            onClick={() => {
              handleExportSelectedRows(selectedRowKeys);
            }}
          >
            Export
          </Button>
        </FooterToolbar> 
      )}*/}
    </Modal>
  );
};

export default OrderListModal;
