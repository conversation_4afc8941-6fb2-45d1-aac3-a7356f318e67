DROP TABLE IF EXISTS `product_entry_detail`;
DROP TABLE IF EXISTS `product_entry`;

CREATE TABLE `stock_stable` (
                                `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                                `item_id` BIGINT(20) UNSIGNED DEFAULT NULL,
                                `ean_id` BIGINT(20) UNSIGNED DEFAULT NULL,
                                `parent_ean_id` BIGINT(20) UNSIGNED DEFAULT NULL,
                                `wl_id` BIGINT(20) UNSIGNED DEFAULT NULL,
                                `piece_qty` INT(11) DEFAULT 0,
                                `box_qty` INT(11) DEFAULT 0,
                                `case_qty` INT(11) DEFAULT 0,
                                `total_piece_qty` INT(11) DEFAULT 0 COMMENT 'piece_qty + box_qty * case_qty',
                                `exp_date` DATE DEFAULT NULL,
                                `created_on` DATETIME DEFAULT CURRENT_TIMESTAMP(),
                                `created_by` BIGINT(20) UNSIGNED DEFAULT NULL,
                                `updated_on` D<PERSON>ETIME DEFAULT CURRENT_TIMESTAMP(),
                                `updated_by` BIGINT(20) UNSIGNED DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                KEY `FK_stock_stable_item_id` (`item_id`),
                                KEY `FK_stock_stable_ean_id` (`ean_id`),
                                KEY `FK_stock_stable_parent_ean_id` (`parent_ean_id`),
                                KEY `FK_stock_stable_wl_id` (`wl_id`),
                                KEY `IDX_stock_stable_exp_date` (`exp_date`),
                                UNIQUE KEY `UQ_stock_stable_mix` (`ean_id`,`wl_id`,`exp_date`),
                                CONSTRAINT `FK_stock_stable_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE SET NULL,
                                CONSTRAINT `FK_stock_stable_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON DELETE SET NULL ON UPDATE SET NULL,
                                CONSTRAINT `FK_stock_stable_wl_id` FOREIGN KEY (`wl_id`) REFERENCES `warehouse_location` (`id`) ON DELETE SET NULL ON UPDATE SET NULL,
                                CONSTRAINT `FK_stock_stable_parent_ean_id` FOREIGN KEY (`parent_ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE SET NULL
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;
