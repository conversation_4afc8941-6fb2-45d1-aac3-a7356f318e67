import { DictType } from '@/constants';
import Util from '@/util';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-form';
import { Button, Space } from 'antd';
import ButtonGroup from 'antd/lib/button/button-group';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';

const useEanSpecialFilter = (parentLoading?: boolean, lsKey?: string) => {
  const { getDictOptionsCV } = useModel('app-settings');

  const [filterId, setFilterId] = useState<any>();

  const options = useMemo(() => getDictOptionsCV(DictType.EanPageFilter, 'sort', 'asc', 'label'), [getDictOptionsCV]);

  /**
   * Prev / Next navigation in Trademark filter
   * @param mode
   */
  const handleNavigation = useCallback(
    (mode: 'prev' | 'next') => {
      const len = options.length;
      const curId = filterId;
      const index = options.findIndex((x) => x.value === curId) ?? 0;
      const dir: number = mode == 'prev' ? -1 : 1;

      let nextIndex = (index + dir + len) % len;
      if (options[nextIndex]?.value == 'sep') {
        nextIndex = (nextIndex + dir + len) % len;
      }
      setFilterId(options[nextIndex]?.value);
    },
    [options, filterId],
  );

  useEffect(() => {
    setFilterId(Util.getSfValues(`ean_special_filter${lsKey}`)?.['filterId'] || '');
  }, [lsKey]);

  const renderedEle = useMemo(() => {
    return (
      <Space style={{ marginLeft: 64 }}>
        <ProFormSelect
          name="filterId"
          placeholder="00 No Filter"
          width={200}
          allowClear
          showSearch
          options={options}
          formItemProps={{ style: { marginBottom: 0 } }}
          fieldProps={{
            dropdownMatchSelectWidth: false,
            value: filterId,
            onChange(value, option) {
              setFilterId(value);
              Util.setSfValues(`ean_special_filter${lsKey}`, { filterId: value });
            },
          }}
        />
        <ButtonGroup style={{ marginRight: 8 }}>
          <Button
            onClick={() => {
              handleNavigation('prev');
            }}
            icon={<ArrowLeftOutlined />}
            disabled={parentLoading}
          />
          <Button
            onClick={() => {
              handleNavigation('next');
            }}
            icon={<ArrowRightOutlined />}
            disabled={parentLoading}
          />
        </ButtonGroup>
      </Space>
    );
  }, [filterId, handleNavigation, lsKey, options, parentLoading]);

  return { renderedEle, filterId };
};

export default useEanSpecialFilter;
