drop table if exists offer_item_shipped;

CREATE TABLE `offer_item_shipped`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `offer_item_id` bigint(20) unsigned DEFAULT NULL,
    `ibo_pre_id`    bigint(20) unsigned DEFAULT NULL,
    `ean_id`        bigint(20) unsigned DEFAULT NULL,
    `case_qty`      int(11)             DEFAULT NULL,
    `qty`           int(11)             DEFAULT NULL,
    `exp_date`      date                DEFAULT NULL,
    `wa_no`         int(11)             DEFAULT NULL COMMENT 'WA No',
    `wa_date`       date                DEFAULT NULL COMMENT 'WA Date',
    `note`          text                DEFAULT NULL,
    `created_on`    datetime            DEFAULT NULL,
    `created_by`    int(11)             DEFAULT NULL,
    `updated_on`    datetime            DEFAULT NULL,
    `updated_by`    int(11)             DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `FK_offer_item_shipped_offer_item_id` (`offer_item_id`),
    KEY `FK_offer_item_shipped_ean_id` (`ean_id`),
    KEY `FK_offer_item_shipped_offer_ibo_pre_id` (`ibo_pre_id`),
    KEY `IDX_offer_item_shipped_wa_no` (`wa_no`),
    KEY `IDX_offer_item_shipped_wa_date` (`wa_date`),
    KEY `IDX_offer_item_shipped_exp_date` (`exp_date`),
    CONSTRAINT `FK_offer_item_shipped_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_shipped_offer_ibo_pre_id` FOREIGN KEY (`ibo_pre_id`) REFERENCES `ibo_pre` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_shipped_offer_item_id` FOREIGN KEY (`offer_item_id`) REFERENCES `offer_item` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

ALTER TABLE `ibo_pre_management`
    ADD COLUMN `note2` longtext NULL COMMENT 'Note 2' AFTER `note`;


drop table ibo_pre_management_file;

CREATE TABLE `ibo_pre_management_file`
(
    `ibo_pre_management_id` bigint(20) unsigned NOT NULL,
    `file_id`  bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`ibo_pre_management_id`, `file_id`),
    KEY `FK_ibo_pre_management_file_file_id` (`file_id`),
    CONSTRAINT `FK_ibo_pre_management_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_ibo_pre_management_file_ibo_pre_management_id` FOREIGN KEY (`ibo_pre_management_id`) REFERENCES `ibo_pre_management` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;




