import EditableCell from '@/components/EditableCell';
import { DEFAULT_PER_PAGE_PAGINATION, DictType } from '@/constants';
import { MagentoOrderStatus } from '@/pages/Magento/Order';
import { getCrmCaseList, updateCrmCase } from '@/services/foodstore-one/Crm/crm-case';
import { updateEmail } from '@/services/foodstore-one/Email/email';
import Util, { nf2, sn } from '@/util';
import { MailOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Button, Col, Modal, Row, Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useRef } from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useModel } from 'umi';

/**
 * Search form data type
 */
export type CrmCaseListModalSearchFormType = {
  sender?: string; // email
  // sender_name?: string;
  order_id?: number | null;
  increment_id?: string;
  keyword?: string;
  name_address?: string;
};

/**
 * Component props type
 */
type CrmCaseListModalPropsValuesTypes = {
  senderEmail?: string;
  senderName?: string;
  orderId?: number | null;
  incrementId?: string;
  emailId?: number;
  caseId?: number | null;
  // keyword?: string;
};

type CrmCaseListModalProps = {
  values?: CrmCaseListModalPropsValuesTypes;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Email) => Promise<boolean | void>;
  cb?: (action: 'select' | 'deselect', data: any) => void;
};

const CrmCaseListModal: React.FC<CrmCaseListModalProps> = (props) => {
  const { modalVisible, handleModalVisible, values, cb } = props;
  const { getDictByCode, getDictOptionsCV } = useModel('app-settings');

  const [loading, setLoading] = useState<boolean>(false);

  // table
  const actionRef = useRef<ActionType>();

  // Search form
  const searchFormRef = useRef<ProFormInstance<CrmCaseListModalSearchFormType>>();

  const handleCaseSelection = useCallback(
    (caseId: number, deslect?: boolean) => {
      updateEmail({ id: values?.emailId, crm_case_id: deslect ? null : caseId })
        .then((res) => {
          message.success('Updated successfully.');
          cb?.(deslect ? 'deselect' : 'select', res);
          if (!deslect) {
            handleModalVisible(false);
          }
        })
        .catch(Util.error);
    },
    [values?.emailId, cb, handleModalVisible],
  );

  const columns: ProColumns<API.CrmCase>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      width: 60,
      hideInSearch: true,
      defaultSortOrder: 'descend',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: true,
      width: 100,
      hideInSearch: true,
      render: (dom, record, index) => {
        return (
          <EditableCell
            dataType="select"
            defaultValue={record.status}
            dataOptions={getDictOptionsCV(DictType.CRMCaseStatus)}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              return updateCrmCase(sn(record.id), {
                status: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  /* setDatasource((prev) => {
                    const newDatasource = [...prev];
                    newDatasource[index] = { ...newDatasource[index], ...res };
                    return newDatasource;
                  }); */
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {record.status ? getDictByCode(record.status) : ' - '}
          </EditableCell>
        );
      },
    },
    {
      title: 'Order',
      dataIndex: ['order'],
      hideInSearch: true,
      children: [
        {
          title: 'Order ID',
          width: 70,
          align: 'right',
          dataIndex: ['order', 'entity_id'],
        },
        {
          title: 'Increment ID',
          width: 120,
          copyable: true,
          dataIndex: ['order', 'increment_id'],
        },
        {
          title: 'Status',
          width: 80,
          copyable: true,
          dataIndex: ['order', 'status'],
          render(dom, record, index, action, schema) {
            return record.order?.status ? <MagentoOrderStatus status={record.order?.status} /> : null;
          },
        },
        {
          title: 'Name',
          width: 140,
          copyable: true,
          dataIndex: ['order', 'sa_fullname'],
          render(dom, record, index, action, schema) {
            return record.order?.sa_fullname;
          },
        },
        {
          title: 'Items Qty',
          dataIndex: ['order', 'total_item_count'],
          sorter: true,
          align: 'right',
          width: 80,
          tooltip: 'Qty of included items',
          showSorterTooltip: false,
        },
        {
          title: 'Orderd Qty',
          dataIndex: ['order', 'total_qty_ordered'],
          sorter: true,
          align: 'right',
          width: 70,
        },

        {
          title: 'Grand Total',
          dataIndex: ['order', 'grand_total'],
          sorter: true,
          align: 'right',
          width: 70,
          render: (dom, record) => nf2(record.order?.grand_total),
        },
      ],
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      align: 'center',
      render: (dom, record) => (
        <>
          <Row>
            <Col flex={'100%'}>
              {record.id == values?.caseId ? (
                <Button size="small" type="primary" ghost onClick={() => handleCaseSelection(sn(record.id), true)}>
                  Deselect
                </Button>
              ) : (
                <Button
                  size="small"
                  type="primary"
                  onClick={() => handleCaseSelection(sn(record.id))}
                  title="Select a case"
                >
                  Select
                </Button>
              )}
            </Col>
          </Row>
        </>
      ),
    },
  ];

  useEffect(() => {
    if (modalVisible) {
      const sfValues: CrmCaseListModalSearchFormType = {
        sender: values?.senderEmail,
        // sender_name: values?.senderName,
        // order_id: values?.orderId,
        // increment_id: values?.incrementId,
        keyword: '',
      };
      searchFormRef.current?.resetFields();
      searchFormRef.current?.setFieldsValue(sfValues);
      actionRef.current?.reload();
    }
  }, [values?.senderEmail, modalVisible]);

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      title={
        <Space size={24}>
          <div>Cases List </div>
          <div>
            <MailOutlined /> {values?.senderEmail}
          </div>
        </Space>
      }
      width={1300}
      footer={false}
    >
      <ProForm<CrmCaseListModalSearchFormType>
        layout="inline"
        size="small"
        formRef={searchFormRef}
        isKeyPressSubmit
        className="search-form"
        initialValues={{}}
        submitter={{
          submitButtonProps: {
            loading: loading,
            htmlType: 'submit',
          },
          onSubmit: () => actionRef.current?.reload(),
          onReset: () => actionRef.current?.reload(),
          render: (form, dom) => {
            return [...dom];
          },
        }}
      >
        <ProFormText name={'sender'} label="Sender" width={140} placeholder={'Sender'} />
        <ProFormText name={'order_id'} label="Order ID" width={90} placeholder={'Order ID'} />
        <ProFormText name={'increment_idR'} label="Increment ID" width={130} placeholder={'Increment ID'} />
        <ProFormText
          name={'name_address'}
          label="Name & Address"
          width={130}
          placeholder={'Name & Address'}
          tooltip="Search email sender name, order name & address"
        />
        <ProFormText
          name={'keyword'}
          label="Keyword"
          width={130}
          placeholder={'Keyword'}
          tooltip="Search email subject, email body, order name & address, case notes"
        />
      </ProForm>

      <ProTable<API.CrmCase, API.PageParams>
        headerTitle={'Cases list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        bordered
        size="small"
        cardProps={{ bodyStyle: { padding: 0 } }}
        /* toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handlePullEmails();
            }}
          >
            <DownloadOutlined /> Sync
          </Button>,
        ]} */
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          setLoading(true);
          return getCrmCaseList(
            {
              ...params,
              ...searchFormValues,
              with: 'order',
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default CrmCaseListModal;
