ALTER TABLE `warehouse_picklist`
    ADD COLUMN `is_pre` B<PERSON><PERSON><PERSON>N DEFAULT 0 NULL COMMENT 'is pre-picklist?' AFTER `id`;


ALTER TABLE `warehouse_picklist`
    CHANGE `status_pre_picking` `status_pre_picking` TINYINT (4) DEFAULT 0 NOT NULL COMMENT 'Pre Picking Status-availabe if is_pre=1: 0: Open, 1: Processing, 2: Done',
    CHANGE `status_picking` `status_picking` TINYINT (4) DEFAULT 0 NOT NULL COMMENT 'Picking Status-availabe if is_pre=0: 0: Open, 1: Processing, 2: Done',
    CHANGE `status_packing` `status_packing` TINYINT (4) DEFAULT 0 NOT NULL COMMENT 'Packing Status-availabe if is_pre=0: 0: Open, 1: Processing, 2: Done';

