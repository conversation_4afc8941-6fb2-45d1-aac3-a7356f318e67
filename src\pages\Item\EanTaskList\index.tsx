import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Tag, Popconfirm, Spin } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { nl2br, sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getEanTaskList, deleteEanTask, updateEanTask } from '@/services/foodstore-one/Item/ean-task';
import { TaskStatus } from '@/constants';
import { useModel } from 'umi';

/**
 *  Delete node
 *
 * @param selectedRows
 */

export const handleRemove = async (selectedRow: Partial<API.EanTask>) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRow) return true;

  try {
    await deleteEanTask({
      id: selectedRow.id,
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export const TaskStatusComp: React.FC<{
  status: TaskStatus;
  id?: number;
  actionRef: React.MutableRefObject<any>;
}> = ({ status, id, actionRef }) => {
  const [loading, setLoading] = useState<boolean>(false);
  let statusText = 'Open';
  let color = 'grey';

  switch (sn(status)) {
    case TaskStatus.Done:
      statusText = 'Done';
      color = 'green';
      break;
    case TaskStatus.InProgress:
      statusText = 'In Progress';
      color = 'blue';
      break;
  }

  const changeStatus = async (e: any) => {
    setLoading(true);
    const nextStatus = (sn(status) + 1) % 3;
    updateEanTask(id, { status: nextStatus })
      .then((res) => {
        actionRef.current.reload();
      })
      .finally(() => setLoading(false));
  };

  return (
    <Spin spinning={loading}>
      <Tag color={color} className="cursor-pointer m-0 text-sm" title="click to change." onClick={changeStatus}>
        {statusText}
      </Tag>
    </Spin>
  );
};

type EanTaskListProps = {
  ean?: string;
  reloadList?: () => void;
  refreshTick?: number;
};

const EanTaskList: React.FC<EanTaskListProps> = ({ ean, reloadList, refreshTick }) => {
  const { getDictByCode } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.EanTask>();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<API.EanTask>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 70,
      align: 'center',
      sorter: true,
      renderText: (val: string) => `${val}`,
    },
    {
      title: 'Category',
      dataIndex: ['category_code'],
      width: 130,
      sorter: false,
      render: (__, record) => getDictByCode(record.category_code),
    },
    {
      title: 'Task',
      dataIndex: 'task',
      sorter: false,
      render: (dom, record) => <span dangerouslySetInnerHTML={{ __html: nl2br(record.task) }} />,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: false,
      align: 'center',
      className: 'p-0',
      width: 90,
      render: (dom, record) => (
        <TaskStatusComp status={record.status ?? TaskStatus.Open} id={record.id} actionRef={actionRef} />
      ),
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          <EditOutlined />
        </a>,

        <Popconfirm
          key="delete"
          title={<>Are you sure you want to delete?</>}
          okText="Yes"
          cancelText="No"
          overlayStyle={{ maxWidth: 350 }}
          onConfirm={() => {
            handleRemove({ id: record?.id }).then((res) => {
              actionRef.current?.reload();
            });
          }}
        >
          <DeleteOutlined className="c-red" />
        </Popconfirm>,
      ],
    },
  ];

  useEffect(() => {
    if (refreshTick) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  return (
    <>
      <ProTable<API.EanTask, API.PageParams>
        headerTitle={'Ean tasks - ' + ean}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        cardProps={{ bodyStyle: { padding: 0 } }}
        options={{ fullScreen: false, density: false, reload: true, setting: false, search: true }}
        search={false}
        request={async (params, sort, filter) => {
          const res = await getEanTaskList(
            {
              ...params,
              ean: ean,
              pageSize: 10,
              with: 'categoryDict',
            },
            { created_on: 'descend', ...sort },
            filter,
          );
          return res;
        }}
        pagination={{
          hideOnSinglePage: true,
        }}
        columns={columns}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            size="small"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        rowSelection={false}
      />
      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        values={{ ean: ean }}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
          reloadList?.();
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </>
  );
};

export default EanTaskList;
