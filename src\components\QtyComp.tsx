import { ni, sn } from '@/util';
import { CSSProperties } from 'react';

type QtyCompProps = {
  case_qty?: number;
  qty?: number;
  show_zero?: boolean;
  style?: CSSProperties;
  title?: string;
};
const QtyComp: React.FC<QtyCompProps> = ({ case_qty, qty, show_zero, style, title, ...rest }) => {
  return qty ? (
    <span style={style} title={title}>
      {sn(case_qty) > 1 ? `${ni(qty, true)} x ${ni(case_qty, true)}` : ni(qty, true)}
    </span>
  ) : show_zero ? (
    <span style={style} title={title}>
      0
    </span>
  ) : null;
};

export default QtyComp;
