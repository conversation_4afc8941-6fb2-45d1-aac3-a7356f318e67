import { updateImportSearchFilters } from '@/services/foodstore-one/Import/import-search-filter';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormCheckbox, ProFormGroup, ProFormList, ProFormText } from '@ant-design/pro-form';
import { message } from 'antd';
import type { DefaultOptionType } from 'antd/lib/select';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef } from 'react';

type FormValueType = Partial<API.Import> & {
  trademark?: string;
};

type ImportSearchFiltersModalFormProps = {
  trademark?: DefaultOptionType;
  values?: Partial<API.Import>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Import) => void;
};

const ImportSearchFiltersModalForm: React.FC<ImportSearchFiltersModalFormProps> = (props) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();
  const { values, trademark, modalVisible, handleModalVisible, onSubmit } = props;

  useEffect(() => {
    if (modalVisible && trademark) {
      formRef.current?.setFieldsValue({
        trademark: `${trademark.label || ''}`,
        ...values,
        import_search_filters: values?.import_search_filters?.map((x) => ({ ...x, uid: x.id })) || [],
      });
    }
  }, [modalVisible, values, trademark]);

  return (
    <ModalForm
      title={'Search Terms of Imported XLS data'}
      width="600px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const hide = message.loading('Updating search terms...', 0);
        updateImportSearchFilters({
          supplier_id: values?.supplier_id,
          trademark: trademark?.label as string,
          import_search_filters: value.import_search_filters,
        })
          .then((res) => {
            onSubmit?.(res);
          })
          .finally(hide);
      }}
    >
      <div>Supplier: {values?.supplier?.name}</div>
      <div style={{ marginBottom: 16, marginTop: 8 }}>Trademark: {trademark?.label}</div>

      <ProFormList
        label="Search Terms"
        name={['import_search_filters']}
        copyIconProps={false}
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: 'Add a new search term',
        }}
        creatorRecord={(index: number) => {
          return {
            uid: Util.genNewKey(),
          };
        }}
      >
        <ProFormGroup>
          <ProFormText name="keyword_title" width="md" label="Title Keyword" placeholder="Keyword" />
          <ProFormCheckbox name="is_all_supplier" width={100} label="All supplier?" />
        </ProFormGroup>
      </ProFormList>
    </ModalForm>
  );
};

export default ImportSearchFiltersModalForm;
