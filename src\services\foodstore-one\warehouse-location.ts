/* eslint-disable */
import Util from '@/util';
import { request } from 'umi';

const urlPrefix = '/api/warehouse-location';

export async function getWarehouseLocationList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function updateWarehouseLocation(data: API.WarehouseLocation, options?: { [key: string]: any }) {
  return request<API.WarehouseLocation>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

export async function updateWarehouseLocationPriorities(
  data: API.WarehouseLocation & { rows?: API.WarehouseLocation[] },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<boolean>>(`${urlPrefix}/updateBulkPriority`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/users-list */
export async function addWarehouseLocation(data: API.WarehouseLocation, options?: { [key: string]: any }) {
  return request<API.WarehouseLocation>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/rule */
export async function deleteWarehouseLocation(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/item/ac-list
 *
 * get the autocomplete lists.
 *
 */
export async function getWarehouseLocationACList(params: { [key: string]: string }, sort?: any) {
  return request<API.BaseResult>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 10000,
      sort_detail: JSON.stringify(sort ?? { name: 'ascend' }),
    },
    withToken: true,
  }).then((res) =>
    res.message.map((x: API.WarehouseLocation) => ({
      ...x,
      value: Util.safeInt(x.id),
      label: `${x.name}${x.address ? ` - ${x.address}` : ''}`,
    })),
  );
}
export async function getWarehouseLocationMap(params: { [key: string]: string }, sort?: any) {
  return request<API.BaseResult>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 10000,
      sort_detail: JSON.stringify(sort ?? { name: 'ascend' }),
    },
    withToken: true,
  }).then((res) => {
    const map = {};
    res.message.forEach((x: API.WarehouseLocation) => {
      map[x.id || 0] = { value: Util.safeInt(x.id), label: `${x.name} - ${x.address}` };
    });
    return map;
  });
}
