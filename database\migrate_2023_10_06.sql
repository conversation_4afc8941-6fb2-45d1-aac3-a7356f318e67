CREATE TABLE `item_ean_update_log`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `type`        varchar(255)        DEFAULT NULL COMMENT 'longging type',
    `request_uri` varchar(255)        DEFAULT NULL,
    `ean_id`      bigint(20) unsigned DEFAULT NULL COMMENT 'Referenced EAN Id',
    `data`        longtext            DEFAULT NULL COMMENT 'JSON data',
    `created_by`  bigint(20)          DEFAULT NULL COMMENT 'Updater',
    `created_on`  datetime            DEFAULT NULL COMMENT 'Created date',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


truncate table item_ean_gdsn;

ALTER TABLE `item_ean_gdsn`
    <PERSON><PERSON><PERSON> `sync_last_change_dt` `sync_last_change_dt` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'GDSN last submitted date';
