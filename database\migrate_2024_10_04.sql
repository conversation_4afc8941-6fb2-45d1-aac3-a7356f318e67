ALTER TABLE `stock_stable_problem`
    CHANGE `piece_qty` `piece_qty` INT(11) DEFAULT 0 NULL COMMENT 'Taken pieces qty',
    <PERSON>ANGE `box_qty` `box_qty` INT(11) DEFAULT 0 NULL COMMENT 'Taken boxes qty',
    ADD COLUMN `qty_missing` INT NULL COMMENT 'Missing qty in this location. Box or piece' AFTER `sku`,
    CHANGE `qty_ordered` `qty_ordered` INT(11) NULL COMMENT 'Ordered quantity. Box or piece';

ALTER TABLE `stock_stable_problem`
    CHANGE `qty_ordered` `qty_ordered` INT(11) NULL COMMENT 'Ordered quantity. Box or piece. Reference only',
    ADD UNIQUE INDEX `UQ_stock_stable_problem_mix1` (
                                                     `stock_stable_id`,
                                                     `order_item_id`
        );



update stock_stable_problem
set stock_stable_problem.qty_missing = stock_stable_problem.qty_ordered - stock_stable_problem.total_piece_qty / stock_stable_problem.case_qty
where stock_stable_problem.qty_missing is null
;
