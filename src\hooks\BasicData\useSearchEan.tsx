import { getEanDetail } from '@/services/foodstore-one/Item/ean';
import type { ProFormFieldProps, ProFormInstance } from '@ant-design/pro-form';
import { ProFormItem, ProFormText } from '@ant-design/pro-form';
import { message, Typography } from 'antd';
import { debounce } from 'lodash';
import React, { useCallback, useMemo, useState } from 'react';

/**
 * Searching EAN.
 */
export default (
  defaultParams?: Record<string, any>,
  formRef?: React.MutableRefObject<ProFormInstance | undefined>,
  eleOptions?: ProFormFieldProps,
) => {
  const [loading, setLoading] = useState<boolean>(false);

  const [itemEan, setItemEan] = useState<API.Ean | null>(null);

  // ---------------------------------------------------------------------------------
  // Search EAN
  // ---------------------------------------------------------------------------------
  const handleSearchEan = async (v: string, cb: any) => {
    if (!v) {
      cb(null);
      return;
    }
    message.destroy();
    setLoading(true);
    const hide = message.loading('Searching EAN...', 0);
    await getEanDetail({ sku: v })
      .then((res) => {
        cb(res);
      })
      .catch(() => {
        cb(null);
      })
      .finally(() => {
        setLoading(false);
        hide();
      });
  };

  const debouncedHandleSearchEan = useCallback(
    debounce((newValue, cb) => handleSearchEan(newValue, cb), 330),
    [],
  );

  const formElements = useMemo(() => {
    return (
      <>
        <ProFormText
          formItemProps={{ style: { marginBottom: 0 } }}
          label="SKU"
          {...eleOptions}
          fieldProps={{
            placeholder: 'SKU',
            onChange: (e: any) => {
              const value = e.target.value;

              debouncedHandleSearchEan(value, (eanData: API.Ean) => {
                if (eanData) {
                  console.log('[offerItem][search][eanData]', eanData);
                  setItemEan(eanData);
                } else {
                  setItemEan(null);
                }
              });
            },
            onBlur: () => {},
          }}
        />
        <ProFormItem label=" " style={{ marginBottom: 0 }} colon={false}>
          <Typography.Text style={{ display: 'inline-block', fontSize: 14 }} copyable={{ text: itemEan?.sku || '' }}>
            {itemEan?.sku} {itemEan?.is_single ? '' : `(Qty.Pkg: ${itemEan?.attr_case_qty ?? '-'})`}
          </Typography.Text>
        </ProFormItem>

        <ProFormItem label="Name" style={{ marginTop: 4 }}>
          <Typography.Text style={{ display: 'inline-block', fontSize: 14 }} copyable>
            {itemEan?.ean_texts?.[0]?.name}
          </Typography.Text>
        </ProFormItem>
      </>
    );
  }, [eleOptions, loading, debouncedHandleSearchEan]);

  return { itemEan, loading, eleOptions, formElements, debouncedHandleSearchEan };
};
