import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Drawer } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm, { getWlPriority } from './components/UpdateForm';

import Util, { ni } from '@/util';
import CreateForm from './components/CreateForm';
import {
  getWarehouseLocationList,
  deleteWarehouseLocation,
  updateWarehouseLocationPriorities,
} from '@/services/foodstore-one/warehouse-location';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.WarehouseLocation[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteWarehouseLocation({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const WarehouseLocation: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.WarehouseLocation>();
  const [selectedRowsState, setSelectedRows] = useState<API.WarehouseLocation[]>([]);

  const columns: ProColumns<API.WarehouseLocation>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      width: 100,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      sorter: true,
      hideInForm: true,
      width: 100,
      align: 'right',
      renderText: (val: string) => `${ni(val)}`,
    },
    /* {
      title: 'Code',
      dataIndex: 'code',
      sorter: true,
      hideInForm: true,
      renderText: (val: string) => `${val}`,
    }, */
    /* {
      title: 'Address',
      dataIndex: 'address',
      sorter: true,
      hideInForm: true,
      renderText: (val: string) => `${val}`,
    }, */
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey',
      render(dom, record) {
        return Util.dtToDMYHHMM(record.created_on);
      },
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey',
      render(dom, record) {
        return Util.dtToDMYHHMM(record.updated_on);
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      // tip: 'The username is the unique key',
      colSize: 1,
      search: false,
      className: 'c-grey',
      width: 80,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
    {
      title: '',
      dataIndex: 'id___',
      colSize: 1,
      search: false,
      className: 'c-grey',
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.WarehouseLocation, API.PageParams>
        headerTitle={'Warehouse Location list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={getWarehouseLocationList}
        columns={columns}
        pagination={{
          onChange: (current, pageSize) => {
            // setPagination({ page: current, pageSize: pageSize });
          },
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              warehouse locations &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            type="primary"
            onClick={async () => {
              const hide = message.loading('Updating priorities...', 0);
              updateWarehouseLocationPriorities({
                rows: selectedRowsState.map((x) => ({
                  id: x.id,
                  name: x.name,
                  priority: getWlPriority(x.name || ''),
                })),
              })
                .then((res) => {
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            Update Priority
          </Button>
          <Button
            danger
            ghost
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.WarehouseLocation>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<API.WarehouseLocation>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default WarehouseLocation;
