/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/offer-item';

/** get GET /api/offer-item */
export async function getOfferItemList(params: API.PageParams, sort: any, filter: any) {
  return request<API.ResultObject<API.PaginatedResult<API.OfferItem> & { imports?: API.Import[] }>>(`${urlPrefix}`, {
    // return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    imports: res.message.imports ?? [],
  }));
}

export async function getOfferItem(params: API.PageParams): Promise<API.OfferItem> {
  return getOfferItemList(params, null, null).then(res => {
    return res.data?.[0];
  })
}

/** put PUT /api/offer-item/{id} */
export async function updateOfferItem(id: number, data: API.OfferItem, options?: { [key: string]: any }) {
  return request<API.OfferItem>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}


/** put PUT /api/offer-item/{id}/assignIboPre */
export async function assignIboPre(id: number, ibo_pre_id: number, mode: 'unlink' | 'link', options?: { [key: string]: any }) {
  return request<API.OfferItem>(`${urlPrefix}/${id}/assignIboPre`, {
    method: 'PUT',
    data: {
      ibo_pre_id,
      mode,
    },
    ...(options || {}),
  });
}



/** post POST /api/offer-item */
export async function addOfferItem(data: API.OfferItem, options?: { [key: string]: any }) {
  return request<API.OfferItem>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/offer-item/bulk */
export async function addOfferItemBulk(data: API.OfferItem & { list_str?: string }, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.OfferItem[]>>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/offer-item */
export async function deleteOfferItem(id: number | string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}


export async function exportOfferItemList(params: API.PageParamsExt & { group_id?: number | string }, sort?: any, filter?: any) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export`, {
    method: 'GET',
    params: {
      ...params,
      sort,
      ...filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

export async function exportOfferItemListWithStocks(data: { offer_id: number, ids?: number[] }, sort?: any, filter?: any) {
  return request<API.ResultDownloadable>(`${urlPrefix}/exportWithStocks`, {
    method: 'GET',
    params: {
      ...data,
      sort,
      ...filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

export async function exportOfferItemListWithIboPres(data: { offer_id: number, ids?: number[] }, sort?: any, filter?: any) {
  return request<API.ResultDownloadable>(`${urlPrefix}/exportWithIboPres`, {
    method: 'GET',
    params: {
      ...data,
      sort,
      ...filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/**
 * PUT /api/offer-item/updateItemEanWishQty *
 *
 */
export async function updateItemEanWishQty(data: { dir?: -1 | 1, offer_id: number }) {
  return request<API.AppApiResponse>(`${urlPrefix}/updateItemEanWishQty`, {
    method: 'PUT',
    data: data,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * POST 
 * 
 * /api/offer-item/addToIboPre
 */
export async function addToIboPre(ibo_pre_management_id: number, offer_item_ids: number[]) {
  return request<API.AppApiResponse>(`${urlPrefix}/addToIboPre`, {
    method: 'POST',
    data: {
      ibo_pre_management_id,
      offer_item_ids,
    },
    paramsSerializer,
  }).then((res) => res.message);
}