ALTER TABLE `item_ean_price_history`
    ADD COLUMN `type` int default 1 AFTER `price_type_id`;

ALTER TABLE `item_ean_price_history`
    ADD COLUMN `detail` text default null AFTER `deleted_on`;

ALTER TABLE `item_ean_price_history`
    ADD COLUMN `updated_on` datetime default null AFTER `created_on`;


ALTER TABLE `item_ean_price_history`
    CHANGE `type` `type` INT(11) DEFAULT 1 NULL COMMENT '1: Price change, 2: Discount change';

