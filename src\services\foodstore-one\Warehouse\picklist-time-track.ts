/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/warehouse/picklist-time-track';


/** 
 * Get time tracks
 * 
 * GET /api/warehouse/picklist-time-track */
export async function getPicklistTimeTrackList(
    params?: API.PageParams,
    sort?: any,
    filter?: any,
): Promise<API.PaginatedResult<API.WarehousePicklistTimeTrack>> {
    return request<API.BaseResult>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params?.pageSize,
            page: params?.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
    }));
}


/** 
 * Create time track record
 * POST /api/warehouse/picklist-time-track */
export async function createPicklistTimeTrack(
    data?: API.WarehousePicklistTimeTrack,
    options?: { [key: string]: any },
): Promise<API.WarehousePicklistTimeTrack> {
    return request<API.BaseResult>(`${urlPrefix}`, {
        method: 'POST',
        data: data,
        ...(options || {}),
    }).then((res) => res.message);
}


/** 
 * Update time track record 
 * 
 * PUT /api/warehouse/picklist-time-track/{id} */
export async function updatePicklistTimeTrack(
    id?: number,
    data?: API.WarehousePicklistTimeTrack,
    options?: { [key: string]: any },
): Promise<API.WarehousePicklistTimeTrack> {
    return request<API.BaseResult>(`${urlPrefix}/${id}`, {
        method: 'PUT',
        data: data,
        ...(options || {}),
    }).then((res) => res.message);
}


/** 
 * 
 * Get picking time stats per day.
 * 
 * GET /api/warehouse/picklist-time-track/getPickingTimeStatsSummary 
 */
export async function getPickingTimeStatsSummary(
    params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
    sort: any,
    filter: any,
): Promise<API.PaginatedResult<any>> {
    return request<API.BaseResult>(`${urlPrefix}/getPickingTimeStatsSummary`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        dataTotal: res.message.dataTotal,
        success: res.status == 'success',
        total: res.message.data.length,
        avgRow: res.message.avgRow,
    }));
}