import { getSupplierAddACList } from '@/services/foodstore-one/Import/import';
import Util from '@/util';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';
import type { DefaultOptionType } from 'antd/lib/select';
import { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion list of supplier_add
 */
export default (defaultParams?: Record<string, any>, eleOptions?: ProFormSelectProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [supplierAddOptions, setSupplierAddOptions] = useState<DefaultOptionType[]>([]);
  const [supplierAdd, setSupplierAdd] = useState<DefaultOptionType>();

  const searchSupplierAddOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getSupplierAddACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setSupplierAddOptions([{ value: '*', label: ' - Any -' }, ...res]);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  useEffect(() => {
    searchSupplierAddOptions();
  }, [searchSupplierAddOptions]);

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        {...eleOptions}
        name={eleOptions?.name ?? 'supplier_id'}
        label={eleOptions?.label ?? 'Supplier_ADD'}
        options={supplierAddOptions}
        fieldProps={{
          ...eleOptions?.fieldProps,
          onSearch(value) {
            searchSupplierAddOptions({ keyWords: value });
          },
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          onChange: (value, option) => {
            setSupplierAdd(option as any);
          },
        }}
      />
    );
  }, [eleOptions, supplierAddOptions]);

  return { supplierAddOptions, searchSupplierAddOptions, loading, formElements, supplierAdd };
};
