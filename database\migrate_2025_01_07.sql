CREATE TABLE `offer_recv_weight`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `offer_id`      bigint(20) unsigned DEFAULT NULL,
    `pallet_weight` decimal(20, 4)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    <PERSON><PERSON><PERSON> `FK_offer_recv_weight_offer_id` (`offer_id`),
    CONSTRAINT `FK_offer_recv_weight_offer_id` FOREIGN KEY (`offer_id`) REFERENCES `offer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;



CREATE TABLE `offer_recv_file`
(
    `offer_id` bigint(20) unsigned NOT NULL,
    `file_id`  bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`offer_id`, `file_id`),
    KEY `FK_offer_recv_file_file_id` (`file_id`),
    CONSTRAINT `FK_offer_recv_file_file_id` FOREI<PERSON>N KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_recv_file_offer_id` FOREIGN KEY (`offer_id`) REFERENCES `offer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;



ALTER TABLE `offer`
    ADD COLUMN `recv_note` TEXT NULL COMMENT 'Note of received pallets' AFTER `gfc_note`;


