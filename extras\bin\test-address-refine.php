<?php

use App\Lib\Func;
use App\Models\ItemEan;
use App\Models\Magento\MagProductAttributeSet;
use App\Models\Trademark;
use Illuminate\Support\Facades\DB;

require __DIR__ . '/../../src/App/App.php';

$addresses = [
    'Möhlaustraße, 7 b',
    'Möhlaustraße, 26 b',
    'Möhlaustraße, 26b',
];

foreach ($addresses as $addr) {
    echo sprintf("%20s : %s", $addr, Func::refineAddress($addr)) . PHP_EOL;
}

// Real order
/** @var \App\Models\Magento\MagOrder $order */
//$order = \App\Models\Magento\MagOrder::findOrFail(13351);
$order = \App\Models\Magento\MagOrder::findOrFail(13148);

$item = $order->toArray();
$item['extension_attributes']['shipping_assignments'] = [

        [

                'shipping' => [
                    'address' => $item['detail']['shipping_address'],
                ]

        ]

];
$item['billing_address'] = $item['detail']['billing_address'];

$item['extension_attributes']['payment_additional_info'] =$item['detail']['extension_attributes']['payment_additional_info'] ?? null;

$newArr = \App\Models\Magento\MagOrder::getBoundData($item);

$newArr['detail'] = json_decode($newArr['detail']);

print_r($newArr);