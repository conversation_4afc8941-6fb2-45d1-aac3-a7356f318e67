/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/fileBrowser';

/** 
 * Get root path configured on sys_dict
 * 
 * GET /api/fileBrowser/getRoot
 */
export async function getRootFolder(params?: any) {
    return request<API.ResultObject<API.FmFile>>(`${urlPrefix}/getRoot`, {
        method: 'GET',
        params,
        paramsSerializer,
        withToken: true,
    }).then((res) => res.message);
}

/** 
 * 
 * GET /api/fileBrowser/getChildren */
export async function getChildrenInFolder(
    params: Partial<API.PageParams> & { id?: string },
    sort?: any,
    filter?: any,
) {
    return request<API.ResultObject<{ data: API.FmFile[], folderChain: API.FmFile[] }>>(`${urlPrefix}/getChildren`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        paramsSerializer,
        withToken: true,
    }).then((res) => res.message);
}


export function getFileUrlInBrowser(id?: string) {
    return `${API_URL}/api/fileBrowser/getFile?id=${id}`;
}

export function getFileUrlInMiscFolder(id?: string) {
    return `${API_URL}/api/fileBrowser/getFileInMisc?id=${id}`;
}


