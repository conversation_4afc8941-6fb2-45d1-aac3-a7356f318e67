import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/basic-data/order-shipping-desc-provider-map';

/**
 * Get providers list.
 *
 * GET /api/basic-data/order-shipping-desc-provider-map
 */
export async function getOrderShippingDescProviderMapList(params: API.PageParams, sort: any, filter: any) {
  return request<API.ResultList<API.ShippingDescProvider>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/basic-data/order-shipping-desc-provider-map  */
export async function updateOrderShippingDescProviderMap(
  id: number,
  data: API.ShippingDescProvider,
  options?: Record<string, any>,
) {
  return request<API.ResultObject<API.ShippingDescProvider>>(`${urlPrefix}/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/basic-data/order-shipping-desc-provider-map  */
export async function addOrderShippingDescProviderMap(data: API.ShippingDescProvider, options?: Record<string, any>) {
  return request<API.ResultObject<API.ShippingDescProvider>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/**
 * Import shipping description
 * POST /api/basic-data/order-shipping-desc-provider-map/import  */
export async function importOrderShippingDescProviderMap() {
  return request<API.BaseResult>(`${urlPrefix}/import`, {
    method: 'POST',
  }).then((res) => res.message);
}

/** put DELETE /api/basic-data/order-shipping-desc-provider-map  */
export async function deleteOrderShippingDescProviderMap(id: number, options?: Record<string, any>) {
  return request<API.BaseResult>(`${urlPrefix}/` + id, {
    method: 'DELETE',
    ...(options || {}),
  });
}
