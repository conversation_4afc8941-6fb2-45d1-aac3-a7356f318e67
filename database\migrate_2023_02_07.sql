CREATE TABLE `xmag_order_shipment_comment`
(
    `entity_id`            int(11)              NOT NULL AUTO_INCREMENT COMMENT 'Entity ID',
    `parent_id`            int(11)              NOT NULL COMMENT 'Parent ID',
    `is_customer_notified` int(11)                       DEFAULT NULL COMMENT 'Is Customer Notified',
    `is_visible_on_front`  smallint(5) unsigned NOT NULL DEFAULT 0 COMMENT 'Is Visible On Front',
    `comment`              text                          DEFAULT NULL COMMENT 'Comment',
    `created_at`           timestamp            NOT NULL DEFAULT current_timestamp() COMMENT 'Created At',
    PRIMARY KEY (`entity_id`),
    KEY `SALES_SHIPMENT_COMMENT_CREATED_AT` (`created_at`),
    KEY `SALES_SHIPMENT_COMMENT_PARENT_ID` (`parent_id`),
    CONSTRAINT `SALES_SHIPMENT_COMMENT_PARENT_ID_SALES_SHIPMENT_ENTITY_ID` FOREIGN KEY (`parent_id`) REFERENCES `xmag_order` (`entity_id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='Sales Flat Shipment Comment';

ALTER TABLE `user` ADD COLUMN `settings` LONGTEXT NULL COMMENT 'User setting in JSON' AFTER `role`;

