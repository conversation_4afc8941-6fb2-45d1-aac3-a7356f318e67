import {
  QuestionOutlined,
  DashboardOutlined,
  SmileOutlined,
  FormOutlined,
  TabletOutlined,
  ProfileOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  UserOutlined,
  HighlightOutlined,
  TableOutlined,
  CloseCircleOutlined,
  DragOutlined,
  ShopOutlined,
  CarryOutOutlined,
  GroupOutlined,
  AlertOutlined,
  SettingOutlined,
  FileTextOutlined,
  MailOutlined,
  CloudServerOutlined,
  FileExcelOutlined,
  SwapOutlined,
  NodeIndexOutlined,
  AreaChartOutlined,
  CalendarOutlined,
  MonitorOutlined,
  LogoutOutlined,
  FileSearchOutlined,
  ShoppingCartOutlined,
  BarcodeOutlined,
  ContainerOutlined,
  ImportOutlined,
  BarChartOutlined,
  GoogleOutlined,
  EuroCircleOutlined,
  EditOutlined,
  CheckSquareOutlined,
} from '@ant-design/icons';
import * as PropTypes from 'prop-types';

const IconSelector: React.FC<any> = ({ type, ...rest }) => {
  const getIcon = (iconType: string) =>
    ({
      QuestionOutlined: <QuestionOutlined {...rest} />,
      dashboard: <DashboardOutlined {...rest} />,
      DashboardOutlined: <DashboardOutlined {...rest} />,
      SmileOutlined: <SmileOutlined {...rest} />,
      FormOutlined: <FormOutlined {...rest} />,
      TabletOutlined: <TabletOutlined {...rest} />,
      ProfileOutlined: <ProfileOutlined {...rest} />,
      CheckCircleOutlined: <CheckCircleOutlined {...rest} />,
      WarningOutlined: <WarningOutlined {...rest} />,
      UserOutlined: <UserOutlined {...rest} />,
      HighlightOutlined: <HighlightOutlined {...rest} />,
      CloseCircleOutlined: <CloseCircleOutlined {...rest} />,
      TableOutlined: <TableOutlined {...rest} />,
      DragOutlined: <DragOutlined {...rest} />,
      form: <FormOutlined />,
      ShopOutlined: <ShopOutlined />,
      CarryOutOutlined: <CarryOutOutlined />,
      GroupOutlined: <GroupOutlined />,
      SettingOutlined: <SettingOutlined />,
      AlertOutlined: <AlertOutlined />,
      table: <TableOutlined />,
      user: <UserOutlined />,
      FileTextOutlined: <FileTextOutlined />,
      MailOutlined: <MailOutlined />,
      CloudServerOutlined: <CloudServerOutlined />,
      FileExcelOutlined: <FileExcelOutlined />,
      NodeIndexOutlined: <NodeIndexOutlined />,
      SwapOutlined: <SwapOutlined />,
      AreaChartOutlined: <AreaChartOutlined />,
      CalendarOutlined: <CalendarOutlined />,
      MonitorOutlined: <MonitorOutlined />,
      LogoutOutlined: <LogoutOutlined />,
      FileSearchOutlined: <FileSearchOutlined />,
      ShoppingCartOutlined: <ShoppingCartOutlined />,
      BarcodeOutlined: <BarcodeOutlined />,
      ContainerOutlined: <ContainerOutlined />,
      ImportOutlined: <ImportOutlined />,
      BarChartOutlined: <BarChartOutlined />,
      GoogleOutlined: <GoogleOutlined />,
      EuroCircleOutlined: <EuroCircleOutlined />,
      EditOutlined: <EditOutlined />,
      CheckSquareOutlined: <CheckSquareOutlined />,
    }[iconType]);

  return getIcon(type) || <QuestionOutlined {...rest} />;
};

IconSelector.propTypes = {
  type: PropTypes.string,
};

export default IconSelector;
