/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/magento/order-comments';

/** rule GET /api/magento/order-comments */
export async function getMagOrderShipmentCommentList(
  params: API.PageParams,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderShipmentComment>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
