CREATE TABLE `sys_text_module`
(
    `number` int(11)  NOT NULL,
    `text`   longtext NOT NULL,
    PRIMARY KEY (`number`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


/*Table structure for table `email_server` */

CREATE TABLE `email_server`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `domain`        varchar(255) DEFAULT NULL,
    `imap_host`     varchar(255) DEFAULT NULL,
    `imap_port`     int(11)      DEFAULT NULL,
    `imap_port_ssl` int(11)      DEFAULT NULL,
    `imap_ssl`      int(11)      DEFAULT 0,
    `pop_host`      varchar(255) DEFAULT NULL,
    `pop_port`      int(11)      DEFAULT NULL,
    `pop_port_ssl`  int(11)      DEFAULT NULL,
    `smtp_host`     varchar(255) DEFAULT NULL,
    `smtp_port`     int(11)      DEFAULT NULL,
    `settings`      text         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

/*Table structure for table `email_account` */

CREATE TABLE `email_account`
(
    `id`         int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `email`      varchar(255)     NOT NULL,
    `pop_type`   varchar(32)         DEFAULT 'IMAP' COMMENT 'IMAP or POP',
    `server_id`  int(11)             DEFAULT NULL,
    `password`   text                DEFAULT NULL,
    `status`     tinyint(1)          DEFAULT 1,
    `settings`   text                DEFAULT NULL,
    `created_on` datetime            DEFAULT NULL,
    `created_by` bigint(20) UNSIGNED DEFAULT NULL,
    `updated_on` datetime            DEFAULT NULL,
    `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `FK_email_account_server_id` (`server_id`),
    CONSTRAINT `FK_email_account_server_id` FOREIGN KEY (`server_id`) REFERENCES `email_server` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `email`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `email_account_id` int(10) UNSIGNED    NOT NULL COMMENT 'PK in email_account table',
    `message_id`       varchar(255) DEFAULT NULL COMMENT 'UQ',
    `mail_id`          bigint(20)   DEFAULT NULL,
    `box`              varchar(255)        NOT NULL COMMENT 'mailbox name',
    `sender`           varchar(255) DEFAULT NULL,
    `sender_host`      varchar(255) DEFAULT NULL,
    `sender_name`      varchar(255) DEFAULT NULL,
    `receiver`         varchar(255) DEFAULT NULL,
    `subject`          varchar(255) DEFAULT NULL,
    `date`             datetime     DEFAULT NULL COMMENT 'received date',
    `date_str`         varchar(63)  DEFAULT NULL,
    `text_html`        longtext     DEFAULT NULL COMMENT 'email body',
    `text_plain`       longtext     DEFAULT NULL,
    `has_attachments`  tinyint(1)   DEFAULT NULL,
    `attachments_ids`  text         DEFAULT NULL COMMENT 'CSV',
    `is_seen`          tinyint(1)   DEFAULT NULL,
    `is_answered`      tinyint(1)   DEFAULT NULL,
    `is_recent`        tinyint(1)   DEFAULT NULL,
    `is_flagged`       tinyint(1)   DEFAULT NULL,
    `is_deleted`       tinyint(1)   DEFAULT NULL,
    `is_draft`         tinyint(1)   DEFAULT NULL,
    `from_host`        varchar(255) DEFAULT NULL,
    `from_name`        varchar(255) DEFAULT NULL,
    `to`               text         DEFAULT NULL COMMENT 'CSV',
    `reply_to`         text         DEFAULT NULL COMMENT 'CSV',
    `cc`               text         DEFAULT NULL COMMENT 'CSV',
    `bcc`              text         DEFAULT NULL COMMENT 'CSV',
    `created_on`       datetime     DEFAULT NULL,
    `order_id`         int(11)      DEFAULT NULL COMMENT 'Order ID',
    `tracking_no`      varchar(255) DEFAULT NULL,
    `ext_order`        varchar(255) DEFAULT NULL COMMENT 'ext order',
    `ext_order_id`     varchar(255) DEFAULT NULL COMMENT 'Ext. Order ID',
    `status`           varchar(255) DEFAULT NULL COMMENT 'Status set by user',
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_email_message_id_mail_id` (`email_account_id`, `message_id`, `mail_id`),
    KEY `IDX_email_subject` (`subject`),
    KEY `IDX_email_sender` (`sender`),
    KEY `IDX_email_receiver` (`receiver`),
    KEY `FK_email_email_acount_id` (`email_account_id`),
    CONSTRAINT `FK_email_email_acount_id` FOREIGN KEY (`email_account_id`) REFERENCES `email_account` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


ALTER TABLE `email_server`
    ADD COLUMN `smtp_user`     VARCHAR(255) NULL COMMENT 'SMTP user' AFTER `smtp_port`,
    ADD COLUMN `smtp_password` TEXT         NULL COMMENT 'SMTP user\'s password' AFTER `smtp_user`;
