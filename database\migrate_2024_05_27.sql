ALTER TABLE `warehouse_picklist`
    add column `status_pre_picking` tinyint NOT NULL default 0 COMMENT 'Pre Picking Status: 0: Open, 1: Processing, 2: Done' after `label_files`;

ALTER TABLE `warehouse_picklist_time_track`
    CHANGE `type` `type` SMALLINT(4) NOT NULL COMMENT 'Type. e.g. Prepick:0, Pick: 1, Pack: 2';


ALTER TABLE `warehouse_picklist`
    ADD COLUMN `final_pdf_id2` BIGINT UNSIGNED NULL COMMENT 'Final PDF file. Created when full booking (Summary 2 grouped by order)' AFTER `final_pdf_id`,
    ADD INDEX `FK_warehouse_picklist_final_pdf_id2` (`final_pdf_id2`);


ALTER TABLE `warehouse_picklist`
    ADD COLUMN `pre_pdf_id` BIGINT UNSIGNED NULL COMMENT 'Pre picklist PDF file.' AFTER `order_shipment_created_on`,
    ADD INDEX `FK_warehouse_picklist_pre_pdf_id` (`pre_pdf_id`);


