import { IboPreManagementStatus, OfferIboStatus, OfferIboStatusKv } from '@/constants';
import { getOfferMatrixExt } from '@/services/foodstore-one/Offer/offer-matrix';
import Util, { ni, sEllipsed, sn } from '@/util';
import { EditOutlined, LinkOutlined, ReloadOutlined } from '@ant-design/icons';
import ProForm, { ProFormInstance, ProFormRadio, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { Card, Col, message, Popover, Row } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { IRouteComponentProps } from 'umi';
import {
  IboPreManagementListInCell,
  IboPreManagementListInCellWithCount,
  IboPreTitle,
  OfferIboStatusComp,
} from './OfferList';
import EditableCell from '@/components/EditableCell';
import { updateOffer } from '@/services/foodstore-one/Offer/offer';
import { getIboPreManagementList } from '@/services/foodstore-one/IBO/ibo-pre-management';
import UpdateGfcNoteFormModal from './OfferList/components/UpdateGfcNoteFormModal';

type SearchFormValueType = any;
type RecordType = API.Offer & Record<string, any>;

type OfferMatrixProps = IRouteComponentProps;

const OfferMatrix: React.FC<OfferMatrixProps> = ({}) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingOpenIboPreM, setLoadingOpenIboPreM] = useState<boolean>(false);
  const [mode, setMode] = useState<'A' | 'B' | 'C' | 'D'>('A');
  const [currentRow, setCurrentRow] = useState<API.Offer>();
  const [openGfcNoteEditModal, setOpenGfcNoteEditModal] = useState<boolean>(false);

  const [iboPreManagementList, setIboPreManagementList] = useState<API.IboPreManagement[]>([]);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  /**
   * Setting search form values by last saved values
   */
  useEffect(() => {
    const savedSfValues = Util.getSfValues('sf_offer_matrix_ext', {}, {});
    if (!savedSfValues.mode) savedSfValues.mode = 'A';
    setMode(savedSfValues.mode);
    searchFormRef.current?.setFieldsValue(savedSfValues);
  }, []);

  /* const loadIboPreManagementList = useCallback(() => {
    setLoadingOpenIboPreM(true);
    getIboPreManagementList({ nin_statuses: [IboPreManagementStatus.Done], pageSize: 100 }, {}, {})
      .then((res) => {
        setIboPreManagementList(res.data);
      })
      .catch(Util.error)
      .finally(() => {
        setLoadingOpenIboPreM(false);
      });
  }, []);

  useEffect(() => {
    if (mode == 'C' || mode == 'D') {
      loadIboPreManagementList();
    }
  }, [loadIboPreManagementList, mode]); */

  useEffect(() => {
    const colsStatus: ProColumns<RecordType>[] = [
      {
        title: 'Status',
        dataIndex: 'ibo_status',
        hideInForm: true,
        tooltip: 'Click to Edit',
        align: 'center',
        width: 110,
        render(__, record) {
          return (
            <EditableCell
              dataType="select"
              defaultValue={record.ibo_status || ''}
              style={{ marginRight: 0 }}
              fieldProps={{ style: { lineHeight: 1 } }}
              valueEnum={OfferIboStatusKv}
              triggerUpdate={async (newValue: any, cancelEdit) => {
                if (!newValue && !record.id) {
                  cancelEdit?.();
                  return;
                }
                return updateOffer(sn(record.id), {
                  ibo_status: newValue,
                })
                  .then((res) => {
                    message.destroy();
                    message.success('Updated successfully.');
                    actionRef.current?.reload();
                    cancelEdit?.();
                  })
                  .catch(Util.error);
              }}
            >
              <OfferIboStatusComp status={record.ibo_status} />
            </EditableCell>
          );
        },
      },
      {
        title: 'GFC Notes',
        dataIndex: 'gfc_note',
        hideInForm: true,
        tooltip: 'Click to Edit',
        width: mode == 'B' ? 750 : mode == 'A' ? 450 : 300,
        render(__, record) {
          return (
            <Row wrap={false}>
              <Col flex="auto">{sEllipsed(record.gfc_note, 300)}</Col>
              <Col flex="16px">
                <EditOutlined
                  className="cursor-pointer"
                  onClick={() => {
                    setCurrentRow({ id: record.id, gfc_note: record.gfc_note });
                    setOpenGfcNoteEditModal(true);
                  }}
                />
              </Col>
            </Row>
          );
        },
      },
    ];

    // build columns
    const cols: ProColumns<RecordType>[] = [
      {
        title: 'Offer No',
        dataIndex: 'offer_no',
        sorter: true,
        hideInForm: true,
        defaultSortOrder: 'descend',
        width: 100,
        fixed: 'left',
        render(__, record) {
          const customerId = record.quote?.customer?.id;
          return (
            <Row>
              <Col flex="auto">
                <a href={`/quotes/offer-item?offer_id=${record.id}&offer_no=${record.offer_no}`} target="_blank">
                  {record.offer_no}
                </a>
              </Col>
              <Col flex="20px">
                {!!customerId && (
                  <a
                    href={`${GFC_CUST_PIM_URL}/customers/detail/${customerId}`}
                    target="_blank"
                    title="Open GFC Cust page in new tab."
                  >
                    <LinkOutlined />
                  </a>
                )}
              </Col>
            </Row>
          );
        },
      },
      {
        title: 'Notes',
        dataIndex: 'note',
        ellipsis: true,
        width: 200,
        render(__, entity) {
          return `${entity.note ?? ''}${
            entity.quote?.customer_fullname ? ` - ${entity.quote?.customer_fullname}` : ''
          }`;
        },
      },
      ...(mode != 'A' ? colsStatus : []),
      ...(mode == 'C' || mode == 'D'
        ? [
            {
              title: 'Mapped Info',
              dataIndex: 'mapped_offer_item_cnt',
              tooltip: 'x / y: x -> mapped IBO Pre Count / Offer Item Count',
              width: 80,
              render: (__: any, record: API.Offer) => {
                const num1 = sn(record.mapped_offer_item_cnt);
                const num2 = sn(record.offer_items_count_valid);

                return num2 ? (
                  <span
                    className={num1 < num2 ? 'c-red' : 'c-grey'}
                    title={`Valid: ${ni(record.offer_items_count_valid)} of ${ni(record.offer_items_count)}`}
                  >
                    {`${ni(num1, true)} / ${ni(num2, true)}`}
                  </span>
                ) : null;
              },
            },
          ]
        : []),
    ];

    if (mode == 'A' || mode == 'B') {
      for (const status in OfferIboStatusKv) {
        if (mode == 'B') {
          if (status != OfferIboStatus.InDiscussion) continue;
        } else if (mode == 'A') {
          if (status == OfferIboStatus.InDiscussion) continue;
        }

        if (mode == 'A' || mode == 'B') {
          if (status == OfferIboStatus.Closed || status == OfferIboStatus.ClosedLost) continue;
        }

        cols.push({
          title: (
            <div className="rotate-rl" style={{ paddingRight: 40, textAlign: 'center' }}>
              {OfferIboStatusKv[status]}
            </div>
          ),
          dataIndex: [status],
          width: 100,
          align: mode == 'A' || mode == 'B' ? 'center' : 'left',
          render(__, entity) {
            if (entity.ibo_status == status) {
              const num1 = sn(entity.mapped_offer_item_cnt);
              const num2 = sn(entity.offer_items_count_valid);
              if (num2) {
                return entity.ibo_pre_managements?.length ? (
                  <Popover
                    title={`Offer #${entity.offer_no}`}
                    content={<IboPreManagementListInCell ibo_pre_managements={entity.ibo_pre_managements ?? []} />}
                    trigger={['click', 'hover']}
                  >
                    <span
                      className={num1 < num2 ? 'c-red' : 'c-grey'}
                      title={`Valid: ${ni(entity.offer_items_count_valid)} of ${ni(entity.offer_items_count)}`}
                    >
                      {`${ni(num1, true)} / ${ni(num2, true)}`}
                    </span>
                  </Popover>
                ) : (
                  <span className={num1 < num2 ? 'c-red' : 'c-grey'}>{`${ni(num1, true)} / ${ni(num2, true)}`}</span>
                );
              }
            }

            return null;
          },
        });
      }
      if (mode == 'A') {
        colsStatus.forEach((x) => {
          cols.push(x);
        });
      }
    } else {
      for (const x of iboPreManagementList) {
        cols.push({
          title: (
            <div className="rotate-rl" style={{ paddingRight: 2, textAlign: 'center' }}>
              <IboPreTitle iboPreManagement={x} />
            </div>
          ),
          dataIndex: [`${x.id}`],
          width: 50,
          align: 'center',
          render(__, entity) {
            return (
              <div style={{ width: 50 }}>
                <IboPreManagementListInCellWithCount
                  offer_id={entity.id}
                  mapped_detail={entity.mapped_offer_detail}
                  ibo_pre_managements={
                    entity.ibo_pre_managements?.filter((x2) => {
                      if (x2.id == x.id) {
                        return true;
                      } else {
                        return false;
                      }
                    }) ?? []
                  }
                />
              </div>
            );
          },
        });
      }
    }

    if (mode == 'A' || mode == 'B') {
      cols.push({ valueType: 'option' });
    }
    setColumns(cols);
  }, [mode, iboPreManagementList]);

  useEffect(() => {
    actionRef.current?.reload();
  }, [mode]);

  return (
    <PageContainer title="Offer Matrix">
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormRadio.Group
            label="Mode"
            name="mode"
            options={['A', 'B', 'C', 'D']}
            radioType="button"
            fieldProps={{
              value: mode,
              buttonStyle: 'solid',
              onChange: (e) => {
                setMode(e.target.value);
              },
            }}
          />

          {/* <ProFormSwitch
            name="inc_closed"
            label="Show Closed Offer?"
            fieldProps={{
              onChange() {
                searchFormRef.current?.submit();
              },
            }}
          /> */}
          <ProFormText name="offer_no" label="Offer No" />
        </ProForm>

        <ProTable<RecordType, API.PageParams>
          headerTitle={
            <>
              Offer Matrix&nbsp;&nbsp;&nbsp;
              <ReloadOutlined
                className="text-sm"
                onClick={() => {
                  if (mode == 'C' || mode == 'D') {
                    // loadIboPreManagementList();
                  }
                  actionRef.current?.reload();
                }}
              />
            </>
          }
          sticky
          actionRef={actionRef}
          rowKey="id"
          options={false}
          size="small"
          loading={loading}
          bordered
          columnEmptyText=""
          scroll={{ x: 1200 }}
          cardProps={{ bodyStyle: { padding: 0 } }}
          request={async (p, sort, filter) => {
            const params = {
              ...p,
              ...sort,
              ...filter,
              ...searchFormRef.current?.getFieldsValue(),
            };
            Util.setSfValues('sf_offer_matrix_ext', params);

            setLoading(true);
            return getOfferMatrixExt(params)
              .then((res) => {
                setIboPreManagementList(res.ibo_pre_managements || []);
                return res;
              })
              .finally(() => {
                setLoading(false);
              });
          }}
          onRequestError={Util.error}
          pagination={{
            showSizeChanger: true,
            hideOnSinglePage: true,
            defaultPageSize: 20,
          }}
          search={false}
          columns={columns}
          tableAlertRender={false}
        />
      </Card>

      <UpdateGfcNoteFormModal
        modalVisible={openGfcNoteEditModal}
        handleModalVisible={setOpenGfcNoteEditModal}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          setOpenGfcNoteEditModal(false);
        }}
      />
    </PageContainer>
  );
};

export default OfferMatrix;
