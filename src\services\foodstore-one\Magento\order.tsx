/* eslint-disable */
import { MagentoOrderStatusOptions } from '@/constants';
import Util from '@/util';
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/magento-data/orders';

/** rule GET /api/magento-data/orders */
export async function getOrdersList(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.Order>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

/** rule GET /api/magento-data/orders/statusACList */
export async function getOrderStatusACList(
  params?: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<(DefaultOptionType & { cnt?: number })[]> {
  return request<API.BaseResult>(`${urlPrefix}/statusACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => {
    const ret = Object.keys(MagentoOrderStatusOptions).map((x) => ({
      value: x,
      label: (
        <div style={{ display: 'flex' }}>
          <div style={{ flex: 'auto' }}>{MagentoOrderStatusOptions[x]}</div>
          {res.message[x] && (
            <div style={{ flex: '0 0 65px', textAlign: 'right', paddingRight: 10 }}>
              ({Util.numberFormat(res.message[x])})
            </div>
          )}
        </div>
      ),
      cnt: res.message[x],
    }));
    return ret;
  });
}

export async function exportOrdersList(
  params: API.PageParamsExt & { entity_ids?: number[] },
  sort?: any,
  filter?: any,
) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export`, {
    method: 'GET',
    params: {
      ...params,
      sort,
      ...filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/**
 * Update order address (shipping or invoice) into system and Magento
 *
 * PUT /api/magento-data/orders/usOrderAddressUpdate/{entity_id}
 */
export async function usOrderAddressUpdate(
  entity_id: number,
  address: API.OrderAddress,
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/usOrderAddressUpdate/${entity_id}`, {
    method: 'PUT',
    data: address,
    ...(options || {}),
  });
}
/**
 * Update order addresses (shipping and invoice) into system and Magento
 *
 * PUT /api/magento-data/orders/usOrderAddressesUpdate/{entity_id}
 */
export async function usOrderAddressesUpdate(
  entity_id: number,
  addresses: API.OrderAddress[],
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/usOrderAddressesUpdate/${entity_id}`, {
    method: 'PUT',
    data: { addresses },
    ...(options || {}),
  });
}

/**
 * Create order shipment
 *
 * POST /api/magento/orders/${entity_id}/createOrderShipment
 */
export async function createOrderShipment(
  entity_id: number,
  shipment: Partial<API.WarehousePicklistShippingImported>,
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`/api/magento/orders/${entity_id}/createOrderShipment`, {
    method: 'POST',
    data: { shipment },
    ...(options || {}),
  });
}

/**
 * Update order extra info
 *
 * PUT /api/magento-data/orders/extra/{entity_id}
 */
export async function updateOrderExtra(entity_id: number, data: API.OrderExtra, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Order>>(`${urlPrefix}/extra/${entity_id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/**
 * Get warehousePicklist shipping imported list by order ID.
 *
 * GET /api/magento-data/orders/{id}/shipping-imported-list */
export async function getWarehousePicklistShippingImportedListById(orderId: number) {
  return request<API.ResultObject<API.WarehousePicklistShippingImported[]>>(
    `${urlPrefix}/${orderId}/shipping-imported-list`,
    {
      method: 'GET',
      withToken: true,
      paramsSerializer,
    },
  ).then((res) => res.message);
}

/**
 * Print PDF Label
 *
 * GET /api/magento-data/orders/{id}/export-label */
export async function exportShippingLabels(id?: number, params?: Record<string, any>) {
  return request<API.ResultObject<{ files?: API.Downloadable[]; labels?: API.OrderLabel[] }>>(
    `${urlPrefix}/${id}/export-label`,
    {
      method: 'GET',
      params,
    },
  ).then((res) => res.message);
}

/**
 * get stats of orders with address & stock problems
 *
 * GET /api/magento-data/orders/problematicStats */
export async function getProblematicOrderStats(params?: Record<string, any>) {
  return request<
    API.ResultObject<{ orderCountWithErrors: number; missingItemsQty: number; orderCountToBeOpened: number }>
  >(`${urlPrefix}/problematicStats`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/**
 * Create a manual shipping label
 *
 * POST /api/magento/createShippingLabel
 */
export async function createShippingLabel(data: any) {
  return request<API.ResultObject<{ files?: API.Downloadable[]; labels?: API.OrderLabel[] }>>(
    `/api/magento/createShippingLabel`,
    {
      method: 'POST',
      data,
    },
  ).then((res) => res.message);
}
