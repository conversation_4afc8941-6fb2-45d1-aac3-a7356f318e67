import { useEffect } from 'react';
import React, { useRef, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { getStockMovementList } from '@/services/foodstore-one/Stock/stock-movement';
import { StockMovementReasonOptions } from '@/constants';
import Util, { ni } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import StockMovementRefComp from '@/pages/Stock/StockMovement/StockMovementRefComp';

type SearchFormValueType = Partial<API.StockMovement>;
type StockMovementListSearchParamsType = {
  sku?: string;
  ibo_id?: number;
  is_single?: boolean;
};

type StockMovementListProps = {
  searchParams?: StockMovementListSearchParamsType;
};

const StockMovementList: React.FC<StockMovementListProps> = (props) => {
  const { searchParams } = props;
  const { sku, ibo_id } = searchParams || {};

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.StockMovement>[] = [
    {
      title: 'Ref',
      dataIndex: ['ref_type'],
      sorter: true,
      width: 50,
      fixed: 'left',
      render: (__, record) => <StockMovementRefComp record={record} />,
    },
    /* {
      title: 'Item',
      align: 'center',
      dataIndex: ['item', 'name'],
      hideInSearch: true,
      formItemProps: { tooltip: 'Search by item name.' },
      children: [
        {
          title: 'Name',
          dataIndex: ['item', 'name'],
          sorter: true,
          ellipsis: true,
          hideInSearch: false,
          width: 180,
        },
        {
          title: 'Trademark',
          dataIndex: ['item', 'trademark', 'name'],
          sorter: false,
          width: 100,
          ellipsis: true,
          hideInSearch: true,
          render: (dom, record) =>
            record?.item?.trademark?.name ? `${record?.item?.trademark?.name}` : '',
        },
      ],
    },
    {
      title: 'EAN',
      dataIndex: 'ean',
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
    },
    {
      title: 'Single EAN',
      dataIndex: 'parent_ean',
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      width: 100,
    }, */
    {
      title: 'Old WL',
      dataIndex: ['old_wl_name'],
      sorter: false,
      width: 80,
    },
    {
      title: 'New WL',
      dataIndex: ['new_wl_name'],
      width: 80,
      sorter: false,
      render(dom, entity) {
        return (
          <a href={`/stock/stock-warehouse?sku=${entity.item_id}_&wl_id=${entity.new_wl_id}`} target="_blank">
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Reason',
      dataIndex: ['reason'],
      width: 130,
      sorter: false,
    },
    {
      title: 'Reason Detail',
      dataIndex: ['reason_text'],
      width: 120,
      sorter: false,
    },
    {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.box_qty),
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.case_qty),
    },
    {
      title: 'Total Piece Qty',
      dataIndex: ['total_piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      render: (dom, record) => ni(record.total_piece_qty),
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      sorter: false,
      width: 100,
      align: 'center',
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    /* {
      title: 'Ref',
      dataIndex: ['ref_type'],
      sorter: true,
      width: 50,
      fixed: 'right',
      render: (__, record) => <StockMovementRefComp record={record} />,
    }, */
    /* {
      title: 'Process Code',
      dataIndex: ['batch_code'],
      sorter: true,
      width: 100,
      align: 'center',
      className: 'text-xs c-lightgrey',
    }, */
    {
      title: 'Owner',
      dataIndex: ['owner'],
      sorter: false,
      width: 80,
      align: 'center',
    },
    {
      title: 'Created on',
      dataIndex: ['created_on'],
      sorter: true,
      defaultSortOrder: 'descend',
      width: 120,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },

    /* {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    }, */
  ];

  useEffect(() => {
    if (sku && ibo_id) {
      console.log('   --> effect', 'SKU:', sku, 'iboID:', ibo_id);
      actionRef?.current?.reload();
    }
  }, [sku, ibo_id]);

  console.log(sku, 'iboID', ibo_id);

  return (
    <ProTable<API.StockMovement, API.PageParams>
      actionRef={actionRef}
      rowKey="id"
      revalidateOnFocus={false}
      options={{ fullScreen: false, reload: true, density: false, search: false, setting: false }}
      search={false}
      sticky
      size="small"
      scroll={{ x: 800 }}
      cardProps={{ bodyStyle: { padding: 0 }, headStyle: { padding: 0 } }}
      rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
      request={async (params, sort, filter) => {
        const searchFormValues = searchFormRef.current?.getFieldsValue();
        setLoading(true);
        console.log('==>Form', searchFormValues, 'searchParams', searchParams);
        return getStockMovementList(
          {
            ...params,
            ...searchFormValues,
            ...searchParams,
            with: '',
          },
          sort,
          filter,
        ).finally(() => setLoading(false));
      }}
      onRequestError={Util.error}
      columns={columns}
      rowSelection={false}
      columnEmptyText=""
      toolBarRender={(action, rows) => {
        return [
          <ProForm<SearchFormValueType>
            layout="inline"
            formRef={searchFormRef}
            isKeyPressSubmit
            className="search-form"
            submitter={false}
            key="form"
            size="small"
          >
            <ProFormSelect
              name={'reason'}
              label="Reason"
              width={'sm'}
              allowClear
              options={StockMovementReasonOptions as any}
              placeholder={'Reason'}
              fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
            />
            {/* <ProFormSelect
                name={'refFilterMode'}
                label="Filter Mode"
                width={'sm'}
                allowClear
                options={[
                  { value: 'onlyOrderItem', label: 'Only Order Item' },
                  { value: 'notOnlyOrderItem', label: 'Not Order Item' },
                ]}
                placeholder={'Filter Mode'}
                fieldProps={{
                  onChange(value, option) {
                    actionRef.current?.reload();
                  },
                }}
              /> */}
          </ProForm>,
        ];
      }}
      locale={{ emptyText: <></> }}
    />
  );
};

export default StockMovementList;
