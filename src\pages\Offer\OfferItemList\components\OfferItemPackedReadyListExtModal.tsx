import Util, { ni, sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Col, Modal, Row, Typography } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef } from 'react';
import { ProFormInstance } from '@ant-design/pro-form';
import { getOfferItemIboPrePackReadyMapList } from '@/services/foodstore-one/Offer/offer-item-packed-ready';
import SkuComp from '@/components/SkuComp';
import LastImportEanDisabledList from './LastImportEanDisabledList';
import ItemStockStableList from './ItemStockStableList';

export type SearchFormValueType = Partial<API.OfferItemIboPrePackReadyMap>;

type OfferItemPackedReadyListExtModalProps = {
  offerItem?: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const OfferItemPackedReadyListExtModal: React.FC<OfferItemPackedReadyListExtModalProps> = (props) => {
  const { offerItem, modalVisible, handleModalVisible } = props;

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  useEffect(() => {
    if (modalVisible && offerItem?.offer_id) {
      actionRef.current?.reload();
    }
  }, [modalVisible, offerItem?.offer_id]);

  const columns: ProColumns<API.OfferItemIboPrePackReadyMap>[] = [
    {
      title: 'Offer No',
      dataIndex: ['offer_item', 'offer', 'offer_no'],
      width: 160,
      render(dom, entity) {
        return (
          <span>
            {dom} - {`Pre IBO #${entity.ibo_pre?.id}`}
          </span>
        );
      },
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 65,
      align: 'right',
      className: 'bl2',
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },

    {
      title: 'Qty (pcs)',
      dataIndex: ['qty_pcs'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record?.case_qty) * sn(record?.qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Created on',
      dataIndex: 'updated_on',
      valueType: 'date',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (__, record) => Util.dtToDMYHHMM(record.updated_on),
    },
  ];

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={1000}
      maskClosable={false}
      footer={false}
      title={
        <Row gutter={32}>
          <Col>Latest Info - Offer #{offerItem?.offer?.offer_no}</Col>
          <Col>
            <SkuComp sku={offerItem?.item_ean?.sku} />
          </Col>
          <Col>
            <Typography.Text copyable>{offerItem?.item_ean?.ean}</Typography.Text>
          </Col>
          <Col>Offer Qty: {ni(sn(offerItem?.case_qty) * sn(offerItem?.qty), true)} pcs</Col>
        </Row>
      }
    >
      <ItemStockStableList item_id={offerItem?.item_ean?.item_id} />

      <ProTable<API.OfferItemIboPrePackReadyMap, API.PageParams>
        headerTitle={<span>Last 10 Qty Packed Ready List</span>}
        actionRef={actionRef}
        rowKey={(entity) => `${entity.offer_item}_${entity.ibo_pre_id}_${entity.ean_id}_${entity.exp_date}`}
        revalidateOnFocus={false}
        options={{ fullScreen: false, reload: true, density: false, search: false, setting: false }}
        size="small"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        bordered={true}
        pagination={false}
        request={(params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            ean_id: offerItem?.item_ean?.id,
            sort: { id: 'descend' },
            with: 'iboPre,itemEan',
            pageSize: 10,
          };

          return getOfferItemIboPrePackReadyMapList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record?.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
      <LastImportEanDisabledList searchParams={{ ean: offerItem?.item_ean?.ean }} hideCreatable={true} perPage={10} />
    </Modal>
  );
};

export default OfferItemPackedReadyListExtModal;
