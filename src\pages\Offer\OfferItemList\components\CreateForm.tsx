import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Typography, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormItem, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { addOfferItem } from '@/services/foodstore-one/Offer/offer-item';
import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';
import { debounce } from 'lodash';
import { getEanDetail } from '@/services/foodstore-one/Item/ean';

export type FormValueType = Partial<API.OfferItem>;

const handleCreate = async (fields: FormValueType) => {
  const hide = message.loading('Creating...', 0);

  try {
    await addOfferItem(fields);
    hide();
    message.success('Created successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type CreateFormProps = {
  modalVisible: boolean;
  offer_id: number;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OfferItem) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { offer_id, modalVisible, handleModalVisible, onSubmit } = props;
  const formRef = useRef<ProFormInstance>();

  const [itemEan, setItemEan] = useState<API.Ean | null>(null);

  // ---------------------------------------------------------------------------------
  // Search EAN
  // ---------------------------------------------------------------------------------
  const handleSearchEan = async (v: string, cb: any) => {
    if (!v) {
      cb(null);
      return;
    }
    message.destroy();
    const hide = message.loading('Searching EAN...', 0);
    await getEanDetail({ skuOrEan: v })
      .then((res) => {
        cb(res);
      })
      .catch(() => {
        cb(null);
      })
      .finally(() => hide());
  };

  const debouncedHandleSearchEan = useCallback(
    debounce((newValue, cb) => handleSearchEan(newValue, cb), 330),
    [],
  );

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
      setItemEan(null);
    }
  }, [modalVisible]);

  return (
    <ModalForm
      title={<div>Create New Offer Item</div>}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 5 }}
      formRef={formRef}
      onFinish={async (value) => {
        if (!offer_id) {
          message.error('Please select an Offer first!');
          return;
        }

        if (!itemEan) {
          message.error('Please search correct EAN!');
          return;
        }

        const data = {
          ...value,
          offer_id,
          sku: itemEan.sku,
          ean: itemEan.ean,
          ean_id: itemEan.id,
          item_id: itemEan.item_id,
          case_qty: itemEan.attr_case_qty,
        };
        const success = await handleCreate(data);

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProFormText
        formItemProps={{ style: { marginBottom: 0 } }}
        label="SKU or EAN"
        fieldProps={{
          placeholder: 'SKU or EAN',
          onChange: (e: any) => {
            const value = e.target.value;

            debouncedHandleSearchEan(value, (eanData: API.Ean) => {
              if (eanData) {
                console.log('[offerItem][search][eanData]', eanData);
                setItemEan(eanData);
              } else {
                setItemEan(null);
              }
            });
          },
          onBlur: () => {},
        }}
      />

      <ProFormItem label=" " style={{ marginBottom: 0 }} colon={false}>
        <Typography.Text style={{ display: 'inline-block', fontSize: 14 }} copyable={{ text: itemEan?.sku || '' }}>
          {itemEan?.sku} {itemEan?.is_single ? '' : `(Qty.Pkg: ${itemEan?.attr_case_qty ?? '-'})`}
        </Typography.Text>
      </ProFormItem>

      <ProFormItem label="Name" style={{ marginBottom: 0 }}>
        <Typography.Text style={{ display: 'inline-block', fontSize: 14 }} copyable>
          {itemEan?.ean_texts?.[0]?.name}
        </Typography.Text>
      </ProFormItem>

      <SProFormDigit width="sm" name="qty" label="Qty" />
      {/* <SProFormDigit width="sm" name="price" label="Price" fieldProps={{ precision: 3 }} /> */}
      <ProFormTextArea width="lg" name="customer_note" label="Customer Note" />
    </ModalForm>
  );
};

export default CreateForm;
