import Util from '@/util';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { Button } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { getImportEanDisabledList } from '@/services/foodstore-one/Import/import-ean-disabled';
import CreateForm from '@/pages/Import/ImportEanDisabled/components/CreateForm';
import UpdateForm from '@/pages/Import/ImportEanDisabled/components/UpdateForm';

type RecordType = API.ImportEanDisabled;

export type SearchFormValueType = Partial<API.ImportEanDisabled>;

type LastImportEanDisabledListProps = {
  searchParams?: { ean?: string; supplier_add?: string };
  hideCreatable?: boolean;
  perPage?: number;
};

const LastImportEanDisabledList: React.FC<LastImportEanDisabledListProps> = (props) => {
  const { searchParams, hideCreatable, perPage } = props;

  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<RecordType>();
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'SUPPLIER_ADD',
      dataIndex: ['supplier_add'],
      width: 130,
    },
    {
      title: 'From Date',
      dataIndex: ['start_date'],
      sorter: true,
      width: 90,
      ellipsis: true,
      showSorterTooltip: false,
      align: 'center',
      defaultSortOrder: 'descend',
      render: (dom, record) => (record.start_date == '1900-01-01' ? null : Util.dtToDMY(record.start_date)),
    },
    {
      title: 'To Date',
      dataIndex: ['end_date'],
      sorter: true,
      width: 90,
      ellipsis: true,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => (record.end_date == '2099-12-31' ? null : Util.dtToDMY(record.end_date)),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({
              ...record,
            });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [searchParams?.ean]);

  return (
    <>
      <ProTable<RecordType, API.PageParams>
        headerTitle={`Last ${perPage ?? 10} Disabled EAN Prices`}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: false, reload: true, density: false, search: false, setting: false }}
        search={false}
        sticky
        bordered
        cardProps={{
          headStyle: { padding: 0 },
          bodyStyle: { padding: 0 },
        }}
        request={async (params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const newParams = {
            ...params,
            perPage: perPage ?? 10,
            ean: searchParams?.ean,
          };

          return getImportEanDisabledList(newParams, { ...newSort }, filter);
        }}
        columns={columns}
        toolBarRender={() =>
          hideCreatable
            ? []
            : [
                <Button
                  type="primary"
                  key="new"
                  onClick={() => {
                    handleCreateModalVisible(true);
                  }}
                >
                  <PlusOutlined /> New
                </Button>,
              ]
        }
        tableAlertRender={false}
        rowSelection={false}
        pagination={false}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />

      <CreateForm
        initialValues={{ ean: searchParams?.ean, supplier_add: searchParams?.supplier_add }}
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={async (value) => {
          handleCreateModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </>
  );
};
export default LastImportEanDisabledList;
