import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanTask } from '@/services/foodstore-one/Item/ean-task';
import Util from '@/util';
import { DictType, TaskStatusOptions } from '@/constants';
import { useModel } from 'umi';

export type FormValueType = Partial<API.EanTask>;

const handleUpdate = async (id?: number, fields?: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEanTask(id, fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateFormProps = {
  initialValues?: Partial<API.EanTask>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EanTask) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { getDictOptionsCV } = useModel('app-settings');
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  return (
    <ModalForm
      title={'Update EAN Task'}
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 5 }}
      labelAlign="left"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate(props.initialValues?.id, value);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        name="category_code"
        label="Category"
        width="sm"
        options={getDictOptionsCV(DictType.EanTaskCategory)}
      />
      <ProFormTextArea
        rules={[
          {
            required: true,
            message: 'Task is required',
          },
        ]}
        name="task"
        label="Task"
      />
      <ProFormSelect name="status" label="Status" width="sm" options={TaskStatusOptions} />
    </ModalForm>
  );
};

export default UpdateForm;
