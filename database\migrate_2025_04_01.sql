drop table if exists xmag_customer;

CREATE TABLE `xmag_customer`
(
    `id`               int(10) unsigned     NOT NULL AUTO_INCREMENT COMMENT 'PK: Customer ID',
    `website_id`       int(11)                       DEFAULT NULL COMMENT 'Website ID',
    `email`            varchar(255)                  DEFAULT NULL COMMENT 'Email',
    `group_id`         smallint(5) unsigned NOT NULL DEFAULT 0 COMMENT 'Group ID',
    `store_id`         int(11)                       DEFAULT 0 COMMENT 'Store ID',
    `created_at`       timestamp            NOT NULL DEFAULT current_timestamp() COMMENT 'Created At',
    `updated_at`       timestamp            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Updated At',
    `is_active`        smallint(5) unsigned NOT NULL DEFAULT 1 COMMENT 'Is Active',
    `created_in`       varchar(255)                  DEFAULT NULL COMMENT 'Created From',
    `prefix`           varchar(40)                   DEFAULT NULL COMMENT 'Name Prefix',
    `firstname`        varchar(255)                  DEFAULT NULL COMMENT 'First Name',
    `middlename`       varchar(255)                  DEFAULT NULL COMMENT 'Middle Name/Initial',
    `lastname`         varchar(255)                  DEFAULT NULL COMMENT 'Last Name',
    `suffix`           varchar(40)                   DEFAULT NULL COMMENT 'Name Suffix',
    `dob`              date                          DEFAULT NULL COMMENT 'Date of Birth',
    `default_billing`  int(10) unsigned              DEFAULT NULL COMMENT 'Default Billing Address',
    `default_shipping` int(10) unsigned              DEFAULT NULL COMMENT 'Default Shipping Address',
    `taxvat`           varchar(50)                   DEFAULT NULL COMMENT 'Tax/VAT Number',
    `confirmation`     varchar(64)                   DEFAULT NULL COMMENT 'Is Confirmed',
    `gender`           smallint(5) unsigned          DEFAULT NULL COMMENT 'Gender',
    PRIMARY KEY (`id`),
    UNIQUE KEY `XMAG_CUSTOMER_EMAIL_WEBSITE_ID` (`email`, `website_id`),
    KEY `XMAG_CUSTOMER_STORE_ID` (`store_id`),
    KEY `XMAG_CUSTOMER_WEBSITE_ID` (`website_id`),
    KEY `XMAG_CUSTOMER_FIRSTNAME` (`firstname`),
    KEY `XMAG_CUSTOMER_LASTNAME` (`lastname`),
    CONSTRAINT `XMAG_CUSTOMER_STORE_ID_STORE_STORE_ID` FOREIGN KEY (`store_id`) REFERENCES `xmag_store_config` (`id`) ON DELETE SET NULL,
    CONSTRAINT `XMAG_CUSTOMER_WEBSITE_ID_STORE_WEBSITE_WEBSITE_ID` FOREIGN KEY (`website_id`) REFERENCES `xmag_store_website` (`id`) ON DELETE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='Customer';


drop table if exists xmag_customer_address;
CREATE TABLE `xmag_customer_address`
(
    `id`           int(10) unsigned     NOT NULL AUTO_INCREMENT COMMENT 'PK: Address ID',
    `customer_id`  int(10) unsigned              DEFAULT NULL COMMENT 'Customer ID',
    `created_at`   timestamp            NOT NULL DEFAULT current_timestamp() COMMENT 'Created At',
    `updated_at`   timestamp            NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Updated At',
    `is_active`    smallint(5) unsigned NOT NULL DEFAULT 1 COMMENT 'Is Active',
    `city`         varchar(255)         NOT NULL COMMENT 'City',
    `company`      varchar(255)                  DEFAULT NULL COMMENT 'Company',
    `country_id`   varchar(255)         NOT NULL COMMENT 'Country',
    `fax`          varchar(255)                  DEFAULT NULL COMMENT 'Fax',
    `firstname`    varchar(255)         NOT NULL COMMENT 'First Name',
    `lastname`     varchar(255)         NOT NULL COMMENT 'Last Name',
    `middlename`   varchar(255)                  DEFAULT NULL COMMENT 'Middle Name',
    `postcode`     varchar(255)                  DEFAULT NULL COMMENT 'Zip/Postal Code',
    `prefix`       varchar(40)                   DEFAULT NULL COMMENT 'Name Prefix',
    `region`       varchar(255)                  DEFAULT NULL COMMENT 'State/Province',
    `region_id`    int(10) unsigned              DEFAULT NULL COMMENT 'State/Province',
    `street`       text                 NOT NULL COMMENT 'Street Address',
    `suffix`       varchar(40)                   DEFAULT NULL COMMENT 'Name Suffix',
    `telephone`    varchar(255)         NOT NULL COMMENT 'Phone Number',
    `vat_id`       varchar(255)                  DEFAULT NULL COMMENT 'VAT number',
    `vat_is_valid` int(10) unsigned              DEFAULT NULL COMMENT 'VAT number validity',
    PRIMARY KEY (`id`),
    KEY `XMAG_CUSTOMER_ADDRESS_CUSTOMER_ID` (`customer_id`),
    CONSTRAINT `XMAG_CUSTOMER_ADDRESS_CUSTOMER_ID` FOREIGN KEY (`customer_id`) REFERENCES `xmag_customer` (`id`) ON DELETE CASCADE on UPDATE SET NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='Customer Address';
