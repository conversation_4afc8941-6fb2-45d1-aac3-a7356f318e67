ALTER TABLE `warehouse_picklist_detail`
    ADD COLUMN `stock_stable_dump`       longtext default NULL COMMENT 'Stock stable snapshot when changing stock stable in JSON' AFTER `status`,
    ADD COLUMN `is_stock_stable_updated` tinyint  default 0 COMMENT 'Update status in stock stable' AFTER `status`
;

ALTER TABLE `warehouse_picklist_detail`
    CHANGE `order_item_id` `order_item_id` INT(11) NOT NULL COMMENT 'PK: OrderItem ID',
    ADD PRIMARY KEY (`order_item_id`);

ALTER TABLE `warehouse_picklist`
    ADD COLUMN `booking_detail`       longtext default NULL COMMENT 'Stock stable snapshot when changing stock stable in JSON' AFTER `note`;

