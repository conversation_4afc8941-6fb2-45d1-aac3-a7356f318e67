<?php

use App\Lib\FuncModel;
use App\Models\Sys\SysLog;
use App\Service\Sys\SysLog\SysLogService;

require __DIR__ . '/../../src/App/App.php';

/** @var \Slim\Container $container */

set_exception_handler(function ($exception) use ($container) {
    FuncModel::cleanUp();
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_ERROR, $exception?->getMessage());
    throw $exception;
});

set_error_handler(function ($exception) use ($container) {
    FuncModel::cleanUp();
    SysLogService::saveLog(SysLog::CATEGORY_ITEM_STATS_CRON, SysLog::NAME_ITEM_STATS_CRON, SysLog::STATUS_ERROR, $exception?->getMessage());
    throw $exception;
});
