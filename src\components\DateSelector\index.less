.content {
  :global {
    width: 930px !important;
    padding: 0;

    .ant-pro-card-title {
      font-size: 70px;
    }
    .step {
      margin-bottom: 32px;
    }

    .ant-btn-lg {
      width: 100px;
      height: 100px;
      padding: 0;
      font-size: 56px;
      line-height: 56px;
      border-radius: 4px;

      &.keypad-btn {
        width: 140px;
      }

      &.other {
        margin-top: 8px;
        margin-right: 8px;
        margin-bottom: 8px;
        margin-left: auto;
      }

      .anticon {
        font-size: 56px;
      }
    }

    .keypad-btn {
      margin: 8px;

      &.selected {
        /* opacity: 80%; */
      }

      &.keypad-btn-month {
        width: 80px;
        height: 80px;
        font-size: 56px;
        line-height: 56px;
        border-radius: 4px;
      }
    }

    .ant-space-compact-block {
      width: unset;
    }
  }
}
.contentSmall {
  :global {
    width: 730px;

    .keypad-btn-sm {
      width: 100px;
      height: 80px;
      margin: 8px;
      font-size: 36px;
    }
  }
}
