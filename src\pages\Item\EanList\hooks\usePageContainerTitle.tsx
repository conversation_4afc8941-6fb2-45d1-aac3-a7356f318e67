import { BarcodeOutlined, EuroCircleOutlined, FireOutlined, PictureOutlined } from '@ant-design/icons';
import { Space } from 'antd';
import { useMemo } from 'react';
import type { ReactElement } from 'react-markdown/lib/react-markdown';
import { IRoute, <PERSON> } from 'umi';

export type RoutePropParamType = IRoute;

export default (route: RoutePropParamType, options?: { titleNext?: any }) => {
  const pageTitleEle = useMemo(() => {
    const tmpTitle: string | ReactElement = route.name ?? '';

    switch (route.path) {
      case '/item/ean-all-summary':
        return (
          <>
            <BarcodeOutlined /> {tmpTitle}
          </>
        );
      case '/item/ean-all-special':
        return (
          <>
            <FireOutlined /> {tmpTitle}
          </>
        );
      case '/item/ean-all-pic':
        return (
          <>
            <PictureOutlined /> {tmpTitle}
          </>
        );
      case '/item/ean-all-prices':
        return (
          <>
            <EuroCircleOutlined /> {tmpTitle}
          </>
        );
      default:
        return tmpTitle;
    }
  }, [route]);

  const btnLinks = useMemo(() => {
    return (
      <Space size={12} style={{ fontSize: 14, marginLeft: 50 }}>
        <Link to="/item/ean-all-summary" title="Show EAN All">
          <BarcodeOutlined />
        </Link>
        <Link to="/item/ean-all-prices" title="Show EAN Prices">
          <EuroCircleOutlined />
        </Link>
        <Link to="/item/ean-all-special" title="Show EAN Specials/Labels">
          <FireOutlined />
        </Link>
        <Link to="/item/ean-all-pic" title="Show EAN Pictures">
          <PictureOutlined />
        </Link>
        <Link to="/item/ean-all-gdsn" title="Show EAN GDSN">
          GDSN
        </Link>
      </Space>
    );
  }, []);

  const pageTitle = useMemo(() => {
    return (
      <Space>
        <div style={{ display: 'inline-block', width: 260 }}>{pageTitleEle}</div>
        {options?.titleNext}
        {btnLinks}
      </Space>
    );
  }, [btnLinks, pageTitleEle, options?.titleNext]);

  return { pageTitle };
};
