import { type EanPriceRecordType } from '@/services/foodstore-one/Item/ean';
import { ProTable, type ActionType, type ProColumns } from '@ant-design/pro-table';
import { useMemo, useState, useRef, useEffect, useCallback } from 'react';
import Util, { ni, sn } from '@/util';
import _ from 'lodash';
import type { Offer2IboPreMatrixResultType } from '@/services/foodstore-one/Offer/offer';
import { getOffer2IboPreMatrix } from '@/services/foodstore-one/Offer/offer';
import { ReloadOutlined } from '@ant-design/icons';

export type RecordType = API.Offer & API.OfferItem & Record<string, any>;

export type Offer2IboPreMatrixTableProps = {
  itemEan?: EanPriceRecordType;
  reloadList?: () => void;
  refreshTick?: number;
};

const Offer2IboPreMatrixTable: React.FC<Offer2IboPreMatrixTableProps> = (props) => {
  const { itemEan, refreshTick } = props;

  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<Offer2IboPreMatrixResultType>();

  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  const loadMatrix = useCallback(() => {
    setLoading(true);
    getOffer2IboPreMatrix({ eanId: itemEan?.id })
      .then((res) => setData(res))
      .catch(Util.error)
      .finally(() => {
        setLoading(false);
      });
  }, [itemEan?.id]);

  const iboPreCols = useMemo<ProColumns<RecordType>[]>(() => {
    const cols: ProColumns<RecordType>[] = [];
    if (data?.iboPreIds && data?.iboPreAssoc) {
      data.iboPreIds.forEach((iboPreId, index) => {
        const iboPre = data.iboPreAssoc[iboPreId];
        cols.push({
          title: (
            <div
              className="rotate-rl"
              style={{ paddingRight: 20 }}
            >{`#${iboPre.ibo_pre_management_id} - ${iboPre.supplier?.name} (${iboPre.status})`}</div>
          ),
          dataIndex: [`ibo_pre_${iboPreId}_total_piece_qty`],
          width: 60,
          align: 'right',
          render(dom, entity) {
            return ni(sn(entity[`ibo_pre_${iboPreId}_total_piece_qty`]));
          },
          onCell: (entity) => {
            return {
              onClick: () => {
                /* const hide = message.loading('Updating...', 0);
                assignIboPre2OfferItem({ offerItemId: sn(entity.id), iboPreId: iboPreId })
                  .then((res) => {
                    message.success('Assigned successfully.');
                    loadMatrix();
                  })
                  .catch(Util.error)
                  .finally(() => hide()); */
              },
            };
          },
        });
      });
    }
    return cols;
  }, [data?.iboPreIds, data?.iboPreAssoc]);

  const orgColumns: ProColumns<RecordType>[] = useMemo(() => {
    return [
      {
        title: 'Open Offer / Expecting Offers',
        dataIndex: ['note'],
        width: 200,
        hideInSearch: true,
        render(__, record) {
          return sn(record.id) > 0 ? `${record.id} - ${record.note}` : record.note;
        },
      },
      {
        title: 'Offer Qty',
        dataIndex: ['qty'],
        width: 60,
        align: 'right',
        hideInSearch: true,
        render(dom, record) {
          return ni(sn(record.case_qty) * sn(record.qty));
        },
      },

      ...iboPreCols,
    ];
  }, [iboPreCols]);

  useEffect(() => {
    loadMatrix();
  }, [loadMatrix]);

  useEffect(() => {
    setColumns([...orgColumns]);
  }, [orgColumns]);

  useEffect(() => {
    if (refreshTick) {
      loadMatrix();
    }
  }, [loadMatrix, refreshTick]);

  return (
    <>
      <ProTable<RecordType, API.PageParams>
        headerTitle={
          <>
            Offer x Ibo Pre&nbsp;&nbsp;&nbsp;
            <ReloadOutlined
              className="text-sm"
              onClick={() => {
                loadMatrix();
              }}
            />
          </>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={false}
        size="small"
        loading={loading}
        bordered
        columnEmptyText=""
        dataSource={data?.offerItems || []}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: 5,
        }}
        search={false}
        columns={columns}
        tableAlertRender={false}
        cardProps={{ bodyStyle: { padding: 0 } }}
      />
    </>
  );
};

export default Offer2IboPreMatrixTable;
