import { deleteIboDraftDetail, getIboDraftDetailList } from '@/services/foodstore-one/IBO/ibo-draft-detail';
import Util from '@/util';
import { CheckCircleOutlined, DeleteOutlined, WarningOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Card, Col, message, Popconfirm, Row, Space, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { ProFormInstance } from '@ant-design/pro-form';
import { useLocation } from 'umi';
import EanIboDetailModal from './EanIboDetailModal';
import styles from './IBODraftDetailListMobile.less';
import moment from 'moment';

type IBODraftDetailListMobileProps = {
  ibomId?: number;
  refreshTick?: number;
};
const IBODraftDetailListMobile: React.FC<IBODraftDetailListMobileProps> = ({ refreshTick, ibomId }) => {
  const location: any = useLocation();

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  // datasource for inline editing
  const [datasource, setDatasource] = useState<API.IboDraftDetail[]>([]);

  const [currentRow, setCurrentRow] = useState<API.IboDraftDetail>();
  const [openEanIboDetailModal, setOpenEanIboDetailModal] = useState<boolean>(false);

  const columns: ProColumns<API.IboDraftDetail>[] = [
    {
      title: 'SKU',
      dataIndex: ['sku'],
      copyable: true,
      filterSearch: true,
      width: 160,
      render(__, entity) {
        return (
          <Space size={6} direction="vertical" style={{ lineHeight: 1.1 }}>
            <Typography.Text copyable>{entity.item_ean?.sku}</Typography.Text>
            <Typography.Text>{entity.item_ean?.ean}</Typography.Text>
            <div>
              {entity.item_ean?.is_single ? null : <Typography.Text>{entity.item_ean?.parent?.ean}</Typography.Text>}
              &nbsp;
            </div>
          </Space>
        );
      },
    },
    {
      title: 'Text of EAN',
      dataIndex: ['ean_name'],
      width: 330,
      search: false,
      render(dom, entity, index, action, schema) {
        return (
          <Space size={6} direction="vertical" style={{ lineHeight: 1.1 }}>
            <div>{entity.ean_name}</div>
            <div className="c-grey">{entity.product_no}</div>
          </Space>
        );
      },
    },
    {
      title: 'Qty of Boxes',
      dataIndex: ['box_qty'],
      valueType: 'digit',
      sorter: false,
      align: 'left',
      width: 120,
      search: false,
      render: (__, record, index) => {
        let expDateEle = null;
        if (record.exp_date) {
          const mDate = record.exp_date ? moment(record.exp_date) : null;
          const days = mDate && mDate.isValid() ? mDate?.diff(moment(), 'days') : 0;
          let cls = '';
          if (days <= 14) {
            cls = 'c-red';
          }

          expDateEle = (
            <span className={cls} title={`Expired after ${days} days.`}>
              {Util.dtToDMY(record.exp_date)}&nbsp;
              {days < 14 ? <WarningOutlined className="c-red" style={{ verticalAlign: 'top' }} /> : null}
            </span>
          );
        }

        /* return record.is_booked ? (
          Util.numberFormat(record.box_qty, false, 0)
        ) : (
          <EditableCell
            dataType="number"
            defaultValue={record.box_qty}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              return updateIboDraftDetail(record.id, {
                box_qty: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  setDatasource((prev) => {
                    const newDatasource = [...prev];
                    newDatasource[index] = { ...newDatasource[index], ...res };
                    return newDatasource;
                  });
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {Util.numberFormat(record.box_qty, false, 0)}
          </EditableCell>
        ); */

        return (
          <Space size={6} direction="vertical" style={{ lineHeight: 1.1 }}>
            <div>
              <span style={{ fontSize: 16, fontWeight: 'bold' }}>{Util.numberFormat(record.box_qty, false, 0)}</span> x{' '}
              {record.case_qty}
            </div>
            {expDateEle}
            <div>{record.warehouse_location?.name}</div>
          </Space>
        );
      },
    },
    /* {
      title: 'Qty',
      dataIndex: ['qty'],
      valueType: 'digit',
      sorter: false,
      align: 'right',
      width: 80,
      search: false,
    }, */
    /* {
      title: 'EXP. Date',
      dataIndex: ['exp_date'],
      valueType: 'date',
      sorter: false,
      align: 'center',
      width: 130,
      search: false,
      render: (dom, record, index) => {
        return record.is_booked ? (
          Util.dtToDMY(record.exp_date)
        ) : (
          <EditableCell
            dataType="date"
            defaultValue={moment(record.exp_date)}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              return updateIboDraftDetail(record.id, {
                exp_date: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  setDatasource((prev) => {
                    const newDatasource = [...prev];
                    newDatasource[index] = { ...newDatasource[index], ...res };
                    return newDatasource;
                  });
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {Util.dtToDMY(record.exp_date)}
          </EditableCell>
        );
      },
    }, */
    /* {
      title: 'Location',
      dataIndex: ['warehouse_location', 'name'],
      sorter: false,
      width: 90,
      search: false,
      render: (dom, record, index) => {
        return record.is_booked ? (
          record.warehouse_location?.name
        ) : (
          <EditableCell
            dataType="select"
            defaultValue={record.warehouse_location_id}
            request={(params) => getWarehouseLocationACList(params)}
            triggerUpdate={(newValue: any, cancelEdit) => {
              return updateIboDraftDetail(record.id, {
                warehouse_location_id: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  setDatasource((prev) => {
                    const newDatasource = [...prev];
                    newDatasource[index] = { ...newDatasource[index], ...res };
                    return newDatasource;
                  });
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {record.warehouse_location?.name}
          </EditableCell>
        );
      },
    }, */
    {
      title: 'Offered Qty',
      dataIndex: ['offers'],
      width: 150,
      search: false,
      render(__, record) {
        return record.item_ean?.expecting_offer_items?.map((x) => {
          return (
            <Row key={x.id}>
              <Col span={14}>Offer #{x.offer_no}</Col>
              <Col span={10} className="text-right">
                {x.qty}
              </Col>
            </Row>
          );
        });
      },
    },
    /* {
      title: 'Marked?',
      dataIndex: ['mark'],
      sorter: false,
      width: 70,
      align: 'center',
      search: false,
      render: (dom, record) =>
        record.mark ? (
          <CheckCircleOutlined style={{ color: 'green' }} />
        ) : (
          <ProFormCheckbox
            fieldProps={{
              skipGroup: true,
              onChange: (e) => {
                updateIboDraftDetail(record.id, { mark: 1 })
                  .then((res) => {
                    message.success('Marked successfully.');
                    actionRef.current?.reload();
                  })
                  .catch(Util.error);
              },
            }}
            formItemProps={{ style: { margin: 0 } }}
          />
        ),
    }, */
    {
      title: 'Booked?',
      dataIndex: ['is_booked'],
      sorter: false,
      width: 30,
      align: 'center',
      className: 'p-0',
      search: false,
      render: (dom, record) => (
        <CheckCircleOutlined style={{ fontSize: 24, color: record.is_booked == 1 ? 'green' : 'lightgrey' }} />
      ),
    },
    {
      title: '',
      valueType: 'option',
      sorter: false,
      width: 50,
      align: 'center',
      search: false,
      render: (dom, record) =>
        record.is_booked != 1 ? (
          <Popconfirm
            title={<>Are you sure you want to delete?</>}
            overlayStyle={{ width: 350, maxWidth: 350 }}
            placement="left"
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              const hide = message.loading('Deleting...', 0);
              deleteIboDraftDetail(record.id)
                .then(() => {
                  actionRef.current?.reload();
                })
                .finally(() => hide());
            }}
          >
            <DeleteOutlined style={{ color: 'red', fontSize: 24 }} />
          </Popconfirm>
        ) : undefined,
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [ibomId]);

  useEffect(() => {
    if (refreshTick && refreshTick > 0) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  useEffect(() => {
    if (location.query.sku) {
      formRef.current?.setFieldValue('sku', location.query.sku);
      actionRef.current?.reload();
    }
  }, [location.query.sku]);

  return (
    <div className={styles.iboRegisterListPageMobile}>
      <Card
        /* title={
          <>
            IBO Draft list&nbsp;
            <InfoCircleOutlined title="You can edit unbooked entries on cells" />
          </>
        } */
        style={{ marginTop: 24 }}
        /* extra={
          <Space>
            <Popconfirm
              title={<>Are you sure you want to book the unbooked drafts?</>}
              overlayStyle={{ width: 350, maxWidth: 350 }}
              placement="left"
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                const hide = message.loading('Booking...', 0);
                bookIboDraftDetail({ ibom_id: ibomId })
                  .then((res) => {
                    message.success('Booked successfully.', 3);
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              }}
            >
              <Button type="primary" size="large">
                Book all
              </Button>
            </Popconfirm>

            <Button
              type="primary"
              size="large"
              icon={<FilePdfOutlined />}
              onClick={() => {
                const hide = message.loading('Downloading summary data as PDF format...', 0);
                exportIboDraftDetailList({ ibomId })
                  .then((res) => {
                    hide();
                    if (res.url) {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Summary
            </Button>
            <Button
              type="default"
              size="large"
              icon={<FilePdfOutlined />}
              onClick={() => {
                const hide = message.loading('Downloading non-booked summary data as PDF format...', 0);
                exportIboDraftDetailList({ ibomId, is_booked: 0 })
                  .then((res) => {
                    hide();
                    if (res.url) {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Summary (only not booked)
            </Button>
            <Button
              type="default"
              size="large"
              icon={<FilePdfOutlined />}
              onClick={() => {
                const hide = message.loading('Downloading marked summary data as PDF format...', 0);
                exportIboDraftDetailList({ ibomId, mark: 1 })
                  .then((res) => {
                    hide();
                    if (res.url) {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Show Summary (marked)
            </Button>

            <Button
              type="primary"
              size="large"
              icon={<FileExcelOutlined />}
              onClick={() => {
                const hide = message.loading('Downloading list in Xls...', 0);
                exportIboDraftDetailListInXls({ ibomId })
                  .then((res) => {
                    hide();
                    if (res.url) {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Export
            </Button>

            <Button size="large" onClick={() => actionRef.current?.reload()} icon={<ReloadOutlined />} />
          </Space>
        } */
      >
        <ProTable<API.IboDraftDetail, API.PageParams>
          formRef={formRef}
          actionRef={actionRef}
          rowKey="id"
          revalidateOnFocus={false}
          options={false}
          search={false}
          sticky
          size="large"
          showHeader={false}
          scroll={{ x: 800, y: 800 }}
          cardProps={{ style: { padding: 0 }, bodyStyle: { padding: 0 } }}
          params={{ ibomId, with: 'warehouseLocation,expectingOfferItems,itemEan,itemEan.parent' }}
          dataSource={datasource}
          request={async (params, sort, filter) => {
            if (!ibomId) {
              setDatasource([]);
              return Promise.resolve([]);
            }
            return getIboDraftDetailList(
              {
                ...params,
                ...formRef.current?.getFieldsValue(),
              },
              { ...sort, id: 'descend' },
              filter,
            )
              .then((res) => {
                setDatasource(res.data);
                return res;
              })
              .finally(() => {});
          }}
          onRequestError={Util.error}
          columns={columns}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: 100,
          }}
          rowClassName={(record) => {
            let cls = record?.case_qty == 1 ? 'row-single' : 'row-multi';
            if (record.item_ean?.expecting_offer_items?.length) {
              cls += ' reset-tds-bg bg-light-orange2';
            }
            return cls;
          }}
          rowSelection={false}
          columnEmptyText=""
        />
      </Card>

      <EanIboDetailModal
        modalVisible={openEanIboDetailModal}
        handleModalVisible={setOpenEanIboDetailModal}
        iboDraftDetail={currentRow}
      />
    </div>
  );
};
export default IBODraftDetailListMobile;
