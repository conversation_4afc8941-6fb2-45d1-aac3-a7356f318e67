ALTER TABLE `warehouse_picklist`
    add column `detail`         TEXT    NULL COMMENT 'Detail in JSON',
    add column `status_picking` tinyint NOT NULL default 0 COMMENT 'Picking Status: 0: Open, 1: Processing, 2: Done' after `label_files`,
    add column `status_packing` tinyint NOT NULL default 0 COMMENT 'Packing Status: 0: Open, 1: Processing, 2: Done' after `status_picking`;


RENAME TABLE `warehouse_picklist_time_tracking` TO `warehouse_picklist_time_track`;


ALTER TABLE `warehouse_picklist_time_track`
    ADD INDEX `IDX_warehouse_picklist_time_tracking_start_datetime` (`start_datetime`);

ALTER TABLE `warehouse_picklist_time_track`
    ADD INDEX `IDX_warehouse_picklist_time_tracking_end_datetime` (`end_datetime`);

