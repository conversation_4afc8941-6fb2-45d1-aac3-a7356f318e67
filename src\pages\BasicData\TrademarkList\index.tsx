import { EditOutlined, FileExcelOutlined, FilePdfOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Tag, Popover, Card, Image } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { nf2, sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getTrademarkList, deleteTrademark, updateTrademark } from '@/services/foodstore-one/BasicData/trademark';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import UpdateSupplierPricesForm from './components/UpdateSupplierPricesForm';
import EditableCell from '@/components/EditableCell';
import {
  ProForm,
  ProFormText,
  type ProFormInstance,
  ProFormSelect,
  ProFormRadio,
  ProFormSwitch,
} from '@ant-design/pro-form';
import { exportOfferItemList } from '@/services/foodstore-one/Offer/offer-item';
import SProFormDigit from '@/components/SProFormDigit';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import UpdateLogoFileForm from './components/UpdateLogoFileForm';
import ExportPdfSettingFormModal from '@/pages/Offer/OfferItemList/components/ExportPdfSettingFormModal';
import useTrademarkGroupOptions from '@/hooks/BasicData/useTrademarkGroupOptions';
import { history } from 'umi';

type ExportForm = {
  include_bp_xls?: boolean;
  percentage?: number;
};

export type SearchFormValueType = Partial<API.Trademark>;

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Trademark[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteTrademark({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const TrademarkList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updateSupplierPriceModalVisible, handleUpdateSupplierPriceModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Trademark>();
  const [selectedRowsState, setSelectedRows] = useState<API.Trademark[]>([]);

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();

  // Export loading
  const [loading, setLoading] = useState<boolean>(false);

  // Export XLS Option Form
  const exportFormRef = useRef<ProFormInstance<ExportForm>>();
  const [openExportForm, setOpenExportForm] = useState<boolean>(false);

  // Export PDF Option Form
  const [openExportPdfForm, setOpenExportPdfForm] = useState<boolean>(false);

  // trademark log file update form
  const [openUpdateLogoFileForm, setOpenUpdateLogoFileForm] = useState<boolean>(false);

  // Trademark group
  const { trademarkGroupOptions } = useTrademarkGroupOptions();

  const columns: ProColumns<API.Trademark>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 50,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Image',
      dataIndex: ['logo_file', 'url'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      width: 80,
      render: (dom, record) => {
        return (
          <div>
            <div style={{ position: 'absolute', top: 0, right: 0, display: 'block' }}>
              <EditOutlined
                className="cursor-pointer text-sm c-blue"
                onClick={() => {
                  setCurrentRow(record);
                  setOpenUpdateLogoFileForm(true);
                }}
              />
            </div>
            {record.logo_file ? (
              <Image
                key={record.logo_file.id}
                src={record.logo_file.thumb_url}
                preview={{
                  src: record.logo_file.url,
                }}
                wrapperStyle={{ display: 'inline-block' }}
                width={40}
              />
            ) : null}
          </div>
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 200,
      sorter: true,
    },
    {
      title: 'Producers',
      dataIndex: 'producers',
      sorter: false,
      align: 'left',
      valueType: 'select',
      width: 250,
      renderText: (val: string, record) => (
        <div>{record?.producers?.map((x) => x.name).join(', ') || <div>&nbsp;</div>}</div>
      ),
    },
    {
      title: 'GFC Price Setting',
      dataIndex: 'supplier_price_settings',
      tooltip: 'Click to edit',
      sorter: false,
      render(dom, record) {
        return record.supplier_price_settings?.map((x) => (
          <Tag key={`${x.uid}`}>
            {x.supplier_name} : {nf2(x.price_percentage, true, true)}%
          </Tag>
        ));
      },
      onCell: (record) => {
        return {
          onClick: () => {
            handleUpdateSupplierPriceModalVisible(true);
            setCurrentRow(record);
          },
          className: 'cursor-pointer',
        };
      },
    },
    {
      title: 'EANs',
      dataIndex: 'item_eans_count',
      width: 100,
      align: 'center',
      renderText: (val: string) => `${val}`,
    },
    {
      title: 'Name in PDF',
      tooltip: 'Click to edit',
      dataIndex: ['name_pdf'],
      width: 300,
      ellipsis: true,
      hideInSearch: true,
      render(dom, record) {
        return (
          <EditableCell
            dataType="text"
            defaultValue={record.name_pdf || ''}
            style={{ marginRight: 0 }}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateTrademark({
                id: record.id,
                name_pdf: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {dom}
          </EditableCell>
        );
      },
    },
    {
      title: 'Sort in PDF',
      tooltip: 'Click to edit',
      dataIndex: 'sort_pdf',
      valueType: 'digit',
      align: 'right',
      width: 120,
      sorter: true,
      showSorterTooltip: false,
      render: (dom, record, index, action) => {
        const defaultValue = record.sort_pdf;
        return (
          <EditableCell
            dataType="number"
            precision={0}
            defaultValue={defaultValue}
            triggerUpdate={(newValue: any, cancelEdit) => {
              return updateTrademark({
                id: record.id,
                sort_pdf: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {Util.numberFormat(record.sort_pdf, false, 0)}
          </EditableCell>
        );
      },
    },
    {
      title: 'Group',
      dataIndex: 'group_id',
      width: 150,
      align: 'center',
      render(__, record) {
        return (
          <EditableCell
            dataType="select"
            defaultValue={record.group_id || ''}
            style={{ marginRight: 0 }}
            fieldProps={{ style: { lineHeight: 1 }, allowClear: true }}
            showSearch
            options={trademarkGroupOptions}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateTrademark({
                id: record.id,
                group_id: newValue ?? null,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {record.group?.name}
          </EditableCell>
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 120,
      align: 'center',
      sorter: true,
      showSorterTooltip: false,
      renderText: (val: string) => `${val}`,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer
      extra={
        <Button
          type="link"
          key="groups"
          title="Go to groups"
          onClick={() => {
            history.push('/basic-data/trademark-group');
          }}
        >
          Groups
        </Button>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_trademarks', {
            name: '',
            name_pdf: '',
            sort_pdf: null,
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            name={'group_id'}
            label={'Group'}
            placeholder=""
            mode="single"
            showSearch
            options={[{ value: -1, label: '** No Group **' }, ...trademarkGroupOptions]}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              maxTagCount: 1,
            }}
            width={200}
            disabled={loading}
          />
          <ProFormText name={'name'} label="Name" width={200} placeholder={'Name'} />
          <ProFormSelect
            name="producers[]"
            label="Producers"
            placeholder="Please select producers"
            mode="multiple"
            request={getProducerListSelectOptions}
            width={180}
          />
          <ProFormText name={'name_pdf'} label="Name in PDF" width={200} placeholder={'Name in PDF'} />
          <ProFormText name={'sort_pdf'} label="Sort in PDF" width={160} placeholder={'Sort in PDF'} />
        </ProForm>
      </Card>

      <ProTable<API.Trademark, API.PageParams>
        headerTitle={'Trademark list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_trademarks_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        params={{ with: 'supplierPriceSettings,logoFile,group' }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_trademarks', searchFormValues);
          Util.setSfValues('sf_trademarks_p', params);

          return getTrademarkList({ ...params, ...searchFormValues }, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
          <Popover
            key="export-xls-popup"
            title="Select XLS Export Mode"
            trigger="click"
            open={openExportForm}
            onOpenChange={(visible) => {
              setOpenExportForm(visible);
            }}
            content={
              <ProForm<ExportForm>
                formRef={exportFormRef}
                size="small"
                layout="horizontal"
                style={{ width: 300 }}
                onFinish={async (values) => {
                  setOpenExportForm(false);

                  const hide = message.loading('Exporting XLS...', 0);
                  setLoading(true);
                  exportOfferItemList({
                    mode: 'byTrademark',
                    trademark_ids: selectedRowsState.map((x) => x.id),
                    group_id: searchFormRef.current?.getFieldValue('group_id'),
                    ...values,
                  })
                    .then((res) => {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
                submitter={{
                  searchConfig: { submitText: 'Export XLS' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormRadio.Group
                  name="include_mode"
                  initialValue={'std'}
                  options={[
                    { value: 'std', label: 'Std' },
                    { value: 'bp', label: 'Inc. BP' },
                    { value: 'supplier', label: 'Inc. Supplier' },
                  ]}
                />
                <ProFormSwitch name="only_gfc_active" label="Only GFC Active?" initialValue={true} />
                <SProFormDigit
                  colProps={{ span: 'auto' }}
                  name="percentage"
                  label="Percentage"
                  width={120}
                  addonAfter={'%'}
                  min={-99999999}
                  fieldProps={{
                    precision: 4,
                  }}
                  initialValue={115}
                  placeholder="Percentage"
                  formItemProps={{
                    tooltip: (
                      <div>
                        <div>GFC Offer Price will be Price * (1 + percentage)</div>
                      </div>
                    ),
                  }}
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenExportForm(true)}
              icon={<FileExcelOutlined />}
            >
              Export XLS (Offer Item)
            </Button>
          </Popover>,

          <Button
            key="export-pdf-modal"
            type="primary"
            htmlType="button"
            loading={loading}
            disabled={loading}
            onClick={() => setOpenExportPdfForm(true)}
            icon={<FilePdfOutlined />}
          >
            Export PDF (Offer Item)
          </Button>,
        ]}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              Trademark &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateSupplierPricesForm
        modalVisible={updateSupplierPriceModalVisible}
        handleModalVisible={handleUpdateSupplierPriceModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateSupplierPriceModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateLogoFileForm
        modalVisible={openUpdateLogoFileForm}
        handleModalVisible={setOpenUpdateLogoFileForm}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          setOpenUpdateLogoFileForm(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <ExportPdfSettingFormModal
        modalVisible={openExportPdfForm}
        handleModalVisible={setOpenExportPdfForm}
        getParentParams={() => {
          return {
            mode: 'byTrademark',
            trademark_ids: selectedRowsState.map((x) => x.id),
          };
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.Trademark>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<API.Trademark>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TrademarkList;
