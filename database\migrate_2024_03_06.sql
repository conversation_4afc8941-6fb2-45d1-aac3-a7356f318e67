drop table if exists `xmag_product`;

CREATE TABLE `xmag_product`
(
    `id`               bigint(20) unsigned NOT NULL,
    `sku`              varchar(100)   DEFAULT NULL,
    `status`           tinyint(4)     DEFAULT 1 comment 'Active: 1, Passive: 2',
    `attribute_set_id` int(11)        DEFAULT NULL comment 'attribute_set_id in Magento',
    `visibility`       int(11)        DEFAULT NULL comment 'visibility',
    `name`             varchar(255)   DEFAULT NULL comment 'Name',
    `price`            decimal(20, 4) DEFAULT NULL comment 'Price',
    `weight`           decimal(12, 4) DEFAULT NULL comment 'Weight',
    `website_ids`      varchar(255)   DEFAULT NULL,
    `created_on`       datetime       DEFAULT NULL,
    `updated_on`       datetime       DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `IDX_item_ean_sku` (`sku`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

replace into xmag_product(id, sku, status, attribute_set_id, visibility, name, price, weight, website_ids, created_on,
                          updated_on)
select JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.id')                       aS id
     , us_hash.sku
     , JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.status')                   aS status
     , JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.attribute_set_id')         aS attribute_set_id
     , JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.visibility')               aS visibility
     , JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.name')                     aS name
     , JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.price')                    aS price
     , JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.weight')                   aS weight
     , REPLACE(
        REPLACE(
                REPLACE(
                        REPLACE(
                                JSON_EXTRACT(us_hash.sync_detail,
                                             '$.downSyncData.extension_attributes.website_ids')
                            , '['
                            , ''
                            )
                    , ']'
                    , ''
                    )
            , '"'
            , ''
            ), ' '
    , '')                                                                           aS website_ids
     , JSON_UNQUOTE(JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.created_at')) aS created_on
     , JSON_UNQUOTE(JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.updated_at')) aS updated_on
from us_hash
         inner join item_ean ie on us_hash.sku = ie.sku
where JSON_EXTRACT(us_hash.sync_detail, '$.downSyncData.id') is not null
;


