import { getFileExt } from '@/util';
import {
  FileExcelOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
} from '@ant-design/icons';

type SFileIconType = {
  ext?: string;
  fileName?: string;
};

const SFileIcon: React.FC<SFileIconType> = (props) => {
  const { ext, fileName } = props;
  const extTmp = ext ? ext : getFileExt(fileName ?? '');

  if (extTmp == 'pdf') {
    return <FilePdfOutlined />;
  } else if (
    extTmp == 'png' ||
    extTmp == 'jpg' ||
    extTmp == 'jpeg' ||
    extTmp == 'bmp' ||
    extTmp == 'gif'
  ) {
    return <FileImageOutlined />;
  } else if (extTmp == 'csv' || extTmp == 'xls' || extTmp == 'xlsx') {
    return <FileExcelOutlined />;
  }

  return <FileTextOutlined />;
};

export default SFileIcon;
