import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Col, Divider, message, Popconfirm, Row, Spin } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { getUploadedDataList, importSupplierData, updateImport } from '@/services/foodstore-one/Import/import';
import SProFormDigit from '@/components/SProFormDigit';
import type { ActionType, ColumnsState, ProColumns, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { DEFAULT_PER_PAGE_PAGINATION, ImportStatus } from '@/constants';
import { CloseSquareOutlined, ImportOutlined, SaveOutlined, SettingOutlined } from '@ant-design/icons';
import { definedColLabelsImported, definedColsImported } from './XlsItemViewer';
import { getSupplier } from '@/services/foodstore-one/supplier';

const handleImport = async (fields: FormValueType) => {
  const hide = message.loading('Importing...', 0);

  try {
    await importSupplierData(fields.id || 0, fields);
    hide();
    message.success('Import is successful');
    return true;
  } catch (error) {
    hide();
    Util.error('Import failed, please try again!');
    return false;
  }
};

export enum ImportOption {
  add_new = 'Add New',
  // add_to_master = 'Add to Master',
  // delete_from_master = 'Delete from Master',
}

export type OptionFormValueType = {
  import_option: keyof ImportOption;
};

export type FormValueType = Partial<API.Import> & { import_option?: keyof ImportOption };

export type ImportExcelDataProps = {
  initialValue?: Partial<API.Import>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData?: FormValueType) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formValues?: FormValueType) => void;
};

const ImportExcelData: React.FC<ImportExcelDataProps> = (props) => {
  const { initialValue, modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);
  const formRef = useRef<ProFormInstance<API.ImportSettingsInputType>>();
  const actionRef = useRef<ActionType>();

  // excel's detailed data
  const [excelData, setExcelData] = useState<API.ExcelDataType>({
    cols: [],
    header: [],
    rows: [],
  });

  const [colStates, setColStates] = useState<Record<string, ColumnsState>>({});

  // Upload option form
  const optionFormRef = useRef<ProFormInstance<OptionFormValueType>>();
  const [openUploadOptionForm, setOpenUploadOptionForm] = useState<boolean>(false);

  const loadExcelData = async () => {
    setLoading(true);
    return getUploadedDataList({
      ...formRef.current?.getFieldsValue(),
      filters: formRef.current?.getFieldValue(['filters']),
      imported: 0,
      excel_list: 1,
      pageSize: actionRef.current?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
      id: initialValue?.id,
    })
      .then((res) => {
        const tmp = res.message.excelData || {};
        setExcelData({
          cols: tmp.cols,
          header: tmp.header,
          highestCol: tmp.highestCol,
          highestRow: tmp.highestRow,
          rows: res.data,
        });
        return res;
      })
      .catch((err) => {
        Util.error(err);
        return [];
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (modalVisible && formRef.current) {
      const newValues = { ...(initialValue || {}) };
      const settings = newValues.settings;

      formRef.current.resetFields();
      formRef.current.setFieldsValue({
        ...settings?.input,
        header_row: settings?.input?.header_row ?? 1,
        data_start_row: settings?.input?.data_start_row ?? 2,
      });

      setExcelData({
        cols: settings?.cols ?? settings?.input?.cols ?? [],
        rows: [],
        pagination: undefined,
        header: settings?.header ?? settings?.input?.header ?? [],
        highestCol: settings?.highestCol ?? settings?.input?.highestCol,
        highestRow: settings?.highestRow ?? settings?.input?.highestRow,
      });
    }
  }, [initialValue, modalVisible]);

  const columns: ProColumns<any>[] = useMemo(() => {
    return [
      {
        dataIndex: excelData.cols.length,
        title: 'Row No',
        width: 60,
        align: 'center',
        render: (item, record, index, action) => {
          return record[excelData.cols.length];
        },
      },
      ...excelData.cols.map((col, ind) => ({
        dataIndex: ind,
        title: (config: ProColumnType, type: any, filters: any) => {
          return (
            <>
              <CloseSquareOutlined
                className="cursor-pointer text-small c-gray"
                title="Close column"
                style={{ position: 'absolute', right: 4, top: 4 }}
                onClick={(e) => {
                  setColStates((prev) => ({ ...prev, [ind]: { show: false } }));
                }}
              />
              <div>
                {excelData.header?.[ind] ? `${excelData.header?.[ind]}` : ''}
                {excelData.header?.[ind] ? ` (${col})` : col}
              </div>
              <ProFormDependency key={'col_table_dep' + col} name={definedColsImported}>
                {(depValues) => {
                  let selectedCol = null;
                  for (const keyCol of Object.keys(depValues)) {
                    if (depValues[keyCol] == col) {
                      selectedCol = definedColLabelsImported[keyCol] ?? null;
                    }
                  }
                  return selectedCol ? <div className="c-green">{selectedCol}</div> : null;
                }}
              </ProFormDependency>
            </>
          );
        },
        width: 150,
        ellipsis: true,
        align: 'left',
      })),
    ] as ProColumns<any>[];
  }, [excelData.cols, excelData.header]);

  const colOptions = useMemo(
    () =>
      excelData.cols.map((col, index) => ({
        value: col,
        label: `${col} ${excelData.header?.[index] ? `(${excelData.header?.[index]})` : ''}`,
      })),
    [excelData.cols, excelData.header],
  );

  const handleSaveSetting = async (saveSupplierSetting?: boolean) => {
    if (initialValue?.status != ImportStatus.STATUS_UPLOADED) return;

    if (!formRef.current?.isFieldsTouched()) {
      return;
    }

    const hide = message.loading('Saving settings...', 0);

    try {
      await updateImport(initialValue?.id, {
        new_settings: {
          ...formRef.current?.getFieldsValue(),
          header: excelData?.header || [],
          cols: excelData?.cols || [],
          highestCol: excelData?.highestCol,
          highestRow: excelData?.highestRow,
        },
        saveSupplierSetting,
      });
      hide();
      message.success('Settings saved successful');
      return true;
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  const handleLoadSettingInSupplier = async () => {
    const hide = message.loading('Loading a default setting...', 0);

    try {
      return getSupplier(initialValue?.supplier_id)
        .then((res) => {
          const defaultSettings = res.import_setting;

          formRef.current?.resetFields();
          if (defaultSettings) {
            formRef.current?.setFieldsValue(defaultSettings?.input || {});
          }

          return true;
        })
        .finally(() => {
          hide();
          message.success('Setting is loaded successfully');
        });
    } catch (error) {
      hide();
      Util.error(error);
      return false;
    }
  };

  const tableEle = useMemo(
    () => (
      <ProTable<any, API.PageParams>
        headerTitle="Excel Data"
        rowKey={(record, index) => {
          return `${index}`;
        }}
        /* headerTitle={`Excel total rows: ${Util.numberFormat(
        excelData.highestRow || 0,
      )} Total data: ${Util.numberFormat(excelData?.pagination?.totalRows || 0)}`}
      rowKey={(record, index) => {
        return `${index}`;
      }} */
        revalidateOnFocus={false}
        actionRef={actionRef}
        options={{ fullScreen: true, reload: false, density: false, search: false, setting: false }}
        scroll={{ x: 800, y: 300 }}
        size="small"
        bordered
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        search={false}
        dataSource={[...(excelData.rows || [])]}
        columns={columns}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
      />
    ),
    [excelData.rows, colStates, columns],
  );

  return (
    <ModalForm<API.ImportSettingsInputType>
      title={`Import Supplier Data${initialValue?.is_master ? ' - RW/RW_D Master' : ''}`}
      width="1200px"
      visible={modalVisible}
      layout="horizontal"
      initialValues={initialValue || {}}
      formRef={formRef}
      labelCol={{ flex: '150px' }}
      isKeyPressSubmit={false}
      size="small"
      className="modal-form"
      onVisibleChange={async (visible: boolean) => {
        if (!visible) {
          await handleSaveSetting();
        }
        handleModalVisible(visible);
      }}
      submitter={{
        render: (form, doms) => {
          return (
            <Button
              onClick={async () => {
                await handleSaveSetting();
                handleModalVisible(false);
              }}
            >
              {' '}
              Close
            </Button>
          );
        },
      }}
      onFinish={async (value) => {
        setLoading(true);
        const success = await handleImport({
          ...value,
          id: initialValue?.id,
          import_option: 'add_new' as any,
        });
        setLoading(false);

        if (success) {
          handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value as any);
        }
      }}
    >
      <Spin spinning={loading}>
        <Row>
          <Col span={12}>
            <ProForm.Item name="table_name" label="Table Name">
              <div>{initialValue?.table_name}</div>
            </ProForm.Item>
          </Col>
          <Col span={12}>
            <ProForm.Item name="table_name" label="File">
              <a href={initialValue?.files?.[0]?.url || '#'} target="_blank" rel="noreferrer">
                {initialValue?.files?.[0]?.clean_file_name}
              </a>
            </ProForm.Item>
          </Col>
        </Row>
        <Divider orientation="left">Define data range</Divider>
        <Row gutter={24}>
          <Col>
            <SProFormDigit
              name="header_row"
              label="Header row No"
              tooltip={'0 means no header'}
              initialValue={1}
              min={0}
              max={excelData.highestRow}
              width="xs"
            />
          </Col>
          <Col>
            <SProFormDigit
              name="data_start_row"
              label="Data start row No"
              min={1}
              initialValue={2}
              max={excelData.highestRow}
              width="xs"
            />
          </Col>
          <Col>
            <SProFormDigit name="page" label="Page No" min={1} initialValue={1} width="xs" />
          </Col>
          <Col>
            <Button type="primary" onClick={loadExcelData}>
              {excelData.cols?.length ? 'Reload XLS' : 'Load XLS'}
            </Button>
          </Col>
        </Row>
        <Divider orientation="left">Select specific columns</Divider>
        <Row>
          <Col span={8}>
            <ProFormDependency
              key={'col_ean_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_ean')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    showSearch
                    rules={[
                      {
                        required: true,
                        message: 'EAN Column is required',
                      },
                    ]}
                    width="sm"
                    name="col_ean"
                    label="EAN column"
                    placeholder="EAN column"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_price_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_price')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    showSearch
                    placeholder="Price"
                    width="sm"
                    name="col_price"
                    label="Price"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_multi_ean_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_multi_ean')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    showSearch
                    placeholder="Multi EAN"
                    width="sm"
                    name="col_multi_ean"
                    label="Multi EAN"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_case_qty_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_case_qty')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    showSearch
                    placeholder="Qty/pkg"
                    width="sm"
                    name="col_case_qty"
                    label="Qty/pkg"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_name_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_name')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    showSearch
                    placeholder="Name"
                    width="sm"
                    name="col_name"
                    label="Name"
                    rules={[
                      {
                        required: true,
                        message: 'Name Column is required',
                      },
                    ]}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_trademark_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_trademark')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    showSearch
                    placeholder="Trademark"
                    width="sm"
                    name="col_trademark"
                    label="Trademark"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_hs_code_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_hs_code')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="HS Code"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_hs_code"
                    label="HS Code"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_shelf_life_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_shelf_life')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="Normal Shelf Life"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_shelf_life"
                    label="Normal Shelf Life"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_vat_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_vat')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="VAT"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_vat"
                    label="VAT"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_article_no_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_article_no')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="Article No"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_article_no"
                    label="Article No"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_maker_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_maker')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="Manufacturer"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_maker"
                    label="Manufacturer"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_gln_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_gln')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="GLN"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_gln"
                    label="GLN"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_uvp_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_uvp')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="UVP"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_uvp"
                    label="UVP"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_nan_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_nan')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_nan}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_nan"
                    label={definedColLabelsImported.col_nan}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_category_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_category')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_category}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_category"
                    label={definedColLabelsImported.col_category}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_price_pallet_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_price_pallet')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_price_pallet}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_price_pallet"
                    label={definedColLabelsImported.col_price_pallet}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_price_valid_from_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_price_valid_from')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_price_valid_from}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_price_valid_from"
                    label={definedColLabelsImported.col_price_valid_from}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_price_valid_to_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_price_valid_to')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_price_valid_to}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_price_valid_to"
                    label={definedColLabelsImported.col_price_valid_to}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_ve_pallet_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_ve_pallet')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_ve_pallet}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_ve_pallet"
                    label={definedColLabelsImported.col_ve_pallet}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_languages_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_languages')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_languages}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_languages"
                    label={definedColLabelsImported.col_languages}
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_bbd_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_bbd')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder={definedColLabelsImported.col_bbd}
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_bbd"
                    label={definedColLabelsImported.col_bbd}
                  />
                );
              }}
            </ProFormDependency>
          </Col>

          <Divider />
          <Col span={8}>
            <ProFormDependency
              key={'col_qty_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_qty')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="Qty"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_qty"
                    label="Qty"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_box_qty_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_box_qty')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="Qty of boxes"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_box_qty"
                    label="Qty of boxes"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency
              key={'col_exp_date_dep'}
              name={['col_ignores', ...definedColsImported.filter((x) => x != 'col_exp_date')]}
            >
              {(depValues) => {
                const values = [...Object.values(depValues), ...(depValues?.col_ignores || [])];
                return (
                  <ProFormSelect
                    showSearch
                    placeholder="EXP. Date"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    width="sm"
                    name="col_exp_date"
                    label="EXP. Date"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={24}>
            <ProFormSelect
              showSearch
              placeholder={'Name override'}
              options={colOptions}
              mode="multiple"
              width="xl"
              name="calc_name"
              label={'Name override'}
              labelCol={{ span: 3 }}
            />
          </Col>
          <Col span={8}>
            <SProFormDigit
              name="calc_price"
              label="Price %"
              tooltip="Update price by percentage"
              initialValue={100}
              fieldProps={{ precision: 3 }}
            />
          </Col>
        </Row>

        {excelData?.header && excelData.header?.findIndex((x) => `${x || ''}`.toLowerCase() == 'bezugsweg') >= 0 && (
          <>
            <Divider orientation="left">Import Filters</Divider>
            <Row>
              <Col span={8}>
                <ProFormSelect
                  showSearch
                  placeholder={definedColLabelsImported.filter_ref_path}
                  options={colOptions}
                  width="sm"
                  name="filter_ref_path"
                  label={definedColLabelsImported.filter_ref_path}
                  tooltip="If specified, filtered Xls rows will be imported."
                />
              </Col>
              <Col span={8}>
                <ProFormSelect name="filter_ref_path_value" label="Bezugsweg Value" options={['Strecke', 'Lager']} />
              </Col>
            </Row>
          </>
        )}

        <Divider />
        <Row>
          <Col span={24}>
            <ProFormDependency key={'col_ignores_dep'} name={definedColsImported}>
              {(depValues) => {
                const values = Object.values(depValues);
                return (
                  <ProFormSelect
                    showSearch
                    mode="multiple"
                    placeholder="Ignores"
                    options={colOptions.filter((x) => !values.includes(x.value))}
                    formItemProps={{ labelCol: { span: 3 } }}
                    name="col_ignores"
                    label="Ignores"
                    tooltip="Specify columns to be excluded in import"
                  />
                );
              }}
            </ProFormDependency>
          </Col>
        </Row>

        <Row style={{ marginTop: 12, marginBottom: 16 }}>
          <Col offset={3}>
            {/* <Popover
              placement="bottom"
              title="Selection import option"
              trigger="click"
              open={openUploadOptionForm}
              onOpenChange={(visible) => {
                setOpenUploadOptionForm(visible);
              }}
              content={
                <ProForm<OptionFormValueType>
                  formRef={optionFormRef}
                  size="small"
                  initialValues={{ import_option: 'add_new' }}
                  onFinish={async (values) => {
                    formRef.current?.submit();
                    setOpenUploadOptionForm(false);
                    return Promise.resolve(true);
                  }}
                  submitter={{
                    searchConfig: { submitText: 'Import' },
                    render(__, dom) {
                      return [dom[1]];
                    },
                  }}
                >
                  <ProFormRadio.Group
                    name={['import_option']}
                    radioType="radio"
                    fieldProps={{ style: { margin: 0, padding: 0 }, buttonStyle: 'solid' }}
                    formItemProps={{ style: { padding: 0 } }}
                    valueEnum={ImportOption}
                    required
                    rules={[
                      {
                        required: true,
                        message: 'Import option is required',
                      },
                    ]}
                  />
                </ProForm>
              }
            >
              <Button
                type="primary"
                htmlType="button"
                icon={<ImportOutlined />}
                loading={loading}
                disabled={loading}
                onClick={() => setOpenUploadOptionForm(true)}
              >
                Import
              </Button>
            </Popover> */}
            <Popconfirm
              key="import"
              title={<>Are you sure you want to import?</>}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                formRef.current?.submit();
              }}
            >
              <Button type="primary" htmlType="button" icon={<ImportOutlined />} loading={loading} disabled={loading}>
                Import
              </Button>
            </Popconfirm>

            <Button
              type="primary"
              icon={<SaveOutlined />}
              loading={loading}
              disabled={loading}
              ghost
              style={{ marginLeft: 32 }}
              onClick={(e) => handleSaveSetting(true)}
              title="Save current setting as default one in Supplier info"
            >
              Save setting
            </Button>
            <Button
              type="primary"
              icon={<SettingOutlined />}
              loading={loading}
              disabled={loading}
              style={{ marginLeft: 32 }}
              onClick={(e) => handleLoadSettingInSupplier()}
              title="Load supplier setting"
            >
              Load setting
            </Button>
          </Col>
        </Row>

        {/* <Divider />
        ------------------------------------------------------------------------------------
        // Filters below: Will be used to filter xls data
        ------------------------------------------------------------------------------------
        <Row gutter={16}>
          <Col span={8}>
            <ProFormDependency name={['col_ean']}>
              {(depValues) => {
                return (
                  depValues.col_ean && (
                    <ProFormText
                      name={['filters', depValues.col_ean]}
                      label={`EAN (${depValues.col_ean})`}
                      width="sm"
                    />
                  )
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency name={['col_multi_ean']}>
              {(depValues) => {
                return (
                  depValues.col_multi_ean && (
                    <ProFormText
                      name={['filters', depValues.col_multi_ean]}
                      label={`Multi EAN (${depValues.col_multi_ean})`}
                      width="sm"
                    />
                  )
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency name={['col_name']}>
              {(depValues) => {
                return (
                  depValues.col_name && (
                    <ProFormText
                      name={['filters', depValues.col_name]}
                      label={`Name (${depValues.col_name})`}
                      width="sm"
                    />
                  )
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency name={['col_trademark']}>
              {(depValues) => {
                return (
                  depValues.col_trademark && (
                    <ProFormText
                      name={['filters', depValues.col_trademark]}
                      label={`Trademark (${depValues.col_trademark})`}
                      width="sm"
                    />
                  )
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <ProFormDependency name={['col_hs_code']}>
              {(depValues) => {
                return (
                  depValues.col_hs_code && (
                    <ProFormText
                      name={['filters', depValues.col_hs_code]}
                      label={`HS Code (${depValues.col_hs_code})`}
                      width="sm"
                    />
                  )
                );
              }}
            </ProFormDependency>
          </Col>
          <Col span={8}>
            <Space style={{ float: 'right' }}>
              <Button type="primary" loading={loading} onClick={() => actionRef.current?.reload()}>
                Filter Excel
              </Button>
              <Button
                type="default"
                loading={loading}
                onClick={() => {
                  formRef.current?.setFieldsValue({ filters: undefined });
                  actionRef.current?.reload();
                }}
              >
                Reset
              </Button>
            </Space>
          </Col>
        </Row> */}

        {tableEle}
      </Spin>
    </ModalForm>
  );
};

export default ImportExcelData;
