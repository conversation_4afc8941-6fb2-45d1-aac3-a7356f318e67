import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';

import Util, { sn } from '@/util';
import { updateIboPreManagement } from '@/services/foodstore-one/IBO/ibo-pre-management';
import { IboPreStatusOptions } from '../../IboPreList';
import useSupplierOptions from '@/hooks/BasicData/useSupplierOptions';

const handleUpdate = async (id: number, fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    const res = await updateIboPreManagement(id, fields);
    message.success('Updated successfully.');
    return res;
  } catch (error) {
    Util.error(error);
    return null;
  } finally {
    hide();
  }
};

type FormValueType = API.IboPreManagement;

export type UpdateFormProps = {
  initialValues?: API.IboPreManagement;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IboPreManagement) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { initialValues, modalVisible } = props;

  const formRef = useRef<ProFormInstance>();

  const { formElements } = useSupplierOptions(undefined, formRef, { required: true });

  useEffect(() => {
    if (formRef.current && modalVisible) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues, modalVisible]);

  return (
    <ModalForm
      title={`Update Pre IBO Management - #${initialValues?.id}`}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          // order_date: Util.dtToYMD(value.order_date),
          // received_date: Util.dtToYMD(value.received_date),
          id: props.initialValues?.id,
        };
        const res = await handleUpdate(sn(props.initialValues?.id), data);

        if (res) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit({ ...res });
        }
      }}
    >
      {formElements}
      <ProFormSelect name="status" label="Status" options={IboPreStatusOptions} />
      <ProFormText name="note" label="Note" width="md" />
      <ProFormText name="inbound_no" label="InboundNo" width="md" />
      <ProFormText name="note_supplier" label="Notes (Supplier)" width="md" />
      <ProFormTextArea
        name="note_customer"
        label="Notes PreOrder"
        fieldProps={{ maxLength: 255 }}
        help="Max 255 chars."
      />
    </ModalForm>
  );
};

export default UpdateForm;
