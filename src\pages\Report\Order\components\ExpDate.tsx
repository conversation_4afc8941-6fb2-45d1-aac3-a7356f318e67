import Util from '@/util';
import moment from 'moment';

export type ExpDateProps = {
  date?: string;
  booking_date?: string;
};

const ExpDate: React.FC<ExpDateProps> = ({ date, booking_date }) => {
  const mDate = date ? moment(date) : null;
  const days = mDate && mDate.isValid() ? mDate?.diff(moment(), 'days') : 0;
  const months = days / 30;

  let cls = '';
  if (months > 9) {
    cls = 'c-grey';
  } else if (months < 0.5) {
    cls = 'c-red';
  } else if (months >= 0.5 && months <= 2) {
    cls = 'c-orange';
  } else if (months > 2 && months <= 4) {
    cls = 'c-yellow';
  } else if (months > 4 && months <= 9) {
    cls = '';
  }

  let title = mDate ? mDate.fromNow() : '';
  if (booking_date) {
    title += `. Booking. Date: ${Util.dtToDMY(booking_date)}`;
  }
  if (mDate) {
    title += `. Exp. Date: ${Util.dtToDMY(date)}`;
  }

  return mDate ? (
    <span className={cls} title={title}>
      {Util.dtToDMY(date)}
    </span>
  ) : null;
};
export default ExpDate;
