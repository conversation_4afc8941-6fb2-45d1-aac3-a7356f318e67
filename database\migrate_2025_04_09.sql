ALTER TABLE `import`
    ADD COLUMN `is_master` TINYINT NULL COMMENT 'Is a Master file of RWD?' AFTER `is_buying_active`;

ALTER TABLE `import`
    ADD COLUMN `parent_id` BIGINT UNSIGNED NULL COMMENT 'FK: parent ID' AFTER `is_master`,
    ADD CONSTRAINT `FK_import_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `import` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE `offer`
    CHANGE `ibo_status` `ibo_status` ENUM (
        'open',
        'expecting items',
        'closed',
        'in discussion',
        'in preparation',
        'paid',
        'closed lost',
        'waiting for loading',
        'shipped'
        ) CHARSET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'open' NULL COMMENT 'Status for IBO. open,expecting items,closed';


