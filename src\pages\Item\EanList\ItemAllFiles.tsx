import { <PERSON><PERSON>, <PERSON>, <PERSON>, Popover, Row, Space } from 'antd';
import { message, Typography } from 'antd';
import React, { useState, useRef, useEffect, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import SDatePicker from '@/components/SDatePicker';

import Util, { nf2, sEllipsed, skuToItemId, sn, sShortImportDbTableName } from '@/util';
import { getAllItemFilesList, ItemInFileType } from '@/services/foodstore-one/Item/ean';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from '@/constants';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import <PERSON>Form, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
import { IRouteComponentProps, useLocation, useModel } from 'umi';
import usePageContainerTitle from './hooks/usePageContainerTitle';
import useEanSpecialFilter from './hooks/useEanSpecialFilter';
import { LinkOutlined, ReconciliationOutlined } from '@ant-design/icons';
import { updateEanXlsData } from '@/services/foodstore-one/Import/import';
import { calcCheapestXlsPrice } from './EanAllPrices';
import SProFormDigit from '@/components/SProFormDigit';

type SearchFormValueType = Partial<API.Ean>;

export type EanSummaryComponentProps = IRouteComponentProps & {
  eanType?: 'default' | 'base' | 've';
};

const ItemAllFiles: React.FC<EanSummaryComponentProps> = (eanComponentProps) => {
  const eanTypeProp = eanComponentProps.eanType || 'default';
  const { getDictByCode } = useModel('app-settings');

  const [loading, setLoading] = useState<boolean>(false);

  const [xlsImports, setXlsImports] = useState<API.Import[]>([]);

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  // datasource for inline editing
  const [datasource, setDatasource] = useState<ItemInFileType[]>([]);

  const location: any = useLocation();

  const [columns, setColumns] = useState<ProColumns<ItemInFileType>[]>([]);

  // Percentage
  const [percent, setPercent] = useState<number | null>();
  useEffect(() => {
    setPercent(getDictByCode(DictCode.OFFER_DEFAULT_PERCENTAGE));
  }, [getDictByCode]);

  const orgColumns: ProColumns<ItemInFileType>[] = useMemo(
    () => [
      {
        title: 'EAN',
        dataIndex: ['ean'],
        copyable: true,
        hideInSearch: true,
        width: 130,
        render: (dom, record) => {
          return dom;
        },
      },
      {
        title: 'Multi EAN',
        dataIndex: 'multi_ean',
        hideInSearch: true,
        width: 130,
        render: (dom, record) => {
          return record.item_ean?.siblings_multi ? record.item_ean?.siblings_multi?.map((x) => x.ean).join(', ') : null;
        },
      },
      {
        title: 'HS Code',
        dataIndex: 'hs_code',
        hideInSearch: true,
        width: 90,
      },
      {
        title: 'Article No.',
        dataIndex: 'article_no',
        hideInSearch: true,
        width: 90,
      },

      /* {
        title: 'Qty/case',
        dataIndex: 'attr_case_qty',
        valueType: 'digit',
        sorter: true,
        align: 'right',
        hideInSearch: true,
        width: 60,
        render: (dom, record) => Util.numberFormat(record.case_qty),
      }, */
      {
        title: 'SKU',
        dataIndex: 'sku',
        ellipsis: true,
        hideInSearch: true,
        width: 130,
        render: (__, record) => {
          const itemEan = record.item_ean;
          return (
            !!itemEan && (
              <Row>
                <Col flex="auto">
                  <Typography.Link
                    href={`/item/ean-all-summary?sku=${skuToItemId(itemEan.sku)}_`}
                    target="_blank"
                    copyable
                  >
                    {itemEan.sku}
                  </Typography.Link>
                </Col>
                <Col flex="16px">
                  <a
                    href={`/item/ean-all-prices?sku=${skuToItemId(itemEan.sku)}_`}
                    target="_blank"
                    title="Open EAN price page on new tab."
                    className="text-sm"
                    rel="noreferrer"
                  >
                    <LinkOutlined />
                  </a>
                </Col>
              </Row>
            )
          );
        },
      },
      {
        title: 'Created on',
        sorter: true,
        dataIndex: 'created_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        width: 100,
        render: (dom, record) => Util.dtToDMYHHMM(record.item_ean?.created_on),
      },
      {
        title: 'Deactivated On',
        sorter: true,
        dataIndex: ['ean_price_stable', 'deleted_on'],
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        width: 100,
        render: (__, record) =>
          record.ean_price_stable?.is_deleted ? Util.dtToDMYHHMM(record.ean_price_stable?.deleted_on) : null,
      },
      {
        title: 'Activated On',
        sorter: true,
        dataIndex: ['ean_price_stable', 'updated_on'],
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        width: 100,
        render: (__, record) =>
          record.ean_price_stable && !record.ean_price_stable.is_deleted
            ? Util.dtToDMYHHMM(record.ean_price_stable?.updated_on)
            : null,
      },
      {
        title: 'Name DE',
        dataIndex: ['name'],
        width: 320,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        render: (dom, record, index) => {
          const defaultValue = record?.name;
          return defaultValue;
        },
      },
      {
        title: 'GFC Price (€)',
        dataIndex: ['price_percentage'],
        tooltip: 'GFC price * Percentage',
        width: 70,
        align: 'right',
        render: (__, record) => {
          const gfcPrice = sn(record?.item_ean?.ean_price_gfc?.price);
          const stablePrice =
            record.item_ean?.ean_price_stable?.is_deleted == 0 ? sn(record.item_ean?.ean_price_stable?.cur_price) : 0;

          return (
            <Popover
              content={
                <table style={{ textAlign: 'left' }} cellPadding={'2'}>
                  <tr>
                    <td style={{ width: 80 }}>GFC</td>
                    <td style={{ width: 60 }} className="text-right">
                      {nf2(gfcPrice)}
                    </td>
                    <td></td>
                  </tr>
                  <tr>
                    <td className="bg-yellow3">Online</td>
                    <td className="bg-yellow3 text-right">{nf2((gfcPrice * sn(percent)) / 100)}</td>
                    <td className="bg-yellow3"></td>
                  </tr>
                  <tr>
                    <td>{gfcPrice ? nf2((100 * (stablePrice * 1.14 - gfcPrice)) / gfcPrice) + '%' : ''}</td>
                    <td className="text-right">{nf2(stablePrice * 1.14)}</td>
                    <td>(114%)</td>
                  </tr>
                  <tr>
                    <td>{gfcPrice ? nf2((100 * (stablePrice * 1.08 - gfcPrice)) / gfcPrice) + '%' : ''}</td>
                    <td className="text-right">{nf2(stablePrice * 1.08)}</td>
                    <td>(108%)</td>
                  </tr>
                  <tr>
                    <td>{gfcPrice ? nf2((100 * (stablePrice * 1.02 - gfcPrice)) / gfcPrice) + '%' : ''}</td>
                    <td className="text-right">{nf2(stablePrice * 1.02)}</td>
                    <td>(102%)</td>
                  </tr>
                  <tr>
                    <td colSpan={4}>&nbsp;</td>
                  </tr>
                  <tr>
                    <td>Price Stable</td>
                    <td>{nf2(stablePrice)}</td>
                    <td></td>
                  </tr>
                </table>
              }
              trigger="hover"
            >
              <div style={{ minHeight: 16 }}>{nf2((gfcPrice * sn(percent)) / 100)}</div>
            </Popover>
          );
        },
      },
      {
        title: 'PriceStable',
        dataIndex: ['ean_price_stable', 'cur_import'],
        width: 160,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        render: (__, record, index) => {
          const curImport = record.ean_price_stable?.cur_import;
          if (curImport) {
            return (
              <Row wrap={false}>
                <Col flex="auto">
                  <Typography.Text ellipsis>
                    {`${curImport.supplier?.name} | ${
                      curImport.supplier_add
                        ? curImport.supplier_add
                        : sShortImportDbTableName(curImport.table_name, true)
                    }`}
                  </Typography.Text>
                </Col>
                <Col flex="70px" className="text-right">
                  {nf2(record.ean_price_stable?.cur_price)}
                </Col>
              </Row>
            );
          }
          return null;
        },
      },
      {
        title: 'Cur. Suppliers',
        dataIndex: ['import'],
        tooltip: 'Hover: show in tables',
        width: 160,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        render: (__, record: any, index) => {
          const curImport = record.ean_price_stable?.cur_import;

          const curImportNames = xlsImports
            ?.filter((x, ind) => {
              const price = record?.[`xls_bp_${x.id}`] ?? 0;
              return price > 0;
            })
            ?.map((x, ind) => (
              <Row wrap={false} key={x.id} className={`${curImport.id == x.id ? '' : ' c-grey'}`}>
                <Col flex="auto">
                  <Typography.Text ellipsis>
                    <span className={`${curImport.id == x.id ? '' : ' c-grey'}`}>
                      {`${x.supplier?.name} | ${
                        x.supplier_add ? x.supplier_add : sShortImportDbTableName(x.table_name, true)
                      }`}
                    </span>
                  </Typography.Text>
                </Col>
                <Col flex="70px" className="text-right">
                  {nf2(record?.[`xls_bp_${x.id}`] ?? 0)}
                </Col>
              </Row>
            ));

          return (
            <Popover
              title={
                <>
                  <Row gutter={16}>
                    <Col>Price Details</Col>
                    <Col>{sEllipsed(record.name, 50, 10)}</Col>
                    {!!record.item_ean && (
                      <Col>
                        <Typography.Link
                          href={`/item/ean-all-summary?sku=${skuToItemId(record.item_ean.sku)}_`}
                          target="_blank"
                          copyable
                        >
                          {record.item_ean.sku}
                        </Typography.Link>
                      </Col>
                    )}
                    {!!record.item_ean && (
                      <Col flex="30px">
                        <a
                          href={`/item/ean-all-prices?sku=${skuToItemId(record.item_ean.sku)}_`}
                          target="_blank"
                          title="Open EAN price page on new tab."
                          className="text-sm"
                          rel="noreferrer"
                        >
                          <LinkOutlined />
                        </a>
                      </Col>
                    )}
                  </Row>
                </>
              }
              content={() => {
                const xlsColumns: ProColumns<API.ZImportSupplierXlsEan>[] = xlsImports?.map((x, ind) => {
                  return {
                    title: (
                      <>
                        <div style={{ textAlign: 'left' }}>{x.supplier?.name}</div>
                        <div style={{ textAlign: 'left', fontSize: 10 }}>
                          {x.supplier_add ? x.supplier_add : sShortImportDbTableName(x.table_name, true)}
                        </div>
                      </>
                    ),
                    dataIndex: [`xls_bp_${x.id}`],
                    sorter: false,
                    align: 'right',
                    width: 80,
                    className: ind == 0 ? 'bl2' : '',
                    render: (___, r) => {
                      const price = r[`xls_bp_${x.id}`] ?? 0;

                      return (
                        <>
                          <div style={{ lineHeight: 1.5, minHeight: 12 }}>{nf2(price)}</div>
                        </>
                      );
                    },
                    onCell: (r) => {
                      const price = r[`xls_bp_${x.id}`] ?? 0;

                      const arr = calcCheapestXlsPrice(r ?? ({} as any), xlsImports);
                      const cheapestPrice = arr[0];
                      let cls = '';
                      if (cheapestPrice == price && price > 0) {
                        cls = 'bg-green3';
                      }
                      return {
                        className: cls,
                      };
                    },
                  } as ProColumnType<any>;
                });

                return (
                  <ProTable<ItemInFileType, API.PageParams>
                    cardProps={{ bodyStyle: { padding: 0 } }}
                    bordered
                    columns={[
                      {
                        title: 'Price Stable',
                        dataIndex: ['ean_price_stable', 'cur_import', 'supplier'],
                        width: 60,
                        align: 'right',
                        tooltip: 'Price Stable & Supplier',
                        className: 'bl2',
                        render(__, recordIn) {
                          const priceStable = record.ean_price_stable;
                          return priceStable ? (
                            <Space
                              style={{ lineHeight: 1 }}
                              direction="vertical"
                              size={6}
                              title={
                                priceStable.cur_import
                                  ? `${priceStable.cur_import_id} | ${priceStable.cur_import?.table_name}`
                                  : ''
                              }
                            >
                              <div className="c-green-dark">{nf2(priceStable?.cur_price)}</div>
                              <div className="c-grey text-sm">
                                &nbsp;
                                {priceStable.cur_import?.supplier_add ? '' : priceStable.cur_import?.supplier?.name}
                              </div>
                            </Space>
                          ) : null;
                        },
                      },
                      {
                        title: 'SUPPLIER_ADD',
                        dataIndex: ['ean_price_stable', 'cur_import', 'supplier_add'],
                        width: 60,
                        render(__, recordIn) {
                          return `${curImport.supplier?.name} | ${
                            curImport.supplier_add
                              ? curImport.supplier_add
                              : sShortImportDbTableName(curImport.table_name, true)
                          }`;
                        },
                      },
                      ...xlsColumns,
                    ]}
                    style={{ width: 900 }}
                    rowKey="item_id"
                    headerTitle={false}
                    search={false}
                    options={false}
                    pagination={false}
                    size="small"
                    dataSource={[record]}
                    columnEmptyText={''}
                  />
                );
              }}
            >
              {curImportNames}
            </Popover>
          );
        },
      },
      {
        dataIndex: 'option',
        valueType: 'option',
      },

      /* {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 110,
        shouldCellUpdate(record, prevRecord) {
          return false;
        },
        render: (dom, record, index) => {
          const options = [
            <a
              key="update-item"
              title="Update item"
              onClick={() => {
                handleUpdateItemModalVisible(true);
                setCurrentRow({ ...record });
              }}
            >
              <SnippetsOutlined className="btn-gray" />
            </a>,
            <a
              key="texts"
              title="Update texts"
              onClick={() => {
                handleUpdateTextsModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <FileTextOutlined />
            </a>,
            <a
              key="config"
              title="Update attributes"
              onClick={() => {
                handleUpdateModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <EditTwoTone />
            </a>,
            <a
              key="files"
              title="Update pictures"
              onClick={() => {
                handleUpdatePicturesModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <PictureOutlined />
            </a>,
            <Popconfirm
              key="upsync"
              placement="topRight"
              title={
                <>
                  Are you sure you want to up sync the EAN？ <br />
                  <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
                </>
              }
              overlayStyle={{ width: 350 }}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                if (!record.id) return;
                handleUpSync(record.id);
              }}
            >
              <CloudUploadOutlined className="btn-gray" />
            </Popconfirm>,
          ];
          return <Space>{options.map((option) => option)}</Space>;
        },
      }, */
    ],
    [getDictByCode, xlsImports, percent],
  );

  useEffect(() => {
    setColumns(orgColumns);
  }, [orgColumns]);

  const mergeColumns: any = (columns ?? []).map((col, index) => ({
    ...col,
  }));

  const { pageTitle } = usePageContainerTitle(eanComponentProps.route);

  const { filterId: filter_id, renderedEle } = useEanSpecialFilter(loading, 'all');

  useEffect(() => {
    actionRef.current?.reload();
  }, [filter_id]);

  return (
    <PageContainer
      className={styles.eanListContainer}
      title={
        <>
          {pageTitle}
          {renderedEle}
        </>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues(
            'sf_item_all_files',
            {
              status: 1,
              min_stock_qty: -999,
            },
            { sku: location.query?.sku ?? undefined },
          )}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { disabled: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Button
                  key="update_ean_xls_all"
                  type="primary"
                  icon={<ReconciliationOutlined />}
                  style={{ marginLeft: 16 }}
                  className="btn-green"
                  title="Update all ean table."
                  onClick={() => {
                    const hide = message.loading('Updating the data...', 0);
                    updateEanXlsData()
                      .then((res) => {
                        message.success('Updated successfully.');
                      })
                      .catch(Util.error)
                      .finally(() => hide());
                  }}
                >
                  Update EAN_XLS All
                </Button>,
              ];
            },
          }}
        >
          <ProFormText name={'name2'} label="Name" width={180} placeholder={'Search by keywords'} />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormSelect
            name="sys_exists_mode"
            label="Exists?"
            options={[
              { value: 1, label: 'Exists in Sys?' },
              { value: 2, label: 'Not Exists in Sys?' },
            ]}
          />
          <ProFormGroup size="small">
            <SDatePicker name="created_on_start" label="Date of creation" />
            <SDatePicker name="created_on_end" addonBefore="~" />
          </ProFormGroup>
        </ProForm>
      </Card>

      <ProTable<ItemInFileType, API.PageParams>
        headerTitle={'Item All Files'}
        actionRef={actionRef}
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        dataSource={datasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_item_all_files_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
        search={false}
        toolBarRender={() => [
          <div key="percent">
            <SProFormDigit
              width={70}
              fieldProps={{
                precision: 2,
                value: percent,
                onChange(value) {
                  setPercent(sn(value));
                },
              }}
              addonAfter="%"
              formItemProps={{ style: { marginBottom: 0 } }}
            />
          </div>,
        ]}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_item_all_files', searchFormValues);
          Util.setSfValues('sf_item_all_files_p', params);

          setLoading(true);
          return getAllItemFilesList(
            {
              ...params,
              ...searchFormValues,
              trademarks: [searchFormValues.trademark?.value],
              ean_type: eanTypeProp || 'base',
              special_filter_id: filter_id,
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);
              /* // Update the selected row data which should be valid for modal navigation
              if (currentRow?.uid && res.data.length) {
                setCurrentRow(res.data.find((x: ItemInFileType) => x.uid == currentRow.uid));
              } */

              setXlsImports(res.imports || []);

              /* // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              } */
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={mergeColumns}
        tableAlertRender={false}
      />
    </PageContainer>
  );
};

export default ItemAllFiles;
