import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import {
  deleteOrderShippingProvider,
  getOrderShippingProviderList,
  updateOrderShippingProvider,
} from '@/services/foodstore-one/BasicData/order-shipping-provider';
import Util, { sn } from '@/util';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Popconfirm, message } from 'antd';
import { useRef, useState } from 'react';
import CreateOrderShippingProviderForm from './components/CreateOrderShippingProviderForm';
import EditableCell from '@/pages/Item/EanList/EditableCell';

const OrderShippingProviderList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);

  const columns: ProColumns<API.ShippingProvider>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      render(dom, record) {
        return (
          <EditableCell
            dataType="text"
            defaultValue={record.name || ''}
            style={{ marginRight: 0 }}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateOrderShippingProvider(sn(record.id), {
                name: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {dom}
          </EditableCell>
        );
      },
    },
    {
      dataIndex: 'options',
      valueType: 'option',
      width: 40,
      render(dom, record) {
        return (
          <Popconfirm
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              if (record.id) {
                await deleteOrderShippingProvider(record.id);
                actionRef.current?.reloadAndRest?.();
              }
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />} size="small" />
          </Popconfirm>
        );
      },
    },
  ];
  return (
    <>
      <ProTable<API.ShippingProvider, API.PageParams>
        headerTitle={'Shipping Providers List'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            size="small"
            onClick={() => {
              setOpenCreateModal(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={getOrderShippingProviderList}
        onRequestError={Util.error}
        columns={columns}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
      />

      <CreateOrderShippingProviderForm
        modalVisible={openCreateModal}
        handleModalVisible={setOpenCreateModal}
        onSubmit={async (value) => actionRef.current?.reload()}
      />
    </>
  );
};
export default OrderShippingProviderList;
