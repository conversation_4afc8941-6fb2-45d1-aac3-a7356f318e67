import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import {
  deleteOrderShippingDescProviderMap,
  getOrderShippingDescProviderMapList,
  importOrderShippingDescProviderMap,
  updateOrderShippingDescProviderMap,
} from '@/services/foodstore-one/BasicData/order-shipping-desc-provider-map';
import Util, { ni, sn } from '@/util';
import { DeleteOutlined, ImportOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Popconfirm, message } from 'antd';
import { useRef, useState } from 'react';
import CreateOrderShippingDescProviderFormMap from './components/CreateOrderShippingDescProviderMapForm';
import EditableCell from '@/pages/Item/EanList/EditableCell';
import { getOrderShippingProviderList } from '@/services/foodstore-one/BasicData/order-shipping-provider';

const OrderShippingDescProviderMapList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);

  const columns: ProColumns<API.ShippingDescProvider>[] = [
    {
      title: 'Shipping Description',
      dataIndex: 'shipping_desc',
      sorter: true,
      tooltip: "40 characters of order's shipping description.",
      render(dom, record) {
        return (
          <EditableCell
            dataType="text"
            defaultValue={record.shipping_desc || ''}
            style={{ marginRight: 0 }}
            fieldProps={{ maxLength: 40 }}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateOrderShippingDescProviderMap(sn(record.id), {
                shipping_desc: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {dom}
          </EditableCell>
        );
      },
    },
    {
      title: 'Orders Qty',
      dataIndex: 'orders_cnt',
      sorter: true,
      align: 'right',
      width: 80,
      render(dom, record) {
        return <span style={{ paddingRight: 12 }}>{ni(record.orders_cnt)}</span>;
      },
    },
    {
      title: 'Provider Name',
      dataIndex: 'provider_name',
      sorter: true,
      render(dom, record) {
        return (
          <EditableCell
            dataType="select"
            defaultValue={record.provider_name || ''}
            style={{ marginRight: 0 }}
            fieldProps={{ style: { lineHeight: 1 } }}
            request={async (params) => {
              return getOrderShippingProviderList(params, {}, {}).then((res) =>
                res.data.map((x) => ({ value: x.name, lable: x.name })),
              );
            }}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              if (!newValue && !record.id) {
                cancelEdit?.();
                return;
              }
              return updateOrderShippingDescProviderMap(sn(record.id), {
                provider_name: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {dom}
          </EditableCell>
        );
      },
    },

    {
      dataIndex: 'options',
      valueType: 'option',
      width: 40,
      render(dom, record) {
        return (
          <Popconfirm
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              if (record.id) {
                await deleteOrderShippingDescProviderMap(record.id);
                actionRef.current?.reloadAndRest?.();
              }
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />} size="small" />
          </Popconfirm>
        );
      },
    },
  ];
  return (
    <>
      <ProTable<API.ShippingDescProvider, API.PageParams>
        headerTitle={'Provider Mapping'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        search={false}
        toolBarRender={() => [
          <Button
            key="import"
            type="primary"
            size="small"
            onClick={() => {
              const hide = message.loading('Importing 40 chars of shipping description from orders...', 0);
              importOrderShippingDescProviderMap()
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            <ImportOutlined /> Import
          </Button>,
          <Button
            key="new"
            type="primary"
            size="small"
            onClick={() => {
              setOpenCreateModal(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={(params, sort, filter) =>
          getOrderShippingDescProviderMapList({ ...params, with: 'orders_cnt' }, sort, filter)
        }
        onRequestError={Util.error}
        columns={columns}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
      />

      <CreateOrderShippingDescProviderFormMap
        modalVisible={openCreateModal}
        handleModalVisible={setOpenCreateModal}
        onSubmit={async (value) => actionRef.current?.reload()}
      />
    </>
  );
};
export default OrderShippingDescProviderMapList;
