
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { ShippingServiceNameType } from '@/constants';

const urlPrefix = '/api/magento-data/order-parcel';

/** 
 * 
 * GET /api/magento-data/order-parcel */
export async function getOrderParcelList(params: API.PageParams, sort?: any, filter?: any) {
    return request<API.ResultList<API.OrderParcel>>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
        pagination: res.message.pagination, // For total row pagination hack.
    }));
}

/** 
 * 
 * GET /api/magento-data/order-parcel/check */
export async function checkParcelStatus(params: {
    parcel_no?: string;
    order_id?: number;
    service_name?: ShippingServiceNameType;
}) {
    return request<API.ResultObject<API.OrderParcelLog[]>>(`${urlPrefix}/check`, {
        method: 'GET',
        params: {
            ...params,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => res.message);
}


