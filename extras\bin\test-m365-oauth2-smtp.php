<?php



require __DIR__ . '/../../src/App/App.php';

/** @var \Slim\Container $container */


$accessToken = \App\Service\Email\Email\EmailService::getM365Token();

$emailData = [
    "message" => [
        "subject" => "Test email!!!",
        "body" => [
            "contentType" => "Text",
            "content" => "Hello, this is a test email sent via Microsoft Graph!"
        ],
        "toRecipients" => [
            [
                "emailAddress" => [
                    "address" => "<EMAIL>"
                ]
            ]
        ]
    ]
];

$ch = curl_init('https://graph.microsoft.com/v1.0/me/sendMail');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $accessToken,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($emailData));

$response = curl_exec($ch);
if (curl_errno($ch)) {
    echo 'Error: ' . curl_error($ch);
} else {
    echo 'Email sent successfully!';
}
curl_close($ch);
