import {
  DEFAULT_PER_PAGE_PAGINATION,
  DictCode,
  ItemEANStatus,
  ItemEANStatusOptions,
  ProductWebsiteShortCode,
} from '@/constants';
import { getEanList } from '@/services/foodstore-one/Item/ean';
import Util, { nf2, sn, sUrlByTpl } from '@/util';
import { CheckCircleOutlined, CloseOutlined, SnippetsOutlined } from '@ant-design/icons';
import ProForm, { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { Card, Space } from 'antd';
import { useMemo, useRef, useState } from 'react';
import { IRouteComponentProps, useModel } from 'umi';
import WebsiteIcons from './components/WebsiteIcons';
import EanFilesComp from '@/components/EanFilesComp';
import * as UpdateItemForm from '../ItemList/components/UpdateForm';
import SkuComp from '@/components/SkuComp';

export type SearchFormValueType = Partial<API.Ean>;

export type EanAllBioComponentProps = IRouteComponentProps;

const EanAllBio: React.FC<EanAllBioComponentProps> = (eanComponentProps) => {
  const location = eanComponentProps.location;

  const { appSettings, getDictByCode } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const [selectedRows, setSelectedRows] = useState<API.Ean[]>([]);

  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);

  const columns: ProColumns<API.Ean>[] = useMemo(
    () => [
      {
        title: 'Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        align: 'center',
        ellipsis: true,
        width: 50,
        showSorterTooltip: false,
        valueEnum: ItemEANStatusOptions as any,
        render: (__, record) => {
          let ele = null;
          if (record.status == ItemEANStatus.ACTIVE) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          } else {
            ele = <CloseOutlined style={{ color: 'gray' }} />;
          }
          return ele;
        },
      },
      {
        title: 'Shops',
        dataIndex: 'product_websites',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        width: 50,
        showSorterTooltip: false,
        render: (__, record) => (
          <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
        ),
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 80,
        render: (dom, record) => <EanFilesComp files={record.files} />,
      },
      {
        title: 'Item ID',
        dataIndex: 'item_id',
        fixed: 'left',
        sorter: true,
        ellipsis: true,
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: 'Name DE',
        dataIndex: ['ean_text_de', 'name'],
        width: 280,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        fixed: 'left',
        tooltip: 'Orange color indicates the inherited value from its item.',
      },
      {
        title: 'BIO Cert',
        dataIndex: ['item', 'fs_bio_certificate'],
        copyable: true,
        hideInSearch: true,
        width: 120,
      },
      {
        title: 'BIO Origin',
        dataIndex: ['item', 'fs_bio_origin'],
        copyable: true,
        hideInSearch: true,
        width: 160,
      },
      {
        title: 'EAN',
        dataIndex: 'ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
      },
      {
        title: 'Single EAN',
        dataIndex: ['parent', 'ean'],
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
      },
      {
        title: 'Qty/case',
        dataIndex: 'attr_case_qty',
        valueType: 'digit',
        sorter: true,
        align: 'right',
        hideInSearch: true,
        width: 60,
        render: (dom, record) => Util.numberFormat(record.attr_case_qty),
      },
      {
        title: 'SKU',
        dataIndex: 'sku',
        sorter: true,
        ellipsis: true,
        hideInSearch: true,
        width: 100,
        render: (dom, record) => {
          return <SkuComp sku={record.sku} />;
        },
      },
      {
        title: 'VAT',
        dataIndex: ['item', 'vat', 'value'],
        sorter: false,
        width: 50,
        ellipsis: true,
        align: 'right',
        hideInSearch: true,
        render: (__, record) =>
          sn(record?.item?.vat?.value) > 0 ? `${nf2(record?.item?.vat?.value, false, true)}%` : '',
      },

      {
        title: 'BIO cert',
        dataIndex: ['item', 'fs_bio_certificate'],
        sorter: false,
        width: 130,
        ellipsis: true,
      },
      {
        title: 'BIO Origin',
        dataIndex: ['item', 'fs_bio_origin'],
        sorter: false,
        width: 130,
        ellipsis: true,
      },
      {
        title: 'Option',
        valueType: 'option',
        fixed: 'right',
        render: (__, record, index) => {
          const options = [
            <a
              key="update-item"
              title="Update item"
              onClick={() => {
                handleUpdateItemModalVisible(true);
                setCurrentRow({ ...record });
              }}
            >
              <SnippetsOutlined className="btn-gray" />
            </a>,
          ];
          return <Space>{options.map((option) => option)}</Space>;
        },
      },
    ],
    [getDictByCode],
  );

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues(
            'sf_ean_all_bio',
            {
              status: 1,
              product_websites: [ProductWebsiteShortCode[1]],
            },
            { sku: location.query?.sku ?? undefined },
          )}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormSelect
            name="product_websites"
            label="Websites"
            width={160}
            mode="multiple"
            placeholder={'Websites'}
            options={appSettings.storeWebsites
              ?.filter((x) => x.code != 'admin')
              ?.map((x) => ({
                value: `${x.id}`,
                label: x.name,
              }))}
          />
          <ProFormText name={'fs_bio_certificate'} label="BIO Cert" width={150} placeholder={'BIO Certificate'} />
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
        </ProForm>
      </Card>

      <ProTable<API.Ean, API.PageParams>
        headerTitle={'EANs List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_all_bio_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_all_bio', searchFormValues);
          Util.setSfValues('sf_ean_all_bio_p', params);

          return getEanList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              bio_only: 1,
              with: 'item,vats,trademarks,eanTextDe,parentTexts,prices,files,gdsnItem',
            },
            sort,
            filter,
          )
            .then((res) => {
              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Ean) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally();
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        /* rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }} */
      />

      <UpdateItemForm.default
        modalVisible={updateItemModalVisible}
        handleModalVisible={handleUpdateItemModalVisible}
        initialValues={{
          ...currentRow?.item,
          eanId: currentRow?.id,
          ean: currentRow?.parent?.ean,
          sku: currentRow?.parent?.sku,
        }}
        // handleNavigation={handleNavigation}
        onSubmit={async (value) => {
          setCurrentRow((prev) => ({ ...prev, item: { ...prev?.item, ...value } }));
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateItemModalVisible(false);
        }}
        gdsn
      />
    </PageContainer>
  );
};

export default EanAllBio;
