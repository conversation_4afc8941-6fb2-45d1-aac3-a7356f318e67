import { useCallback, useEffect, useState } from 'react';
import { getChildrenInFolder, getFileUrlInBrowser } from '@/services/foodstore-one/FolderViewer/folder-viewer';
import Util, { isImageFile, isPdfFile } from '@/util';
import { PageContainer } from '@ant-design/pro-layout';

import { Col, Row, Spin, Image, Card } from 'antd';
import type { FileActionHandler, FileArray, FileData } from 'chonky';
import {
  ChonkyActions,
  FileBrowser,
  FileContextMenu,
  FileHelper,
  FileList,
  FileNavbar,
  FileToolbar,
  setChonkyDefaults,
} from 'chonky';
import { ChonkyIconFA } from 'chonky-icon-fontawesome';
setChonkyDefaults({ iconComponent: ChonkyIconFA });

import { Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';

// import '@react-pdf-viewer/default-layout/lib/styles/index.css';

import { toolbarPlugin } from '@react-pdf-viewer/toolbar';
import '@react-pdf-viewer/toolbar/lib/styles/index.css';

import type { ToolbarSlot, TransformToolbarSlot } from '@react-pdf-viewer/toolbar';

import { SelectionMode } from '@react-pdf-viewer/selection-mode';

const RootFmFile = { id: '!', name: 'Root', isDir: true };

const FolderViewer: React.FC = () => {
  const [resourceLoading, setResourceLoading] = useState(false);

  const [files, setFiles] = useState<FileArray>();
  const [folderChain, setFolderChain] = useState<FileArray>([RootFmFile]);
  const [currentFmFolder, setCurrentFmFolder] = useState<API.FmFile>();
  const [selectedFmFile, setSelectedFmFile] = useState<API.FmFile>();

  const handleGetFiles = async (id?: string, options?: Record<string, any>) => {
    setResourceLoading(true);
    return getChildrenInFolder({ id, ...options })
      .then((res: any) => {
        setFiles(res.data);
        setFolderChain(res.folderChain);
        return res;
      })
      .catch((error) => {
        Util.error(error);
      })
      .finally(() => setResourceLoading(false));
  };

  useEffect(() => {
    handleGetFiles();
  }, []);

  useEffect(() => {
    if (typeof currentFmFolder?.id !== 'undefined') handleGetFiles(currentFmFolder?.id);
  }, [currentFmFolder?.id]);

  const handleFileItemAction = useCallback<FileActionHandler>((data) => {
    console.log('-->[File action data]:', data);

    if (data.id === ChonkyActions.ChangeSelection.id) {
      if (data.state.selectedFiles.length) setCurrentFmFolder(data.state.selectedFiles[0]);
    } else if (data.id === ChonkyActions.MouseClickFile.id) {
      if (!data.payload.file.isDir) {
        setSelectedFmFile(data.payload.file);
      }
    } else if (data.id === ChonkyActions.OpenFiles.id) {
      // eslint-disable-next-line @typescript-eslint/no-shadow
      const { targetFile, files } = data.payload;
      const fileToOpen = targetFile ?? files[0];

      if (fileToOpen && FileHelper.isDirectory(fileToOpen)) {
        setCurrentFmFolder(fileToOpen);
      }
    } else if (data.id === ChonkyActions.OpenSelection.id) {
      const selectedFiles: API.FmFile[] = data.state.selectedFiles;
    }
  }, []);

  // const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const defaultLayoutPluginInstance = toolbarPlugin({
    searchPlugin: {
      keyword: 'PDF',
    },
    selectionModePlugin: {
      selectionMode: SelectionMode.Hand,
    },
    fullScreenPlugin: {},
    openPlugin: {},
  });

  const { Toolbar, renderDefaultToolbar } = defaultLayoutPluginInstance;

  const transform: TransformToolbarSlot = (slot: ToolbarSlot) => ({
    ...slot,
    // These slots will be empty
    Download: () => <></>,
    Open: () => <></>,
    // EnterFullScreen: () => <></>,
    SwitchTheme: () => <></>,
  });

  return (
    <PageContainer>
      <Row gutter={24}>
        <Col span={14} style={{ paddingBottom: 32 }}>
          <Worker workerUrl={`${(window as any).routerBase}pdf.worker.min.js`}>
            {selectedFmFile?.id && !selectedFmFile?.isDir && isPdfFile(selectedFmFile?.name || '') && (
              <div
                style={{
                  height: '750px',
                  width: '100%',
                  marginLeft: 'auto',
                  marginRight: 'auto',
                }}
              >
                <Toolbar>{renderDefaultToolbar(transform)}</Toolbar>
                <Viewer fileUrl={getFileUrlInBrowser(selectedFmFile?.id)} plugins={[defaultLayoutPluginInstance]} />
              </div>
            )}
          </Worker>
          {selectedFmFile?.id && !selectedFmFile?.isDir && isImageFile(selectedFmFile?.name || '') && (
            <Image src={getFileUrlInBrowser(selectedFmFile?.id)} />
          )}
        </Col>
        <Col span={10}>
          <Spin spinning={resourceLoading}>
            <div style={{ height: 500 }}>
              <FileBrowser
                files={files as any}
                folderChain={folderChain}
                disableSelection
                fileActions={[
                  ChonkyActions.OpenSelection,
                  // ChonkyActions.DownloadFiles,
                  // ChonkyActions.DeleteFiles,
                ]}
                onFileAction={handleFileItemAction}
                disableDragAndDrop
                clearSelectionOnOutsideClick
                defaultFileViewActionId={ChonkyActions.EnableListView.id}
                /* iconComponent={(props) => {
                  return <ChonkyIconFA {...props} style={{ ...props.style, width: 50 }} />;
                }} */
              >
                <FileNavbar />
                <FileToolbar />
                <FileList />
                <FileContextMenu />
              </FileBrowser>
            </div>
          </Spin>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default FolderViewer;
