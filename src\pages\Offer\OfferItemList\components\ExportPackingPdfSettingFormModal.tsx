import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSwitch } from '@ant-design/pro-form';
import { Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useMemo, useRef, useState } from 'react';
import Util from '@/util';
import { exportOfferItemList } from '@/services/foodstore-one/Offer/offer-item';
import useWaNoOptions from '@/hooks/BasicData/useWaNoOptions';

export type ExportPackingPdfFormValueType = {
  inc_footer_address?: boolean;
  inc_footer_logo?: boolean;
  inc_footer_page_no?: boolean;
  inc_footer_truck?: boolean;
  inc_ibo_pre?: boolean;
};

export type ExportPackingPdfSettingFormModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  offer?: API.Offer;
  onSubmit?: (formData: ExportPackingPdfFormValueType) => Promise<boolean | void>;
  // parent search form
  getParentParams: () => any;
};

const ExportPackingPdfSettingFormModal: React.FC<ExportPackingPdfSettingFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<ExportPackingPdfFormValueType>>();

  const { modalVisible, handleModalVisible, offer } = props;

  const [loading, setLoading] = useState(false);

  // WA No list from offer_item_shipped table.
  const useWaNoOptionsParams = useMemo(() => {
    return { offer_id: offer?.id };
  }, []);
  const { formElements: formElementsWaNo } = useWaNoOptions(useWaNoOptionsParams, formRef);

  return (
    <ModalForm<ExportPackingPdfFormValueType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>PDF Export Settings</span>
        </Space>
      }
      width={400}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      size="small"
      labelCol={{ span: 12 }}
      labelAlign="left"
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      colon={false}
      initialValues={{
        inc_header_logo: true,
        inc_footer_address: true,
        inc_footer_logo: false,
        inc_footer_page_no: true,
        inc_footer_truck: true,
        inc_ibo_pre: false,
      }}
      onFinish={async (values) => {
        setLoading(true);

        const hide = message.loading('Exporting Packing PDF...', 0);
        setLoading(true);
        exportOfferItemList({
          ...props.getParentParams(),
          ...values,
          mode: 'withIboPrePacked',
          format: 'pdf',
        })
          .then((res) => {
            window.open(`${API_URL}/api/${res.url}`, '_blank');
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Continue to Export PDF' },
        onReset(value) {
          props.handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading, loading: loading },
        submitButtonProps: { disabled: loading, loading: loading },
      }}
      modalProps={{
        confirmLoading: loading,
      }}
    >
      <ProFormSwitch name="inc_header_logo" label="Include Header Logo?" />
      <ProFormSwitch name="inc_footer_address" label="Include Footer Address?" />
      <ProFormSwitch name="inc_footer_logo" label="Include Footer Logo?" />
      <ProFormSwitch name="inc_footer_truck" label="Include Footer Truck?" />
      <ProFormSwitch name="inc_footer_page_no" label="Include Footer Page No?" />
      <ProFormSwitch name="inc_ibo_pre" label="Include IBO Pre?" />
      {formElementsWaNo}
    </ModalForm>
  );
};

export default ExportPackingPdfSettingFormModal;
