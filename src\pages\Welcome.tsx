import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Row, Col, Tag, Space, Typography } from 'antd';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { getMagSyncLogList } from '@/services/foodstore-one/Magento/sync-log';
import Util from '@/util';
import moment from 'moment';
import { ArrowDownOutlined, ArrowUpOutlined, LoadingOutlined, ReloadOutlined } from '@ant-design/icons';
import { Link, useModel } from 'umi';
import SalesStatDaily from './Report/Order/SalesStatDaily';
import { getOrderCountOnHold, StatsType } from '@/services/foodstore-one/Report/sales-stat';
import { MagOrderExtraStatus } from '@/constants';
import { getSysLogList } from '@/services/foodstore-one/Sys/sys-log';

export const SyncTypeIcon: React.FC<{ type?: number }> = ({ type }) => {
  return type == 1 ? (
    <ArrowUpOutlined style={{ color: '#1890ff' }} />
  ) : type == 0 ? (
    <ArrowDownOutlined style={{ color: '#52c41a' }} />
  ) : null;
};

const Welcome: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  const [loadingStats, setLoadingStats] = useState(false);
  const [stats, setStats] = useState<StatsType>({ count_on_hold: 0 });

  const columns: ProColumns<API.MagSyncLog>[] = [
    {
      dataIndex: 'sync_type',
      width: 15,
      render: (dom: any, record: any) => <SyncTypeIcon type={record.sync_type} />,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 100,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      ellipsis: true,
    },
    {
      title: 'Date',
      dataIndex: 'updated_on',
      width: 110,
      render: (dom, record) =>
        record.updated_on ? (
          <div title={Util.dtToDMYHHMM(record.updated_on)}>{moment(record.updated_on).fromNow()}</div>
        ) : undefined,
    },
  ];

  const columns2: ProColumns<API.MagSyncLog>[] = [
    {
      dataIndex: 'sync_type',
      width: 15,
      render: (dom: any, record: any) => <SyncTypeIcon type={record.sync_type} />,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 100,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      ellipsis: true,
    },
    {
      title: 'Date',
      dataIndex: 'updated_on',
      width: 110,
      render: (dom, record) =>
        record.updated_on ? (
          <div title={Util.dtToDMYHHMM(record.updated_on)}>{moment(record.updated_on).fromNow()}</div>
        ) : undefined,
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
    },
  ];

  const columnsSysLog: ProColumns<API.MagSyncLog>[] = [
    {
      title: 'Status',
      dataIndex: 'status',
      width: 90,
      render: (dom, record) => <Tag color={record.status == 'started' ? 'error' : record.status}>{record.status}</Tag>,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 250,
      ellipsis: true,
    },

    {
      title: 'Note',
      dataIndex: 'note',
      ellipsis: true,
    },
    {
      title: 'Date',
      dataIndex: 'created_on',
      width: 110,
      render: (dom, record) =>
        record.created_on ? (
          <div title={Util.dtToDMYHHMM(record.created_on)}>{moment(record.created_on).fromNow()}</div>
        ) : undefined,
    },
    {
      title: 'User',
      dataIndex: ['user', 'username'],
      ellipsis: true,
      width: 120,
    },
  ];

  const loadStats = () => {
    setLoadingStats(true);
    getOrderCountOnHold()
      .then((res) => setStats(res))
      .catch(Util.error)
      .finally(() => {
        setLoadingStats(false);
      });
  };

  useEffect(() => {
    loadStats();
  }, []);

  return (
    <PageContainer
      extra={
        stats ? (
          <Space>
            {!!stats.count_on_hold && (
              <div style={{ fontSize: 16 }}>
                <Typography.Link
                  href={`/orders/master?extra_note1_status=${MagOrderExtraStatus.OnHold}`}
                  style={{ fontWeight: 'bold', color: 'red' }}
                  target="_blank"
                >
                  <span style={{ fontSize: 24 }}>{stats.count_on_hold}</span>

                  <span> orders in hold.</span>
                </Typography.Link>
                <span style={{ marginLeft: 8 }}>
                  {loadingStats ? (
                    <LoadingOutlined />
                  ) : (
                    <ReloadOutlined
                      className="cursor-pointer"
                      onClick={() => {
                        loadStats();
                      }}
                    />
                  )}
                </span>
              </div>
            )}
          </Space>
        ) : null
      }
    >
      {initialState?.currentUser && (
        <Row gutter={24}>
          <Col xs={24} lg={24} xl={10} xxl={10}>
            <SalesStatDaily pageSize={15} />
          </Col>
          <Col xs={24} lg={12} xl={7} xxl={7}>
            <Card bodyStyle={{ padding: 0 }}>
              <ProTable<API.MagSyncLog, API.PageParams>
                rowKey="id"
                size="small"
                headerTitle="Salable stock & WHC GDSN sync on cronjob"
                revalidateOnFocus={false}
                options={{ reload: true, density: false, setting: false }}
                search={false}
                params={{ action_type: 1 }}
                request={getMagSyncLogList}
                columns={columns}
                pagination={false}
                columnEmptyText=""
              />
              <Link
                href="/monitor/magento-sync-log"
                to={'/monitor/magento-sync-log'}
                style={{ display: 'block', float: 'right', marginBottom: 10, marginRight: 32 }}
              >
                {' '}
                More...
              </Link>
            </Card>
          </Col>
          <Col xs={24} lg={12} xl={7} xxl={7}>
            <Card bodyStyle={{ padding: 0 }}>
              <ProTable<API.MagSyncLog, API.PageParams>
                rowKey="id"
                size="small"
                headerTitle="Latest sync history"
                revalidateOnFocus={false}
                options={{ reload: true, density: false, setting: false }}
                search={false}
                params={{ action_type: 0, with: 'user' }}
                request={getMagSyncLogList}
                columns={columns2}
                pagination={false}
                columnEmptyText=""
              />
              <Link
                href="/monitor/magento-sync-log"
                to={'/monitor/magento-sync-log'}
                style={{ display: 'block', float: 'right', marginBottom: 10, marginRight: 32 }}
              >
                {' '}
                More...
              </Link>
            </Card>
          </Col>

          <Col xs={24} lg={24} xl={10} xxl={10} style={{ marginTop: 24 }}>
            <Card bodyStyle={{ padding: 0 }}>
              <ProTable<API.SysLog, API.PageParams>
                rowKey="id"
                size="small"
                headerTitle="System Log"
                revalidateOnFocus={false}
                options={{ reload: true, density: false, setting: false }}
                search={false}
                params={{ action_type: 0, with: 'user' }}
                // request={getSysLogList}
                request={(params, sort, filter) => getSysLogList(params, { id: 'descend' })}
                columns={columnsSysLog}
                pagination={false}
                columnEmptyText=""
              />
              <Link
                href="/monitor/sys-log"
                to={'/monitor/sys-log'}
                style={{ display: 'block', float: 'right', marginBottom: 10, marginRight: 32 }}
              >
                {' '}
                More...
              </Link>
            </Card>
          </Col>
        </Row>
      )}
    </PageContainer>
  );
};

export default Welcome;
