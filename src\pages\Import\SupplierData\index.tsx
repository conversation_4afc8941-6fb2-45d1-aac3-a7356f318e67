import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText, ProFormUploadButton } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { UploadFile } from 'antd';
import { Col, Row } from 'antd';
import { Space } from 'antd';
import { Switch } from 'antd';
import { message, Popconfirm } from 'antd';
import { Button, Card, Spin, Tag } from 'antd';

import { getSupplierList } from '@/services/foodstore-one/supplier';
import type { RcFile } from 'antd/lib/upload';
import {
  deleteImportedSupplierData,
  getImportList,
  importArticleNoFromSupplierData,
  updateEanPriceStable,
  updateEanXlsData,
  updateImport,
  uploadImportedFile,
} from '@/services/foodstore-one/Import/import';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { DEFAULT_PER_PAGE_PAGINATION, ImportStatus, ImportStatusOptions } from '@/constants';
import Util from '@/util';
import _ from 'lodash';
import {
  BarcodeOutlined,
  DeleteOutlined,
  EditFilled,
  FileExcelOutlined,
  LinkOutlined,
  ReconciliationOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import ImportExcelData from './components/ImportExcelData';
import XlsItemViewer from './components/XlsItemViewer';
import IboViewer from './components/IboViewer';
import type { DefaultOptionType } from 'antd/lib/select';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import './index.less';
import EditableCell from '@/components/EditableCell';
import TmpXlsImportModal from './components/TmpXlsImportModal';

enum LoadingType {
  UPLOAD,
  IMPORT,
  LOADING, // list loading
}

export type FormValueType = {
  files?: UploadFile[];
  supplier_id?: number;
};

export type SearchFormValueType = {
  ean?: string;
  name?: string;
};

const SupplierData: React.FC = () => {
  const [loadings, setLoadings] = useState<boolean[]>([false, false]);
  const enterLoading = (index: LoadingType, loadingStatus?: boolean) => {
    setLoadings((prevLoadings) => {
      const newLoadings = [...prevLoadings];
      newLoadings[index] = !!loadingStatus;
      return newLoadings;
    });
  };

  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const searchFormRef = useRef<ProFormInstance>();
  const [currentRow, setCurrentRow] = useState<API.Import>();
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [viewerVisible, handleViewerModalVisible] = useState<boolean>(false);
  const [iboViwerVisible, handleIboViwerModalVisible] = useState<boolean>(false);

  const [ibomList, setIbomList] = useState<DefaultOptionType[]>([]);

  const handleFinish = async (values: FormValueType) => {
    const data = new FormData();
    data.set('supplierId', `${values.supplier_id}`);
    if (values?.files) {
      data.append(`files[]`, values?.files[0].originFileObj as RcFile);
    }

    enterLoading(LoadingType.UPLOAD, true);
    uploadImportedFile(data)
      .then((res) => {
        actionRef.current?.reload();
        formRef.current?.resetFields();
      })
      .finally(() => enterLoading(LoadingType.UPLOAD, false));
  };

  const columns: ProColumns<API.Import>[] = useMemo(
    () => [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 40,
        align: 'center',
        render: (item, record, index, action) => {
          return (
            <span title={`${record.id}`}>
              {((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
                index +
                1}
            </span>
          );
        },
      },
      {
        title: 'Import Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        align: 'center',
        width: 110,
        showSorterTooltip: false,
        valueEnum: ImportStatusOptions as any,
        render: (__, record) => {
          return (
            <Tag color={record.status == ImportStatus.STATUS_IMPORTED ? 'green' : 'gray'}>
              {_.get(_.find(ImportStatusOptions, { value: record.status }), 'label', '')}
            </Tag>
          );
        },
      },
      {
        title: 'Active?',
        dataIndex: 'is_active',
        hideInForm: false,
        sorter: false,
        filters: false,
        align: 'center',
        width: 110,
        showSorterTooltip: false,
        render: (__, record) => {
          return (
            <Switch
              defaultChecked={record.is_active == 1}
              onChange={(checked) => {
                const hide = message.loading('Updating status...', 0);
                updateImport(record.id, { is_active: checked ? 1 : 0 })
                  .then((res) => {
                    // actionRef.current?.reload();
                  })
                  .catch((error) => Util.error(error))
                  .finally(() => hide());
              }}
            />
          );
        },
      },
      {
        title: 'Buying active?',
        dataIndex: 'is_buying_active',
        hideInForm: false,
        sorter: false,
        filters: false,
        align: 'center',
        width: 110,
        showSorterTooltip: false,
        render: (__, record) => {
          return (
            <Switch
              defaultChecked={record.is_buying_active == 1}
              onChange={(checked) => {
                const hide = message.loading('Updating buying active status...', 0);
                updateImport(record.id, { is_buying_active: checked ? 1 : 0 })
                  .then((res) => {
                    // actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              }}
            />
          );
        },
      },
      {
        title: 'Supplier',
        dataIndex: ['supplier', 'name'],
        hideInForm: false,
        sorter: false,
        filters: false,
        align: 'center',
        ellipsis: true,
        width: 120,
      },
      {
        title: 'SUPPLIER_ADD',
        dataIndex: ['supplier_add'],
        hideInForm: false,
        filters: false,
        ellipsis: true,
        width: 110,
        render(dom, record) {
          return (
            <Row gutter={4} align={'middle'} wrap={false}>
              <Col flex="auto">
                <EditableCell
                  dataType="text"
                  defaultValue={record.supplier_add || ''}
                  style={{ marginRight: 0 }}
                  // fieldProps={{ maxLength: 40 }}
                  triggerUpdate={async (newValue: any, cancelEdit) => {
                    if (!newValue && !record.id) {
                      cancelEdit?.();
                      return;
                    }
                    const hide = message.loading('Updating Supplier_ADD...', 0);
                    updateImport(record.id, { supplier_add: newValue })
                      .then((res) => {
                        message.destroy();
                        message.success('Updated successfully.');
                        actionRef.current?.reload();
                        cancelEdit?.();
                      })
                      .catch(Util.error)
                      .finally(() => hide());
                  }}
                >
                  {dom}
                </EditableCell>
              </Col>
              {!!record.supplier_add && (
                <Col flex="20px">
                  <a
                    href={`/item/ean-all-prices?special_filter2=1&import_id=${record.id}`}
                    target="_blank"
                    title="Open EAN prices filtered."
                  >
                    <LinkOutlined />
                  </a>
                </Col>
              )}
            </Row>
          );
        },
      },
      {
        title: 'Sort',
        dataIndex: ['sort_buying'],
        hideInForm: false,
        sorter: false,
        filters: false,
        align: 'center',
        ellipsis: true,
        width: 80,
        render: (__, record) => {
          return (
            <EditableCell
              dataType="number"
              isDefaultEditing
              defaultValue={record.sort_buying}
              fieldProps={{ placeholder: '', width: 'xs' }}
              triggerUpdate={async (newValue: any, cancelEdit) => {
                const hide = message.loading('Updating buying sort number...', 0);
                updateImport(record.id, { sort_buying: newValue })
                  .then((res) => {
                    // actionRef.current?.reload();
                  })
                  .catch((error) => Util.error(error))
                  .finally(() => hide());
              }}
            >
              {record.sort_buying}
            </EditableCell>
          );
        },
      },
      {
        title: 'Table Name',
        dataIndex: 'table_name',
        render: (__, record) => {
          return record.status == ImportStatus.STATUS_IMPORTED ? (
            <>
              <a
                title="Click to view imported data."
                onClick={() => {
                  setCurrentRow({ ...record });
                  handleViewerModalVisible(true);
                }}
              >
                {record.table_name}
              </a>
            </>
          ) : (
            record.table_name
          );
        },
      },
      {
        title: 'Uploaded File',
        dataIndex: ['files', 0, 'clean_file_name'],
        render: (dom, record) => {
          return (
            <a href={record.files?.[0]?.url || '#'} target="_blank" rel="noreferrer">
              {dom as any}
            </a>
          );
        },
      },
      {
        title: 'Updated on',
        sorter: true,
        dataIndex: 'updated_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        // fixed: 'right',
        width: 100,
        renderFormItem: (item, { type, defaultRender }) => {
          return defaultRender(item);
        },
        render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
      },
      {
        title: 'Created on',
        sorter: true,
        dataIndex: 'created_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        width: 100,
        renderFormItem: (item, { type, defaultRender }) => {
          return defaultRender(item);
        },
        render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 110,
        render: (dom, record) => {
          return (
            <Row>
              <Col span={6}>
                <a
                  key="ibo_viwer"
                  title="View with IBO..."
                  style={{
                    display: record.status == ImportStatus.STATUS_IMPORTED ? 'inline-block' : 'none',
                  }}
                  onClick={() => {
                    setCurrentRow({
                      ...record,
                    });
                    handleIboViwerModalVisible(true);
                  }}
                >
                  <FileExcelOutlined />
                </a>
              </Col>
              <Col span={6}>
                <a
                  key="import"
                  title="Import data..."
                  onClick={() => {
                    setCurrentRow({
                      ...record,
                    });
                    handleUpdateModalVisible(true);
                  }}
                >
                  <EditFilled />
                </a>
              </Col>
              <Col span={6}>
                <Popconfirm
                  key="delete-record"
                  title={<>Are you sure you want to import Article No into EANs?</>}
                  okText="Yes"
                  cancelText="No"
                  // overlayStyle={{ width: 350, maxWidth: 350 }}
                  disabled={record.status != ImportStatus.STATUS_IMPORTED}
                  placement="left"
                  onConfirm={() => {
                    const hide = message.loading(`Importing Article No & Supplier into EAN...`, 0);
                    importArticleNoFromSupplierData(record.id || 0)
                      .then((res) => {
                        message.success('Successfully imported!');
                        actionRef.current?.reload();
                      })
                      .catch((e) => {
                        message.error(e.message ?? 'Failed to import!');
                      })
                      .finally(() => {
                        hide();
                      });
                  }}
                >
                  <BarcodeOutlined
                    title="Import Article No & Supplier into EAN."
                    style={{
                      display: record.status == ImportStatus.STATUS_IMPORTED ? 'inline-block' : 'none',
                    }}
                  />
                </Popconfirm>
              </Col>
              <Col span={6}>
                <Popconfirm
                  key="delete-record"
                  title={<>Are you sure you want to remove the imported data?</>}
                  okText="Yes"
                  cancelText="No"
                  placement="left"
                  // overlayStyle={{ width: 350, maxWidth: 350 }}
                  onConfirm={() => {
                    const hide = message.loading(`Deleting ...`, 0);
                    deleteImportedSupplierData(record.id || 0)
                      .then((res) => {
                        message.success('Successfully deleted!');
                        actionRef.current?.reload();
                      })
                      .catch((e) => {
                        message.error(e.message ?? 'Failed to delete!');
                      })
                      .finally(() => {
                        hide();
                      });
                  }}
                >
                  <DeleteOutlined className="c-red" />
                </Popconfirm>
              </Col>
            </Row>
          );
        },
      },
    ],
    [],
  );

  useEffect(() => {
    getIBOManagementACList({}).then((res) => {
      setIbomList(res);
    });
  }, []);

  // ---------------------------------------------------------
  // tmp xls data upload
  // ---------------------------------------------------------
  const [openTmpXlsImportModal, setOpenTmpXlsImportModal] = useState<boolean>(false);

  return (
    <PageContainer
      className="page-supplier-data"
      extra={
        <>
          <Button
            type="primary"
            key="button"
            ghost
            icon={<UploadOutlined />}
            onClick={() => setOpenTmpXlsImportModal(true)}
          >
            Import Tmp Xls Data...
          </Button>
        </>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <Spin spinning={loadings[LoadingType.UPLOAD]}>
          <ProForm<FormValueType>
            formRef={formRef}
            initialValues={{}}
            submitter={{
              submitButtonProps: { loading: loadings[LoadingType.LOADING || false] },
              render: (props, doms) => {
                return (
                  <Space style={{ alignItems: 'baseline' }}>
                    {/* <Popover
                      placement="bottom"
                      title="Selection options"
                      trigger="click"
                      open={openUploadOptionForm}
                      content={
                        <ProForm<OptionFormValueType>
                          formRef={optionFormRef}
                          size="small"
                          initialValues={{ import_option: 'add_new' }}
                          onFinish={async (values) => {
                            console.log(values);
                            console.log(formRef.current?.getFieldsValue());
                            formRef.current?.submit();
                            setOpenUploadOptionForm(false);
                            return Promise.resolve(true);
                          }}
                          submitter={{
                            searchConfig: { submitText: 'Upload' },
                            render(__, dom) {
                              return [dom[1]];
                            },
                          }}
                        >
                          <ProFormRadio.Group
                            name={['import_option']}
                            radioType="button"
                            fieldProps={{ style: { margin: 0, padding: 0 }, buttonStyle: 'solid' }}
                            formItemProps={{ style: { padding: 0 } }}
                            valueEnum={ImportOption}
                            required
                            rules={[
                              {
                                required: true,
                                message: 'Import option is required',
                              },
                            ]}
                          />
                        </ProForm>
                      }
                    >
                      <Button
                        type="primary"
                        key="button"
                        icon={<UploadOutlined />}
                        onClick={() => setOpenUploadOptionForm(true)}
                      >
                        Upload
                      </Button>
                    </Popover> */}

                    <Button
                      type="primary"
                      key="button"
                      icon={<UploadOutlined />}
                      onClick={() => formRef.current?.submit()}
                    >
                      Upload
                    </Button>

                    <Button type="default" key="rest" onClick={() => formRef.current?.resetFields()}>
                      Reset
                    </Button>
                    <div style={{ position: 'absolute', top: 0, right: 0 }}>
                      <Button
                        type="primary"
                        icon={<ReconciliationOutlined />}
                        style={{ marginLeft: 16 }}
                        className="btn-green"
                        title="Update all ean table."
                        onClick={() => {
                          const hide = message.loading('Updating the data...', 0);
                          updateEanXlsData()
                            .then((res) => {
                              message.success('Updated successfully.');
                            })
                            .catch(Util.error)
                            .finally(() => hide());
                        }}
                      >
                        Update EAN_XLS All
                      </Button>

                      <Button
                        type="primary"
                        icon={<ReconciliationOutlined />}
                        style={{ marginLeft: 16 }}
                        className="btn-green"
                        title="Update ean price stable data of EANs from the active imported data."
                        onClick={() => {
                          const hide = message.loading('Updating the data...', 0);
                          updateEanPriceStable()
                            .then((res) => {
                              message.success('Updated successfully.');
                            })
                            .catch(Util.error)
                            .finally(() => hide());
                        }}
                      >
                        Update Ean Price Stable
                      </Button>
                    </div>
                  </Space>
                );
              },
            }}
            layout="inline"
            onFinish={handleFinish}
          >
            <ProFormSelect
              showSearch
              placeholder="Select a supplier"
              request={async (params) => {
                const res = await getSupplierList({ ...params, pageSize: 1000 }, { name: 'ascend' }, {});
                if (res && res.data) {
                  const tmp = res.data.map((x: API.Item) => ({
                    label: `${x.id} - ${x.name}`,
                    value: x.id,
                  }));
                  return tmp;
                }
                return [];
              }}
              fieldProps={{
                onChange: (value) => {
                  actionRef.current?.reload();
                },
              }}
              required
              rules={[
                {
                  required: true,
                  message: 'Supplier is required',
                },
              ]}
              width="sm"
              name="supplier_id"
              label="Supplier"
            />
            <ProFormUploadButton
              max={1}
              name="files"
              label="File"
              title="Select File"
              accept=".xls,.xlsx,.csv,.txt"
              required
              rules={[
                {
                  required: true,
                  message: 'File is required',
                },
              ]}
              // formItemProps={{ style: { width: 250 } }}
              fieldProps={{
                beforeUpload: (file) => {
                  return false;
                },
              }}
            />
          </ProForm>
        </Spin>
      </Card>

      {/* <MarryEanModal
        modalVisible={true}
        handleModalVisible={() => {}}
        initialValue={{
          id: 1,
          xls_id: 1,
          imported_ean: '',
          ean_name: 'q',
          item_name: 'q',
        }}
        onMarriedCallback={async (values) => {
          actionRef.current?.reload();
        }}
      /> */}
      <ProTable<API.Import, API.PageParams>
        headerTitle={
          <Space size={48}>
            <div>Imported List</div>
            <ProForm<SearchFormValueType>
              key="form"
              formRef={searchFormRef}
              layout="inline"
              submitter={{
                searchConfig: { submitText: 'Search' },
                resetButtonProps: { style: { display: 'none' } },
                onSubmit(value) {
                  actionRef.current?.reload();
                },
              }}
            >
              <ProFormText name="ean" label="EAN" width={180} placeholder="Fill EAN" />
              <ProFormText name="name" label="Name" width={'sm'} placeholder="Fill EAN Name" />
            </ProForm>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        // rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        request={async (params, sort, filter) => {
          enterLoading(LoadingType.LOADING, true);
          return getImportList(
            {
              ...params,
              ...searchFormRef.current?.getFieldsValue(),
              supplier_id: formRef.current?.getFieldValue('supplier_id'),
            },
            sort,
            filter,
          ).finally(() => enterLoading(LoadingType.LOADING, false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText={''}
      />
      <ImportExcelData
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValue={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
      <XlsItemViewer
        modalVisible={viewerVisible}
        handleModalVisible={handleViewerModalVisible}
        initialValue={currentRow || {}}
        onCancel={() => {
          handleViewerModalVisible(false);
        }}
      />
      <IboViewer
        modalVisible={iboViwerVisible}
        handleModalVisible={handleIboViwerModalVisible}
        initialValue={currentRow || {}}
        ibomList={ibomList}
        onCancel={() => {
          handleIboViwerModalVisible(false);
        }}
      />
      <TmpXlsImportModal modalVisible={openTmpXlsImportModal} handleModalVisible={setOpenTmpXlsImportModal} />
    </PageContainer>
  );
};

export default SupplierData;
