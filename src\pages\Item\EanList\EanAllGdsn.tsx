import {
  CheckCircleOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DollarOutlined,
  DownloadOutlined,
  DownOutlined,
  EditTwoTone,
  FileExcelOutlined,
  FileTextOutlined,
  HighlightOutlined,
  InfoCircleOutlined,
  LoadingOutlined,
  PictureOutlined,
  SaveOutlined,
  SnippetsOutlined,
  SyncOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Drawer, Modal, Progress, Image, Checkbox, Radio } from 'antd';
import { Card } from 'antd';
import { Button, message, Dropdown, Space, Menu, Popconfirm, Typography } from 'antd';
import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateAttributeForm from './components/UpdateAttributeForm';
import SDatePicker from '@/components/SDatePicker';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import WebsiteIcons from './components/WebsiteIcons';
import {
  getEanList,
  exportEanList,
  exportEanListAlt,
  exportEanPriceList,
  dsGetCustomAttribute,
  usProductFull,
  EAN_DEFAULT_SUMMARY_WITH,
  exportEanWithCodeList,
  updateEanAttributePartial,
  updateEanBatch,
} from '@/services/foodstore-one/Item/ean';
import { ItemEANStatus, ScrapSystemIds } from '@/constants';
import { DEFAULT_PER_PAGE_PAGINATION, ItemEANStatusOptions } from '@/constants';
import UpdateCategoriesForm from './components/UpdateCategoriesForm';
import type { DataNode } from 'antd/lib/tree';
import { getCategoryList } from '@/services/foodstore-one/Item/category';
import UpdateTextsForm from './components/UpdateTextsForm';
import UpdatePicturesForm from './components/UpdatePicturesForm';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormGroup } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
import * as UpdateItemForm from '../ItemList/components/UpdateForm';
import { useLocation, useModel } from 'umi';
import type { DefaultOptionType } from 'antd/lib/select';
import UpdateCategoriesFormBulk from './components/UpdateCategoriesFormBulk';
import UpdateAttributesFormBulk from './components/UpdateAttributesFormBulk';
import StockStableQtyModal from './components/StockStableQtyModal';
import { scrapWoSPrice } from '@/services/foodstore-one/Scrap/scrap-price';
import ImportedPrices from './components/ImportedPrices';
import UpdatePriceAttributeForm from './components/UpdatePriceAttributeForm';
import UpdateVatFormBulk from './components/UpdateVatFormBulk';
import type { ResizeCallbackData } from 'react-resizable';
import { ResizableTitle } from './EanAllPic';
import EanTasksModals from './components/EanTasksModal';
import useModalNavigation from './hooks/useModalNavigation';
import usePageContainerTitle from './hooks/usePageContainerTitle';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import type { TrademarkChangeCallbackHandlerTypeParamType } from './hooks/useTrademarkFormFilter';
import useTrademarkFormFilter from './hooks/useTrademarkFormFilter';
import { getImportACList } from '@/services/foodstore-one/Import/import';
import UpdatePriceAttributeFormBulk from './components/UpdatePriceAttributeFormBulk';
import { handleEanRemoveBatch } from './EanAllSummary';
import { getCountriesDEOptions } from '@/services/foodstore-one/countries';
import useIbomOptions from '@/hooks/BasicData/useIbomOptions';

type DiffValueKeyType =
  | 'name_de'
  | 'official_title'
  | 'official_ingredients'
  | 'official_nutrition'
  | 'official_country'
  | 'official_producer'
  | 'official_usage'
  | 'official_warning'
  | 'description1'
  | 'short_description'
  | 'meta_title'
  | 'meta_keywords'
  | 'meta_description'
  | 'special_filter'
  | 'length'
  | 'height'
  | 'width'
  | 'weight'
  | 'item_base'
  | 'item.hs_code'
  | 'item.name'
  | 'item.vat.id';

export const isDiff = (field: DiffValueKeyType, record: API.Ean) => {
  // const eanText = record?.ean_texts?.[0];
  const ownText = record.ean_texts?.[0];
  const parentText = record.parent?.ean_texts?.[0];
  if (parentText && ownText?.settings?.useOwnText) {
    parentText.short_description = ownText?.short_description;
    parentText.description1 = ownText?.description1;
    parentText.meta_title = ownText?.meta_title;
    parentText.meta_keywords = ownText?.meta_keywords;
    parentText.meta_description = ownText?.meta_description;
  }

  const gdsn_item = record.gdsn_item;
  // if no gdsn data, then we mark "no diff" by false

  if (!record.gdsn_item) return false;

  switch (field) {
    case 'length':
      return record.length != gdsn_item?.length;
    case 'height':
      return record.height != gdsn_item?.height;
    case 'width':
      return record.width != gdsn_item?.width;
    case 'weight':
      return record.weight != gdsn_item?.gross_weight;
    case 'item.hs_code':
      return record.item?.hs_code != gdsn_item?.detail?.hs_code;
    case 'item.vat.id':
      return record.item?.vat?.id != gdsn_item?.detail?.vat?.id;
    case 'name_de':
      return record.ean_texts?.[0]?.name != gdsn_item?.detail?.short_description;
    case 'short_description':
      return parentText?.short_description != gdsn_item?.name;
    case 'description1':
      return parentText?.description1?.trim() != gdsn_item?.detail?.description1?.trim();
    case 'meta_keywords':
      return parentText?.meta_keywords != gdsn_item?.detail?.tradeItemKeyWords;
    case 'official_usage':
      return parentText?.official_usage != gdsn_item?.detail?.storage_instruction;
    case 'official_warning':
      return parentText?.official_warning != gdsn_item?.detail?.health_compulsory;

    case 'official_ingredients':
      return (
        Util.removeTags(parentText?.official_ingredients) !=
        Util.removeTags(gdsn_item?.detail?.ean_text?.official_ingredients)
      );
    case 'official_title': // okay
      return parentText?.official_title != gdsn_item?.detail?.ean_text?.official_title;
    case 'official_nutrition':
      return (
        Util.removeTags(parentText?.official_nutrition) !=
        Util.removeTags(gdsn_item?.detail?.ean_text?.official_nutrition)
      );

    case 'official_country':
      return parentText?.official_country != gdsn_item?.detail?.country_code;
    case 'official_producer': {
      // const value1 = record.ean_texts?.[0]?.official_producer_obj?.full_name ?? record.ean_texts?.[0]?.official_producer_gdsn;
      return false;
    }
    case 'special_filter':
      const value1 = JSON.stringify(record.item?.special_filter as string[]);
      const value2 = JSON.stringify(gdsn_item?.detail?.diets?.map((x: any) => x.code) ?? []);
      return value1 != value2;
    default:
      break;
  }

  if (field == 'item_base') {
    const value1 = Util.numberFormat(record.item_base, false, 3, true) + (record.item_base_unit ?? '');
    const value2 = Util.numberFormat(gdsn_item?.item_base, false, 3, true) + (gdsn_item?.item_base_unit ?? '');
    return value1.toLowerCase() !== value2.toLowerCase();
  }
  return false;
};

// Designed, but useless?
/* export const isDiffAlt = (field: DiffValueKeyType, record: API.Ean, gdsnItem: API.ItemEanGdsn) => {
  return isDiff(field, {parent: record, gdsn_item: gdsnItem});
} */

export type EanTableCellProp = {
  field: DiffValueKeyType;
  value: any;
  gdsnValue: any;
};
export const EanTableCell: React.FC<EanTableCellProp> = ({ field, value, gdsnValue: gdsnValue }) => {
  let formattedValue: any = value;
  let formattedGdsnValue: any = gdsnValue;

  switch (field) {
    case 'length':
    case 'height':
    case 'width':
    case 'weight':
      formattedValue = Util.numberFormat(value, false, 3, true);
      formattedGdsnValue = Util.numberFormat(gdsnValue, false, 3, true);
      break;
    case 'item.vat.id':
      formattedValue = Util.numberFormat(value, false) + '%';
      formattedGdsnValue = Util.numberFormat(gdsnValue, false) + '%';
      break;

    case 'special_filter':
      const title1 = (value as string[])?.join(', ');
      const title2 = (gdsnValue ?? []).join(', ');
      formattedValue = (
        <Typography.Text ellipsis title={title1}>
          {title1}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={title2}>
          {title2}
        </Typography.Text>
      );
      break;
    case 'official_title':
      formattedValue = (
        <Typography.Text ellipsis title={value}>
          {value}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={gdsnValue}>
          {gdsnValue}
        </Typography.Text>
      );
      break;
    case 'official_country':
      formattedValue = (
        <Typography.Text ellipsis title={value}>
          {value}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={gdsnValue}>
          {gdsnValue}
        </Typography.Text>
      );
      break;
    case 'description1':
      formattedValue = (
        <Typography.Text ellipsis title={value}>
          {value}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={gdsnValue}>
          {gdsnValue}
        </Typography.Text>
      );
      break;
    case 'official_producer':
      formattedValue = (
        <Typography.Text ellipsis title={value}>
          {value}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={gdsnValue}>
          {gdsnValue}
        </Typography.Text>
      );
      break;
    case 'official_ingredients':
      formattedValue = (
        <Typography.Text ellipsis title={value}>
          {value}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={gdsnValue}>
          {gdsnValue}
        </Typography.Text>
      );
      break;
    case 'official_nutrition':
      formattedValue = (
        <Typography.Text ellipsis title={value}>
          {value}
        </Typography.Text>
      );
      formattedGdsnValue = (
        <Typography.Text ellipsis title={gdsnValue}>
          {gdsnValue}
        </Typography.Text>
      );
      break;
    default:
      break;
  }

  return (
    <Space direction="vertical" size={2} style={{ width: '100%' }}>
      <div style={{ borderBottom: '1px solid #eee', minHeight: 20 }}>{formattedValue}</div>
      <div style={{ fontStyle: 'italic', minHeight: 20 }}>{formattedGdsnValue}</div>
    </Space>
  );
};

export type SearchFormValueType = Partial<API.Ean>;

export type EanSummaryComponentProps = {
  eanType?: 'default' | 'base' | 've';
  route?: any;
};

const EanAllGdsn: React.FC<EanSummaryComponentProps> = (eanComponentProps) => {
  const eanTypeProp = eanComponentProps.eanType || 'default';
  const { appSettings } = useModel('app-settings');

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);
  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  // batch
  const [visibleUpdateVatFormBulk, handleVisibleUpdateVatFormBulk] = useState<boolean>(false);
  const [batchUpSyncModalVisible, handleBatchUpSyncModalVisible] = useState<boolean>(false);
  const [batchUpSyncProgress, setBatchUpSyncProgress] = useState(0);
  const [batchModalData, setBatchModalData] = useState<{ title: string; desc?: string }>({
    title: 'Batch Up Syncing...',
    desc: 'Batch UpSync is in progress. Please wait...',
  });

  // bulk updates
  const [visibleUpdateCategoriesFormBulk, handleVisibleUpdateCategoriesFormBulk] = useState<boolean>(false);
  const [visibleUpdateAttributesFormBulk, handleVisibleUpdateAttributesFormBulk] = useState<boolean>(false);
  const [visibleUpdatePriceFormBulk, handleVisibleUpdatePriceFormBulk] = useState<boolean>(false);

  // Ean Tasks model
  const [visibleEanTasksModal, setVisibleEanTasksModal] = useState<boolean>(false);

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});
  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const [selectedRows, setSelectedRows] = useState<API.Ean[]>([]);

  // datasource for inline editing
  const [datasource, setDatasource] = useState<API.Ean[]>([]);

  const exportSupplierRef = useRef<any>(null);

  // hook for modal navigation
  // ------------------------------------------------------------- //
  const { handleNavigation } = useModalNavigation(datasource, {
    item: handleUpdateItemModalVisible,
    attribute: handleUpdateModalVisible,
    picture: handleUpdatePicturesModalVisible,
    price: handleUpdatePricesModalVisible,
    text: handleUpdateTextsModalVisible,
    category: handleUpdateCategoriesModalVisible,
    modals: ['item', 'text', 'price', 'attribute', 'picture', 'category'],
    setCurrentRow,
  });

  // ibom filter
  const { ibom, formElements: formElementsIbom } = useIbomOptions(undefined, searchFormRef);

  const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
    ibom_id: ibom?.id,
  });

  const [loadingExport, setLoadingExport] = useState(false);
  const location: any = useLocation();

  const handleTextsClick = (record: API.Ean) => {
    setCurrentRow({ ...record });
    handleUpdateTextsModalVisible(true);
  };

  // Xls file selection
  const [files, setFiles] = useState<DefaultOptionType[]>([]);
  const [supplierXlsFileId, setSupplierXlsFileId] = useState<number>();

  /**
   * UpSync this EAN fully.
   */
  const handleUpSync = useCallback(async (id: number) => {
    const hide = message.loading(`Up syncing ...`, 0);
    return usProductFull(id)
      .then((res) => {
        if (res.sku) {
          message.success('Successfully up synced on shop!');
        } else {
          message.error(res.upSyncMessage || 'Failed to up sync EAN!');
        }
      })
      .catch((e) => {
        message.error(e.message ?? 'Failed to upsync!');
      })
      .finally(() => {
        hide();
      });
  }, []);

  const [columns, setColumns] = useState<ProColumns<API.Ean>[]>([]);

  const orgColumns = useMemo<ProColumns<API.Ean>[]>(
    () => [
      {
        title: 'Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        align: 'center',
        ellipsis: true,
        width: 50,
        showSorterTooltip: false,
        valueEnum: ItemEANStatusOptions as any,
        render: (__, record) => {
          let ele = null;
          if (record.status == ItemEANStatus.ACTIVE) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          } else {
            ele = <CloseOutlined style={{ color: 'gray' }} />;
          }
          return ele;
        },
      },

      {
        title: 'Shops',
        dataIndex: 'product_websites',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        width: 50,
        showSorterTooltip: false,
        render: (__, record) => (
          <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
        ),
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 80,
        render: (dom, record) => {
          // return dom;
          // return record.files ? <img src={record.files?.[0]?.url} /> : <></>;
          return record.files ? (
            <Image.PreviewGroup>
              {record.files &&
                record.files.map((file, ind) => (
                  <Image
                    key={file.id}
                    src={file.thumb_url}
                    preview={{
                      src: file.url,
                    }}
                    wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                    width={40}
                  />
                ))}
            </Image.PreviewGroup>
          ) : (
            <></>
          );
        },
      },
      {
        title: 'Name DE',
        dataIndex: ['ean_texts', 0, 'name'],
        width: 280,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        fixed: 'left',
        tooltip: 'Orange color indicates the inherited value from its item.',
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record, prevRecord),
        render: (dom, record, index) => {
          const defaultValue = record?.ean_texts?.[0]?.name ?? record?.item?.name;
          return (
            <EanTableCell
              field={'name_de'}
              value={
                <a onClick={() => handleTextsClick(record)}>
                  <Typography.Text
                    type={record?.ean_texts?.[0]?.name ? undefined : 'warning'}
                    title={defaultValue}
                    ellipsis
                  >
                    {defaultValue ?? <CloseOutlined style={{ color: '#cc2200' }} />}
                  </Typography.Text>
                </a>
              }
              gdsnValue={
                <Typography.Text ellipsis title={record.gdsn_item?.detail?.short_description}>
                  {record.gdsn_item?.detail?.short_description}
                </Typography.Text>
              }
            />
          );
        },
        onCell: (record) => ({
          className: isDiff('name_de', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        dataIndex: ['gdsn_item', 'ean'],
        title: <InfoCircleOutlined title="EAN exists in GDSN?" className="cursor-pointer" />,
        width: 20,
        align: 'center',
        render: (__, record) => {
          let ele = null;
          if (record.gdsn_item?.ean) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          }
          return ele;
        },
      },
      {
        dataIndex: ['idInXlsFile'],
        title: <InfoCircleOutlined title="EAN exists in selected XLS file?" className="cursor-pointer" />,
        width: 20,
        align: 'center',
        hideInTable: !supplierXlsFileId,
        render: (__, record) => {
          let ele = null;
          if (supplierXlsFileId && record.idInXlsFile) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          }
          return ele;
        },
      },
      {
        dataIndex: ['action_copy_all'],
        width: 20,
        align: 'center',
        title: <InfoCircleOutlined title="Update all data from GDSN and XLS" className="cursor-pointer" />,
        render: (__, record) => (
          <Popconfirm
            title={
              <>
                <div>Are you sure you want to update data by GDSN and imported supplier XLS?</div>
                <div style={{ paddingTop: 16 }}>
                  <label htmlFor={`isPriceOverride_${record.id}`}>Override price from Xls?&nbsp;&nbsp;</label>
                  <Checkbox id={`isPriceOverride_${record.id}`} />
                </div>
                <div style={{ paddingTop: 16 }}>
                  <label>Update mode?&nbsp;&nbsp;</label>
                  <Radio.Group
                    name={`mode_${record.id}`}
                    buttonStyle="solid"
                    size="small"
                    optionType="button"
                    defaultValue={'3'}
                  >
                    <Radio value={'1'}>
                      A <InfoCircleOutlined title="Update all" />
                    </Radio>
                    <Radio value={'2'}>
                      B <InfoCircleOutlined title="Update all except Name DE" />
                    </Radio>
                    <Radio value={'3'}>
                      C <InfoCircleOutlined title="Update all except Name DE, Short, Short Desc" />
                    </Radio>
                  </Radio.Group>
                </div>
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 400 }}
            onConfirm={() => {
              const gdsnUpdateMode = (document.querySelector(`[name="mode_${record.id}"]:checked`) as HTMLInputElement)
                ?.value;
              const isPriceOverride = (document.getElementById(`isPriceOverride_${record.id}`) as HTMLInputElement)
                ?.checked;
              const hide = message.loading('Updating EAN by GDSN or XLS', 0);
              updateEanAttributePartial({
                id: record.id,
                mode: 'updateByGDSN',
                isPriceOverride: isPriceOverride,
                supplierXlsFileId: searchFormRef.current?.getFieldValue('supplierXlsFileId'),
                gdsnUpdateMode: gdsnUpdateMode,
              })
                .then((res) => {
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            <HighlightOutlined className="cursor-pointer c-blue" />
          </Popconfirm>
        ),
      },
      {
        title: 'Produktbezeichnung',
        dataIndex: ['ean_texts', 0, 'official_title'],
        width: 150,
        ellipsis: true,
        render: (__, record) => (
          <EanTableCell
            field={'official_title'}
            value={record.parent?.ean_texts?.[0]?.official_title}
            gdsnValue={record.gdsn_item?.detail?.ean_text?.official_title}
          />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('official_title', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Herkunftsland',
        dataIndex: ['ean_texts', 0, 'official_country'],
        width: 100,
        ellipsis: true,
        render: (__, record) =>
          record.is_single ? (
            <EanTableCell
              field={'official_country'}
              value={getCountriesDEOptions().find((x) => x.value == record.ean_texts?.[0]?.official_country)?.label}
              gdsnValue={getCountriesDEOptions().find((x) => x.value == record.gdsn_item?.detail?.country_code)?.label}
            />
          ) : null,
        onCell: (record) => ({
          className: record.is_single && isDiff('official_country', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Lebensmittelunternehmen',
        dataIndex: ['ean_texts', 0, 'official_producer'],
        width: 100,
        ellipsis: true,
        render: (__, record) =>
          record.is_single ? (
            <EanTableCell
              field={'official_producer'}
              value={
                record.ean_texts?.[0]?.official_producer_obj?.full_name ?? record.ean_texts?.[0]?.official_producer_gdsn
              }
              gdsnValue={''}
            />
          ) : null,
        onCell: (record) => ({
          className: record.is_single && isDiff('official_producer', record) ? 'bg-light-orange2' : '',
        }),
      },

      {
        title: 'Short Description',
        dataIndex: ['ean_texts', 0, 'short_description'],
        width: 150,
        ellipsis: true,
        render: (__, record) => (
          <EanTableCell
            field={'short_description'}
            value={record.parent?.ean_texts?.[0]?.short_description}
            gdsnValue={record.gdsn_item?.name}
          />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('short_description', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Description',
        dataIndex: ['ean_texts', 0, 'description1'],
        width: 150,
        ellipsis: true,
        render: (__, record) => (
          <EanTableCell
            field={'description1'}
            value={record.parent?.ean_texts?.[0]?.description1}
            gdsnValue={record.gdsn_item?.detail?.description1}
          />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('description1', record) ? 'bg-light-orange2' : '',
        }),
      },

      {
        title: 'Zutaten',
        dataIndex: ['ean_texts', 0, 'official_ingredients'],
        width: 150,
        ellipsis: true,
        render: (__, record) =>
          record.is_single ? (
            <EanTableCell
              field={'official_ingredients'}
              value={Util.removeTags(record.parent?.ean_texts?.[0]?.official_ingredients)}
              gdsnValue={Util.removeTags(record.gdsn_item?.detail?.ean_text?.official_ingredients)}
            />
          ) : null,
        onCell: (record) => ({
          className: record.is_single && isDiff('official_ingredients', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Nährwerte',
        dataIndex: ['ean_texts', 0, 'official_nutrition'],
        width: 150,
        ellipsis: true,
        render: (__, record) =>
          record.is_single ? (
            <EanTableCell
              field={'official_nutrition'}
              value={Util.removeTags(record.parent?.ean_texts?.[0]?.official_nutrition)}
              gdsnValue={Util.removeTags(record.gdsn_item?.detail?.ean_text?.official_nutrition)}
            />
          ) : null,
        onCell: (record) => ({
          className: record.is_single && isDiff('official_nutrition', record) ? 'bg-light-orange2' : '',
        }),
      },

      {
        title: 'SKU',
        dataIndex: 'sku',
        sorter: true,
        copyable: true,
        ellipsis: true,
        hideInSearch: true,
        width: 100,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.sku, prevRecord.sku),
        render: (dom, record) => {
          return (
            <a
              onClick={() => {
                setCurrentRow({
                  ...record,
                });
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Item base',
        dataIndex: 'item_base',
        valueType: 'digit',
        align: 'right',
        width: 60,
        className: 'text-sm',
        render: (__, record) => {
          const value1 = Util.numberFormat(record.item_base, false, 3, true) + (record.item_base_unit ?? '');
          const value2 =
            Util.numberFormat(record.gdsn_item?.item_base, false, 3, true) + (record.gdsn_item?.item_base_unit ?? '');
          return <EanTableCell field={'item_base'} value={value1} gdsnValue={value2} />;
        },
        onCell: (record) => ({
          className: isDiff('item_base', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Wt. (g)',
        dataIndex: 'weight',
        valueType: 'digit',
        align: 'right',
        width: 60,
        render: (__, record) => (
          <EanTableCell field={'weight'} value={record.weight} gdsnValue={record.gdsn_item?.gross_weight} />
        ),
        onCell: (record) => ({
          className: isDiff('weight', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'W (cm)',
        dataIndex: 'width',
        valueType: 'digit',
        width: 55,
        align: 'right',
        render: (__, record) => (
          <EanTableCell field={'width'} value={record.width} gdsnValue={record.gdsn_item?.width} />
        ),
        onCell: (record) => ({
          className: isDiff('width', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'H (cm)',
        dataIndex: 'height',
        valueType: 'digit',
        width: 55,
        align: 'right',
        render: (__, record) => (
          <EanTableCell field={'height'} value={record.height} gdsnValue={record.gdsn_item?.height} />
        ),
        onCell: (record) => ({
          className: isDiff('height', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'L (cm)',
        dataIndex: 'length',
        valueType: 'digit',
        width: 55,
        align: 'right',
        render: (__, record) => (
          <EanTableCell field={'length'} value={record.length} gdsnValue={record.gdsn_item?.length} />
        ),
        onCell: (record) => ({
          className: isDiff('length', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Hs Code',
        dataIndex: ['item', 'hs_code'],
        width: 60,
        render: (__, record) => (
          <EanTableCell
            field={'item.hs_code'}
            value={record.item?.hs_code}
            gdsnValue={record.gdsn_item?.detail?.hs_code}
          />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('item.hs_code', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Special Filter',
        dataIndex: ['item', 'special_filter'],
        width: 100,
        render: (__, record) => (
          <EanTableCell
            field={'special_filter'}
            value={record.item?.special_filter as string[]}
            gdsnValue={record.gdsn_item?.detail?.diets?.map((x: any) => x.code) ?? []}
          />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('special_filter', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'VAT',
        dataIndex: ['item', 'vat', 'value'],
        sorter: false,
        width: 50,
        ellipsis: true,
        align: 'right',
        hideInSearch: true,
        render: (__, record) => (
          <EanTableCell
            field={'item.vat.id'}
            value={record.item?.vat?.value}
            gdsnValue={record.gdsn_item?.detail?.vat?.value}
          />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('item.vat.id', record) ? 'bg-light-orange2' : '',
        }),
      },
      {
        title: 'Item Name',
        dataIndex: ['item', 'name'],
        width: 120,
        ellipsis: true,
        render: (__, record) => (
          <EanTableCell field={'item.name'} value={record.item?.name} gdsnValue={record.gdsn_item?.detail?.name} />
        ),
        onCell: (record) => ({
          className: record.is_single && isDiff('item.name', record) ? 'bg-light-orange2' : '',
        }),
      },

      {
        title: 'EAN',
        dataIndex: 'ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
        render: (dom, record) => {
          return (
            <a
              onClick={async () => {
                let urlKey = record?.mag_url?.value;
                if (!urlKey)
                  urlKey = await dsGetCustomAttribute(record?.id || 0, {
                    force_update: 0,
                    attribute_code: 'url_key',
                  }).catch((e) => {
                    message.error('Not found SKU on the shop.');
                  });

                if (urlKey) {
                  window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                }
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Single EAN',
        dataIndex: ['parent', 'ean'],
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 120,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.parent?.ean, prevRecord.parent?.ean),
      },
      {
        title: 'Qty/case',
        dataIndex: 'attr_case_qty',
        valueType: 'digit',
        sorter: true,
        align: 'right',
        hideInSearch: true,
        width: 60,
        shouldCellUpdate: (record, prevRecord) => !_.isEqual(record.attr_case_qty, prevRecord.attr_case_qty),
        render: (dom, record) => Util.numberFormat(record.attr_case_qty),
      },

      {
        title: 'Updated on',
        sorter: true,
        dataIndex: 'updated_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        className: 'text-xs c-grey',
        width: 100,
        renderFormItem: (item, { type, defaultRender }) => {
          return defaultRender(item);
        },
        render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
      },

      {
        title: 'ID',
        dataIndex: 'id',
        width: 50,
        search: false,
        sorter: true,
        align: 'center',
        className: 'text-xs c-grey',
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 110,
        shouldCellUpdate(record, prevRecord) {
          return false;
        },
        render: (dom, record, index) => {
          const options = [
            <a
              key="update-item"
              title="Update item"
              onClick={() => {
                handleUpdateItemModalVisible(true);
                setCurrentRow({ ...record });
              }}
            >
              <SnippetsOutlined className="btn-gray" />
            </a>,
            <a
              key="texts"
              title="Update texts"
              onClick={() => {
                handleUpdateTextsModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <FileTextOutlined />
            </a>,
            <a
              key="config"
              title="Update attributes"
              onClick={() => {
                handleUpdateModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <EditTwoTone />
            </a>,
            <a
              key="files"
              title="Update pictures"
              onClick={() => {
                handleUpdatePicturesModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <PictureOutlined />
            </a>,
            <Popconfirm
              key="upsync"
              placement="topRight"
              title={
                <>
                  Are you sure you want to up sync the EAN？ <br />
                  <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
                </>
              }
              overlayStyle={{ width: 350 }}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                if (!record.id) return;
                handleUpSync(record.id);
              }}
            >
              <CloudUploadOutlined className="btn-gray" />
            </Popconfirm>,
          ];
          return <Space>{options.map((option) => option)}</Space>;
        },
      },
    ],
    [handleUpSync, supplierXlsFileId],
  );

  useEffect(() => {
    setColumns(orgColumns);
  }, [orgColumns]);

  const handleResize =
    (index: number) =>
    (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const newColumns = [...columns];
      newColumns[index] = {
        ...newColumns[index],
        width: size.width,
      };
      setColumns(newColumns);
    };

  const mergeColumns: any /* ProColumns<API.Ean>[] */ = (columns ?? []).map((col, index) => ({
    ...col,
    onHeaderCell: (column: ProColumns<API.Ean>) => ({
      width: column.width,
      onResize: handleResize(index) as React.ReactEventHandler<any>,
    }),
  }));

  // vats
  const vats = appSettings.vats;

  // Category trees
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  const handleExportMenuClick: MenuProps['onClick'] = async (e) => {
    setLoadingExport(true);
    const hide = message.loading('Exporting...');
    const formValues = searchFormRef.current?.getFieldsValue();
    switch (e.key) {
      case 'export-core':
        exportEanList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-core-alt':
        exportEanListAlt(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-price':
        exportEanPriceList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'sync-sku':
        break;
    }
  };

  useEffect(() => {
    getImportACList({ is_buying_active: 1 }).then(setFiles).catch(Util.error);

    setSupplierXlsFileId(sn(Util.getSfValues('sf_ean_grid_all')?.supplierXlsFileId));
  }, []);

  const { pageTitle } = usePageContainerTitle(eanComponentProps.route);

  return (
    <PageContainer className={styles.eanListContainer} title={pageTitle}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues(
            'sf_ean_grid_all',
            {
              status: 1,
              minimum_order_qty: 1,
            },
            { sku: location.query?.sku ?? undefined },
          )}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (___) => actionRef.current?.reload(),
            onReset: (___) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Popconfirm
                  key="export-xls"
                  icon={false}
                  title={
                    <div id="eas-export">
                      <ProFormSelect
                        showSearch
                        placeholder="Select a supplier"
                        width={300}
                        request={async (params) => {
                          const res = await getSupplierList(
                            { ...params, pageSize: 100, forDropdown: true },
                            { name: 'ascend' },
                            {},
                          );
                          if (res && res.data) {
                            const tmp = res.data.map((x: API.Item) => ({
                              label: `${x.id} - ${x.name}`,
                              value: x.id,
                            }));
                            return tmp;
                          }
                          return [];
                        }}
                        fieldProps={{
                          onChange(value, option) {
                            exportSupplierRef.current = value;
                          },
                          getPopupContainer: (props) => document.getElementById('eas-export') as any,
                        }}
                        name="by-supplier"
                      />
                    </div>
                  }
                  okText="Export"
                  cancelText="Cancel"
                  placement="left"
                  onConfirm={() => {
                    console.log(exportSupplierRef.current);
                    const hide = message.loading(`Downloading...`, 0);
                    const formValues = searchFormRef.current?.getFieldsValue();
                    exportEanWithCodeList(
                      { ...formValues, supplierId: exportSupplierRef.current },
                      tableConfig?.sorter,
                      tableConfig?.filters,
                    )
                      .then((res) => {
                        if (res) {
                          const downloadUrl = `${API_URL}${(res as any).data.file}`;
                          window.location.href = downloadUrl;
                        }
                      })
                      .catch(Util.error)
                      .finally(() => {
                        hide();
                      });
                  }}
                >
                  <Button icon={<FileExcelOutlined />}>Export</Button>
                </Popconfirm>,
                <Dropdown
                  key="export-menu"
                  disabled={loadingExport}
                  overlay={
                    <Menu
                      onClick={handleExportMenuClick}
                      items={[
                        {
                          label: 'Download EANs',
                          key: 'export-core',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          label: 'Download prices',
                          key: 'export-price',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          type: 'divider',
                        },
                        {
                          label: 'Download As Excel',
                          key: 'export-core-alt',
                          icon: <DownloadOutlined type="primary" />,
                        },
                      ]}
                    />
                  }
                >
                  <Button loading={loadingExport}>
                    <Space>
                      {!loadingExport && <DownloadOutlined type="primary" />}
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>,
              ];
            },
          }}
        >
          {eanTypeProp == 'default' && (
            <ProFormSelect
              name="ean_type_search"
              placeholder="Select type"
              label="Type"
              options={[
                { value: '', label: 'All' },
                { value: 'base', label: 'Single' },
                { value: 've', label: 'Multi' },
              ]}
              fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
            />
          )}
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          {/* <ProFormSelect
            name="producers[]"
            label="Producers"
            placeholder="Please select producers"
            mode="multiple"
            request={getProducerListSelectOptions}
            width={180}
          /> */}
          {formElements}
          <ProFormCheckbox name="noTrademark" label="No Trademark?" />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormSelect
            name="create_type"
            placeholder="All"
            label="Creation mode"
            options={[
              { value: '', label: 'All' },
              { value: '1', label: 'Manually' },
              { value: '2', label: 'Imported' },
            ]}
            fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormGroup size="small">
            <SDatePicker name="created_on_start" label="Date of creation" />
            <SDatePicker name="created_on_end" addonBefore="~" />
            {formElementsIbom}
            <ProFormDigit name={'minimum_order_qty'} label="Min. Qty" width={80} placeholder={'Min. Qty'} />
            <ProFormSelect
              name="product_websites"
              label="Websites"
              width={130}
              mode="multiple"
              placeholder={'Websites'}
              options={appSettings.storeWebsites
                ?.filter((x) => x.code != 'admin')
                ?.map((x) => ({
                  value: `${x.id}`,
                  label: x.name,
                }))}
            />
            <ProFormSelect
              showSearch
              placeholder="Select"
              fieldProps={{
                dropdownMatchSelectWidth: 400,
                onChange(value, option) {
                  setSupplierXlsFileId(value);
                  actionRef.current?.reload();
                },
              }}
              tooltip="Lightgreen Background color indicates EANs existed in Xls File. Otherwise lightorange."
              options={files}
              width="sm"
              name="supplierXlsFileId"
              label="Supplier Xls file"
            />
          </ProFormGroup>
        </ProForm>
      </Card>
      <ProTable<API.Ean, API.PageParams>
        headerTitle={'EANs List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        columnEmptyText=""
        onChange={handleTableChange}
        dataSource={datasource}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ean_grid_all_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={
          (record) => (record.is_single ? 'row-single' : 'row-multi') /*  +
          (supplierXlsFileId ? (record.idInXlsFile ? ' bg-green3' : ' bg-light-orange2') : '') */
        }
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_grid_all', searchFormValues);
          Util.setSfValues('sf_ean_grid_all_p', params);

          setLoading(true);
          return getEanList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
              ean_type: eanTypeProp || 'base',
              with: EAN_DEFAULT_SUMMARY_WITH,
            },
            sort,
            filter,
          )
            .then((res) => {
              setDatasource(res.data);
              // Update the selected row data which should be valid for modal navigation
              if (currentRow?.id && res.data.length) {
                setCurrentRow(res.data.find((x: API.Ean) => x.id == currentRow.id));
              }

              // validate selected rows
              if (selectedRows?.length) {
                const ids = res.data.map((x: API.Ean) => x.id || 0);
                setSelectedRows((prev) => prev.filter((x) => ids.findIndex((x2: number) => x2 == x.id) >= 0));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        columns={mergeColumns}
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRows.map((x) => x.id as React.Key),
          onChange: (dom, selectedRowsChanged) => {
            setSelectedRows(selectedRowsChanged);
          },
        }}
      />
      {selectedRows?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRows.length}</a>&nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            title={
              <>
                <div>Are you sure you want to update data by GDSN and imported supplier XLS?</div>
                <div style={{ paddingTop: 16 }}>
                  <label htmlFor={`isPriceOverride_batch`}>Override price from Xls?&nbsp;&nbsp;</label>
                  <Checkbox id={`isPriceOverride_batch`} />
                </div>
                <div style={{ paddingTop: 16 }}>
                  <label>Update mode?&nbsp;&nbsp;</label>
                  <Radio.Group
                    name={`mode_batch`}
                    buttonStyle="solid"
                    size="small"
                    optionType="button"
                    defaultValue={'3'}
                  >
                    <Radio value={'1'}>
                      A <InfoCircleOutlined title="Update all" />
                    </Radio>
                    <Radio value={'2'}>
                      B <InfoCircleOutlined title="Update all except Name DE" />
                    </Radio>
                    <Radio value={'3'}>
                      C <InfoCircleOutlined title="Update all except Name DE, Short, Short Desc" />
                    </Radio>
                  </Radio.Group>
                </div>
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 400 }}
            onConfirm={() => {
              const gdsnUpdateMode = (document.querySelector(`[name="mode_batch"]:checked`) as HTMLInputElement)?.value;
              const isPriceOverride = (document.getElementById(`isPriceOverride_batch`) as HTMLInputElement)?.checked;
              const hide = message.loading('Batch updating EAN by GDSN or XLS', 0);
              updateEanBatch({
                mode: 'updateByGDSNBatch',
                ids: selectedRows.map((x) => x.id as number),
                data: [],
                isPriceOverride: isPriceOverride,
                supplierXlsFileId: searchFormRef.current?.getFieldValue('supplierXlsFileId'),
                gdsnUpdateMode: gdsnUpdateMode,
              })
                .then((res) => {
                  message.success('Batch updated successfully.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            <Button type="primary" icon={<HighlightOutlined />} title="Batch update from GDSN & XLS">
              GDSN & XLS
            </Button>
          </Popconfirm>
          <Button
            type="primary"
            icon={<DollarOutlined />}
            onClick={() => {
              handleVisibleUpdatePriceFormBulk(true);
            }}
          >
            Set Prices
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateAttributesFormBulk(true);
            }}
          >
            Status & Websites
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateCategoriesFormBulk(true);
            }}
          >
            Categories
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => {
              handleVisibleUpdateVatFormBulk(true);
            }}
          >
            Vat
          </Button>

          <Popconfirm
            title={<>Are you sure you want to up sync selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              setBatchUpSyncProgress(0);
              setBatchModalData({
                title: 'Batch Up Syncing...',
                desc: 'Batch UpSync is in progress. Please wait...',
              });
              handleBatchUpSyncModalVisible(true);

              const totalCount = selectedRows.length;
              const skip = 100 / totalCount;

              for (const x of selectedRows) {
                await usProductFull(Number(x.id))
                  .then((res) => {
                    setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
                  })
                  .catch((error) => {
                    Util.error(error);
                    handleBatchUpSyncModalVisible(false);
                    return;
                  });
              }

              setBatchUpSyncProgress(100);
              handleBatchUpSyncModalVisible(false);
            }}
          >
            <Button type="primary" className="btn-green" icon={<UploadOutlined />}>
              Up Sync
            </Button>
          </Popconfirm>

          {ScrapSystemIds.map((id) => (
            <Popconfirm
              key={id}
              title={<>Are you sure you want to scrap prices from WoS?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ maxWidth: 350 }}
              onConfirm={async () => {
                setBatchUpSyncProgress(0);
                setBatchModalData({
                  title: `Getting prices from ${id}...`,
                  desc: 'Batch action is in progress. Please wait...',
                });
                handleBatchUpSyncModalVisible(true);

                const totalCount = selectedRows.length;
                const skip = 100 / totalCount;

                for (const x of selectedRows) {
                  await scrapWoSPrice(x.ean || '', id)
                    .then((res) => {
                      setBatchUpSyncProgress((prev) => Math.round((prev + skip) * 100) / 100);
                    })
                    .catch((error) => {
                      Util.error(error);
                      setTimeout(() => {
                        handleBatchUpSyncModalVisible(false);
                      }, 1000);
                      return;
                    });
                  await Util.waitTime(400);
                }

                setBatchUpSyncProgress(100);
                actionRef.current?.reload();
                message.success('Successfully updated!');
                setTimeout(() => {
                  handleBatchUpSyncModalVisible(false);
                }, 1000);
              }}
            >
              <Button type="default" icon={<SyncOutlined />}>
                {id} prices
              </Button>
            </Popconfirm>
          ))}
          <Popconfirm
            title={<>Are you sure you want to delete selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleEanRemoveBatch(selectedRows);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <UpdateAttributesFormBulk
        modalVisible={visibleUpdateAttributesFormBulk}
        handleModalVisible={handleVisibleUpdateAttributesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateAttributesFormBulk(false);
        }}
      />

      <UpdateCategoriesFormBulk
        modalVisible={visibleUpdateCategoriesFormBulk}
        handleModalVisible={handleVisibleUpdateCategoriesFormBulk}
        eanIds={selectedRows.map((x) => Number(x.id))}
        treeData={treeData}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateCategoriesFormBulk(false);
        }}
      />

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async () => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateAttributeForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
        gdsn
      />

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow || {}}
        handleNavigation={handleNavigation}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
        gdsn
      />

      <UpdateCategoriesForm
        modalVisible={updateCategoriesModalVisible}
        handleModalVisible={handleUpdateCategoriesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={currentRow || {}}
        treeData={treeData}
        onSubmit={async (values) => {
          setCurrentRow((prev) => ({
            ...prev,
            ...values,
          }));

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateCategoriesModalVisible(false);
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        handleNavigation={handleNavigation}
        initialValues={currentRow || {}}
        onSubmit={async (values) => {
          setCurrentRow((prev) => ({
            ...prev,
            ...values,
          }));

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
        gdsn
      />

      <UpdatePicturesForm
        modalVisible={updatePicturesModalVisible}
        handleModalVisible={handleUpdatePicturesModalVisible}
        handleNavigation={handleNavigation}
        initialValues={{
          id: currentRow?.id,
          parent_id: currentRow?.parent_id,
          files: currentRow?.files || [],
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePicturesModalVisible(false);
        }}
        gdsn
      />

      <UpdateItemForm.default
        modalVisible={updateItemModalVisible}
        handleModalVisible={handleUpdateItemModalVisible}
        initialValues={{
          ...currentRow?.item,
          eanId: currentRow?.id,
          ean: currentRow?.parent?.ean,
          sku: currentRow?.parent?.sku,
        }}
        handleNavigation={handleNavigation}
        onSubmit={async (value) => {
          setCurrentRow((prev) => ({ ...prev, item: { ...prev?.item, ...value } }));
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateItemModalVisible(false);
        }}
        gdsn
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          parent_id: currentRow?.parent_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <EanTasksModals
        modalVisible={visibleEanTasksModal}
        handleModalVisible={setVisibleEanTasksModal}
        reloadList={() => actionRef.current?.reload()}
        itemEan={{
          id: currentRow?.id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
      />

      <UpdateVatFormBulk
        modalVisible={visibleUpdateVatFormBulk}
        handleModalVisible={handleVisibleUpdateVatFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        vats={vats || []}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleVisibleUpdateVatFormBulk(false);
        }}
      />

      <UpdatePriceAttributeFormBulk
        modalVisible={visibleUpdatePriceFormBulk}
        handleModalVisible={handleVisibleUpdatePriceFormBulk}
        eanIds={selectedRows.map((x) => x.id as number)}
        supplierXlsFileId={supplierXlsFileId}
        onSubmit={async (values) => {
          // we skip upsync...
          // if (actionRef.current) {
          // await handleBatchUpSync();
          actionRef.current?.reload();
          // }
        }}
        onCancel={() => {
          handleVisibleUpdatePriceFormBulk(false);
        }}
      />

      <Modal title={batchModalData.title} centered open={batchUpSyncModalVisible} closable={false} footer={null}>
        <p>
          <LoadingOutlined /> {batchModalData.desc ?? 'Batch UpSync is in progress. Please wait...'}
        </p>
        <Progress percent={batchUpSyncProgress} style={{ width: '100%' }} status="active" />
      </Modal>
      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EanAllGdsn;
