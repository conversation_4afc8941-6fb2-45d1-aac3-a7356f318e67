ALTER TABLE `user`
    ADD COLUMN `initials` varchar(31) default NULL COMMENT 'initials' AFTER `name`
;

ALTER TABLE `item_ean`
    ADD COLUMN `fs_special_discount` varchar(31) default NULL COMMENT 'discount text' AFTER `fs_ebay_exp_special`
;

ALTER TABLE `item_ean`
    ADD COLUMN `fs_special_badge` varchar(255) default NULL COMMENT 'special badge' AFTER `fs_special_discount`
;


CREATE TABLE `sys_dict` (
                            `code` varchar(32) NOT NULL COMMENT 'UQ-Code',
                            `type` varchar(32) NOT NULL COMMENT 'type: order type, order category',
                            `label` varchar(512) NOT NULL COMMENT 'code label',
                            `status` tinyint(1) DEFAULT 1 COMMENT 'active status',
                            `sort` int(11) NOT NULL DEFAULT 0 COMMENT 'sort value',
                            `value` longtext DEFAULT NULL COMMENT 'extra value',
                            `parent_code` varchar(32) DEFAULT NULL COMMENT 'Parent Code FK',
                            `desc` text DEFAULT NULL COMMENT 'Description',
                            `settings` longtext DEFAULT NULL COMMENT 'Extra setting in JSON',
                            PRIMARY KEY (`code`),
                            UNIQUE KEY `IDX_sys_dict_code` (`code`),
                            KEY `IDX_sys_dict_type` (`type`),
                            KEY `FK_sys_dict_parent_code` (`parent_code`),
                            KEY `IDX_sys_dict_label` (`label`),
                            CONSTRAINT `FK_sys_dict_parent_code` FOREIGN KEY (`parent_code`) REFERENCES `sys_dict` (`code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='system dictionary table';