import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateEan } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import { getItemList } from '@/services/foodstore-one/Item/item';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEan(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Ean>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Ean>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

/**
 * This component is not used!
 *
 * @param props UpdateFormProps
 * @returns React.FC
 */
const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update EAN'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      className={props.initialValues?.is_single ? 'm-single' : 'm-multi'}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'EAN is required',
          },
        ]}
        width="md"
        name="ean"
        label="EAN"
      />
      <ProFormSelect
        showSearch
        placeholder="Select an Item"
        request={async (params) => {
          const res = await getItemList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Item) => ({
              label: `${x.id} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        rules={[
          {
            required: true,
            message: 'Item is required',
          },
        ]}
        width="md"
        name="item_id"
        label="Item"
      />
      <ProFormDigit width="sm" name="attr_case_qty" label="Qty per Case" min={1} fieldProps={{ precision: 0 }} />
      <ProFormDigit width="sm" name="weight" label="Weight" min={1} addonAfter="kg" />
      <ProFormDigit width="sm" name="width" label="Width" min={1} addonAfter="cm" />
      <ProFormDigit width="sm" name="height" label="height" addonAfter="cm" min={1} />
      <ProFormDigit width="sm" name="length" label="Length" min={1} addonAfter="cm" />
    </ModalForm>
  );
};

export default UpdateForm;
