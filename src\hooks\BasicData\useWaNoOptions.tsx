import { getWaNoACList } from '@/services/foodstore-one/Offer/offer-item-shipped';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion of WA No List
 */
export default (
  defaultParams?: Record<string, any>,
  formRef?: React.MutableRefObject<ProFormInstance | undefined>,
  eleOptions?: any,
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [waNoOptions, setWaNoOptions] = useState<DefaultOptionType[]>([]);
  // selected offer
  const [waNo, setWaNo] = useState<DefaultOptionType>();

  const searchWaNoOptions = useCallback(
    async (params?: Record<string, any>) => {
      setLoading(true);

      return getWaNoACList({ ...defaultParams, ...params })
        .then((res) => {
          setWaNoOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  useEffect(() => {
    searchWaNoOptions().then((res) => {
      const offer_id = formRef?.current?.getFieldValue('offer_id');
      if (offer_id) {
        const found = (res || []).find((x: any) => x.id == offer_id);
        setWaNo(found);
      }
    });
  }, [searchWaNoOptions]);

  useEffect(() => {
    if (!waNoOptions?.length) return;
    setWaNo((prev: any) => {
      if (!prev) return prev;
      let newItem = { ...prev };

      if (newItem.id) {
        const found = waNoOptions.find((x: any) => x.id == newItem.id);

        if (found) {
          newItem = { ...newItem, ...found };
        }
      }
      return newItem;
    });
  }, [waNoOptions]);

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        name="wa_no"
        label={'WA No'}
        placeholder="Please select WA No"
        mode="single"
        showSearch
        options={waNoOptions}
        required={eleOptions?.required}
        rules={
          eleOptions?.required
            ? [
                {
                  required: true,
                  message: 'WA No is required',
                },
              ]
            : []
        }
        fieldProps={{
          loading: loading,
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          onChange: (value, option) => {
            setWaNo(option as any);
            eleOptions?.onChange(value, option);
          },
        }}
        labelCol={eleOptions?.labelCol}
        width={eleOptions?.width ?? 100}
      />
    );
  }, [eleOptions?.required, waNoOptions, loading, searchWaNoOptions]);

  return {
    waNoOptions,
    searchWaNoOptions,
    loading,
    waNo,
    setWaNo,
    formElements,
    setWaNoOptions,
  };
};
