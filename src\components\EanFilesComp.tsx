import { Image } from 'antd';

type EanFilesCompProps = {
  files?: API.File[];
  width?: number;
  outlineBorder?: boolean;
};
const EanFilesComp: React.FC<EanFilesCompProps> = ({ files, width, outlineBorder, ...rest }) => {
  return (
    <div style={{ width: width ?? 40, minHeight: width ?? 40, border: outlineBorder ? '1px solid #ddd' : 'none' }}>
      {files ? (
        <Image.PreviewGroup>
          {files &&
            files.map((file, ind) => (
              <Image
                key={file.id}
                src={file.thumb_url}
                preview={{
                  src: file.url,
                }}
                wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                width={width ?? 40}
                style={{ border: outlineBorder ? '1px solid #ddd' : 'none' }}
              />
            ))}
        </Image.PreviewGroup>
      ) : null}
    </div>
  );
};

export default EanFilesComp;
