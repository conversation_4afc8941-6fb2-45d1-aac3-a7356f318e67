ALTER TABLE `xmag_quote`
    ADD COLUMN `customer_id` INT UNSIGNED NULL COMMENT 'Customer ID' AFTER `status`,
    CHANGE `customer` `customer` TEXT CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Customer Detail in JSON',
    ADD INDEX `IDX_xmag_quote_customer_id` (`customer_id`);

-- Fill customer ID in Quote table for quick search.
UPDATE xmag_quote
SET customer_id = JSON_EXTRACT(customer, '$.id')
WHERE customer_id IS NULL;

ALTER TABLE `offer`
    ADD COLUMN `customer_id` INT UNSIGNED NULL COMMENT 'Customer ID' AFTER `quote_id`,
    ADD INDEX `IDX_offer_customer_id` (`customer_id`);

-- Fill customer ID in Offer.
update offer join xmag_quote on offer.`quote_id` = xmag_quote.id
set offer.customer_id = xmag_quote.`customer_id`
where offer.customer_id is null;

