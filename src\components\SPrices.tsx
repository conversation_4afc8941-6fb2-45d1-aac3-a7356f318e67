import { EURO } from '@/constants';
import Util from '@/util';
import type { CSSProperties } from 'react';

type SPricesProps = {
  price: any;
  vat: any;
  hideNet?: boolean;
  hideGross?: boolean;
  isGross?: boolean;
  direction?: 'horizontal' | 'vertical';
  showZero?: boolean;
  showCurrency?: boolean;
  noTextSmallCls?: boolean;
  style?: CSSProperties;
};

const SPrices = ({
  price,
  vat,
  hideNet,
  hideGross,
  isGross,
  direction,
  showZero,
  style,
  showCurrency,
  noTextSmallCls,
}: SPricesProps) => {
  const prices = Util.fPrices(price, vat, false, !isGross);

  const netWrapProps: any = {};
  if (hideGross) {
    netWrapProps.title = `Gross: ${Util.numberFormat(prices[1], showZero, 2)}${showCurrency && prices[1] ? EURO : ''}`;
  }

  const grossWrapProps: any = {};
  if (hideNet) {
    grossWrapProps.title = `Net: ${Util.numberFormat(prices[0], showZero, 2)}${showCurrency && prices[0] ? EURO : ''}`;
  }

  return (
    <>
      {!hideNet && (
        <div
          className={`text-right ${direction === 'horizontal' ? (noTextSmallCls ? '' : ' text-small') : ''}`}
          style={{ ...style, display: direction === 'horizontal' ? 'inline-block' : 'block' }}
          {...netWrapProps}
        >
          {Util.numberFormat(prices[0], showZero, 2)}
          {showCurrency && (prices[0] || showZero) ? EURO : ''}
        </div>
      )}
      {!hideGross && (
        <div
          className={`gross-price text-right ${
            direction === 'horizontal' ? (noTextSmallCls ? '' : ' text-small') : ''
          }`}
          style={{ ...style, display: direction === 'horizontal' ? 'inline-block' : 'block' }}
          {...grossWrapProps}
        >
          {direction === 'horizontal' && !hideNet && prices[0] && prices[1] ? <>&nbsp;/&nbsp;</> : ''}
          {Util.numberFormat(prices[1], showZero, 2)}
          {showCurrency && (prices[0] || showZero) ? EURO : ''}
        </div>
      )}
    </>
  );
};

export default SPrices;
