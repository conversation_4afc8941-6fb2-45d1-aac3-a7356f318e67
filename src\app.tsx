// @ts-nocheck
import type { BasicLayoutProps, Settings as LayoutSettings } from '@ant-design/pro-layout';
import { SettingDrawer } from '@ant-design/pro-layout';
import { PageLoading } from '@ant-design/pro-layout';
import type { RunTimeLayoutConfig } from 'umi';
import { history, Link } from 'umi';
import RightContent from '@/components/RightContent';
import Footer from '@/components/Footer';
import { BackTop, notification, Space, Typography } from 'antd';
import { currentUser as queryCurrentUser } from './services/foodstore-one/api';
import { BookOutlined, LinkOutlined } from '@ant-design/icons';
import defaultSettings from '../config/defaultSettings';
import type { RequestConfig } from '@@/plugin-request/request';
import { LS_TOKEN_NAME, OrderUserActionLogType, OrderUserActionLogNote } from '@/constants';
import DynamicIcon from './components/DynamicIcon';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';
// import Authorized from '@/utils/Authorized';
import { notifySuccess } from './util';
import { createOrderUserActionLog } from './services/foodstore-one/Magento/order-user-action-log';

export const initialStateConfig = {
  loading: <PageLoading />,
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await queryCurrentUser();
      return msg;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // If it is not the login page, execute
  if (history.location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();

    return {
      fetchUserInfo,
      currentUser,
      settings: {
        ...defaultSettings,
      },
    };
  }

  return {
    fetchUserInfo,
    settings: defaultSettings,
  };
}

/**
 * We build new menus from routes config.ts
 *
 * @deprecated
 *
 * @param roleId
 * @param userId
 * @param parent
 * @param parentAccess
 * @returns
 */
/* const getMenusFromRouter = (roleId: number, userId: number, parent: any, parentAccess: any) => {
  const isAdmin = !!userId && roleId === 1;
  const isEditor = !!userId && roleId === 2;

  const menuData = [];
  const children = parent ? parent.routes : routes;
  children.forEach((routeItem) => {
    const menuItem = { ...routeItem, access: routeItem.access ?? parentAccess };

    let hasPermission = false;
    if (isAdmin) {
      hasPermission = true;
    } else {
      if (roleId == 0) {
        hasPermission = menuItem.access != 'canAdmin';
      } else {
        hasPermission = editorRoutes.includes(routeItem.path);
      }
    }

    if (hasPermission) {
      if (routeItem.routes?.length) {
        menuItem.routes = getMenusFromRouter(roleId, userId, routeItem, menuItem.access);
      }

      menuData.push(menuItem);
    }
  });

  return menuData;
}; */

/**
 * Menu data renderer
 * @deprecated NOT use this:
 * @param menuList
 * @returns  any[]
 */
/* const menuDataRender = (menuList: MenuDataItem[]) => {
  return menuList.map((item) => {
    const localItem = {
      ...item,
      icon: typeof item.icon == 'string' ? <DynamicIcon type={item.icon} /> : item.icon,
      children: item.children ? menuDataRender(item.children) : [],
    };

    return localItem;
    // return Authorized.check(item.authority, localItem, null) as MenuDataItem;
  });
}; */

// ProLayout api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = (initData) => {
  const { initialState, setInitialState } = initData;
  const pathCls = location.pathname.replaceAll('/', '_');
  return {
    ...initialState?.settings,
    headerRender(props, defaultDom) {
      return (
        <div id="app-header" className={pathCls}>
          {defaultDom}
        </div>
      );
      // return defaultDom;
    },
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    footerRender: () => <Footer />,
    onPageChange: (location: any) => {
      // If not logged in, redirect to login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }

      // If logged in, we record page visit
      if (location.pathname == '/orders/order-detail' || location.pathname == '/orders/order-detail-packing') {
        let type =
          location.pathname == '/orders/order-detail'
            ? OrderUserActionLogType.OrderDetail
            : OrderUserActionLogType.OrderDetailWH;
        createOrderUserActionLog({ type, note: OrderUserActionLogNote.Visit });
      }
    },
    breadcrumbRender: () => undefined,
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI Docs</span>
          </Link>,
          <Link to="/~docs" key="docs">
            <BookOutlined />
            <span>Documentation</span>
          </Link>,
        ]
      : [],

    menu: {
      locale: false,
      params: { user_id: initialState?.currentUser?.user_id, role: initialState?.currentUser?.role },
      request: async (params, defaultMenuData) => {
        // initialState.currentUser contains all user information
        if (!initialState?.currentUser?.user_id) return [];
        return defaultMenuData;

        /* const menuData = getMenusFromRouter(params.role, params.user_id);
        // const menuData = defaultMenuData;

        const isAdmin = initialState?.currentUser?.role === 1;
        const isEditor = initialState?.currentUser?.role === 2;

        console.log('[m] params', params);
        console.log('[m]', menuData);
        console.log('[m] Default Menudata: ', defaultMenuData); */

        /* const taskGroups = await getTaskGroupACList();
        const menuData = getMenusFromRouter(
          initialState?.currentUser?.role,
          initialState?.currentUser?.user_id,
        ); */

        // update children of TaskBoard Menu
        /* const boardMenu = menuData.find((x) => x.path == '/task-board');

        const tmp = taskGroups
          ?.filter(
            (x) =>
              !x.task_id && x.code != 'OrderIn' && x.code != 'OrderOut' && x.code != 'OrderOutHead',
          )
          ?.map((x) => ({
            path: `/task-board/${x.id}`,
            name: x.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          }));
        boardMenu.children = tmp;

        // update children of TaskBoard Menu
        const orderMenu = menuData.find((x) => x.path == '/order');
        let row = taskGroups?.find((x) => x.code == 'OrderIn');

        if (row && orderMenu.routes.findIndex((m) => m.path == `/task-board/${row.id}`) < 0) {
          orderMenu.routes.push({
            type: 'divider',
          });
          orderMenu.routes.push({
            path: `/task-board/${row.id}`,
            name: row.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          });
        }

        row = taskGroups?.find((x) => x.code == 'OrderOut');
        if (row && orderMenu.routes.findIndex((m) => m.path == `/task-board/${row.id}`) < 0) {
          orderMenu.routes.push({
            path: `/task-board/${row.id}`,
            name: row.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          });
        }

        row = taskGroups?.find((x) => x.code == 'OrderOutHead');
        if (row && orderMenu.routes.findIndex((m) => m.path == `/task-board/${row.id}`) < 0) {
          orderMenu.routes.push({
            type: 'divider',
          });
          orderMenu.routes.push({
            path: `/task-board/${row.id}`,
            name: row.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          });
        }

        orderMenu.routes.push({
          path: `/order/task-board-calendar`,
          name: 'Task Calendar',
          icon: 'CalendarOutlined',
          component: './TaskBoard/TaskCalendar',
        }); */

        // return menuData;
      },
      className: `app_${pathCls}`,
    },
    menuHeaderRender: undefined,
    /* menuRender(props, defaultDom) {
      console.log('===>menuRender', props, defaultDom);
      return defaultDom;
    }, */
    menuContentRender(props, defaultDom) {
      console.log('===>menuContentRender', props, defaultDom);
      return defaultDom;
    },
    menuItemRender(item, defaultDom, menuProps) {
      return item.children?.length ? (
        <Link to={item.path} className={'test-mk'}>
          <Space size={8} className="test-mk-space">
            {typeof item.icon == 'string' ? <DynamicIcon type={item.icon} /> : item.icon}
            {item.name}
          </Space>
        </Link>
      ) : (
        <Link to={item.path} className={'test-mk'}>
          <Space size={8}>
            {typeof item.icon == 'string' ? <DynamicIcon type={item.icon} /> : item.icon}
            {item.name}
          </Space>
        </Link>
      );
    },
    // menuDataRender, // NOT USE.
    unAccessible: <div>Your are not authorized</div>,
    // Add one loading status
    childrenRender: (children: Element, props: BasicLayoutProps) => {
      if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {!props.location?.pathname?.includes('/login') && isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
          <BackTop />
        </>
      );
    },
  };
};

const authHeaderInterceptor = (url: string, options: any) => {
  const token = localStorage.getItem(LS_TOKEN_NAME);
  const authHeader = {};
  if (token /*  && (options.method !== 'GET' || options.widthToken) */) {
    // @ts-ignore
    authHeader.Authorization = `Bearer ${token}`;
  }
  return {
    url: `${url}`,
    options: { ...options, interceptors: true, headers: authHeader },
  };
};

const responseInterceptor = async (response: any, options: any) => {
  if (options.url) {
  }

  return response;
};
console.log('Node ENV: ', isDev);
export const request: RequestConfig = {
  prefix: API_URL,
  errorConfig: {
    adaptor: (resData: any, ctx: any) => {
      // console.log('--- errorConfig adaptor ----');
      // console.log(resData, ctx);
      // console.log(ctx);
      if (401 === resData.code) {
        localStorage.setItem(LS_TOKEN_NAME, '');
        history.push(loginPath);
      }

      const flashMsg = resData?.messageFlash;
      if (flashMsg) {
        for (const key in flashMsg) {
          const msgStr = (
            <ul className="msg">
              {flashMsg[key].map((x: any, ind: number) => {
                const subMsgList = x.split('\n');
                if (subMsgList.length > 1) {
                  return (
                    <li key={ind}>
                      {subMsgList.map((y: any, yind: number) => (
                        <div key={yind}>{y}</div>
                      ))}
                    </li>
                  );
                } else return <li key={ind}>{x}</li>;
              })}
            </ul>
          );
          if (key == 'w') {
            notification.warning({
              message: (
                <Typography.Text
                  style={{ fontSize: 15, fontWeight: 'bold' }}
                  copyable={{ text: flashMsg[key].join('\n') }}
                >
                  Warnings:
                </Typography.Text>
              ),
              description: msgStr,
              duration: 0,
              placement: 'top',
            });
          } else if (key == 'e') {
            notification.error({
              message: (
                <Typography.Text
                  style={{ fontSize: 15, fontWeight: 'bold' }}
                  copyable={{ text: flashMsg[key].join('\n') }}
                >
                  Errors:
                </Typography.Text>
              ),
              description: msgStr,
              duration: 0,
              placement: 'top',
            });
          } else if (key == 's') {
            notifySuccess(msgStr);
          } else if (key == 'i') {
            notification.info({
              message: (
                <Typography.Text
                  style={{ fontSize: 15, fontWeight: 'bold' }}
                  copyable={{ text: flashMsg[key].join('\n') }}
                >
                  Information:
                </Typography.Text>
              ),
              description: msgStr,
              duration: 0,
              placement: 'top',
            });
          }
        }
      }

      return {
        ...resData,
        success: /* (resData.code >= 200 && resData.code <= 210) ||  */ resData.status === 'success',
        errorCode: resData.code,
        errorMessage: resData.status === 'success' ? undefined : resData.message,
        // showType: resData.status === 'error'? message.error : 0,
        showType: 0,
      };
    },
  },
  middlewares: [],
  requestInterceptors: [authHeaderInterceptor],
  responseInterceptors: [responseInterceptor],
};

export interface response {
  status: string; // if request is success
  code?: number; // code for errorType
  message?: any;
  // data?: any;
  showType?: number; // error display type: 0 silent; 1 message.warn; 2 message.error; 4 notification; 9 page
  traceId?: string; // Convenient for back-end Troubleshooting: unique request ID
  host?: string; // onvenient for backend Troubleshooting: host of current access server
}
