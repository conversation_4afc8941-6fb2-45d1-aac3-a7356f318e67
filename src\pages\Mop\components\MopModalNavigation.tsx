import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import React from 'react';
import type { ButtonProps } from 'antd';
import { Button, Space } from 'antd';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons/lib/icons';
import type { HandleNavFuncType, NavigationModelsType } from '../hooks/useMopModalNavigation';

const DefaultButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  style: { width: 24, height: 24, fontSize: 14 },
};

export type MopModalNavigationProps = {
  modalName?: NavigationModelsType;
  eanId?: number;
  style?: any;
  handleNavigation?: HandleNavFuncType;
};

const MopModalNavigation: React.FC<MopModalNavigationProps> = ({ modalName, eanId, style, handleNavigation }) => {
  return (
    <Space.Compact style={style}>
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'prevModal', eanId)}
        icon={<ArrowLeftOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'nextModal', eanId)}
        icon={<ArrowRightOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'prev', eanId)}
        icon={<ArrowUpOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        onClick={() => handleNavigation?.(modalName, 'next', eanId)}
        icon={<ArrowDownOutlined />}
      />
    </Space.Compact>
  );
};

export default MopModalNavigation;
