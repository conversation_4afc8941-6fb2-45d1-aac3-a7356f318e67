declare namespace API {
  type MopProduct = {
    id?: number;
    sku?: string;
    sku_idx?: number;
    name?: string;
    status?: number;
    product_type?: string;
    visibility?: number;
    product_websites?: number[];
    attribute_set_code?: string;
    customer_group?: string;
    note?: string;
    detail?: Record<string, any>;
  } & UpdaterData & CreatorData & {
    files?: File[];
    files_map?: File[];
    product_texts?: MopProductText[];
    product_prices?: MopProductPrice[];
    product_price_gfc?: MopProductPrice;
    product_price?: MopProductPrice;
    product_text_de?: MopProductText;
    files_map?: any;
    mag_product?: MagProduct;
  } & {
    upSyncMessage?: any;
    m_status?: boolean | number;
    m_website_ids?: string;
  };

  type MopProductPrice = {
    mop_product_id?: number;
    price_type_id?: number;
    price?: number;
    min_qty?: number;
    start_date?: string;
    end_date?: string;
    created_on?: string;
    updated_on?: string;
  } & {
    product?: MopProduct;
  }

  type MopProductText = {
    mop_product_id?: number;
    lang?: string;
    name?: string;
    short_description?: string;
    description?: string;
    meta_title?: string;
    meta_keywords?: string;
    meta_description?: string;
    settings?: Record<string, any>;
  } & {
    product?: MopProduct;
  }

  type MopProductFile = {

    mop_product_id?: number;
    file_id?: number;
    position?: number;
    is_main?: number;
    is_parent_file?: boolean;
    types?: string[];
    disabled
    label
    mag_id?: number;
  }
}
