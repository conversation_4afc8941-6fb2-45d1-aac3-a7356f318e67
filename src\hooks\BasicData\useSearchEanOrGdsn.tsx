import { getEanDetail, getEanDetailOrGdsnEan } from '@/services/foodstore-one/Item/ean';
import { ProFormItem } from '@ant-design/pro-form';
import { InputProps, message, Typography } from 'antd';
import { debounce } from 'lodash';
import { InputHTMLAttributes, useCallback, useMemo, useRef, useState } from 'react';

/**
 * Searching EAN.
 */
export default (eleOptions?: InputProps & { with?: string }) => {
  const [loading, setLoading] = useState<boolean>(false);

  const [itemEan, setItemEan] = useState<API.Ean | null>(null);

  const fieldRef = useRef<HTMLInputElement>();

  // ---------------------------------------------------------------------------------
  // Search EAN
  // ---------------------------------------------------------------------------------
  const handleSearchEan = async (v: string, cb: any) => {
    if (!v) {
      cb(null);
      return;
    }
    message.destroy();
    setLoading(true);
    const hide = message.loading('Searching EAN...', 0);
    return getEanDetailOrGdsnEan({ eanExact: v, with: 'suppliers,' + (eleOptions?.with || '') })
      .then((res) => {
        cb(res);
        setItemEan(res);
        return res;
      })
      .catch(() => {
        cb(null);
        setItemEan(null);
      })
      .finally(() => {
        setLoading(false);
        hide();
      });
  };

  const debouncedHandleSearchEan = useCallback(
    debounce((newValue, cb) => handleSearchEan(newValue, cb), 330),
    [],
  );

  const formElements = useMemo(() => {
    return (
      <>
        <input
          className="ant-input ant-input-lg"
          ref={fieldRef as any}
          style={{ ...eleOptions?.style }}
          onChange={(e) => {
            const value = e.target.value;

            debouncedHandleSearchEan(value, (eanData: API.Ean) => {
              if (eanData) {
                setItemEan(eanData);
              } else {
                setItemEan(null);
              }
            });
          }}
        />

        {itemEan?.sku && (
          <>
            <ProFormItem label=" " style={{ marginBottom: 0 }} colon={false}>
              <Typography.Text
                style={{ display: 'inline-block', fontSize: 14 }}
                copyable={{ text: itemEan?.sku || '' }}
              >
                {itemEan?.sku} {itemEan?.is_single ? '' : `(Qty.Pkg: ${itemEan?.attr_case_qty ?? '-'})`}
              </Typography.Text>
            </ProFormItem>
            <ProFormItem label=" " style={{ marginBottom: 0 }} colon={false}>
              <Typography.Text
                style={{ display: 'inline-block', fontSize: 14 }}
                copyable={{ text: itemEan?.ean || '' }}
              >
                {itemEan?.ean}
              </Typography.Text>
            </ProFormItem>
          </>
        )}

        {/* <ProFormItem label="Name" style={{ marginTop: 4 }}>
          <Typography.Text style={{ display: 'inline-block', fontSize: 14 }} copyable>
            {itemEan?.ean_texts?.[0]?.name}
          </Typography.Text>
        </ProFormItem> */}
      </>
    );
  }, [eleOptions, loading, debouncedHandleSearchEan]);

  return { itemEan, setItemEan, fieldRef, loading, eleOptions, formElements, debouncedHandleSearchEan };
};
