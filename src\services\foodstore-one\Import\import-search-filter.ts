import _ from 'lodash';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/import/import-search-filter';

/**
 * Get all search filters of import
 *
 * GET /api/import/import-search-filter */
export async function getImportSearchFilters(params?: Partial<API.ImportSearchFilter>) {
  return request<API.ResultObject<API.ImportSearchFilter[]>>(`${urlPrefix}`, {
    method: 'GET',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * Update all search filters of import
 *
 * PUT /api/import/import-search-filter */
export async function updateImportSearchFilters(params?: Partial<API.Import> & { trademark: string }) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'PUT',
    params,
    paramsSerializer,
  }).then((res) => res.message);
}
