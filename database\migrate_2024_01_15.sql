drop table if exists xmag_order_label;

CREATE TABLE `xmag_order_label`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `entity_id`    int(11)      NOT NULL COMMENT 'FK: Order ID',
    `service_name` varchar(255) NOT NULL COMMENT 'API service provider. e.g. DHL, GLS',
    `ref_no`       varchar(255) NOT NULL COMMENT 'Shipment Ref No.',
    `track_id`     varchar(255) NOT NULL COMMENT 'Parcel No or Track ID',
    `parcel_no`    varchar(255) default NULL COMMENT 'Explicit parcel No',
    `pos`          int          default NULL COMMENT 'Position in packages',
    `file_path`    varchar(255) NOT NULL COMMENT 'Label PDF file path',
    `file_type`    varchar(31)  DEFAULT NULL COMMENT 'File type. e.g. pdf or png',
    `detail`       text         DEFAULT NULL COMMENT 'Detail info in JSON',
    `created_on`   datetime     DEFAULT NULL,
    `created_by`   int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `xmag_order_label_service_name` (`service_name`),
    KEY `xmag_order_label_track_id` (`track_id`),
    KEY `xmag_order_label_parcel_no` (`parcel_no`),
    KEY `xmag_order_label_created_on` (`created_on`),
    CONSTRAINT `FK_xmag_order_label_entity_id` FOREIGN KEY (`entity_id`) REFERENCES `xmag_order` (`entity_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
