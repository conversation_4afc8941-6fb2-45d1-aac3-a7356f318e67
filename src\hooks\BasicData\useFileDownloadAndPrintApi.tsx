import { DictCode, LS_TOKEN_NAME, OrderUserActionLogNote, OrderUserActionLogType } from '@/constants';
import { downloadFileB64 } from '@/services/foodstore-one/api';
import { createOrderUserActionLog } from '@/services/foodstore-one/Magento/order-user-action-log';
import Util from '@/util';
import { message } from 'antd';
import type { PrintTypes } from 'print-js';
import printJS from 'print-js';
import { useCallback, useState } from 'react';
import { useModel } from 'umi';

export const isReturnLabel = (labelInfo?: API.OrderLabel) => labelInfo?.ref_no?.endsWith(',Return');

export const getPrinterName = (labelInfo?: API.OrderLabel, getDictByCode?: any) => {
  if (isReturnLabel(labelInfo) || labelInfo?.service_name == 'Delivery Note') {
    return getDictByCode(DictCode.SHIPPING_PRINTER_DELIVERY_NOTE);
  }

  if (labelInfo?.service_name == 'DHL') {
    return getDictByCode(DictCode.SHIPPING_PRINTER_DHL);
  } else if (labelInfo?.service_name == 'GLS') {
    return getDictByCode(DictCode.SHIPPING_PRINTER_GLS);
  } else if (labelInfo?.service_name == 'DPD') {
    return getDictByCode(DictCode.SHIPPING_PRINTER_DPD);
  }
};

/**
 * Direct Print API call
 */
const useFileDownloadAndPrintApi = (defaultParams?: Record<string, any>) => {
  const { getDictByCode } = useModel('app-settings');

  const [loading, setLoading] = useState<boolean>(false);

  const callDirectPrintApi = useCallback(
    async (labelInfo?: API.OrderLabel, b64?: string, options?: { logType?: OrderUserActionLogType }) => {
      setLoading(true);
      const hide2 = message.loading('Calling printer service...', 0);
      try {
        const res = await fetch(getDictByCode(DictCode.PRINTER_API_URL), {
          method: 'POST',
          // mode: 'no-cors', // custom headers will be dropped.
          cache: 'no-cache',
          mode: 'cors',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + localStorage.getItem(LS_TOKEN_NAME),
            AccessToken: getDictByCode(DictCode.PRINTER_API_TOKEN),
          },
          body: JSON.stringify({
            // printerName: 'Office - EPSON WF-6590 Series', // production default printer
            printerName: getPrinterName(labelInfo, getDictByCode),
            PrintData: b64,
          }),
        });

        const resParsed = await res.json();

        if (resParsed.success) {
          message.success('Called successfully. Please check printed files in printer.');
          createOrderUserActionLog({
            type: options?.logType ?? OrderUserActionLogType.OrderDetail,
            note: OrderUserActionLogNote.Printed,
            order_id: labelInfo?.entity_id,
            detail: {
              service_name: labelInfo?.service_name,
              track_id: labelInfo?.track_id,
              parcel_no: labelInfo?.parcel_no,
              ref_no: labelInfo?.ref_no,
            },
          });
        } else {
          message.info(
            `Error occurred: ${labelInfo?.service_name} : ${getPrinterName(labelInfo, getDictByCode)}: ` +
              resParsed.data,
          );

          createOrderUserActionLog({
            type: options?.logType ?? OrderUserActionLogType.OrderDetail,
            note: OrderUserActionLogNote.PrintFailed,
            order_id: labelInfo?.entity_id,
            detail: {
              service_name: labelInfo?.service_name,
              track_id: labelInfo?.track_id,
              parcel_no: labelInfo?.parcel_no,
              ref_no: labelInfo?.ref_no,
            },
          });
        }
        return Promise.resolve(true);
      } catch (exception) {
        console.log('Exception!!!', exception);
        message.error(
          `Error occurred: ${labelInfo?.service_name} : ${getPrinterName(labelInfo, getDictByCode)}: ${exception}`,
        );

        createOrderUserActionLog({
          type: options?.logType ?? OrderUserActionLogType.OrderDetail,
          note: OrderUserActionLogNote.PrintFailed,
          order_id: labelInfo?.entity_id,
          detail: {
            service_name: labelInfo?.service_name,
            track_id: labelInfo?.track_id,
            parcel_no: labelInfo?.parcel_no,
            ref_no: labelInfo?.ref_no,
          },
        });
      } finally {
        hide2();
      }
      return Promise.reject('Error occurred.');
    },
    [getDictByCode],
  );

  /**
   * Download file's base64 data from URL and call printer service or open a browser printer or open new tab
   */
  const downloadB64AndCallDirectPrintApi = useCallback(
    async (
      labelInfo?: API.OrderLabel,
      mode: 'printerApi' | 'browserPrinter' = 'printerApi',
      options?: { logType?: OrderUserActionLogType },
    ) => {
      const hide = message.loading('Getting label file...', 0);
      const file_type = (labelInfo?.file_type || 'pdf') as PrintTypes;
      console.log('Printing lable info: ', labelInfo);
      return downloadFileB64({ type: file_type, b64: true, key: labelInfo?.file_path || '' })
        .then((b64) => {
          if (!b64) {
            message.error('Base 64 data is empty.');
            return;
          }
          if (mode == 'printerApi') {
            callDirectPrintApi(labelInfo, b64, options);
          } else {
            if (Util.isFirefox()) {
              window.open(`${API_URL}/api/${labelInfo?.url}`, '_blank');
            } else {
              printJS({
                printable: b64,
                type: file_type,
                base64: true,
                showModal: true,
                onPrintDialogClose: () => {
                  createOrderUserActionLog({
                    type: options?.logType ?? OrderUserActionLogType.OrderDetail,
                    note: OrderUserActionLogNote.PrintTried,
                    order_id: labelInfo?.entity_id,
                    detail: {
                      service_name: labelInfo?.service_name,
                      mode,
                      track_id: labelInfo?.track_id,
                      parcel_no: labelInfo?.parcel_no,
                      ref_no: labelInfo?.ref_no,
                    },
                  });
                },
              });
            }
          }
        })
        .catch(Util.error)
        .finally(hide);
    },
    [callDirectPrintApi],
  );

  return { callDirectPrintApi, loading, downloadB64AndCallDirectPrintApi };
};

export default useFileDownloadAndPrintApi;
