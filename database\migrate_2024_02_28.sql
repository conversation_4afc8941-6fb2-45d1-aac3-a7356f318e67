INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`, `sort`)
VALUES ('MOP_ITEM_CATEGORY_IDS', 'sys config', '[88]', 'Category IDs of Magento Offer Product', 'JSON format', 100);

CREATE TABLE `mop_product`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `sku`                varchar(100)   DEFAULT NULL,
    `name`               varchar(255)   DEFAULT NULL,
    `status`             tinyint(4)     DEFAULT 1 comment '0: Draft, 1: Active, 2: Passive',
    `product_type`       varchar(30)    DEFAULT 'simple',
    `visibility`         int(4)         DEFAULT 2 comment '1: Not Visible Individually,2:Catalog, 3: Search, 4: Catalog, Search',
    `product_websites`   varchar(255)   DEFAULT NULL comment 'CSV format. e.g. 1,2',
    `attribute_set_code` int(11)        DEFAULT NULL,
    `customer_group`     varchar(31)    DEFAULT NULL comment 'Customer Group: A, B, C, etc',
    `note`               longtext       DEFAULT NULL,
    `created_on`         datetime       DEFAULT NULL,
    `created_by`         int(11)        DEFAULT NULL,
    `updated_on`         datetime       DEFAULT NULL,
    `updated_by`         int(11)        DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_mop_product_sku` (`sku`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 comment 'Magento Offer Product';


CREATE TABLE `mop_product_file`
(
    `mop_product_id` bigint(20) unsigned NOT NULL,
    `file_id`        bigint(20) unsigned NOT NULL,
    `position`       int(11)      DEFAULT 0,
    `is_main`        tinyint(1)   DEFAULT 0,
    `is_parent_file` tinyint(4)   DEFAULT 0,
    `types`          varchar(255) DEFAULT NULL COMMENT 'csv format',
    `disabled`       tinyint(1)   DEFAULT 0,
    `label`          varchar(255) DEFAULT NULL,
    `mag_id`         int(11)      DEFAULT NULL COMMENT 'Magento file ID',
    PRIMARY KEY (`mop_product_id`, `file_id`),
    KEY `FK_mop_product_file_file_id` (`file_id`),
    CONSTRAINT `FK_mop_product_file_mop_product_id` FOREIGN KEY (`mop_product_id`) REFERENCES `mop_product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_mop_product_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 comment 'Image files of Magento Offer Product';


drop table if exists mop_product_text;

CREATE TABLE `mop_product_text` (
                            `mop_product_id` bigint(20) unsigned NOT NULL,
                            `lang` varchar(10) NOT NULL,
                            `name` varchar(500) NOT NULL,
                            `short_description` text DEFAULT NULL,
                            `description` longtext DEFAULT NULL,
                            `meta_title` varchar(255) DEFAULT NULL,
                            `meta_keywords` varchar(255) DEFAULT NULL,
                            `meta_description` varchar(255) DEFAULT NULL,
                            `settings` text DEFAULT NULL,
                            PRIMARY KEY (`mop_product_id`,`lang`),
                            CONSTRAINT `FK_mop_product_text_mop_product_id` FOREIGN KEY (`mop_product_id`) REFERENCES `mop_product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `mop_product_price` (
                             `mop_product_id` bigint(20) unsigned NOT NULL,
                             `price_type_id` int(11) NOT NULL comment 'Price Type ID: Will be website ID.',
                             `currency` varchar(5) NOT NULL DEFAULT 'EUR',
                             `price` double NOT NULL DEFAULT 0,
                             `min_qty` double NOT NULL DEFAULT 1,
                             `start_date` date DEFAULT NULL,
                             `end_date` date DEFAULT NULL,
                             `created_on` datetime DEFAULT NULL,
                             `updated_on` datetime DEFAULT NULL,
                             PRIMARY KEY (`mop_product_id`,`price_type_id`,`currency`),
                             KEY `FK_mop_product_price_price_type_id` (`price_type_id`),
                             CONSTRAINT `FK_mop_product_price_mop_product_id` FOREIGN KEY (`mop_product_id`) REFERENCES `mop_product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                             CONSTRAINT `FK_mop_product_price_price_type_id` FOREIGN KEY (`price_type_id`) REFERENCES `price_type` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;





