import React from 'react';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback } from 'react';
import { useState } from 'react';
import { useEffect, useRef } from 'react';
import { useMemo } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormSelect } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormSwitch, ProFormText } from '@ant-design/pro-form';
import {
  getUploadedDataList,
  getXlsTrademarkACList,
  importEANsFromSupplierData,
  updateItemTmpTrademarkFromSupplierData,
} from '@/services/foodstore-one/Import/import';
import type { ActionType, ColumnsState, ProColumns, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { FooterToolbar } from '@ant-design/pro-layout';
import { AutoComplete, But<PERSON>, Card, Divider, Input, message, Popconfirm, Spin, Tag, Typography } from 'antd';
import {
  CheckCircleOutlined,
  ClearOutlined,
  CloseSquareOutlined,
  CopyOutlined,
  HighlightOutlined,
  ImportOutlined,
  InfoCircleOutlined,
  SaveFilled,
  SaveOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { updateEanAll, updateEanByEan } from '@/services/foodstore-one/Item/ean';
import Util, { ni, sCap } from '@/util';
import _, { debounce } from 'lodash';
import { updateItem, updateItemAll } from '@/services/foodstore-one/Item/item';

import ViewFileDetailModal from '@/pages/Gdsn/components/ViewFileDetailModal';
import type { DefaultOptionType } from 'antd/lib/select';
import useImagesGroupPreview from '@/pages/Item/EanList/hooks/useImagesGroupPreview';

export type FormValueType = Partial<API.Import>;

export type SearchFormValueType = Partial<API.ImportedSupplierRow>;

export type XlsItemViewerProps = {
  initialValue: Partial<API.Import>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onCancel: (flag?: boolean, formValues?: FormValueType) => void;
};

export const definedColsImported: (keyof API.ImportSettingsInputType)[] = [
  'col_ean',
  'col_price',
  'col_trademark',
  'col_multi_ean',
  'col_hs_code',
  'col_name',
  'col_case_qty',
  'col_shelf_life',
  'col_vat',
  'col_qty',
  'col_box_qty',
  'col_exp_date',
  'col_article_no',
  'col_maker',
  'col_gln',
  'col_uvp',
  //
  'col_nan',
  'col_category',
  'col_price_pallet',
  'col_price_valid_from',
  'col_price_valid_to',
  'col_ve_pallet',
  'col_languages',
  'col_bbd',

  'calc_name',
  'calc_percentage',

  // Bezugsweg: Lager | Strecke
  'filter_ref_path',
];
export const definedColLabelsImported: Record<string, string> = {
  col_ean: 'EAN',
  col_price: 'Price',
  col_trademark: 'Trademark',
  col_multi_ean: 'Multi EAN',
  col_hs_code: 'HS Code',
  col_name: 'Name',
  col_case_qty: 'Qty/pkg',
  col_shelf_life: 'Normal Shelf Life',
  col_vat: 'VAT',
  col_qty: 'Qty',
  col_box_qty: 'Qty of boxes',
  col_exp_date: 'EXP. Date',
  col_article_no: 'Article No',
  col_maker: 'Manufacturer',
  col_gln: 'GLN',
  col_uvp: 'UVP',

  //
  col_nan: 'NAN',
  col_category: 'Category',
  col_price_pallet: 'Price per Pallet',
  col_price_valid_from: 'Price Valid From',
  col_price_valid_to: 'Price Valid To',
  col_ve_pallet: 'VE / Pallet',
  col_languages: 'Languages',
  col_bbd: 'BBD',
  // filter name
  filter_ref_path: 'Bezugsweg Column',
};

const sysCols = [
  'name_sys',
  'item_name_sys',
  'attr_case_qty',
  'vat_sys',
  'trademark_sys',
  'exp_date_sys',
  'hs_code_sys',
  'shelf_life_sys',
  'tmp_trademark_sys',

  'article_no_sys',
  'maker_sys',
  'gln_sys',
];
const excelDefinedCols = [
  'name',
  'case_qty',
  'vat',
  'trademark',
  'exp_date',
  'hs_code',
  'shelf_life',

  'article_no',
  'maker',
  'gln',
];

export const ColumnCloseComponent = ({
  setColStates,
  name,
}: {
  name: string;
  setColStates: Dispatch<SetStateAction<Record<string, ColumnsState>>>;
}) => {
  return (
    <CloseSquareOutlined
      className="cursor-pointer text-small c-gray"
      title="Close column"
      style={{ position: 'absolute', right: 4, top: 4 }}
      onClick={(e) => {
        setColStates((prev) => ({ ...prev, [name]: { show: false } }));
      }}
    />
  );
};

const XlsItemViewer: React.FC<XlsItemViewerProps> = (props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingEdits, setLoadingEdits] = useState<Record<string, boolean>>({});
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [selectedRowsState, setSelectedRows] = useState<API.ImportedSupplierRow[]>([]);
  // column state managements
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>(() => {
    const newStates: Record<string, ColumnsState> = {};
    sysCols.forEach((col) => {
      newStates[col] = { show: true };
    });
    excelDefinedCols.forEach((col) => {
      newStates[col] = { show: true };
    });
    return newStates;
  });
  const [copiedText, setCopiedText] = useState<string>('');

  const { id, settings } = props.initialValue;

  const [xlsTrademarkList, setXlsTrademarkList] = useState<any>([]);

  // GDSN data viewer
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [xlsEan, setXlsEan] = useState<string>('');

  // makers dropdown
  const [makersInXls, setMakersInXls] = useState<DefaultOptionType[]>([]);
  const [categoriesInXls, setCategoriesInXls] = useState<DefaultOptionType[]>([]);

  // images preview
  const { imagePreviewGroupBody, renderPreviewItem } = useImagesGroupPreview();

  // @ts-ignore
  const columns: ProColumns<API.ImportedSupplierRow>[] = useMemo(() => {
    if (!props.modalVisible || !id || !settings) return [];

    const dbCols: string[] = settings?.dbCols || [];

    const newColumns: ProColumns<API.ImportedSupplierRow>[] = [
      {
        title: 'Sale Qty',
        dataIndex: 'oi_piece_qty365',
        valueType: 'digit',
        width: 60,
        tooltip: 'Sale pcs qty for last 365 days.',
        align: 'right',
        render: (__, record) => ni(record.oi_piece_qty365),
      },
      {
        title: 'Stock Qty',
        dataIndex: 'stock_stable_mix_total_piece_qty',
        valueType: 'digit',
        width: 60,
        align: 'right',
        tooltip: 'Stock Qty: If single, total pieces of all family. If Multi, box qty',
        render: (__, record) =>
          ni(record.item_ean?.is_single ? record.stock_stable_mix_total_piece_qty : record.stock_stable_box_qty),
      },
      {
        dataIndex: 'gdsn',
        width: 20,
        align: 'center',
        className: 'p-0',
        title: <InfoCircleOutlined title="EAN exists in GDSN?" />,
        render: (__, record) => {
          return (
            record.gdsn && (
              <CheckCircleOutlined
                title="Exists in GDSN. Click to view details..."
                className="c-green cursor-pointer"
                onClick={() => {
                  setXlsEan(record.ean || '');
                  setOpenModal(true);
                }}
              />
            )
          );
        },
      },
      {
        title: 'GDSN Image',
        dataIndex: ['gdsn_item', 'detail', 'images'],
        valueType: 'image',
        sorter: false,
        align: 'center',
        width: 50,
        render: (__, record: any) => {
          return renderPreviewItem(record?.gdsn_item?.detail?.images ?? []);
        },
      },
    ];
    const lastColumns: ProColumns<API.ImportedSupplierRow>[] = [];

    // EAN & EAN name column
    const includedCols: string[] = [];

    let col = 'ean';
    let ind = dbCols?.findIndex?.((x: string) => x == 'ean');
    if (ind >= 0) {
      ind = dbCols?.findIndex?.((x: string) => x == col);
      newColumns.push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="ean" setColStates={setColStates} />
            <div className="c-green">EAN (XLS)</div>
            {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
            {settings?.header?.[ind] && (
              <div className="c-gray" style={{ fontSize: '90%' }}>
                {settings?.header?.[ind]}
              </div>
            )}
          </>
        ),
        width: 150,
        ellipsis: false,
        filters: true,
        className: definedColLabelsImported[`col_${col}`] ? 'bg-light-green' : '',
        render: (dom, record: API.ImportedSupplierRow) => {
          return record.ean ? (
            <Typography.Paragraph copyable={{ text: record.ean }} className="margin-0">
              <span className={record.ean_exist ? 'c-green' : ''}>{record.ean}</span>
            </Typography.Paragraph>
          ) : (
            <></>
          );
        },
      });
      includedCols.push(col);

      // EAN name
      col = 'name';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      newColumns.push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="name" setColStates={setColStates} />
            <div className={ind < 0 ? '' : 'c-green'}>EAN Name (XLS)</div>
            {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
            {settings?.header?.[ind] && (
              <div className="c-gray" style={{ fontSize: '90%' }}>
                {settings?.header?.[ind]}
              </div>
            )}
          </>
        ),
        width: 200,
        filters: true,
        render: (dom, record: API.ImportedSupplierRow) => (
          <div style={{ display: 'flex' }}>
            <Typography.Paragraph
              copyable={{
                text: sCap(record.name),
                tooltips: 'Copy with the capitalized of the  .',
                icon: <CopyOutlined style={{ color: 'gray' }} />,
                onCopy: (e) => {
                  // setCopiedText((record.name || '').replace(/\w+/g, _.capitalize));
                  setCopiedText((record.name || '').replace(/\w+/g, _.capitalize));
                },
              }}
              className="margin-0"
              style={{ display: 'inline-block' }}
            >
              {record.name}
              <Typography.Paragraph
                copyable={{
                  text: record.name,
                  icon: <CopyOutlined />,
                  onCopy: (e) => {
                    setCopiedText(record.name || '');
                  },
                }}
                className="margin-0"
                style={{ display: 'inline-block' }}
              />
            </Typography.Paragraph>
          </div>
        ),
        onCell: (record, rowIndex) => {
          let cls = '';
          if (record.name_sys != record.name) {
            cls = 'bg-light-orange2';
          }
          return {
            className: cls,
          };
        },
      });
      includedCols.push(col);

      col = 'item_name_sys';
      newColumns.push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="item_name_sys" setColStates={setColStates} />
            <div className="c-blue">Item Title</div>
          </>
        ),
        width: 200,
        filters: true,
        tooltip: 'Please click to edit.',
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          const eanStr = record.ean || '__undefined';
          return (
            <Spin spinning={loadingEdits[eanStr] || false}>
              <Typography.Paragraph
                style={{ margin: 0, left: 0 }}
                editable={{
                  tooltip: 'click to edit text',
                  onChange: async (value) => {
                    if (value != record.item_name_sys) {
                      setLoadingEdits((prev) => ({ ...prev, [eanStr]: true }));
                      await updateEanByEan(eanStr, { item: { name: value } }).catch(Util.error);
                      setLoadingEdits((prev) => ({ ...prev, [eanStr]: false }));
                      action?.reload();
                    }
                    return value;
                  },
                  triggerType: ['text'],
                }}
              >
                {record.item_name_sys}
              </Typography.Paragraph>
              <HighlightOutlined
                title="Update with a copied content in the clipboard."
                className="c-blue"
                style={{ position: 'absolute', top: -2, right: -2, display: 'block' }}
                onClick={async () => {
                  let value: any = '';
                  if (navigator.clipboard.readText) {
                    value = await navigator.clipboard.readText().catch((e) => message.error(e));
                  } else {
                    value = copiedText;
                  }

                  if (value) {
                    const hide = message.loading('Updating...', 0);
                    setLoadingEdits((prev) => ({ ...prev, [eanStr]: true }));
                    await updateEanByEan(eanStr, { item: { name: value } }).catch((e) =>
                      Util.error(e.message ?? 'Failed to update.', e),
                    );
                    setLoadingEdits((prev) => ({ ...prev, [eanStr]: false }));
                    hide();
                    action?.reload();
                  } else {
                    message.error('No copied data!');
                  }
                }}
              />
            </Spin>
          );
        },
      });
      includedCols.push(col);

      col = 'name_sys';
      newColumns.push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="name_sys" setColStates={setColStates} />
            <div className="c-blue">Name of EAN</div>
          </>
        ),
        tooltip: 'Please click to edit.',
        width: 200,
        filters: true,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          const eanStr = record.ean || '__undefined';
          return (
            <Spin spinning={loadingEdits[eanStr + 'name'] || false}>
              <Typography.Paragraph
                style={{ margin: 0, left: 0 }}
                editable={{
                  tooltip: 'click to edit text',
                  onChange: async (value) => {
                    if (value != record.name_sys) {
                      setLoadingEdits((prev) => ({ ...prev, [eanStr + 'name']: true }));
                      await updateEanByEan(eanStr, { ean_texts: [{ name: value }] }).catch(Util.error);
                      setLoadingEdits((prev) => ({ ...prev, [eanStr + 'name']: false }));
                      action?.reload();
                    }
                    return value;
                  },
                  triggerType: ['text'],
                }}
              >
                {record.name_sys}
              </Typography.Paragraph>
              <HighlightOutlined
                title="Update with a copied content in the clipboard."
                className="c-blue"
                style={{ position: 'absolute', top: -4, right: -5, display: 'block' }}
                onClick={async () => {
                  let value: any = '';
                  if (navigator.clipboard.readText) {
                    value = await navigator.clipboard.readText().catch((e) => message.error(e));
                  } else {
                    value = copiedText;
                  }

                  if (value !== null) {
                    const hide = message.loading('Updating...', 0);
                    setLoadingEdits((prev) => ({ ...prev, [eanStr + 'name']: true }));
                    await updateEanByEan(eanStr, { ean_texts: [{ name: value }] }).catch((e) =>
                      Util.error(e.message ?? 'Failed to update.', e),
                    );
                    setLoadingEdits((prev) => ({ ...prev, [eanStr + 'name']: false }));
                    action?.reload();
                    hide();
                    action?.reload();
                  } else {
                    message.error('No copied data!');
                  }
                }}
              />
            </Spin>
          );
        },
        onCell: (record, rowIndex) => {
          let cls = '';
          if (record.name_sys != record.name) {
            cls = 'bg-light-orange2';
          }
          return {
            className: cls,
          };
        },
      });
      includedCols.push(col);

      // Qty/Pkg
      // -------------------------------------------------------- //
      col = 'case_qty';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      if (ind >= 0) {
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="case_qty" setColStates={setColStates} />
              <div className={ind < 0 ? '' : 'c-green'}>Qty/Pkg (XLS)</div>
              {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )}
            </>
          ),
          width: 120,
          render: (dom, record: API.ImportedSupplierRow) => (
            <div style={{ display: 'flex' }}>{ni(record.case_qty)}</div>
          ),
        });
        includedCols.push(col);
      }

      col = 'attr_case_qty';
      (ind >= 0 ? newColumns : lastColumns).push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="attr_case_qty" setColStates={setColStates} />
            <div className="c-blue">Qty/Pkg</div>
          </>
        ),
        width: 100,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          return <>{Util.numberFormat(record?.item_ean?.attr_case_qty)}</>;
        },
      });
      includedCols.push(col);

      // Shelf Life
      // -------------------------------------------------------- //
      col = 'shelf_life';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      if (ind >= 0) {
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="shelf_life" setColStates={setColStates} />
              <div className={ind < 0 ? '' : 'c-green'}>Shelf Life (XLS)</div>
              {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )}
            </>
          ),
          width: 100,
          ellipsis: true,
          render: (dom, record: API.ImportedSupplierRow) => (
            <div style={{ display: 'flex' }}>{Util.numberFormat(record.shelf_life)}</div>
          ),
          onCell: (record, rowIndex) => {
            let cls = '';
            if (Util.numberFormat(record.shelf_life) != Util.numberFormat(record?.item_ean?.item?.shelf_life)) {
              cls = 'bg-light-orange1';
            }
            return {
              className: cls,
            };
          },
        });
        includedCols.push(col);

        col = 'shelf_life_sys';
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="shelf_life_sys" setColStates={setColStates} />
              <div className="c-blue">Shelf Life</div>
            </>
          ),
          width: 100,
          render: (dom, record: API.ImportedSupplierRow, index, action) => {
            return (
              <>
                {record.item_ean?.item_id &&
                  record.shelf_life &&
                  ni(record.shelf_life) != ni(record?.item_ean?.item?.shelf_life) && (
                    <HighlightOutlined
                      title="Update item with Shelf Life (XLS)"
                      className="c-blue"
                      style={{ position: 'absolute', top: -2, right: -2, display: 'block' }}
                      onClick={async () => {
                        const hide = message.loading("Updating item's Shelf Life...", 0);
                        await updateItem({
                          id: record.item_ean?.item_id,
                          shelf_life: record.shelf_life,
                        }).catch((e) => Util.error(e.message ?? 'Failed to update.', e));
                        hide();
                        action?.reload();
                      }}
                    />
                  )}
                {Util.numberFormat(record?.item_ean?.item?.shelf_life)}
              </>
            );
          },
          onCell: (record, rowIndex) => {
            let cls = '';
            if (Util.numberFormat(record.shelf_life) != Util.numberFormat(record?.item_ean?.item?.shelf_life)) {
              cls = 'bg-light-orange1';
            }
            return {
              className: cls,
            };
          },
        });
        includedCols.push(col);
      }

      // Exp. Date
      // -------------------------------------------------------- //
      col = 'exp_date';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      if (ind >= 0) {
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="exp_date" setColStates={setColStates} />
              <div className={ind < 0 ? '' : 'c-green'}>Exp. Date (XLS)</div>
              {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )}
            </>
          ),
          width: 100,
          ellipsis: true,
          render: (dom, record: API.ImportedSupplierRow) => (
            <div style={{ display: 'flex' }}>{Util.dtToDMY(record.exp_date)}</div>
          ),
        });
        includedCols.push(col);
      }
      col = 'exp_date_sys';
      (ind >= 0 ? newColumns : lastColumns).push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="exp_date_sys" setColStates={setColStates} />
            <div className="c-blue">Exp. Date</div>
          </>
        ),
        width: 100,
        tooltip: "Latest IBO's Exp. Date",
        ellipsis: true,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          return <>{Util.dtToDMY(record?.item_ean?.latest_own_ibo?.exp_date)}</>;
        },
      });
      includedCols.push(col);

      // trademark
      // -------------------------------------------------------- //
      col = 'trademark';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      if (ind >= 0) {
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="trademark" setColStates={setColStates} />
              <div className={ind < 0 ? '' : 'c-green'}>Trademark (XLS)</div>
              {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )}
            </>
          ),
          width: 100,
          render: (dom, record: API.ImportedSupplierRow) => <div style={{ display: 'flex' }}>{record.trademark}</div>,
        });
        includedCols.push(col);
      }
      col = 'trademark_sys';
      (ind >= 0 ? newColumns : lastColumns).push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="trademark_sys" setColStates={setColStates} />
            <div className="c-blue">Trademark</div>
          </>
        ),
        width: 100,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          return <>{record?.item_ean?.item?.trademark?.name}</>;
        },
      });
      includedCols.push(col);

      col = 'tmp_trademark_sys';
      (ind >= 0 ? newColumns : lastColumns).push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="tmp_trademark_sys" setColStates={setColStates} />
            <div className="c-blue">Tmp Trademark</div>
          </>
        ),
        width: 100,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          return <>{record?.item_ean?.item?.tmp_trademark}</>;
        },
      });
      includedCols.push(col);

      // VAT
      // -------------------------------------------------------- //
      col = 'vat';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      if (ind >= 0) {
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="vat" setColStates={setColStates} />
              <div className={ind < 0 ? '' : 'c-green'}>VAT (XLS)</div>
              {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )}
            </>
          ),
          width: 80,
          render: (dom, record: API.ImportedSupplierRow) => <div style={{ display: 'flex' }}>{record.vat}</div>,
          onCell: (record: API.ImportedSupplierRow, rowIndex) => {
            let cls = '';
            if (
              Util.numberFormat(record.vat, true, 2, true) !=
              Util.numberFormat(record?.item_ean?.item?.vat?.value, true, 2, true)
            ) {
              cls = 'bg-light-red2';
            }
            return {
              className: cls,
            };
          },
        });
        includedCols.push(col);
      }

      col = 'vat_sys';
      (ind >= 0 ? newColumns : lastColumns).push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="vat_sys" setColStates={setColStates} />
            <div className="c-blue">VAT</div>
          </>
        ),
        width: 80,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          return <>{Util.numberFormat(record?.item_ean?.item?.vat?.value, true, 2, true)}</>;
        },
        onCell: (record: API.ImportedSupplierRow, rowIndex) => {
          let cls = '';
          if (
            Util.numberFormat(record.vat, true, 2, true) !=
            Util.numberFormat(record?.item_ean?.item?.vat?.value, true, 2, true)
          ) {
            cls = 'bg-light-red2';
          }
          return {
            className: cls,
          };
        },
      });
      includedCols.push(col);

      // HS Code
      // -------------------------------------------------------- //
      col = 'hs_code';
      ind = dbCols?.findIndex?.((x: string) => x == col);
      if (ind >= 0) {
        newColumns.push({
          dataIndex: col,
          title: (
            <>
              <ColumnCloseComponent name="hs_code" setColStates={setColStates} />
              <div className={ind < 0 ? '' : 'c-green'}>HS Code (XLS)</div>
              {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )}
            </>
          ),
          width: 100,
          render: (dom, record: API.ImportedSupplierRow) => <div style={{ display: 'flex' }}>{record.hs_code}</div>,
        });
        includedCols.push(col);
      }
      col = 'hs_code_sys';
      (ind >= 0 ? newColumns : lastColumns).push({
        dataIndex: col,
        title: (
          <>
            <ColumnCloseComponent name="hs_code_sys" setColStates={setColStates} />
            <div className="c-blue">HS Code</div>
          </>
        ),
        width: 100,
        render: (dom, record: API.ImportedSupplierRow, index, action) => {
          return <>{record?.item_ean?.item?.hs_code}</>;
        },
      });
      includedCols.push(col);
    }

    return [
      ...newColumns,
      ...dbCols.map((dbCol, i) =>
        includedCols.includes(dbCol)
          ? {
              dataIndex: includedCols.includes(dbCol) ? 'hidden_' + dbCol : dbCol,
              width: 0,
              hideInSetting: includedCols.includes(dbCol),
              hideInTable: includedCols.includes(dbCol),
              hideInForm: includedCols.includes(dbCol),
            }
          : ({
              dataIndex: includedCols.includes(dbCol) ? 'hidden_' + dbCol : dbCol,
              title: (
                <>
                  <ColumnCloseComponent
                    name={includedCols.includes(dbCol) ? 'hidden_' + dbCol : dbCol}
                    setColStates={setColStates}
                  />
                  <div className="c-green">
                    {definedColLabelsImported[`col_${dbCol}`] ?? (
                      <span className="c-gray" style={{ fontSize: '90%', color: 'gray' }}>
                        {dbCol}
                      </span>
                    )}
                  </div>
                  {settings?.header?.[i] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
                  {settings?.header?.[i] && (
                    <div className="c-gray" style={{ fontSize: '90%' }}>
                      {settings?.header?.[i]}
                    </div>
                  )}
                </>
              ),
              width: 150,
              ellipsis: true,
              filters: true,
              hideInSetting: includedCols.includes(dbCol),
              hideInTable: includedCols.includes(dbCol),
              className: definedColLabelsImported[`col_${dbCol}`] ? 'bg-light-green' : '',
              render: (dom, record: API.ImportedSupplierRow) => {
                let newDom = null;

                if (dbCol == 'ean') {
                  newDom = (
                    <Typography.Paragraph copyable={{ text: record.ean }} className="margin-0">
                      <span className={record.ean_exist ? 'c-green' : ''}>{record.ean}</span>
                    </Typography.Paragraph>
                  );
                } else if (dbCol == 'multi_ean') {
                  newDom = (
                    <Typography.Paragraph copyable={{ text: record.multi_ean }} className="margin-0">
                      <span className={record.multi_ean_exist ? 'c-green' : ''}>{record.multi_ean}</span>
                    </Typography.Paragraph>
                  );
                } else if (dbCol == 'exp_date') {
                  newDom = Util.dtToDMY(record.exp_date);
                } else {
                  newDom = dom;
                }

                return <>{newDom}</>;
              },
            } as ProColumnType),
      ),
      ...lastColumns,
    ];
  }, [props.modalVisible, id, settings, renderPreviewItem, loadingEdits, copiedText]);

  useEffect(() => {
    if (id && props.modalVisible) {
      setSelectedRows([]);
      actionRef.current?.reload();
    }
  }, [props.modalVisible, id]);

  const existsValidItemInSelection = useMemo(() => {
    if (selectedRowsState.length) {
      for (const row of selectedRowsState) {
        if (row.item_ean?.item_id) return true;
        break;
      }
    }
    return false;
  }, [selectedRowsState]);

  const handleSearchXlsTrademark = useCallback(
    async (value?: string) => {
      await getXlsTrademarkACList(id, { trademark: value })
        .then((res) => {
          setXlsTrademarkList(res);
          if (res.length < 1)
            searchFormRef.current?.setFieldsValue({
              trademark: undefined,
            });
        })
        .catch(() => {
          setXlsTrademarkList([]);
          searchFormRef.current?.setFieldsValue({
            trademark: undefined,
          });
        });
    },
    [id],
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearchXlsTrademark = useCallback(
    debounce((newValue) => handleSearchXlsTrademark(newValue), 300),
    [handleSearchXlsTrademark, debounce],
  );

  return (
    <ModalForm
      title={
        <>
          Imported Supplier Data Viewer (XLS_ITEM) -{' '}
          <Tag className="text-small">{props.initialValue.files?.[0]?.clean_file_name}</Tag> /{' '}
          <Tag>{props.initialValue.table_name}</Tag>
        </>
      }
      width="90%"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      initialValues={props.initialValue || {}}
      labelCol={{ span: 5 }}
      isKeyPressSubmit={true}
      submitter={false}
    >
      <Card style={{ marginBottom: 0, paddingTop: 0 }} bordered={false} bodyStyle={{ paddingBottom: 0 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          style={{ marginBottom: 0 }}
          submitter={{
            submitButtonProps: { loading: loading },
            render: (__, doms) => {
              return [
                <Button
                  type="primary"
                  key="submit"
                  onClick={() => actionRef.current?.reload()}
                  loading={loading}
                  disabled={loading}
                  icon={<SearchOutlined />}
                >
                  Search
                </Button>,
                <Button
                  type="default"
                  key="rest"
                  loading={loading}
                  disabled={loading}
                  icon={<ClearOutlined />}
                  onClick={() => {
                    searchFormRef.current?.resetFields();
                    actionRef.current?.reload();
                  }}
                >
                  Reset
                </Button>,
              ];
            },
          }}
        >
          <ProFormSwitch
            name={'only_existing_ean'}
            label="Only existing EAN"
            initialValue={true}
            fieldProps={{
              onChange: () => {
                searchFormRef.current?.setFieldsValue({ only_not_existing_ean: false });
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormSwitch
            name={'only_not_existing_ean'}
            label="Only not existing EAN"
            initialValue={false}
            fieldProps={{
              onChange: () => {
                searchFormRef.current?.setFieldsValue({ only_existing_ean: false });
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormText
            name={'name_search'}
            label={'Name'}
            width={180}
            fieldProps={{
              onPressEnter: (e) => actionRef.current?.reload(),
            }}
          />

          <ProFormCheckbox.Group
            name={'column_mode'}
            label="Column Mode"
            options={[
              { value: 'sys', label: 'Sys' },
              { value: 'excel', label: 'Comparison' },
              { value: 'not_defined', label: 'XLS Not defined' },
            ]}
            initialValue={['sys', 'excel', 'not_defined']}
            fieldProps={{
              onChange: (value) => {
                const cols: string[] = ['ean'];
                value.forEach((v) => {
                  if (v == 'sys') {
                    cols.push(...sysCols);
                  } else if (v == 'excel') {
                    cols.push(...excelDefinedCols);
                  } else if (v == 'not_defined') {
                    cols.push(
                      ...columns
                        .filter(
                          (c) => !sysCols.includes(`${c.dataIndex}`) && !excelDefinedCols.includes(`${c.dataIndex}`),
                        )
                        .map((c) => c.dataIndex as string),
                    );
                  }
                });

                const newStates: Record<string, ColumnsState> = {};
                columns.forEach((col) => {
                  newStates[`${col.dataIndex}`] = { show: false };
                });
                cols.forEach((col) => {
                  newStates[col] = { show: true };
                });
                setColStates(newStates);
              },
            }}
          />

          {settings?.dbCols?.map((col: string) => {
            if (['ean', 'multi_ean', 'trademark'].includes(col)) {
              return col == 'trademark' ? (
                <ProForm.Item key={col} name={col} label={definedColLabelsImported[`col_${col}`]}>
                  <AutoComplete
                    options={xlsTrademarkList}
                    onSearch={debouncedHandleSearchXlsTrademark}
                    allowClear
                    onSelect={(value: string, option: any) => {
                      searchFormRef.current?.setFieldsValue({ trademark: value });
                    }}
                    style={{ width: '100%' }}
                  >
                    <Input.Search size="middle" placeholder="search item name" />
                  </AutoComplete>
                </ProForm.Item>
              ) : (
                <ProFormText
                  key={col}
                  name={col}
                  label={definedColLabelsImported[`col_${col}`]}
                  width={180}
                  fieldProps={{
                    onPressEnter: (e) => actionRef.current?.reload(),
                  }}
                />
              );
            }
            return undefined;
          })}

          <ProFormCheckbox.Group
            name={'filter_modes'}
            label="Filter Mode"
            options={[
              { value: 'no_shelf_life', label: 'No Shelflife' },
              { value: 'diff_shelf_life', label: '<> Shelflife' },
              { value: 'no_trademark', label: 'No Trademark' },
              { value: 'no_hs_code', label: 'No HS Code' },
            ]}
            initialValue={[]}
            fieldProps={{
              onChange: (value) => {
                // actionRef.current?.reload();
              },
            }}
          />
          <ProFormSelect
            showSearch
            name="maker"
            placeholder="Manufacturer"
            options={makersInXls}
            width="sm"
            label="Manufacturer"
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange: (value) => {
                actionRef.current?.reload();
              },
            }}
          />
          {settings?.dbCols?.includes('category') && (
            <ProFormSelect
              showSearch
              name="category"
              placeholder="Category"
              options={categoriesInXls}
              width="sm"
              label="Category"
              fieldProps={{
                dropdownMatchSelectWidth: false,
                onChange: (value) => {
                  actionRef.current?.reload();
                },
              }}
            />
          )}
          <ProFormText
            name={'gln'}
            label={'GLN'}
            placeholder={'GLN'}
            width={150}
            fieldProps={{
              onPressEnter: (e) => actionRef.current?.reload(),
            }}
          />
          <ProFormSelect
            label="GDSN exists?"
            name="gdsn_exists"
            tooltip="Check if EAN exists in GDSN cache. Note: Cache data is used."
            options={[
              { value: 1, label: 'Yes' },
              { value: 2, label: 'Yes, inc. Pic' },
              { value: 0, label: 'No' },
            ]}
            /* fieldProps={{
              onChange: () => {
                actionRef.current?.reload();
              },
            }} */
          />
        </ProForm>
      </Card>

      <ProTable<API.ImportedSupplierRow, API.PageParams>
        headerTitle={'Data list - Green indicates the existed EANs'}
        rowKey="id"
        revalidateOnFocus={false}
        actionRef={actionRef}
        options={{ fullScreen: true }}
        scroll={{ x: 800, y: 600 }}
        size="small"
        bordered
        sticky
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 30,
        }}
        search={false}
        request={async (params, sort, filter) => {
          setLoading(true);
          return getUploadedDataList(
            {
              ...params,
              ...searchFormRef.current?.getFieldsValue(),
              with: 'oi_piece_qty365,stock_stable_qty',
              id,
            },
            sort,
            filter,
          )
            .then((res) => {
              if (res?.data?.length) {
                setSelectedRows((prevRows) => {
                  const newRows: API.ImportedSupplierRow[] = [];
                  prevRows.forEach((element) => {
                    const found = _.find(res.data, { id: element.id });
                    if (found) {
                      newRows.push({ ...found });
                    }
                  });
                  return newRows;
                });
              } else {
                setSelectedRows([]);
              }
              setMakersInXls(res.makers);
              setCategoriesInXls(res.categories);
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (__, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              Chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              entries &nbsp;&nbsp;
            </div>
          }
        >
          <Popconfirm
            title={
              <>
                EANs and Multi-EANs will be imported. <br />
                <br />
                Are you sure you want to import EANs?
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={() => {
              const hide = message.loading(`Batch importing EANs ...`, 0);
              importEANsFromSupplierData(id, { ids: selectedRowsState.map((x) => x.id) })
                .then((res: any) => {
                  setSelectedRows([]);
                  actionRef.current?.reloadAndRest?.();
                  if (res.createdCount) {
                    message.success(`${res.createdCount} EANs created!`);
                  }
                  if (res.errors?.length) {
                    message.error(
                      <div>
                        But there were errors:
                        {res.errors.map((e: string) => (
                          <div key={e}>{e}</div>
                        ))}
                      </div>,
                    );
                  }
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" icon={<ImportOutlined />}>
              Import EANs
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                tmp_trademark in items will be updated. Not existed EANs will be ignored! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            disabled={!existsValidItemInSelection}
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={() => {
              const hide = message.loading(`Batch updating item's tmp_trademark ...`, 0);
              updateItemTmpTrademarkFromSupplierData(id, {
                ids: selectedRowsState.map((x) => x.id),
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="default" icon={<ImportOutlined />} disabled={!existsValidItemInSelection}>
              tmp_trademark
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                Item names will be updated from Name (XLS)! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            disabled={!existsValidItemInSelection}
            onConfirm={() => {
              const availableData = selectedRowsState
                .filter((x) => !!x?.item_ean?.item_id)
                .map((x) => ({
                  id: x?.item_ean?.item_id,
                  name: x?.name,
                }));
              if (!availableData.length) {
                message.error('There are no valid item entries in the selection.');
                return;
              }

              const hide = message.loading(`Batch updating items ...`, 0);

              updateItemAll({
                items: availableData,
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" icon={<SaveFilled />} disabled={!existsValidItemInSelection}>
              Item Name
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                Item names will be updated from Name (XLS), but uppercase the 1st letter! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            disabled={!existsValidItemInSelection}
            onConfirm={() => {
              const availableData = selectedRowsState
                .filter((x) => !!x?.item_ean?.item_id)
                .map((x) => ({
                  id: x?.item_ean?.item_id,
                  name: sCap(x?.name),
                }));
              if (!availableData.length) {
                message.error('There are no valid item entries in the selection.');
                return;
              }

              const hide = message.loading(`Batch updating items ...`, 0);

              updateItemAll({
                items: availableData,
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button
              type="ghost"
              icon={<SaveOutlined />}
              title="Update with uppercase of 1st letter."
              disabled={!existsValidItemInSelection}
            >
              Item Name (UC)
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                EAN names will be updated from Name (XLS)! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            disabled={!existsValidItemInSelection}
            onConfirm={() => {
              const availableData = selectedRowsState
                .filter((x) => !!x?.item_ean?.id)
                .map((x) => ({
                  id: x?.item_ean?.id,
                  ean_texts: [{ name: x?.name }],
                }));
              if (!availableData.length) {
                message.error('There are no valid item entries in the selection.');
                return;
              }

              const hide = message.loading(`Batch updating EAN names ...`, 0);

              updateEanAll({
                item_eans: availableData,
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" icon={<SaveFilled />} disabled={!existsValidItemInSelection}>
              EAN Name
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                EAN names will be updated from Name (XLS), but uppercase the 1st letter! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            disabled={!existsValidItemInSelection}
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={() => {
              const availableData = selectedRowsState
                .filter((x) => !!x?.item_ean?.id)
                .map((x) => ({
                  id: x?.item_ean?.id,
                  ean_texts: [{ name: sCap(x?.name) }],
                }));
              if (!availableData.length) {
                message.error('There are no valid item entries in the selection.');
                return;
              }

              const hide = message.loading(`Batch updating EAN names ...`, 0);

              updateEanAll({
                item_eans: availableData,
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button
              type="ghost"
              icon={<SaveOutlined />}
              title="Update with uppercase of 1st letter."
              disabled={!existsValidItemInSelection}
            >
              EAN Name (UC)
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                Item Shelf Life will be updated from XLS! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            disabled={!existsValidItemInSelection}
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={() => {
              const availableData = selectedRowsState
                .filter((x) => !!x?.item_ean?.item_id)
                .map((x) => ({
                  id: x?.item_ean?.item_id,
                  shelf_life: x?.shelf_life,
                }));
              if (!availableData.length) {
                message.error('There are no valid item entries in the selection.');
                return;
              }

              const hide = message.loading(`Batch updating item shelf life ...`, 0);

              updateItemAll({
                items: availableData,
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="dashed" icon={<SaveOutlined />} disabled={!existsValidItemInSelection}>
              Shelf Life
            </Button>
          </Popconfirm>

          <Popconfirm
            title={
              <>
                HS Code Life will be updated from XLS! <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            disabled={!existsValidItemInSelection}
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={() => {
              const availableData = selectedRowsState
                .filter((x) => !!x?.item_ean?.item_id)
                .map((x) => ({
                  id: x?.item_ean?.item_id,
                  hs_code: x?.hs_code,
                }));
              if (!availableData.length) {
                message.error('There are no valid item entries in the selection.');
                return;
              }

              const hide = message.loading(`Batch updating item HS Code ...`, 0);

              updateItemAll({
                items: availableData,
              })
                .then((res: any) => {
                  message.success('Successfully updated.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="dashed" icon={<SaveOutlined />} disabled={!existsValidItemInSelection}>
              HS Code
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}
      <ViewFileDetailModal
        modalVisible={openModal}
        handleModalVisible={setOpenModal}
        initialValues={{ gtin: xlsEan, with: 'import,gdsnMessage.file_url' }}
      />
      {imagePreviewGroupBody}
    </ModalForm>
  );
};

export default XlsItemViewer;
