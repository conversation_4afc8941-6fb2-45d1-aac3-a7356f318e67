import { Card } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { getPicklistACList } from '@/services/foodstore-one/Warehouse/picklist';
import { getPickListDetails, getPickListDetailsSummary } from '@/services/foodstore-one/Warehouse/picklist-detail';
import type { DefaultOptionType } from 'antd/lib/select';

export type SearchFormValueType = Partial<API.WarehousePicklist>;

const PicklistDetail: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();

  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [picklist, setPicklist] = useState<API.WarehousePicklist[]>([]);
  const [selectedPicklist, setSelectedPicklist] = useState<DefaultOptionType>();
  const [data, setData] = useState<API.WarehousePicklistDetail>({});

  // comments modal
  // const [currentOrder, setCurrentOrder] = useState<API.Order>();
  // const [openCommentsModal, setOpenCommentsModal] = useState<boolean>(false);

  const handleSearch = () => {
    const searchFormValues = searchFormRef.current?.getFieldsValue();
    if ((searchFormValues.sku || searchFormValues.ean) && searchFormValues.picklist_id) {
      getPickListDetailsSummary(searchFormValues).then((res) => setData(res?.[0]));
      actionRef.current?.reload();
    }
  };

  useEffect(() => {
    getPicklistACList({}, { id: 'descend' }).then((res) => {
      setPicklist(res);
      if (res && res[0]) {
        searchFormRef.current?.setFieldsValue({ picklist_id: res[0].id });
        if (res[0]) {
          setSelectedPicklist({
            value: res[0].id,
            label: `#${res[0]?.id}-${res[0]?.username}-${res[0]?.date} ${res[0]?.note ?? ''}`,
          });
        }
        actionRef.current?.reload();
      }
    });
  }, []);

  const columns: ProColumns<API.WarehousePicklistDetail>[] = useMemo(
    () => [
      {
        title: 'SKU',
        dataIndex: ['sku'],
        sorter: true,
        ellipsis: true,
        width: 100,
      },
      {
        title: 'EAN',
        dataIndex: ['mag_order_item', 'item_ean', 'ean'],
        sorter: true,
        ellipsis: true,
        width: 150,
      },
      {
        title: 'Order ID',
        dataIndex: 'order_id',
        sorter: true,
        hideInSearch: true,
        align: 'center',
        width: 90,
      },
      {
        title: 'Name',
        dataIndex: ['mag_order_item', 'item_ean', 'ean_texts', 0, 'name'],
        sorter: false,
        ellipsis: true,
      },
      {
        title: 'Orderd Qty',
        dataIndex: ['mag_order_item', 'qty_ordered'],
        sorter: false,
        align: 'right',
        width: 120,
        render: (_, record) => ni(record?.mag_order_item?.qty_ordered),
      },
      {
        title: 'Created on',
        dataIndex: ['mag_order_item', 'created_at'],
        sorter: false,
        width: 120,
        render: (_, record) => Util.dtToDMYHHMM(record?.mag_order_item?.created_at),
      },
    ],
    [],
  );

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => handleSearch(),
            onReset: (values) => {
              searchFormRef.current?.resetFields();
              handleSearch();
            },
          }}
        >
          <ProFormSelect
            name={'picklist_id'}
            label="Picklist"
            allowClear
            options={picklist.map((x) => ({
              value: x.id,
              label: `#${x.id}-${x.username}-${x.date} ${x.note ?? ''}`,
            }))}
            fieldProps={{
              onChange: (value, option) => {
                setSelectedPicklist(option as any);
                actionRef.current?.reload();
              },
            }}
            placeholder={'Picklist'}
            width={'sm'}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'exact SKU'} />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'exact EAN'} />
        </ProForm>
      </Card>
      {data?.sku && (
        <Card style={{ marginBottom: 16, background: 'green', width: 400 }}>
          <h2 style={{ color: 'white' }}>
            EAN: {data?.item_ean?.ean} Qty: {data?.sum_qty_ordered}
          </h2>
        </Card>
      )}
      {data?.sku && (
        <ProTable<API.WarehousePicklistDetail, API.PageParams>
          headerTitle={`Warehouse Picklist Detail - ${selectedPicklist?.label ?? ''}`}
          actionRef={actionRef}
          rowKey="order_item_id"
          revalidateOnFocus={false}
          options={{ fullScreen: true }}
          search={false}
          sticky
          size="small"
          scroll={{ x: 800 }}
          request={(params, sort, filter) => {
            const searchFormValues = searchFormRef.current?.getFieldsValue();
            if (!searchFormValues.picklist_id) return new Promise((resolve) => resolve([]));
            if (!searchFormValues.sku && !searchFormValues.ean) return new Promise((resolve) => resolve([]));
            setLoading(true);
            return getPickListDetails(
              {
                ...params,
                ...searchFormValues,
                with: 'magOrderItem,magOrder',
              },
              sort,
              filter,
            ).finally(() => setLoading(false));
          }}
          columns={columns}
          /* expandable={{
          expandedRowRender,
          expandRowByClick: true,
          defaultExpandAllRows: true,
          indentSize: 200,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            setExpandedRowKeys(expandedKeys as any);
          },
        }} */
          rowSelection={false}
          columnEmptyText=""
          pagination={{ hideOnSinglePage: true }}
        />
      )}
      {/* <Modal
        title={`Order shipment comments`}
        width={600}
        open={openCommentsModal}
        onCancel={() => setOpenCommentsModal(false)}
        footer={false}
      >
        <OrderShipmentcommentsList orderId={sn(currentOrder?.entity_id)} />
      </Modal> */}
    </PageContainer>
  );
};

export default PicklistDetail;
