import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addItem } from '@/services/foodstore-one/Item/item';
import { message } from 'antd';
import Util from '@/util';
import { getTrademarkList } from '@/services/foodstore-one/BasicData/trademark';
import { ItemSpecialFilterOptions } from '@/constants';

const handleAdd = async (fields: API.Item) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addItem(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.Item>;

export type CreateFormProps = {
  values?: Partial<API.Item>;
  modalVisible: boolean;
  vats: API.Vat[];
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Item) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Item'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Item);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="lg"
        name="name"
        label="Name"
      />
      <ProFormSelect
        showSearch
        placeholder="Select filters"
        mode="multiple"
        options={ItemSpecialFilterOptions}
        width="md"
        name="special_filter"
        label="Special Filters"
      />
      {/* <ProFormSelect
        showSearch
        placeholder="Select a supplier"
        request={async (params) => {
          const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Item) => ({
              label: `${x.id} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        width="md"
        name="supplier_id"
        label="Supplier"
      /> */}
      <ProFormSelect
        showSearch
        placeholder="Select a trademark"
        request={async (params) => {
          const res = await getTrademarkList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Trademark) => ({
              label: `${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        proFieldProps={{}}
        width="md"
        name="trademark_id"
        label="Trademark"
        colProps={{ xl: 6 }}
      />
      <ProFormSelect
        showSearch
        placeholder="Select a VAT"
        request={async (params) =>
          props.vats.map((x) => ({
            label: `${x.value}%`,
            value: x.id,
          }))
        }
        width="md"
        name="vat_id"
        label="VAT"
      />
      <ProFormText name="hs_code" label="HS Code" />

      <ProFormText name="fs_bio_certificate" label="Bio Certificate" />

      <ProFormSelect
        showSearch
        placeholder="Bio PlaceOfFarming"
        options={['NON_EU_AGRICULTURE', 'EU_AGRICULTURE', 'EU_OR_NON_EU_AGRICULTURE']}
        width="md"
        name="fs_bio_origin"
        label="Bio PlaceOfFarming"
      />

      <ProFormTextArea width="lg" name="description" label="Description" />
    </ModalForm>
  );
};

export default CreateForm;
