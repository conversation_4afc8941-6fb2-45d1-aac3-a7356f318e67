CREATE TABLE `xmag_order_user_action_log`
(
    `id`         int(11)      NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `type`       varchar(31)  NOT NULL COMMENT 'Log Type',
    `note`       varchar(255) NOT NULL COMMENT 'Log Title',
    `order_id`   int(11)  DEFAULT NULL COMMENT 'FK: Order ID',
    `detail`     text     DEFAULT NULL COMMENT 'Detail info in JSON',
    `created_by` int(11)  DEFAULT NULL COMMENT 'Actor',
    `created_on` datetime DEFAULT NULL COMMENT 'Created on',
    PRIMARY KEY (`id`),
    KEY `IDX_xmag_order_user_action_log_type` (`type`),
    KEY `IDX_xmag_order_user_action_log_note` (`note`),
    KEY `FK_xmag_order_user_action_log_order_id` (`order_id`),
    CONSTRAINT `FK_xmag_order_user_action_log_order_id` FOREIGN KEY (`order_id`) REFERENCES `xmag_order` (`entity_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;