/* eslint-disable */
import Util from '@/util';
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/offer';

/** get GET /api/offer */
export async function getOfferList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


export async function getOfferById(offerId: number, params?: Record<string, any>) {
  return getOfferList({ ...params, id: offerId, pageSize: 1 }, {}, {}).then(res => res.data?.[0]);
}

/** put PUT /api/offer */
export async function updateOffer(id: number, data: API.Offer, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Offer>>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** 
 * Save received pallets info.
 * 
 * PUT /api/offer/recv-data */
export async function updateOfferRecvData(id: number, data: API.Offer, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Offer>>(`${urlPrefix}/${id}/recv-data`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/offer */
export async function addOffer(data: API.Offer, options?: { [key: string]: any }) {
  return request<API.Offer>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/offer */
export async function deleteOffer(id: number | string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * get GET /api/offer/ac-list
 *
 * get the autocomplete lists.
 *
 */
export async function getOfferACList(params: { [key: string]: string }, sort?: any) {
  return request<DefaultOptionType & API.Offer>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
      with: (params?.with ? `${params?.with},` : '') + 'quote',
      perPage: params.pageSize || 1000,
      sort: !Object.keys(sort ?? {}).length ? { offer_no: 'descend' } : {},
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.map((x: API.Offer) => ({
      ...x,
      value: x.id,
      label: `#${x.offer_no || '-'} | ${Util.dtToDMY(x.created_on)}${x.note ? ` | ${x.note}` : ''}${x.quote?.customer_fullname ? ` - ${x.quote?.customer_fullname}` : ''}${x.ibo_status ? ` (${x.ibo_status})` : ''}`,
    })),
  );
}

export type Offer2IboPreMatrixResultType = {
  iboPreIds: number[];
  offerItems: (API.Offer & API.OfferItem)[];
  iboPreAssoc: Record<number, API.IboPre>;
}

/**
 * get GET /api/offer/getOffer2IboPreMatrix
 *
 * get matrix table.
 *
 */
export async function getOffer2IboPreMatrix(params: { [key: string]: string | any }, sort?: any) {
  return request<API.ResultObject<Offer2IboPreMatrixResultType>>(`${urlPrefix}/getOffer2IboPreMatrix`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize || 1000,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}



/** 
 * assign iboPre to OfferItem
 * 
 * PUT /api/offer/assignIboPre2OfferItem */
export async function assignIboPre2OfferItem(data: { offerItemId: number, iboPreId: number }, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.OfferItemIboPreMap>>(`${urlPrefix}/assignIboPre2OfferItem`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}


/** delete DELETE /api/offer/delete-picture */
export async function deleteOfferPicture(id: number | string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/delete-picture/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
