import {
  CheckCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  EditTwoTone,
  FileSyncOutlined,
  FileTextOutlined,
  LinkOutlined,
  PictureOutlined,
  SnippetsOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Tag } from 'antd';
import { Card } from 'antd';
import { Col, Row } from 'antd';
import { Button, Image, message, Drawer, Dropdown, Space, Menu, Popconfirm, Typography } from 'antd';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateAttributeForm from './components/UpdateAttributeForm';
import SDatePicker from '@/components/SDatePicker';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import WebsiteIcons from './components/WebsiteIcons';
import {
  getEanList,
  deleteEan,
  exportEanList,
  exportEanListAlt,
  exportEanPriceList,
  dsGetCustomAttribute,
  usProductFull,
  dsProductImages,
} from '@/services/foodstore-one/Item/ean';
import { ItemEANStatus } from '@/constants';
import { DEFAULT_PER_PAGE_PAGINATION, ItemEANStatusOptions } from '@/constants';
import UpdateCategoriesForm from './components/UpdateCategoriesForm';
import type { DataNode } from 'antd/lib/tree';
import { getCategoryList } from '@/services/foodstore-one/Item/category';
import UpdateTextsForm from './components/UpdateTextsForm';
import UpdatePicturesForm from './components/UpdatePicturesForm';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';

import styles from './style.less';
import { getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
import { getProducerListSelectOptions } from '@/services/foodstore-one/BasicData/producer';
import SPrices from '@/components/SPrices';
import * as UpdateItemForm from '../ItemList/components/UpdateForm';
import { useLocation, useModel } from 'umi';
import SocialLinks from './components/SocialIcons';
import ImportedPrices from './components/ImportedPrices';
import type { DefaultOptionType } from 'antd/lib/select';
import { getIBOManagementACList } from '@/services/foodstore-one/IBO/ibo-management';
import StockStableQtyModal from './components/StockStableQtyModal';

/**
 *  Delete node
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.Ean[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEan({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type SearchFormValueType = Partial<API.Ean>;

export type EanComponentProps = {
  eanType?: 'default' | 'base' | 've';
};

const Ean: React.FC<EanComponentProps> = (eanComponentProps) => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [updateItemModalVisible, handleUpdateItemModalVisible] = useState<boolean>(false);
  const [updateCategoriesModalVisible, handleUpdateCategoriesModalVisible] = useState<boolean>(false);
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false);
  const [updatePicturesModalVisible, handleUpdatePicturesModalVisible] = useState<boolean>(false);
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [tableConfig, setTableConfig] = useState<{ pagination?: any; filters?: any; sorter?: any }>({});
  const [currentRow, setCurrentRow] = useState<API.Ean>();
  const [currentItem, setCurrentItem] = useState<API.Item>();
  const [selectedRowsState, setSelectedRows] = useState<API.Ean[]>([]);
  const [ibomList, setIbomList] = useState<DefaultOptionType[]>([]);
  const { appSettings } = useModel('app-settings');

  const [loadingExport, setLoadingExport] = useState(false);
  const location: any = useLocation();

  const handleTextsClick = (record: API.Ean) => {
    setCurrentRow({ ...record });
    handleUpdateTextsModalVisible(true);
  };

  const pricesColDefs = useMemo<ProColumns<API.Ean>[]>(
    () =>
      appSettings.priceTypes.map(
        (pt): ProColumns<API.Ean> => ({
          title: pt.name,
          dataIndex: ['ean_prices', pt?.id || 0],
          valueType: 'digit',
          sorter: false,
          align: 'right',
          width: 110,
          hideInSearch: true,
          shouldCellUpdate(record, prevRecord) {
            return (
              !_.isEqual(record.ean_prices, prevRecord.ean_prices) ||
              !_.isEqual(record.parent?.ean_prices, prevRecord.parent?.ean_prices)
            );
          },
          render: (dom, record) => {
            const vat = record.item?.vat?.value || 0;
            /* const priceSingle = Util.safeNumber(
              _.get(
                _.find(record?.parent?.ean_prices, { price_type_id: pt.id }),
                'price',
                0,
              ).toFixed(2),
            ); */
            const priceSingle =
              Util.safeNumber(_.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0)) /
              (record?.attr_case_qty ? record?.attr_case_qty : 1);
            const price = Util.safeNumber(
              _.get(_.find(record?.ean_prices, { price_type_id: pt.id }), 'price', 0).toFixed(2),
            );
            return (
              <Row gutter={4}>
                <Col span={12}>
                  <SPrices price={priceSingle} vat={vat} />
                </Col>
                {!record.is_single && (
                  <Col span={12}>
                    <SPrices price={price /*  * (record?.attr_case_qty ?? 0) */} vat={vat} />
                  </Col>
                )}
              </Row>
            );
          },
        }),
      ),
    [appSettings.priceTypes],
  );

  const columns: ProColumns<API.Ean>[] = useMemo(
    () => [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 40,
        align: 'center',
        fixed: 'left',
        render: (item, record, index, action) => {
          return (
            ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
            index +
            1
          );
        },
      },
      {
        title: 'Status',
        dataIndex: 'status',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        align: 'center',
        ellipsis: true,
        width: 50,
        showSorterTooltip: false,
        valueEnum: ItemEANStatusOptions as any,
        render: (__, record) => {
          let ele = null;
          if (record.status == ItemEANStatus.ACTIVE) {
            ele = <CheckCircleOutlined style={{ color: 'green' }} />;
          } else {
            ele = <CloseOutlined style={{ color: 'gray' }} />;
          }
          return ele;
        },
      },
      {
        title: 'Shops',
        dataIndex: 'product_websites',
        hideInForm: false,
        sorter: false,
        filters: false,
        fixed: 'left',
        width: 50,
        showSorterTooltip: false,
        render: (__, record) => (
          <WebsiteIcons product_websites={record.product_websites as number[]} website_ids={record.website_ids} />
        ),
      },
      {
        title: 'Item',
        align: 'center',
        dataIndex: ['item', 'name'],
        hideInSearch: true,
        formItemProps: { tooltip: 'Search by item name.' },
        children: [
          {
            title: 'Item ID',
            dataIndex: 'item_id',
            fixed: 'left',
            sorter: true,
            ellipsis: true,
            hideInSearch: true,
            hideInTable: true,
          },
          {
            title: 'Name',
            dataIndex: ['item', 'name'],
            sorter: true,
            ellipsis: true,
            hideInSearch: false,
            width: 180,
          },
          {
            title: 'VAT',
            dataIndex: ['item', 'vat', 'value'],
            sorter: false,
            width: 60,
            ellipsis: true,
            align: 'right',
            hideInSearch: true,
            render: (dom, record) => (sn(record?.item?.vat?.value) >= 0 ? `${record?.item?.vat?.value}%` : ''),
          },
          {
            title: 'Trademark',
            dataIndex: ['item', 'trademark', 'name'],
            sorter: false,
            width: 100,
            ellipsis: true,
            hideInSearch: true,
            render: (dom, record) => (record?.item?.trademark?.name ? `${record?.item?.trademark?.name}` : ''),
          },
        ],
      },
      {
        title: 'Image',
        dataIndex: ['files', 0, 'url'],
        valueType: 'image',
        fixed: 'left',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 80,
        render: (dom, record) => {
          // return dom;
          // return record.files ? <img src={record.files?.[0]?.url} /> : <></>;
          return record.files ? (
            <Image.PreviewGroup>
              {record.files &&
                record.files.map((file, ind) => (
                  <Image
                    key={file.id}
                    src={file.thumb_url}
                    preview={{
                      src: file.url,
                    }}
                    wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                    width={40}
                  />
                ))}
            </Image.PreviewGroup>
          ) : (
            <></>
          );
        },
      },
      {
        title: 'EAN',
        dataIndex: 'ean',
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 150,
        render: (dom, record) => {
          return (
            <a
              onClick={() => {
                setCurrentRow({
                  ...record,
                });
                setShowDetail(true);
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Single EAN',
        dataIndex: ['parent', 'ean'],
        sorter: true,
        copyable: true,
        hideInSearch: true,
        width: 150,
      },
      {
        title: '-',
        dataIndex: ['mag_url', 'value'],
        valueType: 'text',
        align: 'center',
        hideInSearch: true,
        sorter: false,
        width: 50,
        render: (dom, record) => {
          return (
            <Space>
              <LinkOutlined
                // style={{ color: record?.mag_url?.value ? 'green' : 'c-lightgrey' }}
                title="Go to shop."
                className={record?.mag_url?.value ? 'green' : 'c-lightgrey'}
                onClick={async () => {
                  let urlKey = record?.mag_url?.value;
                  if (!urlKey)
                    urlKey = await dsGetCustomAttribute(record?.id || 0, {
                      force_update: 0,
                      attribute_code: 'url_key',
                    }).catch(() => {
                      message.error('Not found SKU on the shop.');
                    });

                  if (urlKey) {
                    window.open(`${SHOP_BASE_URL}/${urlKey}`, '_blank');
                  }
                }}
              />
              <Dropdown
                key="social-links-menu"
                overlay={
                  <Menu
                    items={[
                      {
                        key: 'all',
                        label: <SocialLinks ean={record.ean || ''} title={record?.ean_texts?.[0]?.name} />,
                      },
                    ]}
                  />
                }
              >
                <a onClick={(e) => e.preventDefault()}>
                  <DownOutlined />
                </a>
              </Dropdown>
            </Space>
          );
        },
      },
      {
        title: 'Qty/case',
        dataIndex: 'attr_case_qty',
        valueType: 'digit',
        sorter: true,
        align: 'right',
        hideInSearch: true,
        width: 60,
        render: (dom, record) => Util.numberFormat(record.attr_case_qty),
      },
      {
        title: 'SKU',
        dataIndex: 'sku',
        sorter: true,
        copyable: true,
        ellipsis: true,
        hideInSearch: true,
        width: 100,
        render: (dom, record) => {
          return (
            <a
              onClick={() => {
                setCurrentRow({
                  ...record,
                });
                setShowDetail(true);
              }}
            >
              {dom}
            </a>
          );
        },
      },
      {
        title: 'Categories',
        dataIndex: ['categories'],
        width: 120,
        align: 'left',
        ellipsis: true,
        hideInSearch: true,
        render: (dom, record) => {
          let categories = record?.categories || [];
          if (!record?.note?.catMode) {
            categories = [...(record?.categories || []), ...(record.item?.categories || [])] ?? [];
            categories = _.uniqBy(categories, 'id');
          }
          return (
            <a
              onClick={() => {
                setCurrentRow({ ...record });
                handleUpdateCategoriesModalVisible(true);
              }}
            >
              {categories.map((x) => x.name).join(', ') || <div>&nbsp;</div>}
            </a>
          );
        },
      },
      {
        title: 'Total Qty (IBO)',
        dataIndex: ['quantity_total'],
        width: 90,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer bl2 b-gray',
        tooltip: 'Please click to view stock details.',
        render: (dom, record) => {
          return record.is_single
            ? Util.numberFormat(record?.quantity_total)
            : Util.numberFormat(record?.box_quantity_total);
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: () => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Total Qty (Sys Stock)',
        dataIndex: ['stock_stables_sum_box_qty'],
        width: 90,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer',
        tooltip: 'Please click to view stock details.',
        render: (dom, record) => {
          return record.is_single
            ? // ? Util.numberFormat(record?.stock_stables_sum_piece_qty)
              Util.numberFormat(record?.parent_stock_stables_sum_total_piece_qty)
            : Util.numberFormat(record?.stock_stables_sum_box_qty);
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: () => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'Magento Qty',
        dataIndex: ['mag_inventory_stocks_sum_quantity'],
        width: 90,
        align: 'right',
        hideInSearch: true,
        className: 'cursor-pointer',
        tooltip: 'Please click to view stock details. This may be outdated.',
        render: (dom, record) => {
          return Util.numberFormat(record?.mag_inventory_stocks_sum_quantity);
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: () => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
      {
        title: 'IBO',
        dataIndex: 'ibo_group',
        hideInSearch: true,
        className: 'bl2 b-gray',
        children: [
          {
            title: 'Latest Qty',
            dataIndex: ['latest_ibo', 'box_qty'],
            width: 80,
            align: 'right',
            ellipsis: true,
            hideInSearch: true,
            className: 'cursor-pointer bl2 b-gray',
            render: (dom, record) => {
              const isSingle = record.is_single;
              return (
                <>
                  {isSingle
                    ? Util.numberFormat(record?.latest_ibo?.qty)
                    : Util.numberFormat(record?.latest_ibo?.box_qty)}
                  {/* <Row gutter={4}>
                    <Col span={12}>{Util.numberFormat(record?.latest_ibo?.qty)}</Col>
                    <Col span={12}>{Util.numberFormat(record?.latest_ibo?.box_qty)}</Col>
                  </Row> */}
                </>
              );
            },
          },
          {
            title: 'Latest Price',
            dataIndex: ['latest_ibo', 'price'],
            width: 90,
            align: 'right',
            ellipsis: true,
            hideInSearch: true,
            render: (dom, record) => {
              const vat = record.item?.vat?.value || 0;
              const isSingle = record.is_single;
              return (
                <>
                  <Row
                    gutter={4}
                    title="View prices list..."
                    className="cursor-pointer"
                    onClick={() => {
                      setCurrentRow({ ...record });
                      setShowImportedPrices(true);
                    }}
                    style={{ minHeight: 24 }}
                  >
                    <Col span={12}>
                      <SPrices price={record?.latest_ibo?.price} vat={vat} />
                    </Col>
                    {!isSingle && (
                      <Col span={12}>
                        <SPrices price={(record?.latest_ibo?.price ?? 0) * (record?.attr_case_qty ?? 0)} vat={vat} />
                      </Col>
                    )}
                  </Row>
                </>
              );
            },
          },
          {
            title: 'Min EXP Date',
            dataIndex: ['ibos_item_min_exp_date'],
            width: 100,
            align: 'center',
            ellipsis: true,
            sorter: true,
            hideInSearch: true,
            render: (dom, record) => {
              return <>{Util.dtToDMY(record.is_single ? record.ibos_item_min_exp_date : record.ibos_min_exp_date)}</>;
            },
          },
        ],
      },
      ...pricesColDefs,
      {
        title: 'Texts',
        dataIndex: 'texts',
        tooltip: 'Please click to update texts',
        hideInSearch: true,
        children: [
          {
            title: 'Name DE',
            dataIndex: ['ean_texts', 0, 'name'],
            width: 180,
            align: 'left',
            ellipsis: true,
            hideInSearch: true,
            tooltip: 'Orange color indicates the inherited value from its item.',
            render: (dom, record) => {
              return (
                <a onClick={() => handleTextsClick(record)}>
                  <Typography.Text type={record?.ean_texts?.[0]?.name ? undefined : 'warning'}>
                    {record?.ean_texts?.[0]?.name ?? record?.item?.name ?? (
                      <CloseOutlined style={{ color: '#cc2200' }} />
                    )}
                  </Typography.Text>
                </a>
              );
            },
          },
          {
            title: 'Desc1 DE',
            dataIndex: ['ean_texts', 0, 'description1'],
            sorter: false,
            width: 90,
            align: 'center',
            ellipsis: true,
            hideInSearch: true,
            render: (dom, record) => {
              return (
                <a onClick={() => handleTextsClick(record)}>
                  {record?.ean_texts?.[0]?.description1 ? (
                    <CheckOutlined style={{ color: 'green' }} />
                  ) : (
                    <CloseOutlined style={{ color: '#cc2200' }} />
                  )}
                </a>
              );
            },
          },
        ],
      },

      {
        title: 'EAN base info',
        dataIndex: 'eanGroup',
        align: 'center',
        hideInSearch: true,
        children: [
          {
            title: 'Min qty',
            dataIndex: 'minimum_order_qty',
            valueType: 'digit',
            align: 'right',
            width: 80,
            render: (dom, record) => Util.numberFormat(record.minimum_order_qty),
          },
          {
            title: 'Item base',
            dataIndex: 'item_base',
            valueType: 'digit',
            align: 'right',
            width: 80,
            render: (dom, record) => Util.numberFormat(record.item_base, false, 3),
          },
          {
            title: 'Unit',
            dataIndex: 'item_base_unit',
            valueType: 'digit',
            align: 'right',
            width: 80,
            render: (dom, record) => record.item_base_unit,
          },
        ],
      },
      {
        title: 'Wt. / W / H / L',
        align: 'center',
        dataIndex: 'girthGroup',
        hideInSearch: true,
        children: [
          {
            title: 'Wt. (g)',
            dataIndex: 'weight',
            valueType: 'digit',
            sorter: true,
            hideInSearch: true,
            align: 'right',
            width: 100,
            render: (dom, record) => Util.numberFormat(record.weight),
          },
          {
            title: 'W (cm)',
            dataIndex: 'width',
            valueType: 'digit',
            width: 80,
            align: 'right',
            sorter: true,
            hideInSearch: true,
            render: (dom, record) => Util.numberFormat(record.width),
          },
          {
            title: 'H (cm)',
            dataIndex: 'height',
            valueType: 'digit',
            width: 80,
            align: 'right',
            sorter: true,
            hideInSearch: true,
            render: (dom, record) => Util.numberFormat(record.height),
          },
          {
            title: 'L (cm)',
            dataIndex: 'length',
            valueType: 'digit',
            width: 80,
            align: 'right',
            sorter: true,
            hideInSearch: true,
            render: (dom, record) => Util.numberFormat(record.length),
          },
        ],
      },

      {
        title: 'Updated on',
        sorter: true,
        dataIndex: 'updated_on',
        valueType: 'dateTime',
        search: false,
        ellipsis: true,
        // fixed: 'right',
        width: 100,
        renderFormItem: (item, { defaultRender }) => {
          return defaultRender(item);
        },
        render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
      },

      {
        title: 'ID',
        dataIndex: 'id',
        width: 50,
        search: false,
        sorter: true,
        align: 'center',
      },
      {
        title: 'Option',
        dataIndex: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 110,
        render: (dom, record) => {
          const options = [
            <a
              key="update-item"
              title="Update item"
              onClick={() => {
                handleUpdateItemModalVisible(true);
                setCurrentItem({
                  ...record.item,
                });
              }}
            >
              <SnippetsOutlined className="btn-gray" />
            </a>,
            <a
              key="texts"
              title="Update texts"
              onClick={() => {
                handleUpdateTextsModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <FileTextOutlined />
            </a>,
            <a
              key="config"
              title="Update attributes"
              onClick={() => {
                handleUpdateModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <EditTwoTone />
            </a>,

            <a
              key="files"
              title="Update pictures"
              onClick={() => {
                handleUpdatePicturesModalVisible(true);
                setCurrentRow({
                  ...record,
                });
              }}
            >
              <PictureOutlined />
            </a>,
            <Popconfirm
              key="upsync"
              placement="topRight"
              title={
                <>
                  Are you sure you want to up sync the EAN？ <br />
                  <br />A new product will be created in the shop if SKU {`"${record.sku}"`} does not exist.
                </>
              }
              overlayStyle={{ width: 350 }}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                if (!record.id) return;
                const hide = message.loading(`Up syncing ...`, 0);
                usProductFull(record.id)
                  .then((res) => {
                    // console.log('---- Up sync result ---');
                    //console.log(res);
                    if (res.sku) {
                      message.success('Successfully up synced on shop!');
                    } else {
                      message.error(res.upSyncMessage || 'Failed to up sync EAN!');
                    }
                  })
                  .catch((e) => {
                    message.error(e.message ?? 'Failed to upsync!');
                  })
                  .finally(() => {
                    hide();
                  });
              }}
            >
              <CloudUploadOutlined className="btn-gray" />
            </Popconfirm>,
          ];
          return <Space>{options.map((option) => option)}</Space>;
        },
      },
    ],
    [pricesColDefs],
  );

  // Category trees
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  const reloadTree = useCallback(async () => {
    return getCategoryList({}, {}, {}).then((res) => {
      setTreeData(res.data);
      return res.data;
    });
  }, []);

  useEffect(() => {
    reloadTree();
  }, [reloadTree]);

  useEffect(() => {
    getIBOManagementACList({}, {}).then((res) => {
      setIbomList(res);
    });
  }, []);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setTableConfig({ pagination, filters, sorter });
  };

  const handleExportMenuClick: MenuProps['onClick'] = async (e) => {
    setLoadingExport(true);
    const hide = message.loading('Exporting...');
    const formValues = searchFormRef.current?.getFieldsValue();
    switch (e.key) {
      case 'export-core':
        exportEanList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-core-alt':
        exportEanListAlt(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'export-price':
        exportEanPriceList(formValues, tableConfig?.sorter, tableConfig?.filters)
          .then((res: any) => {
            if (res) {
              const downloadUrl = `${API_URL}${(res as any).data.file}`;
              window.location.href = downloadUrl;
            }
          })
          .finally(() => {
            setLoadingExport(false);
            hide();
          });
        break;
      case 'sync-sku':
        break;
    }
  };

  return (
    <PageContainer className={styles.eanListContainer}>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ean_grid_all' + eanComponentProps.eanType, {
            status: 1,
            sku: location.query?.sku || '',
            minimum_order_qty: 1,
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [
                ...dom,
                <Dropdown
                  key="export-menu"
                  disabled={loadingExport}
                  overlay={
                    <Menu
                      onClick={handleExportMenuClick}
                      items={[
                        {
                          label: 'Download EANs',
                          key: 'export-core',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          label: 'Download prices',
                          key: 'export-price',
                          icon: <DownloadOutlined type="primary" />,
                        },
                        {
                          type: 'divider',
                        },
                        {
                          label: 'Download As Excel',
                          key: 'export-core-alt',
                          icon: <DownloadOutlined type="primary" />,
                        },
                      ]}
                    />
                  }
                >
                  <Button loading={loadingExport}>
                    <Space>
                      {!loadingExport && <DownloadOutlined type="primary" />}
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>,
              ];
            },
          }}
        >
          {eanComponentProps.eanType == 'default' && (
            <ProFormSelect
              name="ean_type_search"
              placeholder="Select type"
              label="Type"
              options={[
                { value: '', label: 'All' },
                { value: 'base', label: 'Single' },
                { value: 've', label: 'Multi' },
              ]}
              formItemProps={{ style: { width: 140 } }}
              fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
            />
          )}
          <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} />
          <ProFormSelect
            name="producers[]"
            label="Producers"
            placeholder="Please select producers"
            mode="multiple"
            request={getProducerListSelectOptions}
            width={180}
          />
          <ProFormSelect
            name="trademarks[]"
            label="Trademarks"
            placeholder="Please select trademarks"
            mode="multiple"
            request={getTrademarkListSelectOptions}
            width={180}
          />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormSelect
            name="status"
            placeholder="Select status"
            label=""
            options={[
              { value: '', label: 'All' },
              { value: 1, label: 'Active' },
              { value: 0, label: 'Inactive' },
            ]}
            formItemProps={{ style: { width: 100 } }}
            fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
          />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormSelect
            name="create_type"
            placeholder="Creation type"
            label="Creation mode"
            options={[
              { value: '', label: 'All' },
              { value: '1', label: 'Manually' },
              { value: '2', label: 'Imported' },
            ]}
            // formItemProps={{ style: { width: 140 } }}
            fieldProps={{ onChange: () => searchFormRef.current?.submit() }}
          />
          <SDatePicker name="created_on_start" label="Date of creation" />
          <SDatePicker name="created_on_end" addonBefore="~" />
          <ProFormSelect name={'ibom_id'} showSearch label="IBOM" options={ibomList} />
          <ProFormDigit name={'minimum_order_qty'} label="Min. Qty" width={80} placeholder={'Min. Qty'} />
          <ProFormSelect
            name="product_websites"
            label="Websites"
            width={130}
            mode="multiple"
            placeholder={'Websites'}
            options={appSettings.storeWebsites
              ?.filter((x) => x.code != 'admin')
              ?.map((x) => ({
                value: `${x.id}`,
                label: x.name,
              }))}
          />
        </ProForm>
      </Card>
      <ProTable<API.Ean, API.PageParams>
        headerTitle={'EANs List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        scroll={{ x: 800 }}
        size="small"
        bordered
        onChange={handleTableChange}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 20, //DEFAULT_PER_PAGE_PAGINATION,
        }}
        rowClassName={(record) => (record.is_single ? 'row-single' : 'row-multi')}
        search={false}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ean_grid_all' + eanComponentProps.eanType, searchFormValues);
          if (searchFormValues.created_on_start) {
            searchFormValues.created_on_start = Util.dtToYMD(searchFormValues.created_on_start);
          } else {
            searchFormValues.created_on_start = undefined;
          }
          if (searchFormValues.created_on_end) {
            searchFormValues.created_on_end = Util.dtToYMD(searchFormValues.created_on_end);
          } else {
            searchFormValues.created_on_end = undefined;
          }

          setLoading(true);
          return getEanList(
            {
              ...params,
              ...searchFormValues,
              ean_type: eanComponentProps.eanType || 'base',
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        columns={columns}
        tableAlertRender={false}
        rowSelection={{
          columnWidth: 30,
          selectedRowKeys: selectedRowsState.map((x) => x.id as React.Key),
          onChange: (dom, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                &nbsp;EANs.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Popconfirm
            title={
              <>
                Are you sure you want to download pictures?
                <br />
                All local images will be removed.
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              if (selectedRowsState.length) {
                const hide = message.loading('Downloading EAN images... It would take some time.', 0);
                dsProductImages({ skus: selectedRowsState.map((x) => x.sku as string) })
                  .then(() => {
                    actionRef.current?.reloadAndRest?.();
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }
            }}
          >
            <Button type="primary" icon={<FileSyncOutlined />}>
              Download pictures
            </Button>
          </Popconfirm>
          <Popconfirm
            title={<>Are you sure you want to delete selected EANs?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async () => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateAttributeForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          setCurrentRow((prev) => ({ ...prev, ...updatedRow }));
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateCategoriesForm
        modalVisible={updateCategoriesModalVisible}
        handleModalVisible={handleUpdateCategoriesModalVisible}
        initialValues={currentRow || {}}
        treeData={treeData}
        onSubmit={async () => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateCategoriesModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (values) => {
          setCurrentRow((prev) => ({
            ...prev,
            ...values,
          }));

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdatePicturesForm
        modalVisible={updatePicturesModalVisible}
        handleModalVisible={handleUpdatePicturesModalVisible}
        initialValues={{
          id: currentRow?.id,
          parent_id: currentRow?.parent_id,
          files: currentRow?.files || [],
          sku: currentRow?.sku,
          ean: currentRow?.ean,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePicturesModalVisible(false);
        }}
      />

      <UpdateItemForm.default
        modalVisible={updateItemModalVisible}
        handleModalVisible={handleUpdateItemModalVisible}
        initialValues={{ ...currentItem, ean: currentRow?.parent?.ean }}
        onSubmit={async () => {
          setCurrentItem(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateItemModalVisible(false);

          if (!showDetail) {
            setCurrentItem(undefined);
          }
        }}
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.id,
          item_id: currentRow?.item_id,
          parent_id: currentRow?.parent_id,
          is_single: currentRow?.is_single,
          sku: currentRow?.sku,
          ean: currentRow?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.Ean>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.Ean>[]}
          />
        )}
      </Drawer>

      <Drawer
        width={700}
        title={`Buying Price History - ${currentRow?.ean}`}
        open={showImportedPrices}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
        // closable={false}
      >
        {currentRow?.id && <ImportedPrices itemEan={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default Ean;
