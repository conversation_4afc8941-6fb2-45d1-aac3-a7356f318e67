ALTER TABLE `ean_supplier`
    ADD COLUMN `item_id` BIGINT UNSIGNED NULL AFTER `supplier_id`,
    ADD CONSTRAINT `FK_ean_supplier_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`);

ALTER TABLE `ean_supplier`
    ADD CONSTRAINT `FK_ean_supplier_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `ean_supplier`
    DROP FOREIGN KEY `FK_ean_supplier_supplier_id`;

UPDATE ean_supplier SET item_id = (SELECT item_id FROM item_ean WHERE id=`ean_supplier`.`ean_id`);

ALTER TABLE `ibom` CHANGE `order_no` `order_no` INT NOT NULL;


ALTER TABLE `ean_supplier`
    ADD CONSTRAINT `FK_ean_supplier_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`);