import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormItem } from '@ant-design/pro-form';
import { Space, message } from 'antd';
import Util, { sn } from '@/util';
import { updateOrderExtra } from '@/services/foodstore-one/Magento/order';
import SNumpad from '@/components/Numpad';

const handleUpdate = async (fields: API.OrderExtra) => {
  const hide = message.loading("Updating order's weight...", 0);
  const data = { ...fields };
  try {
    await updateOrderExtra(sn(data.entity_id), data);
    message.success('Updated successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.OrderExtra>;

export type UpdateOrderExtraWeightFormModalProps = {
  order?: Partial<API.Order>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderExtra) => Promise<boolean | void>;
};

const UpdateOrderExtraWeightFormModal: React.FC<UpdateOrderExtraWeightFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, order } = props;
  const values = order?.extra ?? {};

  // Numpad
  const numpadFieldRef = useRef<HTMLInputElement>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(values || {}) };
      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, values]);

  return (
    <ModalForm
      title={"Confirm Order's Weight"}
      width="300px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const weight = sn(numpadFieldRef.current?.value);

        const dataUpdated: Partial<API.OrderExtra> = {
          ...value,
          weight_confirmed: weight,
          entity_id: values?.entity_id,
          note1_status: '',
        };
        const success = await handleUpdate(dataUpdated);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          handleModalVisible(false);
          if (onSubmit) await onSubmit(dataUpdated);
        }
      }}
    >
      <ProFormItem label={`Weight - ${order?.weight} kg`}>
        <Space size={8} style={{ marginBottom: 16 }}>
          <input ref={numpadFieldRef as any} className="ant-input ant-input-lg" style={{ width: 150 }} />
          <div className="ant-form-item-explain ant-form-item-explain-connected">English format.</div>
        </Space>
        <SNumpad fieldRef={numpadFieldRef} eleOptions={{ showDecimal: true }} />
      </ProFormItem>
    </ModalForm>
  );
};

export default UpdateOrderExtraWeightFormModal;
