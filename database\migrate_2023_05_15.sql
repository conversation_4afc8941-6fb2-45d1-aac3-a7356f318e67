INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('MAG_ADMIN_URL_ORDER_BASE', 'Magento Admin URL', 'https://foodstore.one/admin_v7l7uf/', 'Base URL of order in Magento backend', '');

INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('MAG_ADMIN_URL_TRACKING', 'Magento Admin URL', 'https://foodstore.one/admin_v7l7uf/', 'URL template of tracking no in Magento backend', '');


INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('EMAIL_STATUS_NO_ACTION', 'Email Config', NULL, '\'No Action Needed\' Status', 'Specify `code` of an Email Status');


CREATE TABLE `crm_case`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `status`     varchar(255) DEFAULT NULL COMMENT 'sys_config code: New, in Progress, Wait for customer, Wait for ext., Closed',
    `order_id`   int(11)      DEFAULT NULL COMMENT 'Order ID: FK in xmag_order_item',
    `created_on` datetime     DEFAULT NULL COMMENT 'Created on',
    `created_by` int(11)      DEFAULT NULL COMMENT 'creator',
    `updated_on` datetime     DEFAULT NULL COMMENT 'Updated on',
    `updated_by` int(11)      DEFAULT NULL COMMENT 'updater',
    PRIMARY KEY (`id`),
    KEY `IDX_crm_case_order_id` (`order_id`),
    KEY `IDX_crm_case_status` (`status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `crm_case_note`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `case_id`    int(11) NOT NULL COMMENT 'CRM Case ID (FK)',
    `note`       longtext DEFAULT NULL COMMENT 'Note in rich text',
    `created_on` datetime DEFAULT NULL COMMENT 'Created on',
    `created_by` int(11)  DEFAULT NULL COMMENT 'Creator',
    `updated_on` datetime DEFAULT NULL COMMENT 'Updated on',
    `updated_by` int(11)  DEFAULT NULL COMMENT 'Updater',
    PRIMARY KEY (`id`),
    KEY `FK_crm_case_note_case_id` (`case_id`),
    CONSTRAINT `FK_crm_case_note_case_id` FOREIGN KEY (`case_id`) REFERENCES `crm_case` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

ALTER TABLE `email`
    ADD COLUMN `crm_case_id` INT NULL COMMENT 'CRM Case ID (FK)' AFTER `status`,
    ADD CONSTRAINT `FK_email_crm_case_id` FOREIGN KEY (`crm_case_id`) REFERENCES `crm_case` (`id`) ON UPDATE CASCADE ON DELETE SET NULL;


ALTER TABLE `crm_case` ADD COLUMN `notes` LONGTEXT NULL COMMENT 'Notes' AFTER `order_id`;

