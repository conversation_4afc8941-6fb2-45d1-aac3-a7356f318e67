import { useCallback, useMemo, useState } from 'react';
import { Image } from 'antd';

const useImagesGroupPreview = () => {
  const [openImagesPreviewModal, setOpenImagesPreviewModal] = useState<boolean>(false);
  const [images, setImages] = useState<string[]>();

  const imagePreviewGroupBody = useMemo(() => {
    return (
      <Image.PreviewGroup
        preview={{
          visible: openImagesPreviewModal,
          onVisibleChange: (value) => setOpenImagesPreviewModal(value),
        }}
      >
        {images?.map((x) => {
          return (
            <Image
              key={x}
              src={x}
              preview={{
                src: x,
              }}
              wrapperStyle={{ display: 'none' }}
            />
          );
        })}
      </Image.PreviewGroup>
    );
  }, [images, openImagesPreviewModal]);

  const renderPreviewItem = useCallback((imgList: string[]) => {
    return imgList?.length ? (
      <a
        onClick={() => {
          setOpenImagesPreviewModal(true);
          setImages(imgList ?? []);
        }}
      >
        {imgList[0].slice(-3)}
      </a>
    ) : null;
  }, []);

  return { setOpenImagesPreviewModal, setImages, imagePreviewGroupBody, renderPreviewItem };
};

export default useImagesGroupPreview;
