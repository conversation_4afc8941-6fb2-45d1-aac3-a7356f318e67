@import (reference) '~antd/es/style/themes/index';
@import '~antd/es/style/variable.less';

@import './theme_custom.less';

html,
body,
#root {
  height: 100%;
}

/* ._ibo_item-buying-order {
  @yellow-3: red;
}
.ant-page-header-heading-title {
  color: @yellow-3 !important;
} */

body {
  font-size: 12px;
  line-height: 22px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

.ant-message-notice-content {
  max-width: 500px;
}

.ant-menu-sub.ant-menu-inline > .ant-menu-item,
.ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  height: 30px;
  margin-top: 2px;
  margin-bottom: 2px;
  line-height: 30px;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// Compatible with IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.search-form.ant-form-inline {
  .ant-form-item {
    margin-bottom: 8px;
  }

  .ant-space.ant-space-horizontal {
    margin-bottom: 8px;
    margin-left: auto;
  }
  &.ant-form-inline .ant-form-item {
    margin-bottom: 8px;
  }
}

.ant-table-sticky-scroll-bar {
  height: 12px !important;
}

.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.pink {
  color: #9c5fe3;
}
.green {
  color: #026c02;
}
.red {
  color: #e00;
}
.gross-price {
  color: #9c5fe3;
}
.dark-blue {
  color: #4b81c7;
}

.c-blue {
  color: #1890ff;
}

.c-yellow {
  color: #baba09;
}

.c-orange {
  color: #f9a409;
}
.c-lightorange {
  color: #f4c877;
}

.c-darkorange {
  color: #c17c08 !important;
}

.c-lightgrey {
  color: lightgrey;
}

.c-lightgrey2 {
  color: #e3e3e3;
}

.c-grey {
  color: grey;
}

.c-green {
  color: #07c807;
}
.c-green-dark {
  color: #026c02;
}

.c-lightred {
  color: lightcoral;
}

.c-red {
  color: #ee2201;
}
.c-red8 {
  color: @red-8;
}

.c-dark-purple {
  color: #9882dc !important;
}

.bg-lightgrey {
  background: rgb(241, 240, 240) !important;
}

.bg-green {
  background: green;
}

.bg-green1 {
  background: #f6ffed !important;
}
.bg-green2 {
  background: #d9f7be !important;
}
.bg-green3 {
  background: #b7eb8f !important;
}

.bg-light-green {
  background: #f8fcf8 !important;
}

.bg-red {
  background: #f00 !important;
}
.bg-light-red {
  background: #ff8686 !important;
}

.bg-light-red1 {
  background: #f8d7d7 !important;
}

.bg-light-red2 {
  background: #f8e3e3 !important;
}
.bg-red1 {
  background: #fff1f0 !important;
}
.bg-red2 {
  background: #ffccc7 !important;
}
.bg-red3 {
  background: #ffa39e !important;
}
.bg-red4 {
  background: #ff7875 !important;
}

.bg-pink1 {
  background: #fff0f6 !important;
}
.bg-pink2 {
  background: #ffd6e7 !important;
}
.bg-pink3 {
  background: #ffadd2 !important;
}
.bg-pink4 {
  background: #ff85c0 !important;
}

.bg-light-pink2 {
  background: #f3c5cc !important;
}

.bg-light-orange {
  background: #fdc763 !important;
}
.bg-light-orange1 {
  background: #e4d3b6 !important;
}
.bg-light-orange2 {
  background: #ffeed0 !important;
}
.bg-orange1 {
  background: #fff7e6 !important;
}
.bg-orange2 {
  background: #ffe7ba !important;
}
.bg-orange3 {
  background: #ffd591 !important;
}

.bg-blue1 {
  background: #e6f4ff !important;
}
.bg-blue2 {
  background: #bae0ff !important;
}
.bg-blue3 {
  background: #91caff !important;
}
.bg-light-blue {
  background: #eef5fa !important;
}
.bg-light-blue2 {
  background: #f5f9fc !important;
}

.bg-yellow {
  background-color: yellow;
}
.bg-lightyellow {
  background-color: #f5f2d2;
}
.bg-yellow1 {
  background-color: @yellow-1;
}
.bg-yellow2 {
  background-color: @yellow-2;
}
.bg-yellow3 {
  background-color: @yellow-3;
}

.bg-gray {
  background-color: gray;
}
.bg-gray2 {
  background-color: #fafafa;
}
.bg-gray3 {
  background-color: #f5f5f5;
}
.bg-gray4 {
  background-color: #f0f0f0;
}

.btn-gray {
  color: gray;
  &:hover {
    color: #1890ff;
  }
}

.d-none {
  display: none;
}

.np {
  padding: 0 !important;
}
.align-top {
  vertical-align: top;
}
.align-middle {
  vertical-align: middle;
}

.bl2,
.bl-2 {
  border-left: 2px solid #000 !important;
  &.b-pink {
    border-left: 2px solid #9c5fe3 !important;
  }
  &.b-gray,
  &.b-grey {
    border-left: 2px solid #eee !important;
  }
}

.br2 {
  border-right-width: 2px !important;
  border-right-style: solid !important;
  &.b-pink {
    border-right-color: #9c5fe3 !important;
  }
  &.b-black {
    border-right-color: #000 !important;
  }
  &.b-gray,
  &.b-grey {
    border-right-color: #eee !important;
  }
}
.bb1 {
  border-bottom-width: 1px !important;
  border-bottom-style: solid !important;
  &.bb-pink {
    border-bottom-color: #9c5fe3 !important;
  }
  &.bb-black {
    border-bottom-color: #000 !important;
  }
  &.bb-gray,
  &.bb-grey {
    border-bottom-color: #eee !important;
  }
}
.bb-trans {
  border-bottom: transparent !important;
}

.h-full {
  height: auto !important;
  height: 100%;
  min-height: 100%;
}

.w-full {
  width: 100%;
}

/*
Form customization
-------------------------------------------------------------------
*/
.textForm .ant-pro-form-list-container {
  display: flex;
  flex-wrap: wrap;
}

.ant-pro-form-query-filter .ant-col {
  flex: 0 0 auto !important;
  max-width: 100%;
}

.ant-pro-form-query-filter > .ant-row > .ant-col:last-child {
  margin-left: auto;
}
.ant-pro-form-query-filter .ant-row.ant-form-item {
  margin-bottom: 12px;
}
.ant-table .ant-table-tbody > tr.row-multi {
  & > td:first-child {
    border-left: 2px solid #9c5fe3;
  }
}
.ant-table .ant-table-tbody > tr.total-row {
  td {
    font-weight: bold;
    background-color: #e0f4e0;
  }
}

.ant-table .ant-table-tbody > tr.bt {
  & > td {
    border-top: 1px solid #444;
  }
}

.ant-table .ant-table-tbody > tr.tr-bg-grey {
  td {
    background-color: #fafafa;
  }
}

.ant-table .ant-table-tbody > tr.tr-bg-lightyellow {
  td {
    background-color: #f5f2d2;
  }
  &:hover td {
    background-color: #f5f2d2;
  }
}

.ant-table .ant-table-tbody > tr.tr-bg-green1 {
  td {
    background-color: #adff2f;
  }
  &:hover td {
    background-color: #adff2f;
  }
}
.ant-table .ant-table-tbody > tr.tr-bg-green2 {
  td {
    background-color: #ffcccb;
  }
  &:hover td {
    background-color: #ffcccb;
  }
}
.ant-table .ant-table-tbody > tr.tr-bg-green3 {
  td {
    background-color: #aeffae;
  }
  &:hover td {
    background-color: #aeffae;
  }
}

.ant-table .ant-table-tbody > tr.bg-grey {
  & > td {
    color: #999 !important;
    background-color: #fafafa;
    .ant-typography {
      color: #999 !important;
    }
  }
}
.ant-table .ant-table-tbody > tr.bg-grey:hover {
  & > td {
    color: #ccc !important;
    background-color: #fafafa;
  }
}
.ant-table .ant-table-tbody > tr.reset-tds-bg.bg-grey:hover {
  & > td {
    color: #999 !important;
    background-color: #fafafa !important;
  }
}

.ant-table .ant-table-tbody > tr.reset-tds-bg.bg-light-green,
.ant-table .ant-table-tbody > tr.reset-tds-bg.bg-light-green:hover {
  & > td {
    background-color: #f8fcf8 !important;
  }
}

/*
Modal customization
-------------------------------------------------------------------
*/
.ant-modal.m-multi .ant-modal-header,
.ant-drawer-header {
  border-bottom-color: #b287e2 !important;
  border-bottom-width: 2px !important;
}

.ant-modal.m-masked .ant-modal-body {
  cursor: progress;
  opacity: 0.6;
}

/*
General CSS classes
-------------------------------------------------------------------
*/
.cursor-pointer {
  cursor: pointer;
}

.margin-0,
.m-0 {
  margin: 0 !important;
}
.padding-0,
.p-0 {
  padding: 0 !important;
}

.text-small,
.text-sm,
.text-sm > .ant-typography {
  font-size: 80%;
}
.text-xs {
  font-size: 70%;
}
.text-md {
  font-size: 120%;
}
.text-left {
  text-align: left;
}
.bold {
  font-weight: bold;
}

.italic {
  font-style: italic;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.rounded-full {
  border-radius: 100%;
}

.opacity-80 {
  opacity: 0.8;
}

/*
Popup
-------------------------------------------------------------------
*/
.popup {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  text-align: left;
  list-style-type: none;
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 4px;
  outline: none;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.popup li {
  clear: both;
  margin: 0;
  padding: 5px 12px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  white-space: nowrap;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.popup li:hover {
  background-color: #e6f7ff;
}

.popup li > i {
  margin-right: 8px;
}

.table-label {
  display: inline-block;
  padding-right: 8px;
}

/*
Notification
-------------------------------------------------------------------
*/

/* .ant-notification-notice {
  padding: 8px 12px;
}

.ant-notification-notice-close {
  top: 12px;
} */

.ant-notification-notice-with-icon {
  max-height: 300px;
  overflow-y: auto;
}

.ant-page-header .ant-alert {
  padding: 6px 10px;
}

/*
Extra Buttons
-------------------------------------------------------------------
*/
.ant-btn-primary {
  &.btn-green {
    background: #198754;
    border-color: #198754;

    &:hover {
      background: #1ea063;
      border-color: #1ea063;
    }

    &:active {
      background: #146c43;
      border-color: #13653f;
    }

    &:disabled {
      /* background: #22af6d;
      border-color: #22af6d; */

      color: rgba(0, 0, 0, 0.25);
      text-shadow: none;
      background: #f5f5f5;
      border-color: #d9d9d9;
      box-shadow: none;
    }
  }

  &.btn-dark-red {
    background: #5c0011;
    border-color: #5c0011;

    &:hover {
      background: #820014;
      border-color: #820014;
    }

    &:active {
      background: #a8071a;
      border-color: #a8071a;
    }

    &:disabled {
      color: #a8071a;
      background: #ffa39e;
      border-color: #ffa39e;
    }
  }

  &.btn-pink {
    background: #9e1068;
    border-color: #9e1068;

    &:hover {
      background: #c41d7f;
      border-color: #c41d7f;
    }

    &:active {
      background: #c41d7f;
      border-color: #9e1068;
    }

    &:disabled {
      color: #f759ab;
      background: #ffadd2;
      border-color: #ffadd2;
    }
  }

  &.btn-yellow {
    color: rgba(0, 0, 0, 0.85);
    background: #ffec3d;
    border-color: #ffec3d;

    &:hover {
      color: rgba(0, 0, 0, 0.85);
      background: #fff566;
      border-color: #fff566;
    }

    &:active {
      color: rgba(0, 0, 0, 0.85);
      background: #fff566;
      border-color: #fadb14;
    }

    &:disabled {
      /* color: rgba(0, 0, 0, 0.65);
      background: #ffffb8;
      border-color: #ffffb8;
       */
      color: rgba(0, 0, 0, 0.25);
      text-shadow: none;
      background: #f5f5f5;
      border-color: #d9d9d9;
      box-shadow: none;
    }
  }
}
.ant-btn-default {
  &.btn-green {
    color: #198754;
    border-color: #198754;

    &:hover {
      color: #1ea063;
      border-color: #1ea063;
    }

    &:active {
      color: #146c43;
      border-color: #13653f;
    }

    &:disabled {
      color: #22af6d;
      border-color: #22af6d;
    }
  }
}

.ant-pro-footer-bar {
  bottom: 18px !important;
}

/**
Inline editable cell
*/
.editable-cell-value-wrap {
  cursor: pointer;
}

.ant-table-cell .editable-cell-value-wrap {
  min-height: 26px;
}
.ant-table-cell:hover .editable-cell-value-wrap {
  /* border: 1px solid #d9d9d9;
  border-radius: 2px; */
  background: #eee;
}

.ant-table-cell.react-resizable .react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 100;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  &:hover {
    background-color: #eee;
  }
}

.ant-image-preview-img {
  background: white;
}

/**
Customization
----------------------------------------------- */
[data-theme='dark'] .ant-table-cell:hover .editable-cell-value-wrap {
  border: 1px solid #434343;
}

.ant-upload-list-picture-card-container {
  height: auto;
}

blockquote {
  padding-left: 24px;
  border-left: 1px solid #ddd;
}

/** Html editor
----------------------------------------------- */
.tox-tinymce {
  border: 1px solid #d9d9d9 !important;
  border-radius: 2px !important;
}

a .ant-typography {
  color: unset;
}

/** Override default css if TR has own style
-------------------------------------- */
tr.reset-tds-bg td.ant-table-column-sort {
  background: unset !important;
}
//bg-grey:hover
.ant-table-tbody > tr.ant-table-row.reset-tds-bg:hover > td,
.ant-table-tbody > tr.reset-tds-bg > td.ant-table-cell-row-hover {
  background: unset !important;
}

/** disable table selection
-------------------------------------- */
.ant-table-tbody > tr.disable-selection td.ant-table-selection-column .ant-checkbox-wrapper {
  display: none;
}
tr.ant-table-row.ant-table-row-level-0.bt2 td {
  border-top: 1px solid #000;
}

.rotate-rl {
  transform: rotate(180deg);
  writing-mode: vertical-rl;
}

/** Flex break */
.break {
  flex-basis: 100%;
  height: 0;
}

/** Special CSS for upload button */
.upload-btn-sm .ant-form-item-control-input {
  min-height: 24px;
}

table.headerSmallTable tr > td {
  padding: 0px 8px;
}

ul.msg {
  display: block;
  line-height: 1.2;
  list-style: outside;
  padding-inline-start: 0px;
}

ul.msg > li > ul {
  padding-left: 20px;
  list-style: circle;
  padding-inline-start: 0px;
}

.opacity-0 {
  opacity: 0;
}

.fake-none input {
  text-indent: -9999px;
}

@import './theme_small.less';
@import './theme_mobile.less';
