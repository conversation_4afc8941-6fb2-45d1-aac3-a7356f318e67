insert into `sys_dict` (`code`, `type`, `label`, `status`, `sort`, `value`, `parent_code`, `desc`, `settings`)
values ('FM_DATA_FILE_BASE_PATH', 'sys config', 'Misc Files Base Path', '1', '0', 'D:\\WHC-Data\\foodstore-MISC-FILES', NULL,
        'Data Files Base Path for browsing.', NULL);


-- Added 3 index keys to search logs properly.
ALTER TABLE `sys_log`
    ADD COLUMN `ean_id` BIGINT UNSIGNED NULL COMMENT 'FK: Ean ID' AFTER `status`,
    ADD COLUMN `ref1` VARCHAR (255) NULL COMMENT 'For Search' AFTER `ean_id`,
    ADD COLUMN `ref2` VARCHAR (255) NULL COMMENT 'For Search' AFTER `ref1`,
    ADD KEY `IDX_sys_log_ref1` (`ref1`),
    ADD KEY `IDX_sys_log_ref2` (`ref2`),
    ADD CONSTRAINT `FK_sys_log_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON UPDATE SET NULL ON DELETE SET NULL;

