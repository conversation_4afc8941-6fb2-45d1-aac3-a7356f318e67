/* eslint-disable @typescript-eslint/dot-notation */
import { Modal, Typography, Button, Space, Card } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2, ni, sn, sUrlByTpl } from '@/util';
import type { OrderModalSearchParamsType } from '@/pages/Report/Order/OrderTrademarkProducerList';
import { DictCode, EURO, MagentoQuoteStatusEnum } from '@/constants';
import { getQuotesListByPage, getQuoteStatusACList } from '@/services/foodstore-one/Magento/quote';
import ProForm, { ProFormCheckbox, ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { DefaultOptionType } from 'antd/lib/select';
import { MagentoQuoteStatus } from '@/pages/Magento/Quote';
import { useModel } from 'umi';

type RowType = API.MagQuote;

export type SearchFormValueType = Partial<API.MagQuote>;

type QuoteSelectionModalProps = {
  offer?: API.Offer;
  searchParams?: OrderModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSelect: (quote?: API.MagQuote) => void;
};

const QuoteSelectionModal: React.FC<QuoteSelectionModalProps> = (props) => {
  const { searchParams, modalVisible, handleModalVisible, onSelect, offer } = props;

  const { getDictByCode } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [statusACList, setStatusACList] = useState<(DefaultOptionType & { cnt?: number })[]>([]);

  const columns: ProColumns<API.MagQuote>[] = [
    {
      title: 'Quote ID',
      dataIndex: 'id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      width: 55,
      render(dom, entity) {
        return (
          <Typography.Link
            href={sUrlByTpl(getDictByCode(DictCode.MAG_ADMIN_URL_QUOTE), {
              quote_id: entity.id,
            })}
            title="Go to FsOne Shop admin page."
            target="_blank"
          >
            {dom}
          </Typography.Link>
        );
      },
    },
    {
      title: 'Store',
      dataIndex: ['store', 'code'],
      align: 'center',
      ellipsis: true,
      className: 'text-sm',
      width: 55,
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      align: 'center',
      ellipsis: true,
      width: 50,
      render: (__, record) => <MagentoQuoteStatus status={record.status ?? 0} />,
    },
    {
      title: 'Items Qty',
      dataIndex: 'items_count',
      align: 'right',
      width: 50,
      tooltip: 'Qty of included items',
      showSorterTooltip: false,
      render(dom, entity) {
        return ni(entity.items_count);
      },
    },
    {
      title: 'Ordered Qty',
      dataIndex: 'items_qty',
      align: 'right',
      width: 50,
      render(dom, entity) {
        return ni(entity.items_qty);
      },
    },

    {
      title: 'Grand Total',
      dataIndex: 'grand_total',
      sorter: true,
      align: 'right',
      width: 65,
      render: (dom, record) => (
        <span style={{ paddingRight: 12 }}>
          {nf2(record.grand_total)}
          {record.grand_total ? EURO : ''}
        </span>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'customer_fullname',
      width: 100,
      //   tooltip: 'Red colored rows are in warnings list. Highlighted parts may be wrong!',
      render: (dom, record) => record.customer_fullname,
    },
    {
      title: 'Email',
      dataIndex: 'customer_email',
      width: 100,
    },
    {
      title: 'Customer Note',
      dataIndex: 'quote_customer_note',
      width: 280,
      className: 'text-sm',
    },
    {
      title: 'Created on',
      dataIndex: ['created_at'],
      sorter: true,
      width: 70,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMMTz(record.created_at),
    },
    {
      title: 'Updated on',
      dataIndex: ['updated_at'],
      sorter: true,
      width: 70,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      defaultSortOrder: 'descend',
      render: (__, record) => Util.dtToDMYHHMMTz(record.updated_at),
    },
    {
      title: '',
      valueType: 'option',
      width: 80,
      showSorterTooltip: false,
      fixed: 'right',
      render: (__, record) =>
        offer?.quote_id != record.id ? (
          <Button
            type="primary"
            size="small"
            onClick={() => {
              onSelect(record);
              handleModalVisible(false);
            }}
          >
            Select
          </Button>
        ) : (
          <Button
            type="primary"
            ghost
            size="small"
            onClick={() => {
              onSelect({} as API.MagQuote);
              handleModalVisible(false);
            }}
          >
            Deselect
          </Button>
        ),
    },
  ];

  const loadStatusACList = () => {
    getQuoteStatusACList()
      .catch((err) => {
        Util.error(err);
        return [];
      })
      .then((res) => setStatusACList(res?.map((x) => ({ ...x, status: sn(x.status) }))));
  };

  useEffect(() => {
    loadStatusACList();
    searchFormRef.current?.setFieldsValue(Util.getSfValues('sf_quote_selection', {}));
  }, []);

  return (
    <Modal
      title={
        <>
          Select a Quote
          {offer
            ? ` for #${offer.offer_no || '-'} | ${Util.dtToDMY(offer.created_on)}${
                offer.note ? ` | ${offer.note}` : ''
              }${offer.quote?.customer_fullname ? ` - ${offer.quote?.customer_fullname}` : ''}${
                offer.ibo_status ? ` (${offer.ibo_status})` : ''
              }`
            : ''}
        </>
      }
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="1200px"
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <Card style={{ marginBottom: 0 }} bordered={false} size="small">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          size="small"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            name={'status'}
            label="Status"
            allowClear
            options={statusACList}
            initialValue={MagentoQuoteStatusEnum.Pending}
            placeholder={'Status'}
            width={'sm'}
          />
          <ProFormText name={'id'} label="Quote ID" width={'xs'} placeholder={'Quote ID'} />
          <ProFormText name={'keyWords'} label="KeyWords" width={'md'} placeholder={'Email / Name / Customer notes'} />
        </ProForm>
      </Card>

      <ProTable<API.MagQuote, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Quotes List</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{ defaultPageSize: sn(Util.getSfValues('sf_quote_selection_p')?.pageSize ?? 20) }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_quote_selection', searchFormValues);
          Util.setSfValues('sf_quote_selection_p', params);

          return getQuotesListByPage(
            {
              ...params,
              valid_qty_only: 1,
              with: 'store,offers.basic,offers_count',
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              return res;
            })
            .finally();
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default QuoteSelectionModal;
