import { getSupplierACList } from '@/services/foodstore-one/supplier';
import Util from '@/util';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { useCallback, useMemo, useState } from 'react';

/**
 * Auto completion list of supplier
 */
export default (
  defaultParams?: Record<string, any>,
  formRef?: React.MutableRefObject<ProFormInstance | undefined>,
  eleOptions?: any,
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [supplierOptions, setSupplierOptions] = useState<DefaultOptionType[]>([]);
  // selected supplier
  const [supplier, setSupplier] = useState<DefaultOptionType>();

  const searchSupplierOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getSupplierACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setSupplierOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        name="supplier_id"
        label={'Supplier'}
        placeholder="Please select supplier"
        mode="single"
        showSearch
        options={supplierOptions}
        required={eleOptions?.required}
        request={(params) => {
          return searchSupplierOptions(params);
        }}
        rules={
          eleOptions?.required
            ? [
                {
                  required: true,
                  message: 'Supplier is required',
                },
              ]
            : []
        }
        fieldProps={{
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          onChange: (value, option) => {
            setSupplier(option as any);
          },
        }}
        width={200}
      />
    );
  }, [eleOptions?.required, loading]);

  return { supplierOptions, searchSupplierOptions, loading, formElements, supplier };
};
