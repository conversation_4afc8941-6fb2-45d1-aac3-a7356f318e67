ALTER TABLE `item_ean_gdsn`
    ADD COLUMN `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'PK' FIRST,
    <PERSON><PERSON><PERSON> `ean` `ean` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Single or Package EAN. gtin in GDSN',
    CHANGE `single_ean` `package_ean` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Package EAN: parent_gtin in GDSN',
    DROP PRIMARY KEY,
    ADD PRIMARY KEY (`id`),
    ADD INDEX `uq_item_ean_gdsn_ean_package_ean` (`ean`, `package_ean`),
    ADD INDEX `idx_item_ean_gdsn_sync_last_change_dt` (`sync_last_change_dt`);

-- Update required in GDSN project
-- ALTER TABLE `gdsn_message_item` ADD INDEX `idx_gdsn_message_item_sync_last_change_dt` (`sync_last_change_dt`);


