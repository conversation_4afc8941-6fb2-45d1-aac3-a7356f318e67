import { useRef } from 'react';
import React from 'react';

import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import Util from '@/util';
import { Button, Space } from 'antd';
import { getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
import styles from './index.less';
import { useModel } from 'umi';

type HeaderGlobalFilterProps = {
  hide?: () => void;
};

const HeaderGlobalFilter: React.FC<HeaderGlobalFilterProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { setGSearch } = useModel('app-settings');

  return (
    <ProForm
      layout="horizontal"
      title="Global Filters"
      className={styles.headerGlobalFilter}
      formRef={formRef}
      grid
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      initialValues={Util.getSfValues('sf_gSearch') ?? {}}
      onFinish={async (values) => {
        Util.setSfValues('sf_gSearch', values);
        setGSearch(values);
        props?.hide?.();
      }}
      isKeyPressSubmit
      submitter={{
        render: (p, doms) => {
          return (
            <Space style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button type="primary" key="submit" size="small" onClick={() => formRef.current?.submit()}>
                Save
              </Button>
              <Button
                type="default"
                key="rest"
                size="small"
                onClick={() => {
                  formRef.current?.resetFields();
                }}
              >
                Reset
              </Button>
            </Space>
          );
        },
      }}
    >
      <ProFormText label="SKU" name="sku" width="xs" formItemProps={{ style: { marginBottom: 12 } }} />
      <ProFormText label="EAN" name="ean" formItemProps={{ style: { marginBottom: 12 } }} />
      <ProFormText label="Name" name="name" formItemProps={{ style: { marginBottom: 12 } }} />
      <ProFormSelect
        name="ean_type_search"
        placeholder="Select type"
        label="Type"
        width="xs"
        options={[
          { value: '', label: 'All' },
          { value: 'base', label: 'Single' },
          { value: 've', label: 'Multi' },
        ]}
        formItemProps={{ style: { marginBottom: 12 } }}
      />
      <ProFormSelect
        name="trademarks[]"
        label="Trademarks"
        placeholder="Please select trademarks"
        mode="multiple"
        request={getTrademarkListSelectOptions}
        formItemProps={{ style: { marginBottom: 12 } }}
      />
    </ProForm>
  );
};

export default HeaderGlobalFilter;
