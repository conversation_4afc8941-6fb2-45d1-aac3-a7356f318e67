import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addOffer } from '@/services/foodstore-one/Offer/offer';
import { message } from 'antd';
import Util from '@/util';
import { DictCode } from '@/constants';
import SProFormDigit from '@/components/SProFormDigit';
import { useModel } from 'umi';

const handleAdd = async (fields: API.Offer) => {
  const hide = message.loading('Adding...', 0);
  const data = {
    ...fields,
  };
  try {
    await addOffer(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.Offer>;

export type CreateFormProps = {
  values?: Partial<API.Offer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Offer) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { getDictByCode } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldValue('percentage', getDictByCode(DictCode.OFFER_DEFAULT_PERCENTAGE));
    }
  }, [modalVisible, getDictByCode]);

  return (
    <ModalForm
      title={'New Offer'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Offer);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormDigit
        rules={[
          {
            required: true,
            message: 'Offer no is required',
          },
        ]}
        width="md"
        name="offer_no"
        label="Offer No"
      />

      <ProFormTextArea width="lg" name="note" label="Notes" />
      <ProFormTextArea width="lg" name="offer_customer_note" label="Customer Notes" />

      <SProFormDigit width="md" name="percentage" label="Percentage" fieldProps={{ precision: 2 }} />
    </ModalForm>
  );
};

export default CreateForm;
