CREATE TABLE `offer`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `offer_no`   int(10) unsigned DEFAULT NULL COMMENT 'Offer No',
    `note`       varchar(255)     DEFAULT NULL COMMENT 'Note of offer',
    `created_on` datetime         DEFAULT NULL,
    `created_by` int(11)          DEFAULT NULL,
    `updated_on` datetime         DEFAULT NULL,
    `updated_by` int(11)          DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_offer_offer_no` (`offer_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

drop table if exists offer_item;

CREATE TABLE `offer_item`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `offer_id`   bigint(20) unsigned DEFAULT NULL COMMENT 'FK. Offer ID',
    `sku`        varchar(100)        DEFAULT NULL COMMENT 'SKU',
    `ean`        varchar(255)        DEFAULT NULL COMMENT 'EAN',
    `ean_id`     bigint(20) unsigned DEFAULT NULL COMMENT 'EAN ID',
    `item_id`    bigint(20) unsigned DEFAULT NULL COMMENT 'Item ID',
    `case_qty`   int(11)             DEFAULT NULL COMMENT 'Case qty',
    `pcs_pallet` int(11)             DEFAULT NULL,
    `price`      decimal(20, 4)      DEFAULT NULL COMMENT 'GFC Net price',
    `created_on` datetime            DEFAULT NULL,
    `created_by` int(11)             DEFAULT NULL,
    `updated_on` datetime            DEFAULT NULL,
    `updated_by` int(11)             DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_offer_item` (`ean`),
    KEY `FK_offer_item_offer_id` (`offer_id`),
    KEY `FK_offer_item_sku` (`sku`),
    KEY `FK_offer_item_ean_id` (`ean_id`),
    KEY `FK_offer_item_item_id` (`item_id`),
    CONSTRAINT `FK_offer_item_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_item_id` FOREIGN KEY (`item_id`) REFERENCES `item` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_offer_id` FOREIGN KEY (`offer_id`) REFERENCES `offer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_sku` FOREIGN KEY (`sku`) REFERENCES `item_ean` (`sku`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

alter table `offer_item` add column qty int(11) default 0 after `pcs_pallet`;


ALTER TABLE `offer_item` ADD UNIQUE INDEX `UQ_offer_item_offer_id_ean_id` (`offer_id`, `ean_id`);
