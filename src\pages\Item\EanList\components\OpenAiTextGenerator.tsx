import SMentionsSys from '@/components/SMentionsSys';
import SProFormDigit from '@/components/SProFormDigit';
import { DictCode } from '@/constants';
import { createTextCompletion } from '@/services/foodstore-one/Openai/openai';
import { getSysTextModuleList } from '@/services/foodstore-one/Sys/text-module';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormTextArea } from '@ant-design/pro-form';
import { Card, Col, Row, Spin, message } from 'antd';
import type { NamePath } from 'antd/lib/form/interface';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useModel } from 'umi';

const replaceReservedWords = (
  textP?: string,
  vars?: { single_ean?: string; single_title?: string; multi_ean?: string; multi_title?: string },
) => {
  if (!textP) return '';
  let text = textP;
  if (vars?.single_ean) text = text.replaceAll('[Single_EAN]', vars?.single_ean);
  if (vars?.single_title) text = text.replaceAll('[Single_Title]', vars?.single_title);
  if (vars?.multi_ean) text = text.replaceAll('[Multi_EAN]', vars?.multi_ean);
  if (vars?.multi_title) text = text.replaceAll('[Multi_Title]', vars?.multi_title);
  return text;
};

const filedName2DictCode: Record<string, DictCode> = {
  prompt1: DictCode.CE_OPENAI_PRESET_TEXT_MODULE,
  prompt2: DictCode.CE_OPENAI_PRESET_TEXT_MODULE2,
  prompt_md: DictCode.CE_OPENAI_PRESET_TEXT_MD,
  prompt_mt: DictCode.CE_OPENAI_PRESET_TEXT_MT,
  prompt_sd: DictCode.CE_OPENAI_PRESET_TEXT_SD,
  prompt_ld: DictCode.CE_OPENAI_PRESET_TEXT_LD,
  prompt_fs_export_google_description: DictCode.CE_OPENAI_PRESET_GOOGLE_DESC,
};

const filedName2SuffixDictCode: Record<string, DictCode> = {
  prompt1: DictCode.CE_OPENAI_PRESET_TEXT_MODULE_S,
  prompt2: DictCode.CE_OPENAI_PRESET_TEXT_MODULE2_S,
  prompt_md: DictCode.CE_OPENAI_PRESET_TEXT_MD_S,
  prompt_mt: DictCode.CE_OPENAI_PRESET_TEXT_MT_S,
  prompt_sd: DictCode.CE_OPENAI_PRESET_TEXT_SD_S,
  prompt_ld: DictCode.CE_OPENAI_PRESET_TEXT_LD_S,
  prompt_fs_export_google_description: DictCode.CE_OPENAI_PRESET_GOOGLE_DESC_S,
};

export const filedName2OutputLabel: Record<string, string> = {
  prompt1: 'Output 1',
  prompt2: 'Output 2',
  prompt_mt: 'Output Meta Title',
  prompt_md: 'Output Meta Desc',
  prompt_sd: 'Output Short Desc',
  prompt_ld: 'Output Long Desc',
  prompt_fs_export_google_description: 'Output Google Export Desc',
};

const filedName2PasteNamepath: Record<string, NamePath | null> = {
  prompt1: null,
  prompt2: null,
  prompt_mt: ['ean_texts', 0, 'meta_title'],
  prompt_md: ['ean_texts', 0, 'meta_description'],
  prompt_sd: ['ean_texts', 0, 'short_description'],
  prompt_ld: ['ean_texts', 0, 'description1'],
  prompt_fs_export_google_description: ['ean_texts', 0, 'fs_export_google_description'],
};

type OpenAiTextGeneratorProps = {
  is_single?: boolean;
  single_ean?: string;
  multi_ean?: string;
  single_title?: string;
  multi_title?: string;
  pastable?: boolean;
  eanFormRef?: React.MutableRefObject<ProFormInstance<API.Ean> | undefined>;
};

export const OpenAiTextGenerator: React.FC<OpenAiTextGeneratorProps> = ({
  is_single,
  single_ean,
  multi_ean,
  single_title,
  multi_title,
  pastable,
  eanFormRef,
}) => {
  const { getDictByCode } = useModel('app-settings');

  const formRef = useRef<ProFormInstance<Partial<API.OpenapiCallLog>>>();

  const [loadings, setLoadings] = useState<Record<string, boolean>>({});
  const setLoadingByName = useCallback((name: string, v: boolean) => {
    const nameOutput = `${name}_output`;
    setLoadings((prev) => ({ ...prev, [name]: v, [nameOutput]: v }));
  }, []);

  // system module map by number
  const [textsKv, setTextsKv] = useState<Record<number, string>>({});

  const generateAITextByName = useCallback(
    (name: string) => {
      const nameOutput = `${name}_output`;
      const max_tokens = formRef.current?.getFieldValue('max_tokens');

      formRef.current?.setFieldValue(nameOutput, '');

      let value = formRef.current?.getFieldValue(name);
      if (!value) return;

      setLoadingByName(name, true);

      value = replaceReservedWords(value, {
        single_ean,
        single_title,
        multi_ean,
        multi_title,
      });

      const hide = message.loading('Generating via ChatGPT...', 0);
      createTextCompletion({ prompt: value, max_tokens: max_tokens })
        .then((callLog) => {
          formRef.current?.setFieldValue(
            nameOutput,
            callLog.choices?.[0]?.text ?? callLog.choices?.[0]?.message?.content ?? '',
          );
        })
        .catch(Util.error)
        .finally(() => {
          hide();
          setLoadingByName(name, false);
        });
    },
    [multi_ean, multi_title, setLoadingByName, single_ean, single_title],
  );

  const addSuffixHandler = useCallback(
    (name: string) => {
      const oldValue = formRef?.current?.getFieldValue(name);
      const text = textsKv[getDictByCode(filedName2SuffixDictCode[name])];
      if (text) {
        formRef.current?.setFieldValue(name, oldValue + text);
      }
    },
    [getDictByCode, textsKv],
  );

  const copyOutputHandler = useCallback(
    (name: string) => {
      const value = formRef.current?.getFieldValue(`${name}_output`);
      navigator.clipboard.writeText(value || '');
      message.success('Copied!');
      const namePath = filedName2PasteNamepath[name];
      if (pastable && namePath) {
        eanFormRef?.current?.setFieldValue(namePath, value);
      }
    },
    [eanFormRef, pastable],
  );

  useEffect(() => {
    const presetNos = [
      ...Object.values(filedName2DictCode).map((x) => getDictByCode(x)),
      ...Object.values(filedName2SuffixDictCode).map((x) => getDictByCode(x)),
    ];

    getSysTextModuleList({ numbers: presetNos, pageSize: 50 })
      .then((res) => {
        const tempKv: Record<number, string> = {};
        res.data?.forEach((x) => {
          tempKv[x.number || 0] = x.text || '';
        });

        // preset default text module
        for (const fn in filedName2DictCode) {
          formRef.current?.setFieldValue(fn, tempKv[getDictByCode(filedName2DictCode[fn])]);
        }
        setTextsKv(tempKv);
      })
      .catch(Util.error);
  }, [getDictByCode]);

  return (
    <Spin spinning={false}>
      <ProForm<Partial<API.OpenapiCallLog>>
        formRef={formRef}
        isKeyPressSubmit
        layout="vertical"
        labelAlign="left"
        colon={false}
        submitter={{
          render: (form, dom) => {
            return [];
          },
        }}
      >
        <Card
          style={{ marginBottom: 24 }}
          size="small"
          title="AI Product Description Generator"
          bordered={false}
          bodyStyle={{ paddingBottom: 0 }}
          extra={
            <>
              <SProFormDigit
                fieldProps={{ size: 'small' }}
                formItemProps={{ style: { marginBottom: 0 } }}
                addonBefore={'Max. Tokens'}
                name={'max_tokens'}
                initialValue={3000}
                width="xs"
              />
            </>
          }
        >
          <Row gutter={48}>
            <Col span={12}>
              <SMentionsSys
                formRef={formRef}
                name={'prompt1'}
                label="Prompt 1"
                placeholder="Prompt 1..."
                rows={3}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt1}
              />
              <ProFormTextArea
                name="prompt1_output"
                fieldProps={{ rows: 6, showCount: true }}
                placeholder="Output 1..."
                disabled={loadings.prompt1}
              />

              <SMentionsSys
                formRef={formRef}
                name={'prompt2'}
                label="Prompt 2"
                placeholder="Prompt 2..."
                rows={3}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt2}
              />
              <ProFormTextArea
                name="prompt2_output"
                fieldProps={{ rows: 6, showCount: true }}
                placeholder="Output 2..."
                disabled={loadings.prompt2}
              />

              <SMentionsSys
                formRef={formRef}
                name={'prompt_mt'}
                label="Prompt Meta Title"
                placeholder="Prompt Meta Title..."
                rows={3}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt_mt}
              />
              <ProFormTextArea
                name="prompt_mt_output"
                fieldProps={{ rows: 3, showCount: true }}
                placeholder="Output Meta Title..."
                disabled={loadings.prompt_mt}
              />

              <SMentionsSys
                formRef={formRef}
                name={'prompt_md'}
                label="Prompt Meta Desc"
                placeholder="Prompt Meta Desc..."
                rows={3}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt_md}
              />
              <ProFormTextArea
                name="prompt_md_output"
                fieldProps={{ rows: 6, showCount: true }}
                placeholder="Output Meta Desc..."
                disabled={loadings.prompt_md}
              />

              <SMentionsSys
                formRef={formRef}
                name={'prompt_fs_export_google_description'}
                label="Prompt Google Export Desc"
                placeholder="Prompt Google Export Desc..."
                rows={3}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt_fs_export_google_description}
              />
              <ProFormTextArea
                name="prompt_fs_export_google_description_output"
                fieldProps={{ rows: 6, showCount: true }}
                placeholder="Output Google Export Desc..."
                disabled={loadings.prompt_fs_export_google_description}
              />
            </Col>
            <Col span={12}>
              <SMentionsSys
                formRef={formRef}
                name={'prompt_sd'}
                label="Prompt Short Desc"
                placeholder="Short Desc..."
                rows={3}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt_sd}
              />
              <ProFormTextArea
                name="prompt_sd_output"
                fieldProps={{ rows: 6, showCount: true }}
                placeholder="Output Short Desc..."
                disabled={loadings.prompt_sd}
              />

              <SMentionsSys
                formRef={formRef}
                name={'prompt_ld'}
                label="Prompt Long Desc"
                placeholder="Long Desc..."
                rows={5}
                generateHandler={generateAITextByName}
                addSuffixHandler={addSuffixHandler}
                copyOutputHandler={copyOutputHandler}
                disabled={loadings.prompt_ld}
              />
              <ProFormTextArea
                name="prompt_ld_output"
                fieldProps={{ rows: 30, showCount: true }}
                placeholder="Output Long Desc..."
                disabled={loadings.prompt_ld}
              />
            </Col>
          </Row>
        </Card>
      </ProForm>
    </Spin>
  );
};
export default OpenAiTextGenerator;
