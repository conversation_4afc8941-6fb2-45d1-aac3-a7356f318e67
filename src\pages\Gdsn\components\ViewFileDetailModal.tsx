import { DEFAULT_PER_PAGE_PAGINATION, DT_FORMAT_DMY } from '@/constants';
import { getGdsnMessageItemList } from '@/services/foodstore-one/Item/ean';
import Util, { nf2, ni } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Button, Col, Modal, Row, Typography, Image } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useRef, useEffect, useState } from 'react';
import XmlViewer from './XmlViewer';
import { ContactsOutlined } from '@ant-design/icons';

type ViewFileDetailModalProps = {
  initialValues?: Partial<API.GdsnMessage> & { gtin?: string; with?: string };
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  fetchGdsnItem?: () => void;
};
const ViewFileDetailModal: React.FC<ViewFileDetailModalProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, fetchGdsnItem, ...rest } = props;

  const actionRef = useRef<ActionType>();
  const [showXmlDetail, setShowXmlDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.GdsnMessageItem>();
  const [xmlMode, setXmlMode] = useState<string>('');

  const columns: ProColumns<API.GdsnMessageItem>[] = [
    {
      title: 'Transaction ID',
      dataIndex: 'transaction_id',
      sorter: true,
      //ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a
            href={`${API_URL_EAN}/api/download?type=xml&key=${record.gdsn_message?.file_url}`}
            target="_blank"
            rel="noreferrer"
          >
            {dom}
          </a>
        );
      },
      width: 200,
    },
    {
      title: 'Image',
      dataIndex: ['detail', 'images'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      width: 80,
      render: (dom, record: any) => {
        return record?.detail?.images ? (
          <Image.PreviewGroup>
            {record?.detail?.images?.map((file: string, ind: number) => (
              <Image
                key={file}
                src={file}
                preview={{
                  src: file,
                }}
                wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                width={40}
              />
            ))}
          </Image.PreviewGroup>
        ) : (
          <></>
        );
      },
    },
    {
      title: 'Content owner',
      dataIndex: 'content_owner',
      sorter: true,
      // ellipsis: true,
      copyable: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'GTIN',
      dataIndex: 'gtin',
      sorter: true,
      tooltip: 'Single / Package EAN',
      showSorterTooltip: false,
      copyable: true,
      width: 130,
      className: 'text-sm',
    },
    {
      title: 'Parent GTIN',
      dataIndex: 'parent_gtin',
      sorter: true,
      showSorterTooltip: false,
      tooltip: 'Package EAN',
      copyable: true,
      width: 130,
      className: 'text-sm',
    },
    {
      title: 'Case Qty',
      dataIndex: 'case_qty',
      sorter: true,
      showSorterTooltip: false,
      align: 'center',
      tooltip: 'Parent GTIN includes {Case Qty} GTINs.',
      width: 80,
    },
    {
      title: 'Provider GLN',
      dataIndex: 'provider_gln',
      sorter: true,
      // ellipsis: true,
      copyable: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'Name',
      dataIndex: 'desc',
      sorter: true,
      // ellipsis: true,
      copyable: true,
      width: 250,
      className: 'text-sm',
    },
    {
      title: 'Provider Name',
      dataIndex: 'provider_name',
      sorter: true,
      // ellipsis: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'Category',
      dataIndex: 'gpc_category_name',
      sorter: true,
      ellipsis: true,
      width: 120,
      className: 'text-sm',
    },
    {
      title: 'Ref Item',
      dataIndex: 'ref_item_gtin',
      sorter: true,
      ellipsis: true,
      copyable: true,
      className: 'text-sm bl2 b-gray',
      width: 100,
    },
    {
      title: 'Ref Type',
      dataIndex: 'ref_item_type_code',
      sorter: true,
      ellipsis: true,
      width: 100,
      className: 'text-xs',
    },
    {
      title: 'State',
      dataIndex: 'item_state',
      ellipsis: true,
      width: 100,
      className: 'text-xs',
    },
    {
      title: 'Brand',
      dataIndex: 'brand',
      width: 120,
      className: 'text-sm bl2 b-gray',
    },
    {
      title: 'Trademark',
      dataIndex: 'trademark_name',
      // ellipsis: true,
      width: 150,
    },
    {
      title: 'Maker',
      dataIndex: 'maker_name',
      // ellipsis: true,
      width: 150,
    },

    {
      title: 'Child Count',
      dataIndex: 'children_cnt',
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.children_cnt);
      },
    },
    {
      title: 'Item base',
      dataIndex: 'item_base',
      align: 'center',
      width: 60,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.item_base);
      },
    },
    {
      title: 'Item base unit',
      dataIndex: 'item_base_unit',
      align: 'center',
      width: 60,
      className: 'text-sm',
    },
    {
      title: 'W',
      dataIndex: 'width',
      align: 'right',
      width: 70,
      className: 'text-sm bl2 b-gray',
      render(__, record) {
        return ni(record?.width);
      },
    },
    {
      title: 'H',
      dataIndex: 'height',
      align: 'right',
      width: 70,
      className: 'text-sm',
      render(__, record) {
        return ni(record?.height);
      },
    },
    {
      title: 'D',
      dataIndex: 'depth',
      align: 'right',
      width: 70,
      className: 'text-sm',
      render(__, record) {
        return ni(record?.depth);
      },
    },

    {
      title: 'Net weight',
      dataIndex: 'net_weight',
      align: 'right',
      width: 80,
      className: 'text-sm',
      render(__, record) {
        return nf2(record?.net_weight);
      },
    },
    {
      title: 'Gross weight',
      dataIndex: 'gross_weight',
      align: 'right',
      width: 80,
      className: 'text-sm',
      render(__, record) {
        return nf2(record?.gross_weight);
      },
    },
    {
      title: 'Contact Name',
      dataIndex: 'contact_name',
      // ellipsis: true,
      width: 150,
      className: 'text-sm bl2 b-gray',
    },
    {
      title: 'Contact Addr.',
      dataIndex: 'contact_address',
      // ellipsis: true,
      width: 200,
      className: 'text-sm',
    },
    {
      title: 'Msg ID',
      dataIndex: 'gdsn_message_id',
      align: 'center',
      width: 60,
      className: 'text-sm',
    },
    {
      title: 'Last change',
      sorter: true,
      tooltip: 'Last sync changed date',
      dataIndex: 'sync_last_change_dt',
      valueType: 'dateTime',
      search: false,
      className: 'text-sm',
      width: 90,
      fieldProps: {
        format: DT_FORMAT_DMY,
      },
    },
    {
      title: 'Sync effective date',
      sorter: true,
      dataIndex: 'sync_effective_dt',
      valueType: 'dateTime',
      search: false,
      width: 90,
      className: 'text-sm',
      fieldProps: {
        format: DT_FORMAT_DMY,
      },
    },
    {
      title: 'XML size',
      dataIndex: 'info_xml',
      search: false,
      width: 80,
      className: 'text-sm',
      render: (_, record) => (
        <span title={`${ni(record.info_xml?.length || 0)} bytes`}>
          {Util.humanFileSize(record.info_xml?.length || 0)}
        </span>
      ),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 90,
      render: (_, record) => (
        <Row gutter={4}>
          <Col>
            <Button
              type="link"
              size="small"
              title="Show item information in xml..."
              onClick={() => {
                setCurrentRow(record);
                setXmlMode('');
                setShowXmlDetail(true);
              }}
            >
              XML
            </Button>
          </Col>
          <Col>
            <Button
              type="link"
              size="small"
              title="Show contact info in xml..."
              onClick={() => {
                setCurrentRow(record);
                setXmlMode('contact');
                setShowXmlDetail(true);
              }}
            >
              <ContactsOutlined />
            </Button>
          </Col>
        </Row>
      ),
    },
  ];

  useEffect(() => {
    if (modalVisible && initialValues?.gtin) {
      actionRef.current?.reload();
    }
  }, [modalVisible, initialValues?.gtin]);

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={'90%'}
      bodyStyle={{ padding: 0 }}
      maskClosable={false}
      footer={false}
      title={
        <Row gutter={32}>
          <Col>View GDSN message detail</Col>
          <Col>
            <Typography.Text copyable>{initialValues?.gtin}</Typography.Text>
          </Col>
        </Row>
      }
    >
      <ProTable<API.GdsnMessageItem, API.PageParams>
        headerTitle={'GDSN items list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          // const sfValues = searchFormRef.current?.getFieldsValue();
          const sfValues = {};

          const newSort = { ...sort };

          return getGdsnMessageItemList(
            {
              ...sfValues,
              ...params,
              ean: initialValues?.gtin,
              with: initialValues?.with,
            },
            newSort,
            filter,
          ).then((res) => {
            fetchGdsnItem?.();
            return res;
          });
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
      />
      <XmlViewer
        messageItemId={currentRow?.id}
        modalVisible={showXmlDetail}
        handleModalVisible={setShowXmlDetail}
        mode={xmlMode}
      />
    </Modal>
  );
};

export default ViewFileDetailModal;
