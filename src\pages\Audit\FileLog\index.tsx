import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Card, Space, Typography, InputNumber } from 'antd';
import { getFileLog } from '@/services/foodstore-one/Sys/file-log';

const FileLog: React.FC = () => {
  const [lines, setLines] = useState(100);
  const [data, setData] = useState('');
  const [loading, setLoading] = useState(false);

  return (
    <PageContainer>
      <Card
        title={`Last log (${lines} lines)`}
        loading={loading}
        extra={
          <Space size={32}>
            <div>Last Lines:</div>
            <div>
              <InputNumber value={lines} onChange={(e) => setLines(e as any)} />
            </div>
            <Typography.Text copyable={{ text: data }} />
            <Button
              type="primary"
              size="small"
              onClick={() => {
                setLoading(true);
                getFileLog({ lines })
                  .then((res) => setData(res))
                  .finally(() => setLoading(false));
              }}
            >
              Reload
            </Button>
          </Space>
        }
      >
        <pre style={{ maxHeight: '90vh', overflowY: 'auto' }}>{data}</pre>
      </Card>
    </PageContainer>
  );
};

export default FileLog;
