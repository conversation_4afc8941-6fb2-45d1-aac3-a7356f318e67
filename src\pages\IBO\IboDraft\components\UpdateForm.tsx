import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateIBOManagement } from '@/services/foodstore-one/IBO/ibo-management';
import Util from '@/util';
import { getSupplierList } from '@/services/foodstore-one/supplier';
import SDatePicker from '@/components/SDatePicker';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateIBOManagement(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.UserListItem>;

export type UpdateFormProps = {
  initialValues?: Partial<API.IBOManagement>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.IBOManagement) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  return (
    <ModalForm
      title={'Update Item Buying Order Management'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          order_date: Util.dtToYMD(value.order_date),
          received_date: Util.dtToYMD(value.received_date),
          id: props.initialValues?.id,
        };
        const success = await handleUpdate(data);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        showSearch
        placeholder="Select a supplier"
        request={async (params) => {
          const res = await getSupplierList({ ...params, pageSize: 100 }, { name: 'ascend' }, {});
          if (res && res.data) {
            const tmp = res.data.map((x: API.Supplier) => ({
              label: `${x.supplier_no} - ${x.name}`,
              value: x.id,
            }));
            return tmp;
          }
          return [];
        }}
        rules={[
          {
            required: true,
            message: 'Supplier is required',
          },
        ]}
        width="md"
        name="supplier_id"
        label="Supplier"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Order no is required',
          },
        ]}
        width="md"
        name="order_no"
        label="Order No"
      />
      <SDatePicker width="md" name="order_date" label="Order Date" />
      <SDatePicker width="md" name="received_date" label="Received Date" />
    </ModalForm>
  );
};

export default UpdateForm;
