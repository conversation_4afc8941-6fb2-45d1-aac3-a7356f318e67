/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/magento-data/orders/items';

/**
 * Get Order Items List with GP information.
 *
 * GET /api/magento-data/orders/items */
export async function getOrderItemsList(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.Order & API.OrderItem>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}
