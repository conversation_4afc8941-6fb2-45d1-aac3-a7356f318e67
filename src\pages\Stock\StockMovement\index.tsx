import { Card } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { getStockMovementList } from '@/services/foodstore-one/Stock/stock-movement';
import { DEFAULT_PER_PAGE_PAGINATION, StockMovementReasonOptions } from '@/constants';
import Util, { ni } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import StockMovementRefComp from './StockMovementRefComp';
import { IRouteComponentProps } from 'umi';

export type SearchFormValueType = Partial<API.StockMovement>;

const StockMovementList: React.FC<IRouteComponentProps> = (props) => {
  const { location } = props;
  const iboIdParam = location.query.ibo_id;

  const searchFormRef = useRef<ProFormInstance>();
  // const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  // const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  // const [showDetail, setShowDetail] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  // const [currentRow, setCurrentRow] = useState<API.Vat>();
  // const [selectedRowsState, setSelectedRows] = useState<API.Vat[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.StockMovement>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Item',
      align: 'center',
      dataIndex: ['item', 'name'],
      hideInSearch: true,
      formItemProps: { tooltip: 'Search by item name.' },
      children: [
        {
          title: 'Name',
          dataIndex: ['item', 'name'],
          sorter: true,
          ellipsis: true,
          hideInSearch: false,
          width: 180,
        },
        /* {
          title: 'VAT',
          dataIndex: ['item', 'vat', 'value'],
          sorter: false,
          width: 60,
          ellipsis: true,
          align: 'right',
          hideInSearch: true,
          render: (dom, record) =>
            record?.item?.vat?.value >= 0 ? `${record?.item?.vat?.value}%` : '',
        }, */
        {
          title: 'Trademark',
          dataIndex: ['item', 'trademark', 'name'],
          sorter: false,
          width: 100,
          ellipsis: true,
          hideInSearch: true,
          render: (dom, record) => (record?.item?.trademark?.name ? `${record?.item?.trademark?.name}` : ''),
        },
      ],
    },
    {
      title: 'EAN',
      dataIndex: 'ean',
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
    },
    {
      title: 'Single EAN',
      dataIndex: 'parent_ean',
      sorter: true,
      copyable: true,
      hideInSearch: true,
      width: 150,
    },
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'sku'],
      sorter: true,
      copyable: true,
      ellipsis: true,
      hideInSearch: true,
      width: 100,
    },
    {
      title: 'Old WL',
      dataIndex: ['old_wl_name'],
      sorter: false,
      width: 80,
    },
    {
      title: 'New WL',
      dataIndex: ['new_wl_name'],
      width: 80,
      sorter: false,
      render(dom, entity) {
        return (
          <a href={`/stock/stock-warehouse?sku=${entity.item_id}_&wl_id=${entity.new_wl_id}`} target="_blank">
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Reason',
      dataIndex: ['reason'],
      width: 130,
      sorter: false,
    },
    {
      title: 'Reason Detail',
      dataIndex: ['reason_text'],
      width: 120,
      sorter: false,
    },
    {
      title: 'Pcs Qty',
      dataIndex: ['piece_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.piece_qty),
    },
    {
      title: 'Box Qty',
      dataIndex: ['box_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.box_qty),
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      sorter: false,
      width: 60,
      align: 'right',
      render: (dom, record) => ni(record.case_qty),
    },
    {
      title: 'Total Piece Qty',
      dataIndex: ['total_piece_qty'],
      sorter: false,
      width: 80,
      align: 'right',
      render: (dom, record) => ni(record.total_piece_qty),
    },
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      sorter: false,
      width: 100,
      align: 'center',
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Ref',
      dataIndex: ['ref_type'],
      sorter: true,
      width: 130,
      fixed: 'right',
      render: (dom, record) => {
        return <StockMovementRefComp record={record} />;
      },
    },
    {
      title: 'Process Code',
      dataIndex: ['batch_code'],
      sorter: true,
      width: 80,
      align: 'center',
    },
    {
      title: 'Owner',
      dataIndex: ['owner'],
      sorter: false,
      width: 80,
      align: 'center',
    },
    {
      title: 'Created on',
      dataIndex: ['created_on'],
      sorter: true,
      defaultSortOrder: 'descend',
      width: 120,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },

    /* {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    }, */
  ];

  useEffect(() => {
    searchFormRef.current?.setFieldValue('ibo_id', iboIdParam || '');
    actionRef.current?.reload();
  }, [iboIdParam]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_stock_movement')}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormText name={'sku'} label="SKU" width={'sm'} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={'sm'} placeholder={'EAN'} />
          <ProFormSelect
            name={'reason'}
            label="Reason"
            width={'sm'}
            allowClear
            options={StockMovementReasonOptions as any}
            placeholder={'Reason'}
          />
          <ProFormSelect
            name={'refFilterMode'}
            label="Filter Mode"
            width={'sm'}
            allowClear
            options={[
              { value: 'onlyOrderItem', label: 'Only Order Item' },
              { value: 'notOnlyOrderItem', label: 'Not Order Item' },
            ]}
            placeholder={'Filter Mode'}
          />
          <ProFormText name={'ibo_id'} label="IBO ID" width={'xs'} placeholder={'IBO ID'} />
        </ProForm>
      </Card>
      <ProTable<API.StockMovement, API.PageParams>
        headerTitle={'Stock Movements'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        size="small"
        scroll={{ x: 800 }}
        rowClassName={(record) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_stock_movement', searchFormValues);
          setLoading(true);
          try {
            return await getStockMovementList(
              {
                ...params,
                ...Util.mergeGSearch(searchFormValues),
                with: 'refOrderItemJoin,refIbomOrderNo',
              },
              sort,
              filter,
            );
          } finally {
            setLoading(false);
          }
        }}
        onRequestError={Util.error}
        columns={columns}
        /* toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<ImportOutlined />}
            onClick={() => {
              const hide = message.loading('Importing stocks from IBO...');
              ImportStockMovementIbo({})
                .then((res) => {
                  message.success('Successfully imported');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
          >
            Import from IBO
          </Button>,
        ]} */
        rowSelection={false}
        columnEmptyText=""
      />
    </PageContainer>
  );
};

export default StockMovementList;
