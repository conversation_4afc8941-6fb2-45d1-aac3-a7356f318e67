/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/report/order';

/** Get sales stats
 * GET /api/report/order/sales-stat */
export async function getSalesStatsList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderItem>> {
  return request<API.BaseResult>(`${urlPrefix}/sales-stat`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.data.length,
  }));
}

/** Export sales stats
 * GET /api/report/order/export-sales-stat */
export async function exportSalesStatsList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-sales-stat`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** Get sales stats
 * GET /api/report/order/sales-stat-monthly */
export async function getSalesStatsMonthlyList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderItem> & { imports?: API.Import[]; summary?: API.OrderItem }> {
  return request<API.BaseResult>(`${urlPrefix}/sales-stat-monthly`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.data.length,
    imports: res.message.imports ?? [],
    summary: res.message.summary ?? {},
  }));
}

/** Get sales stats by weekly
 * GET /api/report/order/sales-stat-weekly */
export async function getSalesStatsWeeklyList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderItem>> {
  return request<API.BaseResult>(`${urlPrefix}/sales-stat-weekly`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    dataTotal: res.message.dataTotal,
    success: res.status == 'success',
    total: res.message.data.length,
  }));
}


/** Get sales stats by daily
 * GET /api/report/order/sales-stat-daily */
export async function getSalesStatsDailyList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; listMode?: 'detail' },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderItem>> {
  return request<API.BaseResult>(`${urlPrefix}/sales-stat-daily`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

// Used in Pick-value stats
export type StatsPickValueRecordType = API.Ean & {
  ean_item_id?: number;
  turnover?: number;
  gturnover?: number;
  cturnover?: number;
  bp?: number;
  gp?: number;
  orders_cnt?: number;  // Order's count (=Picks count)
  pcs_qty?: number;
} & {
  avg_bp?: number;
  cur_bp?: number;
  avg_sales_price?: number;
  cur_sales_price?: number;
  avg_exp_days?: number;
  last30_sales_qty?: number;
  stockRelated1?: number;
  stockRelated2?: number;
  avg_shelf_life?: number;
}

/** 
 * Get sales stats - pick value
 * GET /api/report/order/sales-stat-pick-value */
export async function getSalesStatsPickValueList(
  params: API.PageParams & { dateRanges?: any[]; sku?: string; },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<StatsPickValueRecordType>> {
  return request<API.BaseResult>(`${urlPrefix}/sales-stat-pick-value`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export type StatsType = {
  count_on_hold?: number;
};

/** 
 * Get order stats
 * 
 * GET /api/report/order/orders-count-on-hold */
export async function getOrderCountOnHold(params?: Record<string, any>) {
  return request<API.ResultObject<StatsType>>(`${urlPrefix}/orders-count-on-hold`, {
    method: 'GET',
    params: {
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}