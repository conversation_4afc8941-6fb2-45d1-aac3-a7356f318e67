/* eslint-disable */
// @ts-ignore
import { request } from 'umi';
import { currentUser as queryCurrentUser } from '@/services/foodstore-one/api';
import { LS_TOKEN_NAME } from '@/constants';

/** login captcha POST /api/login/captcha */
export async function getFakeCaptcha(params: API.getFakeCaptchaParams, options?: { [key: string]: any }) {
  return request<API.FakeCaptcha>('/api/login/captcha', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** Login POST /api/login/outLogin */
export async function outLogin(options?: { [key: string]: any }) {
  localStorage.setItem(LS_TOKEN_NAME, '');
  return queryCurrentUser();
  /*return request<Record<string, any>>('/api/user/logout', {
    method: 'POST',
    ...(options || {}),
  });*/
}

/** account POST /api/login/account */
export async function login(body: API.LoginParams, options?: { [key: string]: any }): Promise<API.LoginResult> {
  return request<API.BaseResult>('/api/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...options,
    skipToken: true,
  }).then((res) => res.message);
}
