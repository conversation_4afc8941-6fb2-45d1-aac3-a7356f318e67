SELECT ibo.id,
       ibo.`ean_id`,
       (SELECT ibom.`supplier_id` FROM ibom WHERE ibom.`id` = ibo.`ibom_id`) AS supplier_id,
       (SELECT group_concat(es.product_no)
        FROM ibom
                 INNER JOIN ean_supplier es ON es.supplier_id = ibom.supplier_id
        WHERE ibom.`id` = ibo.`ibom_id`
          AND es.ean_id IN (ibo.ean_id, (SELECT parent_id FROM item_ean x WHERE x.id = ibo.ean_id))
        )                                                             AS product_no
FROM ibo;