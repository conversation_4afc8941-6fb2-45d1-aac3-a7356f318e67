import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { message, Button } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormGroup, ProFormTreeSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateEanCategoriesBatch } from '@/services/foodstore-one/Item/ean';
import Util from '@/util';
import _ from 'lodash';
import type { DataNode } from 'antd/lib/tree';
import { MinusOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import type { DefaultOptionType } from 'antd/lib/select';

const handleUpdate = async (fields: any) => {
  const hide = message.loading('Batch updating categoreis...', 0);

  try {
    await updateEanCategoriesBatch(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateCategoriesFormBulkProps = {
  eanIds: number[];
  treeData: DataNode[];
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
  onCancel: (flag?: boolean) => void;
};

const UpdateCategoriesFormBulk: React.FC<UpdateCategoriesFormBulkProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      title={<>{`Update ${props.eanIds.length} EANs categories`}</>}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      grid
      modalProps={{
        maskClosable: false,
      }}
      formRef={formRef}
      submitter={{
        render: (p, doms) => {
          return (
            <>
              <Button
                type="primary"
                onClick={() => {
                  formRef.current?.setFieldsValue({ mode: 'update' });
                  formRef.current?.submit();
                }}
                icon={<SaveOutlined />}
              >
                Update
              </Button>
              <Button
                type="default"
                onClick={() => {
                  formRef.current?.setFieldsValue({ mode: 'add' });
                  formRef.current?.submit();
                }}
                icon={<PlusOutlined />}
              >
                Add
              </Button>
              <Button
                type="default"
                danger
                onClick={() => {
                  formRef.current?.setFieldsValue({ mode: 'delete' });
                  formRef.current?.submit();
                }}
                icon={<MinusOutlined />}
              >
                Delete
              </Button>
              <Button type="default" key="close" onClick={() => props.handleModalVisible(false)}>
                Close
              </Button>
            </>
          );
        },
      }}
      onFinish={async (value) => {
        if (formRef.current?.isFieldsTouched()) {
          const data = {
            ids: props.eanIds,
            mode: value.mode,
            data: {
              note: value.note,
              categories: value.categories.map((x: DefaultOptionType) => x.value) || [],
            },
          };

          const success = await handleUpdate(data);

          if (success) {
            props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(value);
          }
        } else {
          props.handleModalVisible(false);
        }
      }}
    >
      <ProFormGroup rowProps={{ gutter: 24 }}>
        <ProFormText name="mode" formItemProps={{ style: { display: 'none' } }} />
        <ProFormSwitch
          name={['note', 'catMode']}
          checkedChildren="Local only"
          unCheckedChildren="Local only"
          tooltip={'If unchecked, both of categories will be used.'}
          initialValue={false}
          colProps={{ span: 'auto' }}
        />
        <ProFormTreeSelect
          placeholder="Select categories"
          request={async () => props.treeData}
          allowClear
          name={'categories'}
          label="Categories"
          // tree-select args
          fieldProps={{
            filterTreeNode: true,
            showSearch: true,
            dropdownMatchSelectWidth: false,
            autoClearSearchValue: true,
            multiple: true,
            labelInValue: true,
            showArrow: true,
            treeLine: true,
            treeNodeFilterProp: 'title',
            fieldNames: {
              label: 'title',
            },
          }}
        />
      </ProFormGroup>
    </ModalForm>
  );
};

export default UpdateCategoriesFormBulk;
