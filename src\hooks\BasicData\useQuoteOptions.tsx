import { getOfferACList } from '@/services/foodstore-one/Offer/offer';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Auto completion list of Quotes
 */
export default (
  defaultParams?: Record<string, any>,
  formRef?: React.MutableRefObject<ProFormInstance | undefined>,
  eleOptions?: any,
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [quoteOptions, setQuoteOptions] = useState<DefaultOptionType[]>([]);
  // selected offer
  const [quote, setQuote] = useState<DefaultOptionType>();

  const searchQuoteOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getOfferACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setQuoteOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  useEffect(() => {
    searchQuoteOptions().then((res) => {
      const quote_id = formRef?.current?.getFieldValue('quote_id');
      if (quote_id) {
        const found = res.find((x: any) => x.id == quote_id);
        setQuote(found);
      }
    });
  }, [formRef, searchQuoteOptions]);

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        name="quote_id"
        label={'Offer'}
        placeholder="Please select Quote"
        mode="single"
        showSearch
        options={quoteOptions}
        required={eleOptions?.required}
        request={(params) => {
          return searchQuoteOptions(params);
        }}
        rules={
          eleOptions?.required
            ? [
                {
                  required: true,
                  message: 'Quote is required',
                },
              ]
            : []
        }
        fieldProps={{
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          onChange: (value, option) => {
            setQuote(option as any);
          },
        }}
        width={200}
        disabled={loading}
      />
    );
  }, [quoteOptions, eleOptions?.required, loading, searchQuoteOptions]);

  return {
    quoteOptions,
    searchQuoteOptions: searchQuoteOptions,
    loading,
    quote,
    setQuote,
    formElements,
  };
};
