import { <PERSON><PERSON>, <PERSON>, Col, Dropdown, Menu, message, Popconfirm, Row, Space, Typography } from 'antd';
import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { ni, sFirstWord, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import type { NoStockSkuType } from '@/services/foodstore-one/Warehouse/picklist';
import {
  bookPicklist,
  exportPicklistCsv,
  exportPicklistDeliveryNote,
  exportPicklistPdf,
  exportPicklistPdfByBox,
  exportShippingLabelsInPicklist,
  exportShippingMergedLabelsInPicklist,
  getPicklist,
} from '@/services/foodstore-one/Warehouse/picklist';
import {
  CheckCircleOutlined,
  DownOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  WarningOutlined,
} from '@ant-design/icons/lib/icons';
import OrderLabelsModal from './components/OrderLabelsModal';
import { useModel } from 'umi';
import { PackingStatus, PickingStatus, PrePickingStatus, UserRole } from '@/constants';
import TimeTracksPopover from './components/TimeTracksPopover';
import StartOrResumePopover from './components/StartOrResumePopover';
import NoStockSkuModal from '../PicklistDetail/components/NoStockSkuModal';
import BoxCreationModal from './components/BoxCreationModal';
import Compact from 'antd/lib/space/Compact';

export type SearchFormValueType = Partial<API.WarehousePicklist>;

const Picklist: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const searchFormRef = useRef<ProFormInstance>();

  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.WarehousePicklist | null>(null);

  // Qty. of Employee selection form
  const [qtyEmployee, setQtyEmployee] = useState<number>(1);

  // modals
  const [openOrderLabelsModal, setOpenOrderLabelsModal] = useState<boolean>(false);

  // no stock SKUs
  const [openNoStockSKUModal, setOpenNoStockSKUModal] = useState<boolean>(false);
  const [noStockSKUList, setNoStockSKUList] = useState<NoStockSkuType[]>([]);

  // Box related modals
  const [openBoxCreationModal, setOpenBoxCreationModal] = useState<boolean>(false);

  const handlePrintLabel = async (picklistId: number, service_name: 'GLS' | 'DHL' | 'GLS Express') => {
    const hide = message.loading(`Downloading ${service_name} Label as PDF format...`, 0);
    setLoading(true);
    exportShippingLabelsInPicklist(sn(picklistId), { service_name })
      .then((res) => {
        hide();
        message.success('Created labels successfully.');
        actionRef.current?.reload();
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  const handlePrintLabelMerged = (id: number) => {
    const hide = message.loading(`Create merged Label as PDF format...`, 0);
    setLoading(true);
    exportShippingMergedLabelsInPicklist(sn(id))
      .then((res) => {
        hide();
        if (res.file) {
          window.open(`${API_URL}/api/${res.file?.url}`, '_blank');
        }
      })
      .catch(Util.error)
      .finally(() => {
        hide();
        setLoading(false);
      });
  };

  const columns: ProColumns<API.WarehousePicklist>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      copyable: true,
      width: 80,
      defaultSortOrder: 'descend',
      render(__, record) {
        if (record.is_pre) return record.id;

        return initialState?.currentUser?.role != UserRole.WAREHOUSE ? (
          <Typography.Link
            title="Open a picklist detail"
            copyable
            href={`/orders/picklist?id=${record?.id}`}
            target={'_blank'}
          >
            {record?.id}
          </Typography.Link>
        ) : (
          <Typography.Text copyable>{record.id}</Typography.Text>
        );
      },
    },
    {
      title: 'Created user',
      dataIndex: ['user_initials'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 80,
    },
    {
      title: 'Created on',
      dataIndex: ['created_on'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      className: 'text-sm',
      width: 100,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Type',
      dataIndex: ['note'],
      tooltip: '[PRE]: Pre Picklist',
      sorter: true,
      ellipsis: true,
      width: 250,
      render: (dom, record) => {
        return record.is_pre ? '[PRE]' : dom;
      },
    },
    {
      title: 'Multi Orders',
      dataIndex: ['multi_orders_count'],
      align: 'right',
      className: 'bl2',
      width: 60,
      render: (_, record) => {
        return record.is_pre ? null : ni(record.multi_orders_count);
      },
    },
    {
      title: 'Single + Multi',
      dataIndex: ['ms_orders_count'],
      align: 'right',
      width: 60,
      render: (_, record) => {
        return record.is_pre ? null : ni(record.ms_orders_count);
      },
    },
    {
      title: 'Single Orders',
      dataIndex: ['single_orders_count'],
      align: 'right',
      width: 60,
      render: (_, record) => (record.is_pre ? null : ni(record.single_orders_count)),
    },
    {
      title: 'Orders Count',
      dataIndex: ['orders_count'],
      align: 'right',
      width: 60,
      className: 'bl2',
      render: (_, record) => (record.is_pre ? null : ni(record.orders_count)),
    },
    {
      title: 'Items Count',
      dataIndex: ['items_count'],
      align: 'right',
      className: 'bl2',
      width: 60,
      render: (_, record) => (record.is_pre ? null : ni(record.items_count)),
    },
    {
      title: 'Unbooked Count',
      dataIndex: ['unbooked_count'],
      align: 'right',
      width: 60,
      render: (_, record) => (record.is_pre ? null : ni(record.unbooked_count)),
    },
    {
      title: 'Booking status',
      dataIndex: ['status'],
      align: 'center',
      ellipsis: true,
      className: 'bl2',
      width: 110,
      render: (_, record) => {
        if (record.is_pre) return null;

        // Check deep booked status
        return !record.is_full_stock_stable_updated ? (
          <>
            <Popconfirm
              key="book-popconfirm"
              title={<>Are you sure you want to book selected picklist?</>}
              okText="Yes"
              cancelText="No"
              onConfirm={() => {
                setCurrentRow(record);
                const hide = message.loading('Booking picklist data...', 0);
                setLoading(true);
                bookPicklist(record.id, {})
                  .then((res) => {
                    hide();
                    if (res) {
                      if (res.isOk) {
                        message.success('Booked successfully.');
                        actionRef.current?.reload();
                        const hide2 = message.loading('Downloading picklist data as PDF format...', 0);
                        exportPicklistPdf(sn(record.id), { mode: 'groupByOrder' })
                          .then((res2) => {
                            hide2();
                            if (res2.url) {
                              window.open(`${API_URL}/api/${res2.url}`, '_blank');
                            }
                          })
                          .catch(Util.error)
                          .finally(() => {
                            hide2();
                          });
                      } else if (res.notEnoughSkus) {
                        message.warning('Out of stocks: ' + Object.keys(res.notEnoughSkus).join(', '));
                        setNoStockSKUList(
                          Object.keys(res.notEnoughSkus).map((sku) => ({
                            sku: sku,
                            qty: res.notEnoughSkus[sku],
                          })),
                        );
                        setOpenNoStockSKUModal(true);
                      }
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                    setLoading(false);
                  });
              }}
            >
              <Button size="small" type="primary" className="btn-green">
                Book
              </Button>
            </Popconfirm>
          </>
        ) : (
          <>
            <span>{record.last_stock_stable_updater?.initials ?? record.last_stock_stable_updater?.username}</span>{' '}
            <span className="text-sm c-grey">{Util.dtToDMYHHMM(record.last_stock_stable_updated_on)}</span>
          </>
        );
      },
    },
    {
      title: 'Parcel',
      dataIndex: ['shipping_provider'],
      width: 100,
      tooltip: 'Create parcels',
      render: (_, record) => {
        if (!record.note) return <></>;

        let buttonEle = null;
        const firstWord = sFirstWord(record.note);

        const isLabelGenerationAllowed = !!firstWord && ['GLS24', 'GLS', 'STD', 'DHL'].includes(firstWord);
        // if (!isLabelGenerationAllowed) return <></>;

        if (record.labeled_orders_count) {
          buttonEle = isLabelGenerationAllowed ? firstWord : '';
        } else {
          if (isLabelGenerationAllowed) {
            switch (firstWord) {
              case 'GLS24':
                buttonEle = (
                  <Button
                    type="primary"
                    size="small"
                    title="Generate GLS Express Shipping Labels in PDF"
                    className="ant-btn-xs"
                    onClick={() => {
                      handlePrintLabel(sn(record.id), 'GLS Express');
                    }}
                  >
                    GLS24
                  </Button>
                );
                break;
              case 'GLS':
              case 'STD':
                buttonEle = (
                  <Button
                    type="primary"
                    size="small"
                    title="Generate GLS Shipping Labels in PDF"
                    className="ant-btn-xs"
                    onClick={() => {
                      handlePrintLabel(sn(record.id), 'GLS');
                    }}
                  >
                    GLS
                  </Button>
                );
                break;
              case 'DHL':
                buttonEle = (
                  <Button
                    type="primary"
                    size="small"
                    title="Generate Print DHL Shipping Labels in PDF"
                    className="ant-btn-xs"
                    onClick={() => {
                      handlePrintLabel(sn(record.id), 'DHL');
                    }}
                  >
                    DHL
                  </Button>
                );
                break;
              case 'DHL24':
              case 'DPD24':
              default:
                buttonEle = null;
                break;
            }
          }
        }
        return (
          <Row gutter={4}>
            <Col flex="70px">{buttonEle}</Col>
            {!!record.labeled_orders_count && (
              <Col flex="20px">
                {record.orders_count &&
                record.labeled_orders_count &&
                sn(record.labeled_orders_count) < sn(record.orders_count) ? (
                  <WarningOutlined
                    className="cursor-pointer"
                    style={{ color: 'red', fontSize: 16 }}
                    title="Some orders don't have parcels! Click to view parcels"
                    onClick={() => {
                      setCurrentRow(record);
                      setOpenOrderLabelsModal(true);
                    }}
                  />
                ) : (
                  <CheckCircleOutlined
                    className="cursor-pointer"
                    style={{ color: 'green', fontSize: 16 }}
                    title="Click to view parcels"
                    onClick={() => {
                      setCurrentRow(record);
                      setOpenOrderLabelsModal(true);
                    }}
                  />
                )}
              </Col>
            )}
          </Row>
        );
      },
    },
    /* {
      title: 'Labeled orders count',
      dataIndex: ['labeled_orders_count'],
      align: 'right',
      width: 70,
      render: (_, record) => ni(record.labeled_orders_count),
    },
    {
      dataIndex: 'labels',
      title: 'Labels',
      width: 400,
      render: (_, record) => {
        return record.order_ids?.map((orderId) => {
          return (
            <Row key={orderId}>
              <Col span={2}>{orderId}</Col>
              <Col span={22}>
                {record.order2labels?.[orderId]?.map((label) => {
                  return (
                    <Row key={label.id}>
                      <Col span={4} className="text-center">
                        {label.service_name}
                      </Col>
                      <Col span={16}>{label.track_id}</Col>
                      <Col span={4} className="text-center">
                        {label.pos}
                      </Col>
                    </Row>
                  );
                })}
              </Col>
            </Row>
          );
        });
      },
    }, */

    {
      title: 'PrePicking / Picking',
      dataIndex: ['pre_picking_section'],
      width: 430,
      className: 'bl2',
      tooltip: 'Pre Picking / Picking Time per Order',
      render(__, record) {
        if (record.is_pre) {
          let ele: React.ReactNode = null;
          const timeTracks: API.WarehousePicklistTimeTrack[] = record?.time_tracks
            ? record?.time_tracks?.filter((x) => x?.type == 0) || []
            : [];

          if (record.status_pre_picking == PrePickingStatus.Open) {
            ele = record.pre_pdf_file ? (
              <StartOrResumePopover
                trackType={0}
                mode="startTimeTrack"
                picklistId={sn(record.id)}
                qtyEmployee={qtyEmployee}
                setQtyEmployee={setQtyEmployee}
                actionRef={actionRef}
              />
            ) : (
              <span className="text-sm italic c-red">&nbsp;</span>
            );
          } else if (record.status_pre_picking == PrePickingStatus.InProgress) {
            const activeTrack = !!timeTracks?.length && timeTracks.find((x) => x && !x.end_datetime);

            // No active time tracking?
            if (!activeTrack) {
              const latestTrack = timeTracks?.[0];
              if (latestTrack) {
                ele = (
                  <>
                    <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                    <StartOrResumePopover
                      trackType={0}
                      mode="resumeTimeTrack"
                      picklistId={sn(record.id)}
                      qtyEmployee={qtyEmployee}
                      setQtyEmployee={setQtyEmployee}
                      actionRef={actionRef}
                    />
                  </>
                );
              }
            } else {
              ele = (
                <StartOrResumePopover
                  trackType={0}
                  mode="pauseTimeTrack"
                  picklistId={sn(record.id)}
                  actionRef={actionRef}
                />
              );
              ele = (
                <>
                  <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                  {ele}
                </>
              );
            }

            ele = (
              <>
                {ele}
                <StartOrResumePopover
                  trackType={0}
                  mode="finishTimeTrack"
                  picklistId={sn(record.id)}
                  actionRef={actionRef}
                />
              </>
            );
          } else {
            // Finished picking picklist
            if (timeTracks?.length) {
              ele = (
                <>
                  <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                  <StartOrResumePopover
                    trackType={0}
                    mode="startTimeTrack"
                    picklistId={sn(record.id)}
                    qtyEmployee={qtyEmployee}
                    setQtyEmployee={setQtyEmployee}
                    actionRef={actionRef}
                    btnType="small"
                  />
                </>
              );
            }
          }

          return (
            <>
              <Space size={4}>
                <Space style={{ width: 130 }} size={4}>
                  <div style={{ width: 90, display: 'flex', alignItems: 'center' }}>
                    {record.pre_pdf_file ? (
                      <>
                        <div className="text-sm c-grey">{Util.dtToDMYHHMM(record.pre_pdf_file?.created_at)}</div>
                        <Button
                          type="link"
                          icon={<FilePdfOutlined />}
                          title="Download Pre summary PDF"
                          size="small"
                          className="text-sm"
                          style={{ width: 16 }}
                          onClick={() => {
                            const hide = message.loading('Downloading Pre summary data as PDF format...', 0);
                            exportPicklistPdf(sn(record.id), { isPreList: true, skipPreFileGeneration: true })
                              .then((res) => {
                                actionRef.current?.reload();
                                hide();
                                if (res.url) {
                                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                                }
                              })
                              .catch(Util.error)
                              .finally(() => {
                                hide();
                              });
                          }}
                        />
                      </>
                    ) : null}
                  </div>
                  {!!record.unbooked_count && (
                    <div>
                      <Button
                        type="link"
                        icon={<FilePdfOutlined />}
                        title="Generate & download Pre summary PDF"
                        size="small"
                        className="text-sm c-grey"
                        style={{ width: 16 }}
                        onClick={() => {
                          const hide = message.loading('Downloading Pre Summary data as PDF format...', 0);
                          exportPicklistPdf(sn(record.id), { isPreList: true })
                            .then((res) => {
                              actionRef.current?.reload();
                              hide();
                              if (res.url) {
                                window.open(`${API_URL}/api/${res.url}`, '_blank');
                              }
                            })
                            .catch(Util.error)
                            .finally(() => {
                              hide();
                            });
                        }}
                      />
                    </div>
                  )}
                </Space>
                <div
                  style={{ width: 40 }}
                  className={record.status_pre_picking == PrePickingStatus.Done ? 'text-sm' : 'text-sm c-grey'}
                >
                  {record.pre_picking_time_tracks_sum_minute
                    ? `${Util.numberFormat(record.pre_picking_time_tracks_sum_minute, false, 1, true)}m`
                    : ''}
                </div>
                {ele}
              </Space>
            </>
          );
        } else {
          let ele: React.ReactNode = null;
          const timeTracks: API.WarehousePicklistTimeTrack[] = record?.time_tracks
            ? record?.time_tracks?.filter((x) => x?.type == 1) || []
            : [];

          if (record.status_picking == PickingStatus.Open) {
            ele =
              sn(record.labeled_orders_count) > 0 ? (
                <StartOrResumePopover
                  trackType={1}
                  mode="startTimeTrack"
                  picklistId={sn(record.id)}
                  qtyEmployee={qtyEmployee}
                  setQtyEmployee={setQtyEmployee}
                  actionRef={actionRef}
                />
              ) : (
                <span className="text-sm italic c-red">No labels yet.</span>
              );
          } else if (record.status_picking == PickingStatus.InProgress) {
            const activeTrack = !!timeTracks?.length && timeTracks.find((x) => x && !x.end_datetime);

            // No active time tracking?
            if (!activeTrack) {
              const latestTrack = timeTracks?.[0];
              if (latestTrack) {
                ele = (
                  <>
                    <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                    <StartOrResumePopover
                      trackType={1}
                      mode="resumeTimeTrack"
                      picklistId={sn(record.id)}
                      qtyEmployee={qtyEmployee}
                      setQtyEmployee={setQtyEmployee}
                      actionRef={actionRef}
                    />
                  </>
                );
              }
            } else {
              ele = (
                <StartOrResumePopover
                  trackType={1}
                  mode="pauseTimeTrack"
                  picklistId={sn(record.id)}
                  actionRef={actionRef}
                />
              );
              ele = (
                <>
                  <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                  {ele}
                </>
              );
            }

            ele = (
              <>
                {ele}
                <StartOrResumePopover
                  trackType={1}
                  mode="finishTimeTrack"
                  picklistId={sn(record.id)}
                  actionRef={actionRef}
                />
              </>
            );
          } else {
            // Finished picking picklist
            if (timeTracks?.length) {
              ele = (
                <>
                  <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                  <StartOrResumePopover
                    trackType={1}
                    mode="startTimeTrack"
                    picklistId={sn(record.id)}
                    qtyEmployee={qtyEmployee}
                    setQtyEmployee={setQtyEmployee}
                    actionRef={actionRef}
                    btnType="small"
                  />
                </>
              );
            }
          }

          return (
            <>
              <Space size={4}>
                <div
                  style={{ width: 40 }}
                  className={record.status_picking == PickingStatus.Done ? 'text-sm' : 'text-sm c-grey'}
                >
                  {record.picking_time_tracks_sum_minute && record.orders_count
                    ? `${Util.numberFormat(
                        record.picking_time_tracks_sum_minute / sn(record.orders_count),
                        false,
                        1,
                        true,
                      )}m`
                    : ''}
                </div>
                {ele}
                {!record.unbooked_count && !!timeTracks?.length && (
                  <Compact>
                    <Button
                      type="primary"
                      size="small"
                      ghost
                      className="ant-btn-xs"
                      danger
                      title={`${record.unboxed_order_count} orders could be sub-grouped.`}
                      disabled={!record || !record.unboxed_order_count}
                      onClick={() => {
                        setCurrentRow(record);
                        setOpenBoxCreationModal(true);
                      }}
                    >
                      {record.unboxed_order_count
                        ? `Next (${record.boxed_order_count} / ${record.orders_count})`
                        : `${record.boxed_order_count} / ${record.orders_count}`}
                    </Button>
                    {!!record.box_steps && (
                      <Dropdown
                        key="export-menu"
                        menu={{
                          onClick: (info) => {
                            const hide = message.loading('Downloading picklist data as PDF format...', 0);
                            exportPicklistPdfByBox(sn(record.id), { box_step: info.key })
                              .then((res) => {
                                if (res.url) {
                                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                                }
                              })
                              .catch(Util.error)
                              .finally(() => {
                                hide();
                              });
                          },
                          items: record.box_steps?.split(',')?.map((x) => ({ key: x, label: `Subgroup #${x} PDF` })),
                        }}
                      >
                        <Button
                          type="default"
                          size="small"
                          ghost
                          danger
                          className="ant-btn-xs"
                          icon={<DownOutlined />}
                        />
                      </Dropdown>
                    )}
                  </Compact>
                )}
              </Space>
              {!record.unbooked_count && (
                <div className="absolute" style={{ top: -2, right: -2 }}>
                  <Button
                    type="link"
                    icon={<FilePdfOutlined />}
                    title="Download summary by Orders in PDF"
                    size="small"
                    className="text-sm"
                    onClick={() => {
                      const hide = message.loading('Downloading picklist data as PDF format...', 0);
                      exportPicklistPdf(sn(record.id), { mode: 'groupByOrder' })
                        .then((res) => {
                          hide();
                          if (res.url) {
                            window.open(`${API_URL}/api/${res.url}`, '_blank');
                          }
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                        });
                    }}
                  />
                </div>
              )}
            </>
          );
        }
      },
    },

    /* {
      title: 'Picking',
      dataIndex: ['picking_section'],
      width: 250,
      className: 'bl2',
      tooltip: 'Picking Time per Order',
      render(__, record) {
        if (record.is_pre) return null;

        let ele: React.ReactNode = null;
        const timeTracks: API.WarehousePicklistTimeTrack[] = record?.time_tracks
          ? record?.time_tracks?.filter((x) => x?.type == 1) || []
          : [];

        if (record.status_picking == PickingStatus.Open) {
          ele =
            sn(record.labeled_orders_count) > 0 ? (
              <StartOrResumePopover
                trackType={1}
                mode="startTimeTrack"
                picklistId={sn(record.id)}
                qtyEmployee={qtyEmployee}
                setQtyEmployee={setQtyEmployee}
                actionRef={actionRef}
              />
            ) : (
              <span className="text-sm italic c-red">No labels yet.</span>
            );
        } else if (record.status_picking == PickingStatus.InProgress) {
          const activeTrack = !!timeTracks?.length && timeTracks.find((x) => x && !x.end_datetime);

          // No active time tracking?
          if (!activeTrack) {
            const latestTrack = timeTracks?.[0];
            if (latestTrack) {
              ele = (
                <>
                  <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                  <StartOrResumePopover
                    trackType={1}
                    mode="resumeTimeTrack"
                    picklistId={sn(record.id)}
                    qtyEmployee={qtyEmployee}
                    setQtyEmployee={setQtyEmployee}
                    actionRef={actionRef}
                  />
                </>
              );
            }
          } else {
            ele = (
              <StartOrResumePopover
                trackType={1}
                mode="pauseTimeTrack"
                picklistId={sn(record.id)}
                actionRef={actionRef}
              />
            );
            ele = (
              <>
                <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                {ele}
              </>
            );
          }

          ele = (
            <>
              {ele}
              <StartOrResumePopover
                trackType={1}
                mode="finishTimeTrack"
                picklistId={sn(record.id)}
                actionRef={actionRef}
              />
            </>
          );
        } else {
          // Finished picking picklist
          if (timeTracks?.length) {
            ele = (
              <>
                <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                <StartOrResumePopover
                  trackType={1}
                  mode="startTimeTrack"
                  picklistId={sn(record.id)}
                  qtyEmployee={qtyEmployee}
                  setQtyEmployee={setQtyEmployee}
                  actionRef={actionRef}
                  btnType="small"
                />
              </>
            );
          }
        }

        return (
          <>
            <Space size={4}>
              <div
                style={{ width: 40 }}
                className={record.status_picking == PickingStatus.Done ? 'text-sm' : 'text-sm c-grey'}
              >
                {record.picking_time_tracks_sum_minute && record.orders_count
                  ? `${Util.numberFormat(
                      record.picking_time_tracks_sum_minute / sn(record.orders_count),
                      false,
                      1,
                      true,
                    )}m`
                  : ''}
              </div>
              {ele}
            </Space>
            {!record.unbooked_count && (
              <div className="absolute" style={{ top: -2, right: -2 }}>
                <Button
                  type="link"
                  icon={<FilePdfOutlined />}
                  title="Download summary by Orders in PDF"
                  size="small"
                  className="text-sm"
                  onClick={() => {
                    const hide = message.loading('Downloading picklist data as PDF format...', 0);
                    exportPicklistPdf(sn(record.id), { mode: 'groupByOrder' })
                      .then((res) => {
                        hide();
                        if (res.url) {
                          window.open(`${API_URL}/api/${res.url}`, '_blank');
                        }
                      })
                      .catch(Util.error)
                      .finally(() => {
                        hide();
                      });
                  }}
                />
              </div>
            )}
          </>
        );
      },
    }, */

    {
      title: 'Packing',
      dataIndex: ['packing_section'],
      width: 250,
      className: 'bl2',
      render(__, record) {
        if (record.is_pre) return null;

        const timeTracks: API.WarehousePicklistTimeTrack[] = record?.time_tracks
          ? record?.time_tracks?.filter((x) => x?.type == 2) || []
          : [];

        let ele: React.ReactNode = null;
        if (record.status_packing == PackingStatus.Open) {
          ele =
            sn(record.labeled_orders_count) > 0 ? (
              <StartOrResumePopover
                trackType={2}
                mode="startTimeTrack"
                picklistId={sn(record.id)}
                qtyEmployee={qtyEmployee}
                setQtyEmployee={setQtyEmployee}
                actionRef={actionRef}
              />
            ) : (
              <span className="text-sm italic c-red">No labels yet.</span>
            );
        } else if (record.status_packing == PackingStatus.InProgress) {
          const activeTrack = !!timeTracks?.length && timeTracks.find((x) => x && !x.end_datetime);

          // No active time tracking?
          if (!activeTrack) {
            ele = (
              <StartOrResumePopover
                trackType={2}
                mode="resumeTimeTrack"
                picklistId={sn(record.id)}
                qtyEmployee={qtyEmployee}
                setQtyEmployee={setQtyEmployee}
                actionRef={actionRef}
              />
            );

            const latestTrack = timeTracks?.[0];
            if (latestTrack) {
              ele = (
                <>
                  <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                  {ele}
                </>
              );
            }
          } else {
            ele = (
              <StartOrResumePopover
                trackType={2}
                mode="pauseTimeTrack"
                picklistId={sn(record.id)}
                actionRef={actionRef}
              />
            );
            ele = (
              <>
                <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                {ele}
              </>
            );
          }

          ele = (
            <>
              {ele}
              <StartOrResumePopover
                trackType={2}
                mode="finishTimeTrack"
                picklistId={sn(record.id)}
                actionRef={actionRef}
              />
            </>
          );
        } else {
          // Finished packing picklist
          if (timeTracks?.length) {
            ele = (
              <>
                <TimeTracksPopover picklist={{ id: record.id, note: record.note }} timeTracks={timeTracks} />
                <StartOrResumePopover
                  trackType={2}
                  mode="startTimeTrack"
                  picklistId={sn(record.id)}
                  qtyEmployee={qtyEmployee}
                  setQtyEmployee={setQtyEmployee}
                  actionRef={actionRef}
                  btnType="small"
                />
              </>
            );
          }
        }

        return (
          <>
            <Space size={4}>
              <div
                style={{ width: 40 }}
                className={record.status_packing == PackingStatus.Done ? 'text-sm' : 'text-sm c-grey'}
              >
                {record.packing_time_tracks_sum_minute && record.orders_count
                  ? `${Util.numberFormat(
                      record.packing_time_tracks_sum_minute / sn(record.orders_count),
                      false,
                      1,
                      true,
                    )}m`
                  : ''}
              </div>
              {ele}
            </Space>
            {/* <div className="absolute" style={{ top: -2, right: -2 }}>
              <Button
                type="link"
                icon={<FilePdfOutlined />}
                title="Download summary 2 by Orders in PDF"
                size="small"
                className="text-sm"
                onClick={() => {
                  const hide = message.loading('Downloading picklist data as PDF format...', 0);
                  exportPicklistPdf(sn(record.id), { mode: 'groupByOrder' })
                    .then((res) => {
                      hide();
                      if (res.url) {
                        window.open(`${API_URL}/api/${res.url}`, '_blank');
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                    });
                }}
              />
            </div> */}
          </>
        );
      },
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      width: 180,
      render: (_, record) =>
        record.is_pre ? null : (
          <Space>
            {/* {record.final_pdf_file?.url ? (
            <a
              href={`${API_URL}/api/${record.final_pdf_file?.url}`}
              target="_blank"
              rel="noreferrer"
              title="View final picklist in PDF format."
            >
              <FilePdfOutlined />
            </a>
          ) : (
            <a
              target="_blank"
              rel="noreferrer"
              title="Generate PDF file"
              className="c-grey"
              onClick={(e) => {
                e.preventDefault();
                const hide = message.loading('Downloading picklist data as PDF format...', 0);
                exportPicklistPdf(record.id)
                  .then((res) => {
                    hide();
                    if (res.url) {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              <FilePdfOutlined />
            </a>
          )} */}
            {initialState?.currentUser?.role != UserRole.WAREHOUSE && (
              <a
                target="_blank"
                rel="noreferrer"
                title="Generate CSV file"
                className="c-grey"
                onClick={(e) => {
                  e.preventDefault();
                  const hide = message.loading('Exporting picklist data in CSV format...', 0);
                  exportPicklistCsv(record.id)
                    .then((res) => {
                      hide();
                      if (res.url) {
                        window.location.href = `${API_URL}/api/${res.url}`;
                      }
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
              >
                <FileExcelOutlined />
              </a>
            )}
            <Button
              type="primary"
              size="small"
              className="btn-pink ant-btn-xs"
              title="Download delivery notes in PDF"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                const hide = message.loading('Downloading delivery note as PDF format...', 0);
                setLoading(true);
                exportPicklistDeliveryNote(sn(record.id))
                  .then((res) => {
                    hide();
                    if (res.length) {
                      const file = res[0];
                      if (file.url) {
                        window.open(`${API_URL}/api/${file.url}`, '_blank');
                      }
                    }
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                    setLoading(false);
                  });
              }}
            >
              Delivery note
            </Button>
            <Button
              type="primary"
              className="btn-dark-red ant-btn-xs"
              size="small"
              title="Generate merged labels in PDF"
              onClick={() => {
                handlePrintLabelMerged(sn(record?.id));
              }}
              // disabled={record.labeled_orders_count != record.orders_count}
              disabled={!record.labeled_orders_count}
            >
              Labels
            </Button>
          </Space>
        ),
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            name={'booking_status'}
            label="Booking status"
            allowClear
            options={[
              {
                value: 'booked',
                label: 'Booked',
              },
              {
                value: 'unbooked',
                label: 'Bookable',
              },
            ]}
            placeholder={'Status'}
            width={'sm'}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormSelect
            name={'is_pre'}
            label="Type"
            allowClear
            options={[
              {
                value: 0,
                label: 'Picklist',
              },
              {
                value: 1,
                label: 'Pre Picklist',
              },
            ]}
            placeholder={'Type'}
            width={'sm'}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
        </ProForm>
      </Card>

      <ProTable<API.WarehousePicklist, API.PageParams>
        headerTitle={'Warehouse Picklist'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        onRequestError={Util.error}
        params={{
          with: 'details,timeTracks,includeQtyPerOrderType,includeOrderIds,includeOrderLabels,includeSubBoxes',
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          setLoading(true);

          return await getPicklist(
            {
              ...params,
              ...searchFormValues,
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        rowClassName={(record) => {
          let cls = 'reset-tds-bg';
          if (sn(record.unbooked_count) > 0) {
            cls += ' bg-light-red1';
          } else {
            if (record.status_picking == PickingStatus.Done && record.status_packing == PackingStatus.Done) {
              cls += ' bg-grey';
            }
          }
          return cls;
        }}
        columns={columns}
        rowSelection={false}
        columnEmptyText=""
      />

      {!!currentRow?.id && noStockSKUList && (
        <NoStockSkuModal
          handleModalVisible={setOpenNoStockSKUModal}
          modalVisible={openNoStockSKUModal}
          picklistId={currentRow.id}
          skus={noStockSKUList}
          successCb={() => {
            actionRef.current?.reload();
          }}
        />
      )}

      {!!currentRow?.id && (
        <OrderLabelsModal
          modalVisible={openOrderLabelsModal}
          handleModalVisible={() => {
            setOpenOrderLabelsModal(false);
            setCurrentRow(null);
          }}
          picklist={{ id: currentRow.id, note: currentRow.note }}
          orderIds={currentRow?.order_ids}
          order2labels={currentRow?.order2labels}
        />
      )}

      {!!currentRow?.id && (
        <BoxCreationModal
          modalVisible={openBoxCreationModal}
          handleModalVisible={setOpenBoxCreationModal}
          picklist={{ id: currentRow.id }}
          onSubmit={(boxStep) => {
            if (boxStep) {
              actionRef.current?.reload();
              // We don't need to open a grouped PDF file.
              /* const hide = message.loading('Downloading picklist data as PDF format...', 0);
              return exportPicklistPdfByBox(sn(currentRow.id), { box_step: boxStep })
                .then((res) => {
                  hide();
                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                }); */

              return Promise.resolve(true);
            } else {
              return Promise.reject(false);
            }
          }}
        />
      )}
    </PageContainer>
  );
};

export default Picklist;
