import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Col, message, Row, Typography } from 'antd';
import type { FormListActionType, ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormList } from '@ant-design/pro-form';
import Util, { ni } from '@/util';
import OfferItemDeliveredList from '../../OfferItemDeliveredList/components/OfferItemDeliveredList';
import { addOfferItemDeliveredBulk } from '@/services/foodstore-one/Offer/offer-item-delivered';
import NumpadExtSelector from '@/components/NumpadExtSelector';
import DateSelector from '@/components/DateSelector';
import EanFilesComp from '@/components/EanFilesComp';
import { getEanDetail } from '@/services/foodstore-one/Item/ean';
import SkuComp from '@/components/SkuComp';

import styles from './CreateOrUpdateQtyDeliveredModalForm.less';
import StockStableQtyModalForPackedQty from '@/pages/Item/EanList/components/StockStableQtyModalForPackedQty';

export type FormValueType = Partial<API.OfferItemDelivered>;

export type CreateOrUpdateQtyDeliveredModalFormProps = {
  itemEan: Partial<API.Ean>;
  offerItem: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  loadOfferItem?: () => Promise<API.OfferItem | null>;
  onSubmit?: (formData: API.OfferItemDelivered) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const CreateOrUpdateQtyDeliveredModalForm: React.FC<CreateOrUpdateQtyDeliveredModalFormProps> = ({
  itemEan,
  offerItem,
  modalVisible,
  handleModalVisible,
  loadOfferItem,
  onSubmit,
  onCancel,
}) => {
  const formRef = useRef<ProFormInstance>();
  const [loadingEan, setLoadingEan] = useState<boolean>(false);
  const [itemEanDetail, setItemEanDetail] = useState<API.Ean>();

  const [listReloadTick, setListReloadTick] = useState<number>(0);
  // Editable list
  const listActionRef = useRef<FormListActionType>();

  // stock stable qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  useEffect(() => {
    setListReloadTick((prev) => prev + 1);
  }, [modalVisible]);

  const resetFormFields = () => {
    const listByRef = listActionRef.current?.getList();
    const newList = listByRef?.map((x) => ({ ...x, qty: null, exp_date: null }));
    formRef.current?.setFieldValue('qty_delivered_list', newList);
  };

  const loadEanDetails = useCallback(() => {
    if (modalVisible && itemEan.id && offerItem.id) {
      setLoadingEan(true);
      getEanDetail({ id: itemEan.id, with: 'siblings,magInventoryStocksQty' })
        .then((res) => {
          setItemEanDetail(res);
          const qtyDeliveredList: any[] = [];
          if (res.siblings) {
            res.siblings.forEach((x) => {
              const rowObj = {
                ean_id: x.id,
                item_id: x.item_id,
                ean: x.ean,
                sku: x.sku,
                case_qty: x.attr_case_qty,
                offer_item_id: offerItem.id,
              };
              qtyDeliveredList.push(rowObj);
            });
          }
          formRef.current?.setFieldValue('qty_delivered_list', qtyDeliveredList);
        })
        .catch((err) => {
          Util.error(err);
          formRef.current?.setFieldValue('qty_delivered_list', []);
        })
        .finally(() => {
          setLoadingEan(false);
        });
    }
  }, [itemEan, offerItem.id]);

  useEffect(() => {
    if (modalVisible) {
      loadEanDetails();
    }
  }, [modalVisible, loadEanDetails]);

  return (
    <ModalForm<FormValueType>
      title={
        <Row gutter={16}>
          <Col>Add Delivered Qty -&nbsp;</Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.ean || '',
                tooltips: 'Copy EAN ' + (itemEan?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.ean || ''}
            </Typography.Paragraph>
          </Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.sku || '',
                tooltips: 'Copy SKU ' + (itemEan?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.sku || ''}
            </Typography.Paragraph>
          </Col>
          <Col>{`Qty / Case: ${itemEan.attr_case_qty}`}</Col>
        </Row>
      }
      width="900px"
      size="large"
      visible={modalVisible}
      onVisibleChange={(visible) => {
        handleModalVisible(visible);
      }}
      grid
      formRef={formRef}
      className={styles.createOrUpdateQtyDeliveredModalForm}
      onFinish={async (value) => {
        return Promise.resolve(true);
      }}
      modalProps={{
        maskClosable: false,
        className: itemEan?.is_single ? 'm-single' : 'm-multi',
        confirmLoading: loadingEan,
      }}
      submitter={{ submitButtonProps: { style: { display: 'none' } } }}
    >
      <Col flex="180px">
        <EanFilesComp files={itemEan.files} width={160} outlineBorder />
        <div className="text-center" style={{ marginTop: 12 }}>
          <SkuComp sku={itemEan.sku} />
        </div>
      </Col>
      <Col flex="auto" style={{ paddingLeft: 24 }}>
        <ProFormList
          actionRef={listActionRef}
          key={'uid'}
          name="qty_delivered_list"
          creatorButtonProps={false}
          creatorRecord={{}}
          deleteIconProps={{ tooltipText: 'Remove' }}
          copyIconProps={false}
          alwaysShowItemLabel={false}
          actionRender={(field, action, doms) => []}
        >
          {(meta, index, action, count) => {
            // console.log('list render', meta, index, action, count);
            const rowData = action.getCurrentRowData();

            return (
              <Row className="test-xxxx" key={meta.key}>
                <Col span={8}>
                  <ProForm.Item
                    name="ean"
                    label={
                      <Typography.Text ellipsis copyable>
                        {itemEan.ean_text_de?.name}
                      </Typography.Text>
                    }
                  >
                    {rowData.ean}
                  </ProForm.Item>
                </Col>

                <Col span={2}>
                  <ProForm.Item name="case_qty" label=" ">
                    {rowData.case_qty}
                  </ProForm.Item>
                </Col>

                <Col span={7}>
                  <ProForm.Item name="qty" label="Delivered Qty">
                    <NumpadExtSelector inputProps={{ style: { width: 140 }, inputMode: 'none' }} isMobile />
                  </ProForm.Item>
                </Col>
                <Col span={7}>
                  <ProForm.Item name="exp_date" label="Exp. Date">
                    <DateSelector
                      showBodyScroll
                      onChange={function (value: string): void {
                        //
                      }}
                      isMobile
                    />
                  </ProForm.Item>
                </Col>
              </Row>
            );
          }}
        </ProFormList>
      </Col>
      <Col span={24}>
        <Row>
          <Col
            offset={1}
            span={5}
            className="cursor-pointer c-blue"
            onClick={() => {
              handleQtyModalVisible(true);
            }}
          >
            <span>Stock Qty: </span>
            <span>{ni(itemEanDetail?.parent_stock_stables_sum_total_piece_qty, true)}</span>
          </Col>
          <Col span={6} className="" style={{ fontSize: 16 }}>
            <span>Offer Qty: </span>
            <span style={{ fontWeight: 'bold' }}>
              {offerItem.case_qty == 1 ? ni(offerItem.qty) : `${ni(offerItem.qty)} x ${ni(offerItem.case_qty)}`}
            </span>
          </Col>
          <Col span={6} style={{ fontSize: 16 }} className="c-orange">
            <span>Sum: </span>
            <span style={{ fontWeight: 'bold' }}>{ni(offerItem.delivered_qty_pcs, true)} pcs</span>
          </Col>
          <Col span={6}>
            <Button
              type="primary"
              size="large"
              className="btn-green"
              onClick={async () => {
                const listByRef = listActionRef.current?.getList();

                const rows: any = [];
                listByRef?.forEach((row) => {
                  if (row.qty && row.exp_date) {
                    rows.push(row);
                  }
                });

                if (rows.length < 1) {
                  message.info('Nothing to save! Please fill data correctly.');
                  return;
                }

                const success = await addOfferItemDeliveredBulk(rows).catch(Util.error);

                if (success) {
                  message.success('Delivery Qty added successfully.');
                  loadOfferItem?.();
                  setListReloadTick((prev) => prev + 1);

                  resetFormFields();
                }
              }}
            >
              Save
            </Button>
          </Col>
        </Row>
      </Col>

      <OfferItemDeliveredList offer_item_id={offerItem.id} item_id={offerItem.item_id} reloadTick={listReloadTick} />

      <StockStableQtyModalForPackedQty
        offer_item_id={offerItem.id}
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: itemEanDetail?.id,
          item_id: itemEanDetail?.item_id,
          parent_id: itemEanDetail?.parent_id,
          is_single: itemEanDetail?.is_single,
          sku: itemEanDetail?.sku,
          ean: itemEanDetail?.ean,
          ean_text_de: itemEanDetail?.ean_text_de,
          mag_inventory_stocks_sum_quantity: itemEanDetail?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: itemEanDetail?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: itemEanDetail?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          loadOfferItem?.();
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
        reload={() => {
          loadEanDetails();
          onCancel?.();
        }}
      />
    </ModalForm>
  );
};

export default CreateOrUpdateQtyDeliveredModalForm;
