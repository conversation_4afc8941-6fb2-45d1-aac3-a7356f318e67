import { Button, Popconfirm, Space, message } from 'antd';
import _ from 'lodash';
import { CloudUploadOutlined, SaveOutlined } from '@ant-design/icons';
import { useCallback, useMemo } from 'react';
import { ProFormText, type ProFormInstance } from '@ant-design/pro-form';
import { usProductFull } from '@/services/foodstore-one/Item/ean';
import type { SizeType } from 'antd/lib/config-provider/SizeContext';

type ParamOptionType = {
  hideSaveButton?: boolean;
  buttonSize?: SizeType;
  showButtonText?: boolean;
};

const useUpdateModalActions = (
  itemEanId: number,
  sku: string,
  formRefCur?: ProFormInstance,
  options?: ParamOptionType,
) => {
  const renderActionButtons = useCallback(
    (optionsP?: ParamOptionType) => {
      const opts = optionsP ?? options;
      return itemEanId && sku && formRefCur ? (
        <Space size={8} style={{ marginLeft: 12 }}>
          {!opts?.hideSaveButton && (
            <Button
              size={opts?.buttonSize ?? 'small'}
              type="primary"
              icon={<SaveOutlined />}
              title="Save data"
              className="btn-green"
              onClick={() => {
                formRefCur?.setFieldValue('closeModal', null);
                formRefCur?.submit();
              }}
            >
              {opts?.showButtonText ? 'Save' : null}
            </Button>
          )}
          <Popconfirm
            key="upsync"
            placement="topRight"
            title={
              <>
                Are you sure you want to up sync the EAN？ <br />
                <br />A new product will be created in the shop if SKU {`"${sku}"`} does not exist.
              </>
            }
            overlayStyle={{ width: 350 }}
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              formRefCur?.setFieldValue('doUpSync', 1);
              formRefCur?.submit();
            }}
          >
            <Button
              size={opts?.buttonSize ?? 'small'}
              type="default"
              icon={<CloudUploadOutlined />}
              title="Save & Upsync"
            >
              {opts?.showButtonText ? 'Save & Upsync' : null}
            </Button>
          </Popconfirm>
        </Space>
      ) : null;
    },
    [formRefCur, itemEanId, options, sku],
  );

  const actionButtons = useMemo(() => {
    return renderActionButtons();
  }, [renderActionButtons]);

  const hiddenFormElements = useMemo(() => {
    return (
      <>
        <ProFormText name="doUpSync" />
      </>
    );
  }, []);

  /**
   * UpSync this EAN fully.
   */
  const handleUpSync = useCallback(async (id: number) => {
    const hide = message.loading(`Up syncing ...`, 0);
    return usProductFull(id)
      .then((res) => {
        if (res.sku) {
          message.success('Successfully up synced on shop!');
        } else {
          message.error(res.upSyncMessage || 'Failed to up sync EAN!');
        }
        return res;
      })
      .catch((e) => {
        message.error(e.message ?? 'Failed to upsync!');
        throw e;
      })
      .finally(() => {
        hide();
      });
  }, []);

  const runActionsCallback = useCallback(async (): Promise<API.Ean | true> => {
    if (itemEanId && formRefCur) {
      const fValues = formRefCur?.getFieldsValue();
      if (fValues.doUpSync) {
        return handleUpSync(itemEanId).finally(() => {
          // !important. We reset upsync flag
          formRefCur?.setFieldValue('doUpSync', '');
        });
      }
    }
    return Promise.resolve(true);
  }, [formRefCur, handleUpSync, itemEanId]);

  return { actionButtons, hiddenFormElements, handleUpSync, runActionsCallback, renderActionButtons };
};

export default useUpdateModalActions;
