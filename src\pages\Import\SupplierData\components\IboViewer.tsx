import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import { useRef } from 'react';
import { useMemo } from 'react';
import React from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import {
  deleteMarriedTwoEANs,
  getUploadedDataWithIboList,
  importPricesFromSupplierData,
  updateMarryTwoEANs,
} from '@/services/foodstore-one/Import/import';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { FooterToolbar } from '@ant-design/pro-layout';
import { But<PERSON>, Card, message, Modal, Popconfirm, Space, Spin, Tag, Typography } from 'antd';
import {
  ApiOutlined,
  EuroOutlined,
  HighlightOutlined,
  MergeCellsOutlined,
  SaveOutlined,
  SearchOutlined,
  SplitCellsOutlined,
} from '@ant-design/icons';
import { updateEanByEan } from '@/services/foodstore-one/Item/ean';
import Util, { sn } from '@/util';
import { definedColLabelsImported } from './XlsItemViewer';
import type { DefaultOptionType } from 'antd/lib/select';
import { updateIbo, updateIboAll } from '@/services/foodstore-one/IBO/ibo';
import MarryEanModal from './MarryEanModal';

const isMarryPossible = (ibo: API.ImportedSupplierAndIbo, xls: API.ImportedSupplierAndIbo) => {
  if (!xls.ibo_id && !ibo.xls_id && xls.ibo_id != ibo.ibo_id && xls.xls_id != ibo.xls_id) return true;
  return false;
};

export type FormValueType = Partial<API.Import>;

export type SearchFormValueType = Partial<API.ImportedSupplierAndIbo>;

export type IboViewerProps = {
  initialValue: Partial<API.Import>;
  modalVisible: boolean;
  ibomList: DefaultOptionType[];
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const IboViewer: React.FC<IboViewerProps> = (props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingEdits, setLoadingEdits] = useState<Record<string, boolean>>({});
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [selectedRowsState, setSelectedRows] = useState<Partial<API.ImportedSupplierAndIbo>[]>([]);

  // popup
  /* const [selectedSysEan, setSelectedSysEan] = useState<Partial<API.ImportedSupplierAndIbo>>();
  const [selectedImportedEan, setSelectedImportedEan] = useState<Partial<API.ImportedSupplierAndIbo>>();
  const [popup, setPopup] = useState({ visible: false, x: 0, y: 0 }); */

  // marriage modal
  const [currentRow, setCurrentRow] = useState<Partial<API.ImportedSupplierAndIbo>>();
  const [openMarryModal, setOpenMarryModal] = useState<boolean>(false);
  const [selectedIbomId, setSelectedIbomId] = useState<number>();

  const { id, settings } = props.initialValue;

  // @ts-ignore
  const columns: ProColumns<API.ImportedSupplierAndIbo>[] = useMemo(() => {
    if (!props.modalVisible || !id || !settings) return [];

    const dbCols: string[] = settings?.dbCols || [];

    // columns definitions.
    // prefix "i_" means "import"
    const newColumns: ProColumns<API.ImportedSupplierAndIbo>[] = [
      {
        dataIndex: 'index',
        valueType: 'indexBorder',
        width: 40,
        align: 'center',
        render: (item, record, index, action) => {
          return (
            ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
            index +
            1
          );
        },
      },
    ];

    // EAN (sys) column
    const includedCols: string[] = [];

    let col = 'ean_sys';
    newColumns.push({
      dataIndex: 'ean_sys',
      title: (
        <>
          <div className="c-blue">EAN</div>
        </>
      ),
      width: 150,
      ellipsis: false,
      filters: true,
      render: (dom, record: API.ImportedSupplierAndIbo) => {
        return record.ean_sys ? (
          <Typography.Paragraph copyable={{ text: record.ean_sys }} className="margin-0">
            <span>{record.ean_sys}</span>
          </Typography.Paragraph>
        ) : (
          <span>{record.ean_sys}</span>
        );
      },
    });
    includedCols.push(col);

    col = 'ean';
    let ind = dbCols?.findIndex?.((x: string) => x == 'ean');
    if (ind >= 0) {
      ind = dbCols?.findIndex?.((x: string) => x == col);
      newColumns.push({
        dataIndex: col,
        title: (
          <>
            <div className="c-green">EAN XLS</div>
          </>
        ),
        width: 150,
        ellipsis: false,
        filters: true,
        render: (dom, record: API.ImportedSupplierAndIbo) => {
          return (
            <>
              {record.ean ? (
                <Typography.Paragraph copyable={{ text: record.ean }} className="margin-0">
                  <span className={record.ean_exist ? 'c-green' : ''}>{record.ean}</span>
                </Typography.Paragraph>
              ) : (
                <div className={record.ean_exist ? 'c-green' : ''}>{record.ean}</div>
              )}
              {record.marry_ean_org ? (
                <Typography.Paragraph copyable={{ text: record.marry_ean_org }} className="margin-0">
                  <span className={'text-small italic' + (record.ean_exist ? ' c-green' : '')}>
                    {record.marry_ean_org}
                  </span>
                </Typography.Paragraph>
              ) : null}
              <div style={{ position: 'absolute', top: 0, right: 0, display: 'block' }}>
                {!record.ibo_id && record.xls_id && !record.marry_ean_org && (
                  <MergeCellsOutlined
                    title="View IBO list to select marriable EAN..."
                    className="c-dark-purple"
                    onClick={async () => {
                      setCurrentRow(record);
                      setOpenMarryModal(true);
                    }}
                  />
                )}
                {record.ibo_id && record.xls_id && record.marry_ean_org && (
                  <SplitCellsOutlined
                    title="Unlink 2 EANs."
                    className="c-lightred"
                    onClick={async () => {
                      if (!record.ibo_id || !record.xls_id) {
                        message.info('Cannot unlink!');
                        return Promise.resolve();
                      }
                      const hide = message.loading('Unlinking EANs between IBO and XLS', 0);
                      deleteMarriedTwoEANs(props.initialValue.id, [
                        {
                          ibo_id: record.ibo_id,
                          xls_id: record.xls_id,
                        },
                      ])
                        .then(() => {
                          message.success('Successfully unlinked!');
                          setSelectedRows([]);
                          actionRef.current?.reload();
                        })
                        .catch(Util.error)
                        .finally(() => hide());
                    }}
                  />
                )}
              </div>
            </>
          );
        },
      });
      includedCols.push(col);

      // EAN name
      col = 'name';
      ind = dbCols?.findIndex?.((x: string) => x == 'name');
      newColumns.push({
        dataIndex: col,
        title: (
          <>
            <div className={ind < 0 ? '' : 'c-green'}>EAN Name (XLS)</div>
          </>
        ),
        width: 200,
        filters: true,
        render: (dom, record: API.ImportedSupplierAndIbo) =>
          record.name ? (
            <Typography.Paragraph copyable={{ text: record.name }} className="margin-0">
              {record.name}
            </Typography.Paragraph>
          ) : (
            record.name
          ),
      });
      includedCols.push(col);

      col = 'i_qty';
      ind = dbCols?.findIndex?.((x: string) => x == 'qty');
      newColumns.push({
        dataIndex: col,
        title: <div className={ind < 0 ? '' : 'c-green'}>Qty (XLS)</div>,
        width: 100,
        filters: true,
        align: 'right',
        render: (dom, record: API.ImportedSupplierAndIbo) => {
          return <span>{Util.numberFormat(record.i_qty)}</span>;
        },
        onCell: (record, rowIndex) => {
          let cls = '';
          if (Util.safeInt(record.i_qty) != Util.safeInt(record.qty)) {
            cls = 'bg-light-red1';
          }
          return {
            className: cls,
          };
        },
      });
      includedCols.push(col);

      col = 'qty';
      newColumns.push({
        dataIndex: col,
        title: <div>Qty IBO</div>,
        width: 100,
        filters: true,
        align: 'right',
        render: (dom, record: API.ImportedSupplierAndIbo) => Util.numberFormat(record.qty),
        onCell: (record, rowIndex) => {
          let cls = '';
          if (Util.safeInt(record.i_qty) != Util.safeInt(record.qty)) {
            cls = 'bg-light-red1';
          }
          return {
            className: cls,
          };
        },
      });

      col = 'i_case_qty';
      ind = dbCols?.findIndex?.((x: string) => x == 'case_qty');
      newColumns.push({
        dataIndex: col,
        title: <div className={ind < 0 ? '' : 'c-green'}>Qty/Pkg (XLS)</div>,
        width: 100,
        filters: true,
        align: 'right',
        // className: ind >= 0 && definedColLabelssImported.col_case_qty ? 'bg-light-green' : '',
        render: (dom, record: API.ImportedSupplierAndIbo) => Util.numberFormat(record.i_case_qty),
        onCell: (record, rowIndex) => {
          let cls = '';
          if (Util.safeInt(record.i_case_qty) != Util.safeInt(record.case_qty)) {
            cls = 'bg-light-orange2';
          }
          return {
            className: cls,
          };
        },
      });

      col = 'case_qty';
      newColumns.push({
        dataIndex: col,
        title: <div>Qty/Pkg IBO</div>,
        width: 100,
        filters: true,
        align: 'right',
        render: (dom, record: API.ImportedSupplierAndIbo) => Util.numberFormat(record.case_qty),
        onCell: (record, rowIndex) => {
          let cls = '';
          if (Util.safeInt(record.i_case_qty) != Util.safeInt(record.case_qty)) {
            cls = 'bg-light-orange2';
          }
          return {
            className: cls,
          };
        },
      });

      col = 'i_price';
      ind = dbCols?.findIndex?.((x: string) => x == 'price');
      newColumns.push({
        dataIndex: col,
        title: <div className={ind < 0 ? '' : 'c-green'}>Price (XLS)</div>,
        width: 100,
        filters: true,
        align: 'right',
        className: ind >= 0 && definedColLabelsImported.col_price ? 'bg-light-green' : '',
        render: (dom, record: API.ImportedSupplierAndIbo) => {
          return Util.numberFormat(record.i_price, false, 2);
        },
        onCell: (record, rowIndex) => {
          const a = record.price;
          const b = record.i_price;

          let cls = '';
          if (Util.safeNumber(a) != Util.safeNumber(b)) {
            cls = 'bg-light-orange1';
          }
          return {
            className: cls,
          };
        },
      });

      col = 'price';
      newColumns.push({
        dataIndex: col,
        title: <div>Price IBO</div>,
        width: 100,
        filters: true,
        align: 'right',
        render: (dom, record: API.ImportedSupplierAndIbo) => {
          const iPriceSelected = Util.safeNumber(record.i_price);
          const iboId = record.ibo_id || null;
          return (
            <>
              {iboId && iPriceSelected ? (
                <HighlightOutlined
                  title="Update IBO price with the first Price (XLS)."
                  className="c-blue"
                  style={{ position: 'absolute', top: -2, right: -2, display: 'block' }}
                  onClick={async () => {
                    const hide = message.loading('Updating IBO price...', 0);
                    await updateIbo({ id: iboId, price: iPriceSelected })
                      .then((res) => {
                        actionRef.current?.reload();
                      })
                      .catch(Util.error);
                    hide();
                  }}
                />
              ) : undefined}
              <div>{Util.numberFormat(record.price, false, 2)}</div>
            </>
          );
        },
        onCell: (record, rowIndex) => {
          const a = record.price;
          const b = record.i_price;

          let cls = '';
          if (Util.safeNumber(a) != Util.safeNumber(b)) {
            cls = 'bg-light-orange1';
          }
          return {
            className: cls,
          };
        },
      });

      col = 'i_exp_date';
      ind = dbCols?.findIndex?.((x: string) => x == 'exp_date');
      newColumns.push({
        dataIndex: col,
        title: <div className={ind < 0 ? '' : 'c-green'}>Exp. Date (XLS)</div>,
        width: 120,
        filters: true,
        align: 'center',
        render: (dom, record: API.ImportedSupplierAndIbo) => {
          return Util.dtToDMY(record.i_exp_date);
        },
      });

      col = 'exp_date';
      newColumns.push({
        dataIndex: col,
        title: <div>Exp. Date IBO</div>,
        width: 100,
        filters: true,
        align: 'right',
        render: (dom, record: API.ImportedSupplierAndIbo) => {
          return <div>{Util.dtToDMY(record.exp_date)}</div>;
        },
      });

      col = 'item_name_sys';
      newColumns.push({
        dataIndex: col,
        title: <div className="c-blue">Name of Item</div>,
        width: 200,
        filters: true,
        // tooltip: 'Please click to edit.',
        render: (dom, record: API.ImportedSupplierAndIbo, index, action) => {
          const eanStr = record.ean || '__undefined';
          return (
            <Spin spinning={loadingEdits[eanStr] || false}>
              <Typography.Paragraph
                style={{ margin: 0, left: 0 }}
                editable={{
                  // tooltip: 'click to edit text',
                  editing: false,
                  onChange: async (value) => {
                    if (value != record.item_name_sys) {
                      setLoadingEdits((prev) => ({ ...prev, [eanStr]: true }));
                      await updateEanByEan(eanStr, { item: { name: value } }).catch(Util.error);
                      setLoadingEdits((prev) => ({ ...prev, [eanStr]: false }));
                      action?.reload();
                    }
                    return value;
                  },
                  triggerType: ['text'],
                }}
              >
                {record.item_name_sys}
              </Typography.Paragraph>
            </Spin>
          );
        },
      });
      includedCols.push(col);

      col = 'name_sys';
      newColumns.push({
        dataIndex: col,
        title: <div className="c-blue">Name of EAN</div>,
        width: 200,
        filters: true,
        render: (dom, record: API.ImportedSupplierAndIbo, index, action) => {
          const eanStr = record.ean || '__undefined';
          return (
            <Spin spinning={loadingEdits[eanStr] || false}>
              <Typography.Paragraph
                style={{ margin: 0, left: 0 }}
                editable={{
                  // tooltip: 'click to edit text',
                  editing: false,
                  onChange: async (value) => {
                    if (value != record.name_sys) {
                      setLoadingEdits((prev) => ({ ...prev, [eanStr]: true }));
                      await updateEanByEan(eanStr, { ean_texts: [{ name: value }] }).catch(Util.error);
                      setLoadingEdits((prev) => ({ ...prev, [eanStr]: false }));
                      action?.reload();
                    }
                    return value;
                  },
                  triggerType: ['text'],
                }}
              >
                {record.name_sys}
              </Typography.Paragraph>
            </Spin>
          );
        },
      });
      includedCols.push(col);
    }

    return [...newColumns];
  }, [props.modalVisible, props.initialValue.id, id, settings, loadingEdits]);

  const marryPossible = useMemo(() => {
    if (selectedRowsState.length != 2) return false;

    let ibo, xls;
    if (selectedRowsState[0].ibo_id) {
      ibo = selectedRowsState[0];
      xls = selectedRowsState[1];
    } else {
      ibo = selectedRowsState[1];
      xls = selectedRowsState[0];
    }

    return isMarryPossible(ibo, xls);
  }, [selectedRowsState]);

  const unmarigeable = useMemo(() => {
    if (!selectedRowsState.length) return false;
    return selectedRowsState.filter((x) => x.marry_ibo_id && x.marry_xls_id).length > 0;
  }, [selectedRowsState]);

  const handleImportPrice = () => {
    const values = searchFormRef.current?.getFieldsValue();
    const ibomId = values.ibom_id;
    if (ibomId) {
      const hide = message.loading('Importing prices...', 0);
      importPricesFromSupplierData(id, ibomId)
        .then((res) => {
          console.log(res);
          message.success(`Updated ${Util.safeInt(res.updatedCount)} rows.`);
        })
        .catch(Util.error)
        .finally(() => hide());
    } else {
      message.warning('Please select IBOM.');
    }
  };

  useEffect(() => {
    if (props.modalVisible && props.initialValue.id) {
      actionRef.current?.reload();
    }
  }, [props.modalVisible, props.initialValue.id]);

  return (
    <Modal
      title={
        <>
          Imported Supplier x IBO List -{' '}
          <Tag className="text-small">{props.initialValue.files?.[0]?.clean_file_name}</Tag> /{' '}
          <Tag>{props.initialValue.table_name}</Tag>
        </>
      }
      width="90%"
      className="iboViewer"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={false}
    >
      <Card style={{ marginBottom: 0, paddingTop: 0 }} bordered={false} bodyStyle={{ padding: 0 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          style={{ marginBottom: 0 }}
          submitter={{
            submitButtonProps: { loading: loading },
            render: (__, doms) => {
              return [
                <Button
                  type="primary"
                  key="submit"
                  onClick={() => actionRef.current?.reload()}
                  loading={loading}
                  disabled={loading || !selectedIbomId}
                  icon={<SearchOutlined />}
                >
                  Search
                </Button>,
                <Button
                  type="default"
                  key="rest"
                  disabled={loading || !selectedIbomId}
                  onClick={() => {
                    searchFormRef.current?.resetFields();
                    actionRef.current?.reload();
                  }}
                >
                  Reset
                </Button>,
                <Button
                  type="primary"
                  key="import-price"
                  title="Import prices into IBO without price."
                  // loading={loading}
                  disabled={!selectedIbomId}
                  className="btn-green"
                  icon={<EuroOutlined />}
                  style={{ marginLeft: 24 }}
                  onClick={() => {
                    handleImportPrice();
                  }}
                >
                  Import price
                </Button>,
              ];
            },
          }}
        >
          <ProFormSelect
            name={'ibom_id'}
            showSearch
            label="IBOM"
            options={props.ibomList}
            required
            rules={[{ required: true }]}
            width={120}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange: (value) => {
                if (value) {
                  actionRef.current?.reload();
                  setSelectedIbomId(value);
                }
              },
            }}
          />

          <ProFormText
            name={'name_search'}
            label={'Name'}
            width={180}
            fieldProps={{
              onPressEnter: (e) => actionRef.current?.reload(),
            }}
          />

          <ProFormText
            name={'ean_search'}
            label={'EAN'}
            width={180}
            fieldProps={{
              onPressEnter: (e) => actionRef.current?.reload(),
            }}
          />

          <ProFormCheckbox.Group
            name={'filter_modes'}
            options={[
              { value: 'marriageable', label: 'Marriageable' },
              { value: 'married', label: 'Married' },
              { value: 'matched', label: 'Matched' },
              { value: 'noIboPrice', label: 'No IBO price' },
              { value: 'noSysEan', label: 'No EAN' },
            ]}
            initialValue={[]}
          />

          {/* <ProFormSwitch
            name={'only_existing_ean'}
            label="Only existing EAN"
            initialValue={true}
            fieldProps={{
              onChange: () => {
                searchFormRef.current?.setFieldsValue({ only_not_existing_ean: false });
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormSwitch
            name={'only_not_existing_ean'}
            label="Only not existing EAN"
            initialValue={false}
            fieldProps={{
              onChange: () => {
                searchFormRef.current?.setFieldsValue({ only_existing_ean: false });
                actionRef.current?.reload();
              },
            }}
          />

          {settings?.dbCols?.map((col: string) => {
            if (['ean', 'multi_ean', 'trademark'].includes(col)) {
              return (
                <ProFormText
                  name={col}
                  label={definedColLabelssImported[`col_${col}`]}
                  width={180}
                  fieldProps={{
                    onPressEnter: (e) => actionRef.current?.reload(),
                  }}
                />
              );
            }
            return undefined;
          })} */}
        </ProForm>
      </Card>

      <ProTable<API.ImportedSupplierAndIbo, API.PageParams>
        headerTitle={'Data list'}
        rowKey="id"
        cardProps={{ bodyStyle: { padding: 0 } }}
        revalidateOnFocus={false}
        actionRef={actionRef}
        options={{ fullScreen: true }}
        scroll={{ x: 800, y: 600 }}
        size="small"
        bordered
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 30,
        }}
        tableAlertRender={false}
        search={false}
        request={async (params, sort, filter) => {
          if (searchFormRef.current?.getFieldValue('ibom_id')) {
            setLoading(true);
            return getUploadedDataWithIboList(
              {
                ...params,
                ...searchFormRef.current?.getFieldsValue(),
                id,
              },
              sort,
              filter,
            ).finally(() => setLoading(false));
          } else return new Promise((resolve) => resolve([]));
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(
              (selectedRows || []).map((x) => ({
                id: Number(x.id),
                ibo_id: x.ibo_id,
                xls_id: x.xls_id,
                i_price: x.i_price,
                ean_sys: x.ean_sys,
                ean: x.ean,
                marry_ean_org: x.marry_ean_org,
                marry_xls_id: x.marry_xls_id,
                marry_ibo_id: x.marry_ibo_id,
              })),
            );
          },
        }}
        rowClassName={(record, index) => {
          let cls = '';
          if (Util.safeInt(record.i_qty) > 0 && Util.safeInt(record.qty) == 0) {
            cls = 'bg-light-red2';
          }
          return cls;
        }}
        /* onRow={(record) => ({
onContextMenu: (event) => {
event.preventDefault()
if (!popup.visible) {
  document.addEventListener(`click`, function onClickOutside() {
    setPopup(prev => ({ ...prev, visible: false }));
    document.removeEventListener(`click`, onClickOutside);
  });
} 

setSelectedSysEan(record);

console.log(document.querySelector('.iboViewer'));
const rect = document.querySelector('.iboViewer')?.getBoundingClientRect();
const rect2 = document.querySelector('.iboViewer')?.getClientRects();
console.log(rect);
console.log(rect2?.[0]);




console.log(event.clientX, event.clientY);
 "></div>
setPopup(prev => ({
  ...prev,
  visible: true,
  x: event.clientX - (rect2?.[0]?.x || 0) + 24,
  y: event.clientY - (rect2?.[0]?.y || 0) + 80
}));
}
})} */
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <Space>
              <span>
                Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                &nbsp;entries.
              </span>
              <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
            </Space>
          }
        >
          <Button
            type="primary"
            icon={<ApiOutlined />}
            disabled={!marryPossible}
            onClick={() => {
              const hide = message.loading('Linking 2 EANs between IBO and XLS', 0);
              let ibo, xls;
              if (selectedRowsState[0].ibo_id) {
                ibo = selectedRowsState[0];
                xls = selectedRowsState[1];
              } else {
                ibo = selectedRowsState[1];
                xls = selectedRowsState[0];
              }
              if (isMarryPossible(ibo, xls)) {
                updateMarryTwoEANs(props.initialValue.id, {
                  ean: ibo.ean_sys,
                  imported_ean: xls.ean,
                  xls_id: xls.xls_id,
                  ibo_id: ibo.ibo_id,
                })
                  .then(() => {
                    message.success('Successfully linked!');
                    setSelectedRows([]);
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              } else {
                message.error('Marry is not possible. Please choose another ones!');
              }
            }}
          >
            Marry 2 EANs
          </Button>

          <Button
            type="default"
            danger
            icon={<ApiOutlined />}
            disabled={!unmarigeable}
            onClick={() => {
              const hide = message.loading('Unlinking EANs between IBO and XLS', 0);

              if (unmarigeable) {
                deleteMarriedTwoEANs(
                  props.initialValue.id,
                  (selectedRowsState || []).map((x) => ({
                    ibo_id: x?.ibo_id,
                    xls_id: x?.xls_id,
                  })),
                )
                  .then(() => {
                    message.success('Successfully unlinked!');
                    setSelectedRows([]);
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(() => hide());
              } else {
                message.error('There are unlinked items! Please select correct ones.');
              }
            }}
          >
            Unlinking EANs
          </Button>

          <Popconfirm
            title={
              <>
                IBO prices will be updated. <br />
                <br />
                Are you sure you want to bulk update?
              </>
            }
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={() => {
              const availabeIbos = selectedRowsState
                .filter((x) => !!x.ibo_id && !!x.xls_id)
                .map((x) => ({
                  id: x?.ibo_id,
                  price: x?.i_price,
                }));
              if (!availabeIbos.length) {
                message.error('There are no valid IBO entries in the selection.');
                return;
              }
              const hide = message.loading(`Batch updating IBO prices ...`, 0);
              updateIboAll({
                ibos: availabeIbos,
              })
                .then((res: any) => {
                  setSelectedRows([]);
                  actionRef.current?.reloadAndRest?.();
                  if (res.errors?.length) {
                    message.error(
                      <div>
                        But there were errors:
                        {res.errors.map((e: string) => (
                          <div key={e}>{e}</div>
                        ))}
                      </div>,
                    );
                  }
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type="primary" icon={<SaveOutlined />}>
              Batch IBO Price Update
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}
      <MarryEanModal
        modalVisible={openMarryModal}
        handleModalVisible={setOpenMarryModal}
        initialValue={{
          id: sn(props.initialValue.id),
          xls_id: sn(currentRow?.xls_id),
          imported_ean: currentRow?.ean ?? '',

          ibom_id: sn(selectedIbomId),
          imported_table_name: props.initialValue.table_name,

          // For prefilling
          ean_name: currentRow?.name,
          item_name: '',
          ibom: props.ibomList.find((x) => x.id == selectedIbomId),

          // Qty/Pkg (XLS) & value of column Qty (XLS)
          i_case_qty: currentRow?.i_case_qty,
          i_qty: currentRow?.i_qty,
        }}
        onMarriedCallback={async () => {
          actionRef.current?.reload();
          setOpenMarryModal(false);
        }}
      />
    </Modal>
  );
};

export default IboViewer;
