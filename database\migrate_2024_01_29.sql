ALTER TABLE `xmag_order_extra` ADD COLUMN `detail` TEXT NULL COMMENT 'Extra Detail setting' AFTER `note1_created_by`;

ALTER TABLE `stock_stable_booked` ADD COLUMN `returned` BO<PERSON><PERSON>N default 0 COMMENT 'Returned?' AFTER `picklist_id`;

ALTER TABLE `stock_stable_booked` ADD COLUMN `detail` BO<PERSON>EAN default 0 COMMENT 'Detail in JSON' AFTER `returned`;

ALTER TABLE `xmag_order_item` ADD COLUMN `detail` TEXT NULL COMMENT 'Detail setting in JSON' AFTER `free_shipping`;
