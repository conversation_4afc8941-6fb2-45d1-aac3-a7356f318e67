ALTER TABLE `crm_case`
    ADD COLUMN `reason` VARCHAR (255) NULL COMMENT 'sys_config code: Shipping, Missing Items, Damage items, Other' AFTER `status`,
    ADD COLUMN `reason_text` TEXT NULL COMMENT 'Reason detail notes' AFTER `reason`,
    ADD INDEX `IDX_crm_case_reason` (`reason`);

ALTER TABLE `crm_case` CHANGE `reason_text` `reason_text` LONGTEXT NULL COMMENT 'Reason detail notes';


INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('EMAIL_BCC', 'Email Config', NULL, 'BCC Emails', 'BCC emails. Comma separated value supported.');


INSERT INTO `sys_dict` (`code`, `type`, `value`, `label`, `desc`)
VALUES ('MAG_ADMIN_URL_INVOICE', 'Magento Admin URL', 'https://foodstore.one/admin_v7l7uf/', 'Invoice URL in Magento', '');
