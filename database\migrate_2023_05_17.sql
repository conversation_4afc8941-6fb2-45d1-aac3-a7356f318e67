ALTER TABLE `email_account`
    ADD COLUMN `sender_name` VA<PERSON><PERSON><PERSON>(255) NULL COMMENT 'Default name used in sending by SMTP' AFTER `status`;

ALTER TABLE `email`
    ADD COLUMN `attachments`  longtext     NULL COMMENT 'Attachments in JSON' AFTER `attachments_ids`,
    ADD COLUMN `content_type` varchar(255) COMMENT 'Content type' AFTER `bcc`,
    ADD COLUMN `mime_version` varchar(255) NULL COMMENT 'Mime version' AFTER `bcc`
;

ALTER TABLE `email`
    DROP COLUMN attachments_ids;

ALTER TABLE `email`
    ADD COLUMN `created_by` int NULL COMMENT 'Creator' AFTER `created_on`
;

ALTER TABLE `xmag_order`
    ADD FULLTEXT INDEX `FT_xmag_order_name_address` (`customer_firstname`, `customer_lastname`, `sa_firstname`, `sa_lastname`, `sa_email`,
                                                     `sa_country_code`, `sa_city`, `sa_zip`, `sa_street`, `sa_company`, `detail`);





