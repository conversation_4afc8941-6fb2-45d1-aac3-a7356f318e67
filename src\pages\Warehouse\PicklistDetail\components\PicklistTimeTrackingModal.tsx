import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { Alert, Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import Util from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import type { DefaultOptionType } from 'antd/lib/select';
import {
  createPicklistTimeTrackingList,
  getPicklistTimeTrackingList,
} from '@/services/foodstore-one/Warehouse/picklist-time-tracking';

type RecordType = {
  key?: React.Key;
  type_str?: string;
} & API.WarehousePicklistTimeTrack;

export type FormValueType = Partial<API.WarehousePicklistTimeTrack>;

export type PicklistTimeTrackingModalProps = {
  initialValues?: Partial<API.WarehousePicklist> & DefaultOptionType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const PicklistTimeTrackingModal: React.FC<PicklistTimeTrackingModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  const { initialValues: picklist, modalVisible, handleModalVisible } = props;

  const [loading, setLoading] = useState(false);

  const editableFormRef = useRef<ProFormInstance>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  const columns: ProColumns<Partial<RecordType>>[] = useMemo(
    () => [
      {
        title: '',
        dataIndex: ['type_str'],
        editable: false,
        ellipsis: true,
        width: 50,
      },
      {
        title: 'Start Time',
        dataIndex: 'start_datetime',
        width: 120,
        valueType: 'time',
        formItemProps: { style: { marginBottom: 0 } },
        fieldProps: { size: 'small' },
      },
      {
        title: 'End Time',
        dataIndex: 'end_datetime',
        width: 120,
        valueType: 'time',
        formItemProps: { style: { marginBottom: 0 } },
        fieldProps: { size: 'small' },
      },
      {
        title: 'Minute',
        dataIndex: ['minute'],
        width: 120,
        renderFormItem: (item, { record }) => {
          return (
            <SProFormDigit
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{ precision: 0, size: 'small' }}
              placeholder=""
            />
          );
        },
      },
      {
        title: 'Qty of Employee',
        dataIndex: ['qty_employee'],
        width: 120,
        renderFormItem: (item, { record }) => {
          return (
            <SProFormDigit
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{ size: 'small' }}
              placeholder=""
            />
          );
        },
      },
    ],
    [],
  );

  useEffect(() => {
    if (modalVisible && picklist?.id) {
      setLoading(true);
      getPicklistTimeTrackingList({ picklist_id: picklist?.id })
        .then((res) => {
          const newDs: RecordType[] = [];
          const types = [1, 2, 3];
          const typesKv: any = { 1: 'Pick', 2: 'Print', 3: 'Pack' };
          for (const type of types) {
            const row: RecordType = { type: type, key: type, id: type, type_str: typesKv[type] };
            row.picklist_id = picklist.id;
            const dbRow = res.data.find((x) => x.type == type && x.picklist_id == picklist.id);
            if (dbRow) {
              row.key = dbRow.id;
              row.id = dbRow.id;
              row.start_datetime = dbRow.start_datetime;
              row.end_datetime = dbRow.end_datetime;
              row.minute = dbRow.minute;
              row.qty_employee = dbRow.qty_employee;
            }

            newDs.push(row);
          }

          setDataSource(newDs);
          const keys: React.Key[] = [];
          for (const x of newDs) {
            keys.push(x.id as React.Key);
          }
          setEditableRowKeys(keys);
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    }
  }, [modalVisible, picklist?.id]);

  return (
    <ModalForm<FormValueType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>Create / Update Tracking List</span>
          <span>{picklist?.label}</span>
        </Space>
      }
      width={700}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 5 }}
      labelAlign="left"
      wrapperCol={{ span: 19 }}
      formRef={formRef}
      onFinish={async (value) => {
        const dataByRef = editableFormRef.current?.getFieldsValue();

        if (!formRef.current) return;

        const rows: API.WarehousePicklistTimeTrack[] = [];
        dataSource.forEach((x) => {
          rows.push({
            ...x,
            picklist_id: picklist?.id,
          });
        });

        console.log('rows  =', rows);
        return;

        setLoading(true);

        createPicklistTimeTrackingList({ ...value, rows, picklist_id: picklist?.id })
          .then((res) => {
            props.handleModalVisible(false);
            props.onSubmit?.(value);
            message.success('Saved tracking list.', 3);
          })
          .catch(Util.error)
          .finally(() => {
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Save & Close' },
        onReset(value) {
          props.handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading, loading: loading },
        submitButtonProps: { disabled: loading, loading: loading },
      }}
    >
      <Alert message="Coming soon!" />
      <EditableProTable
        rowKey={'key'}
        headerTitle={'Time Tracking List'}
        editableFormRef={editableFormRef}
        columns={columns}
        dataSource={dataSource}
        value={dataSource}
        controlled
        onChange={setDataSource}
        search={false}
        toolbar={{
          search: false,
          actions: undefined,
          menu: undefined,
        }}
        scroll={{ x: '100%' }}
        pagination={false}
        className="w-full"
        rowClassName={(record) => {
          // cheapest price
          /* const cheapestPrice = sn(record.price_xls_org);
          if (cheapestPrice <= 0) return '';
          for (const x of dataSource) {
            const p = sn(x.price_xls_org);
            if (!p) continue;
            if (p < cheapestPrice) {
              return '';
            }
          } */
          return '';
        }}
        size="small"
        recordCreatorProps={false}
        style={{ marginBottom: 16 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        editable={{
          type: 'multiple',
          editableKeys,
          onChange: setEditableRowKeys,
          deletePopconfirmMessage: 'Are you sure you want to delete?',
          onlyAddOneLineAlertMessage: 'You can only add one.',
          onValuesChange(record, recordList) {
            setDataSource(recordList);
          },
        }}
        loading={loading}
        columnEmptyText=""
      />
    </ModalForm>
  );
};

export default PicklistTimeTrackingModal;
