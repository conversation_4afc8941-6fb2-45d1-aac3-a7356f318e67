import type { Identifier, XYCoord } from 'dnd-core';
import type { FC } from 'react';
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Image, Space } from 'antd';
import { inCsvStr } from '@/util';

export const PRODUCT_IMAGE_TYPES = [
  {
    value: 'image',
    label: 'BE',
  },
  {
    value: 'small_image',
    label: 'SM',
  },
  {
    value: 'thumbnail',
    label: 'TH',
  },
];

export const ItemTypes = {
  CARD: 'card',
};

const style = {
  backgroundColor: 'white',
  cursor: 'move',
  padding: '3px',
  width: 88,
  display: 'inline-block',
  margin: '4px 4px',
  overflow: 'hidden',
};

export interface ImageCardProps {
  id: any;
  file: API.File;
  index: number;
  isSingleEan: boolean;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  dropFileCallback: (item: DragItem) => void;
  updateTypeCallback: (file: API.File, newTypes: string, isRemove: boolean) => void;
}

export type DragItem = {
  index: number;
  orgIndex: number;
  id: string;
  type: string;
} & Partial<API.File>;

export const ImageCard: FC<ImageCardProps> = ({
  id,
  file,
  index,
  isSingleEan,
  moveCard,
  dropFileCallback,
  updateTypeCallback,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: Identifier | null }>({
    accept: ItemTypes.CARD,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      // const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      // const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top
      const hoverClientX = (clientOffset as XYCoord).x - hoverBoundingRect.left;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      // if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return;
      }

      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
    drop(item, monitor) {
      // console.log(' -------- dropped ----------');
      // console.log('Index', index, 'Item Index', item.index, monitor.getItem());
      dropFileCallback(monitor.getItem());
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.CARD,
    item: () => {
      return { id, index, orgIndex: index, file };
    },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0 : 1;
  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{ ...style, opacity }}
      data-handler-id={handlerId}
      className={`image-card${/* isSingleEan ||  */ file.pivot.is_parent_file ? ' parent-img' : ''}`}
    >
      <Image
        src={file.thumb_url}
        preview={{
          src: file.url,
        }}
        width={80}
        height={80}
      />
      <Space className="card-actions text-sm" size={4}>
        {PRODUCT_IMAGE_TYPES.map((t) => {
          const exists = inCsvStr(t.value, file.pivot.types);
          return (
            <div
              key={t.value}
              className={`type-action${exists ? ' active' : ''}`}
              onClick={() => {
                updateTypeCallback(file, t.value, exists);
              }}
            >
              {t.label}
            </div>
          );
        })}
      </Space>
      {/* <span>{`${file.pivot.position}`}</span> */}
    </div>
  );
};
