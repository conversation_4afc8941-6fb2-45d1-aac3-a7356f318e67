import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Col, Modal, Popconfirm, Row, Space, Typography } from 'antd';
import Util, { ni, sn } from '@/util';

import styles from './CreateOrUpdateQtyShippedModalForm2.less';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { getOfferItemIboPrePackReadyMapList } from '@/services/foodstore-one/Offer/offer-item-packed-ready';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import CreateForm from '../../OfferItemShippedList/components/CreateForm';
import UpdateForm from '../../OfferItemShippedList/components/UpdateForm';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { deleteOfferItemShipped } from '@/services/foodstore-one/Offer/offer-item-shipped';

export type FormValueType = Partial<API.OfferItemShipped>;

export type CreateOrUpdateQtyShippedModalForm2Props = {
  itemEan: Partial<API.Ean>;
  offerItem: Partial<API.OfferItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  loadParentList?: () => void;
  onSubmit?: (formData: API.OfferItemShipped) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
};

const CreateOrUpdateQtyShippedModalForm2: React.FC<CreateOrUpdateQtyShippedModalForm2Props> = ({
  itemEan,
  offerItem,
  modalVisible,
  handleModalVisible,
  loadParentList,
}) => {
  const actionRef = useRef<ActionType>();

  const [listReloadTick, setListReloadTick] = useState<number>(0);

  const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.OfferItemIboPrePackReadyMap>();

  // sub row: shipped
  const [subCurrentRow, setSubCurrentRow] = useState<API.OfferItemShipped>();
  const [openUpdateModal, setOpenUpdateModal] = useState<boolean>(false);

  useEffect(() => {
    if (modalVisible) {
      actionRef.current?.reload();
      // reload child table.
      setListReloadTick((prev) => prev + 1);
    }
  }, [modalVisible]);

  const expandedRowRender = (record: API.OfferItemIboPrePackReadyMap) => {
    return (
      <div>
        <ProTable<API.OfferItemShipped>
          cardProps={{ bodyStyle: { padding: 0 } }}
          rowKey={'id'}
          columns={[
            { title: 'WA No', dataIndex: ['wa_no'], width: 80 },
            {
              title: 'WA. Date',
              dataIndex: 'wa_date',
              valueType: 'date',
              search: false,
              width: 90,
              align: 'center',
              showSorterTooltip: false,
              render: (__, record) => Util.dtToDMY(record.wa_date),
            },
            {
              title: 'Cases',
              dataIndex: ['qty'],
              width: 55,
              align: 'right',
              render: (__, record) => {
                return ni(record.qty);
              },
            },
            {
              title: 'Qty (pcs)',
              dataIndex: ['qty_pcs'],
              width: 65,
              align: 'right',
              render: (__, record) => {
                return ni(sn(record?.case_qty) * sn(record?.qty));
              },
            },
            {
              title: 'Comment',
              dataIndex: 'comment',
              search: false,
              className: 'text-sm',
              width: 190,
            },
            {
              title: 'Updated on',
              dataIndex: ['updated_on'],
              width: 110,
              className: 'text-sm c-grey',
              render: (dom, recordIn) => Util.dtToDMYHHMM(recordIn.updated_on),
            },
            {
              title: '',
              dataIndex: 'option',
              valueType: 'option',
              width: 70,
              fixed: 'right',
              render: (_, record) => {
                return (
                  <Space size={6}>
                    <Button
                      type="primary"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => {
                        setSubCurrentRow(record);
                        setOpenUpdateModal(true);
                      }}
                    />
                    <Popconfirm
                      title={<>Are you sure you want to delete?</>}
                      okText="Yes"
                      cancelText="No"
                      overlayStyle={{ maxWidth: 350 }}
                      onConfirm={async () => {
                        if (record.id) {
                          await deleteOfferItemShipped(record.id);
                          actionRef.current?.reloadAndRest?.();
                        }
                      }}
                    >
                      <Button type="primary" size="small" danger ghost icon={<DeleteOutlined />} />
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
          style={{ width: 600 }}
          headerTitle={false}
          search={false}
          options={false}
          pagination={false}
          size="small"
          dataSource={record.offer_item_shipped_list ?? []}
          columnEmptyText={''}
          locale={{ emptyText: <></> }}
        />
      </div>
    );
  };

  const columns: ProColumns<API.OfferItemIboPrePackReadyMap>[] = [
    {
      title: 'IBO Pre',
      dataIndex: ['ibo_pre'],
      width: 90,
      render(__, entity) {
        return (
          <>
            <Typography.Link
              href={`/ibo/ibo-pre?ibo_pre_management_id=${entity.ibo_pre?.ibo_pre_management_id}`}
              target="_blank"
            >
              {`#${entity.ibo_pre?.ibo_pre_management_id}`}
            </Typography.Link>
            <span>{` / ${entity.ibo_pre?.id}`}</span>
          </>
        );
      },
    },
    {
      title: 'Case Qty',
      dataIndex: ['case_qty'],
      width: 60,
      align: 'right',
      className: 'bl2',
      render: (__, record) => {
        return ni(record.case_qty);
      },
    },
    {
      title: 'Cases',
      dataIndex: ['qty'],
      width: 60,
      align: 'right',
      render: (__, record) => {
        return ni(record.qty);
      },
    },

    {
      title: 'Qty (pcs)',
      dataIndex: ['qty_pcs'],
      width: 65,
      align: 'right',
      render: (__, record) => {
        return ni(sn(record?.case_qty) * sn(record?.qty));
      },
    },
    {
      title: 'Exp. Date',
      dataIndex: 'exp_date',
      valueType: 'date',
      search: false,
      width: 90,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMY(record.exp_date),
    },
    {
      title: 'Created on',
      dataIndex: 'updated_on',
      valueType: 'date',
      search: false,
      // className: 'text-sm',
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (__, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 80,
      render: (_, record) => {
        return (
          <Button
            type="primary"
            onClick={() => {
              setCurrentRow(record);
              setOpenCreateModal(true);
            }}
            disabled={sn(record.qty) <= sn(record.offer_item_shipped_qty)}
          >
            Send
          </Button>
        );
      },
    },
    {
      title: 'Shipped Cases',
      dataIndex: 'offer_item_shipped_qty',
      className: 'bl-2',
      width: 80,
      align: 'right',
      render: (__, record) => ni(record.offer_item_shipped_qty),
    },
    {
      title: 'Shipped Detail',
      dataIndex: 'offer_item_shipped_list',
      width: 550,
      className: 'p-0',
      render(__, entity) {
        return entity?.offer_item_shipped_list?.length ? expandedRowRender(entity) : null;
      },
    },
  ];

  return (
    <Modal
      title={
        <Row gutter={16}>
          <Col>Add Shipped Qty</Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.ean || '',
                tooltips: 'Copy EAN ' + (itemEan?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.ean || ''}
            </Typography.Paragraph>
          </Col>
          <Col>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.sku || '',
                tooltips: 'Copy SKU ' + (itemEan?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.sku || ''}
            </Typography.Paragraph>
          </Col>
          <Col>{`Qty / Case: ${itemEan.attr_case_qty}`}</Col>

          <Col offset={2} className="text-right" style={{ fontSize: 16 }}>
            <span>Offer: </span>
            <span style={{ fontWeight: 'bold' }}>
              {offerItem.case_qty == 1
                ? ni(offerItem.qty, true) + ' pcs'
                : `${ni(offerItem.qty, true)} x ${ni(offerItem.case_qty, true)} = ${ni(
                    sn(offerItem.qty) * sn(offerItem.case_qty),
                    true,
                  )} pcs`}
            </span>
          </Col>
          <Col className="text-right" style={{ fontSize: 16 }}>
            <span>Packed: </span>
            <span style={{ fontWeight: 'bold' }}>{ni(offerItem.packed_ready_qty_pcs, true)} pcs</span>
          </Col>

          <Col style={{ fontSize: 16 }} className="c-orange">
            <span>Shipped: </span>
            <span style={{ fontWeight: 'bold' }}>{ni(offerItem.shipped_qty_pcs, true)} pcs</span>
          </Col>
        </Row>
      }
      width="1400px"
      open={modalVisible}
      onOk={() => handleModalVisible(false)}
      onCancel={() => handleModalVisible(false)}
      footer={false}
      className={styles.createOrUpdateQtyShippedModalForm2}
      bodyStyle={{ paddingTop: 0 }}
    >
      <ProTable<API.OfferItemIboPrePackReadyMap, API.PageParams>
        headerTitle={'Packed & Shipped Qty List'}
        actionRef={actionRef}
        rowKey={(entity) => `${entity.offer_item}_${entity.ibo_pre_id}_${entity.ean_id}_${entity.exp_date}`}
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: false, setting: false }}
        size="large"
        sticky
        search={false}
        scroll={{ x: 800 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        toolBarRender={() => []}
        request={(params, sort, filter) => {
          // const searchValues = searchFormRef.current?.getFieldsValue();
          const searchValues = {};

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            offer_item_id: offerItem?.id,
            with: 'iboPre,itemEan,shippedDetail',
          };

          return getOfferItemIboPrePackReadyMapList(newParam, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record?.item_ean?.is_single ? 'row-single' : 'row-multi';
          return cls;
        }}
        rowSelection={false}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />

      <CreateForm
        initialValues={currentRow}
        modalVisible={openCreateModal}
        handleModalVisible={setOpenCreateModal}
        onSubmit={(values) => {
          actionRef.current?.reload();
          setListReloadTick((prev) => prev + 1);
          loadParentList?.();
        }}
      />

      <UpdateForm
        initialValues={subCurrentRow}
        modalVisible={openUpdateModal}
        handleModalVisible={setOpenUpdateModal}
        onSubmit={(values) => {
          actionRef.current?.reload();
          setListReloadTick((prev) => prev + 1);
          loadParentList?.();
        }}
      />

      {/* <Col flex="auto" style={{ paddingLeft: 24 }}>
        <Row>
          <Col span={7}>
            <Button
              type="primary"
              size="large"
              className="btn-green"
              onClick={async () => {
                const listByRef = listActionRef.current?.getList();

                const rows: any = [];
                listByRef?.forEach((row) => {
                  if (row.qty && row.exp_date && row.wa_no && row.wa_date) {
                    rows.push(row);
                  }
                });

                if (rows.length < 1) {
                  message.info('Nothing to save! Please fill data correctly.');
                  return;
                }

                const hide = message.loading('Adding a Shipment...', 0);

                const success = await addOfferItemShippedBulk(rows).catch(Util.error).finally(hide);

                if (success) {
                  message.success('Shipped Qty added successfully.');
                  loadOfferItem?.();
                  setListReloadTick((prev) => prev + 1);
                }
              }}
            >
              Save
            </Button>
          </Col>
        </Row>
      </Col> */}
      {/* <OfferItemShippedList offer_item_id={offerItem.id} item_id={offerItem.item_id} reloadTick={listReloadTick} /> */}
    </Modal>
  );
};

export default CreateOrUpdateQtyShippedModalForm2;
