import {
  CheckCircleOutlined,
  CloseOutlined,
  CommentOutlined,
  DeleteOutlined,
  EuroCircleFilled,
  FileExcelOutlined,
  FilePdfOutlined,
  HighlightOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  MinusCircleOutlined,
  MinusOutlined,
  MobileOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  TableOutlined,
  TabletFilled,
} from '@ant-design/icons';
import { Button, message, Drawer, Typography, Card, Popconfirm, Popover, Row, Col, Space, Image } from 'antd';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { nf2, ni, skuToItemId, sn, sShortImportDbTableName } from '@/util';
import {
  getOfferItemList,
  deleteOfferItem,
  exportOfferItemList,
  updateOfferItem,
  updateItemEanWishQty,
  exportOfferItemListWithStocks,
  assignIboPre,
  exportOfferItemListWithIboPres,
  addToIboPre,
} from '@/services/foodstore-one/Offer/offer-item';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode, OfferIboStatus, OfferIboStatusKv } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormCheckbox,
  ProFormDependency,
  ProFormGroup,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-form';
import useOfferOptions from '@/hooks/BasicData/useOfferOptions';
import SProFormDigit from '@/components/SProFormDigit';
import { OfferIboStatusComp } from '../OfferList';
import EditableCell from '@/components/EditableCell';
import { getOfferList, updateOffer } from '@/services/foodstore-one/Offer/offer';
import ExportPdfSettingFormModal from './components/ExportPdfSettingFormModal';
import { useLocation, useModel } from 'umi';
import CreateForm from './components/CreateForm';
import useIboPreManagementOptions from '@/hooks/BasicData/useIboPreManagementOptions';
import useTrademarkFormFilter from '@/pages/Item/EanList/hooks/useTrademarkFormFilter';
import useWaNoOptions from '@/hooks/BasicData/useWaNoOptions';

import IboPreListModal from '@/pages/IBO/IboPreList/IboPreListModal';
import { DefaultOptionType } from 'antd/lib/select';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import { calcCheapestXlsPrice } from '@/pages/Item/EanList/EanAllPrices';
import ExportPackingPdfSettingFormModal from './components/ExportPackingPdfSettingFormModal';
import CreateOrUpdateQtyShippedModalForm2 from './components/CreateOrUpdateQtyShippedModalForm2';
import CreateBulkForm from './components/CreateBulkForm';
import UpdateNoteForm from './components/UpdateNoteForm';
import OfferItemPackedReadyListExtModal from './components/OfferItemPackedReadyListExtModal';
import OfferItemPackedReadyListEditableModal from './components/OfferItemPackedReadyListEditableModal';

export type SearchFormValueType = Partial<API.OfferItem> & { includeSubTable?: boolean; trademark?: DefaultOptionType };

type ExportForm = {
  include_bp_xls?: boolean;
  percentage?: number;
};

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.OfferItem[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteOfferItem(selectedRows.map((row) => row.id).join(','));
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const OfferItem: React.FC = () => {
  const { getDictByCode } = useModel('app-settings');

  const location: any = useLocation();
  const offerIdInUrl = location.query?.offer_id;

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [bulkCreateModalVisible, handleBulkCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [openIboPreListModal, setOpenIboPreListModal] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<API.OfferItem>();
  const [selectedRowsState, setSelectedRows] = useState<API.OfferItem[]>([]);
  // Notes of current Offer Item
  const [openUpdateNoteForm, setOpenUpdateNoteForm] = useState(false);

  const offerFilterExtra = useMemo(() => {
    return { nin_ibo_status: [OfferIboStatus.Closed] };
  }, []);
  const { formElements, offer, setOffer, searchOfferOptions } = useOfferOptions(offerFilterExtra, searchFormRef);

  // Offer default
  // const [offerPercentage, setOfferPercentage] = useState<number>();

  // Trademark filter section
  const trademarkCallbackHandler = useCallback((cbMode?: 'reload') => {
    if (cbMode == 'reload') actionRef.current?.reload();
  }, []);

  const { formElements: formElementsTrademark, searchTrademarks } = useTrademarkFormFilter(
    searchFormRef?.current,
    trademarkCallbackHandler,
    {
      acMode: 'forOfferItem',
      offer_id: offer?.id,
      includeQtySuffix: true,
      width: 'sm',
    },
  );

  // Export XLS Option Form
  const exportFormRef = useRef<ProFormInstance<ExportForm>>();
  const [openExportForm, setOpenExportForm] = useState<boolean>(false);
  // WA No list from offer_item_shipped table.
  const useWaNoOptionsParams = useMemo(() => {
    return { offer_id: offer?.id };
  }, []);
  const { formElements: formElementsWaNo } = useWaNoOptions(useWaNoOptionsParams, exportFormRef);

  // Export PDF Option Form
  const [openExportPdfForm, setOpenExportPdfForm] = useState<boolean>(false);
  // Export Packing PDF Option Form
  const [openExportPackingPdfForm, setOpenExportPackingPdfForm] = useState<boolean>(false);

  // IBO Pre selection modal
  const [openIboPreSelectionForm, setOpenIboPreSelectionForm] = useState<boolean>(false);
  const { iboPreManagementOptions, searchIboPreManagementOptions } = useIboPreManagementOptions();

  // Stock Qty modal
  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);

  // New feature with editable
  const [openOfferItemPackedReadyListEditableModal, setOpenOfferItemPackedReadyListEditableModal] =
    useState<boolean>(false);
  const [openOfferItemPackedReadyListExtModal, setOpenOfferItemPackedReadyListExtModal] = useState<boolean>(false);

  // offer detail
  const [offerDetail, setOfferDetail] = useState<API.Offer>();

  // OfferItem Shipped List Modal
  const [openCreateOrUpdateQtyShippedForm, setOpenCreateOrUpdateQtyShippedForm] = useState<boolean>(false);

  const [xlsImports, setXlsImports] = useState<API.Import[]>([]);

  // Percentage
  const [percent, setPercent] = useState<number | null>();
  useEffect(() => {
    setPercent(offerDetail?.percentage ?? offer?.percentage ?? getDictByCode(DictCode.OFFER_DEFAULT_PERCENTAGE));
  }, [offer?.percentage, offerDetail?.percentage, getDictByCode]);

  const columns: ProColumns<API.OfferItem>[] = [
    /* {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) * (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    }, */
    /* {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      hideInForm: true,
      defaultSortOrder: 'descend',
      width: 100,
      renderText: (val: string) => `${val}`,
    }, */

    /* {
      title: 'EXPAND_COLUMN',
      dataIndex: 'EXPAND_COLUMN',
      // valueType: 'EXPAND_COLUMN',
      width: 40,
    }, */
    {
      title: '',
      dataIndex: ['ibo_pre_mapped_count'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      width: 20,
      render(__, entity) {
        return entity.ibo_pre_mapped_count ? (
          <LinkOutlined title="There is an IBO Pre at list" style={{ color: 'green' }} />
        ) : null;
      },
    },
    {
      title: 'Image',
      dataIndex: ['item_ean', 'files', 0, 'url'],
      valueType: 'image',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      sorter: false,
      width: 80,
      render: (dom, record) => {
        const files = record.item_ean?.files;
        return files ? (
          <Image.PreviewGroup>
            {files &&
              files.map((file, ind) => (
                <Image
                  key={file.id}
                  src={file.thumb_url}
                  preview={{
                    src: file.url,
                  }}
                  wrapperStyle={{ display: ind > 0 ? 'none' : 'inline-block' }}
                  width={40}
                />
              ))}
          </Image.PreviewGroup>
        ) : (
          <></>
        );
      },
    },
    {
      title: 'Offer No',
      dataIndex: ['offer', 'offer_no'],
      width: 50,
      fixed: 'left',
      sorter: true,
      defaultSortOrder: 'descend',
    },
    {
      title: 'SKU',
      dataIndex: ['sku'],
      sorter: true,
      copyable: true,
      width: 120,
      fixed: 'left',
      render: (dom, recordIn) => {
        return (
          <Row>
            <Col flex="auto">
              <Typography.Link
                href={`/item/ean-all-summary?sku=${skuToItemId(recordIn.sku)}_`}
                target="_blank"
                copyable
              >
                {recordIn.sku}
              </Typography.Link>
            </Col>
            <Col flex="30px">
              <a
                href={`/item/ean-all-prices?sku=${skuToItemId(recordIn.sku)}_`}
                target="_blank"
                title="Open EAN price page on new tab."
                className="text-sm"
                rel="noreferrer"
              >
                <LinkOutlined />
              </a>
              <a
                href="javascript: void()"
                type="link"
                title="Open Last Packed Qty List & Disabled EANs List..."
                onClick={(e) => {
                  e.preventDefault();
                  setCurrentRow({ ...recordIn });
                  setOpenOfferItemPackedReadyListExtModal(true);
                }}
                style={{ marginLeft: 6 }}
              >
                <TableOutlined className="text-sm cursor-pointer" />
              </a>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'EAN',
      dataIndex: ['ean'],
      sorter: true,
      copyable: true,
      width: 130,
    },
    {
      title: 'EAN Name',
      dataIndex: ['item_ean', 'ean_text_de', 'name'],
      ellipsis: true,
      width: 290,
      tooltip: 'Orange color indicates the inherited value from its item.',
      render: (dom, record) => {
        const eanName = record?.item_ean?.ean_text_de?.name;
        return (
          <Typography.Text type={eanName ? undefined : 'warning'}>
            {eanName ?? record?.item_ean?.item?.name ?? <CloseOutlined style={{ color: '#cc2200' }} />}
          </Typography.Text>
        );
      },
    },
    {
      title: 'Trademark (sys)',
      dataIndex: ['item_ean', 'item', 'trademark', 'name'],
      width: 120,
    },
    {
      title: '',
      dataIndex: ['note'],
      width: 20,
      render(dom, entity) {
        return (
          <CommentOutlined
            className="cursor-pointer"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setCurrentRow(entity);
              setOpenUpdateNoteForm(true);
            }}
            title={entity.note}
            style={{ color: entity.note ? 'red' : 'gray', position: 'absolute', left: 2, top: 2 }}
          />
        );
      },
    },

    /* {
      title: 'Packed Qty (pcs)',
      dataIndex: ['packed_ready_qty_pcs'],
      width: 60,
      align: 'right',
      tooltip: 'Please click to view details.',
      className: 'cursor-pointer',
      render(__, entity) {
        return ni(entity.packed_ready_qty_pcs);
      },
      onCell: (record) => {
        return {
          onClick: () => {
            setCurrentRow({ ...record });
            setOpenOfferItemPackedReadyListModal(true);
          },
        };
      },
    }, */
    {
      title: 'Packed Qty (pcs)',
      dataIndex: ['packed_ready_qty_pcs2'],
      width: 60,
      align: 'right',
      tooltip: 'Please click to view details.',
      className: 'cursor-pointer',
      render(__, entity) {
        return ni(entity.packed_ready_qty_pcs);
      },
      onCell: (record) => {
        return {
          onClick: () => {
            setCurrentRow({ ...record });
            setOpenOfferItemPackedReadyListEditableModal(true);
          },
        };
      },
    },
    {
      title: 'Shipped Qty (pcs)',
      dataIndex: ['shipped_qty_pcs'],
      width: 60,
      align: 'right',
      tooltip: 'Please click to view details.',
      className: 'cursor-pointer',
      render(__, entity) {
        return ni(entity.shipped_qty_pcs);
      },
      onCell: (record) => {
        return {
          onClick: () => {
            setCurrentRow({ ...record });
            setOpenCreateOrUpdateQtyShippedForm(true);
          },
        };
      },
    },
    {
      title: 'Stock',
      dataIndex: ['total_piece_qty'],
      sorter: true,
      align: 'right',
      ellipsis: true,
      width: 80,
      tooltip: 'Please click to view stock details.',
      showSorterTooltip: false,
      className: 'cursor-pointer',
      render: (dom, record) => {
        return ni(record.item_ean?.parent_stock_stables_sum_total_piece_qty);
      },
      onCell: (record) => {
        return {
          onClick: () => {
            setCurrentRow({ ...record });
            handleQtyModalVisible(true);
          },
        };
      },
    },
    {
      title: '',
      dataIndex: 'show_xls_prices',
      width: 20,
      className: 'bl2',
      render(__, entity) {
        return entity.item_ean ? (
          <Popover
            title="EAN Prices Detail"
            content={() => {
              const xlsColumns: ProColumns<API.Ean>[] = xlsImports?.map((x, ind) => {
                return {
                  title: (
                    <>
                      <div style={{ textAlign: 'left' }}>{x.supplier?.name}</div>
                      <div style={{ textAlign: 'left', fontSize: 10 }}>
                        {x.supplier_add ? x.supplier_add : sShortImportDbTableName(x.table_name, true)}
                      </div>
                    </>
                  ),
                  dataIndex: [`xls_bp_${x.id}`],
                  sorter: false,
                  align: 'right',
                  width: 80,
                  className: ind == 0 ? 'bl2' : '',
                  render: (___, r) => {
                    const item_ean: any = r ?? {};
                    const price = item_ean[`xls_bp_${x.id}`] ?? item_ean[`xls_bp2_${x.id}`];

                    return (
                      <>
                        <div style={{ lineHeight: 1.5, minHeight: 12 }}>{nf2(price)}</div>
                      </>
                    );
                  },
                  onCell: (r) => {
                    const item_ean: any = r ?? {};
                    const price = item_ean[`xls_bp_${x.id}`] ?? item_ean[`xls_bp2_${x.id}`] ?? 0;

                    const arr = calcCheapestXlsPrice(item_ean ?? ({} as any), xlsImports);
                    const cheapestPrice = arr[0];
                    let cls = '';
                    if (cheapestPrice == price && price > 0) {
                      cls = 'bg-green3';
                    }
                    return {
                      className: cls,
                    };
                  },
                } as ProColumnType<API.MagQuoteItem>;
              });

              return (
                <ProTable<API.Ean>
                  cardProps={{ bodyStyle: { padding: 0 } }}
                  bordered
                  columns={[
                    {
                      title: 'SKU',
                      dataIndex: 'sku',
                      width: 130,
                      render: (dom, recordIn) => {
                        return (
                          <Row>
                            <Col flex="auto">
                              <Typography.Link
                                href={`/item/ean-all-summary?sku=${skuToItemId(recordIn.sku)}_`}
                                target="_blank"
                                copyable
                              >
                                {recordIn.sku}
                              </Typography.Link>
                            </Col>
                            <Col flex="30px">
                              <a
                                href={`/item/ean-all-prices?sku=${skuToItemId(recordIn.sku)}_`}
                                target="_blank"
                                title="Open EAN price page on new tab."
                                className="text-sm"
                                rel="noreferrer"
                              >
                                <LinkOutlined />
                              </a>
                            </Col>
                          </Row>
                        );
                      },
                    },
                    {
                      title: 'Price Stable',
                      dataIndex: ['ean_price_stable', 'cur_import', 'supplier'],
                      width: 60,
                      align: 'right',
                      tooltip: 'Price Stable & Supplier',
                      className: 'bl2',
                      render(__, record) {
                        const priceStable = record.ean_price_stable;
                        return priceStable ? (
                          <Space
                            style={{ lineHeight: 1 }}
                            direction="vertical"
                            size={6}
                            title={
                              priceStable.cur_import
                                ? `${priceStable.cur_import_id} | ${priceStable.cur_import?.table_name}`
                                : ''
                            }
                          >
                            <div className="c-green-dark">{nf2(priceStable?.cur_price)}</div>
                            <div className="c-grey text-sm">
                              &nbsp;{priceStable.cur_import?.supplier_add ? '' : priceStable.cur_import?.supplier?.name}
                            </div>
                          </Space>
                        ) : null;
                      },
                    },
                    {
                      title: 'SUPPLIER_ADD',
                      dataIndex: ['ean_price_stable', 'cur_import', 'supplier_add'],
                      width: 60,
                    },
                    {
                      title: 'Min Buying',
                      dataIndex: ['ibos_item_min_price'],
                      align: 'right',
                      width: 80,
                      render: (dom, recordIn) => nf2(recordIn?.ibos_item_min_price),
                    },
                    ...xlsColumns,
                  ]}
                  style={{ width: 900 }}
                  rowKey="item_id"
                  headerTitle={false}
                  search={false}
                  options={false}
                  pagination={false}
                  size="small"
                  dataSource={entity.item_ean ? [entity.item_ean] : []}
                  columnEmptyText={''}
                  rowClassName={(recordIn) => (recordIn?.is_single ? 'row-single' : 'row-multi')}
                />
              );
            }}
          >
            <EuroCircleFilled className="cursor-pointer c-grey" />
          </Popover>
        ) : null;
      },
    },
    {
      title: 'Price Stable',
      dataIndex: ['ean_price_stable', 'cur_import', 'supplier'],
      width: 60,
      align: 'right',
      tooltip: 'Price Stable & Supplier',
      render(__, record) {
        const priceStable = record.item_ean?.ean_price_stable;
        return priceStable ? (
          <Space
            style={{ lineHeight: 1 }}
            direction="vertical"
            size={6}
            title={priceStable.cur_import ? `${priceStable.cur_import_id} | ${priceStable.cur_import?.table_name}` : ''}
          >
            <div className="c-green-dark">{nf2(priceStable?.cur_price)}</div>
            <div className="c-grey text-sm">
              &nbsp;{priceStable.cur_import?.supplier_add ? '' : priceStable.cur_import?.supplier?.name}
            </div>
          </Space>
        ) : null;
      },
    },
    {
      title: 'SUPPLIER_ADD',
      dataIndex: ['item_ean', 'ean_price_stable', 'cur_import', 'supplier_add'],
      width: 60,
    },
    {
      title: 'Marked?',
      dataIndex: ['marked'],
      sorter: false,
      width: 40,
      ellipsis: true,
      align: 'center',
      render: (__, record) =>
        record.marked ? (
          <CheckCircleOutlined
            style={{ color: 'green', cursor: 'pointer' }}
            title="Un-mark an item."
            onClick={() => {
              const hide = message.success('Un-marking an item...', 0);
              updateOfferItem(sn(record.id), { marked: 0 })
                .then((res) => {
                  message.success('Un-marked successfully.');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          />
        ) : (
          <ProFormCheckbox
            fieldProps={{
              skipGroup: true,
              onChange: (e) => {
                const hide = message.success('Marking an item...', 0);
                updateOfferItem(sn(record.id), { marked: 1 })
                  .then((res) => {
                    message.success('Marked successfully.');
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(hide);
              },
            }}
            formItemProps={{ style: { margin: 0 } }}
          />
        ),
    },
    {
      title: 'Qty / pkg (Sys)',
      dataIndex: ['case_qty'],
      width: 50,
      align: 'right',
      className: 'bl2',
      render: (dom, record) => {
        return <span>{ni(record?.case_qty)}</span>;
      },
    },
    {
      title: 'Qty / pkg',
      dataIndex: ['item_ean', 'ean_price_stable', 'case_qty'],
      width: 50,
      align: 'right',
      render: (dom, record) => {
        let cls = '';
        if (sn(record.qty) % sn(record.item_ean?.ean_price_stable?.case_qty) !== 0) {
          cls += ' italic';
        }
        return <span className={cls}>{ni(record?.item_ean?.ean_price_stable?.case_qty)}</span>;
      },
    },
    {
      title: 'Pkg / pallet',
      dataIndex: ['item_ean', 'ean_price_stable', 've_pallet'],
      width: 50,
      align: 'right',
      render: (dom, record) => {
        return ni(record?.item_ean?.ean_price_stable?.ve_pallet);
      },
    },
    {
      title: 'Qty',
      dataIndex: ['qty'],
      width: 70,
      align: 'right',
      tooltip: (
        <div>
          Click to edit. <br />
          Red: Packed Qty=0
          <br /> Yellow: Packed Qty &lt; Qty
          <br />
          Blue: Packed Qty &gt; Qty
        </div>
      ),
      render: (__, record) => {
        let cls = '';
        if (sn(record.qty) % sn(record?.item_ean?.ean_price_stable?.case_qty) !== 0) {
          cls += ' italic';
        }
        const defaultValue = record.qty;

        const qty = sn(record.qty) * sn(record.case_qty);
        const packedQty = sn(record.packed_ready_qty_pcs);

        if (qty > 0 && packedQty > 0) {
          if (0 == packedQty) {
            cls += ' bg-red3';
          } else if (qty > packedQty) {
            cls += ' bg-yellow3';
          } else if (qty < packedQty) {
            cls += ' bg-blue3';
          }
        }

        return (
          <EditableCell
            dataType="number"
            precision={0}
            defaultValue={defaultValue}
            fieldProps={{ controls: false }}
            triggerUpdate={(newValue: any, cancelEdit) => {
              return updateOfferItem(sn(record.id), {
                qty: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            <span className={cls} style={{ padding: '2px 4px' }}>
              {ni(record.qty)}
            </span>
          </EditableCell>
        );
      },
      onCell: (record) => {
        return {
          // className: cls,
        };
      },
    },
    {
      title: '',
      dataIndex: ['qty_inc_or_dec'],
      width: 60,
      className: 'p0',
      align: 'right',
      render: (__, record) => {
        const setQty = (dir: -1 | 1) => {
          //
          const caseQty = sn(record?.item_ean?.ean_price_stable?.case_qty);
          if (!caseQty) {
            message.error('No Qty / pkg defined. Check imported XLS data.');
            return;
          }

          let minBoxQty = Math.floor(sn(record.qty) / caseQty);
          let newQty = Math.max(0, minBoxQty + dir) * caseQty;

          if (dir == -1) {
            if (minBoxQty * caseQty < sn(record.qty)) newQty = minBoxQty * caseQty;
          }

          const hide = message.loading('Updating...', 0);
          updateOfferItem(sn(record.id), {
            qty: newQty,
          })
            .then((res) => {
              message.destroy();
              message.success('Updated successfully.');
              actionRef.current?.reload();
            })
            .catch(Util.error)
            .finally(hide);
        };
        return (
          <>
            <Button
              size="small"
              danger
              icon={<MinusOutlined />}
              onClick={() => setQty(-1)}
              style={{ borderRight: 0 }}
            />
            <Button size="small" type="primary" ghost icon={<PlusOutlined />} onClick={() => setQty(1)} />
          </>
        );
      },
    },
    {
      title: '',
      dataIndex: ['qty_alt'],
      width: 50,
      align: 'right',
      tooltip: 'Rough Pkg Count = Qty / (Qty / pkg). Red color -> No exact matched Pkg!',
      render: (dom, record) => {
        const caseQtyXls = sn(record.item_ean?.ean_price_stable?.case_qty);
        return caseQtyXls ? nf2(sn(record.qty) / caseQtyXls, false, true) : null;
      },
      onCell: (record) => {
        let cls = '';
        const caseQtyXls = sn(record.item_ean?.ean_price_stable?.case_qty);
        if (caseQtyXls) {
          if (sn(record.qty) % caseQtyXls !== 0) {
            cls += ' bg-light-red1';
          }
        }
        return {
          className: cls,
        };
      },
    },
    {
      title: 'Special Price (€)',
      dataIndex: ['price_special'],
      tooltip: 'Click to Edit...',
      width: 90,
      align: 'right',
      render: (dom, record) => {
        const defaultValue = record.price_special;
        return (
          <EditableCell
            dataType="number"
            precision={2}
            defaultValue={defaultValue}
            triggerUpdate={(newValue: any, cancelEdit) => {
              return updateOfferItem(sn(record.id), {
                price_special: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error);
            }}
          >
            {nf2(record.price_special)}
          </EditableCell>
        );
      },
    },
    {
      title: 'Fixed Price (€)',
      dataIndex: ['price'],
      tooltip: 'price * percentage. Click to Edit...',
      width: 90,
      align: 'right',
      render: (dom, record) => {
        const defaultValue = record.price;
        return (
          <>
            <div className="absolute" style={{ top: 1, left: 2 }}>
              <HighlightOutlined
                className="cursor-point"
                style={{ color: 'lightgreen' }}
                onClick={() => {
                  const hide = message.loading('Updating Fixed Price by GFC Price...', 0);
                  return updateOfferItem(sn(record.id), {
                    price: sn(record?.item_ean?.ean_price_gfc?.price),
                  })
                    .then((res) => {
                      message.destroy();
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                title="Updating Fixed Price by GFC Price"
              />
            </div>
            <div
              className="absolute"
              style={{ top: 20, left: 2 }}
              onClick={() => {
                const hide = message.loading('Removing Fixed...', 0);
                return updateOfferItem(sn(record.id), {
                  price: 0,
                })
                  .then((res) => {
                    message.destroy();
                    message.success('Removed successfully.');
                    actionRef.current?.reload();
                  })
                  .catch(Util.error)
                  .finally(hide);
              }}
            >
              <DeleteOutlined className="cursor-point" style={{ color: '#ff8686' }} title="Removing a Fixed Price" />
            </div>
            <div>
              <EditableCell
                dataType="number"
                precision={2}
                defaultValue={defaultValue}
                triggerUpdate={(newValue: any, cancelEdit) => {
                  const hide = message.loading('Updating Fixed Price...', 0);
                  return updateOfferItem(sn(record.id), {
                    price: (newValue * 100) / sn(percent),
                  })
                    .then((res) => {
                      message.destroy();
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                      cancelEdit?.();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
              >
                {nf2((sn(record.price) * sn(percent)) / 100)}
              </EditableCell>
            </div>
          </>
        );
      },
      onCell: (record) => {
        return {
          style: {
            paddingLeft: 24,
          },
        };
      },
    },
    {
      title: 'GFC Price (€)',
      dataIndex: ['price_percentage'],
      tooltip: 'GFC price * Percentage',
      width: 70,
      align: 'right',
      render: (__, record) => {
        const gfcPrice = sn(record?.item_ean?.ean_price_gfc?.price);
        const stablePrice =
          record.item_ean?.ean_price_stable?.is_deleted == 0 ? sn(record.item_ean?.ean_price_stable?.cur_price) : 0;

        return (
          <Popover
            content={
              <table style={{ textAlign: 'left' }} cellPadding={'2'}>
                <tr>
                  <td style={{ width: 80 }}>GFC</td>
                  <td style={{ width: 60 }} className="text-right">
                    {nf2(gfcPrice)}
                  </td>
                  <td></td>
                </tr>
                <tr>
                  <td className="bg-yellow3">Online</td>
                  <td className="bg-yellow3 text-right">{nf2((gfcPrice * sn(percent)) / 100)}</td>
                  <td className="bg-yellow3"></td>
                </tr>
                <tr>
                  <td>{gfcPrice ? nf2((100 * (stablePrice * 1.14 - gfcPrice)) / gfcPrice) + '%' : ''}</td>
                  <td className="text-right">{nf2(stablePrice * 1.14)}</td>
                  <td>(114%)</td>
                </tr>
                <tr>
                  <td>{gfcPrice ? nf2((100 * (stablePrice * 1.08 - gfcPrice)) / gfcPrice) + '%' : ''}</td>
                  <td className="text-right">{nf2(stablePrice * 1.08)}</td>
                  <td>(108%)</td>
                </tr>
                <tr>
                  <td>{gfcPrice ? nf2((100 * (stablePrice * 1.02 - gfcPrice)) / gfcPrice) + '%' : ''}</td>
                  <td className="text-right">{nf2(stablePrice * 1.02)}</td>
                  <td>(102%)</td>
                </tr>
                <tr>
                  <td colSpan={4}>&nbsp;</td>
                </tr>
                <tr>
                  <td>Price Stable</td>
                  <td>{nf2(stablePrice)}</td>
                  <td></td>
                </tr>
              </table>
            }
            trigger="hover"
          >
            <div style={{ minHeight: 16 }}>{nf2((gfcPrice * sn(percent)) / 100)}</div>
          </Popover>
        );
      },
    },
    {
      title: 'GFC Price Total (€)',
      dataIndex: ['amount'],
      width: 85,
      align: 'right',
      render: (dom, record) => {
        return nf2((sn(record?.item_ean?.ean_price_gfc?.price) * sn(percent) * sn(record.qty)) / 100);
      },
    },
    {
      title: 'Customer Note',
      dataIndex: ['customer_note'],
      width: 150,
      ellipsis: true,
      className: 'text-sm',
    },
    {
      title: 'Fixed BP (€)',
      dataIndex: ['price_stable'],
      tooltip: 'price_stable. Click to Edit...',
      width: 90,
      align: 'right',
      render: (__, record) => {
        const defaultValue = record.price_stable;
        return (
          <EditableCell
            dataType="number"
            precision={2}
            defaultValue={defaultValue}
            triggerUpdate={(newValue: any, cancelEdit) => {
              const hide = message.loading('Updating Fixed BP...', 0);
              return updateOfferItem(sn(record.id), {
                price_stable: newValue,
              })
                .then((res) => {
                  message.destroy();
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            {nf2(record.price_stable)}
          </EditableCell>
        );
      },
      onCell: (record) => {
        return {
          style: {
            paddingLeft: 24,
          },
        };
      },
    },

    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      align: 'center',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      showSorterTooltip: false,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      align: 'center',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      colSize: 1,
      search: false,
      className: 'c-grey',
      width: 70,
      align: 'center',
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 60,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow({ ...record });
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  const expandedRowRender = useCallback((record: API.OfferItem, index: number) => {
    const isTotalRow = (iboPre: API.IboPre) => sn(iboPre.id) == -1;

    // total row
    const iboPreQtyTotal = sn(record.ibo_pre_piece_qty_total);
    const totalRow: API.OfferItem = {
      id: -1,
      ibo_pre_piece_qty_total: iboPreQtyTotal,
    };

    const itemEan = record.item_ean;
    const caseQty = sn(itemEan?.attr_case_qty);
    // whStr
    let whEle: any = null;

    if (itemEan && caseQty && iboPreQtyTotal) {
      // Calculating stock deductions for iboPreQtyTotal
      const ssList: API.StockStable[] = itemEan.parent_stock_stables ?? [];
      let aggregatedQty = 0;
      let insufficientQty = 0; // pcs qty
      const finalSsList: API.StockStable[] = [];
      const finalSummary: any = {};

      if (ssList.length) {
        for (const ss of ssList) {
          const xCaseQty = sn(ss.case_qty);
          const requiredQty = iboPreQtyTotal - aggregatedQty;

          const ssQty = sn(ss.total_piece_qty);

          // closing
          if (ssQty >= requiredQty) {
            const boxOrPcsQty = Math.ceil(requiredQty / xCaseQty);

            finalSsList.push({
              ...ss,
              box_qty: xCaseQty > 1 ? boxOrPcsQty : 0,
              piece_qty: xCaseQty > 1 ? 0 : boxOrPcsQty,
              total_piece_qty: boxOrPcsQty * xCaseQty,
            });
            aggregatedQty += ssQty;
            insufficientQty = -boxOrPcsQty * xCaseQty + requiredQty;
            break;
          } else {
            finalSsList.push(ss);
            aggregatedQty += ssQty;
          }
        }

        // if not enough stock?
        if (aggregatedQty < iboPreQtyTotal) {
          insufficientQty = iboPreQtyTotal - aggregatedQty;
        }

        // summarizing
        finalSsList.reduce((prev: any, ss: API.StockStable) => {
          const tmpCaseQty = sn(ss.case_qty);
          if (tmpCaseQty) {
            prev[tmpCaseQty] = sn(prev[tmpCaseQty]) + sn(tmpCaseQty == 1 ? ss.piece_qty : ss.box_qty);
          }
          return prev;
        }, finalSummary);
      }

      whEle = (
        <div>
          Warehouse:
          {Object.keys(finalSummary)
            .map((caseQty) => {
              const qty = finalSummary[caseQty];
              return sn(caseQty) == 1 ? `${qty} pcs ` : `${caseQty} x ${qty} boxes `;
            })
            .join(', ')}
          {insufficientQty ? (
            insufficientQty > 0 ? (
              <span style={{ color: 'red', paddingLeft: 8 }}>Insufficient Qty: {insufficientQty} pcs</span>
            ) : (
              <span style={{ color: 'green', paddingLeft: 8 }}>Remaining Qty: {insufficientQty} pcs</span>
            )
          ) : null}
        </div>
      );
    }

    return (
      <div style={{ paddingLeft: 150 }}>
        <ProTable<API.IboPre>
          cardProps={{ bodyStyle: { padding: 0 } }}
          style={{ width: 900 }}
          rowKey="id"
          headerTitle={false}
          search={false}
          options={false}
          pagination={false}
          size="small"
          columnEmptyText={''}
          dataSource={[totalRow, ...(record.ibo_pres_open_sent || [])]}
          rowClassName={(recordIn) => (record?.item_ean?.is_single ? 'row-single' : 'row-multi')}
          rowSelection={false}
          bordered
          locale={{ emptyText: <></> }}
          columns={[
            {
              title: 'IboPre Mgmt. ID',
              dataIndex: ['ibo_pre_management_id'],
              width: 80,
              ellipsis: true,
              align: 'center',
              render(dom, iboPre) {
                return isTotalRow(iboPre) ? (
                  <span className="bold">Total</span>
                ) : iboPre.ibo_pre_management_id ? (
                  <Typography.Link
                    href={`/ibo/ibo-pre?ibo_pre_management_id=${iboPre.ibo_pre_management_id}`}
                    target="_blank"
                  >
                    {`${iboPre.ibo_pre_management_id}`}
                  </Typography.Link>
                ) : null;
              },
            },
            {
              title: 'Supplier',
              dataIndex: ['supplier', 'name'],
              width: 60,
              ellipsis: true,
              align: 'center',
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },
            {
              title: 'Inbound No',
              dataIndex: ['ibo_pre_management', 'inbound_no'],
              width: 110,
              align: 'center',
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },
            {
              title: 'Status',
              dataIndex: 'status',
              align: 'center',
              width: 80,
            },
            {
              title: 'Created Date',
              dataIndex: ['created_on'],
              width: 100,
              align: 'center',
              render(__, entity) {
                return Util.dtToDMYHHMM(entity.created_on);
              },
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },
            {
              title: 'Sent Date',
              dataIndex: ['sent_date'],
              width: 100,
              align: 'center',
              render(__, entity) {
                return Util.dtToDMY(entity.sent_date);
              },
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },
            {
              title: 'Invoiced Date',
              dataIndex: ['invoiced_date'],
              width: 100,
              align: 'center',
              render(__, entity) {
                return Util.dtToDMY(entity.invoiced_date);
              },
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },

            {
              title: 'Type',
              dataIndex: 'type',
              align: 'center',
              width: 80,
              render(dom, entity) {
                if (entity.type == 1) return 'On EanPrice';
                else if (entity.type == 2) return 'By XLS Import';
                return dom;
              },
            },
            {
              title: 'Case Qty',
              dataIndex: 'case_qty',
              align: 'right',
              width: 70,
              render: (__, entity) => ni(entity.case_qty),
            },
            { title: 'Qty', dataIndex: 'qty', align: 'right', width: 100, render: (__, entity) => ni(entity.qty) },
            {
              title: 'Qty (Pcs)',
              dataIndex: 'qty_pcs',
              align: 'right',
              width: 100,
              render: (__, entity) =>
                isTotalRow(entity) ? ni(totalRow.ibo_pre_piece_qty_total) : ni(sn(entity.case_qty) * sn(entity.qty)),
            },
            {
              title: 'Price XLS',
              dataIndex: 'price_xls',
              align: 'right',
              width: 80,
              render: (__, entity) => {
                if (isTotalRow(entity)) {
                  return whEle;
                } else {
                  return nf2(sn(entity.price_xls));
                }
              },
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 8 : 1,
                };
              },
            },
            /* {
              title: 'XLS',
              dataIndex: ['import', 'table_name'],
              width: 110,
              ellipsis: true,
              render(dom, entity) {
                return entity.import ? sShortImportDbTableName(entity.import?.table_name, true) : null;
              },
            }, */
            {
              title: 'SUPPLIER_ADD',
              dataIndex: ['import', 'supplier_add'],
              width: 110,
              align: 'center',
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },

            {
              title: 'Notes (Supplier)',
              dataIndex: ['ibo_pre_management', 'note_supplier'],
              width: 110,
              align: 'center',
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },
            {
              title: 'Notes (Customer)',
              dataIndex: ['ibo_pre_management', 'note_customer'],
              width: 110,
              align: 'center',
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },

            {
              title: 'Updated On',
              dataIndex: ['updated_on'],
              width: 110,
              align: 'center',
              render(__, entity) {
                return Util.dtToDMYHHMM(entity.updated_on);
              },
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },

            {
              title: '',
              valueType: 'option',
              width: 40,
              render(__, entity) {
                if (isTotalRow(entity)) return null;

                const isLinked = `,${entity.offer_item_ids},`.includes(`,${record.id},`);

                const onClickHandler = (isLinked: boolean) => {
                  const hide = message.loading(isLinked ? 'Unlinking...' : 'Linking...', 0);
                  assignIboPre(sn(record.id), sn(entity.id), isLinked ? 'unlink' : 'link')
                    .then((res) => {
                      message.success('Updated successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                };

                if (isLinked) {
                  return (
                    <LinkOutlined
                      title="Unlink IBO Pre to an offer"
                      style={{ color: 'green', cursor: 'pointer' }}
                      onClick={() => onClickHandler(isLinked)}
                    />
                  );
                } else {
                  return (
                    <LinkOutlined
                      title="Link IBO Pre to an offer"
                      style={{ color: 'red', cursor: 'pointer' }}
                      onClick={() => onClickHandler(isLinked)}
                    />
                  );
                }
              },
              onCell: (entity, index) => {
                return {
                  colSpan: isTotalRow(entity) ? 0 : 1,
                };
              },
            },
          ]}
        />
      </div>
    );
  }, []);

  useEffect(() => {
    const formValues = searchFormRef.current?.getFieldsValue();
    if (formValues?.offer_id) {
      searchFormRef.current?.setFieldValue('offer_id', formValues.offer_id);
    }
  }, []);

  useEffect(() => {
    if (offerIdInUrl) {
      searchFormRef.current?.setFieldValue('offer_id', sn(offerIdInUrl));
      actionRef.current?.reload();
    }
  }, [offerIdInUrl]);

  useEffect(() => {
    searchTrademarks();
    actionRef.current?.reload();
  }, [offer?.id, searchTrademarks]);

  const loadOfferDetail = useCallback(() => {
    const offerId = offer?.id ?? offerIdInUrl;
    if (offerId) {
      getOfferList({ with: 'linkedIboPreManagement', pageSize: 1, id: offerId }, {}, {})
        .then((res) => {
          setOfferDetail(res.data?.[0]);
        })
        .catch((err) => {
          Util.error(err);
          setOfferDetail(undefined);
        });
    } else {
      setOfferDetail(undefined);
    }
  }, [offerIdInUrl, offer?.id]);

  useEffect(() => {
    loadOfferDetail();
  }, [loadOfferDetail]);

  return (
    <PageContainer
      title={
        <Space size={48}>
          <span>Offer Items List</span>
          <Typography.Link href="/quotes/offer-matrix" target="_blank" className="text-sm">
            Open Matrix
          </Typography.Link>
          <Space style={{ fontSize: 11, letterSpacing: -0.1, lineHeight: 1.1 }} wrap={true}>
            {offerDetail?.ibo_pre_managements?.map((x) => {
              let cls = '';
              if (x.status == 'sent') {
                cls += ' c-orange';
              } else if (x.status == 'open') {
                cls += ' c-red';
              } else if (x.status == 'invoiced') {
                cls += ' c-green';
              }
              return (
                <Typography.Link key={x.id} href={`/ibo/ibo-pre?ibo_pre_management_id=${x.id}`} target="_blank">
                  <span className={cls} style={{ marginRight: 16 }}>{`${x.id} - ${x.note_supplier || 'N/A'} - ${
                    x.inbound_no || 'N/A'
                  } ${x.status}`}</span>
                </Typography.Link>
              );
            })}
          </Space>
        </Space>
      }
      extra={<div style={{ maxWidth: 700, maxHeight: 32, overflowY: 'auto' }}>{offerDetail?.gfc_note}</div>}
    >
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_offer_item', { includeSubTable: false })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormGroup size="small" rowProps={{ gutter: 0 }}>
            {formElements}
            {formElementsTrademark}
            <ProFormText name="sku" label="SKU" width={130} placeholder="SKU" />
            <ProFormText name="ean" label="EAN" width={150} placeholder="EAN" />
            <ProFormSelect
              name="marked"
              options={[
                { value: 0, label: 'Unmarked' },
                { value: 1, label: 'Marked' },
              ]}
              width="xs"
            />
            <ProFormText name="supplier_name" label="Supplier" width={130} placeholder="Supplier" />
            <ProFormText name="supplier_add" label="SUPPLIER_ADD" width={130} placeholder="Supplier" />
          </ProFormGroup>
          <div style={{ width: '100%', height: 1 }}>&nbsp;</div>
          <ProFormGroup size="small" rowProps={{ gutter: 0 }}>
            <ProFormSwitch
              name="includeSubTable"
              label="Show IBO Pre?"
              fieldProps={{ onChange: () => actionRef.current?.reload() }}
            />

            <ProFormSelect
              name="mappingOptionInIboPresOpenSent"
              label="IBO Pre Mapping"
              options={[
                { value: '', label: 'ALL' },
                { value: 'mapped', label: 'Only Mapped' },
                { value: 'not_mapped', label: 'Not mapped' },
              ]}
              fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
            />
          </ProFormGroup>
        </ProForm>
      </Card>

      <ProTable<API.OfferItem, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Offer Items List</span>

            {offer ? (
              <Space size={8}>
                <span className="text-sm">
                  IBO Pre Status <InfoCircleOutlined title="Click to Edit on Status" />
                </span>
                <EditableCell
                  dataType="select"
                  defaultValue={offer.ibo_status || ''}
                  style={{ marginRight: 0 }}
                  fieldProps={{ style: { lineHeight: 1, width: 180 } }}
                  valueEnum={OfferIboStatusKv}
                  triggerUpdate={async (newValue: any, cancelEdit) => {
                    if (!newValue && !offer.id) {
                      cancelEdit?.();
                      return;
                    }
                    return updateOffer(sn(offer.id), {
                      ibo_status: newValue,
                    })
                      .then((res) => {
                        if (res.message.id) {
                          searchOfferOptions();
                          setOffer((prev) => ({ ...prev, ibo_status: res.message.ibo_status } as any));
                        }

                        message.destroy();
                        message.success('Updated successfully.');
                        cancelEdit?.();
                      })
                      .catch(Util.error);
                  }}
                >
                  <OfferIboStatusComp status={offer.ibo_status} />
                </EditableCell>
              </Space>
            ) : null}
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        sticky
        search={false}
        scroll={{ x: 800 }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_offer_item_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="open-ibo-pre-list-modal"
            title="'Open' IBO Pre List modal"
            ghost
            style={{ marginRight: 24 }}
            onClick={() => {
              setOpenIboPreListModal(true);
            }}
          >
            <TabletFilled />
            IBO Pres
          </Button>,
          <Button
            type="primary"
            key="new"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
          <Button
            type="primary"
            key="bulk-new"
            onClick={() => {
              handleBulkCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> Bulk New
          </Button>,
          <Button
            key="inc-wish-qty"
            type="primary"
            htmlType="button"
            ghost
            disabled={!offer?.value}
            style={{ display: 'none' }}
            onClick={() => {
              if (!offer?.value) return;
              const hide = message.loading('Adding wishing qtys into item eans...', 0);
              updateItemEanWishQty({ dir: 1, offer_id: sn(offer?.value) })
                .catch(Util.error)
                .then((res) => {
                  message.success('Added successfully.');
                })
                .finally(hide);
            }}
            icon={<PlusCircleOutlined />}
            title="Increase Wishing Qty"
          >
            Wishing Qty
          </Button>,
          <Button
            key="dec-wish-qty"
            type="primary"
            htmlType="button"
            danger
            ghost
            disabled={!offer?.value}
            style={{ display: 'none' }}
            title="Decrease Wishing Qty"
            onClick={() => {
              if (!offer?.value) return;
              const hide = message.loading('Deducting wishing qtys into item eans...', 0);
              updateItemEanWishQty({ dir: -1, offer_id: sn(offer?.value) })
                .catch(Util.error)
                .then((res) => {
                  message.success('Deducted successfully.');
                })
                .finally(hide);
            }}
            icon={<MinusCircleOutlined />}
          >
            Wishing Qty
          </Button>,

          <Button
            key="export-xls-with-ibo-pre"
            type="primary"
            htmlType="button"
            loading={loading}
            disabled={loading}
            title="Export XLS with IBO Pre sub table data."
            onClick={() => {
              const searchFormValues = searchFormRef.current?.getFieldsValue();
              const offer_id = searchFormValues?.offer_id;
              if (!offer_id) {
                message.info('Select an Offer!');
                return;
              }

              const searchValues = searchFormRef.current?.getFieldsValue();

              const params = {
                ...Util.mergeGSearch(searchValues),
                with: 'itemEan,itemEan.parent,itemEan.eanTextDe,itemEan.eanPriceGfc,offer,itemEan.item,itemEan.files,itemEan.parentStockStables,iboPresOpenSent,itemEan.eanPriceStable',
              };

              const hide = message.loading('Exporting XLS with Ibo Pres...', 0);
              setLoading(true);
              exportOfferItemListWithIboPres({
                offer_id: offer_id,
                ...params,
              })
                .then((res) => {
                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                })
                .catch(Util.error)
                .finally(() => {
                  hide();
                  setLoading(false);
                });
            }}
            icon={<FileExcelOutlined />}
          >
            XLS (IBO Pre)
          </Button>,

          <div key="percentage" style={{ marginLeft: 16 }}>
            <SProFormDigit
              width={70}
              fieldProps={{
                precision: 2,
                value: percent,
                onChange(value) {
                  setPercent(sn(value));
                },
                onBlur: (e) => {
                  console.log(e.target?.value, percent);
                  if (offerDetail?.id && sn(e.target.value) != sn(offerDetail?.percentage)) {
                    updateOffer(sn(offerDetail.id), { percentage: sn(e.target.value) })
                      .then((res) => {
                        message.success('Saved percentage!');
                        setOfferDetail((prev) => ({ ...prev, percentage: sn(e.target.value) }));
                      })
                      .catch((reason) => {
                        setPercent(sn(offerDetail?.percentage));
                        Util.error(reason);
                      });
                  }
                },
              }}
              addonAfter="%"
              formItemProps={{ style: { marginBottom: 0 } }}
            />
          </div>,

          <Popover
            key="export-xls-popup"
            title="Select XLS Export Mode"
            trigger="click"
            open={openExportForm}
            onOpenChange={(visible) => {
              setOpenExportForm(visible);
            }}
            content={
              <ProForm<ExportForm>
                formRef={exportFormRef}
                size="small"
                layout="horizontal"
                style={{ width: 300 }}
                onFinish={async (values) => {
                  setOpenExportForm(false);
                  const searchFormValues = searchFormRef.current?.getFieldsValue();
                  const offer_id = searchFormValues?.offer_id;
                  if (!offer_id) {
                    message.info('Select an Offer!');
                    return;
                  }

                  const hide = message.loading('Exporting XLS...', 0);
                  setLoading(true);
                  exportOfferItemList({
                    offer_id: offer_id,
                    ...values,
                    percentage: percent,
                  })
                    .then((res) => {
                      window.open(`${API_URL}/api/${res.url}`, '_blank');
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                }}
                submitter={{
                  searchConfig: { submitText: 'Export XLS' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormRadio.Group
                  name="include_mode"
                  initialValue={'std'}
                  options={[
                    { value: 'std', label: 'Std' },
                    { value: 'supplier', label: 'Inc. Supplier' },
                    { value: 'bp', label: 'Inc. BP' },
                  ]}
                />

                {/* <SProFormDigit
                  colProps={{ span: 'auto' }}
                  name="percentage"
                  label="Percentage"
                  width={120}
                  addonAfter={'%'}
                  min={-99999999}
                  fieldProps={{
                    precision: 4,
                  }}
                  initialValue={115}
                  placeholder="Percentage"
                  formItemProps={{
                    tooltip: (
                      <div>
                        <div>GFC Offer Price will be Price * (1 + percentage)</div>
                      </div>
                    ),
                  }}
                /> */}

                <ProFormDependency name={['include_mode']}>
                  {(deps) => {
                    if (deps.include_mode == 'std') {
                      return formElementsWaNo;
                    }
                    return null;
                  }}
                </ProFormDependency>
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              onClick={() => setOpenExportForm(true)}
              icon={<FileExcelOutlined />}
            >
              List XLS
            </Button>
          </Popover>,
          <Button
            key="export-xls-stock"
            type="primary"
            htmlType="button"
            loading={loading}
            disabled={loading}
            onClick={() => {
              const hide = message.loading('Exporting PDF with stocks...');
              exportOfferItemListWithStocks({ offer_id: sn(offer?.id) })
                .then((res) => {
                  message.success('Exported successfully.');
                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
            icon={<FilePdfOutlined />}
          >
            Stock PDF
          </Button>,
          <Button
            key="export-pdf"
            type="primary"
            htmlType="button"
            loading={loading}
            disabled={loading}
            onClick={() => setOpenExportPdfForm(true)}
            icon={<FilePdfOutlined />}
          >
            List PDF
          </Button>,
          <Button
            key="export-pdf-ibo-pre"
            type="primary"
            htmlType="button"
            loading={loading}
            disabled={loading}
            onClick={() => {
              setOpenExportPackingPdfForm(true);
            }}
            icon={<FilePdfOutlined />}
          >
            Packing PDF
          </Button>,
          <Button
            key="open-mobile-list"
            type="link"
            icon={<MobileOutlined />}
            title="Open Offer Items (mobile) in new page"
            href={`/quotes/offer-item-mobile?offer_id=${offer?.value}`}
            target="_blank"
          />,
        ]}
        expandable={{
          expandedRowRender,
          rowExpandable: (record) => !!record.ibo_pres_open_sent?.length,
          showExpandColumn: true,
          columnWidth: 25,
          /* columnTitle: (
            <div style={{ paddingLeft: 16 }}>
              {expandedRowKeys.length == dataSource.length ? (
                <MinusOutlined
                  onClick={() => {
                    setExpandedRowKeys([]);
                  }}
                />
              ) : (
                <PlusOutlined
                  onClick={() => {
                    setExpandedRowKeys(dataSource.map((x) => x.entity_id as any));
                  }}
                />
              )}
            </div>
          ), */
        }}
        request={async (params, sort, filter) => {
          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_offer_item', searchValues);
          Util.setSfValues('sf_offer_item_p', params);

          const newParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            with:
              'itemEan,itemEan.eanTextDe,itemEan.eanPriceGfc,offer,itemEan.ibosItemMinPrice,itemEan.supplierXlsBps,itemEan.item,itemEan.files,itemEan.parentStockStables,itemEan.eanPriceStable,itemEan.magInventoryStocksQty,includeImports,offerItemPackedReadySummary,shippedQty' +
              (searchValues?.includeSubTable ? ',iboPresOpenSent' : ''),
          };

          return getOfferItemList(newParam, sort, filter).then((res) => {
            setXlsImports(res.imports || []);

            if (currentRow?.id) {
              setCurrentRow(res.data.find((x) => x.id == currentRow.id));
            }
            return res;
          });
        }}
        onRequestError={Util.error}
        columns={columns}
        rowClassName={(record) => {
          let cls = record.item_ean?.is_single ? 'row-single' : 'row-multi';
          if (record.ibo_pre_mapped_count) {
            cls += ' reset-tds-bg bg-light-green';
          }
          return cls;
        }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          selectedRowKeys: selectedRowsState.map((x) => x.id as any),
        }}
        tableAlertRender={false}
        columnEmptyText={''}
        locale={{ emptyText: <></> }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              offer items &nbsp;&nbsp;
            </div>
          }
        >
          <Popover
            title="Add to IBO Pre"
            trigger="click"
            open={openIboPreSelectionForm}
            onOpenChange={(visible) => {
              setOpenIboPreSelectionForm(visible);
            }}
            content={
              <ProForm<{ ibo_pre_management_id?: number }>
                size="small"
                onFinish={async (values) => {
                  if (!values.ibo_pre_management_id) {
                    message.error('Please select IBO Pre!');
                    return;
                  }
                  const hide = message.loading('Adding selected SKUs to the selected IBO Pre...', 0);
                  addToIboPre(
                    sn(values.ibo_pre_management_id),
                    selectedRowsState.map((x) => sn(x.id)),
                  )
                    .then((offer) => {
                      message.success(`Added to IBO Pre #${values.ibo_pre_management_id} successfully.`);
                      actionRef.current?.reload();
                      setOpenIboPreSelectionForm(false);
                      setSelectedRows([]);
                      loadOfferDetail();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
                submitter={{
                  searchConfig: { submitText: 'Add' },
                  render(__, dom) {
                    return [dom[1]];
                  },
                }}
              >
                <ProFormSelect
                  name={'ibo_pre_management_id'}
                  showSearch
                  label="Pre Order"
                  width={200}
                  options={iboPreManagementOptions}
                  request={(params) => {
                    return searchIboPreManagementOptions({ ...params, nin_status: ['done', 'sent', 'invoiced'] });
                  }}
                  fieldProps={{
                    filterOption: (inputValue: string, option?: any) => true,
                    dropdownMatchSelectWidth: false,
                  }}
                />
              </ProForm>
            }
          >
            <Button
              type="primary"
              htmlType="button"
              loading={loading}
              disabled={loading}
              title="Add to IBO Pre..."
              onClick={() => setOpenIboPreSelectionForm(true)}
            >
              Add to IBO Pre
            </Button>
          </Popover>
          <Button
            key="export-pdf-stock"
            type="primary"
            htmlType="button"
            loading={loading}
            disabled={loading}
            onClick={() => {
              const hide = message.loading('Exporting PDF with stocks...');
              exportOfferItemListWithStocks({ offer_id: sn(offer?.id), ids: selectedRowsState.map((x) => sn(x.id)) })
                .then((res) => {
                  message.success('Exported successfully.');
                  window.open(`${API_URL}/api/${res.url}`, '_blank');
                })
                .catch(Util.error)
                .finally(() => hide());
            }}
            icon={<FilePdfOutlined />}
          >
            Export Stock PDF
          </Button>
          <Popconfirm
            title={<>Are you sure you want to delete selected offer items?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 350 }}
            onConfirm={async () => {
              const res = await handleRemove(selectedRowsState);
              if (res) {
                setSelectedRows([]);
                actionRef.current?.reload();
              }
            }}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              Batch deletion
            </Button>
          </Popconfirm>
        </FooterToolbar>
      )}

      <CreateForm
        offer_id={sn(offer?.value)}
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={async (value) => {
          handleCreateModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => handleCreateModalVisible(false)}
      />

      <CreateBulkForm
        offer_id={sn(offer?.value)}
        modalVisible={bulkCreateModalVisible}
        handleModalVisible={handleBulkCreateModalVisible}
        onSubmit={async (value) => {
          handleBulkCreateModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => handleBulkCreateModalVisible(false)}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <ExportPdfSettingFormModal
        offer={offer as API.Offer}
        modalVisible={openExportPdfForm}
        handleModalVisible={setOpenExportPdfForm}
        getParentParams={() => {
          return {
            offer_id: offer?.value,
            ...searchFormRef.current?.getFieldsValue(),
            percentage: percent,
          };
        }}
      />

      <ExportPackingPdfSettingFormModal
        modalVisible={openExportPackingPdfForm}
        offer={offer as API.Offer}
        handleModalVisible={setOpenExportPackingPdfForm}
        getParentParams={() => {
          return {
            offer_id: offer?.value,
            ...searchFormRef.current?.getFieldsValue(),
          };
        }}
      />

      <IboPreListModal
        modalVisible={openIboPreListModal}
        handleModalVisible={setOpenIboPreListModal}
        parentSearchFormRef={searchFormRef}
        searchParams={{ statuses: [OfferIboStatus.Open] }}
      />

      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          ean_text_de: currentRow?.item_ean?.ean_text_de,
          mag_inventory_stocks_sum_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_quantity,
          mag_inventory_stocks_sum_res_quantity: currentRow?.item_ean?.mag_inventory_stocks_sum_res_quantity,
          mag_inventory_stocks_sum_res_cal: currentRow?.item_ean?.mag_inventory_stocks_sum_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />

      {/* <OfferItemPackedReadyListModal
        offerItem={currentRow}
        modalVisible={openOfferItemPackedReadyListModal}
        handleModalVisible={setOpenOfferItemPackedReadyListModal}
      /> */}

      <OfferItemPackedReadyListEditableModal
        offerItem={currentRow}
        modalVisible={openOfferItemPackedReadyListEditableModal}
        handleModalVisible={setOpenOfferItemPackedReadyListEditableModal}
        parentReload={() => {
          actionRef.current?.reload();
        }}
      />

      <OfferItemPackedReadyListExtModal
        offerItem={currentRow}
        modalVisible={openOfferItemPackedReadyListExtModal}
        handleModalVisible={setOpenOfferItemPackedReadyListExtModal}
      />

      <UpdateNoteForm
        modalVisible={openUpdateNoteForm}
        handleModalVisible={setOpenUpdateNoteForm}
        initialValues={currentRow}
        onSubmit={async (values) => {
          actionRef.current?.reload();
          return Promise.resolve(true);
        }}
      />

      {currentRow && currentRow.item_ean ? (
        <CreateOrUpdateQtyShippedModalForm2
          itemEan={currentRow.item_ean}
          offerItem={currentRow}
          modalVisible={openCreateOrUpdateQtyShippedForm}
          handleModalVisible={setOpenCreateOrUpdateQtyShippedForm}
          loadParentList={() => {
            actionRef.current?.reload();
          }}
          onSubmit={async (shippedRow) => {
            actionRef.current?.reload();
          }}
          onCancel={() => {
            actionRef.current?.reload();
          }}
        />
      ) : null}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.OfferItem>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.OfferItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default OfferItem;
