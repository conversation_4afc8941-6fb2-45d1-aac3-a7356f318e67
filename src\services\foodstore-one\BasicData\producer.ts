/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/basic-data/producer';

/** rule GET /api/basic-data/producer */
export async function getProducerList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export function getFullAddress(producer: API.Producer) {
  let fullAddress = '';
  if (producer.street) {
    if (fullAddress) fullAddress += ', ';
    fullAddress += producer.street;
  }
  if (producer.zip) {
    if (fullAddress) fullAddress += ', ';
    fullAddress += producer.zip;
  }
  if (producer.city) {
    if (fullAddress) fullAddress += ', ';
    fullAddress += producer.city;
  }
  if (producer.country) {
    if (fullAddress) fullAddress += ', ';
    fullAddress += producer.country;
  }

  return fullAddress;
}

export async function getProducerListSelectOptions(params: API.PageParams) {
  const res = await getProducerList({ ...params, pageSize: 1000 }, { name: 'ascend' }, {});
  if (res && res.data) {
    const tmp = res.data.map((x: API.Producer) => {
      const fullAdress = getFullAddress(x);
      return {
        label: `${x.name}${fullAdress ? ` (${fullAdress})` : ''}`,
        value: x.id,
      };
    });
    return tmp;
  }
  return [];
}

/** put PUT /api/basic-data/producer */
export async function updateProducer(data: API.Producer, options?: { [key: string]: any }) {
  return request<API.Producer>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/producer */
export async function addProducer(data: API.Producer, options?: { [key: string]: any }) {
  return request<API.Producer>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/producer/{id} */
export async function deleteProducer(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
