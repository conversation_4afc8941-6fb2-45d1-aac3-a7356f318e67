import { CURRENT_YEAR, THIS_YEAR } from '@/constants';
import {
  getSalesShippingStatsList,
  RecordTypeSalesShippingStatsList,
} from '@/services/foodstore-one/Report/sales-shipping-stat';
import Util, { nf2, ni } from '@/util';
import ProForm, { ProFormInstance, ProFormSelect, ProFormSwitch } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable, { ActionType, ProColumns } from '@ant-design/pro-table';
import { Card, Col, Row } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { IRouteComponentProps, useModel } from 'umi';

const MAX_YEAR_COUNT = 10;
const yearsList = [...Array(MAX_YEAR_COUNT)].map((x, ind) => `${THIS_YEAR - ind}`);

type RecordType = RecordTypeSalesShippingStatsList;

type SearchFormValueType = {
  year?: number;
  is_fiscal_year?: boolean;
};

const SalesShippingStats: React.FC<IRouteComponentProps> = (props) => {
  const { appSettings } = useModel('app-settings');

  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [totalRow, setTotalRow] = useState<RecordType>();
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);
  const [ymList, setYmList] = useState<string[]>();

  console.log(appSettings);

  useEffect(() => {
    const savedSfValues = Util.getSfValues('sf_order_sales_shipping_stat', { is_fiscal_year: true });
    searchFormRef.current?.setFieldsValue(savedSfValues);

    if (savedSfValues.is_fiscal_year && !savedSfValues.year) {
      const today = Util.dtToYMD(new Date()) ?? '';
      const fiscalDate = `${CURRENT_YEAR}-07-01`;

      if (today < fiscalDate) {
        savedSfValues.year = CURRENT_YEAR - 1;
      } else {
        savedSfValues.year = CURRENT_YEAR;
      }

      searchFormRef.current?.setFieldsValue(savedSfValues);
      Util.setSfValues('sf_order_sales_shipping_stat', savedSfValues);
      actionRef.current?.reload();
    }
  }, []);

  const columnsDefault: ProColumns<RecordType>[] = useMemo(
    () => [
      {
        title: '',
        dataIndex: ['shipping_provider_name'],
        width: 80,
        fixed: 'left',
        render(dom, entity: any) {
          return entity.shipping_provider_name ?? ' - ';
        },
      },
    ],
    [],
  );

  useEffect(() => {
    const newColumns: ProColumns<RecordType>[] = [...columnsDefault];
    ymList?.forEach((ym, index) => {
      if (index < 12) {
        newColumns.push({
          dataIndex: `shipping_amount_${index}`,
          title: Util.dtToDMY(ym, 'MM` YY'),
          width: 120,
          align: 'center',
          render(dom, entity: any) {
            return (
              <Row className="text-right">
                <Col span={12}>{nf2(entity[`shipping_amount_${index}`])}</Col>
                <Col span={12} className="c-grey">
                  {ni(entity[`order_cnt_${index}`])}
                </Col>
              </Row>
            );
          },
        });
      }
    });

    newColumns.push({
      dataIndex: `shipping_amount_total`,
      title: 'Total',
      width: 150,
      align: 'center',
      render(dom, entity: any) {
        return (
          <Row className="text-right">
            <Col span={12}>{nf2(entity[`shipping_amount_total`])}</Col>
            <Col span={12} className="c-grey">
              {ni(entity[`order_cnt_total`])}
            </Col>
          </Row>
        );
      },
    });

    newColumns.push({
      valueType: 'option',
    });

    setColumns(newColumns);
  }, [columnsDefault, ymList]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSwitch
            name="is_fiscal_year"
            label="Fiscal?"
            fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
          />
          <ProFormSelect
            name="year"
            label="Year"
            options={yearsList}
            fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
          />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={'Sales Shipping Stats Monthly'}
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        bordered
        loading={loading}
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_order_sales_shipping_stat', searchFormValues);

          setLoading(true);
          return getSalesShippingStatsList(
            {
              ...params,
              ...searchFormValues,
            },
            sort,
            filter,
          )
            .then((res) => {
              setTotalRow(res.summary);
              setYmList(res.ymList);
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: false,
          hideOnSinglePage: false,
          showQuickJumper: false,
          defaultPageSize: 10000,
        }}
        columns={columns}
        columnEmptyText=""
        tableAlertRender={false}
        onRow={(record, index) => {
          /* if (record.ym) {
            return {
              style: { background: sn(index) % 2 ? '#f5f5f5' : '#eee' },
            };
          }

          return {
            className: cls,
          }; */
          return {};
        }}
      />
    </PageContainer>
  );
};

export default SalesShippingStats;
