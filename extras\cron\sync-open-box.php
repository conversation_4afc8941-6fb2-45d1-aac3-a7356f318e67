<?php
/**
 *
 * 1. Down syncing orders from <PERSON>gento.
 *      Changed orders will be synced into xmag_order table.
 *      Order shipments history will be updated.
 * 2. Open multi-boxes if no singles in stock
 *
 * @package     Cron job script.
 * @since       2023-01-31
 */

use App\Lib\FuncModel;
use App\Models\ItemEan;
use App\Models\Magento\MagSyncLog;
use App\Service\Magento\Stock\MagInventoryService;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

/** @var \Slim\Container $container */
/** @var \App\Service\Magento\Order\MagOrderService $mOrderService */
$mOrderService = $container->get('mag_order_service');

$batchCode = \App\Lib\Func::randomTs();
$eanIdsStockChanged = [];

try {
    FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_AUTO_OPEN_BOX_PROCESS, [
        'note' => 'Started',
        'status' => MagSyncLog::STATUS_SUCCESS,
        'batch_code' => $batchCode,
    ]);

    // to do
    // 1. Down sync latest orders
    $mOrderService->dsLatestOrders();

    // 2. Open boxes
    /** @var \App\Service\Magento\Order\OpenMultiboxService $openMultiService */
    $openMultiService = $container->get(\App\Service\Magento\Order\OpenMultiboxService::class);

    $eanIdsStockChanged = $openMultiService->open();

    // 3. Up syncing qtys
    if ($eanIdsStockChanged) {
        /** @var MagInventoryService $magInventoryService */
        $magInventoryService = $container->get(MagInventoryService::class);

        /** @var ItemEan[] $itemEans */
        $itemEans = ItemEan::query()
            ->whereIn('id', $eanIdsStockChanged)
            ->select('item_ean.id', 'sku', 'ean', 'parent_id', 'item_id', 'product_websites','status', 'attr_case_qty', 'virtual_stock_qty_gfc')
            ->magInventoryStocksQty()
            ->get();

        foreach ($itemEans as $itemEan) {
            /*// to do Later we will remove this debug logic
            FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_AUTO_OPEN_BOX_PROCESS, [
                'note' => 'DEBUG 1: BEFORE UPSYNC: ' . $itemEan->sku,
                'status' => MagSyncLog::STATUS_SUCCESS,
                'batch_code' => $batchCode,
                'detail' => $itemEan->toArray(),
            ]);*/

            $product = $magInventoryService->usUpdateSourceItems($itemEan);
        }
    }

    FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_AUTO_OPEN_BOX_PROCESS, [
        'note' => 'Complete',
        'status' => MagSyncLog::STATUS_SUCCESS,
        'batch_code' => $batchCode,
    ]);
} catch (Exception $exception) {
    FuncModel::createMagSyncLog(MagSyncLog::SYNC_TYPE_SYS_OWN, MagSyncLog::NAME_AUTO_OPEN_BOX_PROCESS, [
        'note' => $exception->getMessage(),
        'status' => MagSyncLog::STATUS_ERROR,
        'batch_code' => $batchCode,
    ]);
}
