rem "arguments: className namespace"
rem php F:\50-SC\php-slim-code-gen\gen.php MagOrderShipmentComment Magento 0
rem php F:\50-SC\php-slim-code-gen\gen.php CrmCase Crm 0
rem php F:\50-SC\php-slim-code-gen\gen.php CrmCaseEmail Crm 0
rem php F:\50-SC\php-slim-code-gen\gen.php CrmCaseNote Crm 0

rem php F:\50-SC\php-slim-code-gen\gen.php IboPre Ibo 0
rem php F:\50-SC\php-slim-code-gen\gen.php IboPreManagement Ibo 0

rem php F:\50-SC\php-slim-code-gen\gen.php Offer Offer 0
rem php F:\50-SC\php-slim-code-gen\gen.php OfferItem Offer 0

rem php F:\50-SC\php-slim-code-gen\gen.php WarehousePicklistTimeTracking Warehouse 0

rem php F:\50-SC\php-slim-code-gen\gen.php MopProduct MopProduct 0

rem php F:\50-SC\php-slim-code-gen\gen.php TrademarkGroup BasicData 0

rem php F:\50-SC\php-slim-code-gen\gen.php MagOrderParcel Magento 0

rem php F:\50-SC\php-slim-code-gen\gen.php MagOrderParcelLog Magento 0

rem php F:\50-SC\php-slim-code-gen\gen.php StockStableProblem Stock 0

rem php F:\50-SC\php-slim-code-gen\gen.php StockStableBooked Stock 0

rem php F:\50-SC\php-slim-code-gen\gen.php MagOrderUserActionLog Magento 0

rem php F:\50-SC\php-slim-code-gen\gen.php OfferItemDelivered Offer 0

rem php F:\50-SC\php-slim-code-gen\gen.php OfferRecvWeight Offer 0

rem php F:\50-SC\php-slim-code-gen\gen.php SysLog Sys 0

rem php F:\50-SC\php-slim-code-gen\gen.php OfferItemShipped Offer 0

rem php F:\50-SC\php-slim-code-gen\gen.php ImportEanDisabled Import 0

rem php F:\50-SC\php-slim-code-gen\gen.php EanPriceStable Import 0

rem php F:\50-SC\php-slim-code-gen\gen.php MagCustomer Magento 0
rem php F:\50-SC\php-slim-code-gen\gen.php MagCustomerAddress Magento 0

php F:\50-SC\php-slim-code-gen\gen.php SysImportRwColMap BasicData 0

rem pause