import { DictCode } from '@/constants';
import { Typography } from 'antd';
import { useModel } from 'umi';

type SIncrementIdLinkToMagAdminProps = {
  entity_id?: number;
  increment_id?: string;
  copyable?: boolean;
};

const SIncrementIdLinkToMagAdmin: React.FC<SIncrementIdLinkToMagAdminProps> = ({
  entity_id,
  increment_id,
  copyable,
}) => {
  const { getDictByCode } = useModel('app-settings');

  return entity_id && increment_id ? (
    <Typography.Link
      href={`${getDictByCode(DictCode.MAG_ADMIN_URL_ORDER_BASE)}/sales/order/view/order_id/${entity_id}/`}
      target="_blank"
      rel="noreferrer"
      copyable={
        copyable
          ? {
              text: `${increment_id}`,
              tooltips: 'Copy Increment ID ' + increment_id,
            }
          : false
      }
    >
      {increment_id}
    </Typography.Link>
  ) : null;
};

export default SIncrementIdLinkToMagAdmin;
