<?php


use App\Lib\Func;
use App\Service\Email\Email\EmailService;
use App\Service\Email\EmailAccount\EmailAccountService;

require __DIR__ . '/../../src/App/App.php';

print_r((strlen('248.0') - strpos('248.0', '.')-1) . PHP_EOL);
print_r((strlen('3.3') - strpos('3.3', '.')-1) . PHP_EOL);
print_r(strrpos('1.3', '.') . PHP_EOL);
print_r(strrpos('0.3', '.') . PHP_EOL);
print_r((strlen('0.3') - strpos('0.3', '.')-1) . PHP_EOL);
print_r((Func::nfAlt(0.5)) . PHP_EOL);
print_r((Func::nfAlt(12)) . PHP_EOL);

/*foreach(\App\Lib\FuncGdsn::NUTRIENT_TYPE_CODES as $key => $value) {
    \App\Models\Gdsn\GdsnCode::updateOrCreate([
        'type' => 'nutrientTypeCode',
        'code' => $key,
        'value' => $value,
    ]);
}*/

exit;

$data = [
    'name_de123' => 'ABC',
    'qty' => '999',
    'address.qty' => '555',
];

$tplStr = "{name_de123} {qty} {address.qty}===";

$patterns = ['/{[\w_\.0-9]+}*/'];

$matches = [];
print_r(preg_match_all($patterns[0], $tplStr, $matches));
print_r($matches);

var_dump(preg_replace_callback($patterns, function ($value) use ($data) {
    return $data[substr($value[0], 1, -1)] ?? $value[0];
}, $tplStr));

// 2. Workflow pattern checking
$tplStr = "{workflowStep.6.desc} {qty} {address.qty}===";

$patterns = ['/{[\w_\.0-9]+}*/'];

$matches = [];
print_r(preg_match_all($patterns[0], $tplStr, $matches));
print_r($matches);

var_dump(preg_replace_callback($patterns, function ($value) use ($data) {
    $fieldDef = substr($value[0], 1, -1);
    var_dump($fieldDef);
    print_r(explode('.', $fieldDef));

    return $data[$fieldDef] ?? $value[0];
}, $tplStr));


exit;


$str = "123456asdfasdfasdfasdfasdfasdf";
$result = Func::mcrypt('encrypt', $str, EmailAccountService::SEC_KEY);
print_r($result . PHP_EOL);

$result = Func::mcrypt('decrypt', $result, EmailAccountService::SEC_KEY);
print_r($result . PHP_EOL);

// exit;

/** @var \Slim\Container $container */
/** @var EmailService $service */
$service = $container->get(EmailService::class);

// $service->popEmailsTest();
$service->popEmails();