import { LS_TOKEN_NAME } from '@/constants';
import { ImportOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadDragger } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import type { UploadChangeParam, UploadFile } from 'antd/lib/upload';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useRef, useState } from 'react';
import _ from 'lodash';

export type PictureImportModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const PictureImportModal: React.FC<PictureImportModalProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    formRef.current?.setFieldsValue({ files: [] });
  }, [props.modalVisible]);

  return (
    <ModalForm
      title={
        <>
          <ImportOutlined /> {` Import pictures`}
        </>
      }
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      modalProps={{
        confirmLoading: loading,
        maskClosable: false,
      }}
      formRef={formRef}
      submitter={false}
      onFinish={async () => {
        // const data = new FormData();
        /* data.set('supplierId', `${values.supplier_id}`);
                if (values?.files) {
                    data.append(`files[]`, values?.files[0].originFileObj as RcFile);
                } */
        /* fileList.forEach(x => {
                    console.log('file ready: ', x);
                    const fileName = x.name;
                    const assumedEan = fileName?.substring(0, 13) || '';
                    // console.log(assumedEan);
                    if (assumedEan && assumedEan.length == 13 && /^[0-9]+$/.test(assumedEan)) {
                        console.log(assumedEan, +assumedEan);
                    }
                }); */
      }}
    >
      <ProFormUploadDragger
        max={8}
        name="files"
        title="Import EAN pictures"
        description="Please select files or drag & drop. File name template: {13 digits}xxxyyy.png"
        accept="image/*"
        className="upload-list-inline"
        fieldProps={{
          multiple: true,
          listType: 'picture-card',
          className: 'upload-list-inline',
          name: 'file',
          action: `${API_URL}/api/item/ean/upload-files`,
          headers: {
            Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
          },
          data: {
            mode: 'pre-files',
          },
          // onPreview: handlePreview,
          beforeUpload: async (file, fList) => {
            const assumedEan = file.name?.substring(0, 13) || '';
            console.log('Assumed list', assumedEan, fList);
            if (assumedEan && assumedEan.length == 13 && /^[0-9]+$/.test(assumedEan)) {
              return Promise.resolve(file);
            } else {
              console.log(' --> skipped', file.name);
              return Promise.reject(false);
            }
          },
          onChange: (info: UploadChangeParam, updateState = true) => {
            // console.log('onChange: ', info.file.status, info.file.name, info.file);
            if (info.file.status == 'done') {
              // console.log('=====> ');
              info.file.url = info.file.response.url;
              info.file.uid = info.file.response.uid;
              (info.file as any).id = info.file.response.uid;
              (info.file as any).file_name = info.file.response.file_name;
              (info.file as any).clean_file_name = info.file.response.clean_file_name;
              (info.file as any).path = info.file.response.path;
              (info.file as any).org_path = info.file.response.org_path;
              (info.file as any).pivot = info.file.response.pivot;

              const newFiles = [...info.fileList];
              formRef.current?.setFieldsValue({ files: newFiles });
              /* const newFiles = formRef.current?.getFieldValue('files') ?? [];
                            newFiles.push(info.file);
                            formRef.current?.setFieldsValue({ files: newFiles }); */
            } else if (typeof info.file.status === 'undefined') {
              const newFiles = formRef.current?.getFieldValue('files') ?? [];
              const index = _.findIndex(newFiles, { uid: info.file.uid });
              console.log('removing by', index);
              if (index > -1) newFiles.splice(index, 1);
              formRef.current?.setFieldsValue({ files: newFiles });
            }
          },
          itemRender: (originNode: React.ReactElement, file: UploadFile) => {
            console.log('itemRenderer', file);
            return (
              <>
                {originNode}
                {file.error && (
                  <div className="c-red text-xs">
                    {file.error.status == 500 ? 'EAN not found: ' + file.name : file.error.message}
                  </div>
                )}
              </>
            );
          },
          style: { marginBottom: 24 },
          onRemove: async () => {
            /* const { confirm } = Modal;
                        return new Promise((resolve, reject) => {
                            confirm({
                                title: file.pivot.is_parent_file
                                    ? 'Are you sure you want to unlink?'
                                    : 'Are you sure you want to delete?',
                                onOk: async () => {
                                    resolve(true);
                                    /* const res = await handleDeleteFile(file);
                                    return res.message; * /
                                },
                                onCancel: () => {
                                    reject(true);
                                },
                            });
                        }); */
          },
        }}
      />
    </ModalForm>
  );
};
export default PictureImportModal;
