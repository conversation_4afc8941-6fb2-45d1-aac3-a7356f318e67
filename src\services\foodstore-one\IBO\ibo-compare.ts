import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/ibo/ibo-compare';

export type IboCompare = Partial<API.Ibo> & {
  sum_qty_pre?: number;
  avg_price_pre?: number;
  sum_price_pre?: number;

  sum_qty_pre_inv?: number;
  avg_price_pre_inv?: number;
  sum_price_pre_inv?: number;

  sum_qty_pre_done?: number;
  avg_price_pre_done?: number;
  sum_price_pre_done?: number;

  sum_qty?: number;
  avg_price?: number;
  sum_price?: number;
};

/** get GET /api/ibo/ibo-compare */
export async function getIboCompareList(params: API.PageParams, sort: any, filter: any) {
  return request<API.ResultList<IboCompare>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
