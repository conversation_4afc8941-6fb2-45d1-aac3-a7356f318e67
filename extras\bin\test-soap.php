<?php


use DiDom\Document;
use DiDom\Element;
use DiDom\Query;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';


// --------------------------------------
// Preparing API request as XML
// --------------------------------------
$d = new Document();
$storeOrders = new Element('ns1:storeOrders');

// 1. print options
$printOption = new Element("printOption");
$printOption->appendChild(new Element("outputFormat", 'PDF'));
$printOption->appendChild(new Element("paperFormat", 'A6'));

$printOptions = new Element("printOptions");
$printOptions->appendChild($printOption);

$storeOrders->appendChild($printOptions);


// 2. order
$order = new Element('order');
$generalShipmentData = new Element('generalShipmentData');
// $generalShipmentData->appendChild(new Element('identificationNumber', 8888));
$generalShipmentData->appendChild(new Element('sendingDepot', '0998'));
$generalShipmentData->appendChild(new Element('product', 'CL'));
$generalShipmentData->appendChild(new Element('mpsCompleteDelivery', 0));
$generalShipmentData->appendChild(new Element('mpsWeight', 300));

// 2.1 Sender
$sender = new Element('sender');
$sender->appendChild(new Element('name1', 'Abholer1'));
$sender->appendChild(new Element('street', 'Street 1'));
$sender->appendChild(new Element('country', 'DE'));
$sender->appendChild(new Element('zipCode', '11111'));
$sender->appendChild(new Element('city', 'Ort1'));
$sender->appendChild(new Element('customerNumber', '12345679'));
$generalShipmentData->appendChild($sender);

$recipient = new Element('recipient');
$recipient->appendChild(new Element('name1', 'Abholer1'));
$recipient->appendChild(new Element('street', 'Street 1'));
$recipient->appendChild(new Element('state', 'BY'));
$recipient->appendChild(new Element('country', 'DE'));
$recipient->appendChild(new Element('zipCode', '11111'));
$recipient->appendChild(new Element('city', 'Ort1'));
$generalShipmentData->appendChild($recipient);

// 3. parcels
$parcels = new Element('parcels');
$parcels->appendChild(new Element('customerReferenceNumber1', 'customerReferenceNumber1'));
$parcels->appendChild(new Element('customerReferenceNumber2', 'customerReferenceNumber2'));

// 4. productAndServiceData
$productAndServiceData = new Element('productAndServiceData');
$productAndServiceData->appendChild(new Element('orderType', 'consignment'));

$order->appendChild($generalShipmentData);
$order->appendChild($parcels);
$order->appendChild($productAndServiceData);


$storeOrders->appendChild($order);

$d->appendChild($storeOrders);

$str = $d->html();

var_dump($str);

// $test = new Element('soapenv:Envelope', null, ['xmlns:soapenv' => 'http://schemas.xmlsoap.org/soap/envelope/']);
// var_dump($test->html());

exit;


