import { <PERSON><PERSON>ontainer } from '@ant-design/pro-layout';
import { Card, Space } from 'antd';
import styles from './style.less';
import type { ActionType, ProColumnType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useEffect, useRef, useState } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import Util, { nf2, ni, sn } from '@/util';
import { getPickingTimeStatsSummary } from '@/services/foodstore-one/Warehouse/picklist-time-track';
import SProFormDateRange, { DRSelection } from '@/components/SProFormDateRange';
import { useModel } from 'umi';

type RecordType = {
  uid?: string;
  dt?: string;
  max_created_at?: string;
  total_cnt?: number;
  s_total_cnt?: number;
  m_total_cnt?: number;
  ms_total_cnt?: number;

  minutes?: number;
  pre_pick_minutes?: number;
  pick_minutes?: number;
  pack_minutes?: number;

  pre_pick_start_dt?: string;
  pick_start_dt?: string;
  pack_start_dt?: string;

  pre_pick_end_dt?: string;
  pick_end_dt?: string;
  pack_end_dt?: string;
  duration_minutes?: number;

  min_start_dt?: string;
  max_end_dt?: string;
  max_qty_employee?: number;
};

type SearchFormValueType = {
  deleteMe?: any;
};

const PicklistTimeTrack: React.FC<any> = (props) => {
  const {
    appSettings: { drSelection },
  } = useModel('app-settings');

  const [loading, setLoading] = useState<boolean>(false);
  // Search forms
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();

  const actionRef = useRef<ActionType>();

  const [avgRow, setAvgRow] = useState<RecordType | null>(null);

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'Date',
      dataIndex: 'dt',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      defaultSortOrder: 'descend',
      width: 100,
      tooltip: 'Picklist date',
      showSorterTooltip: false,
      render(__, entity) {
        return Util.dtToDMY(entity.dt);
      },
    },
    {
      title: 'Multi',
      dataIndex: 'm_total_cnt',
      align: 'right',
      width: 70,
      className: 'bl2',
      render: (__, entity) => ni(entity.m_total_cnt),
    },
    {
      title: 'Multi + Single',
      dataIndex: 'ms_total_cnt',
      align: 'right',
      width: 80,
      render: (__, entity) => ni(entity.ms_total_cnt),
    },
    {
      title: 'Single',
      dataIndex: 's_total_cnt',
      align: 'right',
      width: 70,
      render: (__, entity) => ni(entity.s_total_cnt),
    },
    {
      title: (config: ProColumnType, __: any, ___: any) => (
        <>
          Gesamt
          {avgRow && <div className="avgCol">{ni(avgRow.total_cnt)}</div>}
        </>
      ),
      dataIndex: 'total_cnt',
      align: 'right',
      width: 70,
      className: 'bl2',
      render: (__, entity) => ni(entity.total_cnt),
    },

    {
      title: 'PrePick',
      dataIndex: 'pre_pick_minutes',
      align: 'right',
      width: 80,
      className: 'bl2',
      render: (__, entity) => ni(entity.pre_pick_minutes),
    },
    {
      title: 'Pick',
      dataIndex: 'pick_minutes',
      align: 'right',
      width: 80,
      render: (__, entity) => ni(entity.pick_minutes),
    },
    {
      title: 'Pack',
      dataIndex: 'pack_minutes',
      align: 'right',
      width: 80,
      render: (__, entity) => ni(entity.pack_minutes),
    },
    {
      title: 'Gesamt',
      dataIndex: 'minutes',
      align: 'right',
      width: 80,
      className: 'bl2',
      render: (__, entity) => ni(entity.minutes),
    },

    {
      title: (config: ProColumnType, __: any, ___: any) => (
        <>
          PrePick
          {avgRow && (
            <div className="avgCol">
              {sn(avgRow.total_cnt) ? nf2(sn(avgRow.pre_pick_minutes) / sn(avgRow.total_cnt)) : null}
            </div>
          )}
        </>
      ),
      dataIndex: 'pre_pick_minutes',
      align: 'right',
      width: 80,
      className: 'bl2',
      render: (__, entity) => (sn(entity.total_cnt) ? nf2(sn(entity.pre_pick_minutes) / sn(entity.total_cnt)) : null),
    },
    {
      title: (config: ProColumnType, __: any, ___: any) => (
        <>
          Pick
          {avgRow && (
            <div className="avgCol">
              {sn(avgRow.total_cnt) ? nf2(sn(avgRow.pick_minutes) / sn(avgRow.total_cnt)) : null}
            </div>
          )}
        </>
      ),
      dataIndex: 'pick_minutes',
      align: 'right',
      width: 80,
      render: (__, entity) => (sn(entity.total_cnt) ? nf2(sn(entity.pick_minutes) / sn(entity.total_cnt)) : null),
    },
    {
      title: (config: ProColumnType, __: any, ___: any) => (
        <>
          Pack
          {avgRow && (
            <div className="avgCol">
              {sn(avgRow.total_cnt) ? nf2(sn(avgRow.pack_minutes) / sn(avgRow.total_cnt)) : null}
            </div>
          )}
        </>
      ),
      dataIndex: 'pack_minutes',
      align: 'right',
      width: 80,
      render: (__, entity) => (sn(entity.total_cnt) ? nf2(sn(entity.pack_minutes) / sn(entity.total_cnt)) : null),
    },
    {
      title: (config: ProColumnType, __: any, ___: any) => (
        <>
          Gesamt
          {avgRow && (
            <div className="avgCol">{sn(avgRow.total_cnt) ? nf2(sn(avgRow.minutes) / sn(avgRow.total_cnt)) : null}</div>
          )}
        </>
      ),
      dataIndex: 'minutes',
      align: 'right',
      width: 80,
      className: 'bl2',
      render: (__, entity) => (sn(entity.total_cnt) ? nf2(sn(entity.minutes) / sn(entity.total_cnt)) : null),
    },
    {
      title: '',
      valueType: 'option',
      dataIndex: 'empty1',
      width: 40,
    },
    /* {
      title: 'Start time',
      dataIndex: 'start_dt_section',
      align: 'center',
      width: 80,
      className: 'bl2',
      children: [
        {
          title: 'PrePick',
          dataIndex: 'pre_pick_start_dt',
          align: 'center',
          width: 110,
          className: 'bl2 text-sm',
          render: (__, entity) => Util.dtToDMYHHMM(entity.pre_pick_start_dt),
        },
        {
          title: 'Pick',
          dataIndex: 'pick_start_dt',
          align: 'center',
          width: 110,
          className: 'text-sm',
          render: (__, entity) => Util.dtToDMYHHMM(entity.pick_start_dt),
        },
        {
          title: 'Pack',
          dataIndex: 'pack_start_dt',
          align: 'center',
          width: 110,
          className: 'text-sm',
          render: (__, entity) => Util.dtToDMYHHMM(entity.pack_start_dt),
        },
      ],
    },
    {
      title: 'End time',
      dataIndex: 'end_dt_section',
      align: 'center',
      width: 80,
      className: 'bl2',
      children: [
        {
          title: 'PrePick',
          dataIndex: 'pre_pick_end_dt',
          align: 'center',
          width: 110,
          className: 'bl2 text-sm',
          render: (__, entity) => Util.dtToDMYHHMM(entity.pre_pick_end_dt),
        },
        {
          title: 'Pick',
          dataIndex: 'pick_end_dt',
          align: 'center',
          width: 110,
          className: 'text-sm',
          render: (__, entity) => Util.dtToDMYHHMM(entity.pick_end_dt),
        },
        {
          title: 'Pack',
          dataIndex: 'pack_end_dt',
          align: 'center',
          width: 110,
          className: 'text-sm',
          render: (__, entity) => Util.dtToDMYHHMM(entity.pack_end_dt),
        },
      ],
    }, */
    {
      title: 'Start Time',
      dataIndex: 'min_start_dt',
      align: 'center',
      width: 90,
      className: 'bl2',
      render: (__, entity) => Util.dtToHHMM(entity.min_start_dt),
    },
    {
      title: 'End Time',
      dataIndex: 'max_end_dt',
      align: 'center',
      width: 90,
      render: (__, entity) => Util.dtToHHMM(entity.max_end_dt),
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      align: 'center',
      width: 300,
      className: 'text-sm',
      render: (__, entity) =>
        sn(entity.duration_minutes) ? (
          <Space size={4}>
            <span>{nf2(sn(entity.duration_minutes) / 60)} h</span>
            {sn(entity.total_cnt) ? (
              <>
                {' '}
                / <span className="italic">{nf2(sn(entity.duration_minutes) / sn(entity.total_cnt))} min.</span>
              </>
            ) : null}
            {sn(entity.max_qty_employee) > 0 && <span>({entity.max_qty_employee})</span>}
            {sn(entity.total_cnt) ? (
              <>
                {' '}
                /{' '}
                <span className="c-dark-purple">
                  {nf2((sn(entity.duration_minutes) / sn(entity.total_cnt)) * sn(entity.max_qty_employee))} min.
                </span>
              </>
            ) : null}
          </Space>
        ) : null,
    },
    {
      title: '',
      valueType: 'option',
      dataIndex: 'picklist_ids',
    },
  ];

  useEffect(() => {
    const savedSelection = Util.getSfValues('sf_picking_times')?.dr_selection;

    if (!savedSelection) {
      const initialDrSelection = DRSelection.DR_LAST_30_DAYS;
      const range = drSelection[initialDrSelection as any];
      searchFormRef?.current?.setFieldValue('dr_selection', initialDrSelection);
      if (range) {
        searchFormRef?.current?.setFieldValue('start_date', range[0] ? range[0] : null);
        searchFormRef?.current?.setFieldValue('end_date', range[1] ? range[1] : null);
        actionRef?.current?.reload();
      }
    }
  }, [drSelection]);

  return (
    <PageContainer className={styles.pickingTime}>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_picking_times')}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue({});
              actionRef.current?.reload();
            },
          }}
        >
          <SProFormDateRange label="Date" formRef={searchFormRef} style={{ marginLeft: 16 }} disabled={loading} />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={`Picking Time`}
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        bordered
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_picking_times', searchFormValues);
          setLoading(true);
          return getPickingTimeStatsSummary(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              setAvgRow(res.avgRow);
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: 100,
        }}
        columns={columns}
        columnEmptyText=""
        rowSelection={false}
        rowClassName={(record) => {
          return '';
        }}
      />
    </PageContainer>
  );
};

export default PicklistTimeTrack;
