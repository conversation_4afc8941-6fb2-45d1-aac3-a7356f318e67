import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, Row, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { updateIbo } from '@/services/foodstore-one/IBO/ibo';
import SProFormDigit from '@/components/SProFormDigit';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateIbo(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Ibo>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Ibo>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ibo) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;

  buyingHistoryComp?: JSX.Element;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update IBO'}
      width="1000px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      isKeyPressSubmit={true}
      modalProps={{ okText: 'Update' }}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <Row gutter={32} wrap={false}>
        <Col>
          <SProFormDigit
            width="sm"
            name="price"
            label="Order price (pcs)"
            addonAfter="€"
            required
            rules={[
              {
                required: true,
                message: 'Order price is required',
              },
            ]}
            fieldProps={{ precision: 3 }}
          />
        </Col>
        <Col flex="auto">{props.buyingHistoryComp}</Col>
      </Row>
    </ModalForm>
  );
};

export default UpdateForm;
