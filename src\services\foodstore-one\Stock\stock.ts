/* eslint-disable */
import { StockMovementReason } from '@/constants';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/stock';

export type StockCorrectionParamType = {
  ean_id?: number;
  new_wl_id?: number;
  exp_date?: string;

  box_qty_edit?: number;
  piece_qty_edit?: number;
  reason: StockMovementReason;
};

export type StockMoveParamType = {
  ean_id?: number;
  new_wl_id?: number;
  exp_date?: string;

  old_wl_id?: number;

  box_qty_edit?: number;
  piece_qty_edit?: number;
  reason?: StockMovementReason;

  ibo_id?: number; // @since 2023-04-18, because we added an ibo into stock_stable table.
};

/** stock correction PUT /api/stock/correction */
export async function stockCorrection(data: StockCorrectionParamType, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/correction`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}

/** stock move PUT /api/stock/move */
export async function stockMove(data: StockMoveParamType, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/move`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  });
}
