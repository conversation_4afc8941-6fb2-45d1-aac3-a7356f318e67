import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import type { StockStableRowType } from './StockStableCorrectionEditableTable';
import type { StockStableMoveParamType } from '@/services/foodstore-one/Stock/stock-stable';
import { updateStockStableExpDate } from '@/services/foodstore-one/Stock/stock-stable';
import SDatePicker from '@/components/SDatePicker';
import { SizeType } from '@ant-design/pro-form/lib/BaseForm';

export type FormValueType = Partial<StockStableMoveParamType>;

const handleUpdateExpDate = async (fields: FormValueType) => {
  const hide = message.loading('Updating Exp. Date...', 0);

  try {
    await updateStockStableExpDate(fields);
    hide();
    message.success('Updated successfuly.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type StockStableUpdateExpFormProps = {
  ean?: Partial<API.Ean>;
  initialData?: Partial<StockStableRowType>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: StockStableRowType) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  formSize?: SizeType;
};

const StockStableUpdateExpForm: React.FC<StockStableUpdateExpFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (props.modalVisible) {
      formRef.current?.setFieldValue('new_exp_date', props.initialData?.exp_date);
    }
  }, [props.modalVisible, props.initialData?.exp_date]);

  return (
    <ModalForm<FormValueType>
      title={'Update Exp. Date'}
      width={500}
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      size={props.formSize}
      className={props.ean?.is_single ? 'm-single' : 'm-multi'}
      // modalProps={{ centered: true }}
      onFinish={async (value) => {
        const newData = {
          ...value,
          id: props.initialData?.id,
        };
        const success = await handleUpdateExpDate(newData);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProForm.Item label="Exp. Date" name={'new_exp_date'}>
        <SDatePicker getValueFromEvent={(value) => Util.dtToYMD(value)} />
      </ProForm.Item>
    </ModalForm>
  );
};

export default StockStableUpdateExpForm;
