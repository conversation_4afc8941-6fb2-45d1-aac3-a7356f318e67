drop table if exists `import_supplier_data`;

CREATE TABLE `import_supplier_data`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `supplier_id`      BIGINT unsigned DEFAULT NULL,
    `ean`              varchar(100)    DEFAULT NULL,
    `name`             varchar(255)    DEFAULT NULL,
    `article_no`       varchar(255)    DEFAULT NULL,
    `multi_ean`        varchar(50)     DEFAULT NULL,
    `qty`              double          DEFAULT 0,
    `case_qty`         double          DEFAULT 0,
    `hs_code`          varchar(30)     DEFAULT NULL,
    `trademark`        varchar(255)    DEFAULT NULL,
    `vat`              varchar(10)     DEFAULT NULL,
    `price`            double          DEFAULT 0,
    `uvp`              double          DEFAULT 0,
    `price_pallet`     double          DEFAULT 0,
    `ve_pallet`        double          DEFAULT 0,
    `exp_date`         varchar(30)     DEFAULT NULL,
    `box_qty`          double          DEFAULT 0,
    `maker`            varchar(255)    DEFAULT NULL,
    `shelf_life`       int(11)         DEFAULT NULL,
    `gln`              varchar(255)    DEFAULT NULL,
    `price_valid_from` date            DEFAULT NULL,
    `price_valid_to`   date            DEFAULT NULL,
    `bbd`              date            DEFAULT NULL,
    `category`         varchar(255)    DEFAULT NULL,
    `nan`              varchar(255)    DEFAULT NULL,
    `created_on`       datetime        DEFAULT NULL,
    `created_by`       int             DEFAULT NULL,
    `updated_on`       datetime        DEFAULT NULL,
    `updated_by`       int             DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_import_supplier_data_ean` (`ean`),
    KEY `IDX_import_supplier_data_article_no` (`article_no`),
    KEY `FK_import_supplier_data_supplier_id` (`supplier_id`),
    CONSTRAINT `FK_import_supplier_data_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE set null ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='supplier master data';

drop table if exists `import_supplier_data_price`;

CREATE TABLE `import_supplier_data_price`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `supplier_id` BIGINT unsigned DEFAULT NULL,
    `ean`         varchar(100)    DEFAULT NULL,
    `price`       double          DEFAULT 0,
    `uvp`         double          DEFAULT 0,
    `vat`         varchar(10)     DEFAULT NULL,
    `created_on`  datetime        DEFAULT NULL,
    `created_by`  int             DEFAULT NULL,
    `updated_on`  datetime        DEFAULT NULL,
    `updated_by`  int             DEFAULT NULL,
    `deleted_on`  datetime        DEFAULT NULL,
    `ref_id`      BIGINT unsigned DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_import_supplier_data_price_ean` (`ean`),
    KEY `FK_import_supplier_data_price_supplier_id` (`supplier_id`),
    KEY `FK_import_supplier_data_price_ref_id` (`ref_id`),
    CONSTRAINT `FK_import_supplier_data_price_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE set null ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

