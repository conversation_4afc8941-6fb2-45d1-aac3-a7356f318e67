DELIMITER $$

ALTER ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_stock_stable_aggregated` AS (
    SELECT `s`.`item_id`                                                        AS `item_id`,
           `s`.`ean_id`                                                         AS `ean_id`,
           `s`.`parent_ean_id`                                                  AS `parent_ean_id`,
           IF(`s`.`parent_ean_id` = `s`.`ean_id`,
              (SELECT SUM(`stock_stable`.`total_piece_qty`)
               FROM `stock_stable`
               WHERE `stock_stable`.`item_id` = `s`.`item_id`),
              SUM(`s`.`box_qty`))                                               AS `mix_qty`,
           SUM(`s`.`piece_qty`)                                                 AS `piece_qty`,
           SUM(`s`.`box_qty`)                                                   AS `box_qty`,
           SUM(`s`.`total_piece_qty`)                                           AS `total_piece_qty`,
           IF(`s`.`parent_ean_id` = `s`.`ean_id`,
              (SELECT SUM(`stock_stable`.`total_piece_qty`)
               FROM `stock_stable`
               WHERE `stock_stable`.`item_id` = `s`.`item_id`),
              SUM(`s`.`total_piece_qty`))                                       AS `mix_total_piece_qty`,
           MIN(`s`.`exp_date`)                                                  AS `min_exp_date`,
           IF(`s`.`parent_ean_id` = `s`.`ean_id`,
              (SELECT MIN(`stock_stable`.`exp_date`)
               FROM `stock_stable`
               WHERE `stock_stable`.`total_piece_qty` <> 0
                 AND `stock_stable`.`item_id` = `s`.`item_id`),
              MIN(`s`.`exp_date`))                                              AS `mix_min_exp_date`,
           IFNULL((SELECT ibo.price
                   FROM `ibo`
                   WHERE `s`.`ibo_id` = `ibo`.`id`),
                  0)                                                            AS `bp_pcs`,

           (SUM((SELECT ibo.price
                    FROM `ibo`
                    WHERE `s`.`ibo_id` = `ibo`.`id`) * `s`.`total_piece_qty`)) AS bp,
           IF(`s`.`parent_ean_id` = `s`.`ean_id`,
              (SELECT SUM(`stock_stable`.`total_piece_qty` * ibo.price)
               FROM `stock_stable`
                        INNER JOIN ibo ON ibo.id = stock_stable.ibo_id
               WHERE `stock_stable`.`item_id` = `s`.`item_id`),
              (SUM((SELECT ibo.price
                    FROM `ibo`
                    WHERE `s`.`ibo_id` = `ibo`.`id`) * `s`.`total_piece_qty`))) AS mix_bp,

           (SELECT SUM(`m`.`quantity`)
            FROM `xmag_inventory_stock` `m`
            WHERE `m`.`ean_id` = `s`.`ean_id`)                                  AS `mag_qty`,
           (SELECT SUM(`m`.`res_quantity`)
            FROM `xmag_inventory_stock` `m`
            WHERE `m`.`ean_id` = `s`.`ean_id`)                                  AS `mag_res_qty`,
           (SELECT SUM(`m`.`res_cal`)
            FROM `xmag_inventory_stock` `m`
            WHERE `m`.`ean_id` = `s`.`ean_id`)                                  AS `mag_res_cal`
    FROM `stock_stable` `s`
    WHERE `s`.`total_piece_qty` <> 0
    GROUP BY `s`.`ean_id`)$$

DELIMITER ;