CREATE TABLE `warehouse_picklist_ship` (
                                           `order_item_id` int(11) NOT NULL COMMENT 'Order Item ID',
                                           `picklist_id` int(11) NOT NULL COMMENT 'Picklist ID',
                                           `order_id` int(11) NOT NULL COMMENT 'Order ID',
                                           `qty` int(11) DEFAULT NULL COMMENT 'order qty ordered',
                                           `mag_ship_id` int(11) DEFAULT NULL COMMENT 'Shipment ID in Magento',
                                           `created_on` datetime DEFAULT NULL COMMENT 'Created date time',
                                           `created_by` int(11) DEFAULT NULL COMMENT 'Creator ID',
                                           PRIMARY KEY (`order_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `stock_stable_us_log` (
                                       `item_id` bigint(20) unsigned NOT NULL COMMENT 'Item ID',
                                       `ean_id` bigint(20) unsigned NOT NULL COMMENT 'PK: EAN ID',
                                       `last_us_qty` int(11) DEFAULT NULL COMMENT 'Qty: last synced qty',
                                       `updated_on` datetime DEFAULT NULL,
                                       `updated_by` int(11) DEFAULT NULL,
                                       PRIMARY KEY (`ean_id`),
                                       KEY `FK_stock_stable_us_log_item_id` (`item_id`),
                                       CONSTRAINT `FK_stock_stable_us_log_ean_id` FOREIGN KEY (`ean_id`) REFERENCES `item_ean` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                                       CONSTRAINT `FK_stock_stable_us_log_item_id` FOREIGN KEY (`item_id`) REFERENCES `item_ean` (`item_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Log to track the last up synced quantities. Used to check the up syncable status';
