import { <PERSON><PERSON>, <PERSON>, Divider, <PERSON>, <PERSON>, Spin, Typography } from 'antd';
import { Card } from 'antd';
import { message } from 'antd';
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';

import Util, { sn } from '@/util';
import { getEanList, updateEanAttributePartial } from '@/services/foodstore-one/Item/ean';
import _ from 'lodash';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormDependency,
  ProFormField,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';

import styles from './EanDetail.less';

import { IRouteComponentProps, useLocation } from 'umi';
import useGdsnItem from './hooks/useGdsnItem';
import SocialLinks from './components/SocialIcons';
import GdsnItemButton from './components/GdsnItemButton';
import useUpdateModalActions from './hooks/useUpdateModalActions';
import UpdatePicturesFormEditor from './components/UpdatePicturesFormEditor';
import OpenAiTextGenerator from './components/OpenAiTextGenerator';
import useTrademarkFormFilter from './hooks/useTrademarkFormFilter';
import type { SkuChangeCallbackHandlerTypeParamType } from './hooks/useSkuNavigator';
import useSkuNavigator from './hooks/useSkuNavigator';

const handleUpdate = async (fields: FormValueType) => {
  const data = new FormData();
  const texts: API.EanText[] = [];
  if (fields?.ean_texts && fields?.ean_texts.length) {
    let ind = 0;
    for (const tmp of fields.ean_texts) {
      /* const i_pic_obj = tmp.i_pic?.[0]?.originFileObj ?? tmp.i_pic?.[0]?.id ?? '';
      const n_pic_obj = tmp.n_pic?.[0]?.originFileObj ?? tmp.n_pic?.[0]?.id ?? ''; */

      const tmpClone = { ...tmp };
      /*data.append(`textsFiles[${ind}][i_pic]`, i_pic_obj);
      delete tmpClone.i_pic;

      data.append(`textsFiles[${ind}][n_pic]`, n_pic_obj);
      delete tmpClone.n_pic; */

      texts.push({
        ...tmpClone,
        // @ts-ignore
        // official_producer: tmp?.official_producer?.id ?? tmp?.official_producer ?? '',
      });
      ind++;
    }
  }
  const hide = message.loading('Updating...', 0);

  data.append('id', `${fields.id}`);
  data.append('mode', 'texts');
  data.append('ean_texts', JSON.stringify(texts));

  try {
    const res = await updateEanAttributePartial(data as API.Ean);
    hide();
    message.success('Updated successfully.');
    if (res.upSyncMessage) {
      message.error('Up sync error: ' + res.upSyncMessage);
    }
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!');
    return false;
  }
};

type SearchFormValueType = Partial<API.Ean>;
type FormValueType = {
  upSync?: string;
  texts?: API.EanText[];
} & Partial<API.Ean>;

const defaultInitialValues: API.Ean = {
  ean_texts: [
    {
      lang: 'DE',
      i_pic: [],
      n_pic: [],
    },
  ],
};

export type EanSummaryComponentProps = IRouteComponentProps & {
  eanType?: 'default' | 'base' | 've';
};

const EanDetail: React.FC<EanSummaryComponentProps> = (eanComponentProps) => {
  // const { appSettings } = useModel('app-settings');
  const location: any = useLocation();

  const searchFormRef = useRef<ProFormInstance>();
  const formRef = useRef<ProFormInstance<FormValueType>>();
  const formRefSib = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);
  const [itemEan, setItemEan] = useState<API.Ean>();
  const isSingleEan = !!itemEan?.is_single;

  const [picFormInitialValues, setPicFormInitialValues] = useState<Partial<API.Ean>>();

  // GDSN data
  const { gdsnItem, fetchGdsnItem, renderTakeButton } = useGdsnItem(itemEan?.ean, !!itemEan?.ean);

  const [gdsnItemsDiff, setGdsnItemsDiff] = useState<any>({});
  const validateGdsnItemsDiff = useCallback(() => {
    const values = formRef.current?.getFieldsValue() ?? {};
    setGdsnItemsDiff({
      name: values.ean_texts?.[0]?.name == gdsnItem?.name,
      short_description: values.ean_texts?.[0]?.short_description == gdsnItem?.detail?.short_description,
      description1: values.ean_texts?.[0]?.description1 == gdsnItem?.detail?.description1,
      meta_keywords: values.ean_texts?.[0]?.meta_keywords == gdsnItem?.detail?.tradeItemKeyWords,
      official_usage: values.ean_texts?.[0]?.official_usage == gdsnItem?.detail?.storage_instruction,
      official_warning: values.ean_texts?.[0]?.official_warning == gdsnItem?.detail?.health_compulsory,
      official_country: values.ean_texts?.[0]?.official_country == gdsnItem?.detail?.country_code,
      official_ingredients:
        values.ean_texts?.[0]?.official_ingredients == gdsnItem?.detail?.ean_text?.official_ingredients,
      official_title: values.ean_texts?.[0]?.official_title == gdsnItem?.detail?.ean_text?.official_title,
      official_nutrition: values.ean_texts?.[0]?.official_nutrition == gdsnItem?.detail?.ean_text?.official_nutrition,
    });
  }, [
    gdsnItem?.name,
    gdsnItem?.detail?.short_description,
    gdsnItem?.detail?.description1,
    gdsnItem?.detail?.tradeItemKeyWords,
    gdsnItem?.detail?.storage_instruction,
    gdsnItem?.detail?.health_compulsory,
    gdsnItem?.detail?.country_code,
    gdsnItem?.detail?.ean_text?.official_ingredients,
    gdsnItem?.detail?.ean_text?.official_title,
    gdsnItem?.detail?.ean_text?.official_nutrition,
  ]);

  useEffect(() => {
    if (itemEan?.ean) {
      validateGdsnItemsDiff();
    }
  }, [itemEan?.ean, validateGdsnItemsDiff]);

  const loadEanDetail = useCallback(async () => {
    const searchFormValues = searchFormRef.current?.getFieldsValue();
    Util.setSfValues('sf_ean_detail', searchFormValues);
    if (searchFormValues.eanExact || searchFormValues.sku) {
      setLoading(true);
      getEanList({
        ...searchFormValues,
        trademark: undefined,
        pageSize: 1,
        with: 'item,texts,parentTexts,parent,usHashSummary,gdsnItem,siblings.eanTextDe',
      })
        .then((res) => {
          const selItem = res.data[0];
          searchFormRef.current?.setFieldValue('sku', selItem?.sku);
          setItemEan(selItem);
        })
        .finally(() => setLoading(false));
    } else {
      setItemEan(undefined);
    }
  }, []);

  useEffect(() => {
    loadEanDetail();
  }, [loadEanDetail]);

  const { formElements: formElementsTrademark } = useTrademarkFormFilter(searchFormRef.current, null, {
    parentLoading: loading,
    label: 'Trademark for SKU',
  });

  const skuChangeCallbackHandler = useCallback(
    (type: SkuChangeCallbackHandlerTypeParamType) => {
      if (type == 'reload') {
        loadEanDetail();
      }
    },
    [loadEanDetail],
  );
  const { formElements: formElementsSkuNavigator } = useSkuNavigator(
    searchFormRef.current,
    skuChangeCallbackHandler as any,
    {
      parentLoading: loading,
    },
  );

  useEffect(() => {
    if (formRef.current && itemEan?.id) {
      const newValues: API.Ean = { ...defaultInitialValues, ...itemEan };

      if (!newValues.ean_texts || newValues.ean_texts.length < 1) {
        newValues.ean_texts = defaultInitialValues.ean_texts;
      }

      if (!isSingleEan) {
        const parentText = newValues.parent?.ean_texts?.[0];
        for (const text of newValues.ean_texts || []) {
          text.official_country = parentText?.official_country;
          text.official_ingredients = parentText?.official_ingredients;
          text.official_nutrition = parentText?.official_nutrition;
          text.official_producer = parentText?.official_producer;
          text.official_producer_obj = parentText?.official_producer_obj;
          text.official_title = parentText?.official_title;
          text.official_title_ebay = parentText?.official_title_ebay;
          text.official_usage = parentText?.official_usage;
          text.official_warning = parentText?.official_warning;
          // text.ingredients_pic = parentText?.ingredients_pic;
          // text.nutrition_pic = parentText?.nutrition_pic;

          // Check override mode
          if (text.settings?.useOwnText) {
          } else {
            text.short_description = parentText?.short_description;
            text.description1 = parentText?.description1;
            text.meta_title = parentText?.meta_title;
            text.meta_keywords = parentText?.meta_keywords;
            text.meta_description = parentText?.meta_description;
            text.fs_export_google_description = parentText?.fs_export_google_description;
          }
        }
      }

      formRef.current?.resetFields();
      formRef.current.setFieldsValue(newValues);
    } else {
      formRef.current?.resetFields();
      formRef.current?.setFieldsValue({ ...defaultInitialValues });
    }

    formRefSib.current?.resetFields();
    if (formRefSib.current && itemEan?.id) {
      const newValues: API.Ean = {
        siblings: itemEan.siblings
          ?.filter((x) => !x.is_single)
          ?.map((x) => ({
            id: x.id,
            item_id: x.item_id,
            parent_id: x.parent_id,
            sku: x.sku,
            attr_case_qty: x.attr_case_qty,
            ean_text_de: x.ean_text_de
              ? {
                  lang: x.ean_text_de?.lang || 'DE',
                  name: x.ean_text_de?.name || '',
                }
              : {
                  lang: 'DE',
                  name: '',
                },
          })),
      };
      formRefSib.current?.setFieldsValue(newValues);
    } else {
      formRefSib.current?.setFieldsValue({ siblings: [] });
    }
  }, [itemEan, isSingleEan]);

  useEffect(() => {
    setPicFormInitialValues({
      id: itemEan?.id,
      parent_id: itemEan?.parent_id,
      is_single: itemEan?.is_single,
      sku: itemEan?.sku,
      ean: itemEan?.ean,
    });
  }, [itemEan?.ean, itemEan?.id, itemEan?.is_single, itemEan?.parent_id, itemEan?.sku]);

  // Form extra actions
  const { actionButtons, hiddenFormElements, runActionsCallback, renderActionButtons } = useUpdateModalActions(
    itemEan?.id ?? 0,
    itemEan?.sku ?? '',
    formRef.current,
  );

  const isTextEditable = itemEan?.is_single || !!itemEan?.ean_texts?.[0]?.settings?.useOwnText;

  return (
    <PageContainer
      className={styles.eanDetailContainer}
      title={
        <Row wrap={false}>
          <Col flex="250px">EAN Detail</Col>
          <Col flex="auto">
            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              initialValues={Util.getSfValues('sf_ean_detail', {}, { sku: location.query?.sku ?? undefined })}
              submitter={{
                submitButtonProps: { loading: loading, htmlType: 'submit' },
                searchConfig: { submitText: 'Search' },
                onSubmit: () => {
                  loadEanDetail();
                },
                render: (form, dom) => {
                  return [dom[1]];
                },
              }}
            >
              <ProFormText name={'eanExact'} label="EAN" width="sm" placeholder={'EAN'} />
              <ProFormText
                name={'sku'}
                label="SKU"
                width={130}
                placeholder={'SKU'}
                addonAfter={formElementsSkuNavigator}
              />
              {formElementsTrademark}
            </ProForm>
          </Col>
        </Row>
      }
    >
      <Card
        style={{ marginBottom: 16 }}
        bodyStyle={{ paddingTop: 0 }}
        bordered={false}
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>Update EAN Info -&nbsp;</span>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.ean || '',
                tooltips: 'Copy EAN ' + (itemEan?.ean || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0 }}
            >
              {itemEan?.ean || ''}
            </Typography.Paragraph>
            <Typography.Paragraph
              copyable={{
                text: itemEan?.sku || '',
                tooltips: 'Copy SKU ' + (itemEan?.sku || ''),
              }}
              style={{ display: 'inline-block', marginBottom: 0, marginLeft: 50 }}
            >
              {itemEan?.sku || ''}
            </Typography.Paragraph>
            <SocialLinks ean={itemEan?.ean || ''} title={itemEan?.ean_texts?.[0]?.name} style={{ marginLeft: 50 }} />

            <GdsnItemButton
              ean={itemEan?.ean}
              eanId={itemEan?.id}
              itemId={itemEan?.item_id}
              style={{ marginLeft: 30 }}
              fetchGdsnItem={fetchGdsnItem}
            />

            {actionButtons}
          </div>
        }
      >
        <div style={{ display: 'none' }}>{hiddenFormElements}</div>
        <Row gutter={48}>
          <Col span={8}>
            <Spin spinning={loading}>
              <ProForm<FormValueType>
                formRef={formRef}
                isKeyPressSubmit
                submitter={{
                  render: (form, dom) => {
                    return [];
                  },
                }}
                onFinish={async (values) => {
                  if (formRef.current?.isFieldsTouched()) {
                    const success = await handleUpdate({ ...values, id: itemEan?.id });
                    if (success) {
                      await runActionsCallback();
                      loadEanDetail();
                    }
                  }
                }}
              >
                {itemEan && sn(itemEan?.siblings?.length) > 0 && (
                  <Space size={16} style={{ marginBottom: 8, marginTop: 16 }}>
                    {itemEan.siblings?.map((x) => {
                      return (
                        <div key={x.id}>
                          <Button
                            type="link"
                            size="small"
                            onClick={() => {
                              setPicFormInitialValues({
                                id: x?.id,
                                parent_id: x?.parent_id,
                                is_single: x?.is_single,
                                sku: x?.sku,
                                ean: x?.ean,
                              });

                              searchFormRef.current?.setFieldValue('sku', x?.sku);
                              loadEanDetail();
                            }}
                            style={{ padding: 0, fontWeight: picFormInitialValues?.id == x.id ? 'bold' : 'normal' }}
                          >{`${x.sku} (${x.attr_case_qty} pcs)`}</Button>
                        </div>
                      );
                    })}
                  </Space>
                )}
                <Divider style={{ marginTop: 0, marginBottom: 24 }} />

                {itemEan && !isSingleEan && (
                  <div>
                    <Space size={56}>
                      <ProFormField label="ProductName of Single EAN">
                        <Space>
                          <Typography.Title
                            copyable={{
                              text: itemEan?.parent?.ean_texts?.[0]?.name ?? itemEan?.item?.name ?? '',
                            }}
                            level={5}
                            style={{ marginBottom: 0 }}
                          >
                            {itemEan?.parent?.ean_texts?.[0]?.name ?? itemEan?.item?.name}
                          </Typography.Title>
                        </Space>
                      </ProFormField>
                      <ProFormField label="Qty / package">
                        <Typography.Title level={5} style={{ marginBottom: 0 }}>
                          {itemEan?.attr_case_qty}
                        </Typography.Title>
                      </ProFormField>
                    </Space>
                  </div>
                )}
                <ProFormList
                  key={'ean_texts'}
                  name="ean_texts"
                  creatorButtonProps={{
                    position: 'bottom',
                    creatorButtonText: 'Add a language',
                  }}
                  max={1}
                  itemRender={(dom, listMeta) => <div style={{ width: '100%', flex: '1' }}>{dom.listDom}</div>}
                  deleteIconProps={{ tooltipText: 'Remove' }}
                  copyIconProps={{ tooltipText: 'Copy row' }}
                >
                  <div style={{ display: 'none' }}>
                    <ProFormSelect
                      name={'lang'}
                      label="Language"
                      rules={[
                        {
                          required: true,
                          message: 'Language is required',
                        },
                      ]}
                      initialValue={'DE'}
                      options={[{ value: 'DE', label: 'German' }]}
                    />
                  </div>
                  <ProFormText
                    name={'name'}
                    label={
                      <>
                        <span>Product Name &nbsp;</span>
                        <ProFormDependency key={'name_length'} name={['name']}>
                          {(depValues) => {
                            return (
                              <span className={depValues.name?.length > 65 ? 'red' : ''}>
                                {depValues.name ? '(' + depValues.name.length + ')' : ''}
                              </span>
                            );
                          }}
                        </ProFormDependency>
                      </>
                    }
                    formItemProps={{ initialValue: itemEan?.item?.name }}
                    fieldProps={{
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    help={
                      gdsnItem &&
                      renderTakeButton(
                        gdsnItem.name,
                        () => {
                          formRef.current?.setFieldValue(['ean_texts', 0, 'name'], gdsnItem.name);
                          validateGdsnItemsDiff();
                        },
                        gdsnItemsDiff.name,
                        { showCopyable: true },
                      )
                    }
                  />

                  <ProFormText
                    name={'official_title'}
                    label="Juristische Produktbezeichnung"
                    tooltip="Official title"
                    readonly={true}
                    fieldProps={{
                      showCount: true,
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    colProps={{ span: 24 }}
                  />

                  {itemEan?.is_single ? null : (
                    <ProFormGroup labelLayout="inline">
                      <ProFormSwitch
                        name={['settings', 'useOwnText']}
                        label="No inherit?"
                        fieldProps={{
                          onChange(value) {
                            const hide = message.loading('Updating inheritance mode...', 0);
                            setLoading(true);
                            updateEanAttributePartial({
                              id: itemEan?.id,
                              mode: 'settings.useOwnText',
                              ean_text_de: { lang: 'DE', ean_id: itemEan?.id, settings: { useOwnText: value } },
                            })
                              .then((res) => {
                                loadEanDetail();
                              })
                              .catch(Util.error)
                              .finally(() => {
                                hide();
                                setLoading(false);
                              });
                          },
                        }}
                      />
                    </ProFormGroup>
                  )}

                  <ProFormTextArea
                    name={'short_description'}
                    label="Short Description"
                    disabled={!isTextEditable}
                    fieldProps={{
                      showCount: true,
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    colProps={{ span: 24 }}
                    help={
                      gdsnItem &&
                      renderTakeButton(
                        gdsnItem?.detail?.short_description,
                        () => {
                          formRef.current?.setFieldValue(
                            ['ean_texts', 0, 'short_description'],
                            gdsnItem?.detail?.short_description,
                          );
                          validateGdsnItemsDiff();
                        },
                        gdsnItemsDiff.short_description,
                        { showCopyable: true },
                      )
                    }
                  />
                  <ProFormTextArea
                    name={'description1'}
                    label="Description"
                    tooltip="Description"
                    disabled={!isTextEditable}
                    fieldProps={{
                      showCount: true,
                      rows: 6,
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    colProps={{ span: 24 }}
                    help={
                      isSingleEan &&
                      gdsnItem &&
                      renderTakeButton(
                        gdsnItem?.detail?.description1,
                        () => {
                          formRef.current?.setFieldValue(
                            ['ean_texts', 0, 'description1'],
                            gdsnItem?.detail?.description1,
                          );
                          validateGdsnItemsDiff();
                        },
                        gdsnItemsDiff.description1,
                        { showCopyable: true },
                      )
                    }
                  />
                  <ProFormText
                    name={'meta_title'}
                    label="Meta Title"
                    disabled={!isTextEditable}
                    fieldProps={{
                      showCount: true,
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    colProps={{ span: 24 }}
                    help={
                      isSingleEan &&
                      gdsnItem &&
                      renderTakeButton(
                        gdsnItem?.detail?.meta_title,
                        () => {
                          formRef.current?.setFieldValue(['ean_texts', 0, 'meta_title'], gdsnItem?.detail?.meta_title);
                          validateGdsnItemsDiff();
                        },
                        gdsnItemsDiff.meta_title,
                        { showCopyable: true },
                      )
                    }
                  />
                  <ProFormTextArea
                    name={'meta_keywords'}
                    label="Meta Keywords"
                    disabled={!isTextEditable}
                    fieldProps={{
                      showCount: true,
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    colProps={{ span: 24 }}
                    help={
                      isSingleEan &&
                      gdsnItem &&
                      renderTakeButton(
                        gdsnItem?.detail?.tradeItemKeyWords,
                        () => {
                          formRef.current?.setFieldValue(
                            ['ean_texts', 0, 'meta_keywords'],
                            gdsnItem?.detail?.tradeItemKeyWords,
                          );
                          validateGdsnItemsDiff();
                        },
                        gdsnItemsDiff.meta_keywords,
                        { showCopyable: true },
                      )
                    }
                  />
                  <ProFormTextArea
                    name={'meta_description'}
                    label="Meta Description"
                    disabled={!isTextEditable}
                    fieldProps={{
                      showCount: true,
                      onChange(value) {
                        validateGdsnItemsDiff();
                      },
                    }}
                    colProps={{ span: 24 }}
                    help={
                      isSingleEan &&
                      gdsnItem &&
                      renderTakeButton(
                        gdsnItem?.detail?.meta_description,
                        () => {
                          formRef.current?.setFieldValue(
                            ['ean_texts', 0, 'meta_description'],
                            gdsnItem?.detail?.meta_description,
                          );
                          validateGdsnItemsDiff();
                        },
                        gdsnItemsDiff.meta_description,
                        { showCopyable: true },
                      )
                    }
                  />

                  <ProFormTextArea
                    name={'fs_export_google_description'}
                    label="Google Export Description"
                    tooltip="If blank, we will use short description in up sync."
                    disabled={!isTextEditable}
                    fieldProps={{
                      showCount: true,
                    }}
                    colProps={{ span: 24 }}
                  />
                </ProFormList>
                <div style={{ marginTop: 16, float: 'right' }}>
                  {renderActionButtons({ buttonSize: 'middle', showButtonText: true })}
                </div>
              </ProForm>
            </Spin>
          </Col>
          <Col span={16}>
            {itemEan?.ean ? (
              <OpenAiTextGenerator
                eanFormRef={formRef}
                is_single={itemEan?.is_single}
                pastable={isTextEditable}
                single_ean={itemEan?.parent?.ean}
                single_title={itemEan?.parent?.ean_texts?.[0]?.name || ''}
                multi_ean={itemEan?.ean}
                multi_title={itemEan?.ean_texts?.[0]?.name || ''}
              />
            ) : null}
          </Col>
          {!!itemEan?.id && (
            <Col flex="1000px">
              {isSingleEan && (
                <Card
                  style={{ marginBottom: 24 }}
                  size="small"
                  title="UpdatePackage EAN Names"
                  extra={
                    <Button
                      key="sibSave"
                      size="small"
                      type="primary"
                      className="btn-green"
                      onClick={() => formRefSib.current?.submit()}
                    >
                      Save
                    </Button>
                  }
                  bodyStyle={{ paddingBottom: 0 }}
                >
                  <ProForm<FormValueType>
                    formRef={formRefSib}
                    isKeyPressSubmit
                    layout="horizontal"
                    labelAlign="left"
                    colon={false}
                    submitter={{
                      render: (form, dom) => {
                        return [];
                      },
                    }}
                    onFinish={async (values) => {
                      const hide = message.loading('Updating names...', 0);
                      updateEanAttributePartial({ id: itemEan.id, ...values, mode: 'siblings.ean_text_de' })
                        .then((res) => {
                          message.success('Saved successfully.');
                          loadEanDetail();
                        })
                        .catch(Util.error)
                        .finally(() => {
                          hide();
                        });
                    }}
                  >
                    <ProFormList
                      name={['siblings']}
                      creatorButtonProps={false}
                      deleteIconProps={false}
                      copyIconProps={false}
                    >
                      <ProFormText
                        name={['ean_text_de', 'name']}
                        width={'xl'}
                        label={
                          <div style={{ width: 150 }}>
                            <ProFormDependency
                              key={`name_length`}
                              name={[['ean_text_de', 'name'], ['sku'], ['attr_case_qty']]}
                            >
                              {(depValues) => {
                                return (
                                  <>
                                    <span>
                                      {depValues.sku} ({depValues.attr_case_qty}pcs) &nbsp;
                                    </span>
                                    <span className={depValues.ean_text_de?.name?.length > 65 ? 'red' : ''}>
                                      {depValues.ean_text_de?.name
                                        ? '(' + depValues.ean_text_de?.name.length + ')'
                                        : ''}
                                    </span>
                                  </>
                                );
                              }}
                            </ProFormDependency>
                          </div>
                        }
                      />
                    </ProFormList>
                  </ProForm>
                </Card>
              )}

              <Card
                style={{ marginBottom: 24 }}
                size="small"
                title="Images"
                bodyStyle={{ padding: 0 }}
                bordered={false}
              >
                {sn(itemEan?.siblings?.length) > 0 && (
                  <Space size={16} style={{ marginBottom: 8, marginTop: 8 }}>
                    {itemEan.siblings?.map((x) => {
                      return (
                        <div key={x.id}>
                          <Button
                            type="link"
                            size="small"
                            onClick={() => {
                              setPicFormInitialValues({
                                id: x?.id,
                                parent_id: x?.parent_id,
                                is_single: x?.is_single,
                                sku: x?.sku,
                                ean: x?.ean,
                              });

                              searchFormRef.current?.setFieldValue('sku', x?.sku);
                              loadEanDetail();
                            }}
                            style={{ padding: 0, fontWeight: picFormInitialValues?.id == x.id ? 'bold' : 'normal' }}
                          >{`${x.sku} (${x.attr_case_qty} pcs)`}</Button>
                        </div>
                      );
                    })}
                  </Space>
                )}
                <UpdatePicturesFormEditor initialValues={picFormInitialValues} gdsnItem={gdsnItem} />
              </Card>
            </Col>
          )}
        </Row>
      </Card>
    </PageContainer>
  );
};

export default EanDetail;
