drop table if exists warehouse_picklist_time_tracking;

CREATE TABLE `warehouse_picklist_time_tracking`
(
    `id`             int(11)     NOT NULL AUTO_INCREMENT,
    `picklist_id`    int(11)     NOT NULL COMMENT 'Picklist ID',
    `type`           smallint(4) NOT NULL COMMENT 'Type. e.g. Pick: 1, Print: 2, Pack: 3',
    `qty_employee`   int(11)  DEFAULT NULL COMMENT 'Quantity of employees',
    `minute`         int(11)  DEFAULT NULL COMMENT 'Minute. Calculated by {end_datetime} - {start_datetime}',
    `start_datetime` datetime DEFAULT NULL COMMENT 'Start time',
    `end_datetime`   datetime DEFAULT NULL COMMENT 'End time',
    `created_on`     datetime DEFAULT NULL COMMENT 'Created on',
    `created_by`     int(11)  DEFAULT NULL COMMENT 'Creator ID',
    `updated_on`     datetime DEFAULT NULL COMMENT 'Updated on',
    `updated_by`     int(11)  DEFAULT NULL COMMENT 'Creator ID',
    PRIMARY KEY (`id`),
    <PERSON><PERSON><PERSON> `IDX_FK_warehouse_picklist_time_tracking_picklist_id` (`picklist_id`),
    CONSTRAINT `FK_warehouse_picklist_time_tracking_picklist_id` FOREIGN KEY (`picklist_id`) REFERENCES `warehouse_picklist` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

ALTER TABLE `import_supplier_data`
    CHANGE `case_qty` `case_qty` DOUBLE DEFAULT 0 NULL COMMENT 'Qty per Package?',
    CHANGE `vat` `vat` VARCHAR(10) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'VAT name',
    CHANGE `box_qty` `box_qty` DOUBLE DEFAULT 0 NULL COMMENT 'Package per pallet?';