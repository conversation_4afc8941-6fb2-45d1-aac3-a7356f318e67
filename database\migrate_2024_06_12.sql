CREATE TABLE `offer_item_ibo_pre_map`
(
    `offer_item_id` bigint(20) unsigned NOT NULL,
    `ibo_pre_id`    bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`offer_item_id`, `ibo_pre_id`),
    <PERSON><PERSON>Y `FK_offer_item_ibo_pre_map_ibo_pre_id` (`ibo_pre_id`),
    CONSTRAINT `FK_offer_item_ibo_pre_map_ibo_pre_id` FOREIGN KEY (`ibo_pre_id`) REFERENCES `ibo_pre` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_item_ibo_pre_map_offer_item_id` FOREIGN KEY (`offer_item_id`) REFERENCES `offer_item` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


ALTER TABLE `offer_item_ibo_pre_map`
    ADD COLUMN `case_qty` INT DEFAULT 0 NOT NULL AFTER `ibo_pre_id`,
    ADD COLUMN `qty`      INT DEFAULT 0 NOT NULL AFTER `case_qty`;

