/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/ibo/ibo-draft-detail';

/** create POST /api/ibo/ibo-draft-detail */
export async function createIboDraftDetail(
  data: API.IboDraftDetail,
  options?: { [key: string]: any },
): Promise<API.IboDraftDetail> {
  return request<API.BaseResult>(urlPrefix, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** Book PUT /api/ibo/ibo-draft-detail/book */
export async function bookIboDraftDetail(
  data: API.IboDraftDetail,
  options?: { [key: string]: any },
): Promise<API.IboDraftDetail> {
  return request<API.BaseResult>(urlPrefix + '/book', {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** get GET /api/ibo/ibo-draft-detail */
export async function getIboDraftDetailList(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      ...filter,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getIboDraftDetail(
  id: string | number,
  params?: { [key: string]: any },
) {
  return request<API.ResultObject<API.IboDraftDetail>>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params: {
      ...params,
      with: 'detail',
    },
    withToken: true,
  }).then((res) => res.message);
}

/** put PUT /api/ibo/ibo-draft-detail */
export async function updateIboDraftDetail(
  id?: number,
  data?: API.IboDraftDetail,
  options?: { [key: string]: any },
): Promise<API.IboDraftDetail> {
  return request<API.BaseResult>(`${urlPrefix}/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/ibo/ibo-draft-detail */
export async function deleteIboDraftDetail(id?: number) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
  });
}

/** export as PDF GET /api/ibo/ibo-draft-detai */
export async function exportIboDraftDetailList(params?: any): Promise<{ url?: string }> {
  return request<API.BaseResult>(`${urlPrefix}/export-pdf`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}

/** export as PDF GET /api/ibo/ibo-draft-detai */
export async function exportIboDraftDetailListInXls(params?: any): Promise<{ url?: string }> {
  return request<API.BaseResult>(`${urlPrefix}/export-xls`, {
    method: 'GET',
    params,
  }).then((res) => res.message);
}
