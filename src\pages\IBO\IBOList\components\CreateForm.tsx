import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addIbo } from '@/services/foodstore-one/IBO/ibo';
import { message } from 'antd';
import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';

const handleAdd = async (fields: API.Ean) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addIbo(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = {
  target?: string;
  template?: string;
  type?: string;
  time?: string;
  frequency?: string;
} & Partial<API.RuleListItem>;

export type CreateFormProps = {
  values?: Partial<API.RuleListItem>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Ean) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New EAN'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.Ean);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <SProFormDigit width="sm" name="qty" label="Order Qty" min={1} fieldProps={{ precision: 0 }} />
      <SProFormDigit
        width="sm"
        name="price"
        label="Order price"
        addonAfter="€"
        required
        rules={[
          {
            required: true,
            message: 'Order price is required',
          },
        ]}
      />
    </ModalForm>
  );
};

export default CreateForm;
