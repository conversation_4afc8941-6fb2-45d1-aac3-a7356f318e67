drop table if exists import_current_bp_aggregated;

ALTER TABLE `import`
    ADD KEY `IDX_import_supplier_add` (`supplier_add`);

ALTER TABLE `ibo_pre_management`
    CHANGE `note_supplier` `note_supplier` VARCHAR (255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    CHANGE `note_customer` `note_customer` VARCHAR (255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    ADD KEY `IDX_ibo_pre_management_note_supplier` (`note_supplier`) COMMENT 'SUPPLIER_ADD',
    ADD KEY `IDX_ibo_pre_management_note_customer` (`note_customer`);

ALTER TABLE `ibo_pre`
    CHANGE `price_xls` `price_xls` DECIMAL (20, 4) NULL;