
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/magento-data/order-parcel-log';

/** rule GET /api/magento-data/order-parcel-log */
export async function getOrderParcelLogList(params: API.PageParams, sort?: any, filter?: any) {
    return request<API.ResultList<API.OrderParcelLog>>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        withToken: true,
        paramsSerializer,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
        pagination: res.message.pagination, // For total row pagination hack.
    }));
}