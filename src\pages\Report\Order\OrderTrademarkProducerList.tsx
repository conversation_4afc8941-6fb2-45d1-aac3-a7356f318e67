import { Card } from 'antd';
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import Util, { nf2, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit, ProFormRadio, ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { getOrderTrademarkProducerList } from '@/services/foodstore-one/Report/order-report';
import { getTrademarkListSelectOptions } from '@/services/foodstore-one/BasicData/trademark';
import StockStableQtyModal from '@/pages/Item/EanList/components/StockStableQtyModal';
import type { ResizeCallbackData } from 'react-resizable';
import { Resizable } from 'react-resizable';
import type { ColumnType } from 'antd/lib/table';
import OrderListModal from '@/pages/Magento/Order/components/OrderListModal';
import IboDetailModal from './components/IboDetailModal';
import type { DefaultOptionType } from 'antd/lib/select';
// import OrderDetailListModal from './components/OrderDetailListModal';

export const ResizableTitle = (
  props: React.HTMLAttributes<any> & {
    onResize: (e: React.SyntheticEvent<Element>, data: ResizeCallbackData) => void;
    width: number;
  },
) => {
  const { onResize, width, ...restProps } = props;

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

export type SearchFormValueType = Partial<API.OrderItem> & {
  intervalType?: 'd' | 'm' | 'w';
  lastInterval?: number;
  colMode?: 'qty' | 'turnover' | 'gp' | 'cturnover' | 'ebayFee';
};

export type OrderModalSearchParamsType = SearchFormValueType & {
  dateRange?: DateRangeType;
  source?: string;
  wd?: number; //week of day

  trademark?: number | DefaultOptionType | API.Trademark;
  trademark_name?: string; // It's not a filter on backend. It's just a prop of modal title on frontend.
};

type RecordType = API.OrderItem & Record<string, any>;

const defaultSearchFormValues = {
  lastInterval: 7,
  intervalType: 'd',
  colMode: 'qty',
  sku: '',
  ean: '',
  'trademarks[]': [],
};

const OrderTrademarkProducerList: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();

  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);
  const [dateRanges, setDateRanges] = useState<DateRangeType[]>([]);

  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [openIBOModal, setOpenIBOModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RecordType>();

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});

  // const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);

  const handleResize: any = useCallback(
    (index: number) =>
      (__: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
        setColumns((prev) => {
          const newColumns = [...prev];
          newColumns[index] = {
            ...newColumns[index],
            width: size.width,
          };
          return newColumns;
        });
      },
    [],
  );

  /**
   * Handler to open the orders list modal.
   *
   * @param columnField
   * @param intervalType
   * @param dateRange
   * @param options
   */
  const handleOnClick = (
    columnField: string,
    intervalType: 'd' | 'w' | 'm',
    dateRange: { from?: string; to?: string },
    options?: Record<string, any>,
  ) => {
    setModalSearchParams({
      source: 'salesReport',
      intervalType,
      dateRange,
      ...searchFormRef.current?.getFieldsValue(),
      ...options,
    });
    setOpenOrderListModal(true);
  };

  const defaultColumns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        title: 'Trademark',
        dataIndex: ['trademark_name'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 120,
      },
      {
        title: 'SKU',
        dataIndex: ['sku'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 70,
      },
      {
        title: 'EAN',
        dataIndex: ['ean'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 120,
      },
      {
        title: 'Name',
        dataIndex: ['name'],
        sorter: true,
        align: 'left',
        ellipsis: true,
        width: 250,
        onHeaderCell: (column: ColumnType<RecordType>) => ({
          width: column.width,
          onResize: handleResize((column as any).index) as React.ReactEventHandler<any>,
        }),
      },
      {
        title: 'Shortest Exp',
        dataIndex: ['mix_min_exp_date'],
        sorter: true,
        align: 'center',
        ellipsis: true,
        width: 100,
        render: (dom, record) => {
          return Util.dtToDMY(record?.mix_min_exp_date);
        },
      },
      {
        title: 'AVG BP',
        dataIndex: 'ibo_avg_bp',
        sorter: true,
        align: 'right',
        width: 80,
        className: 'br2 b-grey',
        render: (dom, record) => {
          return nf2(record.ibo_avg_bp * record.attr_case_qty);
        },
        onCell: (record) => {
          if (record.item_ean) {
            return {
              className: 'cursor-pointer',
              onClick: (e) => {
                setCurrentRow(record);
                setOpenIBOModal(true);
              },
            };
          }
          return {};
        },
      },
      {
        title: 'AVG Net Turnover',
        dataIndex: 'avg_turnover',
        sorter: false,
        align: 'right',
        width: 90,
        className: 'br2 b-grey c-grey',
        render: (dom, record) => {
          return nf2(record.turnover / record.qty);
        },
      },
      {
        title: 'AVG Net/Net Turnover',
        dataIndex: 'avg_cturnover',
        sorter: false,
        align: 'right',
        width: 90,
        className: 'br2 b-grey',
        showSorterTooltip: false,
        tooltip: 'Please click to see details.',
        render: (dom, record) => {
          return nf2(record.cturnover / record.qty);
        },
        onCell: (record) => {
          if (record.qty && record.sku) {
            return {
              className: 'cursor-pointer',
              onClick: () => {
                const searchValues = searchFormRef.current?.getFieldsValue();
                const intervalType = searchValues?.intervalType ?? 'd';

                handleOnClick(
                  'avg_cturnover',
                  intervalType,
                  { from: dateRanges?.[dateRanges.length - 1].from, to: dateRanges?.[0].to },
                  { sku: record.sku },
                );
              },
            };
          } else return {};
        },
      },
      {
        title: 'Grand Total Qty',
        dataIndex: 'qty_ordered',
        sorter: true,
        align: 'right',
        width: 80,
        className: 'br2 b-grey',
      },
      {
        title: 'Magento Stock',
        dataIndex: ['mag_qty'],
        valueType: 'digit',
        sorter: true,
        align: 'right',
        width: 80,
        tooltip: 'Magento Stock. Please down sync to get the latest stock info.',
        showSorterTooltip: false,
        render: (dom, record) => {
          return Util.numberFormat(record.mag_qty);
        },
      },
      {
        title: 'Stock',
        dataIndex: ['mix_qty'],
        sorter: true,
        align: 'right',
        ellipsis: true,
        width: 80,
        tooltip: 'Please click to view stock details.',
        showSorterTooltip: false,
        className: 'cursor-pointer',
        render: (dom, record) => {
          return Util.numberFormat(record.mix_qty);
        },
        onCell: (record: API.Ean) => {
          return {
            onClick: () => {
              setCurrentRow({ ...record });
              handleQtyModalVisible(true);
            },
          };
        },
      },
    ],
    [dateRanges, handleResize],
  );

  const buildColumns = useCallback(() => {
    const lastInterval = dateRanges.length;
    if (lastInterval < 1) return;

    const getColumnDef = (
      groupColumnTitle: string,
      indNo: number,
      dateRange: DateRangeType,
    ): ProColumns<RecordType> => {
      const ind = indNo == -100 ? '' : indNo;

      const searchValues = searchFormRef.current?.getFieldsValue();
      const intervalType = searchValues?.intervalType ?? 'd';
      const colMode = searchValues?.colMode ?? 'qty';

      const onCellHandler = (record: any, index?: number) =>
        sn(record[`qty${ind}`])
          ? {
              className: 'cursor-pointer',
              onClick: (e: any) => {
                handleOnClick('', intervalType, dateRange, { sku: record.sku });
              },
            }
          : {};

      const defaultAttr: Partial<ProColumns<RecordType>> = {
        align: 'right',
        width: intervalType == 'w' ? 100 : 80,
        sorter: true,
        className: ind !== '' ? '' : 'bg-green3',
        onCell: onCellHandler,
      };

      const newCol: ProColumns<RecordType> = {
        title: groupColumnTitle,
        dataIndex: [`colGroup${ind}`],
        align: 'center',
        ellipsis: true,
        children: [
          {
            title: 'Qty',
            dataIndex: `qty${ind}`,
            hideInTable: colMode != 'qty',
            ...defaultAttr,
            render: (dom, record) => Util.numberFormat(record[`qty${ind}`], false),
          },
          {
            title: 'Order Turnover',
            dataIndex: `turnover${ind}`,
            hideInTable: colMode != 'turnover',
            ...defaultAttr,
            render: (dom, record) => Util.numberFormat(record[`turnover${ind}`], false, 2),
          },
          {
            title: 'Turnover',
            dataIndex: `cturnover${ind}`,
            hideInTable: colMode != 'cturnover',
            ...defaultAttr,
            render: (dom, record) => Util.numberFormat(record[`cturnover${ind}`], false, 2),
          },
          {
            title: 'GP',
            dataIndex: `gp${ind}`,
            hideInTable: colMode != 'gp',
            ...defaultAttr,
            render: (dom, record) => Util.numberFormat(record[`gp${ind}`], false, 2),
          },
          {
            title: 'ebay Fee',
            dataIndex: `ebay_fee${ind}`,
            hideInTable: colMode != 'ebayFee',
            ...defaultAttr,
            render: (dom, record) => Util.numberFormat(record[`ebay_fee${ind}`], false, 2),
          },
        ],
      };

      return newCol;
    };

    const newCols = [];
    for (let ind = 0; ind < lastInterval; ind++) {
      const range = dateRanges[ind];
      newCols.push(getColumnDef(range.title || '', ind, range));
    }

    const arr = [...defaultColumns];
    if (lastInterval > 1) {
      arr.push(
        getColumnDef('Total', -100, {
          from: dateRanges[dateRanges.length - 1].from,
          to: dateRanges[0].to,
        }),
      );
    }
    setColumns([...arr, ...newCols]);
  }, [dateRanges, defaultColumns]);

  useEffect(() => {
    // const initialValues = Util.getSfValues('sf_orders_stats', defaultSearchFormValues);
    const initialValues = searchFormRef.current?.getFieldsValue();
    setDateRanges(Util.dtBuildRanges(initialValues?.intervalType ?? 'd', initialValues?.lastInterval ?? 7));
  }, []);

  useEffect(() => {
    buildColumns();
  }, [buildColumns]);

  useEffect(() => {
    if (dateRanges.length) {
      actionRef.current?.reload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateRanges?.length, dateRanges?.[0]?.from, dateRanges?.[0]?.to]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          onValuesChange={(values, allValues) => {
            if ('lastInterval' in values || 'intervalType' in values || 'colMode' in values)
              setDateRanges(Util.dtBuildRanges(allValues.intervalType ?? 'd', allValues.lastInterval ?? 7));
          }}
          initialValues={Util.getSfValues('sf_orders_stats', defaultSearchFormValues)}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue(defaultSearchFormValues as any);
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormDigit name={'lastInterval'} label="Last" width="xs" placeholder="" initialValue={7} />
          <ProFormRadio.Group
            name="intervalType"
            options={[
              { value: 'd', label: 'Day' },
              { value: 'w', label: 'Week' },
              { value: 'm', label: 'Month' },
            ]}
            initialValue={'d'}
            radioType="button"
            fieldProps={{ buttonStyle: 'solid' }}
          />
          <ProFormRadio.Group
            name="colMode"
            label="Show"
            initialValue={'qty'}
            options={[
              { value: 'qty', label: 'Qty' },
              { value: 'cturnover', label: 'Turnover' },
              { value: 'gp', label: 'GP' },
              { value: 'turnover', label: 'Order Turnover' },
              { value: 'ebayFee', label: 'Ebay Fee' },
            ]}
            radioType="button"
            fieldProps={{ buttonStyle: 'solid' }}
          />
          <ProFormSelect
            name="ean_type_search"
            placeholder="Select type"
            label="Type"
            options={[
              { value: '', label: 'All' },
              { value: 'base', label: 'Single' },
              { value: 've', label: 'Multi' },
            ]}
            // fieldProps={{ onChange: (e) => searchFormRef.current?.submit() }}
          />
          <ProFormSelect
            name="trademarks[]"
            label="Trademarks"
            placeholder="Please select trademarks"
            mode="multiple"
            request={getTrademarkListSelectOptions}
            width={180}
          />
          <ProFormText name={'ean'} label="EAN" width={160} placeholder={'EAN'} />
          <ProFormText name={'sku'} label="SKU" width={'xs'} placeholder={'SKU'} />
          <ProFormText name={'order_id'} label="Order ID" width={'xs'} placeholder={'Order ID'} />
        </ProForm>
      </Card>
      <ProTable<RecordType, API.PageParams>
        headerTitle={'Orders Stats'}
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_orders_stats', searchFormValues);
          setLoading(true);
          return getOrderTrademarkProducerList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              dateRanges,
            },
            sort,
            filter,
          )
            .then((res) => {
              const totalRow: RecordType = {
                uid: 'total',
                trademark_name: 'Total',
                qty_ordered: 0,
                mix_qty: 0,
                mag_qty: 0,
                avg_turnover: 0,
                avg_cturnover: 0,
              };
              const qtyColCount = dateRanges.length;
              res.data.forEach((row: RecordType) => {
                totalRow.qty_ordered = sn(totalRow.qty_ordered) + sn(row.qty_ordered);
                totalRow.mix_qty = sn(totalRow.mix_qty) + sn(row?.mix_qty);
                totalRow.mag_qty = sn(totalRow.mag_qty) + sn(row?.mag_qty);

                for (let i = -1; i < qtyColCount; i++) {
                  const suffix = i >= 0 ? i : '';
                  totalRow[`qty${suffix}`] = sn(totalRow[`qty${suffix}`]) + sn(row[`qty${suffix}`]);
                  totalRow[`gp${suffix}`] = sn(totalRow[`gp${suffix}`]) + sn(row[`gp${suffix}`]);
                  totalRow[`turnover${suffix}`] = sn(totalRow[`turnover${suffix}`]) + sn(row[`turnover${suffix}`]);
                  totalRow[`cturnover${suffix}`] = sn(totalRow[`cturnover${suffix}`]) + sn(row[`cturnover${suffix}`]);

                  totalRow[`ebay_fee${suffix}`] = sn(totalRow[`ebay_fee${suffix}`]) + sn(row[`ebay_fee${suffix}`]);
                }
              });

              res.data.splice(0, 0, totalRow);

              return res;
            })
            .finally(() => setLoading(false));
        }}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
          defaultPageSize: 10000,
        }}
        columns={columns}
        columnEmptyText=""
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        rowSelection={false}
        rowClassName={(record) => {
          if (record.uid == 'total') {
            return 'total-row';
          } else return record.ean_id ? (record?.is_single ? 'row-single' : 'row-multi') : '';
        }}
      />
      <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_qty,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_res_qty,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />
      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
      />
      {currentRow?.item_ean?.id && (
        <IboDetailModal
          eanId={currentRow?.item_ean?.id}
          modalVisible={openIBOModal}
          handleModalVisible={setOpenIBOModal}
        />
      )}
      {/* {currentRow?.sku && (
        <OrderDetailListModal
          sku={currentRow?.sku}
          modalVisible={openDetailModal}
          handleModalVisible={setOpenDetailModal}
          dateRanges={dateRanges}
          searchFormRef={searchFormRef}
        />
      )} */}
    </PageContainer>
  );
};

export default OrderTrademarkProducerList;
