import React, { useEffect, useRef, useState, useMemo } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import { sn } from '@/util';
import { ni } from '@/util';
import Util, { nf2 } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import type { DefaultOptionType } from 'antd/lib/select';
import { getSalesStatsDailyList } from '@/services/foodstore-one/Report/sales-stat';
import Link from 'antd/lib/typography/Link';

type SearchFormValueType = {
  trademark?: DefaultOptionType;
  sku?: string;
  name?: string;
};

export type OrderModalSearchParamsType = SearchFormValueType & {
  dateRange?: DateRangeType;
} & { filtered_only?: boolean };

type RecordType = API.OrderItem &
  API.Order & {
    uid?: string;
    orders_cnt?: number;
    s_orders_cnt?: number;
    m_orders_cnt?: number;
    turnover?: number;
    gturnover?: number;
    cturnover?: number;
    bp?: number;
    gp?: number;
  };

type SalesStatDailyPropsType = {
  pageSize?: number;
};

const SalesStatDaily: React.FC<SalesStatDailyPropsType> = ({ pageSize }) => {
  const searchFormRef = useRef<ProFormInstance<SearchFormValueType>>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [columns, setColumns] = useState<ProColumns<RecordType>[]>([]);

  /* const trademarkChangeCallbackHandler = useCallback((type: TrademarkChangeCallbackHandlerTypeParamType) => {
    if (type == 'reload') {
      actionRef.current?.reload();
    }
  }, []);
  const { formElements } = useTrademarkFormFilter(searchFormRef.current, trademarkChangeCallbackHandler, {
    parentLoading: loading,
  });

  const [showImportedPrices, setShowImportedPrices] = useState<boolean>(false);
  const [updatePricesModalVisible, handleUpdatePricesModalVisible] = useState<boolean>(false);

  // trademarks dropdown options
  const [trademarks, setTrademarks] = useState<DefaultOptionType[]>([]);

  const [qtyModalVisible, handleQtyModalVisible] = useState<boolean>(false);
  const [openIBOModal, setOpenIBOModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RecordType>();

  // Order Detail modal
  const [openOrderListModal, setOpenOrderListModal] = useState<boolean>(false);
  const [modalSearchParams, setModalSearchParams] = useState<OrderModalSearchParamsType>({});
  // EAN Text modal
  const [updateTextsModalVisible, handleUpdateTextsModalVisible] = useState<boolean>(false); */

  const defaultColumns: ProColumns<RecordType>[] = useMemo<ProColumns<RecordType>[]>(
    () => [
      {
        title: 'Date',
        dataIndex: ['uid'],
        width: 80,
        align: 'center',
        hideInSearch: true,
        render(dom, record) {
          return Util.dtToDMY(record.uid);
        },
      },
      {
        title: 'Qty',
        dataIndex: ['orders_cnt'],
        width: 40,
        align: 'right',
        render(__, entity) {
          return (
            <Link
              href={`/report/order/sales-stat-order-list?start_date=${entity.uid}&end_date=${entity.uid}`}
              target="_blank"
            >
              {ni(entity.orders_cnt)}
            </Link>
          );
        },
      },

      {
        title: 'Qty. Multi',
        dataIndex: ['s_orders_cnt'],
        width: 50,
        align: 'right',
        render(__, entity) {
          return ni(entity.s_orders_cnt);
        },
      },
      {
        title: 'Qty. Single',
        dataIndex: ['m_orders_cnt'],
        width: 50,
        align: 'right',
        render(__, entity) {
          return ni(entity.m_orders_cnt);
        },
      },
      {
        title: 'GP',
        dataIndex: `gp`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          return <span>{nf2(record.gp)}</span>;
        },
      },
      {
        title: 'GP / Order',
        dataIndex: `gp`,
        align: 'right',
        width: 50,
        sorter: false,
        render: (__, record) => {
          return sn(record.orders_cnt) ? <span>{nf2(sn(record.gp) / sn(record.orders_cnt))}</span> : null;
        },
      },
      {
        title: 'G. Turnover',
        dataIndex: ['turnover'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return nf2(record.cturnover);
        },
      },
      {
        title: 'G. Turnover / Order',
        dataIndex: ['turnover'],
        width: 70,
        align: 'right',
        hideInSearch: true,
        render(__, record) {
          return sn(record.orders_cnt) ? nf2(sn(record.cturnover) / sn(record.orders_cnt)) : null;
        },
      },
      {
        title: 'BP',
        dataIndex: `bp`,
        align: 'right',
        width: 60,
        sorter: false,
        render: (__, record) => {
          return <span>{nf2(record.bp)}</span>;
        },
      },
    ],
    [],
  );

  useEffect(() => {
    setColumns(defaultColumns);
  }, [defaultColumns]);

  return (
    <>
      {/* <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 0, paddingTop: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          size="small"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_order_sales_stat_daily', {})}
          submitter={{
            searchConfig: { submitText: 'Search' },
            submitButtonProps: { loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue(defaultSearchFormValues as any);
              actionRef.current?.reload();
            },
          }}
        >
          <SProFormDateRange label="Date" formRef={searchFormRef} style={{ marginLeft: 16 }} disabled={loading} />
          {formElements}

          <ProFormText name={'sku'} label="SKU" width={100} placeholder={'SKU'} disabled={loading} />
          <ProFormText name={'name'} label="Name" width={150} placeholder={'Name'} disabled={loading} />
        </ProForm>
      </Card> */}

      <ProTable<RecordType, API.PageParams>
        headerTitle={'Daily Sales Statistics'}
        actionRef={actionRef}
        size="small"
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, setting: false }}
        search={false}
        sticky
        bordered
        scroll={{ x: 400 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};

          setLoading(true);
          return getSalesStatsDailyList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
              trademarks: [searchFormValues.trademark?.value],
            },
            sort,
            filter,
          )
            .then((res) => {
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        pagination={{
          showSizeChanger: true,
          // hideOnSinglePage: true,
          defaultPageSize: pageSize ?? 10,
          // defaultPageSize: sn(Util.getSfValues('sf_order_sales_stat_daily_p')?.pageSize ?? 10),
        }}
        columns={columns}
        columnEmptyText=""
        rowSelection={false}
        rowClassName={(record) => {
          if (record.uid == 'total') {
            return 'total-row';
          } else return record?.item_ean?.is_single ? 'row-single' : 'row-multi';
        }}
      />

      {/* <StockStableQtyModal
        modalVisible={qtyModalVisible}
        handleModalVisible={handleQtyModalVisible}
        initialValues={{
          id: currentRow?.item_ean?.id,
          item_id: currentRow?.item_ean?.item_id,
          parent_id: currentRow?.item_ean?.parent_id,
          is_single: currentRow?.item_ean?.is_single,
          sku: currentRow?.item_ean?.sku,
          ean: currentRow?.item_ean?.ean,
          mag_inventory_stocks_sum_quantity: currentRow?.mag_qty,
          mag_inventory_stocks_sum_res_quantity: currentRow?.mag_res_qty,
          mag_inventory_stocks_sum_res_cal: currentRow?.mag_res_cal,
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            // actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleQtyModalVisible(false);
        }}
      />
      <OrderListModal
        modalVisible={openOrderListModal}
        handleModalVisible={setOpenOrderListModal}
        searchParams={modalSearchParams}
      />
      {currentRow?.item_ean?.id && (
        <IboDetailModal
          eanId={currentRow?.item_ean?.id}
          modalVisible={openIBOModal}
          handleModalVisible={setOpenIBOModal}
        />
      )}

      <UpdatePriceAttributeForm
        modalVisible={updatePricesModalVisible}
        handleModalVisible={handleUpdatePricesModalVisible}
        initialValues={currentRow?.item_ean || {}}
        reloadList={async (updatedRow: Partial<API.Ean>) => {
          actionRef.current?.reload();
        }}
        onSubmit={async () => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdatePricesModalVisible(false);
        }}
      />

      <UpdateTextsForm
        modalVisible={updateTextsModalVisible}
        handleModalVisible={handleUpdateTextsModalVisible}
        initialValues={currentRow?.item_ean || {}}
        isRefetchInitialValues
        onSubmit={async (values) => {
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateTextsModalVisible(false);
        }}
      />

      <Drawer
        title={`Buying Price History - ${currentRow?.item_ean?.ean}`}
        width={700}
        open={showImportedPrices}
        className={currentRow?.item_ean?.is_single ? 'm-single' : 'm-multi'}
        onClose={() => {
          setCurrentRow(undefined);
          setShowImportedPrices(false);
        }}
        // closable={false}
      >
        {currentRow?.item_ean && (
          <ImportedPrices
            itemEan={currentRow.item_ean}
            iboListProps={{
              filters: {
                filterMode: 'latest',
                pageSize: 8,
              },
            }}
          />
        )}
      </Drawer> */}
    </>
  );
};

export default SalesStatDaily;
