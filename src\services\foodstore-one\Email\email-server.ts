/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/email-server';

/** rule GET /api/email-server */
export async function getEmailServerList(
  params: API.PageParams &
    Partial<API.EmailServer> & {
      customer_id?: number;
      supplier_id?: number;
      order_no?: number;
    },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.EmailServer>> {
  return request<API.Result<API.EmailServer>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/email-server */
export async function updateEmailServer(data: Partial<API.EmailServer>, options?: { [key: string]: any }) {
  return request<API.EmailServer>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/email-server/{order_no} */
export async function updateOrderEmailServer(
  order_no?: number,
  data?: Partial<API.EmailServer> & { by_email_id?: number; type?: string },
  options?: { [key: string]: any },
) {
  return request<API.EmailServer>(`${urlPrefix}/${order_no}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email-server */
export async function addEmailServer(data: API.EmailServer, options?: { [key: string]: any }) {
  return request<API.EmailServer>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/email-server/{id} */
export async function deleteEmailServer(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
