import React, { useState } from 'react';
import { Button, Space } from 'antd';
import ViewFileDetailModal from '@/pages/Gdsn/components/ViewFileDetailModal';
import { SearchOutlined } from '@ant-design/icons';

export type GdsnItemButtonProps = {
  ean?: string;
  eanId?: number;
  itemId?: number;
  style?: any;
  fetchGdsnItem?: () => void;
};

const GdsnItemButton: React.FC<GdsnItemButtonProps> = ({ ean, itemId, eanId, style, fetchGdsnItem, ...rest }) => {
  const [openModal, setOpenModal] = useState<boolean>(false);

  return (
    <Space style={style}>
      <Button
        size="small"
        type="primary"
        onClick={() => setOpenModal(true)}
        title="Search GDSN data and import related EANs"
        icon={<SearchOutlined />}
      >
        GDSN
      </Button>
      <ViewFileDetailModal
        modalVisible={openModal}
        handleModalVisible={setOpenModal}
        initialValues={{ gtin: ean, with: 'import,gdsnMessage.file_url' }}
        fetchGdsnItem={fetchGdsnItem}
      />
    </Space>
  );
};

export default GdsnItemButton;
