import { MagentoOrderStatus } from '@/pages/Magento/Order';
import { updateCrmCase } from '@/services/foodstore-one/Crm/crm-case';
import { getOrdersList } from '@/services/foodstore-one/Magento/order';
import Util, { nf2, skuToItemId, sn } from '@/util';
import { MailOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Button, Modal, Space, Typography, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect } from 'react';
import { useRef } from 'react';
import { useCallback, useState } from 'react';
import type { CrmCaseListModalSearchFormType } from './CrmCaseListModal';

/**
 * Search form data type
 */
type OrderSelectModalSearchFormType = {
  sender?: string; // email
  // sender_name?: string;
  order_id?: number | null;
  increment_id?: string;
  keyword?: string;
  name_address?: string;
};

type OrderSelectModalPropsValuesType = OrderSelectModalSearchFormType & {
  emailId?: number;
  caseId?: number | null;
};

type OrderSelectModalProps = {
  values?: OrderSelectModalPropsValuesType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Email) => Promise<boolean | void>;
  cb?: (action: 'select' | 'deselect', data: any) => void;

  parentSearchFormRef?: React.MutableRefObject<ProFormInstance<CrmCaseListModalSearchFormType> | undefined>;
};

const OrderSelectModal: React.FC<OrderSelectModalProps> = (props) => {
  const { modalVisible, handleModalVisible, values, cb, parentSearchFormRef } = props;

  const [loading, setLoading] = useState<boolean>(false);

  // table
  const actionRef = useRef<ActionType>();

  // Search form
  const searchFormRef = useRef<ProFormInstance<OrderSelectModalSearchFormType>>();

  const handleOrderSelectionForCrmCase = useCallback(
    (orderId: number, deslect?: boolean) => {
      updateCrmCase(sn(values?.caseId), { order_id: deslect ? null : orderId })
        .then((res) => {
          message.success('Updated successfully.');
          cb?.(deslect ? 'deselect' : 'select', res);
          if (!deslect) {
            handleModalVisible(false);
          }
        })
        .catch(Util.error);
    },
    [values?.caseId, cb, handleModalVisible],
  );

  const columns: ProColumns<API.Order>[] = [
    {
      title: 'Order ID',
      dataIndex: 'entity_id',
      sorter: true,
      hideInSearch: true,
      align: 'center',
      defaultSortOrder: 'descend',
      width: 80,
    },
    {
      title: 'Store',
      dataIndex: ['store', 'code'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: 'Increment ID',
      dataIndex: ['increment_id'],
      sorter: true,
      align: 'left',
      ellipsis: true,
      width: 120,
    },
    {
      title: 'status',
      dataIndex: ['status'],
      sorter: true,
      align: 'center',
      ellipsis: true,
      width: 120,
      render: (_, record) => <MagentoOrderStatus status={record.status || ''} />,
    },
    {
      title: 'Items Qty',
      dataIndex: 'total_item_count',
      sorter: true,
      align: 'right',
      width: 80,
      tooltip: 'Qty of included items',
      showSorterTooltip: false,
    },
    {
      title: 'Orderd Qty',
      dataIndex: 'total_qty_ordered',
      sorter: true,
      align: 'right',
      width: 70,
    },

    {
      title: 'Grand Total',
      dataIndex: 'grand_total',
      sorter: true,
      align: 'right',
      width: 100,
      render: (dom, record) => nf2(record.grand_total),
    },
    {
      title: 'Name',
      dataIndex: 'sa_firstname',
      sorter: true,
      align: 'left',
      width: 100,
      render: (dom, record) => record.sa_firstname + ' ' + record.sa_lastname,
    },
    {
      title: 'Zip',
      dataIndex: 'sa_zip',
      sorter: true,
      align: 'center',
      width: 100,
    },
    {
      title: 'City',
      dataIndex: 'sa_city',
      sorter: true,
      align: 'left',
      width: 150,
      showSorterTooltip: false,
    },
    {
      title: 'Created on',
      dataIndex: ['created_at'],
      sorter: true,
      width: 100,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_at),
    },
    {
      title: 'Updated on',
      dataIndex: ['updated_at'],
      sorter: true,
      width: 100,
      className: 'text-sm c-grey',
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_at),
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      align: 'left',
      render: (dom, record) => (
        <>
          <Space>
            {record.entity_id == values?.order_id ? (
              <Button
                size="small"
                type="primary"
                ghost
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleOrderSelectionForCrmCase(sn(record.entity_id), true);
                }}
              >
                Deselect
              </Button>
            ) : (
              <Button
                size="small"
                type="primary"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleOrderSelectionForCrmCase(sn(record.entity_id));
                }}
                title="Select an order into Case"
              >
                Select
              </Button>
            )}
          </Space>
        </>
      ),
    },
  ];

  const expandedRowRender = (record: API.Order) => {
    return (
      <div style={{ paddingLeft: 160 }}>
        <ProTable<API.OrderItem>
          cardProps={{ bodyStyle: { padding: 0 } }}
          bordered
          columns={[
            {
              title: 'SKU',
              dataIndex: 'sku',
              width: 80,
              render: (dom, recordIn) => {
                return (
                  <Typography.Link
                    href={`/item/ean-all-summary?sku=${skuToItemId(recordIn.sku)}_`}
                    target="_blank"
                    copyable
                  >
                    {recordIn.sku}
                  </Typography.Link>
                );
              },
            },
            { title: 'EAN', dataIndex: ['item_ean', 'ean'], width: 100 },
            { title: 'Name', dataIndex: 'name' },
            { title: 'Ordered Qty', dataIndex: 'qty_ordered', align: 'right', width: 100 },
            {
              title: 'Net Price',
              dataIndex: ['price'],
              align: 'right',
              width: 80,
              render: (dom, recordIn) => nf2(recordIn?.price),
            },
            {
              title: 'BP / pcs',
              dataIndex: ['oi_idx', 'bp'],
              align: 'right',
              width: 80,
              render: (dom, recordIn) => {
                const idx = recordIn.oi_idx ?? {};
                const typeStr = idx.bp_type == 1 ? 'By stock booking' : idx.bp_type == 2 ? 'By latest IBO' : '';
                return (
                  <span title={`${typeStr}${idx.ibo_id ? ` #${idx.ibo_id}` : ''}`}>
                    {nf2(sn(idx.bp) * sn(idx.case_qty))}
                  </span>
                );
              },
            },
            {
              title: 'Order Item ID',
              dataIndex: 'item_id',
              align: 'center',
              width: 90,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
            },
            {
              title: 'Product ID',
              dataIndex: 'product_id',
              align: 'center',
              width: 90,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
            },
            {
              title: 'Updated on',
              dataIndex: ['updated_at'],
              sorter: true,
              defaultSortOrder: 'descend',
              width: 110,
              className: 'text-sm c-grey',
              showSorterTooltip: false,
              render: (dom, recordIn) => Util.dtToDMYHHMM(recordIn.updated_at),
            },
          ]}
          style={{ width: 1200 }}
          rowKey="item_id"
          headerTitle={false}
          search={false}
          options={false}
          pagination={false}
          size="small"
          dataSource={record.mag_order_items ?? []}
          columnEmptyText={''}
        />
      </div>
    );
  };

  useEffect(() => {
    if (modalVisible) {
      const sfValues: OrderSelectModalSearchFormType = {
        sender: values?.sender,
        ...parentSearchFormRef?.current?.getFieldsValue(),
      };
      searchFormRef.current?.resetFields();
      searchFormRef.current?.setFieldsValue(sfValues);
    }
  }, [values, modalVisible, parentSearchFormRef]);

  return (
    <Modal
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      title={
        <Space size={24}>
          <div>Select an Order for Case # {values?.caseId} </div>
          <div>
            <MailOutlined /> {values?.sender}
          </div>
        </Space>
      }
      width={1400}
      footer={false}
    >
      <ProForm<OrderSelectModalSearchFormType>
        layout="inline"
        size="small"
        formRef={searchFormRef}
        isKeyPressSubmit
        className="search-form"
        initialValues={{}}
        submitter={{
          submitButtonProps: {
            loading: loading,
            htmlType: 'submit',
          },
          onSubmit: () => actionRef.current?.reload(),
          onReset: () => actionRef.current?.reload(),
          render: (form, dom) => {
            return [...dom];
          },
        }}
      >
        <ProFormText name={'sender'} label="Sender" width={180} placeholder={'Sender'} />
        <ProFormText name={'order_id'} label="Order ID" width={90} placeholder={'Order ID'} />
        <ProFormText name={'increment_idR'} label="Increment ID" width={120} placeholder={'Increment ID'} />
        <ProFormText name={'ebay_order_id'} label="Ebay/KL Order" width={150} placeholder={'Ebay/KL Order'} />
        <ProFormText name={'name_address'} label="Name & Address" width={140} placeholder={'Name & Address'} />
        {/* <ProFormText
          name={'keyword'}
          label="Keyword"
          width={140}
          placeholder={'Keyword'}
          tooltip="Search customer's full name"
        /> */}
      </ProForm>

      <ProTable<API.Order, API.PageParams>
        headerTitle={'Orders List'}
        actionRef={actionRef}
        rowKey="entity_id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_orders', searchFormValues);
          setLoading(true);
          // await loadStatusACList();
          return getOrdersList(
            {
              ...params,
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        pagination={{ defaultPageSize: 50 }}
        columns={columns}
        expandable={{
          expandedRowRender,
          expandRowByClick: true,
          indentSize: 200,
          rowExpandable(record) {
            return sn(record?.total_qty_ordered) > 0;
          },
        }}
        rowSelection={false}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default OrderSelectModal;
