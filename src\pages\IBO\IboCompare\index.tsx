import Util, { nf2, ni, sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Card } from 'antd';
import { useRef, useState } from 'react';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import type { IboCompare } from '@/services/foodstore-one/IBO/ibo-compare';
import { getIboCompareList } from '@/services/foodstore-one/IBO/ibo-compare';
import useSupplierOptions from '@/hooks/BasicData/useSupplierOptions';
import useIbomOptions from '@/hooks/BasicData/useIbomOptions';
import useIboPreManagementOptions from '@/hooks/BasicData/useIboPreManagementOptions';

export type RecordType = Partial<IboCompare>;
export type SearchFormValueType = Partial<API.IboPre>;

export type IboComparedListProps = {
  refreshTick?: number;
  filterType?: 'light' | 'query';
};

const IboComparedList: React.FC<IboComparedListProps> = (props) => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const { supplierOptions, searchSupplierOptions } = useSupplierOptions();
  const { ibomOptions, searchIbomOptions } = useIbomOptions();
  const { iboPreManagementOptions, searchIboPreManagementOptions } = useIboPreManagementOptions();

  const columns: ProColumns<RecordType>[] = [
    {
      title: 'SKU',
      dataIndex: ['item_ean', 'is_single'],
      width: 90,
      copyable: true,
      render(dom, record) {
        return dom;
      },
    },
    {
      title: 'SKU',
      dataIndex: ['sku'],
      width: 90,
      copyable: true,
      render(dom, record) {
        return dom;
      },
    },
    {
      title: 'EAN',
      dataIndex: ['item_ean', 'ean'],
      width: 140,
      copyable: true,
      render(dom, record) {
        return dom;
      },
    },

    {
      title: 'Pre. Qty (Inv)',
      dataIndex: ['sum_qty_pre_inv'],
      width: 90,
      align: 'right',
      className: 'bl2',
      render(dom, record) {
        return ni(record.sum_qty_pre_inv);
      },
    },
    {
      title: 'Pre. Price (Inv)',
      dataIndex: ['avg_price_pre_inv'],
      width: 90,
      align: 'right',
      render(dom, record) {
        return nf2(record.avg_price_pre_inv);
      },
    },
    {
      title: 'Pre. Total Amount (Inv)',
      dataIndex: ['sum_price_pre_inv'],
      width: 90,
      align: 'right',
      render(dom, record) {
        return nf2(record.sum_price_pre_inv);
      },
    },

    {
      title: 'Pre. Qty (Done)',
      dataIndex: ['sum_qty_pre_done'],
      width: 90,
      align: 'right',
      className: 'bl2',
      render(dom, record) {
        return ni(record.sum_qty_pre_done);
      },
    },
    {
      title: 'Pre. Price (Done)',
      dataIndex: ['avg_price_pre_done'],
      width: 90,
      align: 'right',
      render(dom, record) {
        return nf2(record.avg_price_pre_done);
      },
    },
    {
      title: 'Pre. Total Amount (Done)',
      dataIndex: ['sum_price_pre_done'],
      width: 90,
      align: 'right',
      render(dom, record) {
        return nf2(record.sum_price_pre_done);
      },
    },

    {
      title: 'IBO Qty',
      dataIndex: ['sum_qty'],
      width: 90,
      align: 'right',
      className: 'bl2',
      render(dom, record) {
        return ni(record.sum_qty);
      },
    },
    {
      title: 'Price',
      dataIndex: ['avg_price'],
      width: 90,
      align: 'right',
      render(dom, record) {
        return nf2(record.avg_price);
      },
    },
    {
      title: 'Total Amount',
      dataIndex: ['sum_price'],
      width: 90,
      align: 'right',
      render(dom, record) {
        return nf2(record.sum_price);
      },
    },
    {
      title: '',
      dataIndex: ['__'],
    },
  ];

  return (
    <PageContainer>
      <Card>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_ibo_compare', {
            ean_search_mode: 'contain_siblings',
          })}
          submitter={{
            submitButtonProps: { loading: loading, htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
          }}
        >
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            options={supplierOptions}
            request={async (params) => {
              return searchSupplierOptions(params);
            }}
            width="sm"
            name="supplier_id"
            label="Supplier"
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormSelect
            name={'ibo_pre_management_id'}
            showSearch
            label="Pre IBOM"
            width={200}
            options={iboPreManagementOptions}
            request={(params, p) => {
              return searchIboPreManagementOptions({
                ...params,
                supplier_id: searchFormRef.current?.getFieldValue('supplier_id'),
              });
            }}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormSelect
            name={'ibom_id'}
            showSearch
            label="IBOM"
            width={200}
            options={ibomOptions}
            request={(params, p) => {
              return searchIbomOptions({
                ...params,
                supplier_id: searchFormRef.current?.getFieldValue('supplier_id'),
              });
            }}
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />

          {/* <ProFormText name={'name'} label="Name" width={180} placeholder={'Item Name'} /> */}
          <ProFormText name={'sku'} label="SKU" width={150} placeholder={'SKU'} />
          <ProFormText name={'ean'} label="EAN" width={150} placeholder={'EAN'} />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        style={{ marginTop: 20 }}
        headerTitle="Pre Orders & IBOs List"
        actionRef={actionRef}
        rowKey="id"
        bordered
        revalidateOnFocus={false}
        sticky
        scroll={{ x: '100%' }}
        size="small"
        onLoadingChange={(loadingParam) => setLoading(loadingParam as boolean)}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues('sf_ibo_compare_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        rowClassName={(record) => (record.item_ean?.is_single ? 'row-single' : 'row-multi')}
        options={{ fullScreen: true }}
        search={false}
        request={(params, sort, filter) => {
          let sortStr = JSON.stringify(sort || {});
          sortStr = sortStr.replaceAll(/ean_detail\./g, 'e.');
          const newSort = Util.safeJsonParse(sortStr);

          const searchValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_ibo_compare', searchValues);
          Util.setSfValues('sf_ibo_compare_p', params);

          const iboParam = {
            ...params,
            ...Util.mergeGSearch(searchValues),
            trademarks: [searchValues.trademark?.value],
            with: 'mode1,item_no,supplier,iboPreManagement',
          };
          return getIboCompareList(iboParam, { ...newSort }, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        /* rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }} */
        columnEmptyText=""
        tableAlertRender={false}
        toolBarRender={() => []}
      />
      {/* {selectedRowsState?.length > 0 && (
            <FooterToolbar
              extra={
                <Space>
                  <span>
                    Chosen &nbsp;<a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
                    &nbsp;Pre IBOs.
                  </span>
                  <a onClick={() => actionRef.current?.clearSelected?.()}>Clear</a>
                </Space>
              }
            >
              <Button
                onClick={async () => {
                  await handleRemove(selectedRowsState);
                  setSelectedRows([]);
                  actionRef.current?.reloadAndRest?.();
                }}
              >
                Batch deletion
              </Button>
            </FooterToolbar>
          )}    */}
    </PageContainer>
  );
};

export default IboComparedList;
