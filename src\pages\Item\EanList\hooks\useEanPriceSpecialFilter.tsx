import { Status } from '@/constants';
import Util from '@/util';
import { FilterOutlined } from '@ant-design/icons';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import { ActionType } from '@ant-design/pro-table';
import { Space } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { useEffect, useMemo, useState } from 'react';

const useEanPriceSpecialFilter = (
  actionRef: React.MutableRefObject<ActionType | undefined>,
  searchRef: React.MutableRefObject<ProFormInstance | undefined>,
  parentLoading?: boolean,
  lsKey?: string,
) => {
  const [filterId, setFilterId] = useState<any>();

  const options: DefaultOptionType[] = [
    {
      value: '01',
      label: '01 - Item inactive, but Price_Stable',
    },
    {
      value: '02',
      label: '02 - Active FS_One, no Stock, no Price_Stable',
    },
    {
      value: '03',
      label: '03 - Active GFC, no Price_Stable',
    },
    {
      value: '04',
      label: '04 - Active FS_ONE, Last Changed Price < 2 days',
    },

    {
      value: '50',
      label: '50 - Value 1 <= 1',
    },
    {
      value: '51',
      label: '51 - Value 2 >= 2 ',
    },
  ];

  useEffect(() => {
    setFilterId(Util.getSfValues(`ean_special_filter${lsKey}`)?.['filterId'] || '');
  }, [lsKey]);

  useEffect(() => {
    searchRef.current?.resetFields();

    if (filterId == '01') {
      searchRef.current?.setFieldValue('status', Status.INACTIVE);
    } else if (filterId == '02') {
      searchRef.current?.setFieldValue('fsOneActiveOrNot', 1);
      searchRef.current?.setFieldValue('noStablePriceOnly', true);
      searchRef.current?.setFieldValue('noStock', true);
    } else if (filterId == '03') {
      searchRef.current?.setFieldValue('gfcActiveOrNot', 1);
      searchRef.current?.setFieldValue('noStablePriceOnly', true);
    } else if (filterId == '04') {
      searchRef.current?.setFieldValue('fsOneActiveOrNot', 1);
      searchRef.current?.setFieldValue('priceNDaysChangeOpt', '<');
      searchRef.current?.setFieldValue('priceNDaysChange', 2);
    } else if (filterId == '50') {
      searchRef.current?.setFieldValue('stockRelated1', 1);
    } else if (filterId == '51') {
      searchRef.current?.setFieldValue('stockRelated2', 2);
    }

    actionRef.current?.reload();
  }, [filterId]);

  const renderedEle = useMemo(() => {
    return (
      <Space style={{ marginLeft: 64 }}>
        <ProFormSelect
          name="filterId"
          label={<FilterOutlined />}
          colon={false}
          placeholder="00 No Filter"
          width={200}
          allowClear
          showSearch
          options={options}
          formItemProps={{ style: { marginBottom: 0 } }}
          fieldProps={{
            dropdownMatchSelectWidth: false,
            value: filterId,
            onChange(value, option) {
              setFilterId(value);
              Util.setSfValues(`ean_special_filter${lsKey}`, { filterId: value });
            },
          }}
        />
        {/* <ButtonGroup style={{ marginRight: 8 }}>
          <Button
            onClick={() => {
              handleNavigation('prev');
            }}
            icon={<ArrowLeftOutlined />}
            disabled={parentLoading}
          />
          <Button
            onClick={() => {
              handleNavigation('next');
            }}
            icon={<ArrowRightOutlined />}
            disabled={parentLoading}
          />
        </ButtonGroup> */}
      </Space>
    );
  }, [filterId, /* handleNavigation,  */ lsKey, options, parentLoading]);

  const eleInsertedToForm = useMemo(() => {
    return <div style={{ display: 'none' }}></div>;
  }, []);

  return { renderedEle, filterId, eleInsertedToForm };
};

export default useEanPriceSpecialFilter;
