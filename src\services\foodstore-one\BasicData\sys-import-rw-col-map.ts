/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/basic-data/sys-import-rw-col-map';

/** 
 * Get SysImportRwColMap list
 * 
 * GET /api/basic-data/sys-import-rw-col-map */
export async function getSysImportRwColMapList(
  params: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysImportRwColMap>> {
  return request<API.ResultList<API.SysImportRwColMap>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * Create SysImportRwColMap
 * 
 * POST /api/basic-data/sys-import-rw-col-map */
export async function createSysImportRwColMap(
  data: Partial<API.SysImportRwColMap>,
  options?: Record<string, any>,
): Promise<API.SysImportRwColMap> {
  return request<API.ResultObject<API.SysImportRwColMap>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    withToken: true,
    ...(options || {}),
  }).then((res) => res.message);
}

/** 
 * Update SysImportRwColMap
 * 
 * PUT /api/basic-data/sys-import-rw-col-map/{id} */
export async function updateSysImportRwColMap(
  id: number,
  data: Partial<API.SysImportRwColMap>,
  options?: Record<string, any>,
): Promise<API.SysImportRwColMap> {
  return request<API.ResultObject<API.SysImportRwColMap>>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    withToken: true,
    ...(options || {}),
  }).then((res) => res.message);
}

/** 
 * Delete SysImportRwColMap
 * 
 * DELETE /api/basic-data/sys-import-rw-col-map/{id} */
export async function deleteSysImportRwColMap(
  id: number | string,
  options?: Record<string, any>,
): Promise<void> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    withToken: true,
    ...(options || {}),
  }).then(() => undefined);
}
