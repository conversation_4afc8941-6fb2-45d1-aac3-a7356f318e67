CREATE TABLE `xopenapi_call_log`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `model`       varchar(255) DEFAULT NULL,
    `prompt`      text         DEFAULT NULL,
    `max_tokens`  int(11)      DEFAULT NULL,
    `temperature` int(11)      DEFAULT NULL,
    `res_id`      varchar(255) DEFAULT NULL,
    `object`      varchar(255) DEFAULT NULL,
    `choice`      longtext     DEFAULT NULL COMMENT 'First choice',
    `choices`     longtext     DEFAULT NULL COMMENT 'JSON Array',
    `usage`       text         DEFAULT NULL COMMENT 'JSON',
    `created`     int(11)      DEFAULT NULL,
    `created_on`  datetime     DEFAULT NULL,
    `created_by`  int(11)      DEFAULT NULL,
    `updated_on`  datetime     DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;